# SplitTestService 详细流程文档

## 概述
SplitTestService 是一个用于执行 A/B 测试的服务，它通过分析网站流量数据来找到最佳的 URL 分组策略。该服务的核心是调用 SplitTest 类来执行复杂的统计分析和相关性计算。

## 主要流程

### 1. 服务启动和任务监听 (main方法)

```java
public static void main(String[] args) throws Exception {
    SplitTestService splitTestService = new SplitTestService();
    while (true) {
        // 1.1 获取待处理的缓存列表
        final List<String> cacheList = WorkerUtils.getCacheList(WorkerUtils.CACHE_LIST_URL + WORKER_KEY_PREFIX);
        
        // 1.2 解析实例ID
        final String key = cacheList.get(0);
        final Matcher matcher = KEY_PATTERN.matcher(key);
        final int instanceId = Integer.parseInt(matcher.group(1));
        
        // 1.3 启动分割测试
        splitTestService.start(instanceId);
        
        // 1.4 删除处理完的key
        EventUtils.deleteKey(key);
    }
}
```

**关键点：**
- 使用 `event_splittester_url_{instanceId}` 格式的 key 来监听任务
- 每次处理一个实例，处理完后删除对应的 key

### 2. 核心处理流程 (start方法)

#### 2.1 数据准备阶段

```java
private void start(int instanceId) {
    // 获取分割测试实例
    final UrlSplitCorrelationInstance urlSplitCorrelationInstance = 
        this.urlSplitCorrelationInstanceDAO.getById(instanceId);
    
    // 检查页面优化配置是否存在
    final PageOptimization pageOptimization = 
        this.pageOptimizationDAO.fetchById(urlSplitCorrelationInstance.getGroupId());
}
```

#### 2.2 创建 SplitTest.MetricsParams 参数

这是与 SplitTest 类交互的关键步骤：

```java
// 创建指标参数
final SplitTest.MetricsParams metricsParams = urlSplitCorrelationInstance.createMetricsParams();
```

**MetricsParams 包含的关键信息：**
- `domainId`: 域名ID
- `metricsType`: 指标类型 (1=GSC, 2=GA)
- `dataSourceType`: 数据源类型
- `enableParentChildRel`: 是否启用父子页面关系
- `relIds`: 关联ID列表
- `urlFilter`: URL过滤参数
- `endDate`: 结束日期
- `googleAnalyticsVersion`: GA版本

#### 2.3 处理共享域名和数据源配置

```java
if (pageOptimization.getUrlDataSource() == 1) {
    if (metricsType == 1) {
        // GSC数据源处理
        final List<GWMDomainRel> relList = this.gwmDomainRelDAO.findByReferDomainId(domainId);
        if (!relList.isEmpty()) {
            // 设置共享域名ID
            metricsParams.setDomainId(gwmDomainRel1.getBaseDomainId());
            // 设置关联ID列表
            metricsParams.setRelIds(stringBuilder.substring(0, stringBuilder.length() - 1));
        }
        // 检查父子页面关系
        final boolean hasParentChildUrls = this.cdbParentChildPageRelEntityDAO.hasParentChildURLs(domainId);
        metricsParams.setEnableParentChildRel(hasParentChildUrls);
    } else {
        // GA数据源处理
        final GroupRelationshipEntity groupRelationshipEntity = this.groupRelationshipEntityDAO.get(domainId);
        if (groupRelationshipEntity != null) {
            metricsParams.setDomainId(groupRelationshipEntity.getGroupId());
        }
        // 获取流量类型配置
        final OwnDomainSettingEntity ownDomainSettingEntity = this.ownDomainSettingEntityDAO.getSettingByDomainId(domainId);
        if (ownDomainSettingEntity != null && ownDomainSettingEntity.getTrafficType() != null) {
            metricsParams.setDataSourceType(ownDomainSettingEntity.getTrafficType());
        }
    }
}
```

#### 2.4 配置 URL 过滤参数

```java
final SplitTest.UrlFilterParam urlFilterParam = new SplitTest.UrlFilterParam();
final String filterSqlCondition = urlSplitCorrelationInstance.getFilterSqlCondition();
final boolean sqlType = StringUtils.isNotEmpty(filterSqlCondition);

if (sqlType) {
    // SQL过滤方式
    urlFilterParam.setFilterType("Sql");
    urlFilterParam.setSearchInput(filterSqlCondition);
} else {
    // URL列表过滤方式
    urlFilterParam.setFilterType("Urls");
    urlSplitCorrelationDetails = getNewUrlSplitCorrelationDetails(urlSplitCorrelationInstance);
    urlFilterParam.setSearchInput(urlSplitCorrelationDetails.stream()
        .map(detail -> StringUtils.isNotBlank(detail.getBrowserMurmur3hash()) ? 
             detail.getBrowserMurmur3hash() : detail.getUrlMurmur3Hash())
        .collect(Collectors.joining("','", "'", "'")));
}
```

### 3. 调用 SplitTest 执行分析

这是整个流程的核心部分，展示了如何使用 SplitTest 类：

#### 3.1 创建 SplitTest 实例

```java
// 更新状态为处理中
this.urlSplitCorrelationInstanceDAO.updateStatus(instanceId, UrlSplitCorrelationInstance.Status.PROCESSING);

// 创建 SplitTest 实例
SplitTest splitTest = new SplitTest(metricsParams);
```

#### 3.2 获取内部SQL查询

```java
// 获取内部SQL查询语句
final String innerSql = splitTest.getInnerSql();
```

**SplitTest.getInnerSql() 方法的作用：**
- 根据 `metricsParams` 中的配置生成相应的SQL查询
- 支持两种过滤类型：
  - `"Urls"`: 基于URL哈希值的过滤
  - `"Sql"`: 基于自定义SQL条件的过滤
- 根据指标类型 (GSC/GA) 生成不同的查询语句

#### 3.3 处理URL列表

```java
if (urlSplitCorrelationDetails.isEmpty()) {
    // 如果没有预定义的URL列表，从数据库获取
    List<String> distinctUrlList = splitTest.getDistinctUrlList(innerSql);
    if (CollectionUtils.isEmpty(distinctUrlList)) {
        log.warn("instanceId: {}, distinctUrlList is empty", instanceId);
        return;
    }
    
    // 批量插入URL详情
    urlSplitCorrelationDetails = distinctUrlList.stream().map(url -> {
        final UrlSplitCorrelationDetail urlSplitCorrelationDetail = new UrlSplitCorrelationDetail();
        urlSplitCorrelationDetail.setInstanceId(instanceId);
        urlSplitCorrelationDetail.setOwnDomainId(domainId);
        urlSplitCorrelationDetail.setUrl(url);
        urlSplitCorrelationDetail.setUrlMurmur3Hash(MurmurHashUtils.getMurmurHash3_64(url));
        urlSplitCorrelationDetail.setBrowserMurmur3hash(MurmurHashUtils.getMurmurHash3_64(encodeUrlWithOkHttp(url)));
        return urlSplitCorrelationDetail;
    }).collect(Collectors.toList());
    
    this.urlSplitCorrelationDetailDAO.saveBatch(urlSplitCorrelationDetails);
}
```

**SplitTest.getDistinctUrlList() 方法的作用：**
- 执行 `SELECT DISTINCT url FROM (innerSql)` 查询
- 从ClickHouse数据库获取去重后的URL列表
- 返回符合条件的所有唯一URL

#### 3.4 执行分割测试分析

```java
// 执行分割测试的核心分析
correlationReport = splitTest.start(innerSql);
```

**SplitTest.start() 方法的详细流程：**

1. **循环执行多次测试** (默认100次)：
   ```java
   for (int i = 1; i <= this.metricsParams.getLoopCount(); i++) {
       // 获取指标数据
       final List<String> metrics = retrieveMetrics(sql);
       
       // 转换为UrlMetrics对象
       List<UrlMetrics> urlMetricsList = metrics.stream()
           .map(s -> createUrlMetrics(url, data))
           .collect(Collectors.toList());
   }
   ```

2. **分组处理**：
   ```java
   // 将URL列表分成两组
   int midIndex = urlMetricsList.size() / 2;
   List<UrlMetrics> testGroup = new ArrayList<>(urlMetricsList.subList(0, midIndex));
   List<UrlMetrics> controlGroup = new ArrayList<>(urlMetricsList.subList(midIndex, urlMetricsList.size()));
   ```

3. **计算相关系数**：
   ```java
   // 构建请求参数
   final CorrelationCoefficientRequest correlationCoefficientRequest = 
       buildRequest(correlationReport.testTimeSeries, 
                   Collections.singletonList(correlationReport.controlTimeSeries), 
                   this.metricsParams.domainId + "_" + i);
   
   // 调用外部API计算相关系数
   final Double correlation = calculateCorrelationCoefficient(correlationCoefficientRequest);
   ```

4. **选择最高相关性结果**：
   ```java
   if (correlation > highestCorrelationReport.getCorrelation()) {
       highestCorrelationReport = correlationReport;
   }
   ```

**CorrelationReport 包含的关键信息：**
- `correlation`: 相关系数
- `testGroup`: 测试组URL列表
- `controlGroup`: 控制组URL列表
- `extremeTestUrl`: 测试组中的极值URL
- `extremeControlUrl`: 控制组中的极值URL

### 4. 结果处理和标签添加

#### 4.1 保存相关性分数

```java
log.info("correlation report: {}", correlationReport.summary());
this.urlSplitCorrelationInstanceDAO.saveScore(instanceId, correlationReport.getCorrelation());
```

#### 4.2 添加页面标签

```java
// 为测试组和控制组的URL添加页面标签
final Collection<UrlSplitCorrelationDetail> urlSplitCorrelationDetailsUpdated = 
    this.addPageTag(domainId, urlSplitCorrelationDetails, correlationReport, pageOptimization.getName());
```

**addPageTag 方法的作用：**
- 为控制组URL添加 `{testName}(Control Group)` 标签
- 为测试组URL添加 `{testName}(Test Group)` 标签
- 处理极值数据，标记为 `DISCARD_TYPE_EXTREME_DATA`
- 对于没有数据的URL，随机分配到两组并标记为 `DISCARD_TYPE_NO_DATA`

#### 4.3 保存页面优化配置

```java
// 保存页面优化配置
final String version = this.savePageOptimization(pageOptimization, domainId, urlSplitCorrelationDetailsUpdated);
```

**savePageOptimization 方法的作用：**
- 为测试组中的每个URL创建 ContentAssistant 记录
- 建立 PageOptimization 和 ContentAssistant 的关联关系
- 返回版本号用于后续的爬虫任务

#### 4.4 触发爬虫任务

```java
// 保存爬虫任务key
final String crawlWorkerKey = CRAWL_WORKER_KEY_PREFIX + pageOptimization.getId() + "_" + domainId + "_" + version;
WorkerUtils.syncEventKeyToWorkers(WorkerUtils.WORKER_BASE_URL, crawlWorkerKey, "");
```

## SplitTest 类的关键功能

### 1. 数据源适配
- **GSC数据源**: 使用 `actonia_gsc.gsc_daily_insert` 表
- **GA数据源**: 根据 `trafficType` 选择不同的表 (dis_ga, dis_analytics_ga4, dis_adobe, dis_upload)

### 2. SQL查询生成
- 根据过滤类型生成相应的内部SQL
- 支持URL哈希过滤和自定义SQL条件过滤
- 处理父子页面关系和共享域名配置

### 3. 统计分析
- 执行多轮随机分组测试
- 计算每组的时间序列数据
- 调用外部API计算相关系数
- 选择相关性最高的分组结果

### 4. 异常数据处理
- 识别和移除极值数据
- 确保最小URL数量和天数要求
- 处理数据不足的情况

## 错误处理和监控

### 1. 数据验证
- 检查URL列表是否为空
- 验证分组后的URL数量
- 确保相关性计算的有效性

### 2. 状态管理
- `PROCESSING`: 正在处理中
- `COMPLETED_SUCCESSFULLY`: 处理成功完成
- 异常情况下的状态回滚

### 3. 日志记录
- 详细记录每个步骤的执行情况
- 记录SQL查询和API调用
- 监控处理时间和性能指标

## 总结

SplitTestService 通过精心设计的流程，利用 SplitTest 类的强大分析能力，实现了：

1. **自动化的A/B测试设置**：从任务监听到结果输出的全自动化流程
2. **灵活的数据源支持**：支持GSC和GA等多种数据源
3. **智能的分组策略**：通过统计分析找到最优的URL分组
4. **完整的结果管理**：从标签添加到后续任务触发的完整链路

整个系统的核心价值在于将复杂的统计分析过程自动化，为网站优化提供数据驱动的分组策略。
