# SplitTestService 业务流程文档

## 概述
SplitTestService 是一个A/B测试服务，通过分析网站流量数据来找到最佳的URL分组策略，为页面优化提供数据支持。

## 主要业务流程

### 1. 任务监听和启动
**目的**: 监听待处理的分割测试任务
- 持续监听Redis中的任务队列 (`event_splittester_url_{instanceId}`)
- 获取到任务后解析出实例ID
- 启动对应的分割测试处理
- 处理完成后清理任务队列

### 2. 数据准备和配置
**目的**: 收集和准备分析所需的基础数据
- 获取分割测试实例配置信息
- 验证页面优化任务是否还存在（防止用户删除）
- 根据数据源类型（GSC/GA）配置不同的参数
- 处理共享域名和域名组关系
- 设置父子页面关系配置

### 3. URL过滤参数配置
**目的**: 确定要分析的URL范围
- **SQL过滤方式**: 使用自定义SQL条件筛选URL
- **URL列表方式**: 使用预定义的URL列表
- 处理父子页面关系（子页面替换为父页面进行分析）
- 生成URL的哈希值用于数据库查询

### 4. 调用SplitTest执行核心分析
**目的**: 执行统计分析找到最佳分组

#### 4.1 创建SplitTest实例
- 传入配置好的MetricsParams参数
- 更新任务状态为"处理中"

#### 4.2 生成数据查询SQL
- **SplitTest.getInnerSql()**: 根据数据源类型和过滤条件生成查询语句
- 支持GSC和GA两种不同的数据源查询

#### 4.3 获取URL列表
- **SplitTest.getDistinctUrlList()**: 如果没有预定义URL，从数据库获取符合条件的所有URL
- 将获取到的URL保存到数据库中供后续使用

#### 4.4 执行分割测试分析
- **SplitTest.start()**: 执行核心的统计分析
  - **多轮测试**: 默认执行100轮随机分组测试
  - **数据获取**: 从ClickHouse获取每个URL的历史流量数据
  - **随机分组**: 将URL随机分成测试组和控制组
  - **时间序列分析**: 计算每组的时间序列数据
  - **相关性计算**: 调用外部API计算两组数据的相关系数
  - **最优选择**: 选择相关性最高的分组结果

### 5. 结果处理和应用
**目的**: 将分析结果应用到实际的页面优化中

#### 5.1 保存分析结果
- 保存最高的相关系数到数据库
- 记录最优的测试组和控制组URL列表

#### 5.2 添加页面标签
- 为控制组URL添加 `{测试名称}(Control Group)` 标签
- 为测试组URL添加 `{测试名称}(Test Group)` 标签
- 标记极值数据（异常高或低的流量数据）
- 对没有足够数据的URL进行随机分配

#### 5.3 创建页面优化任务
- 为测试组中的每个URL创建ContentAssistant记录
- 建立页面优化任务与URL的关联关系
- 生成版本号用于任务追踪

#### 5.4 触发后续处理
- 生成爬虫任务key (`event_splittester_crawlUrl_{groupId}_{domainId}_{version}`)
- 通知爬虫系统开始处理测试组的URL

## SplitTest类的核心作用

### 1. 数据源适配
- **GSC数据**: 处理Google Search Console的搜索流量数据
- **GA数据**: 处理Google Analytics的网站访问数据
- 根据域名配置选择合适的数据表

### 2. 统计分析引擎
- **多轮随机测试**: 通过多次随机分组减少偶然性
- **时间序列分析**: 分析URL流量的时间变化趋势
- **相关性计算**: 使用外部API计算两组数据的统计相关性
- **异常数据处理**: 识别和排除极值数据的干扰

### 3. 查询优化
- 动态生成高效的SQL查询语句
- 处理大数据量的URL筛选
- 确保查询结果的准确性和完整性

## 业务价值

### 1. 自动化A/B测试
- 无需人工干预的自动分组
- 基于历史数据的科学分组
- 减少主观判断的偏差

### 2. 数据驱动决策
- 通过统计分析确保分组的有效性
- 提供量化的相关性指标
- 为页面优化提供可靠的基础

### 3. 规模化处理
- 支持大量URL的批量处理
- 适应不同数据源和域名配置
- 可扩展的任务处理架构

## 关键成功因素

### 1. 数据质量
- 确保有足够的历史流量数据
- 处理数据缺失和异常情况
- 维护数据的一致性和准确性

### 2. 统计有效性
- 通过多轮测试提高结果可靠性
- 合理的分组大小和测试周期
- 科学的相关性计算方法

### 3. 系统稳定性
- 完善的错误处理和重试机制
- 详细的日志记录和监控
- 优雅的任务状态管理
