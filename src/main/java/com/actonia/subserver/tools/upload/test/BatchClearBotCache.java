package com.actonia.subserver.tools.upload.test;


import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;

public class BatchClearBotCache {

    private static final List<Integer> domains1909 = Arrays.asList(13046,12914,13067,13059,13095,13054,13099,13082,13109,13065,13092,10050,13108,13070,12967,12952,13047,12985,10020,12942,13043,13018,12956,12921,12927,12994,12894,12997,12898,13035,13022,13020,12971,12660,12972,12944,12906,10680,13033,10679,12933,13012,12937,12945,13006,12904,12926,12907,12953,12966,13036,13004,12911,12913,13034,13015,12943,12999,12987,13055,13063,13085,13079,13075,13081,13061,13104,13060,13087,10113,13073,10045,2221,8987,2219,13083,10352,10059,10049,10221,4354,13050,5063,10913,10914,5725,10912,10558,13001,12948,13005,12912,13007,12932,13014,12950,12963,13037,12916,13097,13100,13111,13076,13072,13090,2327,12938,10028,10487,12902,12893,2323,2213,10019,2318,2317,10034,12081,10039,10038,2169,2311,2211,13098,10488,12918,2210,13089,2209,2208,13102,13096,10559,10023,2363,2671,13051,2299,9009,5097,5919,11315,2205,13078,2204,2202,2358,10058,2201,2200,2199,10068,12080,2288,2287,10036,10035,5255,2281,12901,10560,2198,2197,2276,2196,13008,2195,10030,2194,2270,2269,2268,2193,2212,12922,13080,2264,2338,2191,10714,2260,10024,2190,2189,5635,2188,10031,10026,12079,10037,4711,2185,2394,10047,13071,5098,10017,12979,12947,12919,12934,12977,12930,12959,12925,2181,10025,13040,13019,12924,12905,12968,13013,12931,12900,13002,12892,13025,12957,13038,12988,12955,13091,13023,13044,13030,12981,12940,12965,12982,13003,12909,12929,13031,12983,13039,12910,12973,12960,12978,13009,12920,12996,12951,12980,12992,12941,13062,10015,13010,12908,12917,13028,12998,13041,12970,13088,10489,13017,12964,13027,13049,13066,13053,10046,13086,13101,9633,13069,13064,11382,13052,10055,13094,13032,13024,12928,12962,12991,12975,12946,12923,12899,12993,12936,12939,12995,13029,12958,13016,12974,13045,13026,13000,12935,12903,12986,13021,13048,12989,13084,10222,12174,12954,12969,12976,13068,10563,10219,12990,10021);
    private static final List<Integer> domainsExpedia = Arrays.asList(4744,4762,5071,557,9522,4756,4741,4731,5680,4749,558,4748,12832,9299,5675,551,4753,553,12831,5461,4735,4754,4727,12826,12288,4746,550,4747,4758,898,5084,9276,12823,5676,4755,4738,9305,4736,8648,5681,12824,5674,5673,12827,554,555,9286,551,9295,9303,9306,552,5671,4737,4739,4750,9271,9290,556,4763,12829,9279,8644,5678,4740,9288,4730,3458,1180,593,12830,9308,10666,9300,8650,4728,9331,9296,4729,12839,12828,5679,8647,8743,5157,9283,9304,12822,522,4752,4765,12287,4751,5903,562,9307,4733,9275,9311,4743,4745,5677,561,4734,5672,9287,9277,9294,9281,4761,9529,9309,9297,560,5070,8744,522,5085,9298,8645,4764,8649,550,9301,4742,9332,12825,4732,1508);
    private static final String CACHE_SERVICE_URL = "https://s11-dev.seoclarity.dev/seoClarity/cacheService/cleanCache?access_token=c09yxv13-opr3-d745-9734-8pu48420nj67&type=bot&oid=";
    private static final String SUMMARY_SERVICE_URL = "https://s11-dev.seoclarity.dev/seoClarity/cacheService/cleanSummaryCache?access_token=c09yxv13-opr3-d745-9734-8pu48420nj67&type=bot&oid=";

    public static void main(String[] args) {
        BatchClearBotCache batchClearBotCache = new BatchClearBotCache();
        batchClearBotCache.process(1);
//        batchClearBotCache.process(2);
    }

    /**
     *
     * @param processType 0: 1909 bot domain 1: expedia domain
     */
    private void process(int processType) {
        if (processType == 0) {
            startProcess(domains1909);
        } else if (processType == 1) {
            startProcess(domainsExpedia);
        }

    }

    private void startProcess(List<Integer> oids) {
        // Create HttpClient
        try (CloseableHttpClient client = HttpClients.createDefault()) {
            for (int oid : oids) {
                // Construct URLs
                String cacheUrl = CACHE_SERVICE_URL + oid;
                String summaryUrl = SUMMARY_SERVICE_URL + oid;

                // Send requests
                sendRequest(client, cacheUrl);
                sendRequest(client, summaryUrl);
                System.out.println("===========================");
                try {
                    TimeUnit.MILLISECONDS.sleep(300);
                }catch (Exception ee) {
                    ee.printStackTrace();
                }

            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private static void sendRequest(CloseableHttpClient client, String url) {
        HttpGet request = new HttpGet(url);

        try (CloseableHttpResponse response = client.execute(request)) {
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                // Print response (for debugging purposes)
                String result = EntityUtils.toString(entity);
                System.out.println("Response for URL " + url + ": " + response.getStatusLine().getStatusCode());
                System.out.println(result);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}


