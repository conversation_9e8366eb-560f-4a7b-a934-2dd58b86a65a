package com.actonia.subserver.tools.upload.bot;

import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateUtils;
import seoclarity.backend.entity.clickhouse.bot.BotJsonVO;
import seoclarity.backend.utils.BotUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.UrlFilterUtil;

import java.io.IOException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> on 2017/2/27.
 * mvn exec:java -Dexec.mainClass="com.actonia.subserver.tools.upload.bot.Extract765BotData" -Dexec.cleanupDaemonThreads=false -Dexec.args="2018-12-04"
 */
@CommonsLog
public class Extract765BotData extends AbstractExtractBotCommonJsonFile {

	CSVFormat csvFormat = CSVFormat.DEFAULT.withDelimiter(',');
	private static Date processDate = new Date();
	private static Date logDate;
	private static String localPath = "/opt/bot/";

	private static Integer version = 0;

	public Extract765BotData() {
		super();
		domainMaps.put(765, "www.grainger.com");

		List<String> fileNames = new ArrayList<>();
		for (int i = 1; i < 3; i++) {
			fileNames.add("%s.02.00.0"+i+".SEO_ApachelogExtract.csv.gz");
		}

		domainFileNameMap.put(765, fileNames);
	}

	public static void main(String[] args) throws Exception{
		Extract765BotData extractMechaBotData = new Extract765BotData();
		if (args != null & args.length > 0) {
			if(StringUtils.containsIgnoreCase(args[0], ",")) {
				Date sDate = FormatUtils.toDate(args[0].split(",")[0], "yyyy-MM-dd");
				Date eDate = FormatUtils.toDate(args[0].split(",")[1], "yyyy-MM-dd");
				if (args.length > 1) {
					version = NumberUtils.toInt(args[1]);
				}
				while(sDate.compareTo(eDate) <= 0) {
					logDate = sDate;
					processDate = logDate;
					extractMechaBotData.startProcess(true, false);
					sDate = DateUtils.addDays(sDate, 1);
				}
			} else {
				logDate = FormatUtils.toDate(args[0], "yyyy-MM-dd");
				processDate = logDate;
				if (args.length > 1) {
					version = NumberUtils.toInt(args[1]);
				}
				extractMechaBotData.startProcess(true, false);
			}
			extractMechaBotData.waitForThreadPool();
		} else {
            logDate = new Date();
			processDate = logDate;
			extractMechaBotData.startProcess(false);
        }
	}

	@Override
	protected boolean isMultiFile() {
		return true;
	}

	@Override
	protected Date getBotLogDate() {
		return logDate;
	}

	@Override
	protected String getRemoteFilePath(int domainId) {
		return "/home/<USER>/"+domainId+"/";
	}

	@Override
	protected List<String> getRemoteFileNames(int domainId, Date logDate) {
		List<String> fileNames = new ArrayList<>(domainFileNameMap.keySet().size());
//		domainFileNameMap.get(domainId).stream().forEach(s -> {
//			fileNames.add(String.format(s, FormatUtils.formatDate(logDate, "yyyy-MM-dd")))
//		});
		for (String fileName : domainFileNameMap.get(domainId)) {
			fileNames.add(String.format(fileName, FormatUtils.formatDate(logDate, "yyyy-MM-dd")));
		}
		return fileNames;
	}

	@Override
	protected String getLocalFilePath() {
		return "/tmp/";
	}

	@Override
	protected BotJsonVO parserLineToVO(String line, String domainName, Integer domainId) throws ParseException {
		// "_time",site,clientip,uri,"uri_path","uri_query",useragent,status,port
		CSVParser csvParser;
		try {
			csvParser = CSVParser.parse(line, csvFormat);
		} catch (IOException e) {
			e.printStackTrace();
			return null;
		}
		List<CSVRecord> csvRecords;
		try {
			csvRecords = csvParser.getRecords();
		} catch (IOException e) {
			e.printStackTrace();
			return null;
		}
		try {
			for (CSVRecord csvRecord : csvRecords) {
				String timeStr = csvRecord.get(0);
				Long time = NumberUtils.toLong(timeStr.replaceAll("\\.", ""), 0);
				if (timeStr.length() == 10) {
					time *= 1000;
				}
				String site = removeHostPotocal(csvRecord.get(1));
				String clientip = "0.0.0.0";
	//			String clientip = csvRecord.get(2);

				String url = csvRecord.get(2);
				if (UrlFilterUtil.shouldSkipUrl(url)) {
					return null;
				}
				String uri = FormatUtils.getUriFromUrl(url);
				String useragent = csvRecord.get(5);
				int uaGroupId = BotUtils.getUaGroupIdByUA(useragent);
				if (uaGroupId < 0) {
					//System.out.println("error useragent line: " + line);
					return null;
				}
				//https://www.wrike.com/open.htm?id=447370701
				if(StringUtils.containsIgnoreCase(useragent, "AdsBot")){
					System.out.println("error AdsBot line: " + line);
					continue;
				}
				String status = csvRecord.get(6);
				Date date = new Date(time);

				if (StringUtils.isEmpty(site) ||
						StringUtils.isEmpty(clientip) ||
						StringUtils.isEmpty(status) ||
						StringUtils.isEmpty(useragent) ||
						StringUtils.isEmpty(uri) ||
						time == 0) {
					System.out.println("Error : " + csvRecord.toString());
					continue;
				}

				BotJsonVO jsonVO = new BotJsonVO();
				BotJsonVO.BotDataVO dataVO = new BotJsonVO().new BotDataVO();
				dataVO.setUaGroupId(uaGroupId);
				dataVO.setOriginIP(clientip);
				dataVO.setReqHost(site);
				dataVO.setStatus(status);
				dataVO.setUserAgent(useragent);
				dataVO.setUserAgentStr(useragent);
				dataVO.setReqPath(uri);
				//Leo - https://www.wrike.com/open.htm?id=186725979
				dataVO.setDate(FormatUtils.formatDate(date, "yyyy-MM-dd HH:mm:ss"));
				dataVO.setTimestamp(String.valueOf(time));
				dataVO.setDomainId(domainId);
				dataVO.setDisableTimeZone(true);
				jsonVO.setData(dataVO);
				dataVO.setVersion(version);
				return jsonVO;
			}
		} catch (Exception e) {
			e.printStackTrace();
			System.out.println("error catch line: " + line);
		}
		return null;
	}

	@Override
	protected String getLocalGzFilePath() {
		return localPath;
	}

	private String removeHostPotocal(String host) {
		return host.replaceAll("^http[s]://", "");
	}
}
