package com.actonia.subserver.tools.upload.newbot;

import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.lang.time.DateUtils;
import org.joda.time.LocalDateTime;
import seoclarity.backend.dao.actonia.BotManagerDao;
import seoclarity.backend.entity.actonia.BotManagerEntity;
import seoclarity.backend.utils.CollectionUtils;
import seoclarity.backend.utils.SpringBeanFactory;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 修改记录：
 *  ewain 20200827:
 *      1.script_running_detail表增加字段存储文件名，使程序可以支持单个domain下多个文件异步解析log，不同文件名的log不会产生关联影响
 *  ewain 20200924：
 *      1.解析json文件时，bot_date的值来源更改，上个版本为参数传入，自本次版本更新之后，此值来源json文件中的log-date字段！
 *      2.增加isTestFlag字段，默认值为false。测试环境下可以在相对应的 bot reference 构造函数初始化此值，测试环境下不更新和检查script_running_detail表
 *
 *  计划：增加对多文件domain每一个文件分别重跑的功能，execBot方法做重载，增加fileName参数。
 *
 *  ewain 20210224：
 *      计划 增加对日志目录的支持，日志目录可以包含无限多个文件, 当log为日志目录时，isDynamicFileName()返回 false, isFolder()返回 true
 *
 *  ewain 20220728
 *      isFolder和 isMultiFile有很大区别, isFolder用于同一日期同一文件名下有多个文件  isMultiFile用于同一日期有多个不同文件名.
 */

@CommonsLog
public class BotManager {

    protected static SimpleDateFormat formatterDateTime = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    protected static SimpleDateFormat formatterDate = new SimpleDateFormat("yyyy-MM-dd");

    private BotManagerDao botManagerDao;

    public BotManager() {

        botManagerDao = SpringBeanFactory.getBean("botManagerDao");
    }

    /**
     * @param args 参数
     *             1. 0    backProcess：是否重跑；可选值：true 重跑，false 不重跑
     *             2. 1    botType：bot 单个执行还是分组执行；可选值：bot, group
     *             3. 2    name：botName(全类名)或者groupName ,可以多bot，也可以多分组
     *             4. 3    date：执行时间，可以一天，也可以一段时间，定时任务执行时任意值
     *             5. 4    version：执行版本，只有重跑的时候才会放版本号，定时任务执行时任意值
     *
     * @Example
     *      nohup /bin/mvn -f /home/<USER>/source/ewain/dev-clarity-backend-scripts/pom.xml exec:java
     *          -Dexec.mainClass="com.actonia.subserver.tools.upload.newbot.BotManager"
     *          -Dexec.args="true bot com.actonia.subserver.tools.upload.newbot.Extract8422BotData 2021-04-07 0"
     *          -Dexec.cleanupDaemonThreads=false >> log/Extract8422BotData-01.log 2>&1 &
     *      nohup /usr/bin/mvn -f /home/<USER>/source/botGroupUpload/clarity-backend-scripts/pom.xml exec:java -Dexec.mainClass="com.actonia.subserver.tools.upload.newbot.BotManager" -Dexec.cleanupDaemonThreads=false -Dexec.args="false group 100" -Dexec.cleanupDaemonThreads=false >> log/botManager-01.log 2>&1 &
     *
     *      process single domain: nohup /usr/bin/mvn -f /home/<USER>/source/ewain/dev-clarity-backend-scripts/pom.xml exec:java -Dexec.mainClass="com.actonia.subserver.tools.upload.newbot.BotManager" -Dexec.cleanupDaemonThreads=false -Dexec.args="true bot com.actonia.subserver.tools.upload.newbot.Extract11739BotData 2023-02-17 0" -Dexec.cleanupDaemonThreads=false >> log/Extract11739BotData-01.log 2>&1 &
     *
     * @throws Exception 异常机制：涉及参数的异常会抛出，bot内部异常catch，防止阻塞其他bot执行，但会打出错误日志。
     */
    public static void main(String[] args) throws Exception {
        BotManager botManager = new BotManager();
        //log.info("+++++ log output +++++" + args.toString());
        LocalDateTime now = new LocalDateTime();
        log.info("======================= log output ======================= execTime: " + now.toString("yyyy-MM-dd HH:mm:ss"));
        if (null != args && args.length != 0) {
            if ("true".equals(args[0])) {
                //重跑1
                if ("group".equals(args[1])) {
                    //分组执行重跑
                    String[] groups = args[2].split(",");
                    String[] dates = args[3].split(",");
                    List<Integer> groupIntList = BotManager.strArrParseToIntList(groups);
                    ArrayList<String> dateStrList = new ArrayList<String>(dates.length);
                    Collections.addAll(dateStrList, dates);
                    Integer version = Integer.parseInt(args[4]);
                    //执行调用
                    botManager.loopGroup(true, groupIntList, dateStrList, version);
                } else if ("bot".equals(args[1])) {
                    //bot执行重跑
                    String[] botNames = args[2].split(",");
                    ArrayList<String> clazzStrList = new ArrayList<String>(botNames.length);
                    Collections.addAll(clazzStrList, botNames);
                    String[] dates = args[3].split(",");
                    ArrayList<String> dateStrList = new ArrayList<String>(dates.length);
                    Collections.addAll(dateStrList, dates);
                    Integer version = Integer.parseInt(args[4]);
                    //调用执行
                    botManager.loopBot(true, dateStrList, clazzStrList, version);
                }
            } else if ("false".equals(args[0])) {
                //定时任务执行
                if ("group".equals(args[1])) {
                    //分组执行定时任务

                    String[] groups = args[2].split(",");
                    List<Integer> groupIntList = BotManager.strArrParseToIntList(groups);

                    ArrayList<String> dateStrList = new ArrayList<String>();
                    Date lastDateTime = DateUtils.addDays(new Date(), -1);
                    dateStrList.add(formatterDateTime.format(lastDateTime));

                    //执行调用
                    botManager.loopGroup(false, groupIntList, dateStrList, 0);
                } else if ("bot".equals(args[1])) {
                    //bot执行定时任务
                    String[] botNames = args[2].split(",");
                    ArrayList<String> clazzStrList = new ArrayList<String>(botNames.length);
                    Collections.addAll(clazzStrList, botNames);
                    ArrayList<String> dateStrList = new ArrayList<String>();
                    Date lastDateTime = DateUtils.addDays(new Date(), -1);
                    dateStrList.add(formatterDateTime.format(lastDateTime));
                    //调用执行
                    botManager.loopBot(false, dateStrList, clazzStrList, 0);
                } else {
                    throw new Exception("error type");
                }
            } else {
                throw new Exception("error backProcess params");
            }
        } else {
            //空参数
            //定时任务 执行全部bot
            Date lastDateTime = DateUtils.addDays(new Date(), -1);
            ArrayList<String> dateStrList = new ArrayList<String>();
            dateStrList.add(formatterDateTime.format(lastDateTime));
            botManager.execAllBot(false, dateStrList, 0, botManager);
        }
    }

    /**
     * 字符串数组转List
     *
     * @param arr 字符数组 {@link String[]}
     * @return 集合 {@link List<Integer>}
     */
    public static List<Integer> strArrParseToIntList(String[] arr) {
        List<Integer> integerList = new ArrayList<>(arr.length);
        for (String str : arr) {
            Integer i = Integer.parseInt(str);
            integerList.add(i);
        }
        log.info("+++++ log output +++++" + "str arr[] TO List--->" + integerList);
        return integerList;
    }

    /**
     * (定时任务)执行全部 bot
     *
     * @param backProcess 是否重跑 {@link Boolean}
     * @param dateStrList 文件时间 {@link List<String>}
     * @param version     DB版本号 {@link Integer}
     * @param botManager  {@link BotManager}实例
     * @throws ParseException
     */
    private void execAllBot(Boolean backProcess, ArrayList<String> dateStrList, Integer version, BotManager botManager) throws ParseException {

        List<BotManagerEntity> botGroupList = botManagerDao.getBotEntityByIsEnable(true);

        List<Integer> groupIntList = botGroupList.stream().map(BotManagerEntity::getUploadGroup).collect(Collectors.toList());
        botManager.loopGroup(backProcess, groupIntList, dateStrList, version);
    }

    /**
     * 执行一段时间内所选列表 bot
     *
     * @param backProcess  是否重跑 {@link Boolean}
     * @param groupIntList 分组名称 {@link List<Integer>}
     * @param dateStrList  文件时间 {@link List<String>}
     * @param version      DB版本号 {@link Integer}
     * @throws ParseException 抛出时间解析异常
     */
    public void loopGroup(Boolean backProcess, List<Integer> groupIntList, List<String> dateStrList, Integer version) throws ParseException {
        log.info("+++++ log output +++++" + "start loop group");
        List<Date> dateList = new ArrayList<>();
        for (String dateStr : dateStrList) {
            dateList.add(formatterDate.parse(dateStr));
        }
        Date sDate = dateList.get(0);
        if (dateList.size() > 1) {
            Date eDate = dateList.get(1);
            while (sDate.compareTo(eDate) <= 0) {
                for (Integer groupInt : groupIntList) {
                    execGroup(backProcess, groupInt, sDate, version);
                }
                sDate = DateUtils.addDays(sDate, 1);
            }
        } else {
            for (Integer groupInt : groupIntList) {
                execGroup(backProcess, groupInt, sDate, version);
            }
        }
    }

    /**
     * 执行单个 bot
     *
     * @param backProcess  是否重跑 {@link Boolean}
     * @param dateStrList  文件时间 {@link List<String>}
     * @param clazzStrList bot全类名集合 {@link List<String>}
     * @param version      DB版本号 {@link Integer}
     * @throws ParseException 抛出时间解析异常
     */
    public void loopBot(Boolean backProcess, List<String> dateStrList, List<String> clazzStrList, Integer version) throws ParseException {
        log.info("+++++ log output +++++" + "start loop bot");
        List<Date> dateList = new ArrayList<>();
        for (String dateStr : dateStrList) {
            dateList.add(formatterDate.parse(dateStr));
        }
        Date sDate = dateList.get(0);
        if (dateList.size() > 1) {
            Date eDate = dateList.get(1);
            while (sDate.compareTo(eDate) <= 0) {
                for (String clazzStr : clazzStrList) {
                    execBot(backProcess, clazzStr, sDate, version);
                }
                sDate = DateUtils.addDays(sDate, 1);
            }
        } else {
            for (String clazzStr : clazzStrList) {
                execBot(backProcess, clazzStr, sDate, version);
            }
        }
    }

    private void execGroup(Boolean backProcess, Integer groupInt, Date sDate, Integer version) {
        log.info("+++++ log output +++++" + "start process group");

        List<BotManagerEntity> botList = botManagerDao.getBotEntityByGroupNameAndIsEnable(groupInt, true);
        for (BotManagerEntity bot : botList) {
            execBot(backProcess, bot.getFullQualifiedClass(), sDate, version);
        }
    }

    private void execBot(Boolean backProcess, String clazzStr, Date date, Integer version) {
        log.info("+++++ log output +++++" + "start process bot");
        try {
            Class<?> clazz = Class.forName(clazzStr);
            Method initParam = clazz.getMethod("initParam", Date.class, Integer.TYPE);
            Object o = clazz.newInstance();
            initParam.invoke(o, date, version);
            Method exec = clazz.getMethod("exec", Boolean.TYPE, AbstractExtractBotCommonJsonFile.class);
            exec.invoke(o, backProcess, o);
        } catch (ClassNotFoundException e) {
            log.error(clazzStr + " ClassNotFoundException:" + CollectionUtils.getErrorMsg(e));
        } catch (NoSuchMethodException e) {
            log.error(clazzStr + " NoSuchMethodException:" + CollectionUtils.getErrorMsg(e));
        } catch (IllegalAccessException e) {
            log.error(clazzStr + " IllegalAccessException:" + CollectionUtils.getErrorMsg(e));
        } catch (InvocationTargetException e) {
            e.printStackTrace();
            log.error(clazzStr + " InvocationTargetException:" + CollectionUtils.getErrorMsg(e));
        } catch (InstantiationException e) {
            log.error(clazzStr + " InstantiationException:" + CollectionUtils.getErrorMsg(e));
        } catch (Exception e) {
            e.printStackTrace();
            log.error(clazzStr + " Exception:" + CollectionUtils.getErrorMsg(e));
        }
    }
}
