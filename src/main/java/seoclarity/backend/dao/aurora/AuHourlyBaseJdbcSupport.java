/**
 *
 */
package seoclarity.backend.dao.aurora;

import javax.annotation.Resource;
import javax.sql.DataSource;

import seoclarity.backend.dao.BaseJdbcSupport;


/**
 * base jdbc support
 * use spring jdbcdaosupport as base class
 */
public abstract class AuHourlyBaseJdbcSupport<T> extends BaseJdbcSupport<T> {
	
	@Resource(name="auroraActoniaHourlyRankDataSource")
    private DataSource dataSource;

	@Override
	public DataSource getDataSource() {
		return this.dataSource;
	}
	
	

}
