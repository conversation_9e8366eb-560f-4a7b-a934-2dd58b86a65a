/**
 *
 */
package seoclarity.backend.dao.aurora;

import javax.annotation.Resource;
import javax.sql.DataSource;

import seoclarity.backend.dao.BaseJdbcSupport;


/**
 * base jdbc support
 * use spring jdbcdaosupport as base class
 */
public abstract class AuBaseJdbcSupport<T> extends BaseJdbcSupport<T> {
	
	@Resource(name="auroraActoniaDataSource")
    private DataSource dataSource;

	@Override
	public DataSource getDataSource() {
		return this.dataSource;
	}
	
	

}
