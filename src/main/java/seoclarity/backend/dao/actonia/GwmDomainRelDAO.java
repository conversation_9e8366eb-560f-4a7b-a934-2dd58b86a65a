package seoclarity.backend.dao.actonia;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.actonia.GWMDomainRel;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;

@Repository
public class GwmDomainRelDAO extends ActoniaBaseJdbcSupport<GWMDomainRel> {

    @Override
    public String getTableName() {
        return "gwm_domain_rel";
    }

    public String getProfileById(int id) {
        String sql = "select gwm_domain_name from gwm_domain_rel where id = ? ";
        return queryForString(sql, id);
    }

    public String getProfileByRelId(int relId) {
        String sql = "select gwm_domain_name from gwm_domain_rel_log where rel_id = ? and operation_type = 1 order by created_at limit 1";
        return queryForString(sql, relId);
    }

    public List<Integer> getProfilesByDomainId(int domainId) {
        String sql = "select id from " + getTableName() + " where own_domain_id = ?";
        return queryForIntegerList(sql, domainId);
    }

    public List<GWMDomainRel> getByDomainId(Integer id, int dataSource, String countryCode, Set<String> urls) {
        String sql = " select *  FROM " + getTableName() + " WHERE own_domain_id = ? and data_source = ? and gwm_country_code = ? and gwm_domain_name in('" + StringUtils.join(urls, "','") + "') ";
        return this.findBySql(sql, id, dataSource, countryCode);
    }

    public void insertData(List<GWMDomainRel> gwmDomainRels) {

        String sql = "insert into " + getTableName() + " (own_domain_id, gwm_domain_name, data_source, gwm_country_code, create_date, default_domain"
                + ") values (?, ?, ?, ?, ?, ?) ";
        List<Object[]> batch = new ArrayList<Object[]>();
        for (GWMDomainRel entity : gwmDomainRels) {
            batch.add(new Object[]{
                    entity.getOwnDomainId(),
                    entity.getGwmDomainName(),
                    entity.getDataSource(),
                    entity.getGwmCountryCode(),
                    new Date(),
                    null
            });
        }
        this.executeBatch(sql, batch);
    }

    public List<GWMDomainRel> findByReferDomainId(int domainId) {
        final String sql = "select rel.*, gm.refer_profile_id, gm.base_profile_id, gm.base_domain_id from gwm_domain_rel rel" +
                " left join gsc_multi_domain_profile gm " +
                " on rel.id = gm.refer_profile_id and gm.refer_domain_id = ? " +
                " where data_source = 0 and rel.own_domain_id = ? ";

        return this.findBySql(sql, domainId, domainId);
    }
}
