package seoclarity.backend.dao.actonia;

import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.actonia.PageOptimizationCaRel;

import java.util.List;
import java.util.stream.Collectors;

@Repository
public class PageOptimizationCaRelDAO extends ActoniaBaseJdbcSupport<PageOptimizationCaRel> {

    private static final String ID = "id";
    private static final String OPTIMIZATION_ID = "optimizationId";
    private static final String CONTENT_ASSISTANT_ID = "contentAssistantId";
    private static final String CREATE_USER = "createUser";
    private static final String CREATE_DATE = "createDate";

    @Override
    public String getTableName() {
        return "page_optimization_ca_rel";
    }

    public void createBatch(List<PageOptimizationCaRel> relList) {
        final String sql = "INSERT INTO " + getTableName() + " (" +
                OPTIMIZATION_ID + "," + CONTENT_ASSISTANT_ID + ", " + CREATE_USER + ", " + CREATE_DATE + ") VALUES(?,?,?,?)";
        final List<Object[]> data = relList.stream().map(rel -> new Object[]{
                rel.getOptimizationId(),
                rel.getContentAssistantId(),
                rel.getCreateUser(),
                rel.getCreateDate()
        }).collect(Collectors.toList());
        super.executeBatch(sql, data);
    }
}