package seoclarity.backend.dao.actonia;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;

import seoclarity.backend.entity.actonia.ResourceBatchDetailEntity;
@Repository
public class ResourceBatchDetailEntityDAO extends ActoniaBaseJdbcSupport<ResourceBatchDetailEntity>{

    @Override
    public String getTableName() {
        return "queue_base_detail";
    }


    public void insertBatchIgnoreDup(List<ResourceBatchDetailEntity> detailList) {
        String sql = "insert ignore into " + getTableName() + " (infoId, actionType, ownDomainId,resourceId,resourceSubId,resourceMain,resourceSubordinate,resourceSearchengines,resourceMd5,resourceCategory,createDate,resourceAdditional)" +
                " values(?,?,?,?,?,?,?,?,?,?,?,?) ";
        List<Object[]> batch = new ArrayList<Object[]>();
        for (ResourceBatchDetailEntity detailEntity : detailList) {
            Object[] values = new Object[]{ detailEntity.getInfoId(), detailEntity.getActionType(), detailEntity.getOwnDomainId(),detailEntity.getResourceId(),detailEntity.getResourceSubId(),
                    detailEntity.getResourceMain(), detailEntity.getResourceSubordinate(),detailEntity.getResourceSearchengines(),detailEntity.getResourceMd5(), detailEntity.getResourceCategory(), detailEntity.getCreateDate(),detailEntity.getResourceAdditional()};
            batch.add(values);
        }
        executeBatch(sql, batch);
    }


    public List<ResourceBatchDetailEntity> getDetailByInfoId(long id) {
        StringBuilder sbd = new StringBuilder();
        sbd.append(" select * from ").append(getTableName());
        sbd.append(" where infoId = ? ");
        sbd.append(" and ( status = ? or status = ? )");
        sbd.append(" order by id ");
        return this.findBySql(sbd.toString(), id, ResourceBatchDetailEntity.STATUS_CREATED, ResourceBatchDetailEntity.STATUS_ERROR);
    }

    public int getNotCompletedDetailCntByInfoId(long id) {
        StringBuilder sbd = new StringBuilder();
        sbd.append(" select count(*) from ").append(getTableName());
        sbd.append(" where infoId = ? ");
        sbd.append(" and ( status = ? or status = ? )");
        Integer cnt = this.queryForInteger(sbd.toString(), id, ResourceBatchDetailEntity.STATUS_CREATED, ResourceBatchDetailEntity.STATUS_ERROR);
        return cnt == null ? 0 : cnt.intValue();
    }

    public void updateDetailInfo(List<Long> detailIds, int status, String message) {
        StringBuilder sbd = new StringBuilder();
        sbd.append(" update ").append(getTableName());
        sbd.append(" set status = ?").append( " , errorMessage = ? ").append(" , processDate = ?");
        sbd.append(" where id in (").append(StringUtils.join(detailIds, ",")).append(")");
        this.executeUpdate(sbd.toString(), status, message, new Date());
    }


	public void updateDetail() {
		String sql = "delete from queue_base_info  where id in (168600, 168601, 168602) and ownDomainId = 8422  ";
		this.executeUpdate(sql);
		
		String sql2 = "delete from  queue_base_detail  where  infoId in (168600, 168601, 168602) and ownDomainId = 8422 ";
		this.executeUpdate(sql2);
		
	}


    public List<ResourceBatchDetailEntity> getAllByInfoId(int infoId) {
        StringBuilder sbd = new StringBuilder();
        sbd.append(" select * from ").append(getTableName());
        sbd.append(" where infoId = ? ");
        sbd.append(" order by id ");
        return this.findBySql(sbd.toString(), infoId);
    }
}
