package seoclarity.backend.dao.actonia;

import java.math.BigInteger;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;

import seoclarity.backend.entity.actonia.Clarity360ReportEntity;

@Repository
public class Clarity360ReportEntityDAO extends ActoniaBaseJdbcSupport<Clarity360ReportEntity> {

	public String getTableName() {
		return "clarity_360_report";
	}

	public Integer insert(Clarity360ReportEntity report) {

		Map<String, Object> parameters = new HashMap<String, Object>();

		parameters.put("ownDomainId", report.getOwnDomainId());
		//Cee : testing 360 v3 on Dev, and need to hide v3 report on App
		//parameters.put("enabled", 3);
		parameters.put("enabled", 1);
		parameters.put("title", report.getTitle());
		parameters.put("siteAuditCrawlId", report.getSiteAuditCrawlId());
		parameters.put("siteAuditProjectId", report.getSiteAuditProjectId());
		parameters.put("metricsDateRange", report.getMetricsDateRange());
		parameters.put("startDate", report.getStartDate());
		parameters.put("endDate", report.getEndDate());
		parameters.put("paramJson", report.getParamJson());
		parameters.put("paramHash", report.getParamHash());
		parameters.put("createUserId", report.getCreateUserId());
		parameters.put("createDate", new Date());

		//Cee - https://www.wrike.com/open.htm?id=1318497805
		if (report.getGenerateBy() != null) {
			parameters.put("generateBy", report.getGenerateBy());
		}

		//Cee - https://www.wrike.com/open.htm?id=1363063180
		if (StringUtils.isNotBlank(report.getClarity360SummaryHashType())) {
			parameters.put("clarity360SummaryHashType", report.getClarity360SummaryHashType());
		}

		//Cee - https://www.wrike.com/open.htm?id=1275157543
		if (report.getProcessStatus() == null) {
			parameters.put("processStatus", Clarity360ReportEntity.PROCESS_STATUS_NEED_PROCESS);
		} else {
			parameters.put("processStatus", report.getProcessStatus());
		}

		//Cee - https://www.wrike.com/open.htm?id=1268832383
		if (report.getProjectId() != null) {
			parameters.put("projectId", report.getProjectId());
		} else {
			parameters.put("projectId", 0); //Developing on dev, App may throw Error, it can be removed later
		}

		if (report.getVersion() != null) {
			parameters.put("version", report.getVersion());
		} else {
			parameters.put("version", Clarity360ReportEntity.VERSION_1);
		}

		if (StringUtils.isNotBlank(report.getUiJson())) {
			parameters.put("uiJson", report.getUiJson());
		}

		if (report.getStageEnabled() != null) {
			parameters.put("stageEnabled", report.getStageEnabled());
		}

		if (report.getSiteAuditCompletedDate() != null) {
			parameters.put("siteAuditCompletedDate", report.getSiteAuditCompletedDate());
		}

		Integer newId = insert(parameters);
		return newId;
	}

	public List<Clarity360ReportEntity> getNeedProcessReportList() {
		String sql = "select * from clarity_360_report where enabled >= ? and (processStatus = ? or (processStatus = ? and stageEnabled = ? and currentAvailableStage = ?))";
		return findBySql(sql, Clarity360ReportEntity.ENABLED, Clarity360ReportEntity.PROCESS_STATUS_NEED_PROCESS, 
				Clarity360ReportEntity.PROCESS_STATUS_DONE, Clarity360ReportEntity.STAGE_ENABLED, Clarity360ReportEntity.CURRENT_STAGE_1);
	}
	
	public Clarity360ReportEntity getNeedProcessReport() {
		String sql = "select * from " + getTableName() + " where processStatus = ? and enabled >= ? limit 1";
		
		List<Clarity360ReportEntity> resultList = findBySql(sql, Clarity360ReportEntity.PROCESS_STATUS_NEED_PROCESS, Clarity360ReportEntity.ENABLED);
		
		if (resultList != null && resultList.size() > 0) {
			return resultList.get(0);
		}
		return null;
	}
	
	
	public Clarity360ReportEntity getReportById(Integer reportId, Integer domainId) {
		String sql = "select * from " + getTableName() + " where id = ? and ownDomainId = ?";
		
		List<Clarity360ReportEntity> resultList = findBySql(sql, reportId, domainId);
		if (resultList != null && resultList.size() > 0) {
			return resultList.get(0);
		}
		return null;
	}
	
	public Clarity360ReportEntity getReportById(Integer reportId) {
		String sql = "select * from " + getTableName() + " where id = ? ";
		
		List<Clarity360ReportEntity> resultList = findBySql(sql, reportId);
		if (resultList != null && resultList.size() > 0) {
			return resultList.get(0);
		}
		return null;
	}
	
	public void updateStatusAndStartDate(Integer status, Integer reportId) {
		StringBuffer sql = new StringBuffer();
		sql.append(" update " + getTableName() + " set processStatus = ?, processStartDate = ? where id = ? limit 1");
		
		System.out.println(sql.toString() + " " + status + "-" + reportId);
		this.executeUpdate(sql.toString(), status, new Date(), reportId);
	}

	public void updateProcessStatus(int id, int ownDomainId, int processStatus, Date siteAuditCompletedDate) {
		String sql = "update " + getTableName() + " set processStatus = ?, siteAuditCompletedDate = ? where id = ? and ownDomainId = ? ";
		this.executeUpdate(sql, processStatus, siteAuditCompletedDate, id, ownDomainId);
	}

	public void updateSDateAndEDate(int id, int ownDomainId, Date startDate, Date endDate) {
		String sql = "update " + getTableName() + " set startDate = ?, endDate = ? where id = ? and ownDomainId = ? ";
		this.executeUpdate(sql, startDate, endDate, id, ownDomainId);
	}

	public void updateParamJson(int id, int ownDomainId, String paramJson, int processStatus, Date siteAuditCompletedDate) {
		String sql = "update " + getTableName() + " set paramJson = ?, processStatus = ?, siteAuditCompletedDate = ? where id = ? and ownDomainId = ? ";
		this.executeUpdate(sql, paramJson, processStatus, siteAuditCompletedDate, id, ownDomainId);
	}

	public void updateParamJsonAndDate(Integer id, Integer ownDomainId, String paramJson, Date startDate, Date endDate, int processStatus, Date siteAuditCompletedDate) {
		String sql = "update " + getTableName() + " set paramJson = ?, startDate = ?, endDate = ?, processStatus = ?, siteAuditCompletedDate = ? where id = ? and ownDomainId = ? ";
		this.executeUpdate(sql, paramJson, startDate, endDate, processStatus, siteAuditCompletedDate, id, ownDomainId);
	}

	public void updateStatusAndEndDate(Integer status, Integer reportId) {
		StringBuffer sql = new StringBuffer();
		sql.append(" update " + getTableName() + " set processStatus = ?, processEndDate = ? where id = ? limit 1");
		
		System.out.println(sql.toString() + " " + status + "-" + reportId);
		this.executeUpdate(sql.toString(), status, new Date(), reportId);
	}

	public Clarity360ReportEntity get360ReportByCrawlRequest(Integer ownDomainId, Integer crawlRequestLogId, BigInteger crawlRequestProjectId) {
		String sql =
				"select c3r.id, crl.own_domain_id ownDomainId, crl.id siteAuditCrawlId, c3r.metricsDateRange metricsDateRange, c3r.uiJson uiJson, c3r.paramJson paramJson, c3r.startDate, c3r.endDate, c3r.generateBy  " +
				"from crawl_request_log crl  " +
				"left join clarity_360_project c3p on crl.own_domain_id = c3p.ownDomainId  " +
				"left join clarity_360_report c3r on c3p.id = c3r.projectId and crl.project_id = c3r.siteAuditProjectId and crl.id = c3r.siteAuditCrawlId and crl.own_domain_id = c3r.ownDomainId and c3p.ownDomainId = c3r.ownDomainId " +
				"where crl.own_domain_id = ? and crl.id = ? and crl.project_id = ? and c3r.processStatus = ? and (c3r.generateBy = ? or c3r.generateBy = ?)  ";
		return findObject(sql, ownDomainId, crawlRequestLogId, crawlRequestProjectId, Clarity360ReportEntity.PROCESS_STATUS_SCHEDULE, Clarity360ReportEntity.GENERATE_BY_SITE_AUDIT, Clarity360ReportEntity.GENERATE_BY_SITE_AUDIT_GLOBAL);
	}

	
	public void updateStage1Detail(int id, int ownDomainId, Date startDate, Date endDate, Integer stage, Integer stageStatus) {
		String sql = "update " + getTableName() + " set currentAvailableStage = ?, stage1Status = ?, stage1ProcessStartTime = ?, stage1ProcessEndTime = ? "
				+ " where id = ? and ownDomainId = ? ";

		System.out.println("SQL:" + sql);
		System.out.println("stage:" + stage);
		System.out.println("stageStatus:" + stageStatus);
		System.out.println("startDate:" + startDate);
		System.out.println("endDate:" + endDate);
		System.out.println("id:" + id);
		System.out.println("ownDomainId:" + ownDomainId);
		this.executeUpdate(sql, stage, stageStatus, startDate, endDate, id, ownDomainId);
	}
	
	public void updateStage2Detail(int id, int ownDomainId, Date startDate, Date endDate, Integer stage, Integer stageStatus) {
		String sql = "update " + getTableName() + " set currentAvailableStage = ?, stage2Status = ?, stage2ProcessStartTime = ?, stage2ProcessEndTime = ? "
				+ " where id = ? and ownDomainId = ? ";

		System.out.println("SQL:" + sql);
		System.out.println("stage:" + stage);
		System.out.println("stageStatus:" + stageStatus);
		System.out.println("startDate:" + startDate);
		System.out.println("endDate:" + endDate);
		System.out.println("id:" + id);
		System.out.println("ownDomainId:" + ownDomainId);
		this.executeUpdate(sql, stage, stageStatus, startDate, endDate, id, ownDomainId);
	}

	public void updateStageStatus(int id, int ownDomainId, Integer stage, Integer stageStatus) {
		String sql = "update " + getTableName() + " set " + ((stage == Clarity360ReportEntity.CURRENT_STAGE_1) ? "stage1Status" : "stage2Status") + " = ? "
				+ " where id = ? and ownDomainId = ? ";
		this.executeUpdate(sql,  stageStatus,  id, ownDomainId);
	}

    public Clarity360ReportEntity getByTitle(int ownDomainId, int projectId, String title) {
        String sql = "select * from clarity_360_report where ownDomainId = ? and projectId = ? and title = ? ";
        return findObject(sql, ownDomainId, projectId, title);
    }
}
