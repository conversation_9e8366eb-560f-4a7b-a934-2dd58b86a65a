package seoclarity.backend.dao.actonia.extract;

import org.springframework.stereotype.Repository;
import seoclarity.backend.dao.actonia.ActoniaBaseJdbcSupport;
import seoclarity.backend.entity.actonia.extract.ExtractScriptConfigEntity;

import java.util.List;

@Repository
public final class ExtractScriptConfigDAO extends ActoniaBaseJdbcSupport<ExtractScriptConfigEntity> {

    @Override
    public String getTableName() {
        return "extract_script_config";
    }


    public ExtractScriptConfigEntity getUniqueExtractScriptConfig(int category, String projectName, String fullQulifiedClass,
                                                                  String specialCategory, int defaultFrequency) {
        StringBuilder sql = new StringBuilder();
        sql.append(" select t1.id,t1.defaultFrequency,t1.defaultStartDay,t1.groupId,t1.defaultStartProcessHour,t1.defaultExpectedProcessHour ");
        sql.append(" ,t1.mailTitle,t1.mailTo,mailCc");
        sql.append(" from ").append(getTableName()).append(" t1 ");
        sql.append(" where t1.enabled = ? and t1.category = ? and t1.projectName = ? and t1.fullQulifiedClass = ? ");
        sql.append(" and t1.specialCategory = ? and t1.defaultFrequency = ? ");
        return findObject(sql.toString(), ExtractScriptConfigEntity.ENABLED, category, projectName, fullQulifiedClass, specialCategory, defaultFrequency);
    }

    public List<ExtractScriptConfigEntity> getConfigList(){
        StringBuilder sql = new StringBuilder();
        sql.append(" select * ");
        sql.append(" from ").append(getTableName()).append(" t1 ");
        sql.append(" where t1.enabled = ? and groupId = 110 ");//todo test groupId = 110
        return findBySql(sql.toString(), ExtractScriptConfigEntity.ENABLED);
    }

}
