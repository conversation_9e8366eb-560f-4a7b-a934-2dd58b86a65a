/**
 *
 */
package seoclarity.backend.dao.actonia;

import com.alibaba.fastjson.JSON;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import seoclarity.backend.entity.actonia.OwnDomainSettingEntity;
import seoclarity.backend.entity.actonia.TargetUrlEntity;
import seoclarity.backend.sender.pagespeed.SendPageSpeedTargetUrlsSQS;
import seoclarity.backend.utils.Constants;
import seoclarity.backend.utils.FormatUtils;

import java.util.*;

import static seoclarity.backend.entity.actonia.OwnDomainSettingEntity.*;

/**
 * com.actonia.subserver.dao.TargetUrlEntityDAO.java
 *
 * @version $Revision: 154227 $ $Author: limt $
 */
@SuppressWarnings("unchecked")
@Repository
public class TargetUrlEntityDAO extends ActoniaBaseJdbcSupport<TargetUrlEntity> {

	private static String GET_ID_URL_BY_DOMAIN_ID;
	private static String GET_URL_BY_DOMAIN_ID;
	private static String GET_BY_DOMAIN_ID_TRAFFIC_DATE_URL_SQL;
	private static String UPDATE_URL_FOR_BATCH_SQL;
	private static String SQL_GET_BY_RECORD_ID;
	private static String SQL_GET_MANAGED_URL_ID_BY_DOMAIN;
	private static String SQL_GET_ID_URL_OPTIONS_BY_DOMAIN;
	private static String GET_MANAGED_ID_URL_MAP_BY_DOMAIN_ID;

	@Override
	public String getTableName() {
		return "jason_test0412_t_target_url";
	}

	public TargetUrlEntity getById(long id) {
		String sql = "select * from t_target_url where id = ?";
		return findObject(sql, id);
	}
	
	public TargetUrlEntity getByLongId(Long id) {
		String sql = "select * from t_target_url where id = ?";
		return findObject(sql, id);
	}

	public TargetUrlEntity getById(long id, int ownDomainId) {
		String sql = "select * from t_target_url where id = ? and own_domain_id = ?";
		return findObject(sql, id, ownDomainId);
	}

	public Long insert(TargetUrlEntity targetUrlEntity) {
		Map<String, Object> val = new HashMap<String, Object>();
		val.put("own_domain_id", targetUrlEntity.getOwnDomainId());
		val.put("url", targetUrlEntity.getUrl());
		val.put("create_date", targetUrlEntity.getCreateDate());
		val.put("type", targetUrlEntity.getType());
		val.put("status", targetUrlEntity.getStatus());

		return insertForLongId(val);
	}

	public Integer getManagedUrlCountByDomainId(int domainId) {
		String sql = "select count(*) from t_target_url where own_domain_id=? and  status = ? and  type=?";
		return queryForInteger(sql, domainId, Constants.TARGET_URL_STATUS_ACTIVE, Constants.TARGET_URL_TYPE_ADDED_BY_USER);
	}
	
	
	
	public List<TargetUrlEntity> getByUrlAndDomainId(int ownDomainId, String contentTypeName, Integer dynamicFlg, String dtPrefix, Integer tagType) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select u.url, gt.tag_name as tagName from t_group_tag gt ");
		sql.append(" join t_group_tag_relation gtr  ");
		sql.append(" on gt.id = gtr.group_tag_id ");
		sql.append(" join t_target_url u  ");
		sql.append(" on u.id = gtr.resource_id  ");
		sql.append(" where gt.tag_type = ? and gt.domain_id = " + ownDomainId + " and tag_name like '" + contentTypeName + "%" + dtPrefix + "'  ");
		sql.append(" and u.own_domain_id = " + ownDomainId + " and u.status = ? and u.type = ? ");
		sql.append("  and gtr.domain_id = " + ownDomainId + " and gtr.resource_type = ? and gt.dynamic_flg = ? ");
		return this.findBySql(sql.toString(), tagType, TargetUrlEntity.STATUS_ACTIVE, 
				TargetUrlEntity.STATUS_ACTIVE, tagType, dynamicFlg);
	}

	public List<String> getUrls(int domainId) {
		StringBuilder sql = new StringBuilder();
		sql.append(" select url ");
		sql.append(" from t_target_url ");
		sql.append(" where 1 = 1 ");
		sql.append(" and own_domain_id = ? ");
		sql.append(" and type = ").append(TargetUrlEntity.TYPE_ADD_BY_USER);
		sql.append(" and status = ").append(TargetUrlEntity.STATUS_ACTIVE);
		return this.queryForStringList(sql.toString(),domainId);
	}

	public List<TargetUrlEntity> getUrlsByDomain(int domainId) {
		StringBuilder sql = new StringBuilder();
		sql.append(" select * ");
		sql.append(" from t_target_url ");
		sql.append(" where 1 = 1 ");
		sql.append(" and own_domain_id = ? ");
		sql.append(" and type = ").append(TargetUrlEntity.TYPE_ADD_BY_USER);
		sql.append(" and status = ").append(TargetUrlEntity.STATUS_ACTIVE);
		return this.findBySql(sql.toString(),domainId);
	}

	public List<String> getUrlListByDomain(Integer domainId, int pageSize) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select url from t_target_url    ");
		sql.append(" WHERE `status` = ? AND type = ? and ifNull(disable_crawl,0)=0 ");
		sql.append(" AND own_domain_id =  " + domainId);
		sql.append(" limit ? ");
		return queryForStringList(sql.toString(), Constants.TARGET_URL_TYPE_ADDED_BY_USER, Constants.TARGET_URL_TYPE_ADDED_BY_USER, pageSize);
	}

	public List<TargetUrlEntity> getTargetUrlsForWeekly(Long minId, Integer pageSize, List<Integer> execDomainList, List<Integer> skipDomainList, int device) {

		List paramList = new ArrayList();

		StringBuffer sql = new StringBuffer();
		sql.append("  select t5.id,t1.domain_id as ownDomainId,t5.url,t3.mobile_domain_flg,t4.enable_moblie,t4.pagespeed_client_apikey ");
		sql.append("  from t_group_tag t1   ");
		sql.append("  INNER JOIN t_group_tag_relation t2 on t1.id = t2.group_tag_id   ");
		sql.append("  INNER JOIN t_own_domain t3 ON t1.domain_id = t3.id   ");
		sql.append("  INNER JOIN t_own_domain_setting t4 ON t4.own_domain_id = t3.id   ");
		sql.append("  INNER JOIN t_target_url t5 on t5.id = t2.resource_id and t5.type = 1 and t5.status = 1   ");
		sql.append("  where t1.enable_pagespeed_pagetag = 1 and t1.tag_type = 1   ");
		sql.append("  and t3.status = 1 and  t4.pagespeed_setting = " + PAGE_SPEED_SETTING_ENABLED_PAGE_TAG + " and t4.pagespeed_frequency = " + PAGE_SPEED_FREQUENCY_WEEKLY);
		sql.append("  and t4.pagespeed_client_apikey is not null and ifNull(t5.disable_crawl,0)=0 ");
		if(device == SendPageSpeedTargetUrlsSQS.DEVICE_MOBILE){
			sql.append(" and t4.pagespeed_device in (" + OwnDomainSettingEntity.PAGE_SPEED_DEVICE_MOBILE + "," + OwnDomainSettingEntity.PAGE_SPEED_DEVICE_ALL + ")");
		}else if(device == SendPageSpeedTargetUrlsSQS.DEVICE_DESKTOP){
			sql.append(" and t4.pagespeed_device in (" + OwnDomainSettingEntity.PAGE_SPEED_DEVICE_DESKTOP + "," + OwnDomainSettingEntity.PAGE_SPEED_DEVICE_ALL + ")");
		}
		if(!CollectionUtils.isEmpty(skipDomainList)){
			sql.append(" and t1.domain_id not in ( ");
			sql.append(StringUtils.join(skipDomainList, ','));
			sql.append(" ) ");
		}
		if(!CollectionUtils.isEmpty(execDomainList)){
			sql.append(" and t1.domain_id in ( ");
			sql.append(StringUtils.join(execDomainList, ','));
			sql.append(" ) ");
		}

		if (minId != null) {
			sql.append(" AND t5.id > ?   ");
			paramList.add(minId);
		}
		sql.append(" order by t5.id  " );
		if (pageSize != null) {
			sql.append(" limit  ? " );
			paramList.add(pageSize);
		}

		System.out.println("=========== getTargetUrlsForWeekly " + sql);
		System.out.println("================= paramList " + JSON.toJSONString(paramList));

		return this.findBySql(sql.toString(), paramList.toArray());
	}

	public List<TargetUrlEntity> getTargetUrlsForMonthlyV2(Long minId, Integer pageSize, Date queryMonthDate,
														   List<Integer> skipDomainList, int device) {

		List paramList = new ArrayList();

		StringBuffer sql = new StringBuffer();
		sql.append(" select * from (   ");
		sql.append(" select t1.id,t1.own_domain_id,t1.url , t2.mobile_domain_flg,t3.enable_moblie");

		sql.append(" from (select id,url,own_domain_id  from t_target_url ");
		sql.append(" where type = 1 and status = 1  AND create_date < ? and ifNull(disable_crawl,0)=0 ");

		paramList.add(FormatUtils.formatDate(FormatUtils.getMonthFirstDay(queryMonthDate), "yyyy-MM-dd"));

		if(!CollectionUtils.isEmpty(skipDomainList)){
			sql.append(" and own_domain_id not in ( ");
			sql.append(StringUtils.join(skipDomainList, ','));
			sql.append(" ) ");
		}
		sql.append(" ) t1   ");
		sql.append(" INNER JOIN t_own_domain t2 ON t1.own_domain_id = t2.id ");
		sql.append(" INNER JOIN t_own_domain_setting t3 ON t3.own_domain_id = t2.id ");
		sql.append(" where t2.status = 1 and t3.pagespeed_setting = " + PAGE_SPEED_SETTING_ENABLED_ALL_PAGE + " and t3.pagespeed_frequency =" + PAGE_SPEED_FREQUENCY_MONTHLY);
		if(device == SendPageSpeedTargetUrlsSQS.DEVICE_MOBILE){
			sql.append(" and t3.pagespeed_device in (" + OwnDomainSettingEntity.PAGE_SPEED_DEVICE_MOBILE + "," + OwnDomainSettingEntity.PAGE_SPEED_DEVICE_ALL + ")");
		}else if(device == SendPageSpeedTargetUrlsSQS.DEVICE_DESKTOP){
			sql.append(" and t3.pagespeed_device in (" + OwnDomainSettingEntity.PAGE_SPEED_DEVICE_DESKTOP + "," + OwnDomainSettingEntity.PAGE_SPEED_DEVICE_ALL + ")");
		}
		sql.append(" union all ");
		sql.append("  select t5.id,t1.domain_id,t5.url,t3.mobile_domain_flg,t4.enable_moblie   ");
		sql.append("  from t_group_tag t1   ");
		sql.append("  INNER JOIN t_group_tag_relation t2 on t1.id = t2.group_tag_id   ");
		sql.append("  INNER JOIN t_own_domain t3 ON t1.domain_id = t3.id   ");
		sql.append("  INNER JOIN t_own_domain_setting t4 ON t4.own_domain_id = t3.id   ");
		sql.append("  INNER JOIN t_target_url t5 on t5.id = t2.resource_id and t5.type = 1 and t5.status = 1   ");
		sql.append("  where t1.enable_pagespeed_pagetag = 1 and t1.tag_type = 1   ");
		sql.append("  and t3.status = 1 and  t4.pagespeed_setting = " + PAGE_SPEED_SETTING_ENABLED_PAGE_TAG + " and t4.pagespeed_frequency = " + PAGE_SPEED_FREQUENCY_MONTHLY);
		sql.append("  and ifNull(t5.disable_crawl,0)=0 ");
		if(device == SendPageSpeedTargetUrlsSQS.DEVICE_MOBILE){
			sql.append(" and t4.pagespeed_device in (" + OwnDomainSettingEntity.PAGE_SPEED_DEVICE_MOBILE + "," + OwnDomainSettingEntity.PAGE_SPEED_DEVICE_ALL + ")");
		}else if(device == SendPageSpeedTargetUrlsSQS.DEVICE_DESKTOP){
			sql.append(" and t4.pagespeed_device in (" + OwnDomainSettingEntity.PAGE_SPEED_DEVICE_DESKTOP + "," + OwnDomainSettingEntity.PAGE_SPEED_DEVICE_ALL + ")");
		}
		sql.append(" )t   ");

		sql.append(" where 1= 1   ");
		if (minId != null) {
			sql.append(" AND id > ?   ");
			paramList.add(minId);
		}
		sql.append(" order by id  " );
		if (pageSize != null) {
			sql.append(" limit  ? " );
			paramList.add(pageSize);
		}

		System.out.println("=========== getTargetUrlsForMonthlyV2 " + sql);
		System.out.println("================= paramList " + JSON.toJSONString(paramList));

		return this.findBySql(sql.toString(), paramList.toArray());
	}
	public List<TargetUrlEntity> getTargetUrlsForMonthlyByDomains(Long minId, Integer pageSize, Date queryMonthDate, List<Integer> execDomainList) {

		List paramList = new ArrayList();

		StringBuffer sql = new StringBuffer();
		sql.append(" select t1.id,t1.own_domain_id,t1.url , t2.mobile_domain_flg,t3.enable_moblie");

		sql.append(" from (select id,url,own_domain_id  from t_target_url ");
		sql.append(" where type = 1 and status = 1  AND create_date <= ? ");
//		sql.append(" and create_date>= '2022-09-01' ");
		paramList.add(FormatUtils.formatDate(queryMonthDate, "yyyy-MM-dd"));

//		sql.append(" and id in (2763775429) ");
		if (minId != null) {
			sql.append(" AND id > ?   ");
			paramList.add(minId);
		}

		if(!CollectionUtils.isEmpty(execDomainList)){
			sql.append(" and own_domain_id in ( ");
			sql.append(StringUtils.join(execDomainList, ','));
			sql.append(" ) ");
		}

		sql.append(" order by id  " );
		if (pageSize != null) {
			sql.append(" limit  ? " );
			paramList.add(pageSize);
		}

		sql.append(" ) t1   ");

		sql.append(" INNER JOIN t_own_domain t2 ON t1.own_domain_id = t2.id ");
		sql.append(" INNER JOIN t_own_domain_setting t3 ON t3.own_domain_id = t2.id ");
		sql.append(" where t2.status = 1 and t3.pagespeed_setting = 1 and t3.pagespeed_frequency = 30 ");

		sql.append(" order by t1.id  " );

		System.out.println("=========== getTargetUrlsForMonthlyByDomains " + sql);
		System.out.println("================= paramList " + JSON.toJSONString(paramList));

		return this.findBySql(sql.toString(), paramList.toArray());
	}

	public List<TargetUrlEntity> getUrlList(int domainId, List<String> urlMurmur3HashList) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select * from t_target_url ");
		sql.append(" where type= ? and status = ? and own_domain_id = ? ");
		sql.append(" and urlMurmur3Hash in ('").append(StringUtils.join(urlMurmur3HashList, "','")).append("')");
		return findBySql(sql.toString(), Constants.TARGET_URL_TYPE_ADDED_BY_USER, Constants.TARGET_URL_STATUS_ACTIVE, domainId);
	}

	public List<TargetUrlEntity> getUrlListByIds(int domainId, List<Long> urlIdList) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select * from t_target_url ");
		sql.append(" where type= ? and status = ? and own_domain_id = ? ");
		sql.append(" and id in (").append(StringUtils.join(urlIdList, ",")).append(")");
		return findBySql(sql.toString(), Constants.TARGET_URL_TYPE_ADDED_BY_USER, Constants.TARGET_URL_STATUS_ACTIVE, domainId);
	}

	public List<TargetUrlEntity> targetUrlEntitiesWithPaging(int pageNumber, int pageSize) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select id, own_domain_id, url from t_target_url order by id ");
		sql.append(" limit  ? , ? ");
		return findBySql(sql.toString(), (pageNumber*pageSize), pageSize);

	}

	public int [] batchUpdateSql(List<String> sqlList){
		return executeBatch(sqlList);
	}

	public List<TargetUrlEntity> getDomainURLByPage(long lastId, int domainId, int pageSize) {
		StringBuffer sql = new StringBuffer();
		sql.append(" select id,url ");
		sql.append(" from t_target_url ");
		sql.append(" where id > ? ");
		sql.append(" and own_domain_id = ? and type = 1 and status = 1 ");
		sql.append(" order by id ");
		sql.append(" limit ").append(pageSize);
		return this.findBySql(sql.toString(), lastId, domainId);
	}

	public List<String> getUnManagedUrlByIndexKey(List<String> murmurHashList, Integer domainId) {
		StringBuilder sbd = new StringBuilder();
		sbd.append(" select url from t_target_url ");
		sbd.append(" where own_domain_id = ? and status = 1").append(" and url in('").append(StringUtils.join(murmurHashList, "','")).append("')");
		
		System.out.println(sbd.toString());
		return this.queryForStringList(sbd.toString(), domainId);
	}

	public List<TargetUrlEntity> getSrsltidUrlsByDomain(int domainId, String urlFilter) {
		String sql = " select * from t_target_url where own_domain_id = ? and status = ? and url like '%" + urlFilter + "%' ";
		return this.findBySql(sql, domainId, Constants.TARGET_URL_STATUS_ACTIVE);
	}

	public List<TargetUrlEntity> getManagedUrlsToCrawlByDomain(int domainId) {
		final String sql = "select id, own_domain_id, url, urlMurmur3Hash from t_target_url where own_domain_id = ? and status = 1 and type = 1 and ifnull(disable_crawl, 0)=0";
		return this.findBySql(sql, domainId);
	}
}