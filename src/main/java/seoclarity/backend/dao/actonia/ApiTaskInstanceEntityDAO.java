package seoclarity.backend.dao.actonia;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.actonia.ApiTaskInstanceEntity;
import java.util.Date;
import java.util.List;

@Repository
public class ApiTaskInstanceEntityDAO extends ActoniaBaseJdbcSupport<ApiTaskInstanceEntity> {

	public String getTableName() {
		return "api_task_instance ";
	}
	
	public void insert(ApiTaskInstanceEntity apiTaskInstanceEntity) {
		String sql = "insert into " + getTableName() + " (taskInfoId,taskInstanceName,ownDomainId,searchEngineId,languageId,cityId,paramHash,taskId,elementType,requestIp," +
			"createUserId,status,paramElementCount,uniqueElementCount,createDate) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) ";
		this.executeUpdate(sql, apiTaskInstanceEntity.getTaskInfoId(),apiTaskInstanceEntity.getTaskInstanceName(),apiTaskInstanceEntity.getOwnDomainId(),
			apiTaskInstanceEntity.getSearchEngineId(),apiTaskInstanceEntity.getLanguageId(),apiTaskInstanceEntity.getCityId(),apiTaskInstanceEntity.getParamHash(),
			apiTaskInstanceEntity.getTaskId(),apiTaskInstanceEntity.getElementType(),apiTaskInstanceEntity.getRequestIp(),apiTaskInstanceEntity.getCreateUserId(),
			apiTaskInstanceEntity.getStatus(),apiTaskInstanceEntity.getParamElementCount(),apiTaskInstanceEntity.getUniqueElementCount(),apiTaskInstanceEntity.getCreateDate());
	}
	
	public ApiTaskInstanceEntity getTaskInstanceByUniqueKey(int taskInfoId, String taskId) {
		String sql = "select * from " + getTableName() + " where taskInfoId=? and taskId=?";
		return findObject(sql, taskInfoId, taskId);
	}

	// https://www.wrike.com/open.htm?id=1057201829
	public ApiTaskInstanceEntity getTaskInstanceById(int id) {
		String sql = "select * from " + getTableName() + " where id=?";
		return this.findObject(sql, id);
	}
	
	public List<ApiTaskInstanceEntity> getActiveTaskInstanceList(List<Integer> statusList, Date createdFromDate) {
		String sql = "select * from " + getTableName() + " where status in (" + StringUtils.join(statusList, ",") + ") and createdAt > ? order by status,id";
		return this.findBySql (sql, createdFromDate);
	}

	public List<ApiTaskInstanceEntity> getTaskInstanceListById(int id) {
		String sql = "select * from " + getTableName() + " where id = ?";
		return this.findBySql (sql, id);
	}
	
	public void updateDate(int id, Date updateDate) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" update ").append(this.getTableName()).append(" set updateDate=? where id=?");
		executeUpdate(stringBuilder.toString(), updateDate, id);
	}
	
	public void updateProcessStartTime(int id, int status, Date processStartTime, Date updateDate) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" update ").append(this.getTableName()).append(" set status=?, processStartTime=?, updateDate=? where id=?");
		executeUpdate(stringBuilder.toString(), status, processStartTime, updateDate, id);
	}
	
	public void updateProcessEndTime(int id, int status, Date processEndTime, long outputFileSize, String ftpFullPathFilename, Date updateDate) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" update ").append(this.getTableName()).append(" set status=?, processEndTime=?, outputFileSize=?, ftpFullPathFilename=?, updateDate=? where id=?");
		executeUpdate(stringBuilder.toString(), status, processEndTime, outputFileSize, ftpFullPathFilename, updateDate, id);
	}

	public void updateProcessEndTimeV1(int id, int status, Date processEndTime, long outputFileSize, String ftpFullPathFilename, long rowCount, Date updateDate) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" update ").append(this.getTableName()).append(" set status=?, processEndTime=?, outputFileSize=?, ftpFullPathFilename=?,");
		stringBuilder.append("  successElementCount=?, updateDate=? where id=?");
		executeUpdate(stringBuilder.toString(), status, processEndTime, outputFileSize, ftpFullPathFilename, rowCount, updateDate, id);
	}

	public void updateProcessEndTime_fix(int id, long rowCount) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" update ").append(this.getTableName()).append(" set ");
		stringBuilder.append("  successElementCount=? where id=? limit 1");
		//stringBuilder.append("  outputFileSize=? where id=? limit 1");

		executeUpdate(stringBuilder.toString(),  rowCount, id);
	}

	public void updateProcessEndTime_error(int id) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" update ").append(this.getTableName()).append(" set ");
		stringBuilder.append("  status=? where id=? limit 1");
		executeUpdate(stringBuilder.toString(),  3, id);
	}

	public void updateProcessEndTime_error1() {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("update  api_task_instance set status =3 where  id in(4000,4105,5430,5431,5437,5442,5529,5530,5531,5730,5731,5732,5733)  limit 13 ");
		executeUpdate(stringBuilder.toString());
	}



	public void updateProcessEndTime_OptionalUploadFail(int id, int status, Date processEndTime, long outputFileSize, String ftpFullPathFilename, long rowCount, Date updateDate,String[] errors) {
		String errorMessage = "empty error message ";
		if(errors != null && errors.length>0){
			errorMessage = errors[0];
		}
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" update ").append(this.getTableName()).append(" set status=?, processEndTime=?, outputFileSize=?, ftpFullPathFilename=?,");
		stringBuilder.append("  successElementCount=?, updateDate=? ,errorMessage = ? where id=?");
		executeUpdate(stringBuilder.toString(), status, processEndTime, outputFileSize, ftpFullPathFilename, rowCount, updateDate, errorMessage,id);
	}
	
	public int saveErrorMessage(ApiTaskInstanceEntity entity, Date updateDate) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(" update ").append(this.getTableName()).append(" set errorMessage=?, errorTrace=?, updateDate=? where id=?");
		return executeUpdate(stringBuilder.toString(), entity.getErrorMessage(), entity.getErrorTrace(), updateDate, entity.getId());
	}

	public List<ApiTaskInstanceEntity> getByTaskInfoId_Status(int taskInfoId, List<String > status) {
		String sql = "select * from "+getTableName()+" where taskInfoId = ? and status in ("+String.join(",",status)+")";
		return this.findBySql(sql, taskInfoId);
	}
}