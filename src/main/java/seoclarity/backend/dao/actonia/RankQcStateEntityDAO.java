package seoclarity.backend.dao.actonia;

import java.util.List;
import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.actonia.RankQcStateEntity;

@Repository
public class RankQcStateEntityDAO extends ActoniaBaseJdbcSupport<RankQcStateEntity> {

    public String getTableName() {
        return "rank_qc_state";
    }
    
    
	/*
	 * -> id int(11) NOT NULL AUTO_INCREMENT,
-> rankDate int(11) NOT NULL COMMENT 'yyyyMMdd',
-> ownDomainId int(11) NOT NULL,
-> engineId int(10) NOT NULL COMMENT 'search engine id, Example:1',
-> languageId int(10) NOT NULL COMMENT 'search language id, Example:1',
-> device varchar(5) NOT NULL DEFAULT 'd' COMMENT 'd: desktop, m: mobile',
-> rankType tinyint(1) NOT NULL DEFAULT '1' COMMENT '1: national, 2: geo',
-> frequence tinyint(1) NOT NULL DEFAULT '1' COMMENT '1:daily, 7:weekly, 14: bi-weekly, 30: monthly',
-> keywordCount int(11) unsigned NOT NULL,
-> rankedCount int(11) unsigned NOT NULL,
-> status tinyint(1) NOT NULL DEFAULT '1' COMMENT '0: NG-no rank data, 1: NG-missing rank data for some keywords 2: OK, all keywords ranked, 3: Error occurred when checking, 4: No managed keywords',
	 */
    
    
    public boolean insertByEntity(RankQcStateEntity entity) {
		
		StringBuilder sql = new StringBuilder();
		sql.append(" insert into ");
		sql.append(getTableName()).append(" ");
		sql.append(" (rankDate, ownDomainId, engineId, languageId,  ");
		sql.append(" device, rankType, frequence, keywordCount, ");
		sql.append(" rankedCount, status, startProcessDate, serverIp, createdAt ) ");
		sql.append(" VALUES ");
		sql.append(" (?,?,?,?,?,?,?,?,?,?,?,?,?) ");
		
		int result = executeUpdate(sql.toString(), 
			entity.getRankDate(),
			entity.getOwnDomainId(),
			entity.getEngineId(),
			entity.getLanguageId(),
			entity.getDevice(),
			entity.getRankType(),
			entity.getFrequence(),
			entity.getKeywordCount(),
			entity.getRankedCount(),
			entity.getStatus(),
			entity.getStartProcessDate(),
			entity.getServerIp(),
			entity.getCreatedAt()
		);
		return result > 0;
		
	}

//	public int insert(RankQcStateEntity entity) {
//		Map<String, Object> values = new HashMap<String, Object>();
//		
//		
//		
//		
//		
//		
//		values.put("rankDate", entity.getRankDate());
//		values.put("ownDomainId", entity.getOwnDomainId());
//		values.put("engineId", entity.getEngineId());
//		values.put("languageId", entity.getLanguageId());
//		values.put("device", entity.getDevice());
//		values.put("rankType", entity.getRankType());
//		values.put("frequence", entity.getFrequence());
//		values.put("keywordCount", entity.getKeywordCount());
//		values.put("rankedCount", entity.getRankedCount());
//		values.put("status", entity.getStatus());
//		values.put("startProcessDate", entity.getStartProcessDate());
//		values.put("serverIp", entity.getServerIp());
//		values.put("createdAt ", entity.getCreatedAt());
//		
//		return this.insert(values);
//	}
	
	public void update(RankQcStateEntity entity) {
		
		String sql = " update rank_qc_state set status = ?, keywordCount = ?, rankedCount = ?, startProcessDate = ? "
				+ "where rankDate = ? and ownDomainId = ? and engineId = ? and languageId = ? and device = ? "
				+ "and rankType = ? and frequence = ? limit 1 ";
		
		this.executeUpdate(sql, entity.getStatus(), entity.getKeywordCount(), entity.getRankedCount(), entity.getStartProcessDate(), 
				entity.getRankDate(), entity.getOwnDomainId(), entity.getEngineId(),
				entity.getLanguageId(), entity.getDevice(), entity.getRankType(), entity.getFrequence());
	}
	
	public List<RankQcStateEntity> getAllRecords(int processDate) {
        String sql = " select * from rank_qc_state where rankDate = ? ";
        return this.findBySql(sql, processDate);
    }
	
	public List<RankQcStateEntity> getErrorRecords(int processDate) {
        String sql = " select * from rank_qc_state where status not in (?, ?) and rankDate = ? ";
        return this.findBySql(sql, RankQcStateEntity.STATUS_OK, RankQcStateEntity.STATUS_NO_MANAGED_KEYWORD, processDate);
    }
	
	public RankQcStateEntity getRecordByEngineLanguageDevice(String processDate, Integer ownDomainId, Integer engineId, Integer languageId, String device, Integer rankType) {
        String sql = " select * from rank_qc_state where rankDate = ? and ownDomainId = ? and engineId = ? and languageId = ? and device = ? and rankType = ? ";
        
        
        List<RankQcStateEntity> result = this.findBySql(sql, processDate,
        		ownDomainId, engineId, languageId, device, rankType);
        
        if (result != null && result.size() > 0) {
			return result.get(0);
		}
        return null;
    }
	
	
	 public List<RankQcStateEntity> getNgDomainId(int rankDate) {

        StringBuffer sql = new StringBuffer();
        /**
         * 	sql.append(" (rankDate, ownDomainId, engineId, languageId,  ");
			sql.append(" device, rankType, frequence, keywordCount, ");
			sql.append(" rankedCount, status, startProcessDate, serverIp, createdAt ) ");
         */

        sql.append(" SELECT   ");
        sql.append("      ownDomainId, engineId, languageId, device, frequence, sum(keywordCount) as keywordCount, sum(rankedCount) as rankedCount ");
        sql.append("        from   " + getTableName() + " ");
        sql.append("        WHERE rankDate= ? ");
        sql.append("    and status in (" + RankQcStateEntity.STATUS_PROCESSING + ", " + RankQcStateEntity.STATUS_ERROR + ", " + RankQcStateEntity.STATUS_MISSING_DATA + ", " + RankQcStateEntity.STATUS_NO_RANK_DATA + ")");
        sql.append("   group by ownDomainId, engineId, languageId, device, frequence ");
        
        System.out.println(sql.toString());

        List<RankQcStateEntity> result = this.findBySql(sql.toString(), rankDate) ;
        return result;
    }
	 
	 public List<Integer> getNgDomainId(int rankDate, List<Integer> ownDomainIdList, Integer engineId, 
			 Integer languageId, Integer frequence) {

	        StringBuffer sql = new StringBuffer();
	        /**
	         * 	sql.append(" (rankDate, ownDomainId, engineId, languageId,  ");
				sql.append(" device, rankType, frequence, keywordCount, ");
				sql.append(" rankedCount, status, startProcessDate, serverIp, createdAt ) ");
					private Integer engineId;
	private Integer languageId;
	private String device;
	private Integer rankType;
	         */
	        
	        String ownDomainIdStr = getIntegerListQueryParam(ownDomainIdList);

	        sql.append(" SELECT   ");
	        sql.append("      ownDomainId ");
	        sql.append("        from   " + getTableName() + " ");
	        sql.append("        WHERE rankDate= ? ");
	        sql.append("    and status in (" + RankQcStateEntity.STATUS_PROCESSING + ", " + RankQcStateEntity.STATUS_ERROR + ", " + RankQcStateEntity.STATUS_MISSING_DATA + ", " + RankQcStateEntity.STATUS_NO_RANK_DATA + ")");
	        sql.append("    and ownDomainId in (" + ownDomainIdStr + ")");
	        sql.append("    and engineId = " + engineId);
	        sql.append("    and languageId = " + languageId);
	        sql.append("    and frequence = " + frequence);
//	        sql.append("    and device = '" + device + "' ");
//	        sql.append("    and rankType = " + rankType);
	        
	        System.out.println(sql.toString());

	        List<Integer> result = this.queryForIntegerList(sql.toString(), rankDate) ;
	        return result;
	    }

	 
	 public List<RankQcStateEntity> getAll(int rankDate) {

        StringBuffer sql = new StringBuffer();

        sql.append(" SELECT * ");
        sql.append("  from   " + getTableName());
        sql.append("  WHERE rankDate= ? ");
        
        System.out.println(sql.toString());

        List<RankQcStateEntity> result = this.findBySql(sql.toString(), rankDate) ;
        return result;
    }


	public void cleanUp() {
		String sql = " truncate table rank_qc_state";
		
		this.executeUpdate(sql);
	}
}
