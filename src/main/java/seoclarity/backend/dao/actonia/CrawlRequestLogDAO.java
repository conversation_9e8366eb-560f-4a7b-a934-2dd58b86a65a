package seoclarity.backend.dao.actonia;

import java.util.Date;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.stereotype.Repository;

import seoclarity.backend.entity.actonia.CrawlRequestLog;
import seoclarity.backend.entity.clickhouse.internallink.ProcessListVO;
import seoclarity.backend.utils.FormatUtils;

@Repository("crawlRequestLogDAO")
public class CrawlRequestLogDAO extends ActoniaBaseJdbcSupport<CrawlRequestLog>{
	
	@Override
    public String getTableName() {
        return "crawl_request_log";
    }


	public CrawlRequestLog getFinishedCrawlerByOwnDomainIdAndCrawlId(int ownDomainId, int crawlRequestId) {
		StringBuffer sql = new StringBuffer();
		sql.append("	select * ");
		sql.append("	from  ").append(getTableName());
		sql.append("	where own_domain_id = ? and id = ? and crawl_status in (" + CrawlRequestLog.CRAWL_COMPLETED + ", " + CrawlRequestLog.CRAWL_CANCELLED + "," + CrawlRequestLog.CRAWL_ERRORED +  ") ");
		
		return findObject(sql.toString(), ownDomainId, crawlRequestId);
	}

	public CrawlRequestLog getById(int crawlRequestId) {
		StringBuffer sql = new StringBuffer();
		sql.append("	select * ");
		sql.append("	from  ").append(getTableName());
		sql.append("	where  id = ?  ");

		return findObject(sql.toString(), crawlRequestId);
	}

	public List<CrawlRequestLog> getFinishedCrawlerTotalCheckAPi() {
		StringBuffer sql = new StringBuffer();
		sql.append("	select id,crawl_speed ");
		sql.append("	from  ").append(getTableName());
		sql.append("	where  crawl_status =3 and  crawl_request_date >= 20230301 and state <> 2");
		return findBySql(sql.toString());
	}

	public List<CrawlRequestLog> getCrawlIdListForLastOneYear(Integer cutOverDateInt) {
		String date = FormatUtils.formatDate(DateUtils.addYears(new Date(), -1), "yyyy-MM-dd");
		StringBuffer sql = new StringBuffer();
		sql.append("	select * ");
		sql.append("	from  ").append(getTableName());
		sql.append("	where generate_page_links = ? and last_update_time >= '" + date + "' and crawl_request_date < ? ");
		
		return findBySql(sql.toString(), 1, cutOverDateInt);
	}
	
	public List<CrawlRequestLog> getCrawlIdListByDateRange(Integer startDate, Integer endDate) {
		
		String date = FormatUtils.formatDate(DateUtils.addYears(new Date(), -1), "yyyy-MM-dd");
		
		StringBuffer sql = new StringBuffer();
		sql.append("	select * ");
		sql.append("	from  ").append(getTableName());
		sql.append("	where generate_page_links = ? and crawl_request_date >= " + startDate + " and crawl_request_date < " + endDate);
		
		System.out.println("SQL: " + sql.toString());
		return findBySql(sql.toString(), 1);
	}
	
	public List<CrawlRequestLog> getCrawlIdListForLastOneYearV2(Integer cutOverDateInt) {
		
		String date = FormatUtils.formatDate(DateUtils.addYears(new Date(), -1), "yyyy-MM-dd");
		
		StringBuffer sql = new StringBuffer();
		sql.append("	select * ");
		sql.append("	from  ").append(getTableName());
		sql.append("	where generate_page_links = ? and last_update_time >= '" + date + "' and crawl_request_date < ? ");
		
		return findBySql(sql.toString(), 1, cutOverDateInt);
	}
	
	
	public List<CrawlRequestLog> getCrawlIdListForLastOneYearV3(Integer cutOverDateInt) {
		
		String date = FormatUtils.formatDate(DateUtils.addYears(new Date(), -1), "yyyy-MM-dd");
		
		StringBuffer sql = new StringBuffer();
		sql.append("	select * ");
		sql.append("	from  ").append(getTableName());
		sql.append("	where generate_page_links = ? and last_update_time >= '" + date + "' and crawl_request_date >= ? ");
		
		return findBySql(sql.toString(), 1, cutOverDateInt);
	}

	public CrawlRequestLog getCrawlerByOwnDomainIdAndCrawlId(int ownDomainId, int crawlRequestId) {
		StringBuffer sql = new StringBuffer();
		sql.append("	select * ");
		sql.append("	from  ").append(getTableName());
		sql.append("	where own_domain_id = ? and id = ? ");
		
		System.out.println(sql.toString());
		
		return findObject(sql.toString(), ownDomainId, crawlRequestId);
	}

	public void updateAdditionalStatus(List<ProcessListVO> finalProssList) {
		
		if (finalProssList == null || finalProssList.size() == 0) {
			System.out.println(" crawl list is empty!");
			return;
		}
		
		String idStr = "";
		for(ProcessListVO vo : finalProssList) {
			idStr += vo.getCrawl_request_log_id_i() + ",";
		}
		
		if (StringUtils.endsWith(idStr, ",")) {
			idStr = StringUtils.removeEnd(idStr, ",");
		}
		
		StringBuffer sql = new StringBuffer();
		sql.append("	update " + getTableName() + " set ");
		sql.append("	additional_status = ? where id in (" + idStr + ")  ");
		System.out.println("updateAdditionalStatus SQL : " + sql.toString());
		this.executeUpdate(sql.toString(), CrawlRequestLog.INTERNALLINK_DATA_STATUS_COMPLETE);
	}
	        
	public List getProjectName(int crawlId){
		StringBuffer sql = new StringBuffer();
		sql.append("select project_id,project_name,(case enable_javascript when 1 then 'JS' else   'STANARD'  end) as 'Crawl_Type' ,if (recurring_crawl_id is null ,0,1) as crawl_schedule from crawl_request_log where id =?");
		//System.out.println(sql);
		return queryForMapList(sql.toString(),crawlId);
	}
	public List getfinishedProjectList(String crawl_request_date){
		StringBuffer sql = new StringBuffer();
		sql.append("select crawl_request_date,id crawl_id,own_domain_id,project_id,project_name,(case enable_javascript when 1 then 'JS' else   'STANARD'  end) as 'Crawl_Type' ,if (recurring_crawl_id is null ,0,1) as crawl_schedule  from crawl_request_log where  crawl_status =3 and ifnull(state,0) <> 2 and crawl_request_date >= 20230101 and length(crawl_request_date ) =8 order by crawl_request_date, own_domain_id,project_id,id");
		//System.out.println(sql);
		return queryForMapList(sql.toString());
	}

	public void updateAdditionalStatusV2(List<ProcessListVO> finalProssList) {
		
		if (finalProssList == null || finalProssList.size() == 0) {
			System.out.println(" crawl list is empty!");
			return;
		}
		
		String idStr = "";
		for(ProcessListVO vo : finalProssList) {
			idStr += vo.getCrawl_request_log_id_i() + ",";
		}
		
		if (StringUtils.endsWith(idStr, ",")) {
			idStr = StringUtils.removeEnd(idStr, ",");
		}
		
		StringBuffer sql = new StringBuffer();
		sql.append("	update " + getTableName() + " set ");
		sql.append("	additional_status_v2 = ? where id in (" + idStr + ")  ");
		System.out.println("updateAdditionalStatus SQL : " + sql.toString());
		this.executeUpdate(sql.toString(), CrawlRequestLog.INTERNALLINK_DATA_STATUS_COMPLETE);
	}
	
	
	public void updateAdditionalStatusV3(List<Integer> crawlIdList) {
		
		if (crawlIdList == null || crawlIdList.size() == 0) {
			System.out.println(" crawl list is empty!");
			return;
		}
		
		StringBuffer sql = new StringBuffer();
		sql.append("	update " + getTableName() + " set ");
		sql.append("	additional_status_v2 = ? where id in (" + getIntegerListQueryParam(crawlIdList) + ")  ");
		System.out.println("updateAdditionalStatus SQL : " + sql.toString());
		this.executeUpdate(sql.toString(), CrawlRequestLog.INTERNALLINK_DATA_STATUS_COMPLETE);
	}
	
	
	
	public void updateAdditionalStatusV2ByIdList(List<Integer> idList) {
		
		if (idList == null || idList.size() == 0) {
			System.out.println(" crawl list is empty!");
			return;
		}
		
		StringBuffer sql = new StringBuffer();
		sql.append("	update " + getTableName() + " set ");
		sql.append("	additional_status_v2 = ? where id in (" + getIntegerListQueryParam(idList) + ")  ");
		System.out.println("updateAdditionalStatus SQL : " + sql.toString());
		this.executeUpdate(sql.toString(), CrawlRequestLog.INTERNALLINK_DATA_STATUS_COMPLETE);
	}

	public CrawlRequestLog getLogIdByProjectId(int domainId, long projectId, int crawlRequestDate){
		String sql = "select id,additional_status_v2 from " + getTableName();
		sql += " where own_domain_id = ? and project_id = ? and crawl_request_date =? limit 1";
		return findObject(sql, domainId, projectId, crawlRequestDate);
	}

	public List<CrawlRequestLog> getNotUploadCompletedCrawlRequestLog(List<Integer> crawlStatusList, int maxCrawlRequestDate) {
		String sql = "select id, crawl_status, own_domain_id,project_id  from " + getTableName()
				+ " where crawl_status in (" + StringUtils.join(crawlStatusList, ",") + ") and (upload_status <> ? or upload_status is null) and crawl_request_date > ? ;";
		return findBySql(sql, CrawlRequestLog.UPLOAD_STATUS_COMPLETED, maxCrawlRequestDate);
	}

	public void updateUploadStatus(Integer crawlRequestLogId, int expectedStatus) {
		String sql = "update " + getTableName() + " set upload_status = ? where id = ? ";
		System.out.println("update status:" + sql);
		executeUpdate(sql, expectedStatus, crawlRequestLogId);
	}
	
	public void updateErrorPageSummaryStatus(Integer crawlRequestLogId, int status) {
		String sql = "update " + getTableName() + " set error_page_summary_status = ? where id = ? ";
		executeUpdate(sql, status, crawlRequestLogId);
	}
	
}
