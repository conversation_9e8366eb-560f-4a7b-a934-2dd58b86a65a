package seoclarity.backend.dao.actonia;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.actonia.ScriptRunningDetailEntity;

import java.util.*;
import java.util.stream.Collectors;

@Repository
public class ScriptRunningDetailJdbcDAO extends ActoniaBaseJdbcSupport<ScriptRunningDetailEntity>{

	@Override
	public String getTableName() {
		return "script_running_detail";
	}


	public ScriptRunningDetailEntity checkExistsProcessing(int ownDomainId, int configId, int  processdate, Integer[] status) {
		String sql = "select * from script_running_detail where own_domain_id=? and config_id=? and log_date=? ";
		if (status != null) {
			sql += " and status in ( " + StringUtils.join(status, ',') + ") ";
		}
		sql += " limit 1 ";
		return findObject(sql, ownDomainId, configId, processdate);
	}

	public ScriptRunningDetailEntity checkExistByDomainIdAndLogDateAndFileName(int ownDomainId, int configId, int  processDate, String fileName, Integer[] status) {
		String sql = "select * from script_running_detail where own_domain_id=? and config_id=? and log_date=? and symbol = ?";
		if (status != null) {
			sql += " and status in ( " + StringUtils.join(status, ',') + ") ";
		}
		sql += " limit 1 ";
		return findObject(sql, ownDomainId, configId, processDate, fileName);
	}
	
	public List<ScriptRunningDetailEntity> checkExistsErrorProcessing(int domainId, int configId, int sDate, int eDate, Integer status) {
		String sql = "select * from script_running_detail where " +
				"own_domain_id=? and config_id=? and log_date >= ? and log_date <= ? " +
				"and status = ?  order by own_domain_id ";
		return findBySql(sql, domainId, configId, sDate, eDate, status);
	}

	public List<ScriptRunningDetailEntity> checkErrorByDomainIdAndLogDateAndFileName(int domainId, int configId, int sDate, int eDate, Integer status, List<String> symbolList) {
		StringBuilder sbd = new StringBuilder();
		sbd.append(" select * from script_running_detail ");
		sbd.append(" where own_domain_id=? and config_id=? and log_date >= ? and log_date <= ? and status = ? ");
		sbd.append(" and symbol in ('").append(StringUtils.join(symbolList, "','")).append("')");
		sbd.append(" order by own_domain_id ");
		//System.out.println("sql => checkErrorByDomainIdAndLogDateAndFileName: " + sbd.toString());
		return findBySql(sbd.toString(), domainId, configId, sDate, eDate, status);
	}

	public int insert(ScriptRunningDetailEntity entity) {
		Map<String, Object> values = new HashMap<>(10);
		values.put("config_id", entity.getConfigId());
		values.put("create_date", entity.getCreateDate());
		values.put("log_date", entity.getLogDate());
		values.put("own_domain_id", entity.getOwnDomainId());
		values.put("status", entity.getStatus());
		values.put("continent", entity.getContinent());
		values.put("symbol", entity.getSymbol());
		if (entity.getRerunNo() != null) {
			values.put("rerun_no", entity.getRerunNo());
		}
		return insert(values);
	}
	
	public void updateStatusAndRerunNoById(int id, int status, int rerunNO) {
		StringBuilder sql = new StringBuilder();
		sql.append(" update script_running_detail set status = ?, rerun_no = ? ");
		sql.append(" where id = ? ");
		this.executeUpdate(sql.toString(), status, rerunNO, id);
	}

	public List<ScriptRunningDetailEntity> checkExistsListProcessing(int ownDomainId, int configId, int logDate, Integer[] status) {
		String sql = "select * from script_running_detail where own_domain_id=? and config_id=? and log_date=? ";
		if (status != null) {
			sql += " and status in ( " + StringUtils.join(status, ',') + ") ";
		}
		sql += " order by id limit 100";
		return findBySql(sql, ownDomainId, configId, logDate);
	}

	public Set<String> checkExistsList(int ownDomainId, int configId, int logDate) {
		String sql = "select * from script_running_detail where own_domain_id=? and config_id=? and log_date=? ";
		List<ScriptRunningDetailEntity> bySql = findBySql(sql, ownDomainId, configId, logDate);
		if (bySql == null || bySql.isEmpty()) {
			return new HashSet<>();
		}
		return bySql.stream().map(ScriptRunningDetailEntity::getSymbol).collect(Collectors.toSet());
	}

	public ScriptRunningDetailEntity checkExistsByDomainIdAndFileName(int ownDomainId, int configId, String fileName) {
		String sql = "select * from script_running_detail where own_domain_id = ? and config_id = ? and symbol = ? limit 1";
		return findObject(sql, ownDomainId, configId, fileName);
	}

	public List<ScriptRunningDetailEntity> checkExistsByByConfigId(int configId, int status, int logDate, boolean notIncludeDomain, String notIncludeDomainId) {
		String sql = "select srd.*,tod.domain as domainName from script_running_detail  srd left join t_own_domain tod on tod.id = srd.own_domain_id where config_id = ? and srd.status = ? and srd.log_date = ? and tod.status = 1";
		if (notIncludeDomain) {
			sql += " and own_domain_id not in (" + notIncludeDomainId + ") ";
		}
		return findBySql(sql, configId, status, logDate);
	}

	public List<Integer> getAllBotDomainId() {
		String sql = "select distinct id from t_own_domain where id in (select distinct own_domain_id from script_running_detail where own_domain_id != 4765 and config_id = 8) and status = 1";
		return queryForIntegerList(sql);
	}

	public List<ScriptRunningDetailEntity> getDetailByConfigId(int configId, int status, int logDateStart, int logDateEnd, List<Integer> domainList) {
		String sql = "select * from script_running_detail where config_id  = ? and log_date >= ? and log_date <= ? and status = ? and own_domain_id in (" + StringUtils.join(domainList, ',') + ")";
		return findBySql(sql, configId, logDateStart, logDateEnd, status);
	}

	public List<ScriptRunningDetailEntity> getDetailByConfigId(int configId, int status, String logDateStr, String domainIdStr) {
		String sql = "select * from script_running_detail where config_id  = ? and status = ? and own_domain_id in (" + domainIdStr + ") and log_date in (" + logDateStr + ") ";
		return findBySql(sql, configId, status);
	}

	public List<ScriptRunningDetailEntity> getDetailByConfigId(int configId, int status, int ownDomainId, int logDate) {
		String sql = "select * from script_running_detail where config_id  = ? and status = ? and own_domain_id = ? and log_date = ? ";
		return findBySql(sql, configId, status, ownDomainId, logDate);
	}

}
