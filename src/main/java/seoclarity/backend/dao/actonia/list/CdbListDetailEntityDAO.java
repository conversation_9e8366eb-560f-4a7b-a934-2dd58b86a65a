package seoclarity.backend.dao.actonia.list;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;
import seoclarity.backend.dao.actonia.ActoniaBaseJdbcSupport;
import seoclarity.backend.entity.actonia.list.CdbListDetailEntity;

import java.util.*;

@Repository
public class CdbListDetailEntityDAO extends ActoniaBaseJdbcSupport<CdbListDetailEntity> {

    @Override
    public String getTableName() {
        return "cdb_list_detail";
    }

    public long insert(CdbListDetailEntity entity) {

        Map<String, Object> values = new HashMap<String, Object>();
        values.put("ownDomainId", entity.getOwnDomainId());
        values.put("listId", entity.getListId());
        values.put("listType", entity.getListType());
        values.put("keywordString", entity.getKeywordString());
        values.put("keywordHashcode", entity.getKeywordHashcode());
        values.put("urlString", entity.getUrlString());
        values.put("urlMurmur3hash", entity.getUrlMurmur3hash());
        values.put("createDate", new Date());

        return insertForLongId(values);
    }


    public List<CdbListDetailEntity> getListByList(int listType, int ownDomainId, long listId) {

        StringBuffer sql = new StringBuffer();
        sql.append(" select * from ").append(getTableName());
        sql.append(" where listType = ? and ownDomainId = ? and listId = ? ");
        return findBySql(sql.toString(), listType, ownDomainId, listId);
    }


    public int deleteByListId(int ownDomainId, long listId) {

        String sql = " delete from " + getTableName() + " where ownDomainId = ? and listId = ? ";

        return executeUpdate(sql, ownDomainId, listId);
    }

    public void deleteBatch(List<CdbListDetailEntity> cdbListDetailEntityList) {

        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append(" delete from " + getTableName() + " ");
        stringBuffer.append(" where ");
        stringBuffer.append(" id = ? ");

        Object[] values = null;
        List<Object[]> batch = new ArrayList<Object[]>();
        for (CdbListDetailEntity cdbListDetailEntity : cdbListDetailEntityList) {
            values = new Object[]{cdbListDetailEntity.getId()};
            batch.add(values);
        }

        this.executeBatch(stringBuffer.toString(), batch);
    }

    public void saveBatch(List<CdbListDetailEntity> cdbListDetailEntityList) {
        StringBuffer sql = new StringBuffer();
        sql.append("insert into " + getTableName() + "(ownDomainId,listId,listType,keywordString,keywordHashcode,urlString,urlMurmur3hash) values(?,?,?,?,?,?,?,?)");

        List<Object[]> batch = new ArrayList<Object[]>();
        for (CdbListDetailEntity entity : cdbListDetailEntityList) {
            Object[] values = new Object[]{
                    entity.getOwnDomainId(),
                    entity.getListId(),
                    entity.getListType(),
                    entity.getKeywordString(),
                    entity.getKeywordHashcode(),
                    entity.getUrlString(),
                    entity.getUrlMurmur3hash()
            };
            batch.add(values);
        }

        this.executeBatch(sql.toString(), batch);
    }

    public int loadData(String fileName) {
        StringBuffer sql = new StringBuffer();
        sql.append(" load data local infile '").append(fileName).append("' into table ").append(getTableName() + " character set utf8 ");
        sql.append(" fields terminated by '\t' lines terminated by '\n' ");
        sql.append(" (ownDomainId, listId, listType, keywordString, keywordHashcode, urlString, urlMurmur3hash)");

        System.out.println("======loadData into cdb_list_detail:" + sql.toString());

        return this.executeUpdate(sql.toString());
    }

    public List<CdbListDetailEntity> getListByKeyword(int ownDomainId, long listId, List<String> keywordList) {

        StringBuffer sql = new StringBuffer();
        sql.append(" select id from ").append(getTableName());
        sql.append(" where ownDomainId = ? and listId = ? ");
        sql.append(" and keywordString in ( ");
        sql.append("'" + StringUtils.join(keywordList, "','") + "'");
        sql.append(" ) ");

        return findBySql(sql.toString(), ownDomainId, listId);
    }

}
