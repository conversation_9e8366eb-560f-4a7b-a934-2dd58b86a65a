package seoclarity.backend.dao.actonia.adhoc;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;
import seoclarity.backend.dao.actonia.ActoniaBaseJdbcSupport;
import seoclarity.backend.entity.actonia.adhoc.AutoAdhocRankKeywordSVEntity;
import seoclarity.backend.entity.actonia.adhoc.AutoAdhocRankProjectEntity;
import seoclarity.backend.export.AutoAdhocKeywordClusterExtract;
import seoclarity.backend.summary.groupkeyword.AdhocClusterGroupKeyword;

import java.util.Date;
import java.util.List;

import static seoclarity.backend.entity.actonia.adhoc.AutoAdhocRankProjectEntity.STATUS_CANCELLED;

@Repository
public class AutoAdhocRankProjectEntityDAO extends ActoniaBaseJdbcSupport<AutoAdhocRankProjectEntity> {

    @Override
    public String getTableName() {
        return "auto_adhoc_rank_project";
    }

    public List<AutoAdhocRankProjectEntity> getPrepareRunTask(List<Integer> statusList) {

        StringBuffer sql = new StringBuffer();
        sql.append(" select * from " + getTableName()).append(" where enabled=? and frequencyType != ? and status in (");
        sql.append(this.getIntegerListQueryParam(statusList)).append(") ");
        sql.append(" and retrieveType in (" + this.getIntegerListQueryParam(AutoAdhocRankProjectEntity.RETRIEVE_TYPE_SV) + ")");
        sql.append(" and createDate <= date_format(now(), '%Y%m%d') ");

        sql.append(" order by id asc ");
        return findBySql(sql.toString(), AutoAdhocRankProjectEntity.ENABLED, AutoAdhocRankProjectEntity.FREQUENCY_TYPE_HOURLY);
    }

    public List<AutoAdhocRankProjectEntity> getNeedExtractRankTaskByDate(List<Integer> frequencyTypeList, List<Integer> retrieveTypeList) {
        StringBuffer sql = new StringBuffer();
        sql.append(" select * from " + getTableName()).append(" where enabled=? ");
        sql.append(" and retrieveType in (" + this.getIntegerListQueryParam(retrieveTypeList) + ")");
        sql.append(" and status=? and rankUploadStatus = ? ");
        sql.append(" and (exportRankStatus is null or exportRankStatus in (" + AutoAdhocRankProjectEntity.EXPORT_RANK_STSTUS_NOT_STARTED + "," + AutoAdhocRankProjectEntity.EXPORT_RANK_STSTUS_ERROR + "))");
        sql.append(" and frequencyType in (").append(this.getIntegerListQueryParam(frequencyTypeList)).append(") ");
        sql.append(" order by id asc ");
        System.out.println("====SQL getNeedExtractRankTaskByDate:" + sql.toString());
        return findBySql(sql.toString(), AutoAdhocRankProjectEntity.ENABLED, AutoAdhocRankProjectEntity.STATUS_COMPLETED_SUCCESSFULLY,
                AutoAdhocRankProjectEntity.RANK_UPLOAD_STATUS_COMPLETED_WITHOUT_ERROR);
    }

    public List<AutoAdhocRankProjectEntity> getUnRetrieveProject(List<Integer> retrieveTypeList) {
        String sql = " select t1.* from " + getTableName() + " t1 ";
        sql += " inner join auto_adhoc_rank_keyword_sv t2 on t1.id = t2.projectId ";
        sql += " where retrieveType in( " + this.getIntegerListQueryParam(retrieveTypeList) + ")";
        sql += " and t1.status in(" + AutoAdhocRankProjectEntity.STATUS_PROCESSING + "," + AutoAdhocRankProjectEntity.STATUS_RANKCHECK_ERROR + ")";
        sql += " and enabled = " + AutoAdhocRankProjectEntity.ENABLED;
        sql += " and t2.svRetrieveStatus in(" + AutoAdhocRankKeywordSVEntity.SV_RETRIEVE_STATUS_NOT_STARTED + ", " + AutoAdhocRankKeywordSVEntity.SV_RETRIEVE_STATUS_PROCESSING + ","+AutoAdhocRankKeywordSVEntity.SV_RETRIEVE_STATUS_ERROR+")";
        sql += " group by t1.id,t1.criteriaId order by t1.id ";

        System.out.println("==SQL getUnRetrieveProject: " + sql);
        return findBySql(sql);
    }

    public List<Integer> getPostedProjectId(List<Integer> retrieveTypeList) {
        String sql = " select distinct t1.id from " + getTableName() + " t1 ";
        sql += " inner join auto_adhoc_rank_keyword_sv t2 on t1.id = t2.projectId ";
        sql += " where retrieveType in( " + this.getIntegerListQueryParam(retrieveTypeList) + ")";
        sql += " and t1.status in(" + AutoAdhocRankProjectEntity.STATUS_PROCESSING + "," + AutoAdhocRankProjectEntity.STATUS_RANKCHECK_ERROR + ")";
        sql += " and enabled = " + AutoAdhocRankProjectEntity.ENABLED;
        sql += " and t2.svRetrieveStatus in(" + AutoAdhocRankKeywordSVEntity.SV_RETRIEVE_STATUS_PROCESSING + ","+AutoAdhocRankKeywordSVEntity.SV_RETRIEVE_STATUS_ERROR+")";
        sql += " group by t1.id,t1.criteriaId order by t1.id ";

        System.out.println("==SQL getPostedProjectId: " + sql);
        return queryForIntegerList(sql);
    }

    public List<AutoAdhocRankProjectEntity> getUnFinishedSvProject() {
        String sql = " select id,retrieveType,rankStatus,retrieveSVStatus,ownDomainId ";
        sql += " from " + getTableName();
        sql += " where retrieveType in(" + this.getIntegerListQueryParam(AutoAdhocRankProjectEntity.RETRIEVE_TYPE_SV) + ") ";
        sql += " and enabled = " + AutoAdhocRankProjectEntity.ENABLED;
        sql += " and status in (" + AutoAdhocRankProjectEntity.STATUS_PROCESSING + "," + AutoAdhocRankProjectEntity.STATUS_RANKCHECK_ERROR + ")";
        sql += " and retrieveSVStatus = " + AutoAdhocRankProjectEntity.RETRIEVESV_STATUS_PROCESSING;

        System.out.println("==SQL getUnFinishedSvProject: " + sql);
        return findBySql(sql);
    }

    public boolean updateProjectStatus(int projectId, Integer projectStatus, Integer rankStatus, Integer retrieveSVStatus) {
        String sql = " update " + getTableName() + " set id = " + projectId;
        if (projectStatus != null) {
            sql += " ,  status = " + projectStatus;
        }
        if (rankStatus != null) {
            sql += " ,  rankStatus = " + rankStatus;
        }
        if (retrieveSVStatus != null) {
            sql += " ,  retrieveSVStatus = " + retrieveSVStatus;
        }

        sql += " where id = " + projectId;
        return executeUpdate(sql) > 0 ? true : false;
    }

    public AutoAdhocRankProjectEntity getProjectByDomainIdAndProjectName(Integer oid, String projectName) {
        StringBuffer sql = new StringBuffer();
        sql.append(" select * from " + getTableName());
        sql.append(" where ownDomainId=? and projectName=? ");
        return findObject(sql.toString(), oid, projectName);
    }

    public AutoAdhocRankProjectEntity getProjectById(Integer id) {

        StringBuffer sql = new StringBuffer();
        sql.append(" select * from " + getTableName());
        sql.append(" where id=? ");

        return findObject(sql.toString(), id);
    }

    public boolean updateExtractStatus(int projectId, Integer exportRankStatus) {
        String sql = " update " + getTableName() + " set exportRankStatus = " + exportRankStatus + " where id = " + projectId;
        return executeUpdate(sql) > 0 ? true : false;
    }

    public void updateRankUploadStatus (int projectId, int exportRankStatus) {
        String sql = "update " + getTableName() + " set rankUploadStatus =? where id=?";
        executeUpdate(sql, exportRankStatus, projectId);
    }

    public void updateRankUploadStatusAndExtractStatus (int projectId, int exportRankStatus,int rankUploadStatus,int completedKeywordCount) {
        String sql = "update " + getTableName() + " set rankUploadStatus =? , exportRankStatus = ? ,completedKeywordCount = ? where id=?";
        executeUpdate(sql,rankUploadStatus, exportRankStatus, completedKeywordCount,projectId);
    }

    public List<AutoAdhocRankProjectEntity> getFinishedSvProject() {
        String sql = " select id,projectName,ownDomainId,country,engine,language,searchEngineId,languageId,cityId,retrieveType,rankStatus,retrieveSVStatus,createUserId,createdAt,uniqueKeywordCount ";
        sql += " from " + getTableName();
        sql += " where retrieveType in(" + this.getIntegerListQueryParam(AutoAdhocRankProjectEntity.RETRIEVE_TYPE_SV) + ") ";
        sql += " and enabled = " + AutoAdhocRankProjectEntity.ENABLED;
        sql += " and status = " + AutoAdhocRankProjectEntity.STATUS_COMPLETED_SUCCESSFULLY ;
        sql += " and retrieveSVStatus in ("
                + AutoAdhocRankProjectEntity.RETRIEVESV_STATUS_COMPLETEED_WITHOUT_ERROR + ","
                + AutoAdhocRankProjectEntity.RETRIEVESV_STATUS_SKIP_FOR_INVALID_CHARACTERSET;
        sql += " ) ";
        sql += " and (uploadSVStatus is null or uploadSVStatus != " + AutoAdhocRankProjectEntity.UPLOAD_SV_STSTUS_COMPLETED_WITHOUT_ERROR + ")";
        System.out.println("==SQL getFinishedSvProject: " + sql);
        return findBySql(sql);
    }

    public void updateUploadSvStatus (int projectId, int exportRankStatus) {
        String sql = "update " + getTableName() + " set uploadSVStatus =? where id=?";
        executeUpdate(sql, exportRankStatus, projectId);
    }

    public List<AutoAdhocRankProjectEntity> getNotExtractSVProject() {
        String sql = " select * ";
        sql += " from " + getTableName();
        sql += " where enabled = " + AutoAdhocRankProjectEntity.ENABLED;
        sql += " and retrieveType in (" + this.getIntegerListQueryParam(AutoAdhocRankProjectEntity.RETRIEVE_TYPE_SV);
        sql += " ," + AutoAdhocRankProjectEntity.RETRIEVE_RANKCHECK_AND_GOOGLE_SV + ")";
        sql += " and uploadSVStatus = " + AutoAdhocRankProjectEntity.UPLOAD_SV_STSTUS_COMPLETED_WITHOUT_ERROR;
        sql += " and retrieveSVStatus in (" + AutoAdhocRankProjectEntity.RANK_STATUS_COMPLETEED_WITHOUT_ERROR + ")";
        sql += " and (exportSVStatus is null or exportSVStatus in (" + AutoAdhocRankProjectEntity.EXPORT_RANK_STSTUS_NOT_STARTED
                + "," + AutoAdhocRankProjectEntity.EXPORT_RANK_STSTUS_ERROR + "))";

        System.out.println("==SQL getNotExtractSVProject: " + sql);
        return findBySql(sql);
    }

    public void updateStatus(int id, int status, int rankDeadMessageCount) {
        String sql = "update " + getTableName() + " set status=?, rankDeadMessageCount=? where id=?";
        executeUpdate(sql, status, rankDeadMessageCount, id);
    }

    public void updateProjectSVStatus(int projectId, int status, int retrieveSVStatus) {
        String sql = "update " + getTableName() + " set status=?, retrieveSVStatus=? where id=?";
        executeUpdate(sql, status, retrieveSVStatus, projectId);
    }

    public void updateAdwordsInfo(int projectId, Integer criteriaId, String canonicalName) {
        String sql = "update " + getTableName() + " set criteriaId=?, canonicalName=? where id=?";
        executeUpdate(sql, criteriaId, canonicalName, projectId);
    }

    public void updateRetrieveSVStatus(int id, int status) {
        String sql = "update " + getTableName() + " set retrieveSVStatus=? where id=?";
        executeUpdate(sql, status, id);
    }

    public void updateS3FullPathFilename(int id, String s3FullPathFilename) {
        String sql = "update " + getTableName() + " set s3FullPathFilename=? where id=?";
        executeUpdate(sql, s3FullPathFilename, id);
    }

    public void updateRankStatus(int id, int status) {
        String sql = "update " + getTableName() + " set rankStatus=? where id=?";
        executeUpdate(sql, status, id);
    }

    public void updateKeywordCount(int id, int keywordCountInFile, int uniqueKeywordCount) {
        String sql = "update " + getTableName() + " set keywordCountInFile=?, uniqueKeywordCount=? where id=?";
        executeUpdate(sql, keywordCountInFile, uniqueKeywordCount, id);
    }

    public void updateProjectAndRankStatus(int projectId, int status, int rankStatus) {
        String sql = "update " + getTableName() + " set status=?, rankStatus=? where id=?";
        executeUpdate(sql, status, rankStatus, projectId);
    }

    public void updateExportSVStatus(int projectId, int exportSVStatus) {
        String sql = "update " + getTableName() + " set exportSVStatus=? where id=?";
        executeUpdate(sql, exportSVStatus, projectId);
    }

    public void updateUniqueKeywordCount(int id, int uniqueKeywordCount) {
        String sql = "update " + getTableName() + " set uniqueKeywordCount=? where id=?";
        executeUpdate(sql, uniqueKeywordCount, id);
    }

    public List<AutoAdhocRankProjectEntity> getProject(int retrieveType) {
        StringBuffer sql = new StringBuffer();
        sql.append(" select * from ");
        sql.append(getTableName());
        sql.append(" where retrieveType = ?");
        sql.append(" and rankStatus = ?");
        sql.append(" and ( rankUploadStatus !=2 or rankUploadStatus is Null)");
        return this.findBySql(sql.toString(),retrieveType,AutoAdhocRankProjectEntity.RANK_STATUS_COMPLETEED_WITHOUT_ERROR);
    }

    public void updateAllRankUploadStatus() {
        String sql = "update " + getTableName() + " set rankUploadStatus = 2 where  retrieveType = 10 and rankStatus = 2";
        executeUpdate(sql);
    }

    public List<AutoAdhocRankProjectEntity> getAdHocKeywordClusterProject(boolean testFlag) {
        if (!testFlag) {
            StringBuilder sqlBuilder = new StringBuilder();
            sqlBuilder.append("select cityId, ifNull(kewordClusterStatus, 0) as kewordClusterStatus, id, ownDomainId, searchEngineId, languageId, device, ifNull(topXRankCluster, 0) as topXRankCluster from ").append(getTableName()).append(" ");
            sqlBuilder.append("WHERE enabled = ? AND retrieveType = ? AND keywordType = ? AND status = ? ");
            sqlBuilder.append("AND rankStatus = ? AND rankUploadStatus = ? ");
            sqlBuilder.append("AND (frequencyType IN (-1, ?) AND frequencyValue = 0 OR frequencyType = ? AND frequencyValue = 1 AND startDate IS NULL AND endDate IS NULL) ");
            sqlBuilder.append("AND IFNULL(kewordClusterStatus, 0) != ? ");
            sqlBuilder.append("AND ifNull(topXRankCluster,0) >= ? and ifNull(topXRankCluster,0) <= ?  ");
            sqlBuilder.append("AND createDate >= DATE_FORMAT(DATE_ADD(NOW(), INTERVAL -7 DAY), '%Y%m%d') ");
            sqlBuilder.append("AND createDate < DATE_FORMAT(DATE_ADD(NOW(), INTERVAL +1 DAY), '%Y%m%d') ");
            sqlBuilder.append("ORDER BY kewordClusterStatus");
            String sql = sqlBuilder.toString();
            System.out.println("===SQL getAdHocKeywordClusterProject: " + sql);
            return findBySql(sql, AutoAdhocRankProjectEntity.ENABLED, AutoAdhocRankProjectEntity.RETRIEVE_TYPE_RANKCHECK_ONLY,
                    AutoAdhocRankProjectEntity.KEYWORD_TYPE_NATIONAL, AutoAdhocRankProjectEntity.STATUS_COMPLETED_SUCCESSFULLY,
                    AutoAdhocRankProjectEntity.RANK_STATUS_COMPLETEED_WITHOUT_ERROR, AutoAdhocRankProjectEntity.RANK_UPLOAD_STATUS_COMPLETED_WITHOUT_ERROR,
                    AutoAdhocRankProjectEntity.FREQUENCY_TYPE_DAILY, AutoAdhocRankProjectEntity.FREQUENCY_TYPE_DAILY,
                    AutoAdhocKeywordClusterExtract.KW_CLUSTER_STATUS_SAVE_SUCCESS, AutoAdhocKeywordClusterExtract.TOP_X_RANK_CLUSTER_MIN,
                    AutoAdhocKeywordClusterExtract.TOP_X_RANK_CLUSTER_MAX);
        } else {
            String sql = "select cityId, ifNull(kewordClusterStatus, 0) as kewordClusterStatus, id, ownDomainId, searchEngineId, languageId, device, ifNull(topXRankCluster, 0) as topXRankCluster from auto_adhoc_rank_project where id = 1106";
            return findBySql(sql);
        }
    }

    public List<AutoAdhocRankProjectEntity> getAdHocClusterGroupKeywordProject() {
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("select cityId, ifNull(kewordClusterStatus, 0) as kewordClusterStatus, id, ownDomainId, searchEngineId, languageId, device, ifNull(topXRankCluster, 0) as topXRankCluster from ").append(getTableName()).append(" ");
        sqlBuilder.append("WHERE enabled = ? AND retrieveType = ? AND keywordType in(0, 1) AND status = ? ");
        sqlBuilder.append("AND rankStatus = ? AND rankUploadStatus = ? ");
        sqlBuilder.append("AND (frequencyType IN (-1, ?) AND frequencyValue = 0 OR frequencyType = ? AND frequencyValue = 1 AND startDate IS NULL AND endDate IS NULL) ");
        sqlBuilder.append("AND IFNULL(kewordClusterStatus, 0) != ? ");
        sqlBuilder.append("AND createDate >= DATE_FORMAT(DATE_ADD(NOW(), INTERVAL -30 DAY), '%Y%m%d') ");
        sqlBuilder.append("AND createDate < DATE_FORMAT(DATE_ADD(NOW(), INTERVAL +1 DAY), '%Y%m%d') ");
        sqlBuilder.append("AND createdAt >= '2024-03-06' "); // hard code -> https://www.wrike.com/open.htm?id=1307615998
        sqlBuilder.append("ORDER BY kewordClusterStatus");
        String sql = sqlBuilder.toString();
        System.out.println("===SQL getAdHocClusterGroupKeywordProject: " + sql);
        return findBySql(sql, AutoAdhocRankProjectEntity.ENABLED, AutoAdhocRankProjectEntity.RETRIEVE_TYPE_RANKCHECK_ONLY, AutoAdhocRankProjectEntity.STATUS_COMPLETED_SUCCESSFULLY,
                AutoAdhocRankProjectEntity.RANK_STATUS_COMPLETEED_WITHOUT_ERROR, AutoAdhocRankProjectEntity.RANK_UPLOAD_STATUS_COMPLETED_WITHOUT_ERROR,
                AutoAdhocRankProjectEntity.FREQUENCY_TYPE_DAILY, AutoAdhocRankProjectEntity.FREQUENCY_TYPE_DAILY,
                AdhocClusterGroupKeyword.CLUSTER_STATUS_CLUSTER_KEYWORD_ARRAY_SUCCESS
        );
    }

    public List<AutoAdhocRankProjectEntity> getProjectByIdList(List<Integer> idList) {
        String sql = "select * from " + getTableName() + " where id in (" + StringUtils.join(idList, ",") + ")";
        return findBySql(sql);
    }

    public void updateKwClusterStatus(int id, int status, Date updateDate) {
        String sql = "update " + getTableName() + " set kewordClusterStatus = ?, kewordClusterUpdateDate = ? where id = ? ";
        executeUpdate(sql, status, updateDate, id);
    }

    public void updateCompletedKeywordCount (int projectId, int completedKeywordCount) {
        String sql = "update " + getTableName() + " set completedKeywordCount =? where id=?";
        executeUpdate(sql, completedKeywordCount, projectId);
    }

    public List<AutoAdhocRankProjectEntity> getSubProjectsByParentProjectId(Integer parentProjectId) {
        StringBuffer sql = new StringBuffer();
        sql.append(" select t2.* from adhoc_rank_sv_project_rel t1 ");
        sql.append(" join auto_adhoc_rank_project t2 on t1.childProjectId = t2.id ");
        sql.append(" where t1.parentProjectId = ? ");
        return findBySql(sql.toString(), parentProjectId);
    }

    public void updateProjectSVStatusForRankingSv(int projectId, int status, int rankStatus, int retrieveSVStatus,
                                                  int rankUploadStatus, int uploadSVStatus, int keywordCountInFile,
                                                  int uniqueKeywordCount, int completedKeywordCount, Date sendToRankQueueStartTime) {
        String sql = "update " + getTableName() + " set status=?,rankStatus= ?,retrieveSVStatus=?,rankUploadStatus=?,uploadSVStatus=? ";
        sql += " ,keywordCountInFile=?,uniqueKeywordCount= ?,completedKeywordCount=?,sendToRankQueueStartTime=? ";
        sql += " where id=? ";
        executeUpdate(sql, status, rankStatus, retrieveSVStatus, rankUploadStatus, uploadSVStatus,
                keywordCountInFile, uniqueKeywordCount, completedKeywordCount, sendToRankQueueStartTime, projectId);
    }

    public List<Integer> getNotCanceledParentProjectList() {
        StringBuffer sql = new StringBuffer();
        sql.append(" select distinct parentProjectId from adhoc_rank_sv_project_rel t1 ");
        sql.append(" join auto_adhoc_rank_project t2 on t1.parentProjectId = t2.id ");
        sql.append(" where t1.createDate >= DATE_FORMAT(DATE_ADD(NOW(), INTERVAL -7 DAY), '%Y%m%d') and t2.status != ? ");
        System.out.println("==SQL getNotCanceledParentProjectList: " + sql);
        return queryForIntegerList(sql.toString(), STATUS_CANCELLED);
    }

}
