package seoclarity.backend.dao.clickhouse.pixelheight;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;
import ru.yandex.clickhouse.ClickHouseArray;
import seoclarity.backend.entity.ExtractQueryVO;
import seoclarity.backend.entity.clickhouse.CLRankingDetailEntity;
import seoclarity.backend.entity.clickhouse.PixelDetailEntity;

import java.sql.SQLException;
import java.util.Arrays;
import java.util.List;

@Repository
public class PixelDetailDao extends ClPixelHeightRankingBaseJdbcSupport<PixelDetailEntity> {

    private static final String DATABASE_NAME = "actonia_pixel";

    @Override
    public String getTableName() {
        return "m_pixel_detail_us";
    }

    private String getDetailTableName(int device, int year) {
        String tableName = "";
        if (device == 0) {
            tableName = "d_pixel_detail_us_" + year;
        } else {
            tableName = "m_pixel_detail_us_" + year;
        }
        return tableName;
    }

    private String getInfoTableName(int device, int year) {
        String tableName = "";
        if (device == 0) {
            tableName = "d_pixel_info_us_" + year;
        } else {
            tableName = "m_pixel_info_us_" + year;
        }
        return tableName;
    }

    private String getInfoTableName(int engine, int language, boolean isMobile, int year) {
        String tableName = "";
        String prefix = isMobile ? "m_" : "d_";
        tableName = prefix + "pixel_info";
        if (engine == 1 && language == 1) {
            tableName = tableName + "_us";
        } else {
            tableName = tableName + "_intl";
        }
        tableName = tableName + "_" + year;
        return DATABASE_NAME + "." + tableName;
    }

    public static String getDetailTable(int engine, int language, boolean isMobile, int year) {
        String tableName = "";
        String prefix = isMobile ? "m_" : "d_";
        tableName = prefix + "pixel_detail";
        if (engine == 1 && language == 1) {
            tableName = tableName + "_us";
        } else {
            tableName = tableName + "_intl";
        }
        tableName = tableName + "_" + year;
        return DATABASE_NAME + "." + tableName;
    }

    public static String getSubrankTable(int engine, int language, boolean isMobile, int year) {
        String tableName = "";
        String prefix = isMobile ? "m_" : "d_";
        tableName = prefix + "pixel_subrank";
        if (engine == 1 && language == 1) {
            tableName = tableName + "_us";
        } else {
            tableName = tableName + "_intl";
        }
        tableName = tableName + "_" + year;
        return DATABASE_NAME + "." + tableName;
    }

    public List<PixelDetailEntity> checkExistBatch(Integer ownDomainId, String rankingDate, List<Integer> keywordRankcheckIds) {
        String sql = "select keyword_rankcheck_id, count(*) as kwCnt from m_pixel_detail_us where own_domain_id = ? and ranking_date = ? and keyword_rankcheck_id in (" + StringUtils.join(keywordRankcheckIds, ",") + ") group by keyword_rankcheck_id";
        return findBySql(sql, ownDomainId, rankingDate);
    }

    public List<PixelDetailEntity> getKwInfoBatch(Integer ownDomainId, String rankingDate, List<Integer> keywordRankcheckIds, int device) {
        int year = Integer.parseInt(rankingDate.substring(0, 4));
        String sql = "select keyword_name,own_domain_id,keyword_rankcheck_id,domain_reverse,url,true_rank,web_rank,visual_rank,type,avg_search_volume,type_main_array,type_full_array from " + getDetailTableName(device, year) + " where own_domain_id = ? and ranking_date = ? and keyword_rankcheck_id in (" + StringUtils.join(keywordRankcheckIds, ",") + ") order by keyword_rankcheck_id";
        List<PixelDetailEntity> bySql = findBySql(sql, ownDomainId, rankingDate);
        for (PixelDetailEntity pixelDetailEntity : bySql) {
            ClickHouseArray typeFullArray = pixelDetailEntity.getTypeFullArray();
            try {
                String[] array = (String[]) typeFullArray.getArray();
                pixelDetailEntity.setTypeFullList(Arrays.asList(array));
            } catch (SQLException e) {
                e.printStackTrace();
            }

            ClickHouseArray typeMainArray = pixelDetailEntity.getTypeMainArray();
            try {
                String[] array = (String[]) typeMainArray.getArray();
                pixelDetailEntity.setTypeMainList(Arrays.asList(array));
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
        return bySql;
    }

    public List<PixelDetailEntity> getKwDafCntBatch(Integer ownDomainId, String rankingDate, List<Integer> keywordRankcheckIds, int device) {
        int year = Integer.parseInt(rankingDate.substring(0, 4));
        String sql = "select keyword_rankcheck_id, length(splitByString('!_!', attrstr.value[indexOf(attrstr.key, 'discussionsAndForumsUrls')])) AS dafCnt from " + getInfoTableName(device, year) + " where own_domain_id = ? and ranking_date = ? and keyword_rankcheck_id in (" + StringUtils.join(keywordRankcheckIds, ",") + ") and attrstr.value[indexOf(attrstr.key, 'discussionsAndForumsUrls')] != '-'";
        return findBySql(sql, ownDomainId, rankingDate);
    }

    public List<CLRankingDetailEntity> exportTopXKeywords(int ownDomainId, int engineId, int languageId,
                                                          int locationId, String rankingDate,
                                                          boolean isMobile, Integer rank, int pageNum, int pageSize) {
        int year = Integer.parseInt(rankingDate.substring(0, 4));
        String detailTableName = getDetailTable(engineId, languageId, isMobile, year);

        StringBuffer sql = new StringBuffer();
        sql.append(" select engine_id as engine,language_id as language,keyword_rankcheck_id,keyword_name,url," +
                "avg_search_volume,visual_rank as trueRank ,ranking_date,location_id,type ");
        sql.append(" FROM ").append(detailTableName);
        sql.append(" WHERE own_domain_id = ").append(ownDomainId);
        sql.append(" AND engine_id =  ").append(engineId);
        sql.append(" AND language_id =  ").append(languageId);
        sql.append(" AND location_id =  ").append(locationId);
        sql.append(" AND ranking_date = '").append(rankingDate).append("' ");
        sql.append(" AND visual_rank > 0 ");
        if (rank != null) {
            sql.append(" AND visual_rank <=  ").append(rank);
        }
        sql.append(" order by keyword_rankcheck_id,visual_rank  ");
        sql.append(" limit " + (pageNum * pageSize) + " , " + pageSize);
        System.out.println("sql:" + sql.toString());
        return findBySql(sql.toString(), CLRankingDetailEntity.class);
    }



    public List<CLRankingDetailEntity> exportTopXKeywordJsonWithMultFilter(ExtractQueryVO extractQueryVO) {
        int year = Integer.parseInt(extractQueryVO.getRankDate().substring(0, 4));
        int engineId = extractQueryVO.getEngineId();
        int languageId = extractQueryVO.getLanguageId();
        boolean isMobile = extractQueryVO.getDevice().equalsIgnoreCase("mobile") ? true : false;
        String device = extractQueryVO.getDevice().equalsIgnoreCase("desktop") ? "d" : "m";
        String infoTableName = getInfoTableName(engineId, languageId, isMobile, year);

        StringBuffer sql = new StringBuffer();
        sql.append(" select * ");
        if(extractQueryVO.getEnabledDiffSes()){
            sql.append("   ,dictGetInt64('file_dic_tracked_keyword_with_ses', 'create_date', " +
                    "(toUInt32(" + extractQueryVO.getDomainId() + "), " +
                    "toUInt32(" + engineId + "), " +
                    "toUInt32(" + languageId + "), " +
                    "toString('" + device + "'), toUInt64(URLHash(keyword_name)), dictGetUInt32('geo_master_mapping', 'geoMasterId', tuple(location_id))))  AS createDate     ");
        }else {
            sql.append("  ,dictGetInt64('file_dic_tracked_keyword_without_ses', 'create_date', " +
                    "(toUInt32(" + extractQueryVO.getDomainId() + "), " +
                    " toUInt64(URLHash(keyword_name)), dictGetUInt32('geo_master_mapping', 'geoMasterId', tuple(location_id))))  AS createDate      ");
        }
        sql.append(" FROM ( ");
        sql.append(" select * ");
        sql.append(" FROM ( ");
        sql.append(" select ranking_date,keyword_rankcheck_id, location_id,keyword_name,avg_search_volume,URLHash(keyword_name) AS keyword_hash, ");
        if(extractQueryVO.getEnableCustomSearchVolume() != null && extractQueryVO.getEnableCustomSearchVolume() == 1){
            sql.append(" dictGetInt64('file_dic_custom_searchvol', 'avgSearchVolume', (toUInt32(own_domain_id),toUInt16(engine_id), toUInt16(language_id),toUInt64(keyword_rankcheck_id), dictGetUInt32('geo_master_mapping', 'geoMasterId', tuple(location_id)))) as trueDemand,");
        }
        sql.append("   attrstr.value[indexOf(attrstr.key, 'google_recommend')] as googleRecommend, ");
        sql.append("   attrstr.value[indexOf(attrstr.key, 'google_result_count')] as totalResults, ");
        sql.append("   if(attrint.value[indexOf(attrint.key, 'app_flg')] > 0, 'y', 'n') as appFlg, ");
        sql.append("   if(attrint.value[indexOf(attrint.key, 'img_flg')] > 0, 'y', 'n') as imgFlg, ");
        sql.append("   if(attrint.value[indexOf(attrint.key, 'news_flg')] > 0, 'y', 'n') as newsFlg, ");
        sql.append("   if(attrint.value[indexOf(attrint.key, 'video_flg')] > 0, 'y', 'n') as videoFlg, ");
        sql.append("   if(ll_flg > 0, 'y', 'n') as llFlg, ");
        sql.append("   if(ppc_flg > 0, 'y', 'n') as ppcFlg, ");
        sql.append("   if(answerbox_url != '' and answerbox_url != 'null' and length(answerbox_url) > 4, 'y', 'n') as answerBoxFlg, ");
        sql.append("   if(attrint.value[indexOf(attrint.key, 'hotel_flg')] > 0, 'y', 'n') as hotelFlag, ");
        sql.append("   if(attrint.value[indexOf(attrint.key, 'flight_search_flg')] > 0, 'y', 'n') as flightSearchFlg, ");
        sql.append("   if(pla_flg > 0, 'y', 'n') as pla_flg, ");
        sql.append("   if(knog_flg > 0, 'y', 'n') as knog_flg, ");
        sql.append("   if(attrint.value[indexOf(attrint.key, 'fromsourcesacrosstheweb_flg')] > 0, 'y', 'n') as fromsourcesacrossthewebFlag, ");
        sql.append("   if(attrint.value[indexOf(attrint.key, 'findresultson_flg')] > 0, 'y', 'n') as findresultsonFlag, ");
        sql.append("   if(attrint.value[indexOf(attrint.key, 'populardestinations_flg')] > 0, 'y', 'n') as populardestinationsFlag, ");
        sql.append("   if(attrint.value[indexOf(attrint.key, 'ai_genai_search_flg')] > 0, 'y', 'n') as aioFlag, ");
        sql.append("   if(attrint.value[indexOf(attrint.key, 'popularRecipesFlg')] > 0, 'y', 'n') as popularRecipesFlag, ");
        sql.append("   if(attrint.value[indexOf(attrint.key, 'popularStoreFlg')] > 0, 'y', 'n') as popularStoreFlag, ");
        sql.append("   if(attrint.value[indexOf(attrint.key, 'discussions_and_forums')] > 0, 'y', 'n') as discussionsandforumsFlag, ");
        sql.append("   if(attrint.value[indexOf(attrint.key, 'buying_guide_flg')] > 0, 'y', 'n') as buyingguideFlag, ");
        sql.append("   splitByString('@_@', attrstr.value[indexOf(attrstr.key, 'additionalQuestions')]) as peopleAlsoAskList, ");
        sql.append("   splitByString('!_!', attrstr.value[indexOf(attrstr.key, 'refineBy')])[1] as refineBy_title, ");
        sql.append("   splitByString('!_!', attrstr.value[indexOf(attrstr.key, 'refineBy')])[2] as refineBy_detail, ");
        sql.append("   attrstr.value[indexOf(attrstr.key, 'appbar_display_keywords')] as serpFilterButtonTextStr,");
        sql.append("   attrstr.value[indexOf(attrstr.key, 'appbar_searched_keywords')] as serpFilterKeywordsStr, ");
        sql.append("   attrstr.value[indexOf(attrstr.key, 'things_to_know')] AS thingsToKnow ");
        sql.append(" FROM ").append(infoTableName);
        sql.append(" WHERE own_domain_id = ").append(extractQueryVO.getDomainId());
        sql.append(" AND engine_id =  ").append(engineId);
        sql.append(" AND language_id =  ").append(languageId);
        sql.append(" AND ranking_date = '").append(extractQueryVO.getRankDate()).append("' ");
        if (!extractQueryVO.getExtractAllLocation()) {
            if (extractQueryVO.getExtractGeo() == null || !extractQueryVO.getExtractGeo()) {
                sql.append(" AND location_id =  ").append(extractQueryVO.getLocationId());
            } else {
                sql.append(" AND location_id != 0 ");
            }
        }
        //query for tag
        sql.append(" )t1 ANY LEFT JOIN ( ");
        sql.append("    SELECT keyword_rankcheck_id , location_id, arrayDistinct(groupArray(tagName)) AS tagList,groupArray(nodes) as tagIdHierarchy ");
        sql.append("    from ( ");
        sql.append("        SELECT grouptag_id, tag_name as tagName, keyword_rankcheck_id, location_id ");
        sql.append("            ,dictGetHierarchy('dic_keyword_tag_child_parent', toUInt64(grouptag_id)) as nodes");
        sql.append("        FROM ( ");
        sql.append("            SELECT distinct keyword_rankcheck_id,grouptag_id,location_id ");
        sql.append("            FROM seo_daily_ranking.cdb_tracked_keyword ");
        sql.append("            WHERE own_domain_id = " + extractQueryVO.getDomainId());
        sql.append("            AND (grouptag_id > 0) ");
        if (!extractQueryVO.getExtractAllLocation() && extractQueryVO.getExtractGeo() != null && extractQueryVO.getExtractGeo()) {
            sql.append(" AND keyword_type IN (2) ");
        }else {
            sql.append(" AND keyword_type IN (1,2) ");
        }
        sql.append("            GROUP BY grouptag_id, own_domain_id, location_id, grouptag_status, keyword_rankcheck_id ");
        sql.append("            HAVING SUM(sign) > 0 AND (grouptag_status = 1) ");
        sql.append("        ) AS a1 ANY LEFT JOIN ( ");
        sql.append("            SELECT grouptag_id, tag_name ");
        sql.append("            FROM seo_daily_ranking.dis_group_tag ");
        sql.append("            WHERE own_domain_id = " + extractQueryVO.getDomainId() + " AND (tag_type = 2) ");
        sql.append("        ) AS a2 USING (grouptag_id) ");
        sql.append("    )a3 GROUP BY keyword_rankcheck_id, location_id ");
        sql.append(" )t2 USING(keyword_rankcheck_id, location_id) ");

        sql.append(" WHERE 1=1 ");
        if(StringUtils.isNotBlank(extractQueryVO.getTagNames())){
            sql.append("   AND (keyword_rankcheck_id GLOBAL IN  ");
            sql.append("   ( ");
            sql.append("     SELECT DISTINCT keyword_rankcheck_id ");
            sql.append("     FROM seo_daily_ranking.cdb_tracked_keyword ");
            sql.append("     WHERE (own_domain_id = " + extractQueryVO.getDomainId() + ") AND (grouptag_id > 0)  ");
            if (!extractQueryVO.getExtractAllLocation() && extractQueryVO.getExtractGeo() != null && extractQueryVO.getExtractGeo()) {
                sql.append(" AND keyword_type IN (2) ");
            }else {
                sql.append(" AND keyword_type IN (1,2) ");
            }
            sql.append("     AND (grouptag_id GLOBAL IN ");
            sql.append("     ( ");
            sql.append("       SELECT grouptag_id AS groupTagId ");
            sql.append("       FROM seo_daily_ranking.dis_group_tag ");
            sql.append("       WHERE (own_domain_id = " + extractQueryVO.getDomainId() + ") AND (tag_type = 2) AND (tag_name = '"+ extractQueryVO.getTagNames() +"') ");
            sql.append("       GROUP BY  ");
            sql.append("         grouptag_id,  ");
            sql.append("         own_domain_id,  ");
            sql.append("         tag_type ");
            sql.append("       HAVING SUM(sign) > 0 ");
            sql.append("     )) ");
            sql.append("     GROUP BY  ");
            sql.append("       grouptag_id,  ");
            sql.append("       own_domain_id,  ");
            sql.append("       location_id,  ");
            sql.append("       grouptag_status,  ");
            sql.append("       keyword_rankcheck_id ");
            sql.append("     HAVING (SUM(sign) > 0) AND (grouptag_status = 1) ");
            sql.append("   )) ");
        }

        sql.append(" )tt1 All LEFT JOIN( ");
        sql.append("  SELECT keyword_hash, groupArray(url) as plpUrlList ");
        sql.append("      FROM daily_ranking.dis_keyword_page_rel   ");
        sql.append("      WHERE own_domain_id =").append(extractQueryVO.getDomainId());
        sql.append("      group by keyword_hash ");
        sql.append(" )t4 USING (keyword_hash) ");

        System.out.println("sql:" + sql.toString());
        return findBySql(sql.toString(), CLRankingDetailEntity.class);
    }

    public List<CLRankingDetailEntity> getEstdInfoWithMultFilter(ExtractQueryVO extractQueryVO, List<Long> kwRankCheckIdSet) {
        int year = Integer.parseInt(extractQueryVO.getRankDate().substring(0, 4));
        int engineId = extractQueryVO.getEngineId();
        int languageId = extractQueryVO.getLanguageId();
        boolean isMobile = extractQueryVO.getDevice().equalsIgnoreCase("mobile") ? true : false;
        String detailTableName = getDetailTable(engineId, languageId, isMobile, year);

        StringBuffer sql = new StringBuffer();
        sql.append("  select keyword_rankcheck_id, locationId AS location_id,      ");
        sql.append("        maxAvg_search_volume, ownDomainRank, estTraffic, ");
        sql.append("        round(estTraffic/" + extractQueryVO.getMonthDayCount() + ",0) as estdTraffic,   ");
        sql.append("        if(maxAvg_search_volume = 0, 0, round((estTraffic / (maxAvg_search_volume * (ctr[1]))) + 0.00001, 4)) AS shareOfVoice,  ");
        sql.append("        if(maxAvg_search_volume = 0, 0, round((estTraffic / (maxAvg_search_volume * " + extractQueryVO.getSumCtr() + ")) + 0.00001, 4)) AS shareOfMarket,    ");
        sql.append("        ranking_date ");
        sql.append(" from ( ");
        sql.append("    SELECT ");
        sql.append("        keyword_rankcheck_id, dictGetUInt32('geo_master_mapping', 'geoMasterId', tuple(location_id)) AS locationId,         ");
        sql.append("        MAX(avg_search_volume) AS maxAvg_search_volume, ");
        sql.append("         [" + StringUtils.join(extractQueryVO.getCtrList(), ",") + "] AS ctr, ");
        if (extractQueryVO.getIsBroadMatch() != null && extractQueryVO.getIsBroadMatch()) {
            sql.append("        MIN(multiIf((root_domain_reverse = '" + extractQueryVO.getRootDomainReverse() + "') AND (hrrd = 1), true_rank, 101)) AS ownDomainRank,  ");
        }else {
            sql.append("        MIN(multiIf( (root_domain_reverse = '" + extractQueryVO.getRootDomainReverse() + "') and (domain_reverse = '" + extractQueryVO.getDomainReverse() + "') AND (hrd = 1), true_rank, 101)) AS ownDomainRank,  ");
        }

        sql.append("        if(ownDomainRank > 30, 0, maxAvg_search_volume * (ctr[ownDomainRank])) AS estTraffic,  ");
        sql.append("        ranking_date ");
        sql.append(" FROM ").append(detailTableName);
        sql.append(" WHERE (sign = 1) and own_domain_id = ").append(extractQueryVO.getDomainId());
        sql.append(" AND engine_id =  ").append(engineId);
        sql.append(" AND language_id =  ").append(languageId);
        sql.append(" AND ranking_date = '").append(extractQueryVO.getRankDate()).append("' ");
        sql.append(" and keyword_rankcheck_id in (" + StringUtils.join(kwRankCheckIdSet, ",")).append(" ) ");

        if (!extractQueryVO.getExtractAllLocation()) {
            if (extractQueryVO.getExtractGeo() == null || !extractQueryVO.getExtractGeo()) {
                sql.append(" AND location_id =  ").append(extractQueryVO.getLocationId());
            } else {
                sql.append(" AND location_id != 0 ");
            }
        }
        if (extractQueryVO.getIsBroadMatch() != null && extractQueryVO.getIsBroadMatch()) {
            sql.append("     AND root_domain_reverse = '").append(extractQueryVO.getRootDomainReverse()).append("' ");
            sql.append("     AND hrrd = 1 ");
        } else {
            if (StringUtils.isNotEmpty(extractQueryVO.getDomainReverse())) {
                sql.append("     AND domain_reverse = '").append(extractQueryVO.getDomainReverse());
            }
            if (StringUtils.isNotEmpty(extractQueryVO.getRootDomainReverse())) {
                sql.append("' AND root_domain_reverse = '").append(extractQueryVO.getRootDomainReverse()).append("' ");
            }
            sql.append("     AND hrd = 1 ");
        }
        sql.append("  GROUP BY keyword_rankcheck_id, locationId, ranking_date   ");
        sql.append(" )t1 ");
        System.out.println("getEstdInfoWithMultFilter:" + sql.toString());
        return findBySql(sql.toString(), CLRankingDetailEntity.class);
    }

    public List<CLRankingDetailEntity> exportDetailJsonWithMultFilter(ExtractQueryVO extractQueryVO, List<Long> kwRankCheckIdSet) {
        int year = Integer.parseInt(extractQueryVO.getRankDate().substring(0, 4));
        int engineId = extractQueryVO.getEngineId();
        int languageId = extractQueryVO.getLanguageId();
        boolean isMobile = extractQueryVO.getDevice().equalsIgnoreCase("mobile") ? true : false;
        String detailTableName = getDetailTable(engineId, languageId, isMobile, year);

        StringBuffer sql = new StringBuffer();
        sql.append(" SELECT ");
        sql.append("    keyword_rankcheck_id,location_id,type,");
        sql.append("    webRank, trueRank,title,meta,ratingNumber,priceNumber,couponFlag, ");
        sql.append("    avg_search_volume, multiIf(domain0VisualRank2 > 0, domain0VisualRank2, 101) AS visualRank,    ");
        sql.append("    visibility,");
        sql.append("    if((domain0FoldNumber2 > 10) OR (domain0FoldNumber2 = 0), 11, domain0FoldNumber2) AS FoldVisibility,     ");
        sql.append("    domain0OffsetTop2 as PixelDepth,");
        sql.append("    domain0SoVisibility2 as ShareOfVisibility,");
        //https://www.wrike.com/open.htm?id=1613210538 wait for change 'Unknown SERP Feature' url name
        sql.append("    if(if(domain_reverse = '', t2.ved_name, url) = '', 'Unknown SERP Feature', if(domain_reverse = '', t2.ved_name, url)) as url ");
        sql.append(" from (");
        sql.append("    select keyword_rankcheck_id, location_id, url,type,avg_search_volume,domain_reverse,      ");
        sql.append("        web_rank as webRank, true_rank as trueRank, ");
        sql.append("        label as title,meta,rating as ratingNumber, ");
        sql.append("        attrs.value[indexOf(attrs.key, 'actual_price')] as priceNumber, ");
        sql.append("        if(attrs.value[indexOf(attrs.key, 'is_coupon_flg')] = '1', 'y', 'n') as couponFlag, ");
        sql.append("         [" + StringUtils.join(extractQueryVO.getFoldRateList(), ",") + "] AS foldRate, ");
        sql.append("         [" + StringUtils.join(extractQueryVO.getFoldRankRateList(), ",") + "] AS foldRankRate, ");
        sql.append("        toUInt16(if((attrs.value[indexOf(attrs.key, 'total_offset_top')]) = '', '0', attrs.value[indexOf(attrs.key, 'total_offset_top')])) AS totalOffsetTop, ");
        sql.append("        toUInt16(if((attrs.value[indexOf(attrs.key, 'appbar_top')]) = '', '0', attrs.value[indexOf(attrs.key, 'appbar_top')])) AS appbarTop,");
        sql.append("        visual_rank AS domain0VisualRank2,fold AS domain0FoldNumber2,fold_rank AS domain0FoldRank2,height AS domain0Height2,");
        sql.append("        offset_top AS domain0OffsetTop2,");
        sql.append("        round(((foldRate[domain0FoldNumber2]) * (foldRankRate[domain0FoldRank2])) * domain0Height2) AS visibility,");
        sql.append("        ROUND(if(totalOffsetTop = 0, 0, 1 - ((domain0OffsetTop2 - appbarTop) / totalOffsetTop)), 4) AS domain0SoVisibility2,");
        sql.append("        type_main_array[1] as ved ");

        sql.append(" FROM ").append(detailTableName);
        sql.append(" WHERE own_domain_id = ").append(extractQueryVO.getDomainId());
        sql.append(" AND engine_id =  ").append(engineId);
        sql.append(" AND language_id =  ").append(languageId);
        sql.append(" AND ranking_date = '").append(extractQueryVO.getRankDate()).append("' ");
        sql.append(" AND true_rank <=  ").append(extractQueryVO.getRank());
        sql.append(" and keyword_rankcheck_id in (" + StringUtils.join(kwRankCheckIdSet, ",")).append(" ) ");
        if (!extractQueryVO.getExtractAllLocation()) {
            if (extractQueryVO.getExtractGeo() == null || !extractQueryVO.getExtractGeo()) {
                sql.append(" AND location_id =  ").append(extractQueryVO.getLocationId());
            } else {
                sql.append(" AND location_id != 0 ");
            }
        }
        if (extractQueryVO.getIsBroadMatch() != null && extractQueryVO.getIsBroadMatch()) {
            sql.append("     AND root_domain_reverse = '").append(extractQueryVO.getRootDomainReverse()).append("' ");
            if (extractQueryVO.getIsHighest() != null) {
                sql.append("     AND hrrd = 1 ");
            }
        } else {
            if (StringUtils.isNotEmpty(extractQueryVO.getDomainReverse())) {
                sql.append("     AND domain_reverse = '").append(extractQueryVO.getDomainReverse());
            }
            if (StringUtils.isNotEmpty(extractQueryVO.getRootDomainReverse())) {
                sql.append("' AND root_domain_reverse = '").append(extractQueryVO.getRootDomainReverse()).append("' ");
            }
            if (extractQueryVO.getIsHighest() != null) {
                sql.append("     AND hrd = 1 ");
            }
        }

        sql.append(" )t1 any left join ( ");
        sql.append(" select ved, main_feature, domain, if(main_feature = '', domain, main_feature) as ved_name from master_ved_20240723");
        if(!isMobile){//mobile device select all data
            sql.append(" where device = 'd' ");
        }
        sql.append(" ) t2 using (ved)");
        System.out.println("sql:" + sql.toString());
        return findBySql(sql.toString(), CLRankingDetailEntity.class);
    }

    public List<CLRankingDetailEntity> exportSunRankJsonWithMultFilter(ExtractQueryVO extractQueryVO, List<Long> kwRankCheckIdSet) {
        int year = Integer.parseInt(extractQueryVO.getRankDate().substring(0, 4));
        int engineId = extractQueryVO.getEngineId();
        int languageId = extractQueryVO.getLanguageId();
        boolean isMobile = extractQueryVO.getDevice().equalsIgnoreCase("mobile") ? true : false;
        String subrankTableName = getSubrankTable(engineId, languageId, isMobile, year);
        StringBuffer sql = new StringBuffer();
        sql.append("  select keyword_rankcheck_id,location_id,rank as trueRank, sub_rank,   ");
        sql.append("   multiIf(ranking_type = 4 or ranking_type = 1, domain_reverse, url) as subRankUrl,  ");
        sql.append("   attrs.value[indexOf(attrs.key, 'label')] as subRankLabel ");
        sql.append(" from ").append(subrankTableName);
        sql.append("    WHERE own_domain_id = ").append(extractQueryVO.getDomainId());
        sql.append("    AND engine_id =  ").append(engineId);
        sql.append("    AND language_id =  ").append(languageId);
        sql.append("    AND ranking_date = '").append(extractQueryVO.getRankDate()).append("' ");
        if (!extractQueryVO.getExtractAllLocation()) {
            if (extractQueryVO.getExtractGeo() == null || !extractQueryVO.getExtractGeo()) {
                sql.append(" AND location_id =  ").append(extractQueryVO.getLocationId());
            } else {
                sql.append(" AND location_id != 0 ");
            }
        }
        sql.append(" and keyword_rankcheck_id in (" + StringUtils.join(kwRankCheckIdSet, ",")).append(" ) ");
        sql.append(" and ranking_type != 2   ");
        sql.append(" union all   ");
        sql.append("  select keyword_rankcheck_id,location_id,trueRank, sub_rank, subRank_url,subRankLabel  ");
        sql.append("  from (  ");
        sql.append("   select keyword_rankcheck_id,location_id,rank as trueRank,  ");
        sql.append("   indexOf(questionArray, question) as sub_rank, splitByString('!_!', url) as questionArray, ");
        sql.append("   question as subRank_url, attrs.value[indexOf(attrs.key, 'label')] as subRankLabel ");
        sql.append(" from ").append(subrankTableName);
        sql.append(" array join questionArray as question  ");
        sql.append("    WHERE own_domain_id = ").append(extractQueryVO.getDomainId());
        sql.append("    AND engine_id =  ").append(engineId);
        sql.append("    AND language_id =  ").append(languageId);
        sql.append("    AND ranking_date = '").append(extractQueryVO.getRankDate()).append("' ");
        if (!extractQueryVO.getExtractAllLocation()) {
            if (extractQueryVO.getExtractGeo() == null || !extractQueryVO.getExtractGeo()) {
                sql.append(" AND location_id =  ").append(extractQueryVO.getLocationId());
            } else {
                sql.append(" AND location_id != 0 ");
            }
        }
        sql.append(" and keyword_rankcheck_id in (" + StringUtils.join(kwRankCheckIdSet, ",")).append(" ) ");
        sql.append("   and ranking_type = 2  ");
        sql.append("  )  ");
        System.out.println("sql:" + sql.toString());
        return findBySql(sql.toString(), CLRankingDetailEntity.class);
    }

}
