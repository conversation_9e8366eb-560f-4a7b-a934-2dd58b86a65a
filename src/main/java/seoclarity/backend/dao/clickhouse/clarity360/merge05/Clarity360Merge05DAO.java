package seoclarity.backend.dao.clickhouse.clarity360.merge05;

import java.math.BigInteger;
import java.sql.Types;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.stereotype.Repository;

import com.google.gson.Gson;

import seoclarity.backend.clarity360.vo.BacklinkVO;
import seoclarity.backend.clarity360.vo.BotVO;
import seoclarity.backend.clarity360.vo.CustomDataSourceVO;
import seoclarity.backend.clarity360.vo.GscVO;
import seoclarity.backend.clarity360.vo.InternalLinkVO;
import seoclarity.backend.clarity360.vo.RgVO;
import seoclarity.backend.clarity360.vo.RiVO;
import seoclarity.backend.clarity360.vo.SiteHealthVO;
import seoclarity.backend.clarity360.vo.SiteMapVO;
import seoclarity.backend.dao.clickhouse.ClDailyRankingEntityDao;
import seoclarity.backend.entity.ClarityDBConstants;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.export.vo.ClarityDBTypesFilterVO;
import seoclarity.backend.export.vo.PaginationColFilter;
import seoclarity.backend.summary.Clarity360Entity;
import seoclarity.backend.utils.ClarityDBUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.vectordb.EmbeddingEntity;

@Repository
public class Clarity360Merge05DAO extends Clarity360MergeBaseJdbcSupport05<Clarity360Entity> {


    @Override
    public String getTableName() {
        return "local_360_table_v3";
    }
    
    public static String getLocalTableNameByVersion() {
        return "local_360_table_v3";
    }
    
    
    public static String getDisTableNameByType(Boolean isForUrlHash) {
    	if (isForUrlHash) {
    		return getTableNameForUrlHash();
		}
        return "dis_clarity360_v3";
    }
    
    public static String getTableNameForUrlHash() {
		 return "dis_clarity360_custom_urlmurmurhash_v3";
    }
    
    public static String getFinalTableNameByType(Boolean isForUrlHash) {
    	if (isForUrlHash) {
    		return "dis_clarity360_custom_urlmurmurhash_v3_final";
		}
        return "dis_clarity360_v3_final";
    }


    public void optimizeTable(String tempTableName, Integer reportId){
        String sql = "optimize table clarity360." + tempTableName + " partition '" + reportId + "' final";
        System.out.println(sql);
        this.executeUpdate(sql);
    }
    
    public void optimizeTable(String currentProcessMonth){
        String sql = "optimize table clarity360." + getLocalTableNameByVersion() + " partition " + currentProcessMonth + " final";
        System.out.println(sql);
        this.executeUpdate(sql);
    }
    
	public Boolean isTableExist(String tempTableName) {
		String sql = "show tables like '" + tempTableName + "' ";
		System.out.println("sql:" + sql);
		return StringUtils.isNotBlank(this.queryForString(sql));
	}
	
	public void dropTable(String tempTableName) {
		String sql = "drop table " + tempTableName + " ";
		System.out.println("sql:" + sql);
		this.executeUpdate(sql);
	}
	
	public void createTable(String tempTableName, String templateTableName) {
		String sql = "create table " + tempTableName + " as  " + templateTableName
				+ " ENGINE = AggregatingMergeTree PARTITION BY track_date ORDER BY (domain_id, report_id, track_date, url_murmur_hash) SETTINGS index_granularity = 8192 ";
		System.out.println("sql:" + sql);
		this.executeUpdate(sql);
	}
	
	
	
	public void gscClarity360SummaryMerged(Integer ownDomainId, GscVO gscVO, String targetDate, Integer reportId, String tempTableName) {
		
		if (ArrayUtils.isEmpty(gscVO.getAcrossDomainIds()) || ArrayUtils.isEmpty(gscVO.getRelIds())) {
			System.out.println("Domain list is empty or relid is empty!");
			return;
		}
    	StringBuffer sql = new StringBuffer();
    	sql.append(" insert into " + tempTableName + " ( domain_id, url, url_murmur_hash, gsc_clicks, gsc_impressions, gsc_pos_sum,   ");
    	sql.append("  gsc_pos_count, sources,  target_date,  process_date, searchanlytics_numofdays, report_id, folder_level_1, folder_level_2, folder_level_3 )   ");
    	sql.append("  SELECT  ");
    	sql.append("     any(unique_own_domain_id), ");
    	sql.append(" 	any(url),  ");
    	sql.append(" 	any(url_murmur_hash), ");
    	sql.append(" 	sum(clicks), ");
    	sql.append(" 	SUM(impressions), ");
    	sql.append(" 	SUM(position), ");
    	sql.append(" 	count(), ");
    	sql.append(" 	['gsc'],?,  toDate(now()), count(distinct unique_log_date), " + reportId + ",    ");
    	sql.append("  ");
    	sql.append("  if(endsWith(URLPathHierarchy(cutQueryString(any(url)))[1], '/'), replaceAll(URLPathHierarchy(cutQueryString(any(url)))[1], '/', ''), '') AS folder1,   ");
    	sql.append("  if(endsWith(if(length(URLPathHierarchy(cutQueryString(any(url)))[2]) > 0, substring(URLPathHierarchy(cutQueryString(any(url)))[2], length(URLPathHierarchy(cutQueryString(any(url)))[1]), length(URLPathHierarchy(cutQueryString(any(url)))[2])), ''), '/'), replaceAll(if(length(URLPathHierarchy(cutQueryString(any(url)))[2]) > 0, substring(URLPathHierarchy(cutQueryString(any(url)))[2], length(URLPathHierarchy(cutQueryString(any(url)))[1]), length(URLPathHierarchy(cutQueryString(any(url)))[2])), ''), '/', ''), '') AS folder2,   ");
    	sql.append("  if(endsWith(if(length(URLPathHierarchy(cutQueryString(any(url)))[3]) > 0, substring(URLPathHierarchy(cutQueryString(any(url)))[3], length(URLPathHierarchy(cutQueryString(any(url)))[2]), length(URLPathHierarchy(cutQueryString(any(url)))[3])), ''), '/'), replaceAll(if(length(URLPathHierarchy(cutQueryString(any(url)))[3]) > 0, substring(URLPathHierarchy(cutQueryString(any(url)))[3], length(URLPathHierarchy(cutQueryString(any(url)))[2]), length(URLPathHierarchy(cutQueryString(any(url)))[3])), ''), '/', ''), '') AS folder3   ");
    	sql.append("  ");
    	sql.append(" FROM  ");
    	sql.append(" ( ");
    	sql.append(" 	SELECT  ");
    	sql.append(" 		MAX(clicks) AS clicks,  ");
    	sql.append(" 		MAX(impressions) AS impressions,  ");
    	sql.append(" 		MAX(position) AS position,  ");
    	sql.append(" 		MAX(device) AS device,  ");
    	sql.append(" 		MAX(url) AS url,  ");
    	sql.append(" 		MAX(keyword_name) AS keyword_name,  ");
    	sql.append(" 		MAX(url_murmur_hash) AS url_murmur_hash,  ");
    	sql.append(" 		MAX(own_domain_id) AS unique_own_domain_id, ");
    	sql.append(" 		MAX(log_date) AS unique_log_date ");
    	sql.append(" 	FROM actonia_gsc.gsc_daily_insert ");
    	sql.append(" 	WHERE (1 = 1) AND (log_date >= '" + gscVO.getStartDate() + "') AND (log_date <= '" + gscVO.getEndDate() + "') AND (own_domain_id = " + StringUtils.join(gscVO.getAcrossDomainIds(), ",") + ") AND (rel_id IN (" + StringUtils.join(gscVO.getRelIds(), ",") + ")) AND (versioning = dictGetUInt16('claritydbVersionGSC', 'versioning', (1, toUInt32(own_domain_id), toDate(log_date), toUInt64(0)))) AND (type = 1) AND (length(attrs.key) = 0) ");
    	sql.append(" 	GROUP BY keyhash ");
    	sql.append(" 		SETTINGS distributed_group_by_no_merge = 1, distributed_product_mode = 'local' ");
    	sql.append("  ");
    	sql.append(" ) ");
    	sql.append(" GROUP BY url_murmur_hash ");
    	
    	System.out.println(sql.toString());
    	this.executeUpdate(sql.toString(), targetDate, ownDomainId);
    }
	
	public void riClarity360Summary(Integer ownDomainId, RiVO riVO, String targetDate, Integer reportId, String tempTableName) {
		StringBuffer sql = new StringBuffer();
		
		System.out.println("riVO.getEngineId():" + riVO.getEngineId());
		System.out.println("riVO.getLanguageId():" + riVO.getLanguageId());
		
		String detailTableName = ClDailyRankingEntityDao.getTableNameForKP(
				ClDailyRankingEntityDao.getDateFromStringToInteger(riVO.getStartDate()),
				ClDailyRankingEntityDao.getDateFromStringToInteger(riVO.getEndDate()),
                riVO.getEngineId(), riVO.getLanguageId(), 
                StringUtils.equals(riVO.getDevice(), "m"), false, ClDailyRankingEntityDao.RANK_DETAIL_TYPE);
		
		sql.append(" insert into " + tempTableName + " ( domain_id, url, url_murmur_hash, `ri_avg_rank`, `ri_wtd_avg_rank`, ");
		sql.append(" `ri_total_sv`, `ri_top1`, `ri_top3`, `ri_top10`, sources,  target_date,  process_date, ri_mumofdays, report_id ) ");
		sql.append(" select    any(own_domain_id),         any(url) ,         murmurHash3_64(url) AS urlMurmurHash, ");
		sql.append(" round(avg(true_rank),2),          case when sum(avg_search_volume)=0 then 101 else ");
		sql.append(" round(sum(true_rank * avg_search_volume)/sum(avg_search_volume),2) end as wtd_avg_rank, ");
		sql.append(" sum(avg_search_volume) AS svTotal,         sum(if(true_rank = 1, 1, 0)) AS top1Cnt, ");
		sql.append(" sum(if((true_rank <= 3) AND (true_rank > 0), 1, 0)) AS top3Cnt, ");
		sql.append(" sum(if((true_rank <= 10) AND (true_rank > 0), 1, 0)) AS top10Cnt, ");
		sql.append(" ['ri'], ?, toDate(now()), count(distinct ranking_date), " + reportId + " from " + detailTableName + "     ");
		sql.append(" where own_domain_id= ? and ranking_date>='" + riVO.getStartDate() + "' and ranking_date<='" + riVO.getEndDate() + "' ");
		sql.append(" AND (engine_id = ?) AND (language_id = ?) AND (root_domain_reverse = '" + ClarityDBUtils.getReverseRootDomain(riVO.getDomainName(), true) + "') AND (hrrd = 1)    ");
		sql.append("  AND (location_id = ?) AND (sign = 1)       GROUP BY         urlMurmurHash ");
		
		System.out.println(sql.toString());
    	this.executeUpdate(sql.toString(), targetDate, ownDomainId, riVO.getEngineId(), riVO.getLanguageId(), riVO.getLocationId());
	}
	
	public static Integer DATA_SOURCE_TYPE_OMNITURE = 2;
	
    public void botClarity360Summary(Integer ownDomainId, BotVO botVO, String targetDate, Integer reportId, String tempTableName) {
    	StringBuffer sql = new StringBuffer();
    	sql.append(" insert into " + tempTableName + " ( domain_id, url, url_murmur_hash, bot_requestcount, bot_2xxrequestcount, bot_3xxrequestcount, ");
    	sql.append(" bot_4xxrequestcount, bot_5xxrequestcount, sources,  target_date,  process_date, bot_numofdays, report_id, folder_level_1,folder_level_2,folder_level_3)   ");
    	sql.append(" SELECT  any(own_domain_id) AS domain_id,  any(dis_bot_detail.url) ,  ");
    	sql.append(" murmurHash3_64(dis_bot_detail.url) AS url_murmur_hash,  ");
    	sql.append(" count(murmurHash3_64(dis_bot_detail.url)) AS bot_requestcount,  ");
    	sql.append(" sum(multiIf((response_code >= 200) AND (response_code < 300), 1, 0)) AS bot_2xxrequestcount,  ");
    	sql.append(" sum(multiIf((response_code >= 300) AND (response_code < 400), 1, 0)) AS bot_3xxrequestcount,  ");
    	sql.append(" sum(multiIf((response_code >= 400) AND (response_code < 500), 1, 0)) AS bot_4xxrequestcount,  ");
    	sql.append(" sum(multiIf((response_code >= 500) AND (response_code < 600), 1, 0)) AS bot_5xxrequestcount,  ");
    	sql.append(" ['bot'],  ?, toDate(now()), count(distinct bot_date) , " + reportId + ",   ");
    	
    	sql.append(" if(endsWith(URLPathHierarchy(cutQueryString(any(dis_bot_detail.url)))[1], '/'), replaceAll(URLPathHierarchy(cutQueryString(any(dis_bot_detail.url)))[1], '/', ''), '') AS folder1, ");
    	sql.append(" if(endsWith(if(length(URLPathHierarchy(cutQueryString(any(dis_bot_detail.url)))[2]) > 0, substring(URLPathHierarchy(cutQueryString(any(dis_bot_detail.url)))[2], length(URLPathHierarchy(cutQueryString(any(dis_bot_detail.url)))[1]), length(URLPathHierarchy(cutQueryString(any(dis_bot_detail.url)))[2])), ''), '/'), replaceAll(if(length(URLPathHierarchy(cutQueryString(any(dis_bot_detail.url)))[2]) > 0, substring(URLPathHierarchy(cutQueryString(any(dis_bot_detail.url)))[2], length(URLPathHierarchy(cutQueryString(any(dis_bot_detail.url)))[1]), length(URLPathHierarchy(cutQueryString(any(dis_bot_detail.url)))[2])), ''), '/', ''), '') AS folder2, ");
    	sql.append(" if(endsWith(if(length(URLPathHierarchy(cutQueryString(any(dis_bot_detail.url)))[3]) > 0, substring(URLPathHierarchy(cutQueryString(any(dis_bot_detail.url)))[3], length(URLPathHierarchy(cutQueryString(any(dis_bot_detail.url)))[2]), length(URLPathHierarchy(cutQueryString(any(dis_bot_detail.url)))[3])), ''), '/'), replaceAll(if(length(URLPathHierarchy(cutQueryString(any(dis_bot_detail.url)))[3]) > 0, substring(URLPathHierarchy(cutQueryString(any(dis_bot_detail.url)))[3], length(URLPathHierarchy(cutQueryString(any(dis_bot_detail.url)))[2]), length(URLPathHierarchy(cutQueryString(any(dis_bot_detail.url)))[3])), ''), '/', ''), '') AS folder3 ");
    	
    	sql.append(" FROM clarity_bot.dis_bot_detail ");
    	sql.append(" WHERE own_domain_id = ? AND bot_date>= '" + botVO.getStartDate() + "' and bot_date<='" + botVO.getEndDate() + "' AND (ua_group_id IN (1, 2))  ");
    	sql.append(" GROUP BY url_murmur_hash  ");
    	sql.append(" SETTINGS distributed_group_by_no_merge = 1, distributed_product_mode = 'local', max_bytes_before_external_group_by = 20000000000 ");
    	
    	System.out.println(sql.toString());
    	this.executeUpdate(sql.toString(), targetDate, ownDomainId);
    }
	
    public void siteMapClarity360Summary(Integer ownDomainId, SiteMapVO siteMapVO, String targetDate, Integer reportId, String tempTableName) {
    	StringBuffer sql = new StringBuffer();
    	
    	String requestDate = FormatUtils.formatDate(FormatUtils.getMondayOfCurrentWeek(DateUtils.addWeeks(new Date(), -1)), "yyyy-MM-dd");
    	
    	sql.append(" insert into " + tempTableName + " ( domain_id, url, url_murmur_hash, sources,  target_date,  process_date, report_id, sitemap_name,folder_level_1,folder_level_2,folder_level_3 ) ");
    	sql.append(" SELECT any(own_domain_id), any(url),  murmurHash3_64(url) AS url_murmur_hash, ['sitemap'],  ?, toDate(now()), " + reportId + ", any(sitemap),");
    	
    	sql.append(" if(endsWith(URLPathHierarchy(cutQueryString(any(url)))[1], '/'), replaceAll(URLPathHierarchy(cutQueryString(any(url)))[1], '/', ''), '') AS folder1, ");
    	sql.append(" if(endsWith(if(length(URLPathHierarchy(cutQueryString(any(url)))[2]) > 0, substring(URLPathHierarchy(cutQueryString(any(url)))[2], length(URLPathHierarchy(cutQueryString(any(url)))[1]), length(URLPathHierarchy(cutQueryString(any(url)))[2])), ''), '/'), replaceAll(if(length(URLPathHierarchy(cutQueryString(any(url)))[2]) > 0, substring(URLPathHierarchy(cutQueryString(any(url)))[2], length(URLPathHierarchy(cutQueryString(any(url)))[1]), length(URLPathHierarchy(cutQueryString(any(url)))[2])), ''), '/', ''), '') AS folder2, ");
    	sql.append(" if(endsWith(if(length(URLPathHierarchy(cutQueryString(any(url)))[3]) > 0, substring(URLPathHierarchy(cutQueryString(any(url)))[3], length(URLPathHierarchy(cutQueryString(any(url)))[2]), length(URLPathHierarchy(cutQueryString(any(url)))[3])), ''), '/'), replaceAll(if(length(URLPathHierarchy(cutQueryString(any(url)))[3]) > 0, substring(URLPathHierarchy(cutQueryString(any(url)))[3], length(URLPathHierarchy(cutQueryString(any(url)))[2]), length(URLPathHierarchy(cutQueryString(any(url)))[3])), ''), '/', ''), '') AS folder3 ");
    	
    	sql.append(" FROM clarity_bot.dis_sitemap WHERE own_domain_id = ? and  request_date = ? group by url_murmur_hash  ");
    	
    	System.out.println(sql.toString());
    	this.executeUpdate(sql.toString(), targetDate, ownDomainId, requestDate);
    }
    
	
	public void siteHealthClarity360Summary(Integer ownDomainId, SiteHealthVO siteHealthVO, String targetDate, 
    		Integer reportId, String tempTableName) {
    	StringBuffer sql = new StringBuffer();
    	sql.append(" insert into " + tempTableName + " ( `domain_id` , `crawl_request_id` , `crawl_request_date` , `crawl_depth` , `url` , `alt_img_detail` , ");
    	sql.append(" `alt_img_list` , `alternate_links` , `amphtml_flag` , `amphtml_href` , `analyzed_url_flg_s` , `analyzed_url_s` , `archive_flg` , ");
    	sql.append(" `archive_flg_x_tag` , `blocked_by_robots` , `canonical` , `canonical_flg` , `canonical_header_flag` , `canonical_header_type` , ");
    	sql.append(" `canonical_type` , `canonical_url_is_consistent` , `content_extracted_count` , `content_extracted_flg` , `content_flg` , `content_md5` , ");
    	sql.append(" `content_type` , `content_word_count` , `content_words` , `conv_crawl_date` , `count_of_objects` , `crawl_date_long` , `description` , ");
    	sql.append(" `description_flg` , `description_length` , `description_simhash` , `document_size` , `domain` , `domain_name` , `download_latency` , ");
    	sql.append(" `download_time` , `error_message` , `final_response_code` , `folder_level_1` , `folder_level_2` , `folder_level_3` , `folder_level_count` , ");
    	sql.append(" `follow_flg` , `follow_flg_x_tag` , `h1` , `h1_count` , `h1_flg` , `h1_length` , `h1_md5` , `h1_simhash` , `h2_simhash` , `header_noarchive` , ");
    	sql.append(" `header_nofollow` , `header_noindex` , `header_noodp` , `header_nosnippet` , `header_noydir` , `hreflang_errors` , `hreflang_links` , ");
    	sql.append(" `hreflang_links_out_count` , `hreflang_url_count` , `hsts` , `index_flg` , `index_flg_x_tag` , `indexable` , `insecure_resources` , ");
    	sql.append(" `insecure_resources_flag` , `long_redirect` , `meta_charset` , `meta_content_type` , `meta_disabled_sitelinks` , `meta_noodp` , ");
    	sql.append(" `meta_nosnippet` , `meta_noydir` , `meta_redirect` , `mixed_redirects` , `mobile_rel_alternate_url_is_consistent` , `noodp` , `nosnippet` , ");
    	sql.append(" `noydir` , `og_markup` , `og_markup_flag` , `og_markup_length` , `outlink_count` , `page_1` , `page_link` , `page_timeout_flag` , `paginated` , ");
    	sql.append(" `pagination_links` , `parent_page` , `protocol` , `redirect_blocked` , `redirect_blocked_reason` , `redirect_chain` , `redirect_final_url` , ");
    	sql.append(" `redirect_flg` , `redirect_times` , `rel_next_html_url` , `rel_next_url_is_consistent` , `rel_prev_url_is_consistent` , `request_headers` , ");
    	sql.append(" `request_time` , `response_code` , `response_headers` , `retry_attempted` , `robots` , `robots_contents` , `robots_contents_x_tag` , `robots_flg` , ");
    	sql.append(" `robots_flg_x_tag` , `server_response_time` , `source_url` , `splash_took` , `status` , `structured_data` , `title` , `title_flg` , ");
    	sql.append(" `title_length` , `title_md5` , `title_simhash` , `twitter_description_length` , `twitter_markup` , `twitter_markup_flag` , `twitter_markup_length` , ");
    	sql.append(" `url_length` , `valid_twitter_card` , `viewport_content` , `viewport_flag` , `page_analysis_results` , `url_hash` , `change_tracking_hash_cd_json` , ");
    	sql.append(" `hreflang_links_href_array` , `post_processing_issues_array` , `hreflang_links_lang` , `hreflang_links_type` , `hreflang_links_href` , ");
    	sql.append(" `page_analysis_issue_count` , `page_link_destination_url` , `og_markup_property` , `og_markup_content` , `twitter_markup_property` , ");
    	sql.append(" `twitter_markup_content` , `pagination_links_direction` , `page_analysis_issues_array` , `content_words_exclude_count` , ");
    	sql.append(" `content_words_exclude_source` , `content_words_exclude_word` , `content_words_include_count` , `content_words_include_source` , ");
    	sql.append(" `content_words_include_word` , `content_words_search_count` , `content_words_search_source` , `content_words_search_word` , ");
    	sql.append(" `content_words_exclude.count` , `content_words_exclude.source` , `content_words_exclude.word` , `content_words_include.source` , ");
    	sql.append(" `content_words_include.word` , `content_words_search.count` , `content_words_search.source` , `content_words_search.word` , ");
    	sql.append(" `content_words_include.count` , `custom_data.word_count` , `custom_data.selector_type` , `custom_data.match_found` , `custom_data.index` , ");
    	sql.append(" `custom_data.selector` , `custom_data.links` , `custom_data.content` , `hreflang_links_href_hash_array` , `canonical_hash` , `external_link` , ");
    	sql.append(" `h2` , `title_stemmed` , `title_murmurhash` , `description_stemmed` , `description_murmurhash` , `h1_stemmed` , `h1_murmurhash` , `h2_stemmed` , ");
    	sql.append(" `h2_murmurhash` , `page_link_destination_url_hash` , `is_footer_link` , `is_header_link` , `structure_schema_warnings.schema_type` , ");
    	sql.append(" `structure_schema_warnings.encoding` , `structure_schema_warnings.message` , `structure_schema_warnings.path` , `structure_schema_warnings.warning_type` , ");
    	sql.append(" `structure_schema_errors.schema_type` , `structure_schema_errors.encoding` , `structure_schema_errors.message` , `structure_schema_errors.path` , ");
    	sql.append(" `structure_schema_errors.error_type` , `structured_schema.type` , `structured_schema.encoding` , `page_analysis_issues` , `resources.url` , ");
    	sql.append(" `resources.status_code` , `resources.is_blocked` , `resources.request_type` , `resources.is_from_memory_cache` , `resources.types` , ");
    	sql.append(" `structure_schema_errors.info` , `structure_schema_warnings.info` , `page_link_destination_url_murmur_hash` , `lang` , `page_analysis_fragments` , ");
    	sql.append(" `structured_schema_payload` ,  sources,  target_date,  process_date, report_id ) ");
    	sql.append(" select  `domain_id` , `crawl_request_id` , `crawl_request_date` , `crawl_depth` , `url` , `alt_img_detail` , `alt_img_list` , `alternate_links` , ");
    	sql.append(" `amphtml_flag` , `amphtml_href` , `analyzed_url_flg_s` , `analyzed_url_s` , `archive_flg` , `archive_flg_x_tag` , `blocked_by_robots` , `canonical` , ");
    	sql.append(" `canonical_flg` , `canonical_header_flag` , `canonical_header_type` , `canonical_type` , `canonical_url_is_consistent` , `content_extracted_count` , ");
    	sql.append(" `content_extracted_flg` , `content_flg` , `content_md5` , `content_type` , `content_word_count` , `content_words` , `conv_crawl_date` , `count_of_objects` , ");
    	sql.append(" `crawl_date_long` , `description` , `description_flg` , `description_length` , `description_simhash` , `document_size` , `domain` , `domain_name` , ");
    	sql.append(" `download_latency` , toFloat(download_time) , `error_message` , `final_response_code` , `folder_level_1` , `folder_level_2` , `folder_level_3` , ");
    	sql.append(" `folder_level_count` , `follow_flg` , `follow_flg_x_tag` , `h1` , `h1_count` , `h1_flg` , `h1_length` , `h1_md5` , `h1_simhash` , `h2_simhash` , ");
    	sql.append(" `header_noarchive` , `header_nofollow` , `header_noindex` , `header_noodp` , `header_nosnippet` , `header_noydir` , `hreflang_errors` , ");
    	sql.append(" `hreflang_links` , `hreflang_links_out_count` , `hreflang_url_count` , `hsts` , `index_flg` , `index_flg_x_tag` , `indexable` , `insecure_resources` , ");
    	sql.append(" `insecure_resources_flag` , `long_redirect` , `meta_charset` , `meta_content_type` , `meta_disabled_sitelinks` , `meta_noodp` , `meta_nosnippet` , ");
    	sql.append(" `meta_noydir` , `meta_redirect` , `mixed_redirects` , `mobile_rel_alternate_url_is_consistent` , `noodp` , `nosnippet` , `noydir` , `og_markup` , ");
    	sql.append(" `og_markup_flag` , `og_markup_length` , `outlink_count` , `page_1` , `page_link` , `page_timeout_flag` , `paginated` , `pagination_links` , ");
    	sql.append(" `parent_page` , `protocol` , `redirect_blocked` , `redirect_blocked_reason` , `redirect_chain` , `redirect_final_url` , `redirect_flg` , `redirect_times` , ");
    	sql.append(" `rel_next_html_url` , `rel_next_url_is_consistent` , `rel_prev_url_is_consistent` , `request_headers` , `request_time` , `response_code` , ");
    	sql.append(" `response_headers` , `retry_attempted` , `robots` , `robots_contents` , `robots_contents_x_tag` , `robots_flg` , `robots_flg_x_tag` , `server_response_time` , ");
    	sql.append(" `source_url` , `splash_took` , `status` , `structured_data` , `title` , `title_flg` , `title_length` , `title_md5` , `title_simhash` , ");
    	sql.append(" `twitter_description_length` , `twitter_markup` , `twitter_markup_flag` , `twitter_markup_length` , `url_length` , `valid_twitter_card` , ");
    	sql.append(" `viewport_content` , `viewport_flag` , `page_analysis_results` , `url_hash` , `change_tracking_hash_cd_json` , `hreflang_links_href_array` , ");
    	sql.append(" `post_processing_issues_array` , `hreflang_links_lang` , `hreflang_links_type` , `hreflang_links_href` ,");
    	sql.append("  page_analysis_issue_count + length(arrayFilter(x -> notEmpty(x), dupIssueArray)) as page_analysis_issue_count, ");
    	sql.append(" `page_link_destination_url` , `og_markup_property` , `og_markup_content` , `twitter_markup_property` , `twitter_markup_content` , `pagination_links_direction` ,");
    	sql.append("  arrayConcat(page_analysis_issues_array, arrayFilter(x -> notEmpty(x), dupIssueArray)) as page_analysis_issues_array, ");
    	sql.append(" `content_words_exclude_count` , `content_words_exclude_source` , `content_words_exclude_word` , ");
    	sql.append(" `content_words_include_count` , `content_words_include_source` , `content_words_include_word` , `content_words_search_count` , ");
    	sql.append(" `content_words_search_source` , `content_words_search_word` , `content_words_exclude.count` , `content_words_exclude.source` , ");
    	sql.append(" `content_words_exclude.word` , `content_words_include.source` , `content_words_include.word` , `content_words_search.count` , ");
    	sql.append(" `content_words_search.source` , `content_words_search.word` , `content_words_include.count` , `custom_data.word_count` , `custom_data.selector_type` , ");
    	sql.append(" `custom_data.match_found` , `custom_data.index` , `custom_data.selector` , `custom_data.links` , `custom_data.content` , `hreflang_links_href_hash_array` , ");
    	sql.append(" `canonical_hash` , `external_link` , `h2` , `title_stemmed` , `title_murmurhash` , `description_stemmed` , `description_murmurhash` , `h1_stemmed` , ");
    	sql.append(" `h1_murmurhash` , `h2_stemmed` , `h2_murmurhash` , `page_link_destination_url_hash` , `is_footer_link` , `is_header_link` , ");
    	sql.append(" `structure_schema_warnings.schema_type` , `structure_schema_warnings.encoding` , `structure_schema_warnings.message` , `structure_schema_warnings.path` , ");
    	sql.append(" `structure_schema_warnings.warning_type` , `structure_schema_errors.schema_type` , `structure_schema_errors.encoding` , `structure_schema_errors.message` , ");
    	sql.append(" `structure_schema_errors.path` , `structure_schema_errors.error_type` , `structured_schema.type` , `structured_schema.encoding` , `page_analysis_issues` , ");
    	sql.append(" `resources.url` , `resources.status_code` , `resources.is_blocked` , `resources.request_type` , `resources.is_from_memory_cache` , `resources.types` , ");
    	sql.append(" `structure_schema_errors.info` , `structure_schema_warnings.info` , `page_link_destination_url_murmur_hash` , `lang` , `page_analysis_fragments` , ");
    	sql.append(" `structured_schema_payload` ,  ['sitehealth'], ?, toDate(now()), " + reportId);
    	
    	sql.append(" from (select * from prod.dis_site_crawl_doc WHERE (domain_id = ?) and crawl_request_id = ? ");
    	sql.append(" ) ALL left join ( ");
    	sql.append(" SELECT url_murmur_hash, array(if(max(flg1) = 1, 'duplicateTitle', ''), if(max(flg2) = 1, 'duplicateMetaDesc', ''),  ");
    	sql.append("        if(max(flg3) = 1, 'duplicateH1', '')) as dupIssueArray  ");
    	sql.append(" FROM (  ");
    	sql.append("      SELECT url_murmur_hash, page_analysis_issues_array, page_analysis_issue_count, 1 AS flg1, 0 AS flg2, 0 AS flg3  ");
    	sql.append("      FROM (  ");
    	sql.append("         SELECT url_murmur_hash, title_murmurhash, page_analysis_issues_array, page_analysis_issue_count  ");
    	sql.append("         FROM dis_site_crawl_doc WHERE (domain_id = ?) and crawl_request_id = ? AND (analyzed_url_flg_s = 'Yes')  ");
    	sql.append("         AND ((status >= 200) AND (status <= 299)) AND notEmpty(title)  ");
    	sql.append("     ) ALL RIGHT JOIN (  ");
    	sql.append("         SELECT title_murmurhash  ");
    	sql.append("         FROM dis_site_crawl_doc WHERE (domain_id = ?) and crawl_request_id = ? AND (analyzed_url_flg_s = 'Yes')  ");
    	sql.append("         AND ((status >= 200) AND (status <= 299)) AND notEmpty(title)  ");
    	sql.append("         group by title_murmurhash HAVING countDistinct(url_murmur_hash) > 1  ");
    	sql.append("     ) USING (title_murmurhash)  ");
    	sql.append("     union all  ");
    	sql.append("     SELECT url_murmur_hash, page_analysis_issues_array, page_analysis_issue_count, 0 AS flg1, 1 AS flg2, 0 AS flg3  ");
    	sql.append("     FROM (  ");
    	sql.append("         SELECT url_murmur_hash, description_murmurhash, page_analysis_issues_array, page_analysis_issue_count  ");
    	sql.append("         FROM dis_site_crawl_doc WHERE (domain_id = ?) and crawl_request_id = ? AND (analyzed_url_flg_s = 'Yes')  ");
    	sql.append("         AND ((status >= 200) AND (status <= 299)) AND notEmpty(description)  ");
    	sql.append("     ) ALL RIGHT JOIN (  ");
    	sql.append("         SELECT description_murmurhash  ");
    	sql.append("         FROM dis_site_crawl_doc WHERE (domain_id = ?) and crawl_request_id = ? AND (analyzed_url_flg_s = 'Yes')  ");
    	sql.append("         AND ((status >= 200) AND (status <= 299)) AND notEmpty(description)          ");
    	sql.append("         group by description_murmurhash HAVING countDistinct(url_murmur_hash) > 1 ) USING (description_murmurhash)  ");
    	sql.append("     union all  ");
    	sql.append("     SELECT url_murmur_hash, page_analysis_issues_array, page_analysis_issue_count, 0 AS flg1, 0 AS flg2, 1 AS flg3  ");
    	sql.append("     FROM (  ");
    	sql.append("         SELECT url_murmur_hash, murmurHash3_64(field) as h1_murmurhash, page_analysis_issues_array, page_analysis_issue_count  ");
    	sql.append("         FROM dis_site_crawl_doc ARRAY JOIN h1 AS field WHERE (domain_id = ?) and crawl_request_id = ? AND (analyzed_url_flg_s = 'Yes')  ");
    	sql.append("         AND ((status >= 200) AND (status <= 299)) AND notEmpty(description)  ");
    	sql.append("     ) ALL RIGHT JOIN (  ");
    	sql.append("         SELECT murmurHash3_64(field) as h1_murmurhash  ");
    	sql.append("         FROM dis_site_crawl_doc ARRAY JOIN h1 AS field  ");
    	sql.append("         WHERE (domain_id = ?) and crawl_request_id = ? AND (analyzed_url_flg_s = 'Yes') AND ((status >= 200)  ");
    	sql.append("         AND (status <= 299)) AND notEmpty(field) group by h1_murmurhash  ");
    	sql.append("         HAVING countDistinct(url_murmur_hash) > 1  ");
    	sql.append(" 	) USING (h1_murmurhash)  ");
    	sql.append(" ) group by url_murmur_hash SETTINGS max_bytes_before_external_group_by = 20000000000 ");
    	
    	sql.append(" ) using(url_murmur_hash) ");

    	System.out.println("Test SQL:" + sql.toString());
    	this.executeUpdate(sql.toString(), targetDate, 
    			ownDomainId, siteHealthVO.getCrawlRequestId(),
    			ownDomainId, siteHealthVO.getCrawlRequestId(),
    			ownDomainId, siteHealthVO.getCrawlRequestId(),
    			ownDomainId, siteHealthVO.getCrawlRequestId(),
    			ownDomainId, siteHealthVO.getCrawlRequestId(),
    			ownDomainId, siteHealthVO.getCrawlRequestId(),
    			ownDomainId, siteHealthVO.getCrawlRequestId());
    }

	public void processRgSummary(Integer ownDomainId, RgVO rgVO, String targetDate, Integer reportId,
			String tempTableName) {
		
		Boolean isMobile = StringUtils.equalsIgnoreCase(rgVO.getDevice(), "m") ? true : false;
		String detailTableName = getDetailTable(isMobile, rgVO.getEngineId(), rgVO.getIndex());
		
		String svTable = getSvbUrlTable(rgVO);
		
		StringBuffer sql = new StringBuffer();
		
		sql.append(" insert into " + tempTableName + "(domain_id, url, url_murmur_hash, rg_top1cnt, rg_top2cnt, rg_top3cnt, rg_top10cnt, rg_top20cnt,  ");
		sql.append(" rg_top30cnt, rg_top100cnt, rg_sum_top1_avg_sv, rg_sum_top2_avg_sv, rg_sum_top3_avg_sv, rg_sum_top10_avg_sv,  ");
		sql.append(" rg_sum_top20_avg_sv, rg_sum_top30_avg_sv, rg_sum_top100_avg_sv, rg_sum_top1_est_traffic, rg_sum_top2_est_traffic,  ");
		sql.append(" rg_sum_top3_est_traffic, rg_sum_top10_est_traffic, rg_traffic_potential, rg_typicalpage_strength, rg_intent_array, rg_intent_kwcnt_array, rg_avg_rank, ");
		sql.append(" rg_wtd_avg_rank, rg_sov, sources,  target_date,  process_date, report_id )  ");

    	sql.append(" SELECT  ");
    	sql.append(" ?, x_url, mmhash, top1Cnt, top2Cnt, top3Cnt, top10Cnt, top20Cnt, top30Cnt, cnt,  ");
    	sql.append(" sum_top1_avg_search_volume, sum_top2_avg_search_volume, sum_top3_avg_search_volume,  ");
    	sql.append(" sum_top10_avg_search_volume, sum_top20_avg_search_volume, sum_top30_avg_search_volume,  ");
    	sql.append(" sum_avg_search_volume, sum_top1_est_traffic, sum_top2_est_traffic, sum_top3_est_traffic,  ");
    	sql.append(" sum_est_traffic, tp, typicalPageStrength, intentArray,  ");
    	sql.append(" intentKwCntArray, avg_rank, wtd_avg_rank, round(sum_est_traffic / tp * 100, 2) as sov, ");
    	sql.append("    ['rg'], ?, toDate(now()), " + reportId);
    	sql.append("FROM ( ");
    	sql.append("    SELECT  ");
    	sql.append("        urlhash, x_url, mmhash, top1Cnt, top2Cnt, top3Cnt, top10Cnt, top20Cnt, top30Cnt, cnt,  ");
    	sql.append("        sum_top1_avg_search_volume, sum_top2_avg_search_volume, sum_top3_avg_search_volume,  ");
    	sql.append("        sum_top10_avg_search_volume, sum_top20_avg_search_volume, sum_top30_avg_search_volume,  ");
    	sql.append("        sum_avg_search_volume, sum_top1_est_traffic, sum_top2_est_traffic, sum_top3_est_traffic,  ");
    	sql.append("        sum_est_traffic, sum_est_traffic_value, round((sum_avg_search_volume * 0.193)) AS tp,  ");
    	sql.append("        if(svbStatus = 1, toString(ROUND(svb)), '0') AS typicalPageStrength, avg_rank, wtd_avg_rank ");
    	sql.append("    FROM  ");
    	sql.append("    ( ");
    	sql.append("        SELECT  ");
    	sql.append("            any(urlhash) as urlhash, murmurHash3_64(url) AS mmhash, any(url) AS x_url,  ");
    	sql.append("          sumIf(multiIf( ");
    	sql.append("                (engine_id = 30) AND (language_id = 6), dictGetUInt64('file_dic_rg_kwd_sv_cn', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 6) AND (language_id = 8), dictGetUInt64('file_dic_rg_kwd_sv_uk', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 18) AND (language_id = 19), dictGetUInt64('file_dic_rg_kwd_sv_jp', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 14) AND (language_id = 15), dictGetUInt64('file_dic_rg_kwd_sv_de', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 1) AND (language_id = 1), dictGetUInt64('file_dic_rg_kwd_sv_us', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 16) AND (language_id = 17), dictGetUInt64('file_dic_rg_kwd_sv_es', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 24) AND (language_id = 25), dictGetUInt64('file_dic_rg_kwd_sv_in', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 25) AND (language_id = 26), dictGetUInt64('file_dic_rg_kwd_sv_ar', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 3) AND (language_id = 3), dictGetUInt64('file_dic_rg_kwd_sv_ca', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 150) AND (language_id = 6), dictGetUInt64('file_dic_rg_kwd_sv_cn', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                dictGetUInt64('file_dic_rg_kwd_sv', 'avg_search_volume', tuple(concat(toString(keyword_rankcheck_id), '_', toString(language_id)))) ");
    	sql.append("            ), hrd = 1 AND true_rank = 1) AS sum_top1_avg_search_volume,  ");
    	sql.append("             ");
    	sql.append("            sumIf(multiIf( ");
    	sql.append("                (engine_id = 30) AND (language_id = 6), dictGetUInt64('file_dic_rg_kwd_sv_cn', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 6) AND (language_id = 8), dictGetUInt64('file_dic_rg_kwd_sv_uk', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 18) AND (language_id = 19), dictGetUInt64('file_dic_rg_kwd_sv_jp', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 14) AND (language_id = 15), dictGetUInt64('file_dic_rg_kwd_sv_de', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 1) AND (language_id = 1), dictGetUInt64('file_dic_rg_kwd_sv_us', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 16) AND (language_id = 17), dictGetUInt64('file_dic_rg_kwd_sv_es', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 24) AND (language_id = 25), dictGetUInt64('file_dic_rg_kwd_sv_in', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 25) AND (language_id = 26), dictGetUInt64('file_dic_rg_kwd_sv_ar', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 3) AND (language_id = 3), dictGetUInt64('file_dic_rg_kwd_sv_ca', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 150) AND (language_id = 6), dictGetUInt64('file_dic_rg_kwd_sv_cn', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                dictGetUInt64('file_dic_rg_kwd_sv', 'avg_search_volume', tuple(concat(toString(keyword_rankcheck_id), '_', toString(language_id)))) ");
    	sql.append("            ), hrd = 1 AND true_rank <= 2) AS sum_top2_avg_search_volume,  ");
    	sql.append("             ");
    	sql.append("            sumIf(multiIf( ");
    	sql.append("                (engine_id = 30) AND (language_id = 6), dictGetUInt64('file_dic_rg_kwd_sv_cn', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 6) AND (language_id = 8), dictGetUInt64('file_dic_rg_kwd_sv_uk', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 18) AND (language_id = 19), dictGetUInt64('file_dic_rg_kwd_sv_jp', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 14) AND (language_id = 15), dictGetUInt64('file_dic_rg_kwd_sv_de', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 1) AND (language_id = 1), dictGetUInt64('file_dic_rg_kwd_sv_us', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 16) AND (language_id = 17), dictGetUInt64('file_dic_rg_kwd_sv_es', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 24) AND (language_id = 25), dictGetUInt64('file_dic_rg_kwd_sv_in', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 25) AND (language_id = 26), dictGetUInt64('file_dic_rg_kwd_sv_ar', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 3) AND (language_id = 3), dictGetUInt64('file_dic_rg_kwd_sv_ca', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 150) AND (language_id = 6), dictGetUInt64('file_dic_rg_kwd_sv_cn', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                dictGetUInt64('file_dic_rg_kwd_sv', 'avg_search_volume', tuple(concat(toString(keyword_rankcheck_id), '_', toString(language_id)))) ");
    	sql.append("            ), hrd = 1 AND true_rank <= 3) AS sum_top3_avg_search_volume,  ");
    	sql.append("             ");
    	sql.append("            sumIf(multiIf( ");
    	sql.append("                (engine_id = 30) AND (language_id = 6), dictGetUInt64('file_dic_rg_kwd_sv_cn', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 6) AND (language_id = 8), dictGetUInt64('file_dic_rg_kwd_sv_uk', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 18) AND (language_id = 19), dictGetUInt64('file_dic_rg_kwd_sv_jp', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 14) AND (language_id = 15), dictGetUInt64('file_dic_rg_kwd_sv_de', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 1) AND (language_id = 1), dictGetUInt64('file_dic_rg_kwd_sv_us', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 16) AND (language_id = 17), dictGetUInt64('file_dic_rg_kwd_sv_es', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 24) AND (language_id = 25), dictGetUInt64('file_dic_rg_kwd_sv_in', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 25) AND (language_id = 26), dictGetUInt64('file_dic_rg_kwd_sv_ar', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 3) AND (language_id = 3), dictGetUInt64('file_dic_rg_kwd_sv_ca', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 150) AND (language_id = 6), dictGetUInt64('file_dic_rg_kwd_sv_cn', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                dictGetUInt64('file_dic_rg_kwd_sv', 'avg_search_volume', tuple(concat(toString(keyword_rankcheck_id), '_', toString(language_id)))) ");
    	sql.append("            ), hrd = 1 AND true_rank <= 10) AS sum_top10_avg_search_volume,  ");
    	sql.append("             ");
    	sql.append("            sumIf(multiIf( ");
    	sql.append("                (engine_id = 30) AND (language_id = 6), dictGetUInt64('file_dic_rg_kwd_sv_cn', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 6) AND (language_id = 8), dictGetUInt64('file_dic_rg_kwd_sv_uk', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 18) AND (language_id = 19), dictGetUInt64('file_dic_rg_kwd_sv_jp', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 14) AND (language_id = 15), dictGetUInt64('file_dic_rg_kwd_sv_de', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 1) AND (language_id = 1), dictGetUInt64('file_dic_rg_kwd_sv_us', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 16) AND (language_id = 17), dictGetUInt64('file_dic_rg_kwd_sv_es', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 24) AND (language_id = 25), dictGetUInt64('file_dic_rg_kwd_sv_in', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 25) AND (language_id = 26), dictGetUInt64('file_dic_rg_kwd_sv_ar', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 3) AND (language_id = 3), dictGetUInt64('file_dic_rg_kwd_sv_ca', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 150) AND (language_id = 6), dictGetUInt64('file_dic_rg_kwd_sv_cn', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                dictGetUInt64('file_dic_rg_kwd_sv', 'avg_search_volume', tuple(concat(toString(keyword_rankcheck_id), '_', toString(language_id)))) ");
    	sql.append("            ), hrd = 1 AND true_rank <= 20) AS sum_top20_avg_search_volume,  ");
    	sql.append("             ");
    	sql.append("            sumIf(multiIf( ");
    	sql.append("                (engine_id = 30) AND (language_id = 6), dictGetUInt64('file_dic_rg_kwd_sv_cn', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 6) AND (language_id = 8), dictGetUInt64('file_dic_rg_kwd_sv_uk', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 18) AND (language_id = 19), dictGetUInt64('file_dic_rg_kwd_sv_jp', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 14) AND (language_id = 15), dictGetUInt64('file_dic_rg_kwd_sv_de', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 1) AND (language_id = 1), dictGetUInt64('file_dic_rg_kwd_sv_us', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 16) AND (language_id = 17), dictGetUInt64('file_dic_rg_kwd_sv_es', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 24) AND (language_id = 25), dictGetUInt64('file_dic_rg_kwd_sv_in', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 25) AND (language_id = 26), dictGetUInt64('file_dic_rg_kwd_sv_ar', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 3) AND (language_id = 3), dictGetUInt64('file_dic_rg_kwd_sv_ca', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 150) AND (language_id = 6), dictGetUInt64('file_dic_rg_kwd_sv_cn', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                dictGetUInt64('file_dic_rg_kwd_sv', 'avg_search_volume', tuple(concat(toString(keyword_rankcheck_id), '_', toString(language_id)))) ");
    	sql.append("            ), hrd = 1 AND true_rank <= 30) AS sum_top30_avg_search_volume,  ");
    	sql.append("             ");
    	sql.append("            sumIf(multiIf( ");
    	sql.append("                (engine_id = 30) AND (language_id = 6), dictGetUInt64('file_dic_rg_kwd_sv_cn', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 6) AND (language_id = 8), dictGetUInt64('file_dic_rg_kwd_sv_uk', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 18) AND (language_id = 19), dictGetUInt64('file_dic_rg_kwd_sv_jp', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 14) AND (language_id = 15), dictGetUInt64('file_dic_rg_kwd_sv_de', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 1) AND (language_id = 1), dictGetUInt64('file_dic_rg_kwd_sv_us', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 16) AND (language_id = 17), dictGetUInt64('file_dic_rg_kwd_sv_es', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 24) AND (language_id = 25), dictGetUInt64('file_dic_rg_kwd_sv_in', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 25) AND (language_id = 26), dictGetUInt64('file_dic_rg_kwd_sv_ar', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 3) AND (language_id = 3), dictGetUInt64('file_dic_rg_kwd_sv_ca', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 150) AND (language_id = 6), dictGetUInt64('file_dic_rg_kwd_sv_cn', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                dictGetUInt64('file_dic_rg_kwd_sv', 'avg_search_volume', tuple(concat(toString(keyword_rankcheck_id), '_', toString(language_id)))) ");
    	sql.append("            ), hrd = 1) AS sum_avg_search_volume,  ");
    	sql.append("             ");
    	sql.append("            sumIf( ");
    	sql.append("                round(( ");
    	sql.append("                    caseWithExpression(true_rank, 1, 0.193, 2, 0.114, 3, 0.077, 4, 0.052, 5, 0.041, 6, 0.033, 7, 0.026, 8, 0.021, 9, 0.02, 10, 0.022, 0) *  ");
    	sql.append("                    multiIf( ");
    	sql.append("                        (engine_id = 30) AND (language_id = 6), dictGetUInt64('file_dic_rg_kwd_sv_cn', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                        (engine_id = 6) AND (language_id = 8), dictGetUInt64('file_dic_rg_kwd_sv_uk', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                        (engine_id = 18) AND (language_id = 19), dictGetUInt64('file_dic_rg_kwd_sv_jp', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                        (engine_id = 14) AND (language_id = 15), dictGetUInt64('file_dic_rg_kwd_sv_de', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                        (engine_id = 1) AND (language_id = 1), dictGetUInt64('file_dic_rg_kwd_sv_us', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                        (engine_id = 16) AND (language_id = 17), dictGetUInt64('file_dic_rg_kwd_sv_es', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                        (engine_id = 24) AND (language_id = 25), dictGetUInt64('file_dic_rg_kwd_sv_in', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                        (engine_id = 25) AND (language_id = 26), dictGetUInt64('file_dic_rg_kwd_sv_ar', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                        (engine_id = 3) AND (language_id = 3), dictGetUInt64('file_dic_rg_kwd_sv_ca', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                        (engine_id = 150) AND (language_id = 6), dictGetUInt64('file_dic_rg_kwd_sv_cn', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                        dictGetUInt64('file_dic_rg_kwd_sv', 'avg_search_volume', tuple(concat(toString(keyword_rankcheck_id), '_', toString(language_id)))) ");
    	sql.append("                    ) * if(hrd = 1, 1., 0.2)) + 0.01) ");
    	sql.append("            , true_rank = 1) AS sum_top1_est_traffic,  ");
    	sql.append("             ");
    	sql.append("            sumIf( ");
    	sql.append("                round(( ");
    	sql.append("                    caseWithExpression(true_rank, 1, 0.193, 2, 0.114, 3, 0.077, 4, 0.052, 5, 0.041, 6, 0.033, 7, 0.026, 8, 0.021, 9, 0.02, 10, 0.022, 0) *  ");
    	sql.append("                    multiIf( ");
    	sql.append("                        (engine_id = 30) AND (language_id = 6), dictGetUInt64('file_dic_rg_kwd_sv_cn', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                        (engine_id = 6) AND (language_id = 8), dictGetUInt64('file_dic_rg_kwd_sv_uk', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                        (engine_id = 18) AND (language_id = 19), dictGetUInt64('file_dic_rg_kwd_sv_jp', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                        (engine_id = 14) AND (language_id = 15), dictGetUInt64('file_dic_rg_kwd_sv_de', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                        (engine_id = 1) AND (language_id = 1), dictGetUInt64('file_dic_rg_kwd_sv_us', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                        (engine_id = 16) AND (language_id = 17), dictGetUInt64('file_dic_rg_kwd_sv_es', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                        (engine_id = 24) AND (language_id = 25), dictGetUInt64('file_dic_rg_kwd_sv_in', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                        (engine_id = 25) AND (language_id = 26), dictGetUInt64('file_dic_rg_kwd_sv_ar', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                        (engine_id = 3) AND (language_id = 3), dictGetUInt64('file_dic_rg_kwd_sv_ca', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                        (engine_id = 150) AND (language_id = 6), dictGetUInt64('file_dic_rg_kwd_sv_cn', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                        dictGetUInt64('file_dic_rg_kwd_sv', 'avg_search_volume', tuple(concat(toString(keyword_rankcheck_id), '_', toString(language_id)))) ");
    	sql.append("                    ) * if(hrd = 1, 1., 0.2)) + 0.01) ");
    	sql.append("            , true_rank <= 2) AS sum_top2_est_traffic,  ");
    	sql.append("            sumIf( ");
    	sql.append("                round(( ");
    	sql.append("                    caseWithExpression(true_rank, 1, 0.193, 2, 0.114, 3, 0.077, 4, 0.052, 5, 0.041, 6, 0.033, 7, 0.026, 8, 0.021, 9, 0.02, 10, 0.022, 0) *  ");
    	sql.append("                    multiIf( ");
    	sql.append("                        (engine_id = 30) AND (language_id = 6), dictGetUInt64('file_dic_rg_kwd_sv_cn', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                        (engine_id = 6) AND (language_id = 8), dictGetUInt64('file_dic_rg_kwd_sv_uk', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                        (engine_id = 18) AND (language_id = 19), dictGetUInt64('file_dic_rg_kwd_sv_jp', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                        (engine_id = 14) AND (language_id = 15), dictGetUInt64('file_dic_rg_kwd_sv_de', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                        (engine_id = 1) AND (language_id = 1), dictGetUInt64('file_dic_rg_kwd_sv_us', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                        (engine_id = 16) AND (language_id = 17), dictGetUInt64('file_dic_rg_kwd_sv_es', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                        (engine_id = 24) AND (language_id = 25), dictGetUInt64('file_dic_rg_kwd_sv_in', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                        (engine_id = 25) AND (language_id = 26), dictGetUInt64('file_dic_rg_kwd_sv_ar', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                        (engine_id = 3) AND (language_id = 3), dictGetUInt64('file_dic_rg_kwd_sv_ca', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                        (engine_id = 150) AND (language_id = 6), dictGetUInt64('file_dic_rg_kwd_sv_cn', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                        dictGetUInt64('file_dic_rg_kwd_sv', 'avg_search_volume', tuple(concat(toString(keyword_rankcheck_id), '_', toString(language_id)))) ");
    	sql.append("                    ) * if(hrd = 1, 1., 0.2)) + 0.01) ");
    	sql.append("            , true_rank <= 3) AS sum_top3_est_traffic,  ");
    	sql.append("            sum( ");
    	sql.append("                round(( ");
    	sql.append("                    caseWithExpression(true_rank, 1, 0.193, 2, 0.114, 3, 0.077, 4, 0.052, 5, 0.041, 6, 0.033, 7, 0.026, 8, 0.021, 9, 0.02, 10, 0.022, 0) *  ");
    	sql.append("                    multiIf( ");
    	sql.append("                        (engine_id = 30) AND (language_id = 6), dictGetUInt64('file_dic_rg_kwd_sv_cn', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                        (engine_id = 6) AND (language_id = 8), dictGetUInt64('file_dic_rg_kwd_sv_uk', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                        (engine_id = 18) AND (language_id = 19), dictGetUInt64('file_dic_rg_kwd_sv_jp', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                        (engine_id = 14) AND (language_id = 15), dictGetUInt64('file_dic_rg_kwd_sv_de', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                        (engine_id = 1) AND (language_id = 1), dictGetUInt64('file_dic_rg_kwd_sv_us', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                        (engine_id = 16) AND (language_id = 17), dictGetUInt64('file_dic_rg_kwd_sv_es', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                        (engine_id = 24) AND (language_id = 25), dictGetUInt64('file_dic_rg_kwd_sv_in', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                        (engine_id = 25) AND (language_id = 26), dictGetUInt64('file_dic_rg_kwd_sv_ar', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                        (engine_id = 3) AND (language_id = 3), dictGetUInt64('file_dic_rg_kwd_sv_ca', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                        (engine_id = 150) AND (language_id = 6), dictGetUInt64('file_dic_rg_kwd_sv_cn', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                        dictGetUInt64('file_dic_rg_kwd_sv', 'avg_search_volume', tuple(concat(toString(keyword_rankcheck_id), '_', toString(language_id)))) ");
    	sql.append("                    ) * if(hrd = 1, 1., 0.2)) + 0.01) ");
    	sql.append("            ) AS sum_est_traffic,  ");
    	sql.append("            sum( ");
    	sql.append("                round(( ");
    	sql.append("                    caseWithExpression(true_rank, 1, 0.193, 2, 0.114, 3, 0.077, 4, 0.052, 5, 0.041, 6, 0.033, 7, 0.026, 8, 0.021, 9, 0.02, 10, 0.022, 0) *  ");
    	sql.append("                    multiIf( ");
    	sql.append("                        (engine_id = 30) AND (language_id = 6), dictGetUInt64('file_dic_rg_kwd_sv_cn', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                        (engine_id = 6) AND (language_id = 8), dictGetUInt64('file_dic_rg_kwd_sv_uk', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                        (engine_id = 18) AND (language_id = 19), dictGetUInt64('file_dic_rg_kwd_sv_jp', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                        (engine_id = 14) AND (language_id = 15), dictGetUInt64('file_dic_rg_kwd_sv_de', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                        (engine_id = 1) AND (language_id = 1), dictGetUInt64('file_dic_rg_kwd_sv_us', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                        (engine_id = 16) AND (language_id = 17), dictGetUInt64('file_dic_rg_kwd_sv_es', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                        (engine_id = 24) AND (language_id = 25), dictGetUInt64('file_dic_rg_kwd_sv_in', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                        (engine_id = 25) AND (language_id = 26), dictGetUInt64('file_dic_rg_kwd_sv_ar', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                        (engine_id = 3) AND (language_id = 3), dictGetUInt64('file_dic_rg_kwd_sv_ca', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                        (engine_id = 150) AND (language_id = 6), dictGetUInt64('file_dic_rg_kwd_sv_cn', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                        dictGetUInt64('file_dic_rg_kwd_sv', 'avg_search_volume', tuple(concat(toString(keyword_rankcheck_id), '_', toString(language_id)))) ");
    	sql.append("                    ) * round(cpc, 2) * if(hrd = 1, 1., 0.2)) + 0.01) ");
    	sql.append("            ) AS sum_est_traffic_value,  ");
    	sql.append("            countIf(true_rank = 1) as top1Cnt, countIf(true_rank <= 2) as top2Cnt, countIf(true_rank <= 3) as top3Cnt, ");
    	sql.append("            countIf(true_rank <= 10) as top10Cnt, countIf(true_rank <= 20) as top20Cnt, countIf(true_rank <= 30) as top30Cnt, ");
    	sql.append("            count() AS cnt, round(if(cnt = 0, 101, sum(true_rank) /  cnt), 2) as avg_rank, ");
    	sql.append("            sum(multiIf( ");
    	sql.append("                (engine_id = 30) AND (language_id = 6), dictGetUInt64('file_dic_rg_kwd_sv_cn', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 6) AND (language_id = 8), dictGetUInt64('file_dic_rg_kwd_sv_uk', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 18) AND (language_id = 19), dictGetUInt64('file_dic_rg_kwd_sv_jp', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 14) AND (language_id = 15), dictGetUInt64('file_dic_rg_kwd_sv_de', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 1) AND (language_id = 1), dictGetUInt64('file_dic_rg_kwd_sv_us', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 16) AND (language_id = 17), dictGetUInt64('file_dic_rg_kwd_sv_es', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 24) AND (language_id = 25), dictGetUInt64('file_dic_rg_kwd_sv_in', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 25) AND (language_id = 26), dictGetUInt64('file_dic_rg_kwd_sv_ar', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 3) AND (language_id = 3), dictGetUInt64('file_dic_rg_kwd_sv_ca', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                (engine_id = 150) AND (language_id = 6), dictGetUInt64('file_dic_rg_kwd_sv_cn', 'avg_search_volume', toUInt64(keyword_rankcheck_id)),  ");
    	sql.append("                dictGetUInt64('file_dic_rg_kwd_sv', 'avg_search_volume', tuple(concat(toString(keyword_rankcheck_id), '_', toString(language_id)))) ");
    	sql.append("            ) * true_rank) AS wtd_vol_rank,  ");
    	sql.append("            ROUND(if(sum_avg_search_volume = 0, 101, wtd_vol_rank / sum_avg_search_volume) + 0.001, 2) as wtd_avg_rank ");
    	sql.append("        FROM " + detailTableName);
    	sql.append("        WHERE (root_domain_reverse = '" + ClarityDBUtils.getReverseRootDomain(rgVO.getDomainName(), true) + "')   ");
    	sql.append("        AND (engine_id = " + rgVO.getEngineId() + ") AND (language_id = " + rgVO.getLanguageId() + ")  ");
    	sql.append("        AND (dictGetUInt64('file_dic_dirty_kwd', 'rankcheck_id', (toUInt64(1), toUInt64(1), toUInt64(keyword_rankcheck_id))) = 0)  ");
    	sql.append("        GROUP BY mmhash ");
    	sql.append("    ) ");
    	sql.append("    ANY LEFT JOIN  ");
    	sql.append("    ( ");
    	sql.append("        SELECT  ");
    	sql.append("            urlHash AS urlhash, round(if(potentialTraffic = 0, 0, (estdTraffic / potentialTraffic) * 100), 2) AS svb, 1 AS svbStatus ");
    	sql.append("        FROM  " + svTable);
    	sql.append("        WHERE  (root_domain_reverse = '" + ClarityDBUtils.getReverseRootDomain(rgVO.getDomainName(), true) + "')   ");
    	sql.append("        AND (engine_id = " + rgVO.getEngineId() + ") AND (language_id = " + rgVO.getLanguageId() + ") ");
    	sql.append("    ) USING (urlhash) ");
    	sql.append(") ANY LEFT JOIN ( ");
    	sql.append("    select mmhash, groupArray(intent_type) as intentArray, groupArray(cnt) as intentKwCntArray ");
    	sql.append("    from ( ");
    	sql.append("        SELECT  ");
    	sql.append("            mmhash, intent_type, count() AS cnt ");
    	sql.append("        FROM  ");
    	sql.append("        ( ");
    	sql.append("            SELECT  ");
    	sql.append("                murmurHash3_64(url) AS mmhash, url, keyword_rankcheck_id, ['1', '2', '3', '4'] AS idxArr,  ");
    	sql.append("                ['local', 'information', 'navigational', 'transactional'] AS arr,  ");
    	sql.append("                splitByString(',', multiIf( ");
    	sql.append("                    (engine_id = 30)  AND (language_id = 6) ,    dictGetString('file_dic_intent_type_cn', 'intent', toUInt64(keyword_rankcheck_id)), ");
    	sql.append("                    (engine_id = 6)   AND (language_id = 8) ,    dictGetString('file_dic_intent_type_uk', 'intent', toUInt64(keyword_rankcheck_id)), ");
    	sql.append("                    (engine_id = 18)  AND (language_id = 19),    dictGetString('file_dic_intent_type_jp', 'intent', toUInt64(keyword_rankcheck_id)), ");
    	sql.append("                    (engine_id = 14)  AND (language_id = 15),    dictGetString('file_dic_intent_type_de', 'intent', toUInt64(keyword_rankcheck_id)), ");
    	sql.append("                    (engine_id = 1)   AND (language_id = 1) ,    dictGetString('file_dic_intent_type_us', 'intent', toUInt64(keyword_rankcheck_id)), ");
    	sql.append("                    (engine_id = 16)  AND (language_id = 17),    dictGetString('file_dic_intent_type_es', 'intent', toUInt64(keyword_rankcheck_id)), ");
    	sql.append("                    (engine_id = 24)  AND (language_id = 25),    dictGetString('file_dic_intent_type_in', 'intent', toUInt64(keyword_rankcheck_id)), ");
    	sql.append("                    (engine_id = 25)  AND (language_id = 26),    dictGetString('file_dic_intent_type_ar', 'intent', toUInt64(keyword_rankcheck_id)), ");
    	sql.append("                    (engine_id = 3)   AND (language_id = 3) ,    dictGetString('file_dic_intent_type_ca', 'intent', toUInt64(keyword_rankcheck_id)), ");
    	sql.append("                    (engine_id = 150) AND (language_id = 6) ,    dictGetString('file_dic_intent_type_cn', 'intent', toUInt64(keyword_rankcheck_id)), ");
    	sql.append("                    dictGetString('file_dic_intent_type', 'intent', tuple(concat(toString(engine_id), '_', toString(language_id), '_', toString(keyword_rankcheck_id)))) ");
    	sql.append("                )) AS intent,  ");
    	sql.append("                arrayFilter((x, y) -> has(intent, y), arr, idxArr) AS typeArr ");
    	sql.append("            FROM " + detailTableName);
    	sql.append("            WHERE (root_domain_reverse = '" + ClarityDBUtils.getReverseRootDomain(rgVO.getDomainName(), true) + "')   ");
    	sql.append("            AND (engine_id = " + rgVO.getEngineId() + ") AND (language_id = " + rgVO.getLanguageId() + ")  ");
    	sql.append("            AND (dictGetUInt64('file_dic_dirty_kwd', 'rankcheck_id', (toUInt64(1), toUInt64(1), toUInt64(keyword_rankcheck_id))) = 0)  ");
    	sql.append("        ) ");
    	sql.append("        ARRAY JOIN typeArr AS intent_type ");
    	sql.append("        GROUP BY mmhash, intent_type ");
    	sql.append("    ) GROUP BY mmhash ");
    	sql.append(") USING (mmhash) ");
		this.executeUpdate(sql.toString(), ownDomainId, targetDate);
	}
	
	
	public Integer getTotalCountByReportId(Integer reportId, Integer ownDomainId) {
		StringBuffer sql = new StringBuffer();
		
		sql.append(" select count() from dis_clarity360_v3 where report_id = " + reportId + " and domain_id = " + ownDomainId);
		
		System.out.println("Total SQL :" + sql.toString());
		return this.queryForInt(sql.toString());
	}
	
	public Boolean isMergeFinished(Integer reportId, Integer ownDomainId) {
		StringBuffer sql = new StringBuffer();
		
		sql.append(" select if(count() == count(distinct url_murmur_hash), 1, 0) from dis_clarity360_v3 where report_id = " + reportId + " and domain_id = " + ownDomainId);
		
		System.out.println("Flag SQL :" + sql.toString());
		Integer flag = this.queryForInt(sql.toString());
		
		if (flag >= 1) {
			return true;
		}
		return false;
	}
	
	
	public static String getDetailTable(boolean isMobile, int engineId, int index) {
		String qTable = "d_ranking_detail_"+index+"_us";
		if(isMobile){
			if(engineId==1){
				qTable = "m_ranking_detail_"+index+"_us";
			}else{
				qTable = "m_ranking_detail_"+index+"_intl";
			}
		}else{
			if(engineId==1){
				;
			}else{
				qTable = "d_ranking_detail_"+index+"_intl";
			}
		}
		qTable = ClarityDBConstants.MONTHLY_RANKING_DATABASE + qTable;
		return qTable;
	}
	
	public static final String SVB_URL_TABLE = "%device%_ranking_unique_url_top10_searchVisibillity_%month%_%country%";
	public static final String getSvbUrlTable(RgVO rgVO) {
		int minMonth = 202105;
		String device = rgVO.getDevice();
		if (StringUtils.isBlank(device)) {
			device = "d";
		}
		int monthIndex = rgVO.getIndex();
		if (monthIndex < minMonth) {
			monthIndex = minMonth;
		}
		String country = rgVO.getEngineId() == 1 && rgVO.getLanguageId() == 1 ? "us" : "intl";
		String table = StringUtils.replace(SVB_URL_TABLE, "%device%", device);
		table = StringUtils.replace(table, "%month%", String.valueOf(monthIndex));
		table = StringUtils.replace(table, "%country%", country);
		return table;
	}


    public Integer getTotalCount(Integer ownDomainId, BacklinkVO backlinkVO, OwnDomainEntity ownDomainEntity, String targetDate, 
			Integer reportId, Integer urlTagId, ClarityDBTypesFilterVO contentTypeVO) throws Exception {
		String rootDomain = ClarityDBUtils.getRootDomain(ownDomainEntity.getDomain());
		String reverseRootDomain = StringUtils.reverseDelimited(rootDomain, '.');
		String reverseDomain = "";
        if (!StringUtils.equals(rootDomain, ownDomainEntity.getDomain())) {// not equal means is not root domain
			reverseDomain = StringUtils.reverseDelimited(rootDomain, '.');
		}
        
    	StringBuffer sql = new StringBuffer();
    	sql.append(" select count() from (");
    	sql.append(" SELECT  murmurHash3_64(target_url) AS hash  ");
    	sql.append(" FROM backlink.ref_dis_backlink_data  ");
    	
    	sql.append(" WHERE (target_url_root_domain = '" + reverseRootDomain + "')");
    	if (StringUtils.isNotBlank(reverseDomain) && !StringUtils.equals(reverseRootDomain, reverseDomain)) {
    		sql.append(" AND (target_url_domain = '" + reverseDomain + "') ");
		}
    	
    	System.out.println("ownDomainEntity.getSubFolder():" + ownDomainEntity.getSubFolder());
    	System.out.println("ownDomainEntity.getSubFolderFragment():" + ownDomainEntity.getSubFolderFragment());
    	System.out.println("SQL : " + FormatUtils.getFolderSqlForDomain(FormatUtils.mergeDomainSubfolder(ownDomainEntity.getDomain(), ownDomainEntity.getSubFolder(), ownDomainEntity.getSubFolderFragment())));
    	if (StringUtils.isNotBlank(ownDomainEntity.getSubFolder()) || StringUtils.isNotBlank(ownDomainEntity.getSubFolderFragment())) {
    		sql.append(FormatUtils.getFolderSqlForDomain(FormatUtils.mergeDomainSubfolder(ownDomainEntity.getDomain(), ownDomainEntity.getSubFolder(), ownDomainEntity.getSubFolderFragment())));
		}
    	
    	if (backlinkVO.getLeftNaviFilters() != null && backlinkVO.getLeftNaviFilters().length > 0) {
            for (PaginationColFilter paginationColFilter : backlinkVO.getLeftNaviFilters()) {
                if ("url".equalsIgnoreCase(paginationColFilter.getColname())) {
                	sql.append(ClarityDBUtils.generateColumnFilterSql(paginationColFilter, "target_url", Types.VARCHAR, ClarityDBUtils.OPERATOR_AND, true, true));
                }
            }
        }
    	
    	if (urlTagId != null && urlTagId > 0) {
    		sql.append(" AND (dictGetUInt64('file_dic_rg_managed_url', 'target_url_id', (toUInt64(" + ownDomainId + "), toUInt64(" + urlTagId + "), URLHash(lower(target_url)))) > 0) ");
		}
    	
    	if (contentTypeVO != null) {
    		
    		sql.append(ClarityDBUtils.getCommonTypesFilter(contentTypeVO, "target_url"));
		}
    	
    	String today = FormatUtils.formatDate(new Date(), "yyyy-MM-dd");
    	String lastYear = FormatUtils.formatDate(DateUtils.addYears(new Date(), -1) , "yyyy-MM-dd");
    	
    	//requirement from Alps, skype 09/06/2023
//    	sql.append(" AND (toDate(first_indexed_date) >= '" + backlinkVO.getStartDate() + "') AND (toDate(first_indexed_date) <= '" + backlinkVO.getEndDate() + "')  ");
    	sql.append(" AND (toDate(first_indexed_date) >= '" + lastYear + "') AND (toDate(first_indexed_date) <= '" + today + "')  ");
    	sql.append(" GROUP BY hash ");
    	sql.append(" ) ");
        sql.append(" SETTINGS max_bytes_before_external_group_by=20000000000, max_bytes_before_external_sort=10000000000, distributed_aggregation_memory_efficient=1 ");

        System.out.println("Sitemap Totalcount : " + sql.toString());
		return this.queryForInteger(sql.toString());
	}
	
	public void backLink360Summary(Integer ownDomainId, BacklinkVO backlinkVO, OwnDomainEntity ownDomainEntity, String targetDate, 
			Integer reportId, Integer urlTagId, ClarityDBTypesFilterVO contentTypeVO, String urlHashColumn, Boolean isForUrlHash, Integer partNum, Integer num) {
		
		String rootDomain = ClarityDBUtils.getRootDomain(ownDomainEntity.getDomain());
		String reverseRootDomain = StringUtils.reverseDelimited(rootDomain, '.');
		String reverseDomain = "";
        if (!StringUtils.equals(rootDomain, ownDomainEntity.getDomain())) {// not equal means is not root domain
			reverseDomain = StringUtils.reverseDelimited(rootDomain, '.');
		}
		
		StringBuffer sql = new StringBuffer();
    	sql.append(" insert into " + getDisTableNameByType(isForUrlHash) + " (domain_id, url, url_murmur_hash, backlink_trustflow, backlink_citationflow, ");
    	sql.append("  backlink_cnt_backlinks, backlink_cnt_backlinksdomains, backlink_cnt_anchortext, sources,  target_date,  process_date, report_id)   ");
    	sql.append(" SELECT ");
    	sql.append("     ?,  ");
    	sql.append("     any(target_url), " + urlHashColumn + " as  url_murmur_hash, ");
    	sql.append("     max(target_trust_flow), max(target_citation_flow), ");
    	sql.append("     countDistinct(source_url_hash) AS urlCnt,  ");
    	sql.append("     countDistinct(source_url_domain) AS sourceUrlDomainCnt,  ");
    	sql.append("     countDistinct(anchor_text) AS anchorTextCnt,  ");
    	sql.append(" 	['backlink'], ?, toDate(now()), " + reportId);
    	sql.append(" FROM backlink.ref_dis_backlink_data  ");
    	
    	sql.append(" WHERE (target_url_root_domain = '" + reverseRootDomain + "')");
    	if (StringUtils.isNotBlank(reverseDomain) && !StringUtils.equals(reverseRootDomain, reverseDomain)) {
    		sql.append(" AND (target_url_domain = '" + reverseDomain + "') ");
		}
    	
    	if (partNum != 1) {
    		sql.append(" and ((url_murmur_hash % " + partNum + ") = " + num +")");
		}
    	
//		sql.append(" and url_murmur_hash global not in (select url_murmur_hash from default." + Clarity360Lweb05DAO.getDisTableNameByType(isForUrlHash) 
//			+ " where report_id = " + reportId + " and has(sources, 'sitemap')  and  ((url_murmur_hash % " + partNum + ") = " + num +")) ");
    	
    	
    	System.out.println("ownDomainEntity.getSubFolder():" + ownDomainEntity.getSubFolder());
    	System.out.println("ownDomainEntity.getSubFolderFragment():" + ownDomainEntity.getSubFolderFragment());
    	System.out.println("SQL : " + FormatUtils.getFolderSqlForDomain(FormatUtils.mergeDomainSubfolder(ownDomainEntity.getDomain(), ownDomainEntity.getSubFolder(), ownDomainEntity.getSubFolderFragment())));
    	if (StringUtils.isNotBlank(ownDomainEntity.getSubFolder()) || StringUtils.isNotBlank(ownDomainEntity.getSubFolderFragment())) {
    		sql.append(FormatUtils.getFolderSqlForDomain(FormatUtils.mergeDomainSubfolder(ownDomainEntity.getDomain(), ownDomainEntity.getSubFolder(), ownDomainEntity.getSubFolderFragment())));
		}
    	
    	if (backlinkVO.getLeftNaviFilters() != null && backlinkVO.getLeftNaviFilters().length > 0) {
            for (PaginationColFilter paginationColFilter : backlinkVO.getLeftNaviFilters()) {
                if ("url".equalsIgnoreCase(paginationColFilter.getColname())) {
                	sql.append(ClarityDBUtils.generateColumnFilterSql(paginationColFilter, "target_url", Types.VARCHAR, ClarityDBUtils.OPERATOR_AND, true, true));
                }
            }
        }
    	
    	if (urlTagId != null && urlTagId > 0) {
    		sql.append(" AND (dictGetUInt64('file_dic_rg_managed_url', 'target_url_id', (toUInt64(" + ownDomainId + "), toUInt64(" + urlTagId + "), URLHash(lower(target_url)))) > 0) ");
		}
    	
    	if (contentTypeVO != null) {
    		
    		sql.append(ClarityDBUtils.getCommonTypesFilter(contentTypeVO, "target_url"));
		}
    	
    	String today = FormatUtils.formatDate(new Date(), "yyyy-MM-dd");
    	String lastYear = FormatUtils.formatDate(DateUtils.addYears(new Date(), -1) , "yyyy-MM-dd");
    	
    	//requirement from Alps, skype 09/06/2023
//    	sql.append(" AND (toDate(first_indexed_date) >= '" + backlinkVO.getStartDate() + "') AND (toDate(first_indexed_date) <= '" + backlinkVO.getEndDate() + "')  ");
    	sql.append(" AND (toDate(first_indexed_date) >= '" + lastYear + "') AND (toDate(first_indexed_date) <= '" + today + "')  ");
    	sql.append(" GROUP BY url_murmur_hash ");
    	
    	System.out.println(sql.toString());
    	this.executeUpdate(sql.toString(), ownDomainId, targetDate);
	}
	
	

    public Integer getTotalCount(Integer ownDomainId, InternalLinkVO internalLinkVO, String targetDate,
			Integer reportId, Integer urlTagId, ClarityDBTypesFilterVO contentTypeVO) throws Exception {
    	
    	StringBuffer sql = new StringBuffer();
    	sql.append(" select count() from (");
    	sql.append(" SELECT  destinationUrlMurmurHash AS hash  ");
    	sql.append(" FROM actonia_internal_link.ref_internal_link_sampled_view_final  ");
    	sql.append(" WHERE domainId = ? and crawlRequestLogId = ? and crawlRequestLogMod = ?");
    	
    	if (urlTagId != null && urlTagId > 0) {
    		sql.append(" AND (dictGetUInt64('file_dic_rg_managed_url', 'target_url_id', (toUInt64(" + ownDomainId + "), toUInt64(" + urlTagId + "), urlHash)) > 0) ");
		}
    	
    	if (internalLinkVO.getLeftNaviFilters() != null && internalLinkVO.getLeftNaviFilters().length > 0) {
            for (PaginationColFilter paginationColFilter : internalLinkVO.getLeftNaviFilters()) {
                if ("url".equalsIgnoreCase(paginationColFilter.getColname())) {
                	sql.append(ClarityDBUtils.generateColumnFilterSql(paginationColFilter, "destUrl", Types.VARCHAR, ClarityDBUtils.OPERATOR_AND, true, true));
                }
            }
        }
    	
    	if (contentTypeVO != null) {
    		
    		sql.append(ClarityDBUtils.getCommonTypesFilter(contentTypeVO, "destUrl"));
		}
    	
    	sql.append(" GROUP BY hash ");
    	sql.append(" ) ");
        sql.append(" SETTINGS max_bytes_before_external_group_by=20000000000, max_bytes_before_external_sort=10000000000, distributed_aggregation_memory_efficient=1 ");

        System.out.println("Internal link Totalcount : " + sql.toString());
		return this.queryForInteger(sql.toString(), ownDomainId, internalLinkVO.getCrawlRequestId(), internalLinkVO.getCrawlRequestId() % 1000);
	}
	
	public void processInternalLinkSummary(Integer ownDomainId, InternalLinkVO internalLinkVO, String targetDate,
			Integer reportId, Integer urlTagId, ClarityDBTypesFilterVO contentTypeVO, String urlHashColumn, Boolean isForUrlHash, Integer partNum, Integer num) {
		
		StringBuffer sql = new StringBuffer();
    	sql.append(" insert into " + getDisTableNameByType(isForUrlHash) + " (domain_id, url, url_murmur_hash, internallink_inbound_cnt,   ");
    	sql.append("  internallink_outbound_cnt, internallink_inbound_anchortx_cnt, sources,  target_date,  process_date, report_id)   ");
    	sql.append(" SELECT ");
    	sql.append("     any(domainId),  ");
    	sql.append("     any(destUrl),  ");
    	sql.append("     " + urlHashColumn + " as url_murmur_hash,  ");
//    	sql.append("     destinationUrlMurmurHash,  ");
    	sql.append("     (countDistinct(sourceUrlHash) - if(any(sourceUrl) == '' , 1, 0)) AS countInbound,  ");
    	sql.append("     any(outboundCnt) AS countoutbound,  ");
    	sql.append("     (countDistinct(anchorTextHash) - if(any(sourceUrl) == '' , 1, 0)) AS countAnchor, ");
    	sql.append(" 	['internallink'], ?, toDate(now()), " + reportId);
    	sql.append(" FROM actonia_internal_link.ref_internal_link_sampled_view_final  ");
    	sql.append(" WHERE domainId = ? and crawlRequestLogId = ? and crawlRequestLogMod = ?");
    	

    	if (partNum != 1) {
    		sql.append(" and ((url_murmur_hash % " + partNum + ") = " + num +")");
		}
    	
//		sql.append(" and url_murmur_hash global not in (select url_murmur_hash from default." + Clarity360Lweb05DAO.getDisTableNameByType(isForUrlHash) 
//			+ " where report_id = " + reportId + " and has(sources, 'sitemap')  and  ((url_murmur_hash % " + partNum + ") = " + num +")) ");
    	
    	
    	if (urlTagId != null && urlTagId > 0) {
    		sql.append(" AND (dictGetUInt64('file_dic_rg_managed_url', 'target_url_id', (toUInt64(" + ownDomainId + "), toUInt64(" + urlTagId + "), urlHash)) > 0) ");
		}
    	
    	if (internalLinkVO.getLeftNaviFilters() != null && internalLinkVO.getLeftNaviFilters().length > 0) {
            for (PaginationColFilter paginationColFilter : internalLinkVO.getLeftNaviFilters()) {
                if ("url".equalsIgnoreCase(paginationColFilter.getColname())) {
                	sql.append(ClarityDBUtils.generateColumnFilterSql(paginationColFilter, "destUrl", Types.VARCHAR, ClarityDBUtils.OPERATOR_AND, true, true));
                }
            }
        }
    	
    	if (contentTypeVO != null) {
    		
    		sql.append(ClarityDBUtils.getCommonTypesFilter(contentTypeVO, "destUrl"));
		}
    	
    	sql.append(" GROUP BY url_murmur_hash ");
    	
    	System.out.println(sql.toString());
    	this.executeUpdate(sql.toString(), targetDate, ownDomainId, internalLinkVO.getCrawlRequestId(), internalLinkVO.getCrawlRequestId() % 1000);
	}
	
	
    public Integer getTotalCount(Integer ownDomainId, CustomDataSourceVO customDataSourceVO, OwnDomainEntity ownDomainEntity, String targetDate, 
			Integer reportId, Integer urlTagId, ClarityDBTypesFilterVO contentTypeVO) throws Exception {
    	
		if (customDataSourceVO == null || customDataSourceVO.getFile() == null || 
				customDataSourceVO.getFile().getId() == null || customDataSourceVO.getFile().getId() == 0) {
			System.out.println("File id is not found!");
			return 0;
		}
    	
    	StringBuffer sql = new StringBuffer();
    	sql.append(" select count() from (");
    	sql.append(" SELECT  url_murmur_hash AS hash  ");
    	sql.append(" FROM clarity360.dis_url_attributes  ");
    	sql.append("  where file_id = ?");
    	
    	sql.append(" GROUP BY hash ");
    	sql.append(" ) ");
        sql.append(" SETTINGS max_bytes_before_external_group_by=20000000000, max_bytes_before_external_sort=10000000000, distributed_aggregation_memory_efficient=1 ");

        System.out.println("Custom datasource Totalcount : " + sql.toString());
		return this.queryForInteger(sql.toString(), customDataSourceVO.getFile().getId());
	}
    
	
	public void customDataSource360Summary(Integer ownDomainId, CustomDataSourceVO customDataSourceVO, OwnDomainEntity ownDomainEntity, String targetDate, 
			Integer reportId, Integer urlTagId, ClarityDBTypesFilterVO contentTypeVO, String urlHashColumn, Boolean isForUrlHash, Integer partNum, Integer num) {
		
		if (customDataSourceVO == null || customDataSourceVO.getFile() == null || 
				customDataSourceVO.getFile().getId() == null || customDataSourceVO.getFile().getId() == 0) {
			System.out.println("File id is not found!");
			return;
		}
		
		StringBuffer sql = new StringBuffer();
    	sql.append(" insert into " + getDisTableNameByType(isForUrlHash) + " (domain_id, url, url_murmur_hash, "
    			+ " custom_data_source.keys, custom_data_source.values, sources,  target_date,  process_date, report_id)   ");
    	sql.append(" SELECT ");
    	sql.append("     any(domain_id),  ");
    	sql.append("     any(url), " + urlHashColumn + " as  url_murmur_hash, ");
    	sql.append("     any(attr.keys), any(attr.values), ");
    	sql.append(" 	['customDataSource'], ?, toDate(now()), " + reportId);
    	sql.append(" FROM clarity360.dis_url_attributes  ");
    	sql.append("  where file_id = ?");
    	
    	if (partNum != 1) {
    		sql.append(" and ((url_murmur_hash % " + partNum + ") = " + num +")");
		}
    	
//		sql.append(" and url_murmur_hash global not in (select url_murmur_hash from default." + Clarity360Lweb05DAO.getDisTableNameByType(isForUrlHash) 
//			+ " where report_id = " + reportId + " and has(sources, 'sitemap')  and  ((url_murmur_hash % " + partNum + ") = " + num +")) ");
    	
    	
    	sql.append(" GROUP BY url_murmur_hash ");
    	
    	System.out.println(sql.toString());
    	this.executeUpdate(sql.toString(), targetDate, customDataSourceVO.getFile().getId());
	}


	public void insertBatch(List<EmbeddingEntity> insertData, String tableName) {
        String sql = "INSERT INTO " + tableName
                + " (crawl_request_id, crawl_request_date, domain_id, url,"
                + " title, meta, h1, h2, custom_data_1, custom_data_2, custom_data_3, custom_data_4, custom_data_5, "
                + " title_embedding, meta_embedding, h1_embedding, h2_embedding, custom_data_1_embedding,  "
                + " custom_data_2_embedding, custom_data_3_embedding, custom_data_4_embedding, custom_data_5_embedding"
                + ") VALUES (?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, ?,?)";
        List<Object[]> batch = new ArrayList<Object[]>();
        int i = 0;
        for (EmbeddingEntity entity : insertData) {
            Object[] values = new Object[]{
                    entity.getCrawlRequestId(),
                    entity.getCrawlRequestDate(),
                    entity.getDomainId(),
                    entity.getUrl(),
                    entity.getTitle(),
                    entity.getMeta(),
                    entity.getH1(),
                    entity.getH2(),
                    entity.getCustomData1(),
                    entity.getCustomData2(),
                    entity.getCustomData3(),
                    entity.getCustomData4(),
                    entity.getCustomData5(),
                    entity.getTitleEmbedding(),
                    entity.getMetaEmbedding(),
                    entity.getH1Embedding(),
                    entity.getH2Embedding(),
                    entity.getCustomData1Embedding(),
                    entity.getCustomData2Embedding(),
                    entity.getCustomData3Embedding(),
                    entity.getCustomData4Embedding(),
                    entity.getCustomData5Embedding()
            };

            if (i++ < 5) {
                System.out.println(new Gson().toJson(values));
            }

            batch.add(values);
        }
        executeBatch(sql, batch);
    }
	
	public static final String[] IMPORT_HEADER = new String[] {
			 "url", 
			 "alt_img_detail", 
			 "alt_img_list", 
			 "alternate_links", 
			 "amphtml_flag", 
			 "amphtml_href", 
			 "analyzed_url_flg_s", 
			 "analyzed_url_s", 
			 "archive_flg", 
			 "archive_flg_x_tag", 
			 "attrint.key", 
			 "attrint.value", 
			 "attrstr.key", 
			 "attrstr.value", 
			 "backlink_citationflow", 
			 "backlink_cnt_anchortext", 
			 "backlink_cnt_backlinks", 
			 "backlink_cnt_backlinksdomains", 
			 "backlink_trustflow", 
			 "blocked_by_robots", 
			 "bot_2xxrequestcount", 
			 "bot_3xxrequestcount", 
			 "bot_4xxrequestcount", 
			 "bot_5xxrequestcount", 
			 "bot_numofdays", 
			 "bot_requestcount", 
			 "canonical", 
			 "canonical_flg", 
			 "canonical_header_flag", 
			 "canonical_header_type", 
			 "canonical_type", 
			 "canonical_url_is_consistent", 
			 "change_tracking_hash_cd_json", 
			 "content_extracted_count", 
			 "content_extracted_flg", 
			 "content_flg", 
			 "content_md5", 
			 "content_type", 
			 "content_word_count", 
			 "content_words", 
			 "conv_crawl_date", 
			 "count_of_objects", 
			 "crawl_date_long", 
			 "crawl_depth", 
			 "crawl_request_date", 
			 "crawl_request_id", 
			 "custom_data.content", 
			 "custom_data.index", 
			 "custom_data.links", 
			 "custom_data.match_found", 
			 "custom_data.selector", 
			 "custom_data.selector_type", 
			 "custom_data.word_count", 
			 "description", 
			 "description_flg", 
			 "description_length", 
			 "description_murmurhash", 
			 "description_simhash", 
			 "description_stemmed", 
			 "document_size", 
			 "domain", 
			 "domain_id", 
			 "domain_name", 
			 "download_latency", 
			 "download_time", 
			 "error_message", 
			 "external_link", 
			 "final_response_code", 
			 "folder_level_1", 
			 "folder_level_2", 
			 "folder_level_3", 
			 "folder_level_count", 
			 "follow_flg", 
			 "follow_flg_x_tag", 
			 "gsc_clicks", 
			 "gsc_impressions", 
			 "gsc_pos_count", 
			 "gsc_pos_sum", 
			 "h1", 
			 "h1_count", 
			 "h1_flg", 
			 "h1_length", 
			 "h1_md5", 
			 "h1_murmurhash", 
			 "h1_simhash", 
			 "h1_stemmed", 
			 "h2", 
			 "h2_murmurhash", 
			 "h2_simhash", 
			 "h2_stemmed", 
			 "header_noarchive", 
			 "header_nofollow", 
			 "header_noindex", 
			 "header_noodp", 
			 "header_nosnippet", 
			 "header_noydir", 
			 "hreflang_errors", 
			 "hreflang_links", 
			 "hreflang_links_href", 
			 "hreflang_links_href_array", 
			 "hreflang_links_href_hash_array", 
			 "hreflang_links_lang", 
			 "hreflang_links_out_count", 
			 "hreflang_links_type", 
			 "hreflang_url_count", 
			 "hsts", 
			 "index_flg", 
			 "index_flg_x_tag", 
			 "indexable", 
			 "insecure_resources", 
			 "insecure_resources_flag", 
			 "internallink_inbound_anchortx_cnt", 
			 "internallink_inbound_cnt", 
			 "internallink_outbound_cnt", 
			 "is_footer_link", 
			 "is_header_link", 
			 "lang", 
			 "long_redirect", 
			 "meta_charset", 
			 "meta_content_type", 
			 "meta_disabled_sitelinks", 
			 "meta_noodp", 
			 "meta_nosnippet", 
			 "meta_noydir", 
			 "meta_redirect", 
			 "mixed_redirects", 
			 "mobile_rel_alternate_url_is_consistent", 
			 "noodp", 
			 "nosnippet", 
			 "noydir", 
			 "og_markup", 
			 "og_markup_content", 
			 "og_markup_flag", 
			 "og_markup_length", 
			 "og_markup_property", 
			 "outlink_count", 
			 "page_1", 
			 "page_analysis_fragments", 
			 "page_analysis_issue_count", 
			 "page_analysis_issues", 
			 "page_analysis_issues_array", 
			 "page_analysis_results", 
			 "page_link", 
			 "page_link_destination_url", 
			 "page_link_destination_url_hash", 
			 "page_link_destination_url_murmur_hash", 
			 "page_timeout_flag", 
			 "paginated", 
			 "pagination_links", 
			 "pagination_links_direction", 
			 "parent_page", 
			 "post_processing_issues_array", 
			 "protocol", 
			 "redirect_blocked", 
			 "redirect_blocked_reason", 
			 "redirect_chain", 
			 "redirect_final_url", 
			 "redirect_flg", 
			 "redirect_times", 
			 "rel_next_html_url", 
			 "rel_next_url_is_consistent", 
			 "rel_prev_url_is_consistent", 
			 "report_id", 
			 "request_headers", 
			 "request_time", 
			 "resources.is_blocked", 
			 "resources.is_from_memory_cache", 
			 "resources.request_type", 
			 "resources.status_code", 
			 "resources.types", 
			 "resources.url", 
			 "response_code", 
			 "response_headers", 
			 "retry_attempted", 
			 "rg_avg_rank", 
			 "rg_highest_top_100_cnt", 
			 "rg_intent_array", 
			 "rg_intent_kwcnt_array", 
			 "rg_sov", 
			 "rg_sum_top1_avg_sv", 
			 "rg_sum_top1_est_traffic", 
			 "rg_sum_top10_avg_sv", 
			 "rg_sum_top10_est_traffic", 
			 "rg_sum_top100_avg_sv", 
			 "rg_sum_top2_avg_sv", 
			 "rg_sum_top2_est_traffic", 
			 "rg_sum_top20_avg_sv", 
			 "rg_sum_top3_avg_sv", 
			 "rg_sum_top3_est_traffic", 
			 "rg_sum_top30_avg_sv", 
			 "rg_top100cnt", 
			 "rg_top10cnt", 
			 "rg_top1cnt", 
			 "rg_top20cnt", 
			 "rg_top2cnt", 
			 "rg_top30cnt", 
			 "rg_top3cnt", 
			 "rg_total_rank", 
			 "rg_traffic_potential", 
			 "rg_typicalpage_strength", 
			 "rg_wtd_avg_rank", 
			 "rg_wtd_vol_rank", 
			 "ri_avg_rank", 
			 "ri_mumofdays", 
			 "ri_top1", 
			 "ri_top10", 
			 "ri_top3", 
			 "ri_total_sv", 
			 "ri_wtd_avg_rank", 
			 "robots", 
			 "robots_contents", 
			 "robots_contents_x_tag", 
			 "robots_flg", 
			 "robots_flg_x_tag", 
			 "sa_bounces", 
			 "sa_entrances", 
			 "sa_exits", 
			 "sa_goal10completions", 
			 "sa_goal10Value", 
			 "sa_goal11completions", 
			 "sa_goal11Value", 
			 "sa_goal12completions", 
			 "sa_goal12Value", 
			 "sa_goal13completions", 
			 "sa_goal13Value", 
			 "sa_goal14completions", 
			 "sa_goal14Value", 
			 "sa_goal15completions", 
			 "sa_goal15Value", 
			 "sa_goal16completions", 
			 "sa_goal16Value", 
			 "sa_goal17completions", 
			 "sa_goal17Value", 
			 "sa_goal18completions", 
			 "sa_goal18Value", 
			 "sa_goal19completions", 
			 "sa_goal19Value", 
			 "sa_goal1completions", 
			 "sa_goal1Value", 
			 "sa_goal20completions", 
			 "sa_goal20Value", 
			 "sa_goal2completions", 
			 "sa_goal2Value", 
			 "sa_goal3completions", 
			 "sa_goal3Value", 
			 "sa_goal4completions", 
			 "sa_goal4Value", 
			 "sa_goal5completions", 
			 "sa_goal5Value", 
			 "sa_goal6completions", 
			 "sa_goal6Value", 
			 "sa_goal7completions", 
			 "sa_goal7Value", 
			 "sa_goal8completions", 
			 "sa_goal8Value", 
			 "sa_goal9completions", 
			 "sa_goal9Value", 
			 "sa_item_revenue", 
			 "sa_pageviews", 
			 "sa_session_duration", 
			 "sa_time_on_page", 
			 "sa_transactions", 
			 "searchanlytics_numofdays", 
			 "server_response_time", 
			 "siteanalytics_numofdays", 
			 "source_url", 
			 "sources", 
			 "splash_took", 
			 "status", 
			 "structure_schema_errors.encoding", 
			 "structure_schema_errors.error_type", 
			 "structure_schema_errors.info", 
			 "structure_schema_errors.message", 
			 "structure_schema_errors.path", 
			 "structure_schema_errors.schema_type", 
			 "structure_schema_warnings.encoding", 
			 "structure_schema_warnings.info", 
			 "structure_schema_warnings.message", 
			 "structure_schema_warnings.path", 
			 "structure_schema_warnings.schema_type", 
			 "structure_schema_warnings.warning_type", 
			 "structured_data", 
			 "structured_schema.encoding", 
			 "structured_schema.type", 
			 "target_date", 
			 "title", 
			 "title_flg", 
			 "title_length", 
			 "title_md5", 
			 "title_murmurhash", 
			 "title_simhash", 
			 "title_stemmed", 
			 "twitter_description_length", 
			 "twitter_markup", 
			 "twitter_markup_content", 
			 "twitter_markup_flag", 
			 "twitter_markup_length", 
			 "twitter_markup_property", 
			 "url_length", 
			 "valid_twitter_card", 
			 "viewport_content", 
			 "viewport_flag",
			 "root_sitemap_hash_array"
	 };
	 
	
    public void insertForBatch(List<Clarity360Entity> insertData) {


	    String sql = "INSERT INTO tmp_fake_date_20231120"
	            + " (" + StringUtils.join(IMPORT_HEADER, ",") + ") VALUES ("
	            		+ "?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, "
	            		+ "?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, "
	            		+ "?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, "
	            		+ "?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, "
	            		+ "?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, "
	            		+ "?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, "
	            		+ "?,?,? )";
	    List<Object[]> batch = new ArrayList<Object[]>();
	    int i = 0;
	    for (Clarity360Entity entity : insertData) {
	        Object[] values = new Object[]{
	                entity.getUrl(),
	                entity.getAlt_img_detail(),
	                entity.getAlt_img_list(),
	                entity.getAlternate_links(),
	                entity.getAmphtml_flag(),
	                entity.getAmphtml_href(),
	                entity.getAnalyzed_url_flg_s(),
	                entity.getAnalyzed_url_s(),
	                entity.getArchive_flg(),
	                entity.getArchive_flg_x_tag(),
	                getArray(entity.getAttrint_key()),
	                getArray(entity.getAttrint_value()),
	                getArray(entity.getAttrstr_key()),
	                getArray(entity.getAttrstr_value()),
	                entity.getBacklink_citationflow(),
	                entity.getBacklink_cnt_anchortext(),
	                entity.getBacklink_cnt_backlinks(),
	                entity.getBacklink_cnt_backlinksdomains(),
	                entity.getBacklink_trustflow(),
	                entity.getBlocked_by_robots(),
	                entity.getBot_2xxrequestcount(),
	                entity.getBot_3xxrequestcount(),
	                entity.getBot_4xxrequestcount(),
	                entity.getBot_5xxrequestcount(),
	                entity.getBot_numofdays(),
	                entity.getBot_requestcount(),
	                entity.getCanonical(),
	                entity.getCanonical_flg(),
	                entity.getCanonical_header_flag(),
	                entity.getCanonical_header_type(),
	                entity.getCanonical_type(),
	                entity.getCanonical_url_is_consistent(),
	                entity.getChange_tracking_hash_cd_json(),
	                entity.getContent_extracted_count(),
	                entity.getContent_extracted_flg(),
	                entity.getContent_flg(), 
	                entity.getContent_md5(), 
	                entity.getContent_type(), 
	                entity.getContent_word_count(), 
	                entity.getContent_words(), 
	                entity.getConv_crawl_date(), 
	                entity.getCount_of_objects(), 
	                entity.getCrawl_date_long(), 
	                entity.getCrawl_depth(), 
	                "2023-11-18", 
	                entity.getCrawl_request_id(), 
	                getArray(entity.getCustom_data_content()), 
	                getArray(entity.getCustom_data_index()), 
	                getArray(entity.getCustom_data_links()), 
	                getArray(entity.getCustom_data_match_found()), 
	                getArray(entity.getCustom_data_selector()), 
	                getArray(entity.getCustom_data_selector_type()), 
	                getArray(entity.getCustom_data_word_count()), 
	                entity.getDescription(), 
	                entity.getDescription_flg(), 
	                entity.getDescription_length(), 
	                entity.getDescription_murmurhash(), 
	                entity.getDescription_simhash(), 
	                getArray(entity.getDescription_stemmed()), 
	                entity.getDocument_size(), 
	                entity.getDomain(), 
	                12548, 
	                entity.getDomain_name(), 
	                entity.getDownload_latency(), 
	                entity.getDownload_time(), 
	                entity.getError_message(),
	                entity.getExternal_link(),

	                entity.getFinal_response_code(), 
	                entity.getFolder_level_1(), 
	                entity.getFolder_level_2(), 
	                entity.getFolder_level_3(), 
	                entity.getFolder_level_count(), 
	                entity.getFollow_flg(), 
	                entity.getFollow_flg_x_tag(), 
	                entity.getGsc_clicks(), 
	                entity.getGsc_impressions(), 
	                entity.getGsc_pos_count(), 
	                entity.getGsc_pos_sum(), 
	                getArray(entity.getH1()), 
	                entity.getH1_count(), 
	                entity.getH1_flg(), 
	                entity.getH1_length(), 
	                entity.getH1_md5(), 
	                entity.getH1_murmurhash(), 
	                entity.getH1_simhash(), 
	                getArray(entity.getH1_stemmed()), 
	                getArray(entity.getH2()), 
	                entity.getH2_murmurhash(), 
	                entity.getH2_simhash(), 
	                getArray(entity.getH2_stemmed()), 
	                entity.getHeader_noarchive(), 
	                entity.getHeader_nofollow(), 
	                entity.getHeader_noindex(), 
	                entity.getHeader_noodp(), 
	                entity.getHeader_nosnippet(), 
	                entity.getHeader_noydir(), 
	                entity.getHreflang_errors(), 
	                entity.getHreflang_links(), 
	                getArray(entity.getHreflang_links_href()), 
	                getArray(entity.getHreflang_links_href_array()), 
	                getArray(entity.getHreflang_links_href_hash_array()), 
	                getArray(entity.getHreflang_links_lang()), 
	                entity.getHreflang_links_out_count(), 
	                getArray(entity.getHreflang_links_type()), 
	                entity.getHreflang_url_count(), 
	                entity.getHsts(), 
	                entity.getIndex_flg(), 
	                entity.getIndex_flg_x_tag(), 
	                entity.getIndexable(), 
	                entity.getInsecure_resources(), 
	                entity.getInsecure_resources_flag(), 
	                entity.getInternallink_inbound_anchortx_cnt(), 
	                entity.getInternallink_inbound_cnt(), 
	                entity.getInternallink_outbound_cnt(), 
	                entity.getIs_footer_link(), 
	                entity.getIs_header_link(), 
	                entity.getLang(), 
	                entity.getLong_redirect(), 
	                entity.getMeta_charset(), 
	                entity.getMeta_content_type(), 
	                entity.getMeta_disabled_sitelinks(), 
	                entity.getMeta_noodp(), 
	                entity.getMeta_nosnippet(), 
	                entity.getMeta_noydir(), 
	                entity.getMeta_redirect(), 
	                entity.getMixed_redirects(), 
	                entity.getMobile_rel_alternate_url_is_consistent(), 
	                entity.getNoodp(), 
	                entity.getNosnippet(), 
	                entity.getNoydir(), 
	                entity.getOg_markup(), 
	                getArray(entity.getOg_markup_content()), 
	                entity.getOg_markup_flag(), 
	                entity.getOg_markup_length(), 
	                getArray(entity.getOg_markup_property()), 
	                entity.getOutlink_count(), 
	                entity.getPage_1(), 
	                getArray(entity.getPage_analysis_fragments()), 
	                entity.getPage_analysis_issue_count(), 
	                entity.getPage_analysis_issues(), 
	                getArray(entity.getPage_analysis_issues_array()), 
	                entity.getPage_analysis_results(), 
	                entity.getPage_link(), 
	                getArray(entity.getPage_link_destination_url()), 
	                getArray(entity.getPage_link_destination_url_hash()), 
	                getArray(entity.getPage_link_destination_url_murmur_hash()), 
	                entity.getPage_timeout_flag(), 
	                entity.getPaginated(), 
	                entity.getPagination_links(), 
	                getArray(entity.getPagination_links_direction()), 
	                entity.getParent_page(), 
	                getArray(entity.getPost_processing_issues_array()), 
	                entity.getProtocol(), 
	                entity.getRedirect_blocked(), 
	                entity.getRedirect_blocked_reason(), 
	                entity.getRedirect_chain(), 
	                entity.getRedirect_final_url(), 
	                entity.getRedirect_flg(), 
	                entity.getRedirect_times(), 
	                entity.getRel_next_html_url(), 
	                entity.getRel_next_url_is_consistent(), 
	                entity.getRel_prev_url_is_consistent(), 
	                418, 
	                entity.getRequest_headers(), 
	                entity.getRequest_time(), 
	                getArray(entity.getResources_is_blocked()), 
	                getArray(entity.getResources_is_from_memory_cache()), 
	                getArray(entity.getResources_request_type()), 
	                getArray(entity.getResources_status_code()), 
	                getArray(entity.getResources_types()), 
	                getArray(entity.getResources_url()), 
	                entity.getResponse_code(), 
	                entity.getResponse_headers(), 
	                entity.getRetry_attempted(), 
	                entity.getRg_avg_rank(), 
	                entity.getRg_highest_top_100_cnt(), 
	                getArray(entity.getRg_intent_array()), 
	                getArray(entity.getRg_intent_kwcnt_array()), 
	                entity.getRg_sov(), 
	                entity.getRg_sum_top1_avg_sv(), 
	                entity.getRg_sum_top1_est_traffic(), 
	                entity.getRg_sum_top10_avg_sv(), 
	                entity.getRg_sum_top10_est_traffic(), 
	                entity.getRg_sum_top100_avg_sv(), 
	                entity.getRg_sum_top2_avg_sv(), 
	                entity.getRg_sum_top2_est_traffic(), 
	                entity.getRg_sum_top20_avg_sv(), 
	                entity.getRg_sum_top3_avg_sv(), 
	                entity.getRg_sum_top3_est_traffic(), 
	                entity.getRg_sum_top30_avg_sv(), 
	                entity.getRg_top100cnt(), 
	                entity.getRg_top10cnt(), 
	                entity.getRg_top1cnt(), 
	                entity.getRg_top20cnt(), 
	                entity.getRg_top2cnt(), 
	                entity.getRg_top30cnt(), 
	                entity.getRg_top3cnt(), 
	                entity.getRg_total_rank(), 
	                entity.getRg_traffic_potential(), 
	                entity.getRg_typicalpage_strength(), 
	                entity.getRg_wtd_avg_rank(), 
	                entity.getRg_wtd_vol_rank(), 
	                entity.getRi_avg_rank(), 
	                entity.getRi_mumofdays(), 
	                entity.getRi_top1(), 
	                entity.getRi_top10(), 
	                entity.getRi_top3(), 
	                entity.getRi_total_sv(), 
	                entity.getRi_wtd_avg_rank(), 
	                entity.getRobots(), 
	                entity.getRobots_contents(), 
	                entity.getRobots_contents_x_tag(), 
	                entity.getRobots_flg(), 
	                entity.getRobots_flg_x_tag(), 
	                entity.getSa_bounces(), 
	                entity.getSa_entrances(), 
	                entity.getSa_exits(), 
	                entity.getSa_goal10completions(), 
	                entity.getSa_goal10Value(), 
	                entity.getSa_goal11completions(), 
	                entity.getSa_goal11Value(), 
	                entity.getSa_goal12completions(), 
	                entity.getSa_goal12Value(), 
	                entity.getSa_goal13completions(), 
	                entity.getSa_goal13Value(), 
	                entity.getSa_goal14completions(), 
	                entity.getSa_goal14Value(), 
	                entity.getSa_goal15completions(), 
	                entity.getSa_goal15Value(), 
	                entity.getSa_goal16completions(), 
	                entity.getSa_goal16Value(), 
	                entity.getSa_goal17completions(), 
	                entity.getSa_goal17Value(), 
	                entity.getSa_goal18completions(), 
	                entity.getSa_goal18Value(), 
	                entity.getSa_goal19completions(), 
	                entity.getSa_goal19Value(), 
	                entity.getSa_goal1completions(), 
	                entity.getSa_goal1Value(), 
	                entity.getSa_goal20completions(), 
	                entity.getSa_goal20Value(), 
	                entity.getSa_goal2completions(), 
	                entity.getSa_goal2Value(), 
	                entity.getSa_goal3completions(), 
	                entity.getSa_goal3Value(), 
	                entity.getSa_goal4completions(), 
	                entity.getSa_goal4Value(), 
	                entity.getSa_goal5completions(), 
	                entity.getSa_goal5Value(), 
	                entity.getSa_goal6completions(), 
	                entity.getSa_goal6Value(), 
	                entity.getSa_goal7completions(), 
	                entity.getSa_goal7Value(), 
	                entity.getSa_goal8completions(), 
	                entity.getSa_goal8Value(), 
	                entity.getSa_goal9completions(), 
	                entity.getSa_goal9Value(), 
	                entity.getSa_item_revenue(), 
	                entity.getSa_pageviews(), 
	                entity.getSa_session_duration(), 
	                entity.getSa_time_on_page(), 
	                entity.getSa_transactions(), 
	                entity.getSearchanlytics_numofdays(), 
	                entity.getServer_response_time(), 
	                entity.getSiteanalytics_numofdays(), 
	                entity.getSource_url(), 
	                getArray(entity.getSources()), 
	                entity.getSplash_took(), 
	                entity.getStatus(), 
	                getArray(entity.getStructure_schema_errors_encoding()), 
	                getArray(entity.getStructure_schema_errors_error_type()), 
	                getArray(entity.getStructure_schema_errors_info()), 
	                getArray( entity.getStructure_schema_errors_message()), 
	                getArray(entity.getStructure_schema_errors_path()), 
	                getArray(entity.getStructure_schema_errors_schema_type()), 
	                getArray(entity.getStructure_schema_warnings_encoding()), 
	                getArray(entity.getStructure_schema_warnings_info()), 
	                getArray(entity.getStructure_schema_warnings_message()), 
	                getArray(entity.getStructure_schema_warnings_path()), 
	                getArray(entity.getStructure_schema_warnings_schema_type()), 
	                getArray(entity.getStructure_schema_warnings_warning_type()), 
	                entity.getStructured_data(), 
	                getArray(entity.getStructured_schema_encoding()), 
	                getArray(entity.getStructured_schema_type()), 
	                "2023-11-19", 
	                entity.getTitle(), 
	                entity.getTitle_flg(), 
	                entity.getTitle_length(), 
	                entity.getTitle_md5(), 
	                entity.getTitle_murmurhash(), 
	                entity.getTitle_simhash(), 
	                getArray(entity.getTitle_stemmed()), 
	                entity.getTwitter_description_length(), 
	                entity.getTwitter_markup(), 
	                getArray(entity.getTwitter_markup_content()), 
	                entity.getTwitter_markup_flag(), 
	                entity.getTwitter_markup_length(), 
	                getArray(entity.getTwitter_markup_property()), 
	                entity.getUrl_length(), 
	                entity.getValid_twitter_card(), 
	                entity.getViewport_content(), 
	                entity.getViewport_flag(),
	                getArrayToLong(entity.getRoot_sitemap_hash_array())
	        };
	
	        if (i++ < 1) {
	            System.out.println(new Gson().toJson(values));
	        }
	
	        batch.add(values);
	    }
	    executeBatch(sql, batch);
	}
    
    
	public static final String[] IMPORT_HEADER_V2 = new String[] {
			"url", 
			"status", 
			"crawl_depth", 
			"title", 
			"h1", 
			"h2", 
			"custom_data.content", 
			"custom_data.selector_type", 
			"custom_data.match_found", 
			"custom_data.selector", 
			
			
			"description", 
			"indexable", 
			"canonical_type", 
			"index_flg", 
			"outlink_count", 
			"server_response_time", 
			"download_time", 
			
			"title_length", 
			"h1_length",

			"domain", 
			"domain_name", 
			"folder_level_1", 
			"folder_level_2", 
			"folder_level_3", 
			"folder_level_count", 
			"domain_id", 
			"report_id", 
			"crawl_request_id", 
			"sources", 
			"target_date", 
			"description_length",
	 };
    
    public void insertForBatchV2(List<Clarity360Entity> insertData) {


	    String sql = "INSERT INTO tmp_fake_date_20231120"
	            + " (" + StringUtils.join(IMPORT_HEADER_V2, ",") + ") VALUES ("
	            		+ "?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, ?)";
	    List<Object[]> batch = new ArrayList<Object[]>();
	    int i = 0;
	    for (Clarity360Entity entity : insertData) {
	        Object[] values = new Object[]{
	                entity.getUrl(),
	                entity.getStatus(),
	                entity.getCrawl_depth(),
	                entity.getTitle(),
	                getArray(entity.getH1()),
	                getArray(entity.getH2()),
	                getArray(entity.getCustom_data_content()),
	                getArray(entity.getCustom_data_selector_type()),
	                getArray(entity.getCustom_data_match_found()),
	                getArray(entity.getCustom_data_selector()),
	                
	                entity.getDescription(),
	                entity.getIndexable(),
	                entity.getCanonical_type(),
	                entity.getIndex_flg(),
	                entity.getOutlink_count(),
	                entity.getServer_response_time(),
	                entity.getDownload_time(),
	                
	                entity.getTitle_length(),
	                entity.getH1_length(),

	                entity.getDomain(),
	                entity.getDomain_name(),
	                entity.getFolder_level_1(),
	                entity.getFolder_level_2(),
	                entity.getFolder_level_3(),
	                entity.getFolder_level_count(),
	                entity.getDomain_id(),
	                entity.getReport_id(),
	                entity.getCrawl_request_id(),
	                getArray(entity.getSources()),
	                entity.getTarget_date(),
	                entity.getDescription_length(),
	                
	        };
	
	        if (i++ < 3) {
	            System.out.println(new Gson().toJson(values));
	        }
	
	        batch.add(values);
	    }
	    executeBatch(sql, batch);
	}
    
    public String[] getArray(String[] array) {
    	if (ArrayUtils.isEmpty(array)) {
			return new String[] {};
		}
    	return array;
    }

    public Integer[] getArray(Integer[] array) {
    	if (ArrayUtils.isEmpty(array)) {
			return new Integer[] {};
		}
    	return array;
    }
    
    public BigInteger[] getArrayToLong(String[] array) {
    	if (ArrayUtils.isEmpty(array)) {
    		return new BigInteger[] {};
    	}
    	BigInteger[] resultArray = new BigInteger[array.length];
    	for(int i = 0; i < array.length; i++) {
    		resultArray[i] = new BigInteger(array[i]);
    		
    	}
    	return resultArray;
    }

	public void loadDatIntoFinalTable(Integer ownDomainId, Integer reportId, int partNum, int num, boolean isForUrlHash) {
		
		StringBuffer sql = new StringBuffer();
		
		sql.append(" insert into " + getFinalTableNameByType(isForUrlHash));
		sql.append(" select * from " + getDisTableNameByType(isForUrlHash) + " where report_id = " + reportId + " and domain_id = " + ownDomainId);
		
		if (partNum != 1) {
    		sql.append(" and ((url_murmur_hash % " + partNum + ") = " + num +")");
		}
		
		sql.append(" and url_murmur_hash global not in (select url_murmur_hash from " + getFinalTableNameByType(isForUrlHash));
    	sql.append(" where report_id = " + reportId );
    	
    	if (partNum != 1) {
    		sql.append(" and ((url_murmur_hash % " + partNum + ") = " + num +")");
		}
    	
    	sql.append(")");
		System.out.println(sql.toString());
		
		this.executeUpdate(sql.toString());
	}
	
	
	
	
	
}
