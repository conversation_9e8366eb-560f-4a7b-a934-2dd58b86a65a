package seoclarity.backend.dao.clickhouse.gscclicksteam;

import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.actoniamonitor.UploadFileMonitorEntity;
import seoclarity.backend.entity.clickhouse.gscclicksteam.GscClickSteamEntity;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.cityhash.CityHashUtil;
import seoclarity.backend.utils.murmurhash.MurmurHashUtils;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Repository
public class GscClickSteamDAO extends ClGscClickSteamBaseJdbcSupport<GscClickSteamEntity> {

    @Override
    public String getTableName() {
        return "dis_gsc_clickstream";
    }

    public String getTableName(int frequency) {
        if (frequency == UploadFileMonitorEntity.FREQUENCY_DAILY) {
//            return "dis_daily_gsc_clickstream";
            return "dis_gsc_clickstream";
        } else if (frequency == UploadFileMonitorEntity.FREQUENCY_WEEKLY) {
            return "dis_gsc_clickstream";
        } else {
            return getTableName();
        }
    }

    public void insertForBatch(List<GscClickSteamEntity> insertData, int frequency) {
        System.out.println("===insertData:" + insertData.size() + ",tableName:" + getTableName(frequency));
        String sql = "INSERT INTO " + getTableName(frequency)
                + " (siteurl, clicks, ctr, impressions, country_cd, device, keyword_name, " +
                "url, log_date, position, stream, versioning, sign, attrs.key, attrs.value) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
        List<Object[]> batch = new ArrayList<Object[]>();
        int i = 0;
        for (GscClickSteamEntity entity : insertData) {
            Object[] values = new Object[]{
                    entity.getSiteurl(),
                    entity.getClicks(),
                    entity.getCtr(),
                    entity.getImpressions(),
                    entity.getCountryCd(),
                    entity.getDevice(),
                    entity.getKeywordName(),
                    entity.getUrl(),
                    FormatUtils.formatDate(entity.getLogDate(), "yyyy-MM-dd"),
                    entity.getPosition(),
                    entity.getStream(),
                    entity.getVersioning(),
                    entity.getSign(),
                    entity.getAttrsKeyArray(),
                    entity.getAttrsValueArray()
            };

            batch.add(values);
        }
        executeBatch(sql, batch);
    }

    public List<String> getEngineLanguageListFromBigAnnual() {
        String sql = " select concat(toString(engine_id), '_', toString(language_id)) from gsc_clickstream.dis_keyword_stream ";
        sql += " where country_cd global in ";
        sql += " (select distinct country_cd from gsc_clickstream.dis_keyword_summary_annual_big) ";
        sql += " group by engine_id,language_id ";
        return queryForStringList(sql);
    }

    public List<Map<String, Object>> getSvByKeywordListFromBigTable(String countryCd, List<String> keywordHashList) {
        String sql = " SELECT keyword_name as keywordName, toUInt32(avg_search_volume) as sv from dis_keyword_summary_annual_big ";
        sql += " where country_cd= ? and (";
        for (String hash : keywordHashList) {
            sql += " (keyword_hash_mod=" + hash + "%2000 and keyword_hash = " + hash + ") or ";
        }
        sql = sql.substring(0, sql.length() - 3);
        sql += ") ";
        return queryForMapList(sql, countryCd);
    }

    public List<Map<String, Object>> getSvByKeywordListFromNormalTable(String countryCd, List<String> keywordHashList) {
        String sql = " SELECT keyword_name as keywordName, toUInt32(avg_search_volume) as sv from dis_keyword_summary_annual_normal ";
        sql += " where country_cd= ? and (";
        for (String hash : keywordHashList) {
            sql += " (keyword_hash_mod=" + hash + "%500 and keyword_hash = " + hash + ") or ";
        }
        sql = sql.substring(0, sql.length() - 3);
        sql += ") ";
        return queryForMapList(sql, countryCd);
    }


    public List<String> getCountryCode(Integer engineId, Integer languageId) {
        String sql = " SELECT country_cd from gsc_clickstream.dis_keyword_stream WHERE engine_id= ? and language_id= ? limit 1 ";
        return queryForStringList(sql, engineId, languageId);
    }

    public List<Map<String, Object>> getEngineLanguageCountryMapping() {

        StringBuffer sql = new StringBuffer();
        sql.append(" SELECT ");
        sql.append("     engine_id, ");
        sql.append("     language_id, ");
        sql.append("     country_cd, ");
        sql.append("     tableName ");
        sql.append(" FROM ");
        sql.append(" ( ");
        sql.append("     SELECT ");
        sql.append("         engine_id, ");
        sql.append("         language_id, ");
        sql.append("         country_cd ");
        sql.append("     FROM dis_keyword_stream ");
        sql.append("     GROUP BY ");
        sql.append("         engine_id, ");
        sql.append("         language_id, ");
        sql.append("         country_cd ");
        sql.append(" ) AS B ");
        sql.append(" ALL LEFT JOIN ");
        sql.append(" ( ");
        sql.append("     SELECT ");
        sql.append("         country_cd, ");
        sql.append("         'dis_keyword_summary_annual_big_v2' AS tableName ");
        sql.append("     FROM dis_keyword_summary_annual_big_v2 ");
        sql.append("     GROUP BY country_cd ");
        sql.append("     UNION ALL ");
        sql.append("     SELECT ");
        sql.append("         country_cd, ");
        sql.append("         'dis_keyword_summary_annual_normal_v2' AS tableName ");
        sql.append("     FROM dis_keyword_summary_annual_normal_v2 ");
        sql.append("     WHERE country_cd GLOBAL NOT IN ");
        sql.append("     ( ");
        sql.append("         SELECT country_cd ");
        sql.append("         FROM dis_keyword_summary_annual_big_v2 ");
        sql.append("         GROUP BY country_cd ");
        sql.append("     ) ");
        sql.append("     GROUP BY country_cd ");
        sql.append(" ) AS A USING (country_cd) ");
        return queryForMapList(sql.toString());
    }

    public List<GscClickSteamEntity> getKeywordSearchVolumeList(String countryCode, String tableName, List<String> keywordList) {
        StringBuffer sql = new StringBuffer();
        sql.append(" SELECT keyword_hash as keywordHash, any(avg_search_volume) as avgSearchVolume, ");
        sql.append("     any(svAttrInt.key) as svAttrInt_key, any(svAttrInt.value) as svAttrInt_value from " + tableName);
        sql.append("  where country_cd = ? and keyword_hash in (" + StringUtils.join(keywordList, ", ") + ") group by keyword_hash ");
        return findBySql(sql.toString(), countryCode);
    }
    
    public List<GscClickSteamEntity> getTrueDemandByKeywordList(String countryCode, String tableName, int keywordHashMod, List<String> keywordList) {
        StringBuffer sql = new StringBuffer();
        sql.append("SELECT keyword_hash as keywordHash, any(avg_search_volume) as avgSearchVolume, any(svAttrInt.key) as svAttrInt_key, any(svAttrInt.value) as svAttrInt_value");
        sql.append(" FROM " + tableName).append(" WHERE country_cd=? AND (");
        
        boolean addOrFlg = false;
        for (String keywordName : keywordList) {
        	String kwHash = null;
        	String kwMurmurHash = null;
        	try {
        		kwHash = CityHashUtil.getUrlHashForString(StringEscapeUtils.unescapeHtml(StringEscapeUtils.escapeHtml(URLDecoder.decode(keywordName.toLowerCase(), "UTF-8"))));
        		kwMurmurHash = MurmurHashUtils.getMurmurHash3_64(StringEscapeUtils.unescapeHtml(StringEscapeUtils.escapeHtml(URLDecoder.decode(keywordName, "UTF-8"))));
        	} catch (Exception exp) {
        		exp.printStackTrace();
        	}
        	if (kwHash != null && kwMurmurHash != null) {
        		sql.append(addOrFlg ? " OR " : "").append(" keyword_hash_mod=").append(kwHash).append("%").append(keywordHashMod);
        		sql.append(" and keyword_hash=").append(kwHash).append(" and keyword_murmurhash=").append(kwMurmurHash);
        		if (!addOrFlg) {
        			addOrFlg = true;
        		}
        	}
        }
        sql.append(" ) group by keyword_hash");
        return findBySql(sql.toString(), countryCode);
    }

    public List<GscClickSteamEntity> queryLoadData(String table) {
        StringBuffer sql = new StringBuffer();
        sql.append(" select t2.keyword_name as keyword, ");
        sql.append(" round(avg_search_volume, 0) as trueDemand, ");
        sql.append(" (case when rankcheck_keyword_id>0 then 1 else 0 end) as InRG  ");
        sql.append(" from ");
        sql.append("  (select keyword_name,avg_search_volume from  ");
        sql.append(" gsc_clickstream.dis_keyword_summary_annual_big_v2  ");
        sql.append(" where country_cd='usa') t1  ");
        sql.append(" right join ( ");
        sql.append(" select * from  default.").append(table);
        sql.append(" ) t2 using(keyword_name) ");
        System.out.println(sql.toString());
        return findBySql(sql.toString());
    }

    public List<String> getCountryCode_v2(String countryCd, String table) {
        String sql = "";
        if (table.contains("big")) {
            sql = " SELECT country_cd from gsc_clickstream." + table + " where country_cd =? limit 1 ";
        } else if (table.contains("normal")) {
            sql = " SELECT country_cd from gsc_clickstream." + table + "  where country_cd =? limit 1 ";
        }
        return queryForStringList(sql, countryCd);
    }


    public List<Map<String, Object>> getTrueDemandByKeywordAndCountryCd(Integer mod, String countryCd, List<String> keywordSearhList, String table) {
        //select keyword_name,round(avg_search_volume, 0) trueDemand from gsc_clickstream.{annualTable}
        // where country_cd='{countryCode}' and ( keyword_hash=URLHash(lower({keyword1})) or keyword_hash=URLHash(lower({keyword2}))
        // or keyword_hash=URLHash(lower({keyword3}))...... );
        StringBuffer sql = new StringBuffer();
        sql.append(" select keyword_name as keywordName,round(avg_search_volume, 0) trueDemand ");
        sql.append(" from gsc_clickstream.").append(table);
        sql.append(" where  country_cd= ? ");
        sql.append("  and keyword_hash_mod= " + mod + " ");
        sql.append(" and (");
        int i = 0;
        for (String keyword : keywordSearhList) {
            i++;
            sql.append("  keyword_hash=URLHash(lower('" + FormatUtils.decodeKeyword(keyword) + "'))  ");

            if (i < keywordSearhList.size()) {
                sql.append(" or ");
            }
        }
        sql.append(" ) ");
        System.out.println("********************getTrueDemandByKeywordAndCountryCd *******************");
        System.out.println(sql.toString());
        return this.queryForMapList(sql.toString(), countryCd);
    }

    public String getDomainByUrl(String url) {
        String sql = " SELECT domain('" + url + "') ";
        return queryForString(sql);
    }

    public List<GscClickSteamEntity> getKeywordsByDomainReverse(String countryCd, int hashMod, Set<String> domainReverseList, String device) {
        String sql = " select keyword_name,sum(impressions) as impressions from dis_gsc_clickstream ";
        sql += " where country_cd = '" + countryCd + "' and log_date >= '2023-08-01' and log_date <= '2023-10-31' ";
        sql += " and arrayStringConcat(arrayReverse(splitByString('.', domain(url))), '.') in('" + StringUtils.join(domainReverseList, "','") + "' ";
        sql += " ) ";
        sql += " and keyword_hash_mod = " + hashMod;
//        sql += " and device = '" + device + "' ";
        sql += " group by keyword_name";
        System.out.println("======getKeywordsByDomainReverseSQL:" + sql);
        return findBySql(sql);
    }

    public List queryForListFromClarityDB(String sql) {
        return this.getJdbcTemplate().queryForList(sql);
    }

    public Integer getKeywordsByRootDomainReverseCount(String rootDomainReverse, int trueRank){
        String sql = " select count(distinct keyword_name) kwCnt from monthly_ranking.d_ranking_detail_us ";
        sql += " where root_domain_reverse='" + rootDomainReverse + "' and own_domain_id=0 and engine_id=1 and language_id=1 ";
        sql += " and location_id=0 and true_rank<=" + trueRank;
        System.out.println("===getKeywordsByRootDomainReverseCountSQL:" + sql);
        return queryForInt(sql);
    }


}
