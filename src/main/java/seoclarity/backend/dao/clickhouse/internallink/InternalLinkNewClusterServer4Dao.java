package seoclarity.backend.dao.clickhouse.internallink;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.stereotype.Repository;

import com.google.gson.Gson;

import seoclarity.backend.entity.clickhouse.internallink.InternalLinkClarityDBEntity;
import seoclarity.backend.entity.clickhouse.internallink.ProcessListVO;
import seoclarity.backend.utils.FormatUtils;

/**
 * Created by <PERSON> on 2017/5/11.
 */
@Repository
public class InternalLinkNewClusterServer4Dao extends ClInternalLinkNewClusterBaseJdbcSupportServer4<InternalLinkClarityDBEntity> implements InternalLinkImpl{

    @Override
    public String getTableName() {
        return "distributed_internal_link_view";
    }

    public void insertForBatch(List<InternalLinkClarityDBEntity> insertData) {
        insertForBatch(insertData, getTableName());
    }
    
    public String getTempSummaryTableName(Date date){
		return "local_internal_link_view_v5_" + FormatUtils.formatDate(date, "yyyyMMddHH");
	}
    
	public String createTable(Date date) throws Exception{
		String tableName = getTempSummaryTableName(date);
		String sql = "create table " + tableName + " as local_internal_link_view_v5_template "
				+ "  ENGINE = MergeTree" + 
				" PARTITION BY crawl_request_log_mod" + 
				" PRIMARY KEY (domain_id_i, crawl_request_log_id_i)" + 
				" ORDER BY (domain_id_i, crawl_request_log_id_i, destination_url_murmur_hash, source_url_murmur_hash, anchor_text_hash)" + 
				" SETTINGS index_granularity = 8192 ";
		System.out.println("sql:" + sql);
		this.executeUpdate(sql);
		return tableName;
	}
	
	public List<String> getTableListV2(String tableName) {
		String sql = "show tables like '" + tableName + "' ";
		System.out.println("sql:" + sql);
		return this.queryForStringList(sql);
	}
	
	public static final String[] headers = new String[]{
		"link_nofollow", "domain_id_i", "analyzed_url_s", "source_domain", "canonical_type",
		"canonical_flg", "anchor_text", "destination_domain", "destination_root_domain", "crawl_date_long",
		"source_url", "page_robots_meta_index", "page_robots_meta_follow", "page_robots_meta_archive", "destination_url",
		"title_md5", "popularity", "title", "canonical", "source_root_domain",
		"analyzed_url_flg_s", "title_flg", "crawl_request_log_id_i", "canonical_string", "destination_folder_level_1",
		"destination_folder_level_2", "source_folder_level_1", "source_folder_level_2", "today", "source_url_response_code",
		"sourceurl_anchortext_hash", "destinationurl_anchortext_hash"
	};

    public void insertForBatch(List<InternalLinkClarityDBEntity> insertData, String tableName) {
    	
    	String sql = "INSERT INTO "+ tableName + "(link_nofollow, domain_id_i, analyzed_url_s, source_domain, canonical_type,"
				+ "canonical_flg, anchor_text, destination_domain, destination_root_domain, crawl_date_long,"
				+ "source_url, page_robots_meta_index, page_robots_meta_follow, page_robots_meta_archive, destination_url,"
				+ "title_md5, popularity, title, canonical, source_root_domain,"
				+ "analyzed_url_flg_s, title_flg, crawl_request_log_id_i, canonical_string, destination_folder_level_1,"
				+ "destination_folder_level_2, source_folder_level_1, source_folder_level_2, today, source_url_response_code,"
				+ "sourceurl_anchortext_hash, destinationurl_anchortext_hash) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
    	
        List<Object[]> batch = new ArrayList<Object[]>();
        int i = 0;
        for (InternalLinkClarityDBEntity entity : insertData) {
            Object[] values;
			values = new Object[] {
					entity.isLink_nofollow() ? 1 : 0,
			        entity.getDomain_id_i(),
			        entity.getAnalyzed_url_s(),
			        entity.getSource_domain(),
			        entity.getCanonical_type(),
			        entity.isCanonical_flg() ? 1 : 0,
			        FormatUtils.decoderString(entity.getAnchor_text(), true, insertData.size()),
			        entity.getDestination_domain(),
			        entity.getDestination_root_domain(),
			        entity.getCrawl_date_long(),
			        FormatUtils.decoderString(entity.getSource_url(), true, insertData.size()),
			        entity.getPage_robots_meta_index(),
			        entity.getPage_robots_meta_follow(),
			        entity.getPage_robots_meta_archive(),
			        FormatUtils.decoderString(entity.getDestination_url(), true, insertData.size()),
			        entity.getTitle_md5(),
			        entity.getPopularity(),
			        FormatUtils.decoderString(entity.getTitle(), true, insertData.size()),
			        entity.getCanonical(),
			        entity.getSource_root_domain(),
			        entity.isAnalyzed_url_flg_s() ? 1 : 0,
			        entity.isTitle_flg() ? 1 : 0,
			        entity.getCrawl_request_log_id_i(),
			        entity.getCanonical_string(),
			        entity.getDestination_folder_level_1(),
			        entity.getDestination_folder_level_2(),
			        entity.getSource_folder_level_1(),
			        entity.getSource_folder_level_2(),
			        entity.getToday(),			
			        entity.getSource_url_response_code(),			
			        (entity.getSourceurl_anchortext_hash() == null ? 0 : entity.getSourceurl_anchortext_hash()),			
			        (entity.getDestinationurl_anchortext_hash() == null ? 0 : entity.getDestinationurl_anchortext_hash())
			};
			
			if(i++ < 10) {
                System.out.println(new Gson().toJson(values));
            }

            batch.add(values);

            
        }
        executeBatch(sql, batch);
    }

    
    
	public List<String> getTableList(Date date) {
		String tableName = getTempSummaryTableName(date);
		String sql = "show tables like '" + tableName + "' ";
		System.out.println("sql:" + sql);
		return this.queryForStringList(sql);
	}
	
    
	public Integer getTotalCount(Date date) {
		String tableName = getTempSummaryTableName(date);
		String sql = " select count() from " + tableName + "";
		System.out.println("sql:" + sql);
		return this.queryForInteger(sql);
	}
	
	
	private static final String DIS_TABLE_NAME = "dis_internal_link_sampled_view_final";
	
	
	public List<ProcessListVO> getSummaryList() {
		String sql = " select crawl_request_log_id_i, domain_id_i, cnt from (select crawl_request_log_id_i, domain_id_i,count() as cnt from distributed_internal_link_view group by crawl_request_log_id_i, domain_id_i) " + 
				" where crawl_request_log_id_i global not in ( select crawlRequestLogId from " + DIS_TABLE_NAME + " group by crawlRequestLogId  )";
		System.out.println("sql:" + sql);
		
		return this.findBySql(sql, ProcessListVO.class);
	}
	
	public ProcessListVO getCrawlInfoById(Integer crawlRequestId, Integer ownDomainId) {
		String sql = " select crawl_request_log_id_i, domain_id_i, count() as cnt from distributed_internal_link_view  " + 
				" where crawl_request_log_mod = " + (crawlRequestId % 1000) + " AND crawl_request_log_id_i = " + crawlRequestId + 
				" AND domain_id_i = " + ownDomainId + " group by crawl_request_log_id_i, domain_id_i ";
		System.out.println("sql:" + sql);
		
		List<ProcessListVO> resultList = this.findBySql(sql, ProcessListVO.class);
		if (resultList != null && resultList.size() > 0) {
			return resultList.get(0);
		}
		
		return null;
	}
	
	private static final String DIS_TABLE_NAME_V2 = "actonia_internal_link.dis_internal_link_sampled_view_final";
	public List<ProcessListVO> getSummaryListV2(Integer cutOverCrawlId, List<Integer> ignoreCrawlIdList, Long cutOverDateLong) {
		String sql = " select crawl_request_log_id_i, domain_id_i, cnt, today from (select crawl_request_log_id_i, domain_id_i, round(count()/100, 0) as cnt, max(today) as today from actonia_internal_link.distributed_internal_link_view"
				+ " where crawl_request_log_id_i > " + cutOverCrawlId + " AND crawl_request_log_id_i not in (" + getIntegerListQueryParam(ignoreCrawlIdList) + ") "
				+ " group by crawl_request_log_id_i, domain_id_i  HAVING min(crawl_date_long) >= " + cutOverDateLong + " ) " + 
				" where  crawl_request_log_id_i global not in ( select crawlRequestLogId from " + DIS_TABLE_NAME_V2 + " group by crawlRequestLogId  )";
		System.out.println("sql:" + sql);
		
		return this.findBySql(sql, ProcessListVO.class);
	}
	
	public List<ProcessListVO> getBackprocessListV2(Integer crawlRequestLogId) {
		String sql = " select crawl_request_log_id_i, domain_id_i, round(count()/100, 0) as cnt, max(today) as today from actonia_internal_link.distributed_internal_link_view"
				+ " where crawl_request_log_id_i = " + crawlRequestLogId + " group by crawl_request_log_id_i, domain_id_i";
		System.out.println("sql:" + sql);
		
		return this.findBySql(sql, ProcessListVO.class);
	}
	
	
	public ProcessListVO getSummaryVO(Integer crawlRequestId, Integer ownDomainId) {
		String sql = " select crawl_request_log_id_i, domain_id_i, count(1) as cnt, max(today) as today from distributed_internal_link_view where crawl_request_log_mod = " + (crawlRequestId % 1000) + " AND crawl_request_log_id_i = " 
				+ crawlRequestId + " AND domain_id_i = " + ownDomainId + "  group by crawl_request_log_id_i, domain_id_i";
		System.out.println("sql:" + sql);
		List<ProcessListVO> result = this.findBySql(sql, ProcessListVO.class);
		return (result != null && result.size() > 0) ? result.get(0) : null;
	}
	
	private final static int MAX_RESULT_COUNT = 1000000;
	
	
	public String dropTable(String tableName) throws Exception{
		String sql = "drop table " + tableName;
		System.out.println("sql:" + sql);
		this.executeUpdate(sql);
		return tableName;
	}
	
	public String renameTable(String tableName, String oldTableName) throws Exception{
		String sql = "rename table " + oldTableName + " to " + tableName;
		System.out.println("sql:" + sql);
		this.executeUpdate(sql);
		return tableName;
	}
	
	public String getTableList(String tableName) {
		String sql = "show tables like '" + tableName + "' ";
		System.out.println("sql:" + sql);
		return this.queryForString(sql);
	}
	
	public String createInsertDistributeTable(String tableName, String templateTableName, String localTableName) throws Exception{
		String sql = "create table " + tableName + " as " + templateTableName
				+ " ENGINE = Distributed('cdb_shard', 'actonia_internal_link', '" + localTableName + "', rand()) ";
		System.out.println("sql:" + sql);
		this.executeUpdate(sql);
		return tableName;
	}
	
	public String createLocalTable(String tableName, String templateTableName) throws Exception{
		String sql = "create table " + tableName + " as " + templateTableName
				+ " ENGINE = MergeTree PARTITION BY (crawlRequestLogMod) ORDER BY (crawlRequestLogId, domainId, sourceUrlHash) SAMPLE BY sourceUrlHash SETTINGS index_granularity = 8192 ";
		System.out.println("sql:" + sql);
		this.executeUpdate(sql);
		return tableName;
	}
	
    
    public void summaryTopLevelByDestationUrl(int ownDomainId, int crawlerId) {
    	StringBuffer sql = new StringBuffer();
    	
    	sql.append("  insert into actonia_internal_link.distributed_internal_link_merge(today, domainId, crawlRequestLogId, ");
    	sql.append("              crawlDateLong, url, countInbound, countAnchorText ) ");
    	sql.append("  select now(), any(domain_id_i) as domainId, any(crawl_request_log_id_i) as crawlRequestLogId,  ");
    	sql.append("         any(crawl_date_long) as crawlDateLong, any(destination_url) as destUrl, count(distinct source_url_hash) as countInbound, ");
    	sql.append("         count(distinct anchor_text_hash) as countAnchorText  ");
    	sql.append("          ");
    	sql.append("  from actonia_internal_link.local_internal_link_view_v5 ");
    	sql.append("  WHERE domain_id_i = ? and crawl_request_log_id_i = ? ");
    	sql.append("  group by destination_url_hash  ");
    	sql.append("  SETTINGS max_threads = 1, max_bytes_before_external_group_by = 22000000000 ");
    	
    	System.out.println("SQL: " + sql.toString());
    	this.executeUpdate(sql.toString(), ownDomainId, crawlerId);
    }
    
	public Long getUniqueDocumentCountByCrawlRequestIdAndDomainId(int ownDomainId, int crawlerId) {
		String sql = " select count()  from " + getTableName() + " where crawl_request_log_id_i = ? and domain_id_i = ?";
		System.out.println("sql:" + sql);
		return this.queryForLong(sql, crawlerId, ownDomainId);
	}

	@Override
	public List<ProcessListVO> getCountByPopularity(int ownDomainId, int crawlerId) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public List<Integer> getPopularityList(int ownDomainId, int crawlerId) {
		// TODO Auto-generated method stub
		return null;
	}
	
	public void summary(int ownDomainId, int crawlerId, String tableName) {
    	
        StringBuffer sql = new StringBuffer();
        sql.append(" insert into actonia_internal_link." + tableName + "(domainId, crawlRequestLogId, sourceLinkNofollow, destLinkNofollow, sourceUrl, ");
        sql.append(" sourceDomain, sourceRespCode, destUrl, destDomain, destRespCode, outboundCnt, inboundLinkCnt, anchorText, popularity, sourceUrlCanonicalFlg, ");
        sql.append(" sourceUrlPageRobotsMetaIndex, sourceUrlPageRobotsMetaFollow, sourceUrlPageRobotsMetaArchive, destUrlCanonicalFlg, destUrlPageRobotsMetaIndex, ");
        sql.append(" destUrlPageRobotsMetaFollow, destUrlPageRobotsMetaArchive, destUrlTitle, sourceFolderLevel1, sourceFolderLevel2, descFolderLevel1, ");
//        sql.append(" descFolderLevel2, outboundLinkArray.Url, outboundLinkArray.AnchorText )");
        sql.append(" descFolderLevel2 )");
        sql.append(" SELECT");
        sql.append("     domainId,");
        sql.append("     requestId AS crawlRequestLogId,");
        sql.append("     sourceLinkNofollow,");
        sql.append("     destLinkNofollow,");
        sql.append("     sourceUrl,");
        sql.append("     sourceDomain,");
        sql.append("     sourceRespCode,");
        sql.append("     url AS descUrl,");
        sql.append("     destDomain,");
        sql.append("     destRespCode,");
        sql.append(" 	outboundCnt - if(ifNull(emptyOutBoundUrl, 0) > 0, 1, 0) AS outBoundCntWithEmpty,");
        sql.append(" 	inboundLinkCnt,");
        sql.append("     anchorText,");
        sql.append("     depth AS popularity,");
        sql.append("     sourceUrlCanonicalFlg,");
        sql.append("     sourceUrlPageRobotsMetaIndex,");
        sql.append("     sourceUrlPageRobotsMetaFollow,");
        sql.append("     sourceUrlPageRobotsMetaArchive,");
        sql.append("     destUrlCanonicalFlg,");
        sql.append("     destUrlPageRobotsMetaIndex,");
        sql.append("     destUrlPageRobotsMetaFollow,");
        sql.append("     destUrlPageRobotsMetaArchive,");
        sql.append("     destUrlTitle,");
        sql.append("     sourceFolderLevel1,");
        sql.append("     sourceFolderLevel2,");
        sql.append("     descFolderLevel1,");
        sql.append("     descFolderLevel2");
//        sql.append(" 	outboundUrl,");
//        sql.append(" 	outboundAnchorText");
        sql.append(" FROM");
        sql.append(" (");
        sql.append("     SELECT");
        sql.append("         destination_url_murmur_hash AS urlHash,");
        sql.append("         any(lower(trimBoth(anchor_text))) AS anchorText,");
        sql.append("         any(source_domain) AS sourceDomain,");
        sql.append("         any(link_nofollow) AS sourceLinkNofollow,");
        sql.append("         any(source_url) AS sourceUrl,");
        sql.append("         any(source_url_response_code) AS sourceRespCode,");
        sql.append("         any(source_folder_level_1) AS sourceFolderLevel1,");
        sql.append("         any(source_folder_level_2) AS sourceFolderLevel2,");
        sql.append("         any(canonical_flg) AS sourceUrlCanonicalFlg,");
        sql.append("         any(page_robots_meta_index) AS sourceUrlPageRobotsMetaIndex,");
        sql.append("         any(page_robots_meta_follow) AS sourceUrlPageRobotsMetaFollow,");
        sql.append("         any(page_robots_meta_archive) AS sourceUrlPageRobotsMetaArchive,");
        sql.append("         any(title) AS sourceUrlTitle");
        sql.append("     FROM distributed_internal_link_view");
        sql.append("     WHERE (crawl_request_log_mod = ?) AND (crawl_request_log_id_i = ?) AND (domain_id_i = ?) ");
        sql.append("     GROUP BY");
        sql.append("         destination_url_murmur_hash, source_url_murmur_hash, anchor_text_hash ");
        sql.append("     SETTINGS max_bytes_before_external_group_by = 10000000000, distributed_aggregation_memory_efficient = 1 ");
        sql.append(" ) d");
        sql.append(" ALL RIGHT JOIN");
        sql.append(" (");
        sql.append("     SELECT");
        sql.append("         depth,");
        sql.append("         today,");
        sql.append("         requestId,");
        sql.append("         crawl_date_long,");
        sql.append("         domainId,");
        sql.append("         urlHash,");
        sql.append("         url,");
        sql.append("         destLinkNofollow,");
        sql.append("         outboundCnt,");
        sql.append("         emptyOutBoundUrl,");
        sql.append("         destDomain,");
        sql.append("         destUrlCanonicalFlg,");
        sql.append("         destRespCode,");
        sql.append("         destUrlPageRobotsMetaIndex,");
        sql.append("         destUrlPageRobotsMetaFollow,");
        sql.append("         destUrlPageRobotsMetaArchive,");
        sql.append("         destUrlTitle,");
//        sql.append(" 		outboundUrl,");
//        sql.append(" 		outboundAnchorText,");
        sql.append("         descFolderLevel1,");
        sql.append("         inboundLinkCnt,");
        sql.append("         descFolderLevel2");
        sql.append("     FROM");
        sql.append("     (");
        sql.append("         SELECT");
        sql.append("             any(today) AS today,");
        sql.append("             any(crawl_request_log_id_i) AS requestId,");
        sql.append("             any(crawl_date_long) AS crawl_date_long,");
        sql.append("             any(domain_id_i) AS domainId,");
        sql.append("             source_url_murmur_hash AS urlHash,");
        sql.append("             min(popularity) AS depth,");
        sql.append("             any(source_url) AS url,");
        sql.append("             any(link_nofollow) AS destLinkNofollow,");
        sql.append("             countDistinct(destination_url_murmur_hash) AS outboundCnt,");
        sql.append("             countIf((trimBoth(destination_url) = '') OR (destination_url = NULL)) AS emptyOutBoundUrl,");
//        sql.append("             groupArray(destination_url) as outboundUrl, groupArray(anchor_text) outboundAnchorText,");
        sql.append("             any(source_domain) AS destDomain,");
        sql.append("             any(canonical_flg) AS destUrlCanonicalFlg,");
        sql.append("             any(source_url_response_code) AS destRespCode,");
        sql.append("             any(page_robots_meta_index) AS destUrlPageRobotsMetaIndex,");
        sql.append("             any(page_robots_meta_follow) AS destUrlPageRobotsMetaFollow,");
        sql.append("             any(page_robots_meta_archive) AS destUrlPageRobotsMetaArchive,");
        sql.append("             any(title) AS destUrlTitle,");
        sql.append("             any(source_folder_level_1) AS descFolderLevel1,");
        sql.append("             any(source_folder_level_2) AS descFolderLevel2");
        sql.append("         FROM distributed_internal_link_view");
        sql.append("         WHERE (crawl_request_log_mod = ?) AND (crawl_request_log_id_i = ?) AND (domain_id_i = ?) ");
        sql.append("         GROUP BY source_url_murmur_hash");
        sql.append("         SETTINGS max_bytes_before_external_group_by = 10000000000, distributed_aggregation_memory_efficient = 1");
        sql.append("     ) c");
        sql.append("     ANY LEFT JOIN");
        sql.append("     (");
        sql.append("         SELECT");
        sql.append("             destination_url_murmur_hash AS urlHash,");
        sql.append("             countDistinct(source_url_murmur_hash) - if(any(source_url) = '', 1, 0) AS inboundLinkCnt");
        sql.append("         FROM distributed_internal_link_view");
        sql.append("         WHERE (crawl_request_log_mod = ?) AND (crawl_request_log_id_i = ?) AND (domain_id_i = ?) ");
        sql.append("         GROUP BY destination_url_murmur_hash");
        sql.append("         SETTINGS distributed_aggregation_memory_efficient = 1, max_bytes_before_external_group_by = 10000000000");
        sql.append("     ) a USING (urlHash)");
        sql.append(" ) b USING (urlHash) ");
        sql.append(" SETTINGS distributed_aggregation_memory_efficient = 1, max_bytes_before_external_group_by = 10000000000 ");
        
        System.out.println("summary SQL : " + sql);
        System.out.println("param : " + crawlerId);
        System.out.println("param : " + ownDomainId);
        System.out.println("param : " + crawlerId);
        System.out.println("param : " + ownDomainId);
        System.out.println("param : " + crawlerId);
        System.out.println("param : " + ownDomainId);
        
        
        this.executeUpdate(sql.toString(), 
        	crawlerId % 1000, crawlerId, ownDomainId,
        	crawlerId % 1000, crawlerId, ownDomainId,
        	crawlerId % 1000, crawlerId, ownDomainId
		);
    }
	
	public void summaryByHash(int ownDomainId, int crawlerId, String tableName, int pageSize, int hash) {
    	
        StringBuffer sql = new StringBuffer();
        
        sql.append(" insert into actonia_internal_link." + tableName + "(domainId, crawlRequestLogId, sourceLinkNofollow, destLinkNofollow, sourceUrl, ");
        sql.append(" sourceDomain, sourceRespCode, destUrl, destDomain, destRespCode, outboundCnt, inboundLinkCnt, anchorText, popularity, sourceUrlCanonicalFlg, ");
        sql.append(" sourceUrlPageRobotsMetaIndex, sourceUrlPageRobotsMetaFollow, sourceUrlPageRobotsMetaArchive, destUrlCanonicalFlg, destUrlPageRobotsMetaIndex, ");
        sql.append(" destUrlPageRobotsMetaFollow, destUrlPageRobotsMetaArchive, destUrlTitle, sourceFolderLevel1, sourceFolderLevel2, descFolderLevel1, ");
//        sql.append(" descFolderLevel2, outboundLinkArray.Url, outboundLinkArray.AnchorText )");
        sql.append(" descFolderLevel2)");
        sql.append(" SELECT");
        sql.append("     domainId,");
        sql.append("     requestId AS crawlRequestLogId,");
        sql.append("     sourceLinkNofollow,");
        sql.append("     destLinkNofollow,");
        sql.append("     sourceUrl,");
        sql.append("     sourceDomain,");
        sql.append("     sourceRespCode,");
        sql.append("     url AS descUrl,");
        sql.append("     destDomain,");
        sql.append("     destRespCode,");
        sql.append(" 	outboundCnt - if(ifNull(emptyOutBoundUrl, 0) > 0, 1, 0) AS outBoundCntWithEmpty,");
        sql.append(" 	inboundLinkCnt,");
        sql.append("     anchorText,");
        sql.append("     depth AS popularity,");
        sql.append("     sourceUrlCanonicalFlg,");
        sql.append("     sourceUrlPageRobotsMetaIndex,");
        sql.append("     sourceUrlPageRobotsMetaFollow,");
        sql.append("     sourceUrlPageRobotsMetaArchive,");
        sql.append("     destUrlCanonicalFlg,");
        sql.append("     destUrlPageRobotsMetaIndex,");
        sql.append("     destUrlPageRobotsMetaFollow,");
        sql.append("     destUrlPageRobotsMetaArchive,");
        sql.append("     destUrlTitle,");
        sql.append("     sourceFolderLevel1,");
        sql.append("     sourceFolderLevel2,");
        sql.append("     descFolderLevel1,");
        sql.append("     descFolderLevel2");
//        sql.append(" 	outboundUrl,");
//        sql.append(" 	outboundAnchorText");
        sql.append(" FROM");
        sql.append(" (");
        sql.append("     SELECT");
        sql.append("         destination_url_murmur_hash AS urlHash,");
        sql.append("         any(lower(trimBoth(anchor_text))) AS anchorText,");
        sql.append("         any(source_domain) AS sourceDomain,");
        sql.append("         any(link_nofollow) AS sourceLinkNofollow,");
        sql.append("         any(source_url) AS sourceUrl,");
        sql.append("         any(source_url_response_code) AS sourceRespCode,");
        sql.append("         any(source_folder_level_1) AS sourceFolderLevel1,");
        sql.append("         any(source_folder_level_2) AS sourceFolderLevel2,");
        sql.append("         any(canonical_flg) AS sourceUrlCanonicalFlg,");
        sql.append("         any(page_robots_meta_index) AS sourceUrlPageRobotsMetaIndex,");
        sql.append("         any(page_robots_meta_follow) AS sourceUrlPageRobotsMetaFollow,");
        sql.append("         any(page_robots_meta_archive) AS sourceUrlPageRobotsMetaArchive,");
        sql.append("         any(title) AS sourceUrlTitle");
        sql.append("     FROM distributed_internal_link_view");
        sql.append("     WHERE (crawl_request_log_mod = ?) AND (crawl_request_log_id_i = ?) AND (domain_id_i = ?) and destination_url_murmur_hash % " + pageSize + " = " + hash);
        sql.append("     GROUP BY");
        sql.append("         destination_url_murmur_hash, source_url_murmur_hash, anchor_text_hash ");
        sql.append("     SETTINGS max_bytes_before_external_group_by = 10000000000, distributed_aggregation_memory_efficient = 1 ");
        sql.append(" ) d");
        sql.append(" ALL RIGHT JOIN");
        sql.append(" (");
        sql.append("     SELECT");
        sql.append("         depth,");
        sql.append("         today,");
        sql.append("         requestId,");
        sql.append("         crawl_date_long,");
        sql.append("         domainId,");
        sql.append("         urlHash,");
        sql.append("         url,");
        sql.append("         destLinkNofollow,");
        sql.append("         outboundCnt,");
        sql.append("         emptyOutBoundUrl,");
        sql.append("         destDomain,");
        sql.append("         destUrlCanonicalFlg,");
        sql.append("         destRespCode,");
        sql.append("         destUrlPageRobotsMetaIndex,");
        sql.append("         destUrlPageRobotsMetaFollow,");
        sql.append("         destUrlPageRobotsMetaArchive,");
        sql.append("         destUrlTitle,");
//        sql.append(" 		outboundUrl,");
//        sql.append(" 		outboundAnchorText,");
        sql.append("         descFolderLevel1,");
        sql.append("         inboundLinkCnt,");
        sql.append("         descFolderLevel2");
        sql.append("     FROM");
        sql.append("     (");
        sql.append("         SELECT");
        sql.append("             any(today) AS today,");
        sql.append("             any(crawl_request_log_id_i) AS requestId,");
        sql.append("             any(crawl_date_long) AS crawl_date_long,");
        sql.append("             any(domain_id_i) AS domainId,");
        sql.append("             source_url_murmur_hash AS urlHash,");
        sql.append("             min(popularity) AS depth,");
        sql.append("             any(source_url) AS url,");
        sql.append("             any(link_nofollow) AS destLinkNofollow,");
        sql.append("             countDistinct(destination_url_murmur_hash) AS outboundCnt,");
        sql.append("             countIf((trimBoth(destination_url) = '') OR (destination_url = NULL)) AS emptyOutBoundUrl,");
//        sql.append("             groupArray(destination_url) as outboundUrl, groupArray(anchor_text) outboundAnchorText,");
        sql.append("             any(source_domain) AS destDomain,");
        sql.append("             any(canonical_flg) AS destUrlCanonicalFlg,");
        sql.append("             any(source_url_response_code) AS destRespCode,");
        sql.append("             any(page_robots_meta_index) AS destUrlPageRobotsMetaIndex,");
        sql.append("             any(page_robots_meta_follow) AS destUrlPageRobotsMetaFollow,");
        sql.append("             any(page_robots_meta_archive) AS destUrlPageRobotsMetaArchive,");
        sql.append("             any(title) AS destUrlTitle,");
        sql.append("             any(source_folder_level_1) AS descFolderLevel1,");
        sql.append("             any(source_folder_level_2) AS descFolderLevel2");
        sql.append("         FROM distributed_internal_link_view");
        sql.append("         WHERE (crawl_request_log_mod = ?) AND (crawl_request_log_id_i = ?) AND (domain_id_i = ?) and source_url_murmur_hash % " + pageSize + " = " + hash);
        sql.append("         GROUP BY source_url_murmur_hash");
        sql.append("         SETTINGS max_bytes_before_external_group_by = 10000000000, distributed_aggregation_memory_efficient = 1");
        sql.append("     ) c");
        sql.append("     ANY LEFT JOIN");
        sql.append("     (");
        sql.append("         SELECT");
        sql.append("             destination_url_murmur_hash AS urlHash,");
        sql.append("             countDistinct(source_url_murmur_hash) - if(any(source_url) = '', 1, 0) AS inboundLinkCnt");
        sql.append("         FROM distributed_internal_link_view");
        sql.append("         WHERE (crawl_request_log_mod = ?) AND (crawl_request_log_id_i = ?) AND (domain_id_i = ?) and destination_url_murmur_hash % " + pageSize + " = " + hash);
        sql.append("         GROUP BY destination_url_murmur_hash");
        sql.append("         SETTINGS distributed_aggregation_memory_efficient = 1, max_bytes_before_external_group_by = 10000000000");
        sql.append("     ) a USING (urlHash)");
        sql.append(" ) b USING (urlHash) ");
        sql.append(" SETTINGS distributed_aggregation_memory_efficient = 1, max_bytes_before_external_group_by = 10000000000 ");
        
        
        
        System.out.println("summary SQL : " + sql);
        System.out.println("param : " + crawlerId);
        System.out.println("param : " + ownDomainId);
        System.out.println("param : " + crawlerId);
        System.out.println("param : " + ownDomainId);
        System.out.println("param : " + crawlerId);
        System.out.println("param : " + ownDomainId);
        
        
        this.executeUpdate(sql.toString(), 
    		crawlerId % 1000, crawlerId, ownDomainId,
    		crawlerId % 1000, crawlerId, ownDomainId,
    		crawlerId % 1000, crawlerId, ownDomainId
		);
    }
	
	
	public void summaryBigDocDetail(int ownDomainId, int crawlerId, String tableName, Integer partNum, Integer num) {
		
		StringBuffer sql = new StringBuffer();
		sql.append(" insert into actonia_internal_link.dis_summary_detail(domainId, crawlRequestLogId, destUrl, anchorText, sourceUrl, sourceDomain, ");
		sql.append(" sourceLinkNofollow, destDomain, sourceRespCode, sourceFolderLevel1,sourceFolderLevel2, descFolderLevel1, descFolderLevel2,  ");
		sql.append(" sourceUrlCanonicalFlg, sourceUrlPageRobotsMetaIndex, sourceUrlPageRobotsMetaFollow, sourceUrlPageRobotsMetaArchive, sourceUrlTitle) ");
		sql.append(" SELECT  ");
		sql.append("     any(domain_id_i), any(crawl_request_log_id_i), ");
		sql.append("         any(destination_url), ");
		sql.append("         any(lower(trimBoth(anchor_text))) AS anchorText, ");
		sql.append("         any(source_url) AS sourceUrl,         ");
		sql.append(" 		any(source_domain) AS sourceDomain, ");
		sql.append("         any(link_nofollow) AS sourceLinkNofollow, ");
		sql.append(" 		any(destination_domain) as destDomain, ");
		sql.append("         any(source_url_response_code) AS sourceRespCode, ");
		sql.append("         any(source_folder_level_1) AS sourceFolderLevel1, ");
		sql.append("         any(source_folder_level_2) AS sourceFolderLevel2, ");
		sql.append(" 		any(destination_folder_level_1) AS descFolderLevel1, ");
		sql.append("         any(destination_folder_level_2) AS descFolderLevel2, ");
		sql.append("         any(canonical_flg) AS sourceUrlCanonicalFlg, ");
		sql.append("         any(page_robots_meta_index) AS sourceUrlPageRobotsMetaIndex, ");
		sql.append("         any(page_robots_meta_follow) AS sourceUrlPageRobotsMetaFollow, ");
		sql.append("         any(page_robots_meta_archive) AS sourceUrlPageRobotsMetaArchive, ");
		sql.append("         any(title) AS sourceUrlTitle ");
		sql.append("     FROM distributed_internal_link_view ");
		sql.append("     WHERE (crawl_request_log_id_i = ?) AND (domain_id_i = ?) AND (crawl_request_log_mod = ?) AND ((destination_url_murmur_hash % " + partNum + ") = " + num +") ");
		sql.append("     GROUP BY ");
		sql.append("         destination_url_murmur_hash, ");
		sql.append("         anchor_text_hash, ");
		sql.append("         source_url_murmur_hash ");
		sql.append(" 		SETTINGS max_bytes_before_external_group_by = 10000000000, distributed_aggregation_memory_efficient = 1 ");
	
		this.executeUpdate(sql.toString(), 
    		crawlerId, ownDomainId, crawlerId % 1000
		);
	}
    
	
	public void summaryBigDocOutBoundDetail(int ownDomainId, int crawlerId, String tableName, Integer partNum, Integer num) {
		
		StringBuffer sql = new StringBuffer();
		sql.append("insert into actonia_internal_link.dis_summary_outbound_detail(domainId, crawlRequestLogId, popularity, destUrl, destLinkNofollow, outboundCnt, inboundLinkCnt, destUrlCanonicalFlg, ");
//		sql.append("  destRespCode, destUrlPageRobotsMetaIndex, destUrlPageRobotsMetaFollow, destUrlPageRobotsMetaArchive, destUrlTitle, outboundLinkArray.Url, outboundLinkArray.AnchorText) ");
		sql.append("  destRespCode, destUrlPageRobotsMetaIndex, destUrlPageRobotsMetaFollow, destUrlPageRobotsMetaArchive, destUrlTitle) ");
		sql.append("SELECT ");
		sql.append("         domainId, ");
		sql.append("        requestId, ");
		sql.append("		depth, ");
		sql.append("        url, ");
		sql.append("        destLinkNofollow, ");
		sql.append("        outboundCnt - if(ifNull(emptyOutBoundUrl, 0) > 0, 1, 0) AS outBoundCntWithEmpty, ");
		sql.append("        inboundLinkCnt, ");
		sql.append("        destUrlCanonicalFlg, ");
		sql.append("        destRespCode, ");
		sql.append("        destUrlPageRobotsMetaIndex, ");
		sql.append("        destUrlPageRobotsMetaFollow, ");
		sql.append("        destUrlPageRobotsMetaArchive, ");
		sql.append("        destUrlTitle ");
//		sql.append("        outboundUrl, ");
//		sql.append("        outboundAnchorText ");
		sql.append("    FROM ");
		sql.append("    ( ");
		sql.append("        SELECT ");
		sql.append("            any(today) AS today, ");
		sql.append("            any(crawl_request_log_id_i) AS requestId, ");
		sql.append("            any(crawl_date_long) AS crawl_date_long, ");
		sql.append("            any(domain_id_i) AS domainId, ");
		sql.append("            source_url_murmur_hash AS urlHash, ");
		sql.append("            min(popularity) AS depth, ");
		sql.append("            any(source_url) AS url, ");
		sql.append("            any(link_nofollow) AS destLinkNofollow, ");
		sql.append("            countDistinct(destination_url_murmur_hash) AS outboundCnt, ");
		sql.append("            countIf((trimBoth(destination_url) = '') OR (destination_url = NULL)) AS emptyOutBoundUrl, ");
//		sql.append("            groupArray(destination_url) AS outboundUrl, ");
//		sql.append("            groupArray(anchor_text) AS outboundAnchorText, ");
		sql.append("            any(source_domain) AS destDomain, ");
		sql.append("            any(canonical_flg) AS destUrlCanonicalFlg, ");
		sql.append("            any(source_url_response_code) AS destRespCode, ");
		sql.append("            any(page_robots_meta_index) AS destUrlPageRobotsMetaIndex, ");
		sql.append("            any(page_robots_meta_follow) AS destUrlPageRobotsMetaFollow, ");
		sql.append("            any(page_robots_meta_archive) AS destUrlPageRobotsMetaArchive, ");
		sql.append("            any(title) AS destUrlTitle, ");
		sql.append("            any(source_folder_level_1) AS descFolderLevel1, ");
		sql.append("            any(source_folder_level_2) AS descFolderLevel2 ");
		sql.append("        FROM distributed_internal_link_view ");
		sql.append("        WHERE (crawl_request_log_id_i = ?) AND (domain_id_i = ?) AND (crawl_request_log_mod = ?) AND (source_url_murmur_hash % " + partNum + ") = " + num);
		sql.append("        GROUP BY source_url_murmur_hash ");
		sql.append("		SETTINGS max_bytes_before_external_group_by = 10000000000, distributed_aggregation_memory_efficient = 1 ");
		sql.append("    ) AS c ");
		sql.append("    ANY LEFT JOIN ");
		sql.append("    ( ");
		sql.append("        SELECT ");
		sql.append("            destination_url_murmur_hash AS urlHash, ");
		sql.append("            countDistinct(URLHash(source_url)) - if(any(source_url) = '', 1, 0) AS inboundLinkCnt ");
		sql.append("        FROM distributed_internal_link_view ");
		sql.append("        WHERE (crawl_request_log_id_i = ?) AND (domain_id_i = ?) AND (crawl_request_log_mod = ?) AND (destination_url_murmur_hash % " + partNum + ") = " + num);
		sql.append("        GROUP BY destination_url_murmur_hash ");
		sql.append("		SETTINGS max_bytes_before_external_group_by = 10000000000, distributed_aggregation_memory_efficient = 1 ");
		sql.append("    ) AS a USING (urlHash) " );
		this.executeUpdate(sql.toString(), 
    		crawlerId, ownDomainId, crawlerId % 1000,
    		crawlerId, ownDomainId, crawlerId % 1000
		);
	}
	
	public void summaryBigDocIntoFinalTable(int ownDomainId, int crawlerId, String tableName, Integer partNum, Integer num) {
		
		StringBuffer sql = new StringBuffer();
		sql.append(" INSERT INTO actonia_internal_link.dis_internal_link_sampled_view_final (domainId, crawlRequestLogId, sourceLinkNofollow, destLinkNofollow, ");
//		sql.append(" sourceUrl, sourceDomain, sourceRespCode, destUrl, destDomain, destRespCode, outboundCnt, inboundLinkCnt, anchorText, popularity, sourceUrlCanonicalFlg, sourceUrlPageRobotsMetaIndex, sourceUrlPageRobotsMetaFollow, sourceUrlPageRobotsMetaArchive, destUrlCanonicalFlg, destUrlPageRobotsMetaIndex, destUrlPageRobotsMetaFollow, destUrlPageRobotsMetaArchive, destUrlTitle, sourceFolderLevel1, sourceFolderLevel2, descFolderLevel1, descFolderLevel2, outboundLinkArray.Url, outboundLinkArray.AnchorText)  ");
		sql.append(" sourceUrl, sourceDomain, sourceRespCode, destUrl, destDomain, destRespCode, outboundCnt, inboundLinkCnt, anchorText, popularity, sourceUrlCanonicalFlg, sourceUrlPageRobotsMetaIndex, sourceUrlPageRobotsMetaFollow, sourceUrlPageRobotsMetaArchive, destUrlCanonicalFlg, destUrlPageRobotsMetaIndex, destUrlPageRobotsMetaFollow, destUrlPageRobotsMetaArchive, destUrlTitle, sourceFolderLevel1, sourceFolderLevel2, descFolderLevel1, descFolderLevel2)  ");
		sql.append(" SELECT ");
//		sql.append("    domainId, crawlRequestLogId, if(sourceLinkNofollow > 0, 0, 1) as sourceLinkNofollow, destLinkNofollow, if(isNull(sourceUrl), '', sourceUrl), if(isNull(sourceDomain), '', sourceDomain), if(isNull(sourceUrl), 0, sourceRespCode), destUrl, if(isNull(sourceDomain), '', destDomain), destRespCode, outboundCnt, inboundLinkCnt, if(isNull(anchorText), '', anchorText), popularity, if(isNull(sourceUrl), 0, sourceUrlCanonicalFlg), if(isNull(sourceUrl), '', sourceUrlPageRobotsMetaIndex), if(isNull(sourceUrl), '', sourceUrlPageRobotsMetaFollow), if(isNull(sourceUrl), '', sourceUrlPageRobotsMetaArchive), destUrlCanonicalFlg, destUrlPageRobotsMetaIndex, destUrlPageRobotsMetaFollow, destUrlPageRobotsMetaArchive, destUrlTitle, if(isNull(sourceUrl), '', sourceFolderLevel1), if(isNull(sourceUrl), '', sourceFolderLevel2), if(isNull(descFolderLevel1), '', descFolderLevel1), if(isNull(descFolderLevel2), '', descFolderLevel2), outboundLinkArray.Url, outboundLinkArray.AnchorText ");
		sql.append("    domainId, crawlRequestLogId, if(sourceLinkNofollow > 0, 1, 0) as sourceLinkNofollow, destLinkNofollow, if(isNull(sourceUrl), '', sourceUrl), if(isNull(sourceDomain), '', sourceDomain), if(isNull(sourceUrl), 0, sourceRespCode), destUrl, if(isNull(sourceDomain), '', destDomain), destRespCode, outboundCnt, inboundLinkCnt, if(isNull(anchorText), '', anchorText), popularity, if(isNull(sourceUrl), 0, sourceUrlCanonicalFlg), if(isNull(sourceUrl), '', sourceUrlPageRobotsMetaIndex), if(isNull(sourceUrl), '', sourceUrlPageRobotsMetaFollow), if(isNull(sourceUrl), '', sourceUrlPageRobotsMetaArchive), destUrlCanonicalFlg, destUrlPageRobotsMetaIndex, destUrlPageRobotsMetaFollow, destUrlPageRobotsMetaArchive, destUrlTitle, if(isNull(sourceUrl), '', sourceFolderLevel1), if(isNull(sourceUrl), '', sourceFolderLevel2), if(isNull(descFolderLevel1), '', descFolderLevel1), if(isNull(descFolderLevel2), '', descFolderLevel2) ");
		sql.append(" FROM ");
		sql.append(" ( ");
		sql.append("   ");
		sql.append("     SELECT ");
		sql.append("         destinationUrlMurmurHash AS urlHash, ");
		sql.append("         anchorText, ");
		sql.append("         sourceDomain, ");
		sql.append("         sourceLinkNofollow, ");
		sql.append("         sourceUrl, ");
		sql.append("         sourceRespCode, ");
		sql.append("         sourceFolderLevel1, ");
		sql.append("         sourceFolderLevel2, ");
		sql.append("         destDomain, ");
		sql.append(" 		 descFolderLevel1, ");
		sql.append(" 		 descFolderLevel2, ");
		sql.append("         sourceUrlCanonicalFlg, ");
		sql.append("         sourceUrlPageRobotsMetaIndex, ");
		sql.append("         sourceUrlPageRobotsMetaFollow, ");
		sql.append("         sourceUrlPageRobotsMetaArchive, ");
		sql.append("         sourceUrlTitle ");
		sql.append("     FROM dis_summary_detail ");
		sql.append("     WHERE (crawlRequestLogId = ?) AND (domainId = ?) AND (crawlRequestLogMod = ?) AND ((destinationUrlMurmurHash % " + partNum + ") = " + num + ") ");
		sql.append(" 	SETTINGS max_bytes_before_external_group_by = 10000000000, distributed_aggregation_memory_efficient = 1 ");
		sql.append(" ) AS d ");
		sql.append(" ALL RIGHT JOIN ");
		sql.append(" ( ");
		sql.append("     SELECT ");
		sql.append("         popularity, ");
		sql.append("         crawlRequestLogId, ");
		sql.append("         domainId, ");
		sql.append("         destinationUrlMurmurHash as urlHash, ");
		sql.append("         destUrl, ");

		sql.append("         destLinkNofollow, ");
		sql.append("         outboundCnt, ");
		sql.append("         destUrlCanonicalFlg, ");
		sql.append("         destRespCode, ");
		sql.append("         destUrlPageRobotsMetaIndex, ");
		sql.append("         destUrlPageRobotsMetaFollow, ");
		sql.append("         destUrlPageRobotsMetaArchive, ");
		sql.append("         destUrlTitle, ");
		sql.append("         inboundLinkCnt ");
//		sql.append("         outboundLinkArray.Url, ");
//		sql.append("         outboundLinkArray.AnchorText ");
		sql.append("     FROM dis_summary_outbound_detail  ");
		sql.append(" 	WHERE (crawlRequestLogId = ?) AND (domainId = ?) AND (crawlRequestLogMod = ?) AND ((destinationUrlMurmurHash % " + partNum + ") = " + num + ") ");
		sql.append("     SETTINGS max_bytes_before_external_group_by = 10000000000, distributed_aggregation_memory_efficient = 1 ");
		sql.append(" ) AS b USING (urlHash)  ");
		sql.append(" SETTINGS max_bytes_before_external_group_by = 10000000000, distributed_aggregation_memory_efficient = 1 ");

		this.executeUpdate(sql.toString(), 
    		crawlerId, ownDomainId, crawlerId % 1000,
    		crawlerId, ownDomainId, crawlerId % 1000
		);
	}
	
	public void summaryPageRank(int ownDomainId, int crawlerId, Integer partNum, Integer num) {
    	
        StringBuffer sql = new StringBuffer();
        sql.append(" insert into actonia_internal_link.dis_internal_link_sampled_view_final_page_rank( ");
        sql.append(" domainId, crawlRequestLogId, sourceLinkNofollow, destLinkNofollow, sourceUrl, sourceDomain, sourceRespCode, ");
        sql.append(" destUrl, destDomain, destRespCode, outboundCnt, inboundLinkCnt, pageRank, anchorText, popularity,  ");
        sql.append(" sourceUrlCanonicalFlg, sourceUrlPageRobotsMetaIndex, sourceUrlPageRobotsMetaFollow, sourceUrlPageRobotsMetaArchive,  ");
        sql.append(" destUrlCanonicalFlg, destUrlPageRobotsMetaIndex, destUrlPageRobotsMetaFollow, destUrlPageRobotsMetaArchive, destUrlTitle,  ");
        sql.append(" sourceFolderLevel1, sourceFolderLevel2, descFolderLevel1, descFolderLevel2) ");
        sql.append(" ");
        sql.append(" select domainId, crawlRequestLogId, sourceLinkNofollow, destLinkNofollow, sourceUrl, sourceDomain, sourceRespCode,  ");
        sql.append(" destUrl, destDomain, destRespCode, outboundCnt, inboundLinkCnt, page_rank, anchorText, popularity,  ");
        sql.append(" sourceUrlCanonicalFlg, sourceUrlPageRobotsMetaIndex, sourceUrlPageRobotsMetaFollow, sourceUrlPageRobotsMetaArchive,  ");
        sql.append(" destUrlCanonicalFlg, destUrlPageRobotsMetaIndex, destUrlPageRobotsMetaFollow, destUrlPageRobotsMetaArchive, destUrlTitle,  ");
        sql.append(" sourceFolderLevel1, sourceFolderLevel2, descFolderLevel1, descFolderLevel2 from ( ");
        sql.append(" select domainId, crawlRequestLogId, sourceLinkNofollow, destLinkNofollow, sourceUrl, sourceDomain, sourceRespCode,  ");
        sql.append(" destUrl, destDomain, destRespCode, outboundCnt, inboundLinkCnt, anchorText, popularity,  ");
        sql.append(" sourceUrlCanonicalFlg, sourceUrlPageRobotsMetaIndex, sourceUrlPageRobotsMetaFollow, sourceUrlPageRobotsMetaArchive,  ");
        sql.append(" destUrlCanonicalFlg, destUrlPageRobotsMetaIndex, destUrlPageRobotsMetaFollow, destUrlPageRobotsMetaArchive, destUrlTitle,  ");
        sql.append(" sourceFolderLevel1, sourceFolderLevel2, descFolderLevel1, descFolderLevel2, destinationUrlMurmurHash  ");
        sql.append("  from actonia_internal_link.dis_internal_link_sampled_view_final  ");
        sql.append("  where domainId = ? and crawlRequestLogMod = ? and crawlRequestLogId = ? ");
        if (partNum != null) {
			sql.append(" and destinationUrlMurmurHash % " + partNum + " = " + num);
		}
        
        sql.append(" ) A any left join ( ");
        sql.append("   select url_murmur_hash as destinationUrlMurmurHash, page_rank from page_rank.dis_page_rank  ");
        sql.append("   where own_domain_id = ?  and crawl_request_log_mod = ? and crawl_request_log_id = ? ");
        if (partNum != null) {
			sql.append(" and url_murmur_hash % " + partNum + " = " + num);
		}
        
        sql.append(" )  B using(destinationUrlMurmurHash) ");
        sql.append(" SETTINGS max_bytes_before_external_group_by = 10000000000, distributed_aggregation_memory_efficient = 1 ");
        
        System.out.println(sql.toString());
        System.out.println("ownDomainId:" + ownDomainId);
        System.out.println("crawlerId:" + crawlerId);

        this.executeUpdate(sql.toString(), 
        		ownDomainId, crawlerId % 1000, crawlerId, 
        		ownDomainId, crawlerId % 1000, crawlerId
		);
    }
	
	
	public Integer getTotalCount(Integer crawlRequestLogId) {
		String sql = " select count() from " + DIS_TABLE_NAME_V2 + " where crawlRequestLogId = " + crawlRequestLogId;
		System.out.println("sql:" + sql);
		return this.queryForInteger(sql);
	}
	
	
}
