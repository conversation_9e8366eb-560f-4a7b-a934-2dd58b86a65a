package seoclarity.backend.dao.clickhouse.prodclarity;

import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.clickhouse.prod.DisSiteCrawlDoc1Entity;
import seoclarity.backend.entity.clickhouse.prod.DisSiteCrawlDocEntity;

import java.math.BigInteger;
import java.util.List;

@Repository("disSiteCrawlDocDao")
public class DisSiteCrawlDocDao extends ProdClarityJdbcSupport<DisSiteCrawlDocEntity> {

    @Override
    public String getTableName() {
        return "dis_site_crawl_doc";
    }

    public boolean checkIsExists(int domainId, int crawlLogId) {
        String sql = "select count(*) from " + getTableName() + " where domain_id = ? and crawl_request_id = ? ";
        Integer cnt = this.queryForInt(sql, domainId, crawlLogId);
        return cnt > 0;
    }

    public String getLastDataRequestTime(Integer ownDomainId, Integer crawlRequestLogId) {
        String sql = " select max(request_time) as request_time from  " + getTableName() + " where domain_id = ? and crawl_request_id = ? ";
        return queryForString(sql, ownDomainId, crawlRequestLogId);
    }
}