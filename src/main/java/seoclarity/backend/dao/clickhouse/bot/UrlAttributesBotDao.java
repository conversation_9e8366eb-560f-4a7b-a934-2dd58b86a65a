package seoclarity.backend.dao.clickhouse.bot;

import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.clarity360.UrlAttributes;

import java.util.ArrayList;
import java.util.List;

@Repository
public class UrlAttributesBotDao extends ClCdb21BotJdbcSupport<UrlAttributes> {
    @Override
    public String getTableName() {
        return "dis_url_attributes";
    }

    public void insertBatch(List<UrlAttributes> urlAttributesList) {
        String sql = "insert into " + getTableName() + " (domain_id, file_id, url, attr.keys, attr.values) values (?, ?, ?, ?, ?) ";
        List<Object[]> batch = new ArrayList<>();
        for (UrlAttributes entity : urlAttributesList) {
            Object[] values = new Object[] {
                    entity.getDomainId(),
                    entity.getFileId(),
                    entity.getUrl(),
                    entity.getAttr<PERSON>eys(),
                    entity.getAttrValues()
            };
            batch.add(values);
        }
        this.executeBatch(sql, batch);
    }
}
