package seoclarity.backend.dao.clickhouse.bot;

import java.sql.Types;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import seoclarity.backend.clarity360.vo.BotVO;
import seoclarity.backend.dao.actonia.ClarityDBVersioningDAO;
import seoclarity.backend.dao.clickhouse.clarity360.lweb05.Clarity360Lweb05DAO;
import seoclarity.backend.entity.clickhouse.bot.BotDetailEntity;
import seoclarity.backend.export.vo.ClarityDBTypesFilterVO;
import seoclarity.backend.export.vo.PaginationColFilter;
import seoclarity.backend.utils.ClarityDBUtils;
import seoclarity.backend.utils.FormatUtils;

/**
 * Created by <PERSON> on 2017/2/27.
 */
@Repository
public class BotDetailDao extends ClBotBaseJdbcSupport<BotDetailEntity> {

    @Autowired
    private ClarityDBVersioningDAO clarityDBVersioningDAO;

    @Override
    public String getTableName() {
        return "dis_bot_detail";
    }
    public List<BotDetailEntity> getBotDetails(int domainId, Date date) {
        StringBuilder sql = new StringBuilder();
        sql.append(" select * from ");
        sql.append(getTableName());
        sql.append(" where own_domain_id = ? ");
        sql.append(" and bot_date = ? ");

        System.out.println(sql.toString());

        return findBySql(sql.toString(), domainId, FormatUtils.formatDate(date, FormatUtils.DATE_PATTERN_2));
    }

    public boolean getBotExistData(int domainId, Date date) {
        StringBuilder sql = new StringBuilder();
        sql.append(" select count() from ");
        sql.append(getTableName());
        sql.append(" where own_domain_id = ? ");
        sql.append(" and bot_date = ? ");
        sql.append(" limit 1 ");

        Integer result = queryForInteger(sql.toString(), domainId, FormatUtils.formatDate(date, FormatUtils.DATE_PATTERN_2));

        return result != null && result > 0;
    }

    public void insertForBatch(List<BotDetailEntity> insertData, Date date) {

        //check version logic
        Map<String, BotDetailEntity> versionMap = new HashMap<>();
        Set<String> badVersion = new HashSet<>();
        for (BotDetailEntity botDetailEntity : insertData) {
            if(botDetailEntity.getVersioning() != null && botDetailEntity.getVersioning() != 0) {
                String key = botDetailEntity.getOwnDomainId()+"!_!"+botDetailEntity.getVersioning()+"!_!"+FormatUtils.formatDate(botDetailEntity.getBotDate(), FormatUtils.DATE_PATTERN_2);
                if(badVersion.contains(key) || versionMap.containsKey(key)) {
                    continue;
                }

                Integer maxVersion = clarityDBVersioningDAO.findMaxVersionNumberByDomainAndDate(botDetailEntity.getOwnDomainId(), FormatUtils.toDate(FormatUtils.formatDate(botDetailEntity.getBotDate(), FormatUtils.DATE_PATTERN_2), FormatUtils.DATE_PATTERN_2), BotDetailEntity.BOT_VERSION_ID);
                if (maxVersion != null && maxVersion == botDetailEntity.getVersioning()) {
                    badVersion.add(key);
                    logger.error("same version exist for key :"+key);
                    break;
                } else if (maxVersion != null && maxVersion > botDetailEntity.getVersioning()) {
                    badVersion.add(key);
                    logger.error("bad version for key :"+key);
                    break;
                }

                if(!versionMap.containsKey(key)) {
                    versionMap.put(key, botDetailEntity);
                }
            }
        }

        if(!versionMap.isEmpty()) {
            logger.info("start insert version data.");
            for (BotDetailEntity botDetailEntity : versionMap.values()) {
                logger.info("insert version : "+botDetailEntity.getVersioning()+" for date : "+botDetailEntity.getBotDate());
                //delete all exist version and insert new one
                clarityDBVersioningDAO.deleteDomainVersion(botDetailEntity.getOwnDomainId(), FormatUtils.toDate(FormatUtils.formatDate(botDetailEntity.getBotDate(), FormatUtils.DATE_PATTERN_2), FormatUtils.DATE_PATTERN_2), BotDetailEntity.BOT_VERSION_ID);
                clarityDBVersioningDAO.insert(botDetailEntity.getOwnDomainId(), FormatUtils.toDate(FormatUtils.formatDate(botDetailEntity.getBotDate(), FormatUtils.DATE_PATTERN_2), FormatUtils.DATE_PATTERN_2), BotDetailEntity.BOT_VERSION_ID, 0, botDetailEntity.getVersioning());
            }
        }

        String sql = "INSERT INTO "+getTableName()
                +" (own_domain_id, useragent, ua_group_id, url, ip_address, bot_date, timestamp, response_code, versioning) VALUES (?,?,?,?,?,?,?,?,?)";
        List<Object[]> batch = new ArrayList<>();
        for (BotDetailEntity entity : insertData) {
            //Leo - https://www.wrike.com/open.htm?id=213964944
            long timestamp = entity.getTimestamp() == null ? entity.getBotDate().getTime() : entity.getTimestamp().getTime();
            timestamp = timestamp / 1000;
            Object[] values = new Object[]{
                    entity.getOwnDomainId(),
                    entity.getUseragent(),
                    entity.getUa_group_id(),
                    entity.getUrl(),
                    entity.getIpAddress(),
                    FormatUtils.formatDate(entity.getBotDate(), "yyyy-MM-dd"),
                    timestamp,
                    entity.getResponseCode(),
                    entity.getVersioning() == null ? 0 : entity.getVersioning()
            };

            batch.add(values);
        }
        autoRetryBatchInsert(sql, batch);
    }

    public void batchCopy(String currentDate, String copyDate, Integer domainId, Integer version) {
        String sql = "INSERT INTO dis_bot_detail(own_domain_id, useragent, ua_group_id, url, urlhash, ip_address, bot_date, timestamp, response_code, versioning) select own_domain_id, useragent, ua_group_id, url, urlhash, ip_address, '" + currentDate + "', '" + currentDate + " 12:05:01', response_code, versioning "
                + " from dis_bot_detail WHERE (own_domain_id = ?) AND (bot_date = '" + copyDate + "')";

        System.out.println(sql);
        executeUpdate(sql, domainId);
    }
    
    
    public Integer getTotalCount(Integer ownDomainId, BotVO botVO, Integer urlTagId,
			ClarityDBTypesFilterVO contentTypeVO) throws Exception {
    	
    	StringBuffer sql = new StringBuffer();
    	sql.append(" select count() from (");
    	sql.append(" SELECT  murmurHash3_64(dis_bot_detail.url) AS url_murmur_hash  ");
    	sql.append(" FROM clarity_bot.dis_bot_detail ");
    	sql.append(" WHERE own_domain_id = ? AND bot_date>= '" + botVO.getStartDate() + "' and bot_date<='" + botVO.getEndDate() + "' AND (ua_group_id IN (1, 2))  AND (versioning = dictGetUInt16('claritydbVersion', 'versioning', (3, toUInt32(own_domain_id), bot_date, toUInt64(0)))) ");
    	
    	if (botVO.getLeftNaviFilters() != null && botVO.getLeftNaviFilters().length > 0) {
            for (PaginationColFilter paginationColFilter : botVO.getLeftNaviFilters()) {
                if ("url".equalsIgnoreCase(paginationColFilter.getColname())) {
                	sql.append(ClarityDBUtils.generateColumnFilterSql(paginationColFilter, "decodeURLComponent(url)", Types.VARCHAR, ClarityDBUtils.OPERATOR_AND, true, true));
                }
            }
        }
    	
    	if (urlTagId != null && urlTagId > 0) {
    		sql.append(" AND (dictGetUInt64('researchgrid_managed_url', 'target_url_id', (toUInt64(" + ownDomainId + "), toUInt64(" + urlTagId + "), URLHash(lower(url)))) != 0 ");
    		sql.append(" OR dictGetUInt64('researchgrid_managed_url', 'target_url_id', (toUInt64(" + ownDomainId + "), toUInt64(" + urlTagId + "), urlhash)) != 0) ");
		}
    	
    	if (contentTypeVO != null) {
    		sql.append(ClarityDBUtils.getCommonTypesFilter(contentTypeVO, "decodeURLComponent(url)"));
		}
    	
    	sql.append(" GROUP BY url_murmur_hash  ");
    	sql.append(" ) ");
        sql.append(" SETTINGS max_bytes_before_external_group_by=20000000000, max_bytes_before_external_sort=10000000000, distributed_aggregation_memory_efficient=1 ");

        System.out.println("BOT Totalcount : " + sql.toString());
		return this.queryForInteger(sql.toString(), ownDomainId);
	}
	
    public void botClarity360Summary(Integer ownDomainId, BotVO botVO, String targetDate, 
    		Integer reportId, Integer urlTagId, ClarityDBTypesFilterVO contentTypeVO, String urlHashColumn, Boolean isForUrlHash, Integer partNum, Integer num, Integer clusterNum) {
		
    	StringBuffer sql = new StringBuffer();
    	sql.append(" insert into clarity360." + Clarity360Lweb05DAO.getDisTableNameByType(isForUrlHash, clusterNum) + " ( domain_id, url, url_murmur_hash, bot_requestcount, bot_2xxrequestcount, bot_3xxrequestcount, ");
    	sql.append(" bot_4xxrequestcount, bot_5xxrequestcount, sources,  target_date,  process_date, bot_numofdays, report_id, folder_level_1,folder_level_2,folder_level_3)   ");
    	sql.append(" SELECT  any(own_domain_id) AS domain_id,  any(dis_bot_detail.url) ,  ");
    	sql.append(" " + urlHashColumn + " AS url_murmur_hash,  ");
    	sql.append(" count(murmurHash3_64(dis_bot_detail.url)) AS bot_requestcount,  ");
    	sql.append(" sum(multiIf((response_code >= 200) AND (response_code < 300), 1, 0)) AS bot_2xxrequestcount,  ");
    	sql.append(" sum(multiIf((response_code >= 300) AND (response_code < 400), 1, 0)) AS bot_3xxrequestcount,  ");
    	sql.append(" sum(multiIf((response_code >= 400) AND (response_code < 500), 1, 0)) AS bot_4xxrequestcount,  ");
    	sql.append(" sum(multiIf((response_code >= 500) AND (response_code < 600), 1, 0)) AS bot_5xxrequestcount,  ");
    	sql.append(" ['bot'],  ?, toDate(now()), count(distinct bot_date) , " + reportId + ",   ");
    	
    	sql.append(" if(endsWith(URLPathHierarchy(cutQueryString(any(dis_bot_detail.url)))[1], '/'), replaceAll(URLPathHierarchy(cutQueryString(any(dis_bot_detail.url)))[1], '/', ''), '') AS folder1, ");
    	sql.append(" if(endsWith(if(length(URLPathHierarchy(cutQueryString(any(dis_bot_detail.url)))[2]) > 0, substring(URLPathHierarchy(cutQueryString(any(dis_bot_detail.url)))[2], length(URLPathHierarchy(cutQueryString(any(dis_bot_detail.url)))[1]), length(URLPathHierarchy(cutQueryString(any(dis_bot_detail.url)))[2])), ''), '/'), replaceAll(if(length(URLPathHierarchy(cutQueryString(any(dis_bot_detail.url)))[2]) > 0, substring(URLPathHierarchy(cutQueryString(any(dis_bot_detail.url)))[2], length(URLPathHierarchy(cutQueryString(any(dis_bot_detail.url)))[1]), length(URLPathHierarchy(cutQueryString(any(dis_bot_detail.url)))[2])), ''), '/', ''), '') AS folder2, ");
    	sql.append(" if(endsWith(if(length(URLPathHierarchy(cutQueryString(any(dis_bot_detail.url)))[3]) > 0, substring(URLPathHierarchy(cutQueryString(any(dis_bot_detail.url)))[3], length(URLPathHierarchy(cutQueryString(any(dis_bot_detail.url)))[2]), length(URLPathHierarchy(cutQueryString(any(dis_bot_detail.url)))[3])), ''), '/'), replaceAll(if(length(URLPathHierarchy(cutQueryString(any(dis_bot_detail.url)))[3]) > 0, substring(URLPathHierarchy(cutQueryString(any(dis_bot_detail.url)))[3], length(URLPathHierarchy(cutQueryString(any(dis_bot_detail.url)))[2]), length(URLPathHierarchy(cutQueryString(any(dis_bot_detail.url)))[3])), ''), '/', ''), '') AS folder3 ");
    	
    	sql.append(" FROM clarity_bot.dis_bot_detail ");
    	sql.append(" WHERE own_domain_id = ? AND bot_date>= '" + botVO.getStartDate() + "' and bot_date<='" + botVO.getEndDate() + "' AND (ua_group_id IN (1, 2))   AND (versioning = dictGetUInt16('claritydbVersion', 'versioning', (3, toUInt32(own_domain_id), bot_date, toUInt64(0)))) ");
    	
		if (partNum != 1) {
    		sql.append(" and ((url_murmur_hash % " + partNum + ") = " + num +")");
		}
    	
//		sql.append(" and url_murmur_hash global not in (select url_murmur_hash from clarity360." + Clarity360Lweb05DAO.getDisTableNameByType(isForUrlHash) + " where report_id = " + reportId + " and has(sources, 'bot')  and  ((url_murmur_hash % " + partNum + ") = " + num +")) ");
		
    	if (botVO.getLeftNaviFilters() != null && botVO.getLeftNaviFilters().length > 0) {
            for (PaginationColFilter paginationColFilter : botVO.getLeftNaviFilters()) {
                if ("url".equalsIgnoreCase(paginationColFilter.getColname())) {
                	sql.append(ClarityDBUtils.generateColumnFilterSql(paginationColFilter, "decodeURLComponent(url)", Types.VARCHAR, ClarityDBUtils.OPERATOR_AND, true, true));
                }
            }
        }
    	
    	if (urlTagId != null && urlTagId > 0) {
    		sql.append(" AND (dictGetUInt64('researchgrid_managed_url', 'target_url_id', (toUInt64(" + ownDomainId + "), toUInt64(" + urlTagId + "), URLHash(lower(url)))) != 0 ");
    		sql.append(" OR dictGetUInt64('researchgrid_managed_url', 'target_url_id', (toUInt64(" + ownDomainId + "), toUInt64(" + urlTagId + "), urlhash)) != 0) ");
		}
    	
    	if (contentTypeVO != null) {
    		sql.append(ClarityDBUtils.getCommonTypesFilter(contentTypeVO, "decodeURLComponent(url)"));
		}
    	
    	sql.append(" GROUP BY url_murmur_hash  ");
    	//sql.append(" SETTINGS distributed_group_by_no_merge = 1, distributed_product_mode = 'local', max_bytes_before_external_group_by = 20000000000 ");
    	
    	System.out.println(sql.toString());
    	this.executeUpdate(sql.toString(), targetDate, ownDomainId);
    }


    public List<BotDetailEntity> botFullExtract(int ownDomainId, Date sDate, Date eDate, int startIndex, int limit) {
        String sql = "select * from " + getTableName() + " " +
                "where own_domain_id = ? " +
                "and bot_date >= ? " +
                "and bot_date <= ? " +
                "order by urlhash limit ? , ?";
        return findBySql(sql, ownDomainId, FormatUtils.formatDate(sDate, "yyyy-MM-dd"), FormatUtils.formatDate(eDate, "yyyy-MM-dd"), startIndex, limit);
    }

    public BotDetailEntity executeSql(String sql) {
        return findObject(sql);
    }

}
