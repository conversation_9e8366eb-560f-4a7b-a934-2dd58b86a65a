
package seoclarity.backend.dao.clickhouse.ga;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.stereotype.Repository;

import com.google.gson.Gson;

import seoclarity.backend.entity.clickhouse.ga.GoogleAnalyticsEntity;
import seoclarity.backend.entity.clickhouse.ga.GoogleAnalyticsV4Entity;
import seoclarity.backend.utils.FormatUtils;

/**
 *
 * <AUTHOR>
 * @date 2018-01-03
 * com.actonia.subserver.dao.clarityDB.GaClarityDBEntityDAO
 *
 */
@Repository
public class GaClarityDBEntityDAO extends GaBaseJdbcSupport<GoogleAnalyticsEntity>{
        public static final String[] columns = new String[] {
                        "domain_id",
                        "log_date",
                        "keyword_text",
                        "host_name",
                        "uri",
                        "medium",
                        "source",
                        "device",
                        "versoin",
                        "country",
                        "entrances",
                        "pageviews",
                        "exits",
                        "bounces",
                        "session_duration",
                        "time_on_page",
                        "transactions",
                        "item_revenue",
                        "goal1completions",
                        "goal2completions",
                        "goal3completions",
                        "goal4completions",
                        "goal5completions",
                        "goal6completions",
                        "goal7completions",
                        "goal8completions",
                        "goal9completions",
                        "goal10completions",
                        "goal11completions",
                        "goal12completions",
                        "goal13completions",
                        "goal14completions",
                        "goal15completions",
                        "goal16completions",
                        "goal17completions",
                        "goal18completions",
                        "goal19completions",
                        "goal20completions",
                        "goal1Value",
                        "goal2Value",
                        "goal3Value",
                        "goal4Value",
                        "goal5Value",
                        "goal6Value",
                        "goal7Value",
                        "goal8Value",
                        "goal9Value",
                        "goal10Value",
                        "goal11Value",
                        "goal12Value",
                        "goal13Value",
                        "goal14Value",
                        "goal15Value",
                        "goal16Value",
                        "goal17Value",
                        "goal18Value",
                        "goal19Value",
                        "goal20Value",
                        "data_source_type",
                        "url",
        };


        public static final String[] columnsV4 = new String[] {
                        "domain_id",
                        "log_date",
                        "keyword_text",
                        "host_name",
                        "uri",
                        "full_page_url",
                        "session_medium",
                        "session_source",
                        "device_category",
                        "country",
                        "event_name",
                        "sessions",
                        "ecommerce_purchases",
                        "transactions",
                        "engaged_sessions",
                        "engagement_rate",
                        "event_count",
                        "event_value",
                        "versoin",
                        "data_source_type",
                        "batch_no",
                        "goal_id",
                        "page_title",
                        "url",
                        "screen_pageviews",
                        "platform",
//                      "attrs.key",
//                      "attrs.value"
        };

        private static final String[] mixtraffic_ga3_columns = new String[] {
                        "domain_id",
                        "log_date",
                        "medium",
                        "source",
                        "device",
                        "version",
                        "entrances",
                        "pageviews",
                        "bounces",
                        "transactions",
                        "item_revenue",
                        "goal1completions",
                        "goal2completions",
                        "goal3completions",
                        "goal4completions",
                        "goal5completions",
                        "goal6completions",
                        "goal7completions",
                        "goal8completions",
                        "goal9completions",
                        "goal10completions",
                        "goal11completions",
                        "goal12completions",
                        "goal13completions",
                        "goal14completions",
                        "goal15completions",
                        "goal16completions",
                        "goal17completions",
                        "goal18completions",
                        "goal19completions",
                        "goal20completions",
                        "goal1Value",
                        "goal2Value",
                        "goal3Value",
                        "goal4Value",
                        "goal5Value",
                        "goal6Value",
                        "goal7Value",
                        "goal8Value",
                        "goal9Value",
                        "goal10Value",
                        "goal11Value",
                        "goal12Value",
                        "goal13Value",
                        "goal14Value",
                        "goal15Value",
                        "goal16Value",
                        "goal17Value",
                        "goal18Value",
                        "goal19Value",
                        "goal20Value",
                        "data_source_type"
        };


        private static final String[] mixtraffic_ga4_columns = new String[] {
                        "domain_id",
                        "log_date",
                        "medium",
                        "source",
                        "device",
                        "event_name",
                        "sessions",
                        "ecommerce_purchases",
                        "transactions",
                        "engaged_sessions",
                        "engagement_rate",
                        "event_count",
                        "event_value",
                        "version",
                        "batch_no",
                        "goal_id",
                        "platform",

        };

        @Deprecated
        public String getTableName() {
                return null;
        }

        public void dropTable(String tableName) {
                String sql =  "drop table " + tableName;
                System.out.println("sql:" + sql);
                this.executeUpdate(sql);
        }


        public String createTable(Date date, boolean isBackProcess) throws Exception{
                String tableName = getTempSummaryTableName(date, isBackProcess);
                String sql = "create table " + tableName + " as local_ga_summing_template "
                                + " ENGINE = SummingMergeTree(log_date, (domain_id, log_date, medium, source, device, versoin, data_source_type, keyword_hash, host_name_hash, uri_hash, country), 8192, (entrances, pageviews, exits, bounces, session_duration, time_on_page, transactions, item_revenue, goal1completions, goal2completions, goal3completions, goal4completions, goal5completions, goal6completions, goal7completions, goal8completions, goal9completions, goal10completions, goal11completions, goal12completions, goal13completions, goal14completions, goal15completions, goal16completions, goal17completions, goal18completions, goal19completions, goal20completions, goal1Value, goal2Value, goal3Value, goal4Value, goal5Value, goal6Value, goal7Value, goal8Value, goal9Value, goal10Value, goal11Value, goal12Value, goal13Value, goal14Value, goal15Value, goal16Value, goal17Value, goal18Value, goal19Value, goal20Value)) ";
                System.out.println("sql:" + sql);
                this.executeUpdate(sql);
                return tableName;
        }

        public String createTableV4(Date date, boolean isBackProcess, boolean isTrafficMix, boolean isFreshData) throws Exception{
                String tableName = getTempSummaryTableNameV4(date, isBackProcess, isTrafficMix, isFreshData);
                String sql = "create table " + tableName + " as local_analytics_ga4_raw_template "
                                + " ENGINE = MergeTree(log_date, (domain_id, log_date, country, session_medium, session_source, device_category, host_name, uri), 8192) ";
                System.out.println("sql:" + sql);
                this.executeUpdate(sql);
                return tableName;
        }
        
        public String createTableForBigQuery(Date date, boolean isOrganic) throws Exception{
            String tableName = getTempRawTableForBigQuery(isOrganic, date);
            String sql = "create table " + tableName + " as local_analytics_ga4_raw_template "
                            + " ENGINE = MergeTree(log_date, (domain_id, log_date, country, session_medium, session_source, device_category, host_name, uri), 8192) ";
            System.out.println("sql:" + sql);
            this.executeUpdate(sql);
            return tableName;
    }

        public List<String> getTableList(Date date, boolean isBackProcess) {
                String tableName = getTempSummaryTableName(date, isBackProcess);
                String sql = "show tables like '" + tableName + "' ";
                System.out.println("sql:" + sql);
                return this.queryForStringList(sql);
        }

        public List<String> getTableListV4(Date date, boolean isBackProcess, boolean isTrafficMix, boolean isFreshData) {
                String tableName = getTempSummaryTableNameV4(date, isBackProcess, isTrafficMix, isFreshData);
                String sql = "show tables like '" + tableName + "' ";
                System.out.println("sql:" + sql);
                return this.queryForStringList(sql);
        }
        
        public List<String> getTableListForBigQuery(Date date, Boolean isOrganic) {
        	String tableName = getTempRawTableForBigQuery(isOrganic, date);
            String sql = "show tables like '" + tableName + "' ";
            System.out.println("sql:" + sql);
            return this.queryForStringList(sql);
    }

        public String getInsertSql(String tableName) {
                String sql = "INSERT INTO " + tableName + "(" + StringUtils.join((columns), ",") + ") FORMAT TabSeparated";
                return sql;
        }

        public String getInsertSqlV4(String tableName) {
                String sql = "INSERT INTO " + tableName + "(" + StringUtils.join((columnsV4), ",") + ") FORMAT TabSeparated";
                return sql;
        }

        public String getInsertSqlMixTrafficGA3(String tableName) {
                String sql = "INSERT INTO " + tableName + "(" + StringUtils.join((mixtraffic_ga3_columns), ",") + ") FORMAT TabSeparated";
                return sql;
        }


        public String getInsertSqlMixTrafficGA4(String tableName) {
                String sql = "INSERT INTO " + tableName + "(" + StringUtils.join((mixtraffic_ga4_columns), ",") + ") FORMAT TabSeparated";
                return sql;
        }

        public String getMaxDateByDomainId(int domainId) {
                String sql = " select max(log_date) as crawlDate from dis_ga where domain_id = ? and log_date >= ?";

                List<Map<String, Object>> result = this.queryForMapList(sql.toString(), domainId, FormatUtils.formatDate(DateUtils.addDays(new Date(), -3), "yyyy-MM-dd"));
                if (CollectionUtils.isNotEmpty(result) && result.size() > 0) {
                        return result.get(0).get("crawlDate").toString();
                }
                return null;
        }


        public Integer getTotalCount(Date date, boolean isBackProcess) {
                String tableName = getTempSummaryTableName(date, isBackProcess);
                String sql = " select count() from " + tableName + "";
                System.out.println("sql:" + sql);
                return this.queryForInteger(sql);
        }

        public Integer getTotalCountV4(Date date, boolean isBackProcess, boolean isTrafficMix, boolean isFreshData) {
                String tableName = getTempSummaryTableNameV4(date, isBackProcess, isTrafficMix, isFreshData);
                String sql = " select count() from " + tableName + "";
                System.out.println("sql:" + sql);
                return this.queryForInteger(sql);
        }
        
        public Integer getTotalCountForBigQuery(String tableName) {
            String sql = " select count() from " + tableName + "";
            System.out.println("sql:" + sql);
            return this.queryForInteger(sql);
        }


        public String getTempSummaryTableName(Date date, boolean isBackProcess){
                return "local_ga_summing_" + FormatUtils.formatDate(date, "yyyyMMddHH") + (isBackProcess? "0" : "");
        }

        public String getTempSummaryTableNameV4(Date date, boolean isBackProcess, boolean isTrafficMix, boolean isFreshData){

                String prefix = "local_analytics_ga4_raw_";

                if (isTrafficMix) {
                        prefix = "local_mix_ga4_raw_";
                }

                if (isFreshData) {
                        prefix = StringUtils.replace(prefix, "local_", "local_fresh_") ;
                }

                return prefix + FormatUtils.formatDate(date, "yyyyMMddHH") + (isBackProcess? "0" : "");
        }
        
        public String getTempRawTableForBigQuery(boolean isOrganic, Date date){

            String prefix = "local_analytics_ga4bq_raw_";

            if (!isOrganic) {
            	prefix = "local_mix_ga4bq_raw_";
            }
            return prefix + FormatUtils.formatDate(date, "yyyyMMddHH");
    }


    public void batchCopy(String currentDate, String copyDate, Integer domainId, Integer version) {
        String sql = "INSERT INTO dis_ga(domain_id, log_date, keyword_text, keyword_hash, host_name, host_name_hash, uri, uri_hash, medium, "
        		+ "source, device, country, versoin, entrances, pageviews, exits, bounces, session_duration, time_on_page, transactions, "
        		+ "item_revenue, goal1completions, goal2completions, goal3completions, goal4completions, goal5completions, goal6completions, "
        		+ "goal7completions, goal8completions, goal9completions, goal10completions, goal11completions, goal12completions, goal13completions, "
        		+ "goal14completions, goal15completions, goal16completions, goal17completions, goal18completions, goal19completions, goal20completions, "
        		+ "goal1Value, goal2Value, goal3Value, goal4Value, goal5Value, goal6Value, goal7Value, goal8Value, goal9Value, goal10Value, goal11Value, "
        		+ "goal12Value, goal13Value, goal14Value, goal15Value, goal16Value, goal17Value, goal18Value, goal19Value, goal20Value, data_source_type, batch_no, "
        		+ "oid_uri_hash, url, url_hash, url_murmur_hash, engaged_sessions, engagement_rate, screen_pageviews, stable_flg, platform) "
        		+ "select domain_id, '" + currentDate + "', keyword_text, keyword_hash, host_name, host_name_hash, uri, uri_hash, medium, source, device, country, "
        		+ "versoin, entrances, pageviews, exits, bounces, session_duration, time_on_page, transactions, item_revenue, goal1completions, "
        		+ "goal2completions, goal3completions, goal4completions, goal5completions, goal6completions, goal7completions, goal8completions, "
        		+ "goal9completions, goal10completions, goal11completions, goal12completions, goal13completions, goal14completions, goal15completions, "
        		+ "goal16completions, goal17completions, goal18completions, goal19completions, goal20completions, goal1Value, goal2Value, goal3Value, "
        		+ "goal4Value, goal5Value, goal6Value, goal7Value, goal8Value, goal9Value, goal10Value, goal11Value, goal12Value, goal13Value, goal14Value, "
        		+ "goal15Value, goal16Value, goal17Value, goal18Value, goal19Value, goal20Value, data_source_type, batch_no, oid_uri_hash, url, url_hash, "
        		+ "url_murmur_hash, engaged_sessions, engagement_rate, screen_pageviews, stable_flg, platform"
                        + " from dis_ga WHERE (domain_id = ?) AND (log_date = '" + copyDate + "')";

        System.out.println(sql);
        executeUpdate(sql, domainId);
    }


    public List<GoogleAnalyticsEntity> getExtractData(int domainId, String logStartDate, String logEndDate) {
       StringBuffer sql = new StringBuffer();

       sql.append(" SELECT ");
       sql.append("     any(domain_id) as domainId, ");
       sql.append("     log_date, ");
       sql.append("     concat('https://', host_name, uri) AS url, ");
       sql.append("     device, ");
       sql.append("     country, ");
       sql.append("     arrayDistinct(groupArray(if(length(keyword_text) = 0, NULL, keyword_text)))  AS keywordList, ");
       sql.append("     sum(entrances) as entrances, ");
       sql.append("     sum(pageviews) as pageviews, ");
       sql.append("     sum(exits) as exits, ");
       sql.append("     sum(bounces) as bounces, ");
       sql.append("     sum(session_duration) as session_duration, ");
       sql.append("     sum(time_on_page) as timeOnPage, ");
       sql.append("     sum(transactions) as transactions, ");
       sql.append("     sum(item_revenue) as itemRevenue, ");
       sql.append("     sum(goal1completions) as goal1completions, ");
       sql.append("     sum(goal2completions) as goal2completions, ");
       sql.append("     sum(goal3completions) as goal3completions, ");
       sql.append("     sum(goal4completions) as goal4completions, ");
       sql.append("     sum(goal5completions) as goal5completions, ");
       sql.append("     sum(goal6completions) as goal6completions, ");
       sql.append("     sum(goal7completions) as goal7completions, ");
       sql.append("     sum(goal8completions) as goal8completions, ");
       sql.append("     sum(goal9completions) as goal9completions, ");
       sql.append("     sum(goal10completions) as goal10completions, ");
       sql.append("     sum(goal11completions) as goal11completions, ");
       sql.append("     sum(goal12completions) as goal12completions, ");
       sql.append("     sum(goal13completions) as goal13completions, ");
       sql.append("     sum(goal14completions) as goal14completions, ");
       sql.append("     sum(goal15completions) as goal15completions, ");
       sql.append("     sum(goal16completions) as goal16completions, ");
       sql.append("     sum(goal17completions) as goal17completions, ");
       sql.append("     sum(goal18completions) as goal18completions, ");
       sql.append("     sum(goal19completions) as goal19completions, ");
       sql.append("     sum(goal20completions) as goal20completions, ");
       sql.append("     sum(goal1Value) as goal1Value, ");
       sql.append("     sum(goal2Value) as goal2Value, ");
       sql.append("     sum(goal3Value) as goal3Value, ");
       sql.append("     sum(goal4Value) as goal4Value, ");
       sql.append("     sum(goal5Value) as goal5Value, ");
       sql.append("     sum(goal6Value) as goal6Value, ");
       sql.append("     sum(goal7Value) as goal7Value, ");
       sql.append("     sum(goal8Value) as goal8Value, ");
       sql.append("     sum(goal9Value) as goal9Value, ");
       sql.append("     sum(goal10Value) as goal10Value, ");
       sql.append("     sum(goal11Value) as goal11Value, ");
       sql.append("     sum(goal12Value) as goal12Value, ");
       sql.append("     sum(goal13Value) as goal13Value, ");
       sql.append("     sum(goal14Value) as goal14Value, ");
       sql.append("     sum(goal15Value) as goal15Value, ");
       sql.append("     sum(goal16Value) as goal16Value, ");
       sql.append("     sum(goal17Value) as goal17Value, ");
       sql.append("     sum(goal18Value) as goal18Value, ");
       sql.append("     sum(goal19Value) as goal19Value, ");
       sql.append("     sum(goal20Value) as goal20Value ");
       sql.append(" FROM dis_ga ");
       sql.append(" WHERE (domain_id = " + domainId + ") AND (log_date >= '" + logStartDate + "') AND (log_date <= '" + logEndDate + "') ");
       sql.append(" GROUP BY ");
       sql.append(" log_date, ");
       sql.append("     url, ");
       sql.append("     country, ");
       sql.append("     device ");

       return findBySql(sql.toString());
    }


    public List<GoogleAnalyticsEntity> getDomainData(int domainId, String logStartDate, String logEndDate, int version, String device){
                StringBuffer sql = new StringBuffer();
                sql.append(" SELECT domain_id,log_date,keyword_text,host_name,uri,medium,source,device,country,versoin,entrances,pageviews,");
                sql.append(" exits,bounces,session_duration,time_on_page,transactions,item_revenue, ");
                sql.append(" goal1completions,goal2completions,goal3completions,goal4completions,goal5completions,goal6completions, ");
                sql.append(" goal7completions,goal8completions,goal9completions,goal10completions,goal11completions,goal12completions, ");
                sql.append(" goal13completions,goal14completions,goal15completions,goal16completions,goal17completions,goal18completions, ");
                sql.append(" goal19completions,goal20completions,goal1Value,goal2Value,goal3Value,goal4Value,goal5Value,goal6Value,goal7Value, ");
                sql.append(" goal8Value,goal9Value,goal10Value,goal11Value,goal12Value,goal13Value,goal14Value,goal15Value,goal16Value,goal17Value, ");
                sql.append(" goal18Value,goal19Value,goal20Value,data_source_type,batch_no ");
                sql.append(" FROM dis_ga ");
                sql.append(" WHERE (domain_id = " + domainId + ") AND (log_date >= '" + logStartDate + "') AND (log_date <= '" + logEndDate + "') ");
                sql.append(" and versoin = " + version + " and device = '" + device + "'");
                return findBySql(sql.toString());
        }

        private static final String[] columnsTemp = new String[] {
                        "domain_id",
                        "log_date",
                        "keyword_text",
                        "host_name",
                        "uri",
                        "medium",
                        "source",
                        "device",
                        "country",
                        "versoin",
                        "entrances",
                        "pageviews",
                        "exits",
                        "bounces",
                        "session_duration",
                        "time_on_page",
                        "transactions",
                        "item_revenue",
                        "goal1completions",
                        "goal2completions",
                        "goal3completions",
                        "goal4completions",
                        "goal5completions",
                        "goal6completions",
                        "goal7completions",
                        "goal8completions",
                        "goal9completions",
                        "goal10completions",
                        "goal11completions",
                        "goal12completions",
                        "goal13completions",
                        "goal14completions",
                        "goal15completions",
                        "goal16completions",
                        "goal17completions",
                        "goal18completions",
                        "goal19completions",
                        "goal20completions",
                        "goal1Value",
                        "goal2Value",
                        "goal3Value",
                        "goal4Value",
                        "goal5Value",
                        "goal6Value",
                        "goal7Value",
                        "goal8Value",
                        "goal9Value",
                        "goal10Value",
                        "goal11Value",
                        "goal12Value",
                        "goal13Value",
                        "goal14Value",
                        "goal15Value",
                        "goal16Value",
                        "goal17Value",
                        "goal18Value",
                        "goal19Value",
                        "goal20Value",
                        "data_source_type",
                        "batch_no",
        };

        public String getInsertSqlTemp(String tableName) {
                String sql = "INSERT INTO " + tableName + "(" + StringUtils.join((columnsTemp), ",") + ") FORMAT TabSeparated";
                return sql;
        }

        public Map<String, Object> getEntrances(int domainId, Date startDate, Date endDate) {
                StringBuffer sql = new StringBuffer();
//              sql.append(" SELECT sumIf(entrances, match(concat('https://', host_name, uri), 'spokeo.com/[A-Z]([a-z]+)?-(A|B|C|D)([a-z]+)$') ");
//              sql.append(" OR match(decodeURLComponent(concat('https://', host_name, uri)), 'spokeo.com/[A-Z]([a-z]+)?-(A|B|C|D)([a-z]+)$')) as enAD,");
//              sql.append(" sumIf(entrances, match(concat('https://', host_name, uri), 'spokeo.com/[A-Z]([a-z]+)?-(E|F|G|H|I|J|K)([a-z]+)$') ");
//              sql.append(" OR match(decodeURLComponent(concat('https://', host_name, uri)), 'spokeo.com/[A-Z]([a-z]+)?-(E|F|G|H|I|J|K)([a-z]+)$')) as enEK,");
//              sql.append(" sumIf(entrances, match(concat('https://', host_name, uri), 'spokeo.com/[A-Z]([a-z]+)?-(L|M|N|O|P)([a-z]+)$') ");
//              sql.append(" OR match(decodeURLComponent(concat('https://', host_name, uri)), 'spokeo.com/[A-Z]([a-z]+)?-(L|M|N|O|P)([a-z]+)$')) as enLP,");
//              sql.append(" sumIf(entrances, match(concat('https://', host_name, uri), 'spokeo.com/[A-Z]([a-z]+)?-(Q|R|S|T|U|V|X|Y|Z)([a-z]+)$') ");
//              sql.append(" OR match(decodeURLComponent(concat('https://', host_name, uri)), 'spokeo.com/[A-Z]([a-z]+)?-(Q|R|S|T|U|V|X|Y|Z)([a-z]+)$')) as enQZ");
//              sql.append(" FROM dis_ga");
//              sql.append(" WHERE (domain_id = " + domainId+ ") ");
//              sql.append(" AND (lower(source) = 'google') ");
//              sql.append(" AND versoin = dictGetUInt16('claritydbVersion', 'versioning', (2, toUInt32(domain_id), log_date, toUInt64(0))) ");
//              sql.append(" AND (log_date = '" + FormatUtils.formatDate(endDate, "yyyy-MM-dd") +"') ");
//              sql.append(" AND (match(concat('https://', host_name, uri), 'spokeo.com/[A-Z]([a-z]+)?-(A|B|C|D)([a-z]+)$') ");
//              sql.append(" OR match(decodeURLComponent(concat('https://', host_name, uri)), 'spokeo.com/[A-Z]([a-z]+)?-(A|B|C|D)([a-z]+)$')");
//              sql.append(" OR match(concat('https://', host_name, uri), 'spokeo.com/[A-Z]([a-z]+)?-(E|F|G|H|I|J|K)([a-z]+)$') ");
//              sql.append(" OR match(decodeURLComponent(concat('https://', host_name, uri)), 'spokeo.com/[A-Z]([a-z]+)?-(E|F|G|H|I|J|K)([a-z]+)$')");
//              sql.append(" OR match(concat('https://', host_name, uri), 'spokeo.com/[A-Z]([a-z]+)?-(L|M|N|O|P)([a-z]+)$') ");
//              sql.append(" OR match(decodeURLComponent(concat('https://', host_name, uri)), 'spokeo.com/[A-Z]([a-z]+)?-(L|M|N|O|P)([a-z]+)$')");
//              sql.append(" OR match(concat('https://', host_name, uri), 'spokeo.com/[A-Z]([a-z]+)?-(Q|R|S|T|U|V|X|Y|Z)([a-z]+)$') ");
//              sql.append(" OR match(decodeURLComponent(concat('https://', host_name, uri)), 'spokeo.com/[A-Z]([a-z]+)?-(Q|R|S|T|U|V|X|Y|Z)([a-z]+)$')");
//              sql.append(" ) ");
//              sql.append(" AND (URLHash(lowerUTF8(concat('https://', host_name, uri))) GLOBAL IN");
//              sql.append(" (");
//              sql.append("     SELECT DISTINCT urlhash");
//              sql.append("     FROM clarity_bot.dis_bot_detail");
//              sql.append("     WHERE (ua_group_id = 2) AND (own_domain_id = " + domainId + ") ");
//              sql.append("    AND (match(url, 'spokeo.com/[A-Z]([a-z]+)?-(A|B|C|D)([a-z]+)$') ");
//              sql.append("    OR match(url, 'spokeo.com/[A-Z]([a-z]+)?-(E|F|G|H|I|J|K)([a-z]+)$') ");
//              sql.append("    OR match(url, 'spokeo.com/[A-Z]([a-z]+)?-(L|M|N|O|P)([a-z]+)$') ");
//              sql.append("    OR match(url, 'spokeo.com/[A-Z]([a-z]+)?-(Q|R|S|T|U|V|X|Y|Z)([a-z]+)$')) ");
//              sql.append("    AND (bot_date >= '" + FormatUtils.formatDate(startDate, "yyyy-MM-dd") + "') AND (bot_date <= '" + FormatUtils.formatDate(endDate, "yyyy-MM-dd") + "')");
//              sql.append(" ))");

//              sql.append(" SELECT sumIf(entrances, match(concat('https://', host_name, uri), '^https://www.spokeo.com/[A-Z]([a-z]+)?-(C|G|H|I|K|L|M|S|U|X)([A-Za-z]+)(/[0-9]{1,2})?$') ");
//              sql.append(" OR match(decodeURLComponent(concat('https://', host_name, uri)), '^https://www.spokeo.com/[A-Z]([a-z]+)?-(C|G|H|I|K|L|M|S|U|X)([A-Za-z]+)(/[0-9]{1,2})?$')) as enAD,");
//              sql.append(" sumIf(entrances, match(concat('https://', host_name, uri), '^https://www.spokeo.com/[A-Z]([a-z]+)?-(A|B|D|E|F|J|N|O|P|Q|R|T|V|W|Y|Z)([A-Za-z]+)(/[0-9]{1,2})?$') ");
//              sql.append(" OR match(decodeURLComponent(concat('https://', host_name, uri)), '^https://www.spokeo.com/[A-Z]([a-z]+)?-(A|B|D|E|F|J|N|O|P|Q|R|T|V|W|Y|Z)([A-Za-z]+)(/[0-9]{1,2})?$')) as enLP");
//              sql.append(" FROM dis_ga");
//              sql.append(" WHERE (domain_id = " + domainId+ ") ");
//              sql.append(" AND (lower(source) = 'google') ");
//              sql.append(" AND versoin = dictGetUInt16('claritydbVersion', 'versioning', (2, toUInt32(domain_id), log_date, toUInt64(0))) ");
//              sql.append(" AND (log_date = '" + FormatUtils.formatDate(endDate, "yyyy-MM-dd") +"') ");
//              sql.append(" AND (match(concat('https://', host_name, uri), '^https://www.spokeo.com/[A-Z]([a-z]+)?-(C|G|H|I|K|L|M|S|U|X)([A-Za-z]+)(/[0-9]{1,2})?$') ");
//              sql.append(" OR match(decodeURLComponent(concat('https://', host_name, uri)), '^https://www.spokeo.com/[A-Z]([a-z]+)?-(C|G|H|I|K|L|M|S|U|X)([A-Za-z]+)(/[0-9]{1,2})?$')");
//              sql.append(" OR match(concat('https://', host_name, uri), '^https://www.spokeo.com/[A-Z]([a-z]+)?-(A|B|D|E|F|J|N|O|P|Q|R|T|V|W|Y|Z)([A-Za-z]+)(/[0-9]{1,2})?$') ");
//              sql.append(" OR match(decodeURLComponent(concat('https://', host_name, uri)), '^https://www.spokeo.com/[A-Z]([a-z]+)?-(A|B|D|E|F|J|N|O|P|Q|R|T|V|W|Y|Z)([A-Za-z]+)(/[0-9]{1,2})?$')");
//              sql.append(" ) ");
//              sql.append(" AND (URLHash(lowerUTF8(concat('https://', host_name, uri))) GLOBAL IN");
//              sql.append(" (");
//              sql.append("     SELECT DISTINCT urlhash");
//              sql.append("     FROM clarity_bot.dis_bot_detail");
//              sql.append("     WHERE (ua_group_id = 2) AND (own_domain_id = " + domainId + ") ");
//              sql.append("    AND (match(url, '^https://www.spokeo.com/[A-Z]([a-z]+)?-(C|G|H|I|K|L|M|S|U|X)([A-Za-z]+)(/[0-9]{1,2})?$') ");
//              sql.append("    OR match(url, '^https://www.spokeo.com/[A-Z]([a-z]+)?-(A|B|D|E|F|J|N|O|P|Q|R|T|V|W|Y|Z)([A-Za-z]+)(/[0-9]{1,2})?$') ");
//              sql.append("    ) ");
//              sql.append("    AND (bot_date >= '" + FormatUtils.formatDate(startDate, "yyyy-MM-dd") + "') AND (bot_date <= '" + FormatUtils.formatDate(endDate, "yyyy-MM-dd") + "')");
//              sql.append(" ))");

                sql.append(" SELECT sumIf(entrances, ((match(url, '^https://www.spokeo.com/([A-Z]+[a-z]+)-(C|G|H|I|K|L|M|S|U|X)([A-Za-z]+)') OR match(decodeURLComponent(url), '^https://www.spokeo.com/([A-Z]+[a-z]+)-(C|G|H|I|K|L|M|S|U|X)([A-Za-z]+)')) AND ((NOT match(url, '/p[0-9]?')) AND (NOT match(decodeURLComponent(url), '/p[0-9]?'))))) as enAD, ");
                sql.append("        sumIf(entrances, ((match(url, '^https://www.spokeo.com/[A-Z]([a-z]+)?-(A|B|D|E|F|J|N|O|P|Q|R|T|V|W|Y|Z)([A-Za-z]+)') OR match(decodeURLComponent(url), '^https://www.spokeo.com/[A-Z]([a-z]+)?-(A|B|D|E|F|J|N|O|P|Q|R|T|V|W|Y|Z)([A-Za-z]+)')) AND ((NOT match(url, '/p[0-9]?')) AND (NOT match(decodeURLComponent(url), '/p[0-9]?'))))) as enLP ");
                sql.append(" FROM dis_ga ");
                sql.append(" WHERE (domain_id = " + domainId+ ")  ");
                sql.append(" AND (lower(source) = 'google')  ");
                sql.append(" AND (device = 'desktop')  ");
                sql.append(" AND versoin = dictGetUInt16('claritydbVersion', 'versioning', (2, toUInt32(domain_id), log_date, toUInt64(0)))  ");
                sql.append(" AND (log_date = '" + FormatUtils.formatDate(endDate, "yyyy-MM-dd") +"')  ");
                sql.append(" AND (((match(url, '^https://www.spokeo.com/([A-Z]+[a-z]+)-(C|G|H|I|K|L|M|S|U|X)([A-Za-z]+)') OR match(decodeURLComponent(url), '^https://www.spokeo.com/([A-Z]+[a-z]+)-(C|G|H|I|K|L|M|S|U|X)([A-Za-z]+)')) AND ((NOT match(url, '/p[0-9]?')) AND (NOT match(decodeURLComponent(url), '/p[0-9]?'))))  ");
                sql.append("  OR ((match(url, '^https://www.spokeo.com/[A-Z]([a-z]+)?-(A|B|D|E|F|J|N|O|P|Q|R|T|V|W|Y|Z)([A-Za-z]+)') OR match(decodeURLComponent(url), '^https://www.spokeo.com/[A-Z]([a-z]+)?-(A|B|D|E|F|J|N|O|P|Q|R|T|V|W|Y|Z)([A-Za-z]+)')) AND ((NOT match(url, '/p[0-9]?')) AND (NOT match(decodeURLComponent(url), '/p[0-9]?'))))) ");
                sql.append(" AND (URLHash(lowerUTF8(concat('https://', host_name, uri))) GLOBAL IN ");
                sql.append(" ( ");
                sql.append("     SELECT DISTINCT urlhash ");
                sql.append("     FROM clarity_bot.dis_bot_detail ");
                sql.append("     WHERE (ua_group_id = 2) AND (own_domain_id = " + domainId + ")  ");
                sql.append("     AND (((match(url, '^https://www.spokeo.com/([A-Z]+[a-z]+)-(C|G|H|I|K|L|M|S|U|X)([A-Za-z]+)') OR match(decodeURLComponent(url), '^https://www.spokeo.com/([A-Z]+[a-z]+)-(C|G|H|I|K|L|M|S|U|X)([A-Za-z]+)')) AND ((NOT match(url, '/p[0-9]?')) AND (NOT match(decodeURLComponent(url), '/p[0-9]?'))))  ");
                sql.append("      OR ((match(url, '^https://www.spokeo.com/[A-Z]([a-z]+)?-(A|B|D|E|F|J|N|O|P|Q|R|T|V|W|Y|Z)([A-Za-z]+)') OR match(decodeURLComponent(url), '^https://www.spokeo.com/[A-Z]([a-z]+)?-(A|B|D|E|F|J|N|O|P|Q|R|T|V|W|Y|Z)([A-Za-z]+)')) AND ((NOT match(url, '/p[0-9]?')) AND (NOT match(decodeURLComponent(url), '/p[0-9]?'))))) ");
                sql.append("    AND (bot_date >= '" + FormatUtils.formatDate(startDate, "yyyy-MM-dd") + "') AND (bot_date <= '" + FormatUtils.formatDate(endDate, "yyyy-MM-dd") + "') ");
                sql.append(" )) ");
                System.out.println("sql : " + sql.toString());
                List<Map<String, Object>> result = this.queryForMapList(sql.toString());
                if (CollectionUtils.isNotEmpty(result) && result.size() > 0) {
                        return result.get(0);
                }
                return null;
        }

        public List<String> getUrlHashList(int domainId, Date startDate, Date endDate) {
                StringBuffer sql = new StringBuffer();
                sql.append(" SELECT URLHash(lowerUTF8(concat('https://', host_name, uri))) urlHsh ");
                sql.append(" FROM dis_ga ");
                sql.append(" WHERE (domain_id = ?)  ");
                sql.append(" AND (lower(source) = 'bing')  ");
                sql.append(" AND versoin = dictGetUInt16('claritydbVersion', 'versioning', (2, toUInt32(domain_id), log_date, toUInt64(0)))  ");
                sql.append(" AND (log_date = '" + FormatUtils.formatDate(endDate, "yyyy-MM-dd") +"')  ");
                sql.append(" AND (match(concat('https://', host_name, uri), 'https://www.spokeo.com/(1-)?[0-9]{3}-[0-9]{3}-[0-9]{3}(2|5|8|0)')  ");
                sql.append("    OR match(concat('https://', host_name, uri), 'https://www.spokeo.com/(1-)?[0-9]{3}-[0-9]{3}-[0-9]{3}(3|6|9|1)')   ");
                sql.append("    OR match(concat('https://', host_name, uri), 'https://www.spokeo.com/(1-)?[0-9]{3}-[0-9]{3}-[0-9]{3}(4|7)'))   ");

                System.out.println("sql : " + sql.toString());
                List<String> result = this.queryForStringList(sql.toString(), domainId);
                return result;
        }

        public List<String> getUrlHashListFromBot(int domainId, Date startDate, Date endDate, List<String> urlHashList) {
                StringBuffer sql = new StringBuffer();
                sql.append("     SELECT DISTINCT urlhash ");
                sql.append("     FROM clarity_bot.dis_bot_detail ");
                sql.append("     WHERE (ua_group_id = 3) AND (own_domain_id = ?)  ");
                sql.append("    AND urlhash in (" + getStringListQueryParam(urlHashList) + ")   ");
                sql.append("    AND (bot_date >= '" + FormatUtils.formatDate(startDate, "yyyy-MM-dd") + "') AND (bot_date <= '" + FormatUtils.formatDate(endDate, "yyyy-MM-dd") + "') ");


                System.out.println("sql : " + sql.toString());
                List<String> result = this.queryForStringList(sql.toString(), domainId);
                return result;
        }

        public Map<String, Object> getEntrancesV2(int domainId, Date startDate, Date endDate, List<String> urlHashList) {
                StringBuffer sql = new StringBuffer();
                //ua_group_id =3 -- bingbot
                //ua_group_id =2 -- googlebot
                sql.append(" SELECT sumIf(entrances, (match(concat('https://', host_name, uri), 'https://www.spokeo.com/(1-)?[0-9]{3}-[0-9]{3}-[0-9]{3}(2|5|8|0)'))) as enAD, ");
                sql.append("        sumIf(entrances, (match(concat('https://', host_name, uri), 'https://www.spokeo.com/(1-)?[0-9]{3}-[0-9]{3}-[0-9]{3}(3|6|9|1)'))) as enEK, ");
                sql.append("        sumIf(entrances, (match(concat('https://', host_name, uri), 'https://www.spokeo.com/(1-)?[0-9]{3}-[0-9]{3}-[0-9]{3}(4|7)'))) as enLP ");
                sql.append(" FROM dis_ga ");
                sql.append(" WHERE (domain_id = " + domainId+ ")  ");
                sql.append(" AND (lower(source) = 'bing')  ");
                sql.append(" AND versoin = dictGetUInt16('claritydbVersion', 'versioning', (2, toUInt32(domain_id), log_date, toUInt64(0)))  ");
                sql.append(" AND (log_date = '" + FormatUtils.formatDate(endDate, "yyyy-MM-dd") +"')  ");
                sql.append(" AND (match(concat('https://', host_name, uri), 'https://www.spokeo.com/(1-)?[0-9]{3}-[0-9]{3}-[0-9]{3}(2|5|8|0)')  ");
                sql.append("    OR match(concat('https://', host_name, uri), 'https://www.spokeo.com/(1-)?[0-9]{3}-[0-9]{3}-[0-9]{3}(3|6|9|1)')   ");
                sql.append("    OR match(concat('https://', host_name, uri), 'https://www.spokeo.com/(1-)?[0-9]{3}-[0-9]{3}-[0-9]{3}(4|7)'))   ");
                sql.append(" AND (URLHash(lowerUTF8(concat('https://', host_name, uri))) in (" + getStringListQueryParam(urlHashList) + ")) ");

                System.out.println("sql : " + sql.toString());
                List<Map<String, Object>> result = this.queryForMapList(sql.toString());
                if (CollectionUtils.isNotEmpty(result) && result.size() > 0) {
                        return result.get(0);
                }
                return null;
        }

        public Map<String, Object> getDailyExtractPhonePrefix(int domainId, Date startDate, Date endDate) {
                StringBuffer sql = new StringBuffer();
//

                sql.append(" SELECT sumIf(entrances, match(concat('https://', host_name, uri), 'https://www.spokeo.com/[0-9]{3}-[0-9]{2}(3|6|8|9)($|/)') ");
                sql.append(" OR match(decodeURLComponent(concat('https://', host_name, uri)), 'https://www.spokeo.com/[0-9]{3}-[0-9]{2}(3|6|8|9)($|/)')) as en1,");
                sql.append(" sumIf(entrances, match(concat('https://', host_name, uri), 'https://www.spokeo.com/[0-9]{3}-[0-9]{2}(2|4|5|7)($|/)') ");
                sql.append(" OR match(decodeURLComponent(concat('https://', host_name, uri)), 'https://www.spokeo.com/[0-9]{3}-[0-9]{2}(2|4|5|7)($|/)')) as en2");
                sql.append(" FROM dis_ga");
                sql.append(" WHERE (domain_id = " + domainId+ ") ");
                sql.append(" AND (lower(source) = 'google') ");
                sql.append(" AND versoin = dictGetUInt16('claritydbVersion', 'versioning', (2, toUInt32(domain_id), log_date, toUInt64(0))) ");
                sql.append(" AND (log_date = '" + FormatUtils.formatDate(endDate, "yyyy-MM-dd") +"') ");
                sql.append(" AND (match(concat('https://', host_name, uri), 'https://www.spokeo.com/[0-9]{3}-[0-9]{2}(3|6|8|9)($|/)') ");
                sql.append(" OR match(decodeURLComponent(concat('https://', host_name, uri)), 'https://www.spokeo.com/[0-9]{3}-[0-9]{2}(3|6|8|9)($|/)')");
                sql.append(" OR match(concat('https://', host_name, uri), 'https://www.spokeo.com/[0-9]{3}-[0-9]{2}(2|4|5|7)($|/)') ");
                sql.append(" OR match(decodeURLComponent(concat('https://', host_name, uri)), 'https://www.spokeo.com/[0-9]{3}-[0-9]{2}(2|4|5|7)($|/)')");
                sql.append(" ) ");
                sql.append(" AND (URLHash(lowerUTF8(concat('https://', host_name, uri))) GLOBAL IN");
                sql.append(" (");
                sql.append("     SELECT DISTINCT urlhash");
                sql.append("     FROM clarity_bot.dis_bot_detail");
                sql.append("     WHERE (ua_group_id = 2) AND (own_domain_id = " + domainId + ") ");
                sql.append("    AND (match(url, 'https://www.spokeo.com/[0-9]{3}-[0-9]{2}(3|6|8|9)($|/)') ");
                sql.append("    OR match(url, 'https://www.spokeo.com/[0-9]{3}-[0-9]{2}(2|4|5|7)($|/)') ");
                sql.append("    ) ");
                sql.append("    AND (bot_date >= '" + FormatUtils.formatDate(startDate, "yyyy-MM-dd") + "') AND (bot_date <= '" + FormatUtils.formatDate(endDate, "yyyy-MM-dd") + "')");
                sql.append(" ))");

                List<Map<String, Object>> result = this.queryForMapList(sql.toString());
                if (CollectionUtils.isNotEmpty(result) && result.size() > 0) {
                        return result.get(0);
                }
                return null;
        }

        public void insertForBatch(List<GoogleAnalyticsEntity> insertData, String tableName) {


	        String sql = "INSERT INTO " + tableName
	                + " (" + StringUtils.join(columns, ",") + ") VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
	        List<Object[]> batch = new ArrayList<Object[]>();
	        int i = 0;
	        for (GoogleAnalyticsEntity entity : insertData) {
	            Object[] values = new Object[]{
	                    entity.getDomainId(),
	                    entity.getLogDate(),
	                    entity.getKeywordText(),
	                    entity.getHostName(),
	                    entity.getUri(),
	                    entity.getMedium(),
	                    entity.getSource(),
	                    entity.getDevice(),
	                    entity.getVersoin(),
	                    entity.getCountry(),
	                    entity.getEntrances(),
	                    entity.getPageviews(),
	                    entity.getExits(),
	                    entity.getBounces(),
	                    entity.getSessionDuration(),
	                    entity.getTimeOnPage(),
	                    entity.getTransactions(),
	                    entity.getItemRevenue(),
	                    entity.getGoal1completions(),
	                    entity.getGoal2completions(),
	                    entity.getGoal3completions(),
	                    entity.getGoal4completions(),
	                    entity.getGoal5completions(),
	                    entity.getGoal6completions(),
	                    entity.getGoal7completions(),
	                    entity.getGoal8completions(),
	                    entity.getGoal9completions(),
	                    entity.getGoal10completions(),
	                    entity.getGoal11completions(),
	                    entity.getGoal12completions(),
	                    entity.getGoal13completions(),
	                    entity.getGoal14completions(),
	                    entity.getGoal15completions(),
	                    entity.getGoal16completions(),
	                    entity.getGoal17completions(),
	                    entity.getGoal18completions(),
	                    entity.getGoal19completions(),
	                    entity.getGoal20completions(),
	                    entity.getGoal1Value(),
	                    entity.getGoal2Value(),
	                    entity.getGoal3Value(),
	                    entity.getGoal4Value(),
	                    entity.getGoal5Value(),
	                    entity.getGoal6Value(),
	                    entity.getGoal7Value(),
	                    entity.getGoal8Value(),
	                    entity.getGoal9Value(),
	                    entity.getGoal10Value(),
	                    entity.getGoal11Value(),
	                    entity.getGoal12Value(),
	                    entity.getGoal13Value(),
	                    entity.getGoal14Value(),
	                    entity.getGoal15Value(),
	                    entity.getGoal16Value(),
	                    entity.getGoal17Value(),
	                    entity.getGoal18Value(),
	                    entity.getGoal19Value(),
	                    entity.getGoal20Value(),
	                    entity.getDataSourceType(),
	                    entity.getUrl(),
	            };
	
	            if (i++ < 5) {
	                System.out.println(new Gson().toJson(values));
	            }
	
	            batch.add(values);
	        }
	        executeBatch(sql, batch);
	    }

        public void insertForGaV4Batch(List<GoogleAnalyticsV4Entity> insertData, String tableName) {


	        String sql = "INSERT INTO " + tableName
	                + " (" + StringUtils.join(columnsV4, ",") + ") VALUES (?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, ?,?,?,?,?, ?)";
	        List<Object[]> batch = new ArrayList<Object[]>();
	        int i = 0;
	        for (GoogleAnalyticsV4Entity entity : insertData) {
	            Object[] values = new Object[]{
	                    entity.getDomainId(),
	                    entity.getLogDate(),
	                    entity.getKeywordText(),
	                    entity.getHostName(),
	                    entity.getUri(),
	                    entity.getFullPageUrl(),
	                    entity.getSessionMedium(),
	                    entity.getSessionSource(),
	                    entity.getDeviceCategory(),
	                    entity.getCountry(),
	                    entity.getEventName(),
	                    entity.getSessions(),
	                    entity.getEcommercePurchases(),
	                    entity.getTransactions(),
	                    entity.getEngagedSessions(),
	                    entity.getEngagementRate(),
	                    entity.getEventCount(),
	                    entity.getEventValue(),
	                    entity.getVersoin(),
	                    entity.getDataSourceType(),
	                    entity.getBatchNo(),
	                    entity.getGoalId(),
	                    entity.getPageTitle(),
	                    entity.getUrl(),
	                    entity.getScreenPageviews(),
	                    entity.getPlatform()
	            };
	
	            if (i++ < 5) {
	                System.out.println(new Gson().toJson(values));
	            }
	
	            batch.add(values);
	        }
	        executeBatch(sql, batch);
	    }

        public Integer checkExist(String finalTableName, Integer ownDomainId, Date trafficDate, Integer version) {
                String checkExitSql = " select count() from " + finalTableName
                                + " where log_date = '" + FormatUtils.formatDate(trafficDate, "yyyy-MM-dd")
                                + "' and versoin = " + version + " and domain_id = " + ownDomainId;


                return this.queryForInteger(checkExitSql);
        }


        public List<GoogleAnalyticsEntity> getAdobeDataByUrlList(int ownDomainId, String logDate, List<String> urlList) {
                String sql = "select domain_id, log_date, url, sum(entrances) as entrances, sum(pageviews) as pageviews, sum(bounces) as bounces " +
                                " from dis_adobe " +
                                " where domain_id = ? and log_date = ?  and  url in ('" + StringUtils.join(urlList, "','") + "') " +
                                " group by domain_id, log_date, url ";
                return findBySql(sql, ownDomainId, logDate);
        }

        public GoogleAnalyticsEntity getAdobeDataSumByUrlList(int ownDomainId, String logDate, List<String> urlList) {
                String sql = "select domain_id, log_date, sum(entrances) as entrances, sum(pageviews) as pageviews, sum(bounces) as bounces " +
                                " from dis_adobe " +
                                " where domain_id = ? and log_date = ?  and  url in ('" + StringUtils.join(urlList, "','") + "') " +
                                " group by domain_id, log_date ";
                return findObject(sql, ownDomainId, logDate);
        }


	    public List<Map<String, Object>> extractFor11938(int domainId, String logStartDate, String logEndDate, int version, String device){
	        StringBuffer sql = new StringBuffer();
	        sql.append(" SELECT       url1 as url ,      host_name1 as hostName, traffic_alias AS traffic, bounces_alias AS bounces, session_duration_alias AS session_duration, time_on_page_alias ");
	        sql.append("  AS time_on_page, pageviews_alias AS pageviews, transactions_alias AS transactions, item_revenue_alias AS item_revenue,  ");
	        sql.append("  goal1completions_alias AS goal1completions, goal1Value_alias AS goal1Value, goal2completions_alias AS goal2completions,  ");
	        sql.append("  goal2Value_alias AS goal2Value, goal3completions_alias AS goal3completions, goal3Value_alias AS goal3Value,  ");
	        sql.append("  goal4completions_alias AS goal4completions, goal4Value_alias AS goal4Value, goal5completions_alias AS goal5completions, ");
	        sql.append("  goal5Value_alias AS goal5Value, goal6completions_alias AS goal6completions, goal6Value_alias AS goal6Value, goal7completions_alias AS goal7completions, \n" +
	                " goal7Value_alias AS goal7Value, goal9completions_alias AS goal9completions, goal9Value_alias AS goal9Value, goal13completions_alias AS goal13completions, \n" +
	                " goal13Value_alias AS goal13Value, goal14completions_alias AS goal14completions, goal14Value_alias AS goal14Value, goal16completions_alias AS goal16completions, \n" +
	                " goal16Value_alias AS goal16Value, goal17completions_alias AS goal17completions, goal17Value_alias AS goal17Value, goal19completions_alias AS goal19completions, \n" +
	                " goal19Value_alias AS goal19Value, goal20completions_alias AS goal20completions, goal20Value_alias AS goal20Value, ROUND(custmetrics206335, 2) AS custmetrics206335  ");
	        sql.append(" FROM (   ");
	        sql.append(" SELECT              any( uri ) as url1 ,            \n" +
	                " \tany(host_name) as host_name1,           \n" +
	                " \tany(url_murmur_hash) as url_murmur_hashX,  uri_hash as parentUrlHash, sum(entrances) as traffic_alias, sum(bounces) as bounces_alias, \n" +
	                " \tsum(session_duration) as session_duration_alias, sum(time_on_page) as time_on_page_alias, sum(pageviews) as pageviews_alias, \n" +
	                " \tsum(transactions) as transactions_alias, sum(item_revenue) as item_revenue_alias, sum(goal1completions) as goal1completions_alias, \n" +
	                " \tsum(goal1Value) as goal1Value_alias, sum(goal2completions) as goal2completions_alias, sum(goal2Value) as goal2Value_alias, sum(goal3completions) as goal3completions_alias, \n" +
	                " \tsum(goal3Value) as goal3Value_alias, sum(goal4completions) as goal4completions_alias, sum(goal4Value) as goal4Value_alias, sum(goal5completions) as goal5completions_alias, \n" +
	                " \tsum(goal5Value) as goal5Value_alias, sum(goal6completions) as goal6completions_alias, sum(goal6Value) as goal6Value_alias, sum(goal7completions) as goal7completions_alias, \n" +
	                " \tsum(goal7Value) as goal7Value_alias, sum(goal9completions) as goal9completions_alias, sum(goal9Value) as goal9Value_alias, sum(goal13completions) as goal13completions_alias, \n" +
	                " \tsum(goal13Value) as goal13Value_alias, sum(goal14completions) as goal14completions_alias, sum(goal14Value) as goal14Value_alias, sum(goal16completions) as goal16completions_alias, \n" +
	                " \tsum(goal16Value) as goal16Value_alias, sum(goal17completions) as goal17completions_alias, sum(goal17Value) as goal17Value_alias, sum(goal19completions) as goal19completions_alias, \n" +
	                " \tsum(goal19Value) as goal19Value_alias, sum(goal20completions) as goal20completions_alias, sum(goal20Value) as goal20Value_alias, \n" +
	                " \tsum(goal1completions)+sum(goal13completions)+sum(goal14completions)+sum(goal16completions)+sum(goal17completions)+sum(goal19completions)+sum(goal2completions)\n" +
	                " \t+sum(goal20completions)+sum(goal3completions)+sum(goal4completions)+sum(goal5completions)+sum(goal6completions)+sum(goal7completions)\n" +
	                " \t+sum(goal9completions)+sum(transactions) as custmetrics206335  FROM actonia_site_analytics.dis_ga  ");
	        sql.append(" WHERE (domain_id = " + domainId + ") AND (log_date >= '" + logStartDate + "') AND (log_date <= '" + logEndDate + "') ");
	        sql.append(" AND (medium = 'organic') and device = '" + device + "'");
	        sql.append(" AND versoin = dictGetUInt16('claritydbVersion', 'versioning', (2, toUInt32(domain_id), log_date, toUInt64(0)))    ");
	        sql.append(" group by parentUrlHash  HAVING 1=1 ) t1");
	        return queryForMapList(sql.toString());
	    }
}
