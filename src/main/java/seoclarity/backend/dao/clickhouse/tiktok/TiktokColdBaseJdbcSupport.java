/**
 *
 */
package seoclarity.backend.dao.clickhouse.tiktok;

import javax.annotation.Resource;
import javax.sql.DataSource;

import seoclarity.backend.dao.BaseJdbcSupport;


/**
 * base jdbc support
 * use spring jdbcdaosupport as base class
 */
public abstract class TiktokColdBaseJdbcSupport<T> extends BaseJdbcSupport<T> {
	
	@Resource(name="tiktokBackupClarityDBDataSource")
    private DataSource dataSource;

	@Override
	public DataSource getDataSource() {
		return this.dataSource;
	}
	
	

}
