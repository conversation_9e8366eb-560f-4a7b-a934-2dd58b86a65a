package seoclarity.backend.dao.rankcheck;

import org.springframework.stereotype.Repository;
import seoclarity.backend.entity.rankcheck.RgRealtimeExpansionEntity;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository("realtimeExpansionEntityDAO")
public class RgRealtimeExpansionEntityDAO extends RankCheckBaseJdbcSupport<RgRealtimeExpansionEntity> {
    @Override
    public String getTableName() {
        return "rg_realtime_expansion";
    }

    public int insert(RgRealtimeExpansionEntity entity) {
        Map<String, Object> paramMap = new HashMap<>();

        paramMap.put("cacheBodyId", entity.getCacheBodyId());
        paramMap.put("engineId", entity.getEngineId());
        paramMap.put("languageId", entity.getLanguageId());
        paramMap.put("countryCd", entity.getCountryCd());
        paramMap.put("status", entity.getStatus());
        paramMap.put("mutualStatus", entity.getMutualStatus());
        paramMap.put("createDate", entity.getCreateDate());
        paramMap.put("createUserId", entity.getCreateUserId());
        paramMap.put("createdAt", entity.getCreatedAt());

        long longId = this.insertForLongId(paramMap);
        int id = 0;
        if (longId >= Integer.MIN_VALUE && longId <= Integer.MAX_VALUE) {
            id = (int) longId;
        }
        return id;
    }

    public void updateMutualStatusById(int id, int mutualStatus) {
        String sql = "update " + getTableName() + " set mutualStatus = ? where id = ? ";
        executeUpdate(sql, mutualStatus, id);
    }

    public void updateStatusById(int id, int status) {
        String sql = "update " + getTableName() + " set status = ? where id = ? ";
        executeUpdate(sql, status, id);
    }

    public void updateStatusAndMutualStatusById(int id, int status, int mutualStatus) {
        String sql = "update " + getTableName() + " set status = ?, mutualStatus = ? where id = ? ";
        executeUpdate(sql, status, mutualStatus, id);
    }

    public RgRealtimeExpansionEntity getByUniqueKey(long apiBodyId) {
        String sql = "select * from " + getTableName() + " where cacheBodyId = ? ";
        return findObject(sql, apiBodyId);
    }

    public List<RgRealtimeExpansionEntity> getUnfinishedExpansions() {
        String sql = "select * from " + getTableName() + " where status = ?";
        return findBySql(sql, RgRealtimeExpansionEntity.STATUS_PROCESSING);
    }

    public RgRealtimeExpansionEntity getById(int expansionId) {
        String sql = "select * from " + getTableName() + " where id = ? ";
        return findObject(sql, expansionId);
    }
}
