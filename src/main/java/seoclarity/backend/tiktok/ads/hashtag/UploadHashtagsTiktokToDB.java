package seoclarity.backend.tiktok.ads.hashtag;

import cn.hutool.core.io.FileUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import seoclarity.backend.dao.actonia.tiktok.TiktokHashtagDao;
import seoclarity.backend.entity.actonia.tiktok.TiktokHashtagEntity;
import seoclarity.backend.tiktok.ads.UploadTiktokToDB;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class UploadHashtagsTiktokToDB extends UploadTiktokToDB<TiktokHashtagEntity> {

    private static final String DEFAULT_DATA_DIR = "/disk1/scribeKeyword/ads_tiktok_hashtag";
    private final TiktokHashtagDao tiktokHashtagDao;

    public UploadHashtagsTiktokToDB(String dataDir, int crawlDate) {
        super(dataDir == null ? DEFAULT_DATA_DIR : dataDir, crawlDate);
        tiktokHashtagDao = SpringBeanFactory.getBean("tiktokHashtagDao");
    }

    public static void main(String[] args) {
        int crawlDate = Integer.parseInt(LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE));
        if (args.length > 0) {
            // if a date is passed in, use it
            crawlDate = Integer.parseInt(args[0]);
        }
        final String dataDir = args.length > 1 ? args[1] : null;
        UploadHashtagsTiktokToDB uploadKeywordsToDB = new UploadHashtagsTiktokToDB(dataDir, crawlDate);
        uploadKeywordsToDB.upload();
    }

    @Override
    protected List<TiktokHashtagEntity> readScribeFile(File file) {
        final List<TiktokHashtagVO> voList = FileUtil.readLines(file, StandardCharsets.UTF_8)
                .stream()
                .map(line -> JSONUtil.toBean(line, TiktokHashtagVO.class))
                .collect(Collectors.toList());
        List<TiktokHashtagEntity> tiktokHashtagEntities = new LinkedList<>();
        for (int i = 0; i < voList.size(); i++) {
            final TiktokHashtagVO tiktokHashtagVO = voList.get(i);
            TiktokHashtagEntity tiktokHashtagEntity = new TiktokHashtagEntity();
            try {
                tiktokHashtagEntity = tiktokHashtagVO.convertToEntity(this.crawlWeek, this.crawlDate);
            } catch (Exception e) {
                log.error("Error converting TiktokHashtagVO to TiktokHashtagEntity: {}", tiktokHashtagVO);
                if (tiktokHashtagEntity.getIndustry() == null) {
                    // if industry is null, try to set it based on the previous
                    final TiktokHashtagVO previous = voList.get(i - 1);
                    tiktokHashtagVO.setIndustryInfo(previous.getIndustryInfo());
                    tiktokHashtagEntity = tiktokHashtagVO.convertToEntity(this.crawlWeek, this.crawlDate);
                }
            }
            tiktokHashtagEntities.add(tiktokHashtagEntity);
        }
        return tiktokHashtagEntities;
    }

    @Override
    protected void batchInsert(List<TiktokHashtagEntity> needUploadList) {
        this.tiktokHashtagDao.batchInsert(needUploadList);
    }

}
