package seoclarity.backend.pixel.crawl.service;

import com.alibaba.fastjson.JSON;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.ecs.model.v20140526.*;
import com.amazonaws.services.sqs.AmazonSQS;
import com.google.gson.JsonObject;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import seoclarity.backend.sender.pagespeed.SendPageSpeedTargetUrlsSQS;
import seoclarity.backend.utils.amazon.SQSUtils;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

public class CallerApiService {

    private static final String SQS_NAME_D = "SQS_IN_PIXEL_WEEKLY_DESKTOP";
    private static final String SQS_NAME_M = "SQS_IN_PIXEL_WEEKLY_MOBILE";
    private static final int PAGE_SIZE = 100;
    private String mobileQueueUrl;
    private String desktopQueueUrl;
    private ApiCaller caller;

    public CallerApiService() {
        caller = new ApiCaller();
    }

    public DescribeAvailableResourceResponse getAvailableResource(InstanceParam param) {
        DescribeAvailableResourceRequest request = new DescribeAvailableResourceRequest();
        // 通过DescribeRegionsRequest获取地域ID。
        request.setRegionId(param.getRegionId());
        request.setDestinationResource(param.getDestinationResource());
        request.setInstanceChargeType(param.getChargeType());
        request.setSpotStrategy(param.getSpotStrategy());
        request.setZoneId(param.getZoneId());
        request.setInstanceType(param.getInstanceType());
        request.setSystemDiskCategory(param.getSystemDiskCategory());
        request.setNetworkCategory(param.getNetworkCategory());
        request.setResourceType(param.getResourceType());

        DescribeAvailableResourceResponse response = caller.doAction(request);
        System.out.println(param.getInstanceType() + "的库存:" + JSON.toJSONString(response));
        return response;
    }

    public RunInstancesResponse batchCreateInstance(InstanceParam param) {
        RunInstancesRequest request = new RunInstancesRequest();
        request.setZoneId(param.getZoneId());
        request.setRegionId(param.getRegionId());
        request.setInstanceType(param.getInstanceType());
        request.setImageId(param.getImageId());
        request.setSpotDuration(param.getSpotDuration());
        request.setSpotPriceLimit(param.getSpotPriceLimit());
        request.setSpotStrategy(param.getSpotStrategy());
        request.setInstanceChargeType(param.getChargeType());
        request.setSystemDiskCategory(param.getSystemDiskCategory());
        request.setSystemDiskSize(String.valueOf(param.getSystemDiskSize()));
        request.setSecurityGroupId(param.getSecurityGroupId());
        request.setVSwitchId(param.getVSwitchId());
        request.setInternetMaxBandwidthOut(param.getInternetMaxBandwidthOut());
        request.setClientToken(UUID.randomUUID().toString());

        /*RunInstancesRequest.NetworkInterface networkInterface = new RunInstancesRequest.NetworkInterface();
        networkInterface.setVSwitchId(param.getVSwitchId());
        networkInterface.setNetworkInterfaceName(param.getNetworkInterfaceName());
        request.setNetworkInterfaces(Collections.singletonList(networkInterface));*/

        request.setKeyPairName(param.getKeyPairName());
        request.setUniqueSuffix(true);
        request.setInstanceName(param.getInstanceName());
        request.setHostName(param.getHostName());
        request.setAmount(param.getAmount());
        String userData = param.getUserData();
        String encodedUserData = Base64.getEncoder().encodeToString(userData.getBytes(StandardCharsets.UTF_8));
        request.setUserData(encodedUserData);
        RunInstancesResponse runInstancesResponse = caller.doAction(request);
        System.out.println("===batchCreateInstance:" + JSON.toJSONString(runInstancesResponse));
        return runInstancesResponse;
    }

    public float getLatestPrice(InstanceParam param) {
        float tradePrice = 0.0F;
        // 设置DescribePrice参数，并向DescribePrice发送请求。
        DescribePriceRequest request = new DescribePriceRequest();
        request.setRegionId(param.getRegionId());
        request.setResourceType(param.getResourceType());
        request.setInstanceType(param.getInstanceType());
        request.setInstanceNetworkType(param.getNetworkType());
        request.setSystemDiskCategory(param.getSystemDiskCategory());
        request.setSystemDiskSize(param.getSystemDiskSize());
        request.putQueryParameter("spotStrategy",param.getSpotStrategy());
        request.putQueryParameter("spotDuration",param.getSpotDuration());
        request.putQueryParameter("zoneId",param.getZoneId());
        // 接收调用的返回结果，并输出查询到的抢占式实例当前最新价格。
        DescribePriceResponse describePriceResponse = caller.doAction(request);
        if (describePriceResponse != null) {
            DescribePriceResponse.PriceInfo.Price price = describePriceResponse.getPriceInfo().getPrice();
            System.out.println(param.getInstanceType() + "的抢占式实例价格：" + price.getTradePrice() + " " + price.getCurrency());
            tradePrice = price.getTradePrice();
        }
        return tradePrice;
    }

    private String getImagesId(InstanceParam param) {
        String imageId = "";
        DescribeImagesRequest request = new DescribeImagesRequest();
        request.setRegionId(param.getRegionId());
        request.setInstanceType(param.getInstanceType());
        request.setImageName(param.getImageName());
        DescribeImagesResponse describeImagesResponse = caller.doAction(request);
        if (describeImagesResponse != null) {
            List<DescribeImagesResponse.Image> images = describeImagesResponse.getImages();
            if (images != null && !images.isEmpty()) {
                System.out.println("===queryImages obj:" + JSON.toJSONString(images));
                DescribeImagesResponse.Image image = images.get(0);
                imageId = image.getImageId();
                System.out.println("===imageInfo size:" + images.size() + " imageId:" + imageId);
            }
        }
        return imageId;
    }

    private InstanceParam getInstanceParam(int amount, int processType) {
        Properties prop = new Properties();
        try {
            prop.load(SendPageSpeedTargetUrlsSQS.class.getResourceAsStream("/preemptibleInstances.properties"));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        InstanceParam param = new InstanceParam();
        String regionId = (String) prop.get("regionId");
        String instanceType = (String) prop.get("instanceType");
        String zoneId = (String) prop.get("zoneId");
        String securityGroupId = (String) prop.get("securityGroupId");
        String vSwitchId = (String) prop.get("vSwitchId");
        String systemDiskCategory = (String) prop.get("systemDiskCategory");
        int systemDiskSize = Integer.parseInt((String) prop.get("systemDiskSize"));
        String chargeType = (String) prop.get("chargeType");
        String spotStrategy = (String) prop.get("spotStrategy");
        String networkCategory = (String) prop.get("networkCategory");
        String networkType = (String) prop.get("networkType");
        String networkInterfaceName = (String) prop.get("networkInterfaceName");
        String keyPairName = (String) prop.get("KeyPairName");
        String destinationResource = (String) prop.get("destinationResource");
        int InternetMaxBandwidthIn = Integer.parseInt((String) prop.get("InternetMaxBandwidthIn"));
        int InternetMaxBandwidthOut = Integer.parseInt((String) prop.get("InternetMaxBandwidthOut"));
        float spotPriceLimit = Float.parseFloat((String) prop.get("spotPriceLimit"));

        String imageName = (String) prop.get("imageName");
        String hostName = (String) prop.get("HostName");
        String instanceName = (String) prop.get("instanceName");
        if (processType == 2) {
            imageName = (String) prop.get("imageName_v2");
            hostName = (String) prop.get("HostName_v2");
            instanceName = (String) prop.get("instanceName_v2");
        }

        param.setInstanceType(instanceType);
        param.setRegionId(regionId);
        param.setZoneId(zoneId);
        param.setNetworkCategory(networkCategory);
        param.setNetworkType(networkType);
        param.setSecurityGroupId(securityGroupId);
        param.setImageName(imageName);
        param.setVSwitchId(vSwitchId);
        param.setSystemDiskCategory(systemDiskCategory);
        param.setSystemDiskSize(systemDiskSize);
        param.setChargeType(chargeType);
        param.setSpotStrategy(spotStrategy);
        param.setSpotPriceLimit(spotPriceLimit);
        param.setInternetMaxBandwidthIn(InternetMaxBandwidthIn);
        param.setInternetMaxBandwidthOut(InternetMaxBandwidthOut);
        param.setNetworkInterfaceName(networkInterfaceName);
        param.setKeyPairName(keyPairName);
        param.setHostName(hostName);
        param.setInstanceName(instanceName);
        param.setDestinationResource(destinationResource);
        param.setAmount(amount);
        return param;
    }

    public InstanceParam generateInstanceParam(boolean isTest, int processType) {
        int amount;
        if (isTest) {
            amount = 20;
        } else {
            amount = 1;
        }
        InstanceParam instanceParam = getInstanceParam(amount, processType);
        String imagesId = getImagesId(instanceParam);
        if (StringUtils.isBlank(imagesId)) {
            System.out.println("imageId is null");
            return null;
        }
        instanceParam.setImageId(imagesId);
        return instanceParam;
    }

    public DescribeInstanceStatusResponse getInstanceStatus(InstanceParam param, List<String> instanceIdList) {
        DescribeInstanceStatusRequest request = new DescribeInstanceStatusRequest();
        request.setInstanceIds(instanceIdList);
        request.setRegionId(param.getRegionId());
        request.setZoneId(param.getZoneId());
        request.setPageSize(instanceIdList.size());
        DescribeInstanceStatusResponse describeInstanceStatusResponse = caller.doAction(request);
        System.out.println("===statusJson:" + JSON.toJSONString(describeInstanceStatusResponse));
        return describeInstanceStatusResponse;
    }

    public StopInstancesResponse batchStopInstances(InstanceParam param, List<String> instanceIdList) {
        StopInstancesRequest request = new StopInstancesRequest();
        request.setInstanceIds(instanceIdList);
        request.setRegionId(param.getRegionId());
        request.setStoppedMode("StopCharging");
        request.setBatchOptimization("SuccessFirst");
        StopInstancesResponse stopInstancesResponse = caller.doAction(request);
        System.out.println("===stopJson: " + JSON.toJSONString(stopInstancesResponse));
        return stopInstancesResponse;
    }

    public DeleteInstancesResponse batchDeleteInstance(InstanceParam param, List<String> instanceIdList) {
        DeleteInstancesRequest request = new DeleteInstancesRequest();
        request.setRegionId(param.getRegionId());
        request.setInstanceIds(instanceIdList);
        request.setClientToken(UUID.randomUUID().toString());
        DeleteInstancesResponse deleteInstancesResponse = caller.doAction(request);
        System.out.println("===deleteJson:" + JSON.toJSONString(deleteInstancesResponse));
        return deleteInstancesResponse;
    }

    public int getRunningInstanceNum(InstanceParam param) {
        DescribeInstancesRequest request = new DescribeInstancesRequest();
        request.setRegionId(param.getRegionId());
        request.setVSwitchId(param.getVSwitchId());
        request.setZoneId(param.getZoneId());
        request.setInstanceType(param.getInstanceType());
        request.setInstanceNetworkType(param.getNetworkType());
        request.setImageId(param.getImageId());
        request.setKeyPairName(param.getKeyPairName());
        request.setSecurityGroupId(param.getSecurityGroupId());
        request.setInstanceChargeType(param.getChargeType());
//        request.setStatus("Running");
        DescribeInstancesResponse describeInstancesResponse = caller.doAction(request);
        Integer totalCount = describeInstancesResponse.getTotalCount();
//        System.out.println(JSON.toJSONString(describeInstancesResponse));
        System.out.println("===getRunningInstanceNum totalCount:" + totalCount + " instance:" + param.getInstanceType());
        return totalCount;
    }

    public List<String> getInstanceIdList(InstanceParam param) {
        DescribeInstancesRequest request = new DescribeInstancesRequest();
        request.setRegionId(param.getRegionId());
        request.setVSwitchId(param.getVSwitchId());
        request.setZoneId(param.getZoneId());
        request.setInstanceType(param.getInstanceType());
        request.setInstanceNetworkType(param.getNetworkType());
        request.setImageId(param.getImageId());
        request.setKeyPairName(param.getKeyPairName());
        request.setSecurityGroupId(param.getSecurityGroupId());
        request.setInstanceChargeType(param.getChargeType());
        request.setPageSize(PAGE_SIZE);
        request.setPageNumber(param.getPage());
//        request.setStatus("Running");
        DescribeInstancesResponse describeInstancesResponse = caller.doAction(request);
        if (describeInstancesResponse == null) {
            return null;
        }
        return describeInstancesResponse.getInstances().stream().map(DescribeInstancesResponse.Instance::getInstanceId).collect(Collectors.toList());
    }

    public void getMsgInQueue(int processType) {
        AmazonSQS amazonSQS = SQSUtils.getAmazonSQS();
        if (processType == 1) {
            desktopQueueUrl = SQSUtils.createQueue(SQS_NAME_D, amazonSQS);
            mobileQueueUrl = SQSUtils.createQueue(SQS_NAME_M, amazonSQS);
        } else {
            desktopQueueUrl = SQSUtils.createQueue("FPR_US_COMMON_PIXEL_V2", amazonSQS);
            mobileQueueUrl = SQSUtils.createQueue("FPR_US_COMMON_PIXEL_MOBILE_V2", amazonSQS);
        }

        Map<String, Integer> map = SQSUtils.getMessagesAvailableAndMessageInFilght(amazonSQS, desktopQueueUrl);
        int inFlight = map.get("MESSAGES_IN_FLIGHT");
        int kwCnt = map.get("MESSAGES_AVAILABLE");
        System.out.println("===getMsgInQueueDesktop kwCnt:" + kwCnt + " inFlight:" + inFlight + " crawlType:" + getCrawlTypeName(processType));
        Map<String, Integer> mapM = SQSUtils.getMessagesAvailableAndMessageInFilght(amazonSQS, mobileQueueUrl);
        int inFlightM = mapM.get("MESSAGES_IN_FLIGHT");
        int kwCntM = mapM.get("MESSAGES_AVAILABLE");
        System.out.println("===getMsgInQueueMobile kwCnt:" + kwCntM + " inFlight:" + inFlightM + " crawlType:" + getCrawlTypeName(processType));
    }

    public void getMsgInOutQueue(int processType) { // 1 us 2 intl
        AmazonSQS amazonSQS = SQSUtils.getAmazonSQS();
        if (processType == 1) {
            desktopQueueUrl = SQSUtils.createQueue("SQS_OUT_PIXEL_GOOGLE_DESKTOP_US", amazonSQS);
            mobileQueueUrl = SQSUtils.createQueue("SQS_OUT_PIXEL_GOOGLE_MOBILE_US", amazonSQS);
        } else {
            desktopQueueUrl = SQSUtils.createQueue("SQS_OUT_PIXEL_GOOGLE_DESKTOP_INTL", amazonSQS);
            mobileQueueUrl = SQSUtils.createQueue("SQS_OUT_PIXEL_GOOGLE_MOBILE_INTL", amazonSQS);
        }

        Map<String, Integer> map = SQSUtils.getMessagesAvailableAndMessageInFilght(amazonSQS, desktopQueueUrl);
        int inFlight = map.get("MESSAGES_IN_FLIGHT");
        int kwCnt = map.get("MESSAGES_AVAILABLE");
        System.out.println("===getMsgInQueueDesktop kwCnt:" + kwCnt + " inFlight:" + inFlight + " processType:" + getTypeName(processType));
        Map<String, Integer> mapM = SQSUtils.getMessagesAvailableAndMessageInFilght(amazonSQS, mobileQueueUrl);
        int inFlightM = mapM.get("MESSAGES_IN_FLIGHT");
        int kwCntM = mapM.get("MESSAGES_AVAILABLE");
        System.out.println("===getMsgInQueueMobile kwCnt:" + kwCntM + " inFlight:" + inFlightM + " processType:" + getTypeName(processType));
    }

    private String getTypeName(int processType) {
        if (processType == 1) {
            return "us";
        } else {
            return "intl";
        }
    }

    private String getCrawlTypeName(int processType) {
        if (processType == 1) {
            return "VS 3";
        } else {
            return "VS 2";
        }
    }

    public void getDailyMsgInQueue(int processType) { // 1 us 2 intl
        AmazonSQS amazonSQS = SQSUtils.getAmazonSQS();
        if (processType == 1) {
            desktopQueueUrl = SQSUtils.createQueue("SQS_OUT_PIXEL3_GOOGLE_DESKTOP_US", amazonSQS);
            mobileQueueUrl = SQSUtils.createQueue("SQS_OUT_PIXEL3_GOOGLE_MOBILE_US", amazonSQS);
        } else {
            desktopQueueUrl = SQSUtils.createQueue("SQS_OUT_PIXEL3_GOOGLE_DESKTOP_INTL", amazonSQS);
            mobileQueueUrl = SQSUtils.createQueue("SQS_OUT_PIXEL3_GOOGLE_MOBILE_INTL", amazonSQS);
        }

        Map<String, Integer> map = SQSUtils.getMessagesAvailableAndMessageInFilght(amazonSQS, desktopQueueUrl);
        int inFlight = map.get("MESSAGES_IN_FLIGHT");
        int kwCnt = map.get("MESSAGES_AVAILABLE");
        System.out.println("===getMsgInQueueDesktop kwCnt:" + kwCnt + " inFlight:" + inFlight + " processType:" + getTypeName(processType));
        Map<String, Integer> mapM = SQSUtils.getMessagesAvailableAndMessageInFilght(amazonSQS, mobileQueueUrl);
        int inFlightM = mapM.get("MESSAGES_IN_FLIGHT");
        int kwCntM = mapM.get("MESSAGES_AVAILABLE");
        System.out.println("===getMsgInQueueMobile kwCnt:" + kwCntM + " inFlight:" + inFlightM + " processType:" + getTypeName(processType));
    }

    public void getDailyMsgInOutQueue(int processType) { // 1 us 2 intl
        AmazonSQS amazonSQS = SQSUtils.getAmazonSQS();
        if (processType == 1) {
            desktopQueueUrl = SQSUtils.createQueue("SQS_UPLOAD_PIXEL3_GOOGLE_DESKTOP_US", amazonSQS);
            mobileQueueUrl = SQSUtils.createQueue("SQS_UPLOAD_PIXEL3_GOOGLE_MOBILE_US", amazonSQS);
        } else {
            desktopQueueUrl = SQSUtils.createQueue("SQS_OUT_PIXEL3_GOOGLE_DESKTOP_INTL", amazonSQS);
            mobileQueueUrl = SQSUtils.createQueue("SQS_UPLOAD_PIXEL3_GOOGLE_MOBILE_INTL", amazonSQS);
        }

        Map<String, Integer> map = SQSUtils.getMessagesAvailableAndMessageInFilght(amazonSQS, desktopQueueUrl);
        int inFlight = map.get("MESSAGES_IN_FLIGHT");
        int kwCnt = map.get("MESSAGES_AVAILABLE");
        System.out.println("===getMsgInQueueDesktop kwCnt:" + kwCnt + " inFlight:" + inFlight + " processType:" + getTypeName(processType));
        Map<String, Integer> mapM = SQSUtils.getMessagesAvailableAndMessageInFilght(amazonSQS, mobileQueueUrl);
        int inFlightM = mapM.get("MESSAGES_IN_FLIGHT");
        int kwCntM = mapM.get("MESSAGES_AVAILABLE");
        System.out.println("===getMsgInQueueMobile kwCnt:" + kwCntM + " inFlight:" + inFlightM + " processType:" + getTypeName(processType));
    }


    public static void main(String[] args) throws Exception {
        CallerApiService callerApiService = new CallerApiService();
        /*callerApiService.getDailyMsgInQueue(1);
        callerApiService.getDailyMsgInQueue(2);
        System.out.println("+===============++++++++++++++============++++++++++==========+++++++++++=====");
        callerApiService.getDailyMsgInOutQueue(1);
        callerApiService.getDailyMsgInOutQueue(2);
        System.out.println("+===============++++++++++++++============++++++++++==========+++++++++++=====");*/
//        callerApiService.getMsgInQueue(2);
        /*System.out.println("+===============++++++++++++++============++++++++++==========+++++++++++=====");
        callerApiService.getMsgInQueue(2);
        System.out.println("+===============++++++++++++++============++++++++++==========+++++++++++=====");*/
        InstanceParam param = callerApiService.getInstanceParam(1, 1);
        String imagesId = callerApiService.getImagesId(param);
        param.setImageId(imagesId);
        float latestPrice = callerApiService.getLatestPrice(param);
        callerApiService.batchCreateInstance(param);
//        int runningInstanceNum = callerApiService.getRunningInstanceNum(param);
//        callerApiService.mulutiV2Crawl();
//        callerApiService.mulutiStopCrawl();
//        callerApiService.getRunningInstanceNum(param);
        callerApiService.getAvailableResource(param);
//        List<String> list = Arrays.asList("i-0xi72gvjx3za8295dp4d", "i-0xi72gvjx3za8295dp4e");
//        callerApiService.getInstanceStatus(param, list);
//        callerApiService.batchStopInstances(param, list);
//        TimeUnit.SECONDS.sleep(10);
//        callerApiService.getInstanceStatus(param, list);
//        callerApiService.batchDeleteInstance(param, list);
//        TimeUnit.SECONDS.sleep(10);
//        callerApiService.getInstanceStatus(param, list);
        /*String imagesId = callerApiService.getImagesId(param);
        if (StringUtils.isBlank(imagesId)) {
            System.out.println("imageId is null");
            return;
        }
        param.setImageId(imagesId);
        callerApiService.batchCreateInstance(param);*/
    }

    private void mulutiStopCrawl() throws Exception {
        InstanceParam instanceParam = generateInstanceParam(false, 1);
        List<String> IdList = FileUtils.readLines(new File("/Users/<USER>/Desktop/d_20240218.txt"));
        List<List<String>> lists = splitList(IdList, 100);
        List<String> delList = new ArrayList<>();
        for (List<String> list : lists) {
            StopInstancesResponse stopInstancesResponse = batchStopInstances(instanceParam, list);
            for (StopInstancesResponse.InstanceResponse instanceRespons : stopInstancesResponse.getInstanceResponses()) {
                String message = instanceRespons.getMessage();
                if (!StringUtils.containsIgnoreCase(message, "The specified InstanceId does not exist")) {
                    if (StringUtils.containsIgnoreCase(instanceRespons.getCurrentStatus(), "Stopped")) {
                        delList.add(instanceRespons.getInstanceId());
                    }
                    System.out.println(message + " currentStatus:" + instanceRespons.getCurrentStatus() + " previousStatus:" + instanceRespons.getPreviousStatus());
                }
            }
        }

        System.out.println("need delete list size:" + delList.size());

        List<List<String>> lists1 = splitList(delList, 100);
        for (List<String> list : lists1) {
            batchDeleteInstance(instanceParam, list);
        }
    }

    private void mulutiV2Crawl() {
        InstanceParam instanceParam = getInstanceParam(1, 2);
        String imagesId = getImagesId(instanceParam);
        if (StringUtils.isBlank(imagesId)) {
            System.out.println("imageId is null");
            return;
        }
        instanceParam.setImageId(imagesId);

        float latestPrice = getLatestPrice(instanceParam);
        latestPrice += 0.002;

        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("spotInstance", true);

        /*jsonObject.addProperty("threadCount", 1);
        jsonObject.addProperty("keywordQueueName", "FPR_US_COMMON_PIXEL_V2");
        jsonObject.addProperty("keywordOutQueueName", "FPR_US_COMMON_PIXEL_V2_OUT");
        jsonObject.addProperty("provider", "ec2-pixel-desktop");
        instanceParam.setUserData(jsonObject.toString());
        RunInstancesResponse runInstancesResponse1 = batchCreateInstance(instanceParam);
        System.out.println("run1:" + runInstancesResponse1);*/


        jsonObject.addProperty("threadCount", 1);
        jsonObject.addProperty("keywordQueueName", "FPR_US_COMMON_PIXEL_MOBILE_V2");
        jsonObject.addProperty("keywordOutQueueName", "FPR_US_COMMON_PIXEL_MOBILE_V2_OUT");
        jsonObject.addProperty("provider", "ec2-pixel-mobile");
        instanceParam.setUserData(jsonObject.toString());
        RunInstancesResponse runInstancesResponse2 = batchCreateInstance(instanceParam);
        System.out.println("run2:" + runInstancesResponse2);
    }

    public <T> List<List<T>> splitList(List<T> originalList, int batchSize) {
        List<List<T>> batches = new ArrayList<>();
        int fromIndex = 0;

        while (fromIndex < originalList.size()) {
            int toIndex = Math.min(fromIndex + batchSize, originalList.size());
            batches.add(originalList.subList(fromIndex, toIndex));
            fromIndex = toIndex;
        }

        return batches;
    }
}
