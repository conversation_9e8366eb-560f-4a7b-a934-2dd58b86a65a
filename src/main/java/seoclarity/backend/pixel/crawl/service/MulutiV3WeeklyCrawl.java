package seoclarity.backend.pixel.crawl.service;

import com.aliyuncs.ecs.model.v20140526.DescribeAvailableResourceResponse;
import com.aliyuncs.ecs.model.v20140526.RunInstancesResponse;
import com.amazonaws.services.sqs.AmazonSQS;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import seoclarity.backend.entity.actoniamonitor.RankQcInfoEntity;
import seoclarity.backend.utils.DateUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.amazon.SQSUtils;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Slf4j
public class MulutiV3WeeklyCrawl {

    private static final String PARENT_FILE_PATH = "/Users/<USER>/Desktop/check_issue";
    private static final String SQS_NAME_D = "SQS_IN_PIXEL_WEEKLY_DESKTOP";
    private static final String SQS_NAME_M = "SQS_IN_PIXEL_WEEKLY_MOBILE";
//    private static final String filePath = "/home/<USER>/source/radeL/bot_project/tmp_file/pixel_id_list/idList.txt";
    private static final String filePath = "/Users/<USER>/Desktop/instance/idList12.txt";
    private static final CallerApiService callerApiService = new CallerApiService();
    private static final String[] instanceNameArr = {"ecs.t6-c1m1.large","ecs.n1.small","ecs.n4.small","ecs.mn4.small","ecs.n4.large","ecs.sn1.medium","ecs.hfc5.large","ecs.u1-c1m2.large"};
//    private static final String[] instanceNameArr = {"ecs.n1.small"};
    private final AmazonSQS amazonSQS = SQSUtils.getAmazonSQS();;
    private static final int limitNum = 1000;
    private int currentNum = 0;
    private int rankDate = 20240331;
    private String mobileQueueUrl;
    private String desktopQueueUrl;
    private float addPrice = 0.01f;


    private boolean isTest; // true: test false: prod
    private int processType; // 1: new pixel crawl 2: old pixel crawl
    private Map<String, String> instanceIdFileMap;
    private InstanceParam instanceParamCommon;


    public MulutiV3WeeklyCrawl() {
    }

    public static void main(String[] args) {
        new MulutiV3WeeklyCrawl().queueCheck();
    }

    private void process() {

        desktopQueueUrl = SQSUtils.createQueue(SQS_NAME_D, amazonSQS);
        mobileQueueUrl = SQSUtils.createQueue(SQS_NAME_M, amazonSQS);

//        startCrawl();
        startCrawl2();
    }

    private void queueCheck() {
//        check();
        callerApiService.getMsgInQueue(1);
        callerApiService.getMsgInOutQueue(1);
        callerApiService.getMsgInOutQueue(2);
//            TimeUnit.MINUTES.sleep(1);
        /*callerApiService.getDailyMsgInQueue(1);
        System.out.println("=================");
        callerApiService.getDailyMsgInQueue(2);
        System.out.println("++++++++++++++++++++++++++++++++++++++++++++++");
        callerApiService.getDailyMsgInOutQueue(1);
        System.out.println("=================");
        callerApiService.getDailyMsgInOutQueue(2);*/
        /*System.out.println("=============");
        callerApiService.getMsgInOutQueue(1);
        System.out.println("=============");
        callerApiService.getMsgInOutQueue(2);*/
//        initParam();
//        getRunningInstanceNum("d");
//        getRunningInstanceNum("m");
    }

    private void initParam() {
        isTest = false;
        processType = 1;

        System.out.println("===startParam: isTest:" + isTest + " processType:" + processType + " time:" + getTime());

        int createCount = 0;
        while (true) {
            instanceParamCommon = callerApiService.generateInstanceParam(isTest, processType);
            if (instanceParamCommon != null) {
                break;
            } else {
                createCount++;
                if (createCount > 2) {
                    break;
                }
            }
        }
        if (instanceParamCommon == null) {
            System.out.println("instanceParamCommon is null");
            String subject = "Instance parameter creation failed";
            String content = "Failed to construct parameters for starting the crawler instance.";
            throw new RuntimeException("instanceParamCommon is null");
        }

        desktopQueueUrl = SQSUtils.createQueue(SQS_NAME_D, amazonSQS);
        mobileQueueUrl = SQSUtils.createQueue(SQS_NAME_M, amazonSQS);

        instanceIdFileMap = new HashMap<>();
        Date lastSunday = DateUtils.getLastSunday(new Date());
        int rankDate = Integer.parseInt(FormatUtils.formatDate(lastSunday, "yyyyMMdd"));

        for (int i = 0; i < instanceNameArr.length; i++) {
            String desktopFilePath = PARENT_FILE_PATH + "/" + RankQcInfoEntity.DEVICE_DESKTOP + "_" + i + "_" + rankDate + ".txt";
            instanceIdFileMap.put(RankQcInfoEntity.DEVICE_DESKTOP + i, desktopFilePath);

            String mobileFilePath = PARENT_FILE_PATH + "/" + RankQcInfoEntity.DEVICE_MOBILE + "_" + i + "_" + rankDate + ".txt";
            instanceIdFileMap.put(RankQcInfoEntity.DEVICE_MOBILE + i, mobileFilePath);

            /*String desktopFileOldPath = PARENT_FILE_PATH + "/" + RankQcInfoEntity.DEVICE_DESKTOP + "_" + i + "_old_" + rankDate + ".txt";
            instanceIdFileMap.put(RankQcInfoEntity.DEVICE_DESKTOP + i + "_old", desktopFileOldPath);

            String mobileFileOldPath = PARENT_FILE_PATH + "/" + RankQcInfoEntity.DEVICE_MOBILE + "_" + i + "_old_" + rankDate + ".txt";
            instanceIdFileMap.put(RankQcInfoEntity.DEVICE_MOBILE + i + "_old", mobileFileOldPath);*/
        }
    }

    private int getRunningInstanceNum(String device) {
        int resultNum = 0;
        for (int index = 0; index < instanceNameArr.length; index++) {
            String instanceType = instanceNameArr[index];
            instanceParamCommon.setInstanceType(instanceType);
            int totalCount = callerApiService.getRunningInstanceNum(instanceParamCommon);
            if (totalCount <= 0) {
                log.info("==getRunningInstanceNum processType:{} device:{} totalCount:0", processType, device);
                continue;
            }
            int totalPage = 0;
            if (totalCount % 100 == 0) {
                totalPage = totalCount / 100;
            } else {
                totalPage = totalCount / 100 + 1;
            }
            Set<String> runInstanceIdSet = new HashSet<>();
            for (int i = 1; i <= totalPage; i++) {
                instanceParamCommon.setPage(i);
                List<String> instanceIdList = callerApiService.getInstanceIdList(instanceParamCommon);
                if (instanceIdList != null && !instanceIdList.isEmpty()) {
                    runInstanceIdSet.addAll(instanceIdList);
                }
            }
            String instanceIdFilePath;
            if (processType == 1) {
                instanceIdFilePath = instanceIdFileMap.get(device + index);
            } else {
                instanceIdFilePath = instanceIdFileMap.get(device + index + "_old");
            }
            List<String> currentInstanceIdList = null;
            File file = new File(instanceIdFilePath);
            if (!file.exists()) {
                continue;
            }
            try {
                currentInstanceIdList = FileUtils.readLines(file);
            } catch (IOException e) {
                log.info("===readLinesError filePath:{} processType:{} device:{} error:{}", instanceIdFilePath, processType, device, e.getMessage());
                e.printStackTrace();
            }

            if (currentInstanceIdList == null || currentInstanceIdList.isEmpty()) {
                log.info("==currentInstanceIdList is empty processType:{} device:{} totalCount:{} totalPage:{} filePath{}", processType, device, totalCount, totalPage, instanceIdFilePath);
                continue;
            }
            long count = 0;
            for (String s : currentInstanceIdList) {
                if (runInstanceIdSet.contains(s)) {
                    count++;
                }
            }
//            count = currentInstanceIdList.stream().filter(runInstanceIdSet::contains).count();
            resultNum += (int) count;
            log.info("==getRunningInstanceNum processType:{} device:{} totalCount:{} totalPage:{} currentInstanceNum:{} filePath{}", processType, device, totalCount, totalPage, count, instanceIdFilePath);
            instanceParamCommon.setPage(0);
        }
        return resultNum;
    }

    private void check() {
        for (int i = 0; i < instanceNameArr.length; i++) {
            InstanceParam instanceParam = callerApiService.generateInstanceParam(true, 1);
            if (StringUtils.equalsIgnoreCase(instanceNameArr[i], "ecs.u1-c1m2.large")) {
                instanceParam.setSystemDiskCategory("cloud_auto");
            }
            instanceParam.setInstanceType(instanceNameArr[i]);
            float latestPrice = callerApiService.getLatestPrice(instanceParam);
            int runningInstanceNum = callerApiService.getRunningInstanceNum(instanceParam);
            callerApiService.getAvailableResource(instanceParam);
            currentNum += runningInstanceNum;
        }
        System.out.println("=================" + currentNum + "===============");
    }

    private void startCrawl() {
        while (true) {
            for (int i = 0; i < instanceNameArr.length; i++) {
                mulutiV3Crawl(2, instanceNameArr[i]);
            }
            System.out.println("===currentTotal:" + currentNum);
            if (currentNum >= limitNum || checkStop("m")) {
                currentNum = 0;
                break;
            }
            callerApiService.getMsgInQueue(1);
            System.out.println("=========================================="+ currentNum + "==============================================");
            currentNum = 0;
            try {
                TimeUnit.SECONDS.sleep(1);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
        System.out.println("===endMobileCrawl");
    }

    private void startCrawl2() {
        System.out.println("====start desktop crawl====");
        while (true) {
            for (int i = 0; i < instanceNameArr.length; i++) {
                mulutiV3Crawl(1, instanceNameArr[i]);
            }
            System.out.println("===currentTotal:" + currentNum);
            if (currentNum >= limitNum || checkStop("d")) {
                currentNum = 0;
                break;
            }
            callerApiService.getMsgInQueue(1);
            System.out.println("=========================================="+ currentNum + "==============================================");
            currentNum = 0;
            try {
                TimeUnit.SECONDS.sleep(1);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }

    private boolean checkStop(String device) {
        boolean stopFlag = false;
        String sqsUrl = StringUtils.equalsIgnoreCase(device, RankQcInfoEntity.DEVICE_DESKTOP) ? desktopQueueUrl : mobileQueueUrl;
        Map<String, Integer> map = SQSUtils.getMessagesAvailableAndMessageInFilght(amazonSQS, sqsUrl);
        Integer messagesAvailable = map.get("MESSAGES_AVAILABLE");
        System.out.println("===kwCountInQueue messagesAvailable: " + messagesAvailable + " inFlight: " + map.get("MESSAGES_IN_FLIGHT") + " device: " + device);
        if (messagesAvailable == 0) {
            stopFlag = true;
        }
        return stopFlag;
    }


    private void mulutiV3Crawl(int startType, String instancename) {
        InstanceParam instanceParam = callerApiService.generateInstanceParam(true, 1);
        instanceParam.setInstanceType(instancename);
        float latestPrice = callerApiService.getLatestPrice(instanceParam);
        latestPrice += addPrice;
        instanceParam.setSpotPriceLimit(latestPrice);

        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("spotInstance", true);

        if (startType == 1) {
            jsonObject.addProperty("screenshotVersion", String.valueOf(rankDate));
            jsonObject.addProperty("keywordQueueName", SQS_NAME_D);
            jsonObject.addProperty("keywordCrawlQueueName", "SQS_IN_CRAWL_PIXEL_WEEKLY_DESKTOP");
            jsonObject.addProperty("keywordOutQueueNameUS", "SQS_OUT_PIXEL_GOOGLE_DESKTOP_US");
            jsonObject.addProperty("keywordOutQueueNameINTL", "SQS_OUT_PIXEL_GOOGLE_DESKTOP_INTL");
            jsonObject.addProperty("provider", "vs3-desktop");
            instanceParam.setUserData(jsonObject.toString());
            RunInstancesResponse runInstancesResponse1 = callerApiService.batchCreateInstance(instanceParam);
            processRes(runInstancesResponse1);
            System.out.println("runDesktop:" + runInstancesResponse1);
        } else {
            jsonObject.addProperty("screenshotVersion", String.valueOf(rankDate));
            jsonObject.addProperty("keywordQueueName", SQS_NAME_M);
            jsonObject.addProperty("keywordCrawlQueueName", "SQS_IN_CRAWL_PIXEL_WEEKLY_MOBILE");
            jsonObject.addProperty("keywordOutQueueNameUS", "SQS_OUT_PIXEL_GOOGLE_MOBILE_US");
            jsonObject.addProperty("keywordOutQueueNameINTL", "SQS_OUT_PIXEL_GOOGLE_MOBILE_INTL");
            instanceParam.setUserData(jsonObject.toString());
            RunInstancesResponse runInstancesResponse2 = callerApiService.batchCreateInstance(instanceParam);
            processRes(runInstancesResponse2);
            System.out.println("runMobile:" + runInstancesResponse2);
        }
        int runningInstanceNum = callerApiService.getRunningInstanceNum(instanceParam);
        currentNum += runningInstanceNum;
    }

    private void processRes(RunInstancesResponse response) {
        if (response != null) {
            List<String> instanceIdSets = response.getInstanceIdSets();
            if (instanceIdSets != null && !instanceIdSets.isEmpty()) {
                try {
                    FileUtils.writeLines(new File(filePath), instanceIdSets, true);
                } catch (IOException e) {
                    System.out.println(instanceIdSets);
                    e.printStackTrace();
                }
            }
        }

    }

    private String getTime() {
        return FormatUtils.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss");
    }
}
