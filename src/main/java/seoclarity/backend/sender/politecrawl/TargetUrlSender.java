package seoclarity.backend.sender.politecrawl;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.opensearch.client.opensearch.OpenSearchClient;
import org.opensearch.client.opensearch._types.FieldValue;
import org.opensearch.client.opensearch._types.mapping.Property;
import org.opensearch.client.opensearch.core.IndexRequest;
import org.opensearch.client.opensearch.core.SearchRequest;
import org.opensearch.client.opensearch.core.SearchResponse;
import org.opensearch.client.opensearch.core.search.Hit;
import org.opensearch.client.opensearch.indices.*;
import org.opensearch.client.transport.endpoints.BooleanResponse;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.actonia.TargetUrlEntityDAO;
import seoclarity.backend.dao.actonia.politecrawl.PoliteCrawlDomainSettingDAO;
import seoclarity.backend.dao.actonia.politecrawl.PoliteCrawlUrlDAO;
import seoclarity.backend.dao.clickhouse.ClDailyRankingEntityDao;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.actonia.TargetUrlEntity;
import seoclarity.backend.entity.actonia.politecrawl.PoliteCrawlDomainSetting;
import seoclarity.backend.entity.actonia.politecrawl.PoliteCrawlUrl;
import seoclarity.backend.opensearch.OpenSearchClientFactory;
import seoclarity.backend.opensearch.OpenSearchService;
import seoclarity.backend.service.ScKeywordRankManager;
import seoclarity.backend.utils.ClarityDBUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

@Log4j2
public class TargetUrlSender {
    private static final OpenSearchClient openSearchClient = OpenSearchClientFactory.getInstance();
    public final String indexName = "polite_crawl_url";
    public final PoliteCrawlDomainSettingDAO politeCrawlDomainSettingDAO;
    private final OpenSearchService<OpenSearchDocument> openSearchService;
    private final TargetUrlEntityDAO targetUrlEntityDAO;
    private final PoliteCrawlUrlDAO politeCrawlUrlDAO;
    private final ExecutorService executorService;
    private final TargetUrlSenderMetadata targetUrlSenderMetadata;
    private final ClDailyRankingEntityDao clDailyRankingEntityDao;
    private static final LocalDate today = LocalDate.now();
    private static final LocalDate rankingDate = today.minusDays(2);

    public TargetUrlSender(int concurrentThreads) {
        openSearchService = new OpenSearchService<>(openSearchClient);
        targetUrlEntityDAO = SpringBeanFactory.getBean("targetUrlEntityDAO");
        executorService = Executors.newFixedThreadPool(concurrentThreads);
        final OwnDomainEntityDAO ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        this.politeCrawlDomainSettingDAO = SpringBeanFactory.getBean("politeCrawlDomainSettingDAO");
        politeCrawlUrlDAO = SpringBeanFactory.getBean("politeCrawlUrlDAO");
        final List<PoliteCrawlDomainSetting> politeCrawlDomainSettings = this.politeCrawlDomainSettingDAO.getAllPoliteCrawlDomainSettings();
        final Map<Integer, OwnDomainEntity> ownDomainEntityMap = ownDomainEntityDAO.getOwnDomainEntityMap();
        this.targetUrlSenderMetadata = new TargetUrlSenderMetadata(ownDomainEntityMap, politeCrawlDomainSettings);
        clDailyRankingEntityDao = SpringBeanFactory.getBean("clDailyRankingEntityDao");
    }


    public static void main(String[] args) {
        int concurrentThreads = 10; // Default value
        if (args.length > 0) {
            concurrentThreads = Integer.parseInt(args[0]);
        }
        TargetUrlSender targetUrlSender = new TargetUrlSender(concurrentThreads);
        targetUrlSender.demo();
    }

    private void demo() {
        final int domainId = 256;
        final List<TargetUrlEntity> managedUrls = this.targetUrlEntityDAO.getManagedUrlsToCrawlByDomain(domainId);
        final List<OpenSearchDocument> list = managedUrls.stream().map(OpenSearchDocument::fromTargetUrlEntity).toList();
        this.openSearchService.bulkIndexDocuments(indexName, list, OpenSearchDocument::getId);
    }

    public void sendTargetUrl() {
        // sync polite_crawl_domain_setting enabled status first
        this.syncPoliteCrawlDomainSettingsEnabled();
        // 1. get all own domain ids
        final Set<Integer> ownDomainIds = this.targetUrlSenderMetadata.getOwnDomainIds();
        // 2. get all urls for each own domain id
        ownDomainIds.forEach(this::processUrlsForDomain);
        // 3. operate urls on each own domain id
    }

    public void sendNewUrl() {

    }

    public void processUrlsForDomain(Integer ownDomainId) {
        // Get all URLs for the domain
        List<PoliteCrawlUrl> politeCrawlUrls = politeCrawlUrlDAO.findByOwnDomainId(ownDomainId);

        // Get all target URLs for the domain to get the actual URLs
        List<TargetUrlEntity> targetUrls = targetUrlEntityDAO.getManagedUrlsToCrawlByDomain(ownDomainId);

        // Create a map of URL hash to URL
        Map<String, String> managedUrlHashToUrlMap = targetUrls.stream()
                .collect(Collectors.toMap(TargetUrlEntity::getUrlMurmur3Hash, TargetUrlEntity::getUrl));

        // Separate URLs into created, updated, and disabled
        List<OpenSearchDocument> createdUrls = new ArrayList<>();
        List<OpenSearchDocument> updatedUrls = new ArrayList<>();
        List<OpenSearchDocument> disabledUrls = new ArrayList<>();

        Date now = new Date();
        Date oneHourAgo = new Date(now.getTime() - 3600000);

        // if politeCrawlUrls is empty, all managed urls should be added to createdUrls
        if (politeCrawlUrls.isEmpty()) {
            createdUrls.addAll(targetUrls.stream().map(OpenSearchDocument::fromTargetUrlEntity).toList());
            this.openSearchService.bulkIndexDocuments(indexName, createdUrls, OpenSearchDocument::getId);
            return;
        }

        for (PoliteCrawlUrl url : politeCrawlUrls) {
            // Skip URLs that don't have a corresponding target URL
            if (!managedUrlHashToUrlMap.containsKey(url.getUrlMurmur3Hash())) {
                continue;
            }

            String actualUrl = managedUrlHashToUrlMap.get(url.getUrlMurmur3Hash());
            OpenSearchDocument document = OpenSearchDocument.fromPoliteCrawlUrl(url, actualUrl);

            // This is just example logic - you would have your own criteria
            if (url.getCreatedAt() != null && url.getCreatedAt().after(oneHourAgo)) {
                // Recently created
                createdUrls.add(document);
            } else if (url.getLastUpdateTime() != null && url.getLastUpdateTime().after(oneHourAgo)) {
                // Recently updated
                updatedUrls.add(document);
            } else if (url.getEnabled() == 0) {
                // Disabled
                disabledUrls.add(document);
            }
        }

        // Process each category of URLs
//        int createdCount = processCreatedUrls(createdUrls);
//        int updatedCount = processUpdatedUrls(updatedUrls);
//        int disabledCount = processDisabledUrls(disabledUrls);
//
//        log.info("Processed URLs for domain {}: {} created, {} updated, {} disabled",
//                ownDomainId, createdCount, updatedCount, disabledCount);
    }

    private int processDisabledUrls(List<OpenSearchDocument> disabledUrls) {
        return 0;
    }

    private int processUpdatedUrls(List<OpenSearchDocument> updatedUrls) {
        return 0;
    }

//    private int processCreatedUrls(List<OpenSearchDocument> createdUrls) {
//        // step 1: batch insert to polite_crawl_url and polite_crawl_url_detail
//        politeCrawlUrlDAO.batchInsert(createdUrls.stream().map(OpenSearchDocument::toPoliteCrawlUrl).toList());
//        politeCrawlUrlDAO.batchInsertDetail(createdUrls.stream().map(OpenSearchDocument::toPoliteCrawlUrlDetail).toList());
//        // step 2: Store new documents in OpenSearch
//        return openSearchService.bulkIndexDocuments(indexName, createdUrls, OpenSearchDocument::generateId);
//        return 0;
//    }

    private void syncPoliteCrawlDomainSettingsEnabled() {
        // update disabled polite crawl domain settings
        this.politeCrawlDomainSettingDAO.updateBatchEnabledStatus(this.targetUrlSenderMetadata.getDeletedDomainIds(), 0);
        // add new active polite crawl domain settings
        this.politeCrawlDomainSettingDAO.batchInsert(this.targetUrlSenderMetadata.getAddedDomainIds());
        log.info("Sync polite crawl domain settings enabled status finished");
    }

    // a inner class to record the metadata for this script
    @Data
    @NoArgsConstructor
    private static final class TargetUrlSenderMetadata {
        private Map<Integer, OwnDomainEntity> ownDomainEntityMap;
        private Set<Integer> ownDomainIds;
        private List<PoliteCrawlDomainSetting> politeCrawlDomainSettings;
        private List<Integer> deletedDomainIds;
        private List<PoliteCrawlDomainSetting> activeDomainIds;
        private List<PoliteCrawlDomainSetting> addedDomainIds;

        public TargetUrlSenderMetadata(Map<Integer, OwnDomainEntity> ownDomainEntityMap, List<PoliteCrawlDomainSetting> politeCrawlDomainSettings) {
            this.ownDomainEntityMap = ownDomainEntityMap;
            this.ownDomainIds = ownDomainEntityMap.keySet();
            this.politeCrawlDomainSettings = politeCrawlDomainSettings;
            final Map<Integer, PoliteCrawlDomainSetting> enabledDomainSettingMap = politeCrawlDomainSettings.stream()
                    .filter(politeCrawlDomainSetting -> politeCrawlDomainSetting.getEnabled() == 1)
                    .collect(Collectors.toMap(PoliteCrawlDomainSetting::getOwnDomainId, politeCrawlDomainSetting -> politeCrawlDomainSetting));
            // filter out need deleted domain ids: not in ownDomainIds
            this.deletedDomainIds = enabledDomainSettingMap.keySet().stream().filter(ownDomainId -> !ownDomainIds.contains(ownDomainId)).toList();
            // find added domain ids: in ownDomainIds but not in enabledDomainSettingMap (need to do full operation)
            this.addedDomainIds = ownDomainIds.stream().filter(ownDomainId -> !enabledDomainSettingMap.containsKey(ownDomainId)).map(domainId -> {
                PoliteCrawlDomainSetting politeCrawlDomainSetting = new PoliteCrawlDomainSetting();
                politeCrawlDomainSetting.setOwnDomainId(domainId);
                politeCrawlDomainSetting.setEnabled(1);
                politeCrawlDomainSetting.setFrequence(1);
                return politeCrawlDomainSetting;
            }).collect(Collectors.toList());
            // find active domain ids: in ownDomainIds and in enabledDomainSettingMap (need to do incremental operation)
            this.activeDomainIds = ownDomainIds.stream().filter(enabledDomainSettingMap::containsKey) .map(enabledDomainSettingMap::get).collect(Collectors.toList());
            log.info("TargetUrlSenderMetadata: deletedDomainIds: {}, addedDomainIds: {}, activeDomainIds: {}", deletedDomainIds.size(), addedDomainIds.size(), activeDomainIds.size());
        }
    }

    private void test() {
        final Map<String, Property> mappings = Map.of(
                "id", Property.of(p -> p.keyword(t -> t)),
                "url", Property.of(p -> p.keyword(t -> t)),
                "ownDomainId", Property.of(p -> p.integer(t -> t)),
                "nextFetchDate", Property.of(p -> p.date(d -> d.format("date_optional_time"))),
                "urlId", Property.of(p -> p.integer(t -> t)),
                "urlMurmurHash", Property.of(p -> p.keyword(t -> t)),
                "processEndTime", Property.of(p -> p.date(d -> d.format("date_optional_time"))),
                "sendTime", Property.of(p -> p.date(d -> d.format("date_optional_time"))),
                "sumMd5", Property.of(p -> p.keyword(t -> t))
        );

        try {
            ExistsRequest existsRequest = new ExistsRequest.Builder().index(indexName).build();
            final BooleanResponse existsResponse = openSearchClient.indices().exists(existsRequest);
            if (existsResponse.value()) {
                log.info("Index {} existsResponse, deleting it...", indexName);
            }
            DeleteIndexRequest deleteIndexRequest = new DeleteIndexRequest.Builder().index(indexName + "12321").build();
            final DeleteIndexResponse delete = openSearchClient.indices().delete(deleteIndexRequest);
            if (delete.acknowledged()) {
                log.info("Index {} deleted successfully", indexName);
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        CreateIndexRequest createIndexRequest = new CreateIndexRequest.Builder()
                .index(indexName)
                .mappings(mapping -> mapping.properties(mappings))
                .build();
        try {
            final CreateIndexResponse createIndexResponse = openSearchClient.indices().create(createIndexRequest);

        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        OpenSearchDocument openSearchDocument = new OpenSearchDocument();
        openSearchDocument.setOwnDomainId(4);
        openSearchDocument.setUrl("https://www.example.com");
        openSearchDocument.setId(openSearchDocument.generateId());
        IndexRequest<OpenSearchDocument> indexRequest = new IndexRequest.Builder<OpenSearchDocument>()
                .index(indexName)
//                .id(openSearchDocument.generateId())
                .document(openSearchDocument)
                .build();
        List<OpenSearchDocument> openSearchDocuments = List.of(openSearchDocument);

        var ids = List.of("16f4afc979743286ef9fcceee44d80e371b68a5ded5eb2362f66388fed8e2eba", "4S77MpYB8Kp2B6PyxpNO", "4i78MpYB8Kp2B6PyF5P_", "4y78MpYB8Kp2B6Py2JPq", "4C76MpYB8Kp2B6Py4pNG");
        // search document by query string
        // query by field url
        String query = "https://www.example.com";
        var searchRequest = new SearchRequest.Builder()
                .index(indexName)
                .query(q -> q.terms(tf -> tf.field("url").terms(tv -> tv.value(List.of(FieldValue.of(query), FieldValue.of("https://www.google.com"))))))
                .build();
        final SearchResponse<OpenSearchDocument> searchResponse;
        try {
            searchResponse = openSearchClient.search(searchRequest, OpenSearchDocument.class);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        final List<OpenSearchDocument> list = searchResponse.hits().hits().stream().map(Hit::source).toList();
        final List<OpenSearchDocument> search = this.openSearchService.search(indexName, query, OpenSearchDocument.class);
        final List<OpenSearchDocument> openSearchDocuments1 = openSearchService.searchDocumentByIds(indexName, ids, OpenSearchDocument.class);
//        openSearchService.search(indexName, )
//        this.openSearchService.bulkDeleteDocumentByIds(indexName, ids);
        final List<OpenSearchDocument> updateList = openSearchDocuments1.stream().peek(openSearchDocument1 -> {
            openSearchDocument1.setSendTime(new Date());
        }).toList();
        this.openSearchService.bulkUpdateDocumentByIds(indexName, updateList, OpenSearchDocument::generateId);
        openSearchService.bulkIndexDocuments(indexName, openSearchDocuments, OpenSearchDocument::getUrlMurmurHash);

    }

    private List<String> queryTopRankingUrls(int domainId) {
        final OwnDomainEntity ownDomainEntity = this.targetUrlSenderMetadata.getOwnDomainEntityMap().get(domainId);
        int searchEngineId = ScKeywordRankManager.getSearchEngineId(ownDomainEntity);
        String engineName = ScKeywordRankManager.getSearchEngineNameByEngineId(searchEngineId,null);
        int searchLanguageId = ScKeywordRankManager.getSearchLanguageId(ownDomainEntity);
        boolean isMobile = ownDomainEntity.isMobileDomain();
        String domainName = ownDomainEntity.getDomain();
        String rootDomain = ClarityDBUtils.getRootDomain(domainName);
        String reverseDomain = StringUtils.reverseDelimited(domainName, '.');
        String reverseRootDomain = StringUtils.reverseDelimited(rootDomain, '.');
        final String tableNamePrefix = isMobile ? "m" : "d";
        String tableName = this.getRankTableName(tableNamePrefix, searchEngineId, searchLanguageId);
        final String sql = "select url from " + tableName + " where own_domain_id = " + domainId + " and engine_id = " + searchEngineId + " and language_id = " + searchLanguageId + " and ranking_date = '" + rankingDate + "' and true_rank <= 10";
        return List.of("https://www.example.com", "https://www.google.com");
    }

    private String getRankTableName(String tableNamePrefix, int searchEngineId, int searchLanguageId) {
        // Format date as YYYYMM
        String rankDateYYYYMM = DateTimeFormatter.ofPattern("yyyyMM").format(rankingDate);
        
        // Build table name based on search engine and language
        String tableName;
        if (searchEngineId == 1 && searchLanguageId == 1) {
            tableName = tableNamePrefix + ClarityDBUtils.RANK_DETAIL_TYPE + "us_" + rankDateYYYYMM;
        } else {
            tableName = tableNamePrefix + ClarityDBUtils.RANK_DETAIL_TYPE + "intl_" + rankDateYYYYMM;
        }
        return tableName;
    }
}
