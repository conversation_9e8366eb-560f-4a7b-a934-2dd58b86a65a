package seoclarity.backend.sender.politecrawl;

import seoclarity.backend.entity.actonia.TargetUrlEntity;
import seoclarity.backend.entity.actonia.politecrawl.PoliteCrawlRankingUrl;

import java.util.List;

public interface UrlSenderService {

    void sendNewUrls(List<TargetUrlEntity> targetUrlEntities);

    void deleteUrls(List<TargetUrlEntity> targetUrlEntities);

    /**
     * @param politeCrawlRankingUrls URLs from RI after filtered not in managed URLs
     */
    void processRankingUrls(List<PoliteCrawlRankingUrl> politeCrawlRankingUrls);
}
