package seoclarity.backend.sender.politecrawl;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import seoclarity.backend.entity.actonia.TargetUrlEntity;
import seoclarity.backend.entity.actonia.politecrawl.PoliteCrawlRankingUrl;
import seoclarity.backend.entity.actonia.politecrawl.PoliteCrawlUrl;

import java.util.Date;

/**
 * for now we need those columns
 * url (Strig text)
 * ownDomainId (int)
 * nextFetchDate (date time)
 * urlId(0 for ranking URL)
 * urlMurmurHash
 * batchDate(yyyyMMdd)
 * processFlg(0: not processed, 2: processed)
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OpenSearchDocument {
    private String id;
    private String url;
    private int ownDomainId;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date nextFetchDate = new Date();
    private long urlId = 0;
    private String urlMurmurHash;
    private Date processEndTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date sendTime = new Date();
    private String sumMd5;
    private String lastMd5;

    // Status flag for the URL (enabled/disabled)
//    private Integer enabled;

    // The ID generation method for documents sent to opensearch: org.apache.commons.codec.digest.DigestUtils.sha256Hex(url + domainId); eg: sha256Hex("www.baidu.com" + 4)
    public final String generateId() {
        return org.apache.commons.codec.digest.DigestUtils.sha256Hex(this.url + this.ownDomainId);
    }

    /**
     * Convert a TargetUrlEntity to an OpenSearchDocument
     *
     * @param targetUrlEntity The TargetUrlEntity to convert
     * @return The converted OpenSearchDocument
     */
    public static OpenSearchDocument fromTargetUrlEntity(TargetUrlEntity targetUrlEntity) {
        OpenSearchDocument document = new OpenSearchDocument();
        document.setUrl(targetUrlEntity.getUrl());
        document.setUrlMurmurHash(targetUrlEntity.getUrlMurmur3Hash());
        document.setOwnDomainId(targetUrlEntity.getOwnDomainId());
        document.setUrlId(targetUrlEntity.getId());
//        document.setEnabled(targetUrlEntity.getStatus() == TargetUrlEntity.STATUS_ACTIVE ? 1 : 0);
        document.setId(document.generateId());
        return document;
    }

    /**
     * Convert a PoliteCrawlRankingUrl to an OpenSearchDocument
     *
     * @param rankingUrl The PoliteCrawlRankingUrl to convert
     * @return The converted OpenSearchDocument
     */
    public static OpenSearchDocument fromPoliteCrawlRankingUrl(PoliteCrawlRankingUrl rankingUrl) {
        OpenSearchDocument document = new OpenSearchDocument();
        document.setOwnDomainId(rankingUrl.getOwnDomainId());
        document.setUrl(rankingUrl.getUrl());
        document.setUrlMurmurHash(rankingUrl.getUrlMurmur3Hash());
        document.setUrlId(0); // 0 for ranking URL
//        document.setEnabled(1); // Ranking URLs are always enabled
        document.setId(document.generateId());
        return document;
    }

    /**
     * Convert a PoliteCrawlUrl to an OpenSearchDocument
     * Note: This method requires the URL to be provided separately since PoliteCrawlUrl doesn't contain it
     *
     * @param politeCrawlUrl The PoliteCrawlUrl to convert
     * @param url The URL associated with the PoliteCrawlUrl
     * @return The converted OpenSearchDocument
     */
    public static OpenSearchDocument fromPoliteCrawlUrl(PoliteCrawlUrl politeCrawlUrl, String url) {
        OpenSearchDocument document = new OpenSearchDocument();
        document.setOwnDomainId(politeCrawlUrl.getOwnDomainId());
        document.setUrl(url);
        document.setUrlMurmurHash(politeCrawlUrl.getUrlMurmur3Hash());
        document.setUrlId(politeCrawlUrl.getId());
//        document.setEnabled(politeCrawlUrl.getEnabled());
        document.setId(document.generateId());
        return document;
    }

}
