package seoclarity.backend.keywordexpand;

import com.amazonaws.services.sqs.AmazonSQS;
import com.amazonaws.services.sqs.model.GetQueueUrlRequest;
import com.amazonaws.services.sqs.model.GetQueueUrlResult;
import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import seoclarity.backend.dao.actonia.ApiCacheBodyDao;
import seoclarity.backend.dao.actonia.EngineCountryLanguageMappingEntityDAO;
import seoclarity.backend.dao.actonia.KeywordStreamSearchengineCountryMappingEntityDAO;
import seoclarity.backend.dao.clickhouse.gscclicksteam.GscClickSteamDAO;
import seoclarity.backend.dao.clickhouse.gscclicksteam.KeywordSummaryAnnualBigV2DAO;
import seoclarity.backend.dao.clickhouse.gscclicksteam.KeywordSummaryAnnualNormalV2DAO;
import seoclarity.backend.dao.clickhouse.keywordexpand.RankingInfoDao;
import seoclarity.backend.dao.clickhouse.keywordexpand.SearchVolumeV2Lweb1Dao;
import seoclarity.backend.dao.clickhouse.keywordexpand.SearchVolumeV2Lweb2Dao;
import seoclarity.backend.dao.rankcheck.*;
import seoclarity.backend.entity.EngineCountryLanguageMappingEntity;
import seoclarity.backend.entity.KeywordProperty;
import seoclarity.backend.entity.KeywordRankVO;
import seoclarity.backend.entity.KeywordStreamSearchengineCountryMappingEntity;
import seoclarity.backend.entity.actonia.ApiCacheBodyEntity;
import seoclarity.backend.entity.actonia.CustomSearchVolumeEntity;
import seoclarity.backend.entity.actonia.DomainSearchEngineRelEntity;
import seoclarity.backend.entity.clickhouse.gscclicksteam.KeywordStreamEntity;
import seoclarity.backend.entity.clickhouse.keywordexpand.SearchVolumeV2Entity;
import seoclarity.backend.entity.rankcheck.*;
import seoclarity.backend.keywordexpand.entity.*;
import seoclarity.backend.keywordexpand.utils.KeywordCleanUpUtils;
import seoclarity.backend.service.CommonDataService;
import seoclarity.backend.service.ScKeywordSearchVolumeManager;
import seoclarity.backend.upload.MonthlyKeywordTokenizerUploadV2;
import seoclarity.backend.utils.*;
import seoclarity.backend.utils.amazon.SQSUtils;
import seoclarity.backend.utils.cityhash.CityHashUtil;
import seoclarity.backend.utils.keywordTokenizer.SnowBallAndNgramForForeignLanguages;
import seoclarity.backend.utils.murmurhash.MurmurHashUtils;

import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

// https://www.wrike.com/open.htm?id=1507275645
@Slf4j
public class RealTimeRGKeywordExpand {

    private static final boolean POPULATE_BOTH_EXPAND_FLAG = false; // https://www.wrike.com/open.htm?id=1579752020
    private static final String QUEUE_NAME_DESKTOP = "SQS_IN_RG_REALTIME_EXPANSION_DESKTOP";
    private static final String QUEUE_NAME_MOBILE = "SQS_IN_RG_REALTIME_EXPANSION_MOBILE";
    private static final String WORKER_KEY_PREFIX = "realTimeRGExpand_"; // realTimeRGExpand_{api_cache_body.id}
    private static final String EMAIL_SUBJECT_PREFIX = "realTimeRGKeywordExpand "; // realTimeRGExpand_{api_cache_body.id}
    private static final String WORKER_REQUEST_URL = WorkerUtils.CACHE_LIST_URL + WORKER_KEY_PREFIX;
    private static final String TOPIC_PREFIX = "rgexprealtime_0_";
    private static final String TOPIC_SUFFIX = "_nat";
    private static final String TOPIC_US = "us_";
    private static final String TOPIC_INTL = "intl_";
    private static final String TOPIC_DESKTOP = "d";
    private static final String TOPIC_MOBILE = "m";
    private static final int DEVICE_DESKTOP = 1;
    private static final int DEVICE_MOBILE = 2;
    private static final int DEFAULT_FREQUENCY = 30;
    private static final int FREQUENCY_TRIPLE_MONTHLY = 90;
    private static final int PROCESS_DAY = 24;
    private static final int CHECK_DAY_RANGE = -14;
    private static final String dataFormatter = "yyyy-MM-dd HH:mm:ss";
    private static final String dateIntFormatter = "yyyyMMdd";
    private static final String[] bccTo = null;
    private static final String emailTo = "<EMAIL>";
    private static final String SPLIT_UNDERLINE = "_";
    private static final String SPLIT_LINE = "-";
    private static final int BATCH_INSERT_SIZE = 500;
    private static final int BATCH_QUERY_SIZE = 200;
    private static final int MSG_BATCH_SIZE = 10;
    private static final Gson gson = new Gson();
    private static final int CITY_ID = 0;
    private static final double QC_PASS_RATE = 90.0;

    private ApiCacheBodyDao apiCacheBodyDao;
    private RgRealtimeExpansionEntityDAO realtimeExpansionEntityDAO;
    private RgRealtimeExpansionSeedKeywordEntityDAO realtimeExpansionSeedKeywordEntityDAO;
    private RgRealtimeExpansionKeywordEntityDAO realtimeExpansionKeywordEntityDAO;
    private SeoClarityKeywordMonthlySearchEngineRelationEntityDAO seoClarityKeywordMonthlySearchEngineRelationEntityDAO;
    private KeywordMonthlyRecommendDAO keywordMonthlyRecommendDAO;
    private KeywordStreamSearchengineCountryMappingEntityDAO keywordStreamSearchengineCountryMappingEntityDAO;
    private SeoClarityKeywordEntityDAO keywordEntityDAO;
    private SeoClarityKeywordAdwordsEntityDAO seoClarityKeywordAdwordsEntityDAO;
    private SeoClarityKeywordAdwordsExpandDAO seoClarityKeywordAdwordsExpandDAO;
    private ScKeywordSearchVolumeManager scKeywordSearchVolumeManager;
    private EngineCountryLanguageMappingEntityDAO mappingEntityDAO;
    private RankingInfoDao rankingInfoDao;
    private GscClickSteamDAO keywordStreamDAO;
    private KeywordSummaryAnnualBigV2DAO keywordSummaryAnnualBigV2DAO;
    private KeywordSummaryAnnualNormalV2DAO keywordSummaryAnnualNormalV2DAO;
    private SearchVolumeV2Lweb1Dao searchVolume1Dao;
    private SearchVolumeV2Lweb2Dao searchVolume2Dao;


    private boolean isTest;
    private int processType; // 0: worker; 1: api
    private Map<String, String> engineStrPLanguageNameMap;
    private Map<String, String> engineStrPFullLanguageNameMap;
    private Set<String> DISTINCT_COUNTRY_CODE_IN_KEYWORD_SUMMARY_ANNUAL_BIG_TABLE = new HashSet<>();
    private AmazonSQS amazonSQS;
    public ZeptoMailSenderComponent zeptoMailSenderComponent;

    public RealTimeRGKeywordExpand() {
        apiCacheBodyDao = SpringBeanFactory.getBean("apiCacheBodyDao");
        realtimeExpansionEntityDAO = SpringBeanFactory.getBean("realtimeExpansionEntityDAO");
        realtimeExpansionSeedKeywordEntityDAO = SpringBeanFactory.getBean("realtimeExpansionSeedKeywordEntityDAO");
        realtimeExpansionKeywordEntityDAO = SpringBeanFactory.getBean("realtimeExpansionKeywordEntity");
        seoClarityKeywordMonthlySearchEngineRelationEntityDAO = SpringBeanFactory.getBean("seoClarityKeywordMonthlySearchEngineRelationEntityDAO");
        keywordMonthlyRecommendDAO = SpringBeanFactory.getBean("keywordMonthlyRecommendDAO");
        keywordStreamSearchengineCountryMappingEntityDAO = SpringBeanFactory.getBean("keywordStreamSearchengineCountryMappingEntityDAO");
        keywordEntityDAO = SpringBeanFactory.getBean("seoClarityKeywordEntityDAO");
        seoClarityKeywordAdwordsEntityDAO = SpringBeanFactory.getBean("seoClarityKeywordAdwordsEntityDAO");
        seoClarityKeywordAdwordsExpandDAO = SpringBeanFactory.getBean("seoClarityKeywordAdwordsExpandDAO");
        scKeywordSearchVolumeManager = SpringBeanFactory.getBean("scKeywordSearchVolumeManager");
        mappingEntityDAO = SpringBeanFactory.getBean("engineCountryLanguageMappingEntityDAO");
        rankingInfoDao = SpringBeanFactory.getBean("rankingInfoDao");
        keywordStreamDAO = SpringBeanFactory.getBean("gscClickSteamDAO");
        keywordSummaryAnnualBigV2DAO = SpringBeanFactory.getBean("keywordSummaryAnnualBigV2DAO");
        keywordSummaryAnnualNormalV2DAO = SpringBeanFactory.getBean("keywordSummaryAnnualNormalV2DAO");
        searchVolume1Dao = SpringBeanFactory.getBean("searchVolumeV2Lweb1Dao");
        searchVolume2Dao = SpringBeanFactory.getBean("searchVolumeV2Lweb2Dao");
        amazonSQS = SQSUtils.getAmazonSQS();
        zeptoMailSenderComponent = SpringBeanFactory.getBean("zeptoMailSenderComponent");
    }

    public static void main(String[] args) {
        RealTimeRGKeywordExpand realTimeRGKeywordExpand = new RealTimeRGKeywordExpand();
        realTimeRGKeywordExpand.startProcess(args);
//        realTimeRGKeywordExpand.clearWorker();
//        realTimeRGKeywordExpand.clearQueue(false);
    }

    private void clearWorker() {
        List<String> cacheList = getCacheList();
        if (cacheList == null || cacheList.isEmpty()) {
            return;
        }
        for (String key : cacheList) {
            delWorkerKey(key);
        }
    }

    private void clearQueue(boolean ifClear) {
        String dUrl = getQueueInfo("SQS_IN_PIXEL_WEEKLY_DESKTOP_TMP20240929");
        String mUrl = getQueueInfo("SQS_IN_PIXEL_WEEKLY_MOBILE_TMP20240929");
        Map<String, Integer> messagesAvailableAndMessageInFilght = SQSUtils.getMessagesAvailableAndMessageInFilght(amazonSQS, dUrl);
        Integer totalD = messagesAvailableAndMessageInFilght.get("MESSAGES_AVAILABLE");
        Integer inFlightD = messagesAvailableAndMessageInFilght.get("MESSAGES_IN_FLIGHT");
        log.info("==desktop total: {}, inFlight: {}", totalD, inFlightD);



        Map<String, Integer> messagesAvailableAndMessageInFilghtM = SQSUtils.getMessagesAvailableAndMessageInFilght(amazonSQS, mUrl);
        Integer totalM = messagesAvailableAndMessageInFilghtM.get("MESSAGES_AVAILABLE");
        Integer inFlightM = messagesAvailableAndMessageInFilghtM.get("MESSAGES_IN_FLIGHT");
        log.info("==mobile total: {}, inFlight: {}", totalM, inFlightM);

        if (ifClear) {
            if (totalD > 0 || inFlightD > 0) {
                SQSUtils.purgeQueue(amazonSQS, dUrl);
            }
            if (totalM > 0 || inFlightM > 0) {
                SQSUtils.purgeQueue(amazonSQS, mUrl);
            }
        }
    }

    private void initParam(String[] args) {
        isTest = Boolean.parseBoolean(args[0]);
        processType = Integer.parseInt(args[1]);
        initEngineCountryCodeMap();
        initClickStreamKwSummaryBigTableCountryCode();
    }

    private void initEngineCountryCodeMap() {
        // 语言缩写
        List<EngineCountryLanguageMappingEntity> allEnabledEngineCountryMapping = mappingEntityDAO.getAll();
        engineStrPLanguageNameMap = allEnabledEngineCountryMapping.stream()
                .filter(var -> var.getRankFrom() == EngineCountryLanguageMappingEntity.RANK_FROM_ALL)
                .collect(Collectors.toMap(var1 -> var1.getEngineId() + SPLIT_UNDERLINE + var1.getLanguageId(), EngineCountryLanguageMappingEntity::getLanguageQueryName));

        // 语言全称
        List<KeywordStreamSearchengineCountryMappingEntity> allEnabledEngineCountryMapping1 = keywordStreamSearchengineCountryMappingEntityDAO.findAllEnabledEngineCountryMapping();
        engineStrPFullLanguageNameMap = allEnabledEngineCountryMapping1.stream()
                .filter(var -> var.getMappingType() == KeywordStreamSearchengineCountryMappingEntity.MAPPING_TYPE_CODE_FROM_ENGINE_ID)
                .collect(Collectors.toMap(var1 -> var1.getEngineId() + SPLIT_UNDERLINE + var1.getLanguageId(), KeywordStreamSearchengineCountryMappingEntity::getLanguageFullName));
    }

    private void initClickStreamKwSummaryBigTableCountryCode() {
        List<String> distinctCountryCode = keywordSummaryAnnualBigV2DAO.getDistinctCountryCode();
        DISTINCT_COUNTRY_CODE_IN_KEYWORD_SUMMARY_ANNUAL_BIG_TABLE.addAll(distinctCountryCode);
    }
    
    private void startProcess(String[] args) {
        initParam(args);
        if (processType == 0) {
            while (true) {
                processKw();
                processTopicExplore();
                try {
                    TimeUnit.MINUTES.sleep(5);
                } catch (InterruptedException e) {
                    System.out.println("====sleepError");
                    Thread.interrupted();
                    e.printStackTrace();
                }
            }
        } else if (processType == 1) {
            processQc();
        } else if (processType == 2) { // only sendToQueue
            int expansionId = Integer.parseInt(args[2]);
            int engineId = Integer.parseInt(args[3]);
            int languageId = Integer.parseInt(args[4]);
            sendToQueue(expansionId, engineId, languageId);
        } else if (processType == 3) { // only process tp
            processTopicExplore();
        } else if (processType == 4) { // always run
            while (true) {
                processKw();
                processTopicExplore();
                log.info("====currentProcessEnd time:{}", DateFormatUtils.format(new Date(), dataFormatter));
                try {
                    TimeUnit.SECONDS.sleep(60);
                } catch (InterruptedException e) {

                }

            }
        } else if (processType == 5) {
            String arg = args[2];
            processApiBody(arg);
        }
    }

    private void processApiBody(String filePath) {
        try {
            List<String> list = FileUtils.readLines(new File(filePath));
            if (!list.isEmpty()) {
                String param = list.get(0);
                String body = list.get(1);
                ApiCacheBodyEntity apiCacheBodyEntity = new ApiCacheBodyEntity();
                apiCacheBodyEntity.setApiRequest(param);
                apiCacheBodyEntity.setApiResponse(body);
                long inert = apiCacheBodyDao.inert(apiCacheBodyEntity);
                log.info("==insertApiBody id:{}", inert);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private void processKw() {
        List<String> cacheList = getCacheList();
        if (null == cacheList || cacheList.isEmpty()) {
            log.info("====nothingRun time:{}", DateFormatUtils.format(new Date(), dataFormatter));
            return;
        }

        for (String key : cacheList) {
            if (!StringUtils.containsIgnoreCase(key, WORKER_KEY_PREFIX)) {
                log.info("====keyFormatError not contains '{}' currentKey:{}", WORKER_KEY_PREFIX, key);
                continue;
            }

            String[] split = key.split(WORKER_KEY_PREFIX);
            if (null == split || split.length != 2) {
                log.info("====keySplitArrayError currentKey:{}", key);
                continue;
            }

            long apiBodyId = 0;
            int engineId = 0;
            int languageId = 0;
            String countryCd = "";
            int userId = 0;
            String[] infoArr = split[1].trim().split(SPLIT_LINE);
            try {
                apiBodyId = Long.parseLong(infoArr[0]);
                engineId = Integer.parseInt(infoArr[1]);
                languageId = Integer.parseInt(infoArr[2]);
                countryCd = infoArr[3];
                userId = Integer.parseInt(infoArr[4]);
                log.info("=====currentProcess apiId:{} engineId:{} languageId:{} countryCd:{} userId:{}", apiBodyId, engineId, languageId, countryCd, userId);
            } catch (Exception e) {
                log.info("====parseKeyError currentKey:{}", key);
                delWorkerKey(key);
                e.printStackTrace();
                continue;
            }

            process(apiBodyId, engineId, languageId, countryCd, new Date(), userId);
            delWorkerKey(key);
        }
        log.info("====currentProcessEnd time {}", DateFormatUtils.format(new Date(), dataFormatter));
    }
    
    private void process(long apiBodyId, int engineId, int languageId, String countryCd, Date createDate, int userId) {
        ApiCacheBodyEntity apiCacheBody = apiCacheBodyDao.getById(apiBodyId);
        String apiResponse = apiCacheBody.getApiResponse();
        DataSeoResponse dataSeoTask = null;
        try {
            dataSeoTask = gson.fromJson(apiResponse, DataSeoResponse.class);
        } catch (JsonSyntaxException e) {
            e.printStackTrace();
        }
        if (dataSeoTask == null) {
            log.info("====parseResponseError bodyId:{} time:{}", apiBodyId, FormatUtils.formatDate(new Date(), dataFormatter));
            return;
        }

        int createDateInt = Integer.parseInt(FormatUtils.formatDate(createDate, dateIntFormatter));

        int expansionId = genRealtimeExpansion(apiBodyId, engineId, languageId, countryCd, createDate, createDateInt, userId);
        if (expansionId <= 0) {
            log.info("==genExpansionError bodyId:{} time:{}", apiBodyId, FormatUtils.formatDate(new Date(), dataFormatter));
            String subject = EMAIL_SUBJECT_PREFIX + "genRealtimeExpansion error";
            String content = "create expansion error, bodyId: " + apiBodyId + " engineId: " + engineId + " languageId: " + languageId + " countryCd: " + countryCd;
            sendErrorAlertEmail(subject, content);
            return;
        }

        Map<String, String> sourceKwPEncodeKwMap = new HashMap<>();
        Map<String, List<DataSeoResult>> kwInfoMap = genExpansionSeedKw(expansionId, dataSeoTask, sourceKwPEncodeKwMap);
        if (kwInfoMap == null || kwInfoMap.isEmpty()) {
            log.info("===genExpansionSeedKwError bodyId:{} expansionId:{} time:{}", apiBodyId, expansionId, FormatUtils.formatDate(new Date(), dataFormatter));
            String subject = EMAIL_SUBJECT_PREFIX + "parser resp error";
            String content = "expansionId: " + expansionId + " bodyId: " + apiBodyId;
            sendErrorAlertEmail(subject, content);
            realtimeExpansionEntityDAO.updateStatusAndMutualStatusById(expansionId, RgRealtimeExpansionEntity.STATUS_PROCESS_SUCCESS, RgRealtimeExpansionEntity.MUTUAL_STATUS_CLEAN_UP_ERROR);
            return;
        }

        List<RgRealtimeExpansionSeedKeywordEntity> seedKwListByKeywordList = realtimeExpansionSeedKeywordEntityDAO.getSeedKwListByKeywordList(expansionId);
        if (seedKwListByKeywordList == null || seedKwListByKeywordList.isEmpty()) {
            log.info("==seekKwIsEmpty bodyId:{} expansionId:{} time:{}", apiBodyId, expansionId, FormatUtils.formatDate(new Date(), dataFormatter));
            String subject = EMAIL_SUBJECT_PREFIX + "genSeedKw error";
            String content = "create expansionSeedKw error, expansionId: " + expansionId;
            sendErrorAlertEmail(subject, content);
            return;
        }

        int seekKwId = seedKwListByKeywordList.get(0).getId();

        Map<String, Integer> encodeKwPIdMap = new HashMap<>();
        Set<String> kwList = kwInfoMap.keySet();

        // clean up
        List<String> cleanUpSkipKwList = new ArrayList<>();
        List<String> cleanUpOkKwList = new ArrayList<>();
        cleanupKw(expansionId, kwList, cleanUpSkipKwList, cleanUpOkKwList, sourceKwPEncodeKwMap, kwInfoMap);
        log.info("==cleanUpInfo kwListCnt:{} skipKwCnt:{} okKwCnt:{}", kwList.size(), cleanUpSkipKwList.size(), cleanUpOkKwList.size());

        // exist check
        List<String> rgExsitSkipKwList = new ArrayList<>();
        List<String> rgOkKwList = new ArrayList<>();
        processRgRelationAndRecommendExist(engineId, languageId, cleanUpOkKwList, rgOkKwList, rgExsitSkipKwList, encodeKwPIdMap, sourceKwPEncodeKwMap);
        log.info("==existInfo cleanUpOkKw:{} skipKwCnt:{} okKwCnt:{} kwIdCnt:{}", cleanUpOkKwList.size(), rgExsitSkipKwList.size(), rgOkKwList.size(), encodeKwPIdMap.size());



        // ABBA check
        List<String> tokenKwList = new ArrayList<>();
        List<String> tokenNotKwList = new ArrayList<>();
        processTokens(expansionId, engineId, languageId, rgOkKwList, tokenKwList, tokenNotKwList);
        log.info("==ABBAInfo rgOkKwCnt:{} tokenKwOkCnt:{} tokenNotOkCnt:{}", rgOkKwList.size(), tokenKwList.size(), tokenNotKwList.size());
        realtimeExpansionEntityDAO.updateMutualStatusById(expansionId, RgRealtimeExpansionEntity.MUTUAL_STATUS_CLEAN_UP_SUCCESS);

        // add kw to get kwId
        realtimeExpansionEntityDAO.updateMutualStatusById(expansionId, RgRealtimeExpansionEntity.MUTUAL_STATUS_SAVE_TO_RANK_CHECK);
        processKwForRankCheckId(expansionId, tokenKwList, encodeKwPIdMap, sourceKwPEncodeKwMap);
        realtimeExpansionEntityDAO.updateMutualStatusById(expansionId, RgRealtimeExpansionEntity.MUTUAL_STATUS_SAVE_RANK_CHECK_SUCCESS);

        // save to rg_realtime_expansion_keyword
        if (!cleanUpSkipKwList.isEmpty()) {
            if (isTest) {
                log.info("==insertKwInfoExpansion expansionId:{} cleanUpSkipKwCnt:{} kw:{}",  expansionId, cleanUpSkipKwList.size(), cleanUpSkipKwList);
            } else {
                log.info("==insertKwInfoExpansion expansionId:{} cleanUpSkipKwCnt:{}", expansionId, cleanUpSkipKwList.size());
            }
            processInsertRealtimeExpansionKeyword(expansionId, engineId, languageId, cleanUpSkipKwList, RgRealtimeExpansionKeywordEntity.STATE_SKIP_FOR_CLEAN_UP, sourceKwPEncodeKwMap, seekKwId, kwInfoMap, null);
        }
        if (!rgExsitSkipKwList.isEmpty()) {
            if (isTest) {
                log.info("==insertKwInfoExpansion expansionId:{} exsitSkipKwCnt:{} kw:{}", expansionId, rgExsitSkipKwList.size(), rgExsitSkipKwList);
            } else {
                log.info("==insertKwInfoExpansion expansionId:{} exsitSkipKwCnt:{}", expansionId, rgExsitSkipKwList.size());
            }
            processInsertRealtimeExpansionKeyword(expansionId, engineId, languageId, rgExsitSkipKwList, RgRealtimeExpansionKeywordEntity.STATE_SKIP_FOR_EXISTING_RG, sourceKwPEncodeKwMap, seekKwId, kwInfoMap, encodeKwPIdMap);
        }
        if (!tokenNotKwList.isEmpty()) {
            if (isTest) {
                log.info("==insertKwInfoExpansion expansionId:{} notTokenSkipKwCnt:{} kw:{}", expansionId, tokenNotKwList.size(), tokenNotKwList);
            } else {
                log.info("==insertKwInfoExpansion expansionId:{} notTokenSkipKwCnt:{}", expansionId, tokenNotKwList.size());
            }
            processInsertRealtimeExpansionKeyword(expansionId, engineId, languageId, tokenNotKwList, RgRealtimeExpansionKeywordEntity.STATE_SKIP_FOR_AB_BA, sourceKwPEncodeKwMap, seekKwId, kwInfoMap, null);
        }
        if (!tokenKwList.isEmpty()) {
            if (isTest) {
                log.info("==insertKwInfoExpansion expansionId:{} allOkKwCnt:{} kw:{}", expansionId, tokenKwList.size(), tokenKwList);
            } else {
                log.info("==insertKwInfoExpansion expansionId:{} allOkKwCnt:{}", expansionId, tokenKwList.size());
            }
            processInsertRealtimeExpansionKeyword(expansionId, engineId, languageId, tokenKwList, RgRealtimeExpansionKeywordEntity.STATE_CANDIDATE_RG_KEYWORD, sourceKwPEncodeKwMap, seekKwId, kwInfoMap, encodeKwPIdMap);
            if (!isTest) {
                processInsertKeywordMonthlyRel(expansionId, engineId, languageId, createDateInt, tokenKwList, sourceKwPEncodeKwMap, encodeKwPIdMap, kwInfoMap);
            }
        }

        // save to adwords
        saveSvToAdwords(expansionId, engineId, languageId, createDateInt, kwInfoMap, sourceKwPEncodeKwMap, encodeKwPIdMap);


        // send to queue
        realtimeExpansionEntityDAO.updateMutualStatusById(expansionId, RgRealtimeExpansionEntity.MUTUAL_STATUS_SEND_TO_SQS);
        sendToQueue(expansionId, engineId, languageId);
        realtimeExpansionEntityDAO.updateMutualStatusById(expansionId, RgRealtimeExpansionEntity.MUTUAL_STATUS_SEND_TO_SQS_SUCCESS);

    }

    private List<String> getCacheList() {
        List<String> cacheList = null;
        try {
            cacheList = WorkerUtils.getCacheList(WORKER_REQUEST_URL);
        } catch (Exception e) {
            log.info("====getCacheListError {}", FormatUtils.formatDate(new Date(), dataFormatter));
            e.printStackTrace();
        }
        return cacheList;
    }

    public boolean delWorkerKey(String fullKey) {
        log.info("===delKey key:{}", fullKey);
        boolean delResult = true;
        try {
            WorkerUtils.doDelete(WorkerUtils.DELETE_CACHE_URL_PREFIX + fullKey, WorkerUtils.KEY_SECURITY_HEADER, WorkerUtils.VALUE_SECURITY_HEADER);
        } catch (Exception e) {
            delResult = false;
            e.printStackTrace();
        }
        return delResult;
    }

    private void sendErrorAlertEmail(String emailSubject, String msg) {
        Map<String, Object> reportMap = new HashMap<>();
        reportMap.put("userName", "ALL");
        reportMap.put("errMsg", "&nbsp;&nbsp;&nbsp;&nbsp;" + msg + ". \ntime:" + getTime());
        zeptoMailSenderComponent.sendMimeMultiPartZeptoMailAndBccByFunctionType(emailTo, bccTo, emailSubject, "mail_alert_missing_bot_files.txt", "mail_alert_missing_bot_files.html",
                reportMap, null, ZeptoMailSenderComponent.FUNCTION_TYPE_BACKEND_INTERNAL, null, null);
    }

    private String getTime() {
        return FormatUtils.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss");
    }

    private int genRealtimeExpansion(long apiBodyId, int engineId, int languageId, String countryCd, Date createDate, int createDateInt, int userId) {
        int expansionId = 0;
        RgRealtimeExpansionEntity exitsExpansion = realtimeExpansionEntityDAO.getByUniqueKey(apiBodyId);
        if (exitsExpansion == null) {
            RgRealtimeExpansionEntity expansionEntity = new RgRealtimeExpansionEntity();
            expansionEntity.setCacheBodyId(apiBodyId);
            expansionEntity.setEngineId(engineId);
            expansionEntity.setLanguageId(languageId);
            expansionEntity.setCountryCd(countryCd);
            expansionEntity.setStatus(RgRealtimeExpansionEntity.STATUS_PROCESSING);
            expansionEntity.setMutualStatus(RgRealtimeExpansionEntity.MUTUAL_STATUS_CLEANING_UP);
            expansionEntity.setCreateDate(createDateInt);
            expansionEntity.setCreateUserId(userId);
            expansionEntity.setCreatedAt(createDate);
            expansionId = realtimeExpansionEntityDAO.insert(expansionEntity);
        } else {
            expansionId = exitsExpansion.getId();
        }
        return expansionId;
    }

    private Map<String, List<DataSeoResult>> genExpansionSeedKw(int expansionId, DataSeoResponse dataSeoTask, Map<String, String> sourceKwPEncodeKwMap) {
        List<DataSeoTask> tasks = dataSeoTask.getTasks();
        if (tasks == null || tasks.isEmpty()) {
            log.info("==sourceKwCntEmpty expansionId:{}", expansionId);
            return null;
        }
        String seedKw = "";
        Map<String, List<DataSeoResult>> resultMap = new HashMap<>();
        Date createDate = new Date();
        int sourceKwCnt = 0;

        for (DataSeoTask task : tasks) {
            List<DataSeoResult> result = task.getResult();
            if (result == null || result.isEmpty()) {
                continue;
            }
            if (StringUtils.isEmpty(seedKw)) {
                DataSeoData data = task.getData();
                if (data != null) {
                    List<String> keywords = data.getKeywords();
                    if (keywords != null && !keywords.isEmpty()) {
                        seedKw = keywords.get(0);
                    }
                }
            }
            for (DataSeoResult dataSeoResult : result) {
                sourceKwCnt++;
                String keyword = FormatUtils.decodeKeyword(dataSeoResult.getKeyword());
//                String keyword = dataSeoResult.getKeyword();
                String encodeKeyword = null;
                try {
                    encodeKeyword = CommonDataService.encodeQueueBaseKeyword(keyword.toLowerCase());
                } catch (UnsupportedEncodingException e) {
                    log.info("==encodeError keyword:{}", keyword);
                    e.printStackTrace();
                    encodeKeyword = FormatUtils.encodeKeyword(keyword.toLowerCase());
                }
                
                if (resultMap.containsKey(keyword)) {
                    resultMap.get(keyword).add(dataSeoResult);
                } else {
                    List<DataSeoResult> list = new ArrayList<>();
                    list.add(dataSeoResult);
                    resultMap.put(keyword, list);
                    sourceKwPEncodeKwMap.put(keyword, encodeKeyword);
                }
            }
        }

        if (StringUtils.isEmpty(seedKw)) {
            log.info("==seedKwEmpty expansionId:{}", expansionId);

        }
        RgRealtimeExpansionSeedKeywordEntity expansionSeedKw = new RgRealtimeExpansionSeedKeywordEntity();
        expansionSeedKw.setExpansionId(expansionId);
        expansionSeedKw.setSeedKeyword(seedKw);
        expansionSeedKw.setCreatedAt(createDate);

        int seedKwId = 0;
        try {
            seedKwId = realtimeExpansionSeedKeywordEntityDAO.insert(expansionSeedKw);
        } catch (Exception e) {
            log.info("==insertExpansionSeedKwError keyword:{} time:{}", seedKw, FormatUtils.formatDate(new Date(), dataFormatter));
            e.printStackTrace();
        }
        
        log.info("==sourceKwCnt expansionId:{} seedKwId:{} sourceKwCnt:{} resultMapCnt:{} sourceKwPEncodeKwMapCnt:{}", expansionId, seedKwId, sourceKwCnt, resultMap.size(), sourceKwPEncodeKwMap.size());
        return resultMap;
    }

    private void cleanupKw(int expansionId, Set<String> sourceKwList, List<String> cleanUpSkipKwList, List<String> cleanUpOkKwList, Map<String, String> sourceKwPEncodeKwMap, Map<String, List<DataSeoResult>> kwInfoMap) {
        Set<String> toRemoveFromSourceMap = new HashSet<>();
        Map<String, String> toAddInSourceMap = new HashMap<>();
        Map<String, List<DataSeoResult>> toReplaceInKwInfoMap = new HashMap<>();

        for (String keywordName : sourceKwList) {
            String sourceKw = keywordName;
            if (keywordName.contains("...")) {
                cleanUpSkipKwList.add(keywordName);
                continue;
            }

            if (keywordName.contains("\t")) {
                cleanUpSkipKwList.add(keywordName);
                continue;
            }

            if (keywordName.contains("#NAME")) {
                cleanUpSkipKwList.add(keywordName);
                continue;
            }

            String cleanUpKw = null;
            try {
                cleanUpKw = KeywordCleanUpUtils.cleanUp(keywordName);
            } catch (Exception e) {
                cleanUpSkipKwList.add(keywordName);
                log.info("==cleanUpError expansionId:{} keywordName:{} cleanUpKw:{}", expansionId, keywordName, cleanUpKw);
                e.printStackTrace();
            }

            if (cleanUpKw != null) {
                if (StringUtils.containsAny(cleanUpKw, KeywordCleanUpUtils.ILLEGAL_CHAR_ARRAY)) {
                    log.info("SkipWithBadChar expansionId:{} keywordName:{} cleanUpKw:{}", expansionId, keywordName, cleanUpKw);
                    cleanUpSkipKwList.add(keywordName);
                    continue;
                }
                cleanUpOkKwList.add(cleanUpKw);
                if (!StringUtils.equalsIgnoreCase(cleanUpKw, sourceKw)) {
                    log.info("===processNotEqualOldKw expansionId:{} sourceKw:{} cleanUpKw:{} ", expansionId, sourceKw, cleanUpKw);
                    if (sourceKwPEncodeKwMap.containsKey(sourceKw)) {
                        log.info("==removeOldSkw expansionId:{} sourceKw:{} cleanUpKw:{}", expansionId, sourceKw, cleanUpKw);
                        toRemoveFromSourceMap.add(sourceKw);
                    }
                    if (kwInfoMap.containsKey(sourceKw)) {
                        log.info("==replaceDFSKwData expansionId:{} sourceKw:{} cleanUpKw:{}", expansionId, sourceKw, cleanUpKw);
                        List<DataSeoResult> dataSeoResults = kwInfoMap.get(sourceKw);
                        toReplaceInKwInfoMap.put(cleanUpKw, dataSeoResults);
                    }

                    String encodeKeyword = "";
                    try {
                        encodeKeyword = CommonDataService.encodeQueueBaseKeyword(cleanUpKw.toLowerCase());
                    } catch (UnsupportedEncodingException e) {
                        log.info("==reEncodeError expansionId:{} sourceKw:{} cleanUpKw:{}", expansionId, sourceKw, cleanUpKw);
                        e.printStackTrace();
                        encodeKeyword = FormatUtils.encodeKeyword(cleanUpKw.toLowerCase());
                    }
                    toAddInSourceMap.put(cleanUpKw, encodeKeyword);
                }
            } else {
                cleanUpSkipKwList.add(keywordName);
            }
        }

        if (!toRemoveFromSourceMap.isEmpty()) {
            for (String key : toRemoveFromSourceMap) {
                sourceKwPEncodeKwMap.remove(key);
                kwInfoMap.remove(key);
            }
        }

        if (!toReplaceInKwInfoMap.isEmpty()) {
            kwInfoMap.putAll(toReplaceInKwInfoMap);
        }

        if (!toAddInSourceMap.isEmpty()) {
            sourceKwPEncodeKwMap.putAll(toAddInSourceMap);
        }

    }

    private void processRgRelationAndRecommendExist(Integer engineId, Integer languageId, List<String> cleanUpOkKwList, List<String> rgOkKwList, List<String> rgExsitSkipKwList, Map<String, Integer> encodeKwPIdMap, Map<String, String> sourceKwPEncodeKwMap) {
        Map<String, String> encodeKeywordNamePCleanUpKwMap = new HashMap<>();
        for (String cleanUpOkKw : cleanUpOkKwList) {
            String encodeKw = null;
            if (sourceKwPEncodeKwMap.containsKey(cleanUpOkKw)) {
                encodeKw = sourceKwPEncodeKwMap.get(cleanUpOkKw);
            } else {
                log.info("==notFindEncodeKwWhenCheckExist expansionId:{} cleanUpOkKw:{}", engineId, cleanUpOkKw);
                continue;
            }
            encodeKeywordNamePCleanUpKwMap.put(encodeKw, cleanUpOkKw);
        }

        List<String> tmpExistEncodeKwList = new ArrayList<>();
        Set<String> encodeKeywordNameSet = encodeKeywordNamePCleanUpKwMap.keySet();

        List<List<String>> encodeKeywordNameLists = CollectionSplitUtils.splitCollectionBySizeWithStream(new ArrayList<>(encodeKeywordNameSet), BATCH_QUERY_SIZE);
        for (List<String> encodeKeywordNameListChild : encodeKeywordNameLists) {
            List<SeoClarityKeywordMonthlySearchEngineRelationEntity> seoClarityKeywordMonthlySearchEngineRelationEntities = seoClarityKeywordMonthlySearchEngineRelationEntityDAO.checkExist(engineId, languageId, encodeKeywordNameListChild);
            if (!seoClarityKeywordMonthlySearchEngineRelationEntities.isEmpty()) {
                tmpExistEncodeKwList.addAll(seoClarityKeywordMonthlySearchEngineRelationEntities.stream().map(SeoClarityKeywordMonthlySearchEngineRelationEntity::getKeywordText).collect(Collectors.toList()));
                for (SeoClarityKeywordMonthlySearchEngineRelationEntity entity : seoClarityKeywordMonthlySearchEngineRelationEntities) {
                    encodeKwPIdMap.put(entity.getKeywordText(), entity.getKeywordId());
                }
            }
            List<KeywordMonthlyRecommend> keywordMonthlyRecommends = keywordMonthlyRecommendDAO.checkExists(engineId, languageId, encodeKeywordNameListChild);
            if (!encodeKeywordNameListChild.isEmpty()) {
                tmpExistEncodeKwList.addAll(keywordMonthlyRecommends.stream().map(KeywordMonthlyRecommend::getKeywordText).collect(Collectors.toList()));
                for (KeywordMonthlyRecommend keywordMonthlyRecommend : keywordMonthlyRecommends) {
                    encodeKwPIdMap.put(keywordMonthlyRecommend.getKeywordText(), keywordMonthlyRecommend.getRankcheckId());
                }
            }
        }

        Set<String> _rgExistKeywordSet = new HashSet<>(tmpExistEncodeKwList);
        encodeKeywordNamePCleanUpKwMap.forEach((encodeKw, originalKw)->{
            if (!_rgExistKeywordSet.contains(encodeKw.toLowerCase())) {
                rgOkKwList.add(originalKw);
            } else {
                rgExsitSkipKwList.add(originalKw);
            }
        });
    }

    private String getLanguageName(int engineId, int languageId, boolean isFullName) {
        String fullLanguageName = "";
        String engineStr = engineId + SPLIT_UNDERLINE + languageId;
        if (isFullName) {
            if (engineStrPFullLanguageNameMap.containsKey(engineStr)) {
                fullLanguageName = engineStrPFullLanguageNameMap.get(engineStr);
            }
        } else {
            if (engineStrPLanguageNameMap.containsKey(engineStr)) {
                fullLanguageName = engineStrPLanguageNameMap.get(engineStr);
            }
        }
        return fullLanguageName;
    }

    private void processTokens(int expansionId, Integer engineId, Integer languageId, List<String> rgOkKwList, List<String> tokenKwList, List<String> tokenNotKwList) {
        String fullLanguageName = getLanguageName(engineId, languageId, false);
        if (StringUtils.isEmpty(fullLanguageName)) {
            log.info("==missLanguageFullName expansionId:{} engineId:{} languageId:{}", expansionId, engineId, languageId);
            String subject = EMAIL_SUBJECT_PREFIX + "language not match error";
            String content = "miss language, expansionId: " + expansionId + " engineId:" + engineId + " languageId:" + languageId;
            sendErrorAlertEmail(subject, content);
            return;
        }

        Set<String> abbaSet = new HashSet<>();
        Map<String, String> abbaPkwMap = new HashMap<>();


        for (String keywordName : rgOkKwList) {
            List<String> tokenizedPhrase = SnowBallAndNgramForForeignLanguages.wordTokenizer(keywordName.toLowerCase(), fullLanguageName);
            List<String> cleanedTokenizedPhraseList = new ArrayList<>();
            if (!tokenizedPhrase.isEmpty()) {
                for (String word : tokenizedPhrase) {
                    if (word.contains("'")) {
                        word = word.replaceAll("'", "");
                    }
                    if (word.contains("	")) {
                        word = word.replaceAll("	", "");
                    }
                    cleanedTokenizedPhraseList.add(word);
                }
            } else {
                log.info("==tokenKwError keywordName:{}", keywordName);
                tokenNotKwList.add(keywordName);
            }

            String abbaKw = cleanedTokenizedPhraseList.stream().sorted().collect(Collectors.joining());
            if (!abbaSet.contains(abbaKw)) {
                abbaSet.add(abbaKw);
                abbaPkwMap.put(abbaKw, keywordName);
            } else {
                log.info("==abbaRepeat keywordName:{}", keywordName);
                tokenNotKwList.add(keywordName);
            }
        }
        tokenKwList.addAll(abbaPkwMap.values());
    }

    private void processKwForRankCheckId(int expansionId, List<String> kwList, Map<String, Integer> encodeKwPIdMap, Map<String, String> sourceKwPEncodeKwMap) {
        List<String> needInsertList = new ArrayList<>();
        List<String> encodeKwList = new ArrayList<>();
        Map<String, String> encodeKwPNameMap = new HashMap<>();
        for (String kw : kwList) {
            String encodeKw = null;
            if (sourceKwPEncodeKwMap.containsKey(kw)) {
                encodeKw = sourceKwPEncodeKwMap.get(kw);
            } else {
                log.info("==notFindEncodeKwWhenCheckKwId expansionId:{} kw:{}", expansionId, kw);
                continue;
            }
            encodeKwList.add(encodeKw);
            encodeKwPNameMap.put(encodeKw, kw);
        }

        List<List<String>> lists = CollectionSplitUtils.splitCollectionBySize(encodeKwList, BATCH_QUERY_SIZE);
        for (List<String> listChild : lists) {
            List<SeoClarityKeywordEntity> keywordEntityList = keywordEntityDAO.getKeywordEntityByNames(listChild);
            if (keywordEntityList != null && !keywordEntityList.isEmpty()) {
                Map<String, Integer> encodeKwPIdMapChild = keywordEntityList.stream().collect(Collectors.toMap(SeoClarityKeywordEntity::getKeywordText, SeoClarityKeywordEntity::getId, (var1, var2) -> var1));
                encodeKwPIdMap.putAll(encodeKwPIdMapChild);
                for (String sEncodeKw : listChild) {
                    if (!encodeKwPIdMapChild.containsKey(sEncodeKw)) {
                        needInsertList.add(sEncodeKw);
                    }
                }
            } else {
                needInsertList.addAll(listChild);
            }
        }

        if (!needInsertList.isEmpty()) {
            for (String encodeKw : needInsertList) {
                try {
                    keywordEntityDAO.insert(encodeKw);
                } catch (Exception e) {
                    log.info("==insertEncodeKwError expansionId:{} skw:{} encodeKw:{}", expansionId, encodeKwPNameMap.get(encodeKw), encodeKw);
                }
            }

            List<List<String>> subQueryLists = CollectionSplitUtils.splitCollectionBySize(needInsertList, BATCH_QUERY_SIZE);
            for (List<String> subQueryList : subQueryLists) {
                List<SeoClarityKeywordEntity> keywordEntityList = keywordEntityDAO.getKeywordEntityByNames(subQueryList);
                if (keywordEntityList != null && !keywordEntityList.isEmpty()) {
                    for (SeoClarityKeywordEntity keywordEntity : keywordEntityList) {
                        encodeKwPIdMap.put(keywordEntity.getKeywordText(), keywordEntity.getId());
                    }

                    Map<String, Integer> inserKwPIdMap = keywordEntityList.stream().collect(Collectors.toMap(SeoClarityKeywordEntity::getKeywordText, SeoClarityKeywordEntity::getId, (var1, var2) -> var1));
                    for (String ekw : subQueryList) {
                        if (!inserKwPIdMap.containsKey(ekw)) {
                            log.info("==processKwForRankCheckIdNotFindEncodeKw expansionId:{} skw:{}ekw:{}", expansionId, encodeKwPNameMap.get(ekw), ekw);
                            SeoClarityKeywordEntity keywordEntity = keywordEntityDAO.getByKeyword(ekw);
                            if (keywordEntity == null) {
                                log.info("==singleKwQueryNotFoundRes expansionId:{} skw:{} ekw:{} ", expansionId, encodeKwPNameMap.get(ekw), ekw);
                            } else {
                                log.info("==singleKwQueryFoundRes expansionId:{} skw:{} ekw:{} kwId:{}", expansionId, encodeKwPNameMap.get(ekw), ekw, keywordEntity.getId());
                                encodeKwPIdMap.put(ekw, keywordEntity.getId());
                            }
                        }
                    }
                }
            }
        }

        log.info("==processKwForRankCheckIdEnd sourceKwListCnt:{} encodeKwPIdMapCnt:{}", kwList.size(), encodeKwPIdMap.size());
    }

    private void processInsertRealtimeExpansionKeyword(Integer expansionId, Integer engineId, Integer languageId, List<String> keywordName, int state,
                                                       Map<String, String> sourceKwPEncodeKwMap, int seedKwId, Map<String, List<DataSeoResult>> kwInfoMap, Map<String, Integer> encodeKwPIdMap) {
        List<RgRealtimeExpansionKeywordEntity> insertList = new ArrayList<>();
        for (String rawKw : keywordName) {
            String encodeKw = sourceKwPEncodeKwMap.get(rawKw);
            if (StringUtils.isEmpty(encodeKw)) {
                if (state == RgRealtimeExpansionKeywordEntity.STATE_CANDIDATE_RG_KEYWORD) {
                    log.info("==rgKwNotFoundEkwWillMarkToCleanup sKw:{} expansionId:{}", rawKw, expansionId);
                } else {
                    log.info("==NotFoundEkwWillMarkToCleanup sKw:{} expansionId:{} processState:{}", rawKw, expansionId, state);
                }
                try {
                    encodeKw = CommonDataService.encodeQueueBaseKeyword(rawKw);
                } catch (UnsupportedEncodingException e) {
                    encodeKw = FormatUtils.encodeKeyword(rawKw);
                }
                if (StringUtils.isEmpty(encodeKw)) {
                    log.info("==encodeQueueBaseKeywordErrorWhenMarkToCleanup sKw:{} expansionId:{}", rawKw, expansionId);
                    continue;
                }
                state = RgRealtimeExpansionKeywordEntity.STATE_SKIP_FOR_CLEAN_UP;
            }
            String murmur3Hash = CityHashUtil.getCityHash64ForString(rawKw.toLowerCase());
            long keywordId = 0L;
            if (encodeKwPIdMap != null && !encodeKwPIdMap.isEmpty() && encodeKwPIdMap.containsKey(encodeKw)) {
                keywordId = Long.parseLong(encodeKwPIdMap.get(encodeKw).toString());
            }
            int avgSv = getAvgSv(expansionId, keywordId, rawKw, encodeKw, kwInfoMap, "expansionKw");
            int frequency = getFrequency(engineId, languageId, avgSv);
            RgRealtimeExpansionKeywordEntity entity = rgRealtimeExpansionKeywordEntity(expansionId, seedKwId, engineId, languageId, encodeKw, null, keywordId, murmur3Hash, state, frequency);
            insertList.add(entity);
        }
        List<List<RgRealtimeExpansionKeywordEntity>> lists = CollectionSplitUtils.splitCollectionBySize(insertList, BATCH_INSERT_SIZE);
        for (List<RgRealtimeExpansionKeywordEntity> list : lists) {
            realtimeExpansionKeywordEntityDAO.batchInsert(list);
        }

        /*for (RgRealtimeExpansionKeywordEntity realtimeExpansionKeywordEntity : insertList) {
            try {
                realtimeExpansionKeywordEntityDAO.insert(realtimeExpansionKeywordEntity);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }*/

        if (isTest) {
            log.info("==insertObj expansionId:{} json:{}", expansionId, gson.toJson(insertList));
        }

        if (encodeKwPIdMap == null) {
            log.info("==realtimeExpansionKeywordInfo expansionId:{} seedKwId:{} keywordNameCnt:{} sourceKwCnt:{} insertCnt:{} processState:{} ", expansionId, seedKwId, keywordName.size(), sourceKwPEncodeKwMap.size(), insertList.size(), state);
        } else {
            log.info("==realtimeExpansionKeywordInfo expansionId:{} seedKwId:{} keywordNameCnt:{} sourceKwCnt:{} insertCnt:{} kwIdCnt:{} processState:{}", expansionId, seedKwId, keywordName.size(), sourceKwPEncodeKwMap.size(), insertList.size(), encodeKwPIdMap.size(), state);
        }
    }

    private int getAvgSv(int expansionId, long kwId, String sKw, String eKw, Map<String, List<DataSeoResult>> kwInfoMap, String processType) {
        int avgSv = 0;
        if (kwInfoMap != null && !kwInfoMap.isEmpty() && kwInfoMap.containsKey(sKw)) {
            try {
                DataSeoResult dataSeoResult = kwInfoMap.get(sKw).get(0);
                if (dataSeoResult.getSearch_volume() != null) {
                    avgSv = dataSeoResult.getSearch_volume();
                }
            } catch (Exception e) {
                log.info("==getAvgSvError expansionId:{} kwId:{} sKw:{} eKw:{} processType:{}", expansionId, kwId, sKw, eKw, processType);
                e.printStackTrace();
            }
        } else {
            log.info("==getAvgSvErrorNotFound expansionId:{} kwId:{} sKw:{} eKw:{} processType:{}", expansionId, kwId, sKw, eKw, processType);
        }
        return avgSv;
    }

    private int getFrequency(int engineId, int languageId, int avgSv) {
        int frequency = DEFAULT_FREQUENCY;
        if (engineId == 1 && languageId == 1) {
            if (avgSv <= 10) {
                frequency = FREQUENCY_TRIPLE_MONTHLY;
            }
        }
        return frequency;
    }

    private void processInsertKeywordMonthlyRel(int expansionId, int engineId, int languageId, int createDateInt, List<String> keywordName,
                                                Map<String, String> sourceKwPEncodeKwMap, Map<String, Integer> encodeKwPIdMap, Map<String, List<DataSeoResult>> kwInfoMap) {
        List<SeoClarityKeywordMonthlySearchEngineRelationEntity> insertList = new ArrayList<>();
        for (String rawKw : keywordName) {
            String eKw = sourceKwPEncodeKwMap.get(rawKw);

            if (StringUtils.isEmpty(eKw)) {
                log.info("==notFoundWhenInsertMonthlyRelEkwBySkw sKw:{} expansionId:{}", rawKw, expansionId);
                continue;
            }

            if (!encodeKwPIdMap.containsKey(eKw)) {
                log.info("==insertKeywordMonthlyRelNull expansionId:{} eKw:{}", expansionId, eKw);
                continue;
            }
            long kwId = Long.parseLong(encodeKwPIdMap.get(eKw).toString());
            int avgSv = getAvgSv(expansionId, kwId, rawKw, eKw, kwInfoMap, "keywordMonthlyRel");
            int frequency = getFrequency(engineId, languageId, avgSv);
            insertList.add(genKwMonRelEntity(encodeKwPIdMap.get(eKw), engineId, languageId, createDateInt, frequency));
        }

        if (insertList.isEmpty()) {
            log.info("==nonMonKwRelInsert expansionId:{} skwCnt:{}", expansionId, keywordName.size());
            return;
        }
        int insertCount = 0;
        List<List<SeoClarityKeywordMonthlySearchEngineRelationEntity>> addLists = CollectionSplitUtils.splitCollectionBySize(insertList, BATCH_QUERY_SIZE);
        for (List<SeoClarityKeywordMonthlySearchEngineRelationEntity> list : addLists) {
            int[] insertResult = seoClarityKeywordMonthlySearchEngineRelationEntityDAO.insertForBatch(list);
            insertCount += (int) Arrays.stream(insertResult).filter(var -> var == 1).boxed().count();
        }
        log.info("==insertKeywordMonthlyRelCnt expansionId:{} needInsertCnt:{} realInsertCnt:{} sKwCnt:{}", expansionId, insertList.size(), insertCount, keywordName.size());
    }

    private SeoClarityKeywordMonthlySearchEngineRelationEntity genKwMonRelEntity(int kwId, int engineId, int languageId, int createDateInt, int frequency) {
        SeoClarityKeywordMonthlySearchEngineRelationEntity entity = new SeoClarityKeywordMonthlySearchEngineRelationEntity();
        entity.setKeywordId(kwId);
        entity.setSearchEngineId(engineId);
        entity.setSearchLanguageId(languageId);
        entity.setCreateDate(createDateInt);
        entity.setFrequency(frequency);
        return entity;
    }

    private RgRealtimeExpansionKeywordEntity rgRealtimeExpansionKeywordEntity(Integer expansionId, Integer seedKeywordId, Integer engineId, Integer languageId,
                                                                              String keywordName, String keywordBeforeCleanup, Long keywordId, String murmur3Hash, Integer state,
                                                                              int frequency) {
        RgRealtimeExpansionKeywordEntity entity = new RgRealtimeExpansionKeywordEntity();
        entity.setExpansionId(expansionId);
        entity.setSeedKeywordId(seedKeywordId);
        entity.setEngineId(engineId);
        entity.setLanguageId(languageId);
        entity.setFrequency(frequency);
        entity.setKeywordName(keywordName);
        entity.setKeywordBeforeCleanup(keywordBeforeCleanup);
        entity.setKeywordId(keywordId);
        entity.setCdbKeywordMurmur3hash(murmur3Hash);
        entity.setState(state);
        entity.setCreatedAt(new Date());
        return entity;
    }

    private void saveSvToAdwords(int expansionId, int engineId, int languageId, int createDateInt, Map<String, List<DataSeoResult>> kwInfoMap, Map<String, String> sourceKwPEncodeKwMap, Map<String, Integer> encodeKwPIdMap) {
        List<RgRealtimeExpansionKeywordEntity> expansionKeywordEntityList = realtimeExpansionKeywordEntityDAO.getByExpansionIdAndState(expansionId, RgRealtimeExpansionKeywordEntity.STATE_CANDIDATE_RG_KEYWORD);
        if (expansionKeywordEntityList == null || expansionKeywordEntityList.isEmpty()) {
            log.info("====noNeedSaveSvToAdwords expansionId:{} engineId:{} languageId:{}", expansionId, engineId, languageId);
            return;
        }
        log.info("==processAdwordsKwCnt expansionId:{} kwCnt:{}", expansionId, expansionKeywordEntityList.size());
        List<SVEntity> svEntityList = new ArrayList<>();
        for (RgRealtimeExpansionKeywordEntity expansionKeyword : expansionKeywordEntityList) {
            String keywordName = expansionKeyword.getKeywordName();
            int expandKwId = expansionKeyword.getId();
            if (StringUtils.isEmpty(keywordName)) {
                log.info("==notHaveKeywordName expansionId:{} engineId:{} languageId:{} expandKwId:{}", expansionId, engineId, languageId, expandKwId);
                continue;
            }
            String sourceKw = FormatUtils.decodeKeyword(keywordName);
            if (!sourceKwPEncodeKwMap.containsKey(sourceKw)) {
                log.info("==notFindEKw expansionId:{} expandKwId:{} sKw:{} eKw{}", expansionId, expandKwId, sourceKw, keywordName);
                continue;
            }
            String encodeKw = sourceKwPEncodeKwMap.get(sourceKw);
            if (!encodeKwPIdMap.containsKey(encodeKw)) {
                log.info("==notFindKwId expansionId:{} expandKwId:{} skw:{} eKw:{}", expansionId, expandKwId, sourceKw, keywordName);
                continue;
            }
            Integer kwId = encodeKwPIdMap.get(encodeKw);

            List<DataSeoResult> dataSeoResults = kwInfoMap.get(sourceKw);
            if (dataSeoResults == null || dataSeoResults.isEmpty()) {
                log.info("==notHaveKwRes expansionId:{} expandKwId:{} kwId:{} sKw:{} eKw{}", expansionId, expandKwId, kwId, sourceKw, encodeKw);
                continue;
            }
            DataSeoResult dataSeoResult = dataSeoResults.get(0);
            if (dataSeoResults.size() > 1) {
                log.info("==dupKwRes size:{} expansionId:{} expandKwId:{} kwId:{} sKw:{} eKw{}", dataSeoResults.size(), expansionId, expandKwId, kwId, sourceKw, encodeKw);
                if (dataSeoResult.getSearch_volume() == null) {
                    dataSeoResult = dataSeoResults.get(1);
                }
            }
            SVEntity svEntity = genSVEntity(kwId, engineId, languageId, sourceKw, encodeKw, dataSeoResult);
            if (svEntity == null) {
                log.info("==svEntityNull expansionId:{} expandKwId:{} kwId:{} sKw:{} eKw{}", expansionId, expandKwId, kwId, sourceKw, encodeKw);
                continue;
            }
            svEntityList.add(svEntity);
        }
        log.info("==svInfo expansionId:{} engineId:{} languageId:{} expansionKwCnt:{} svEntityCnt:{} sKwCnt:{} sPEMapCnt:{} ePKwIdMapCnt:{}", expansionId, engineId, languageId, expansionKeywordEntityList.size(), svEntityList.size(), kwInfoMap.size(), sourceKwPEncodeKwMap.size(), encodeKwPIdMap.size());

        if (!svEntityList.isEmpty()) {
            checkKwAdwords(languageId, createDateInt, svEntityList);
            checkKwAdwordsExpand(languageId, createDateInt, svEntityList);
        }
    }

    private void checkKwAdwords(int languageId, int createDateInt, List<SVEntity> svEntityList) {
        List<Integer> kidList = svEntityList.stream().map(SVEntity::getRcKeywordId).collect(Collectors.toList());

        List<List<Integer>> kidLists = CollectionSplitUtils.splitCollectionBySizeWithStream(kidList, BATCH_QUERY_SIZE);
        List<SeoClarityKeywordAdwordsEntity> existAdsKwEntityList = new ArrayList<>();

        for (List<Integer> subKId : kidLists) {
            List<SeoClarityKeywordAdwordsEntity> result = seoClarityKeywordAdwordsEntityDAO.checkExistsForKeywordAdwords(SeoClarityKeywordAdwordsEntity.FIXED_ENGINE_ID, languageId, SeoClarityKeywordAdwordsEntity.DEFAULT_CITY_ID, subKId);
            if (result != null && !result.isEmpty()) {
                existAdsKwEntityList.addAll(result);
                for (SeoClarityKeywordAdwordsEntity seoClarityKeywordAdwordsEntity : result) {
                    seoClarityKeywordAdwordsEntity.setMonthlyRelationshipCreateDate(createDateInt);
                }
                seoClarityKeywordAdwordsEntityDAO.batchUpdateMonthlyRelCreateDate(result);
            }
        }
        Map<Integer, SeoClarityKeywordAdwordsEntity> existAdsKwIdPAdsIdMap = existAdsKwEntityList.stream().collect(Collectors.toMap(SeoClarityKeywordAdwordsEntity::getKeywordId, z -> z, (k, v) -> k));


        Date createDate = new Date();
        int uploadInfoId = Integer.parseInt(FormatUtils.formatDate(createDate, "yyyyMMddHH"));
        List<SeoClarityKeywordAdwordsEntity> insAdWordsList = new ArrayList<>();
        for (SVEntity svEntity : svEntityList) {
            int rcKeywordId = svEntity.getRcKeywordId();
            if (existAdsKwIdPAdsIdMap.containsKey(rcKeywordId)) {
                continue;
            }
            SeoClarityKeywordAdwordsEntity adwordsEntity = createAdwordsEntity(languageId, svEntity, createDate, createDateInt, uploadInfoId);
            insAdWordsList.add(adwordsEntity);
        }

        if (!isTest) {
            int insertCnt = 0;
            List<List<SeoClarityKeywordAdwordsEntity>> insertLists = CollectionSplitUtils.splitCollectionBySizeWithStream(insAdWordsList, CollectionSplitUtils.MYSQL_DB_INSERT);
            for (List<SeoClarityKeywordAdwordsEntity> list : insertLists) {
                int[] result = seoClarityKeywordAdwordsEntityDAO.insertForBatchTable(list);
                insertCnt += (int) Arrays.stream(result).filter(var -> var == 1).boxed().count();
            }
            log.info("==adwordsInfo existCnt:{} needInsertCnt:{} insertCnt:{}", existAdsKwEntityList.size(), insAdWordsList.size(), insertCnt);
        } else {
            log.info("==adwordsInfo existCnt:{} needInsertCnt:{}", existAdsKwEntityList.size(), insAdWordsList.size());
            for (SeoClarityKeywordAdwordsEntity seoClarityKeywordAdwordsEntity : insAdWordsList) {
                log.info("==adwordsInfo {}", gson.toJson(seoClarityKeywordAdwordsEntity));
            }
        }
    }

    private void checkKwAdwordsExpand(int languageId, int createDateInt, List<SVEntity> svEntityList) {
        List<Integer> kidList = svEntityList.stream().map(SVEntity::getRcKeywordId).collect(Collectors.toList());

        List<List<Integer>> kidLists = CollectionSplitUtils.splitCollectionBySizeWithStream(kidList, BATCH_QUERY_SIZE);

        List<SeoClarityKeywordAdwordsExpandEntity> existAdsKwExpandEntityList = new ArrayList<>();
        for (List<Integer> subKId : kidLists) {
            List<SeoClarityKeywordAdwordsExpandEntity> result = seoClarityKeywordAdwordsExpandDAO.getExistMonthList(SeoClarityKeywordAdwordsExpandEntity.FIXED_ENGINE_ID, languageId, SeoClarityKeywordAdwordsExpandEntity.DEFAULT_CITY_ID, subKId);
            if (result != null && !result.isEmpty()) {
                existAdsKwExpandEntityList.addAll(result);

                for (SeoClarityKeywordAdwordsExpandEntity adwordsExpand : result) {
                    adwordsExpand.setMonthlyRelationshipCreateDate(createDateInt);
                }
                seoClarityKeywordAdwordsExpandDAO.updateMonthlyRelationCreateDate(result);
            }
        }
        Map<Integer, List<SeoClarityKeywordAdwordsExpandEntity>> rcKwIdPAdwordsExpandListMap = existAdsKwExpandEntityList.stream().collect(Collectors.groupingBy(SeoClarityKeywordAdwordsExpandEntity::getKeywordId, Collectors.toList()));

        List<SeoClarityKeywordAdwordsExpandEntity> insExpandList = new ArrayList<>();
        for (SVEntity svEntity : svEntityList) {
            int rcKwId = svEntity.getRcKeywordId();
            Map<Integer, Integer> svMap = svEntity.getSvMap();
            Set<Integer> svMonthSet = svMap.keySet();
            if (rcKwIdPAdwordsExpandListMap.containsKey(rcKwId)) {
                List<SeoClarityKeywordAdwordsExpandEntity> existAdwordsExpandList = rcKwIdPAdwordsExpandListMap.get(rcKwId);
                Map<Integer, SeoClarityKeywordAdwordsExpandEntity> ymMonthPAdsEpdEntityMap = existAdwordsExpandList.stream().collect(Collectors.toMap(SeoClarityKeywordAdwordsExpandEntity::getMonth, v -> v));
                for (Integer month : svMonthSet) {
                    int sv = svMap.get(month);
                    if (!ymMonthPAdsEpdEntityMap.containsKey(month)) {
                        SeoClarityKeywordAdwordsExpandEntity needInsertEntity = new SeoClarityKeywordAdwordsExpandEntity(SeoClarityKeywordAdwordsExpandEntity.FIXED_ENGINE_ID, languageId, rcKwId, SeoClarityKeywordAdwordsExpandEntity.DEFAULT_CITY_ID, month, svEntity.getAvgSV(), sv, svEntity.getCpc(), createDateInt);
                        insExpandList.add(needInsertEntity);
                    }
                }
            } else {
                for (Integer month : svMonthSet) {
                    int sv = svMap.get(month);
                    SeoClarityKeywordAdwordsExpandEntity needInsertEntity = new SeoClarityKeywordAdwordsExpandEntity(SeoClarityKeywordAdwordsExpandEntity.FIXED_ENGINE_ID, languageId, rcKwId, SeoClarityKeywordAdwordsExpandEntity.DEFAULT_CITY_ID, month, svEntity.getAvgSV(), sv, svEntity.getCpc(), createDateInt);
                    insExpandList.add(needInsertEntity);
                }
            }
        }

        if (!isTest) {
            int insertCnt = 0;
            if (!insExpandList.isEmpty()) {
                List<List<SeoClarityKeywordAdwordsExpandEntity>> lists = CollectionSplitUtils.splitCollectionBySizeWithStream(insExpandList, CollectionSplitUtils.MYSQL_DB_INSERT);
                for (List<SeoClarityKeywordAdwordsExpandEntity> list : lists) {
                    int[] result = seoClarityKeywordAdwordsExpandDAO.insertIgnoreBatch(list);
                    if(POPULATE_BOTH_EXPAND_FLAG){
                        try {
                            seoClarityKeywordAdwordsExpandDAO.insertRefreshTableForBatch(list);
                            log.error("========insert into new fresh table suc.");
                        }catch (Exception e){
                            e.printStackTrace();
                            log.error("========insert into new fresh table error.");
                        }

                    }
                    insertCnt += (int) Arrays.stream(result).filter(v -> v == 1).boxed().count();
                }
            }
            log.info("==adwordsExpandInfo existCnt:{} needInsertCnt:{} insertCnt:{}", existAdsKwExpandEntityList.size(), insExpandList.size(), insertCnt);
        } else {
            log.info("==adwordsExpandInfo existCnt:{} needInsertCnt:{}", existAdsKwExpandEntityList.size(), insExpandList.size());
            for (SeoClarityKeywordAdwordsExpandEntity seoClarityKeywordAdwordsExpandEntity : insExpandList) {
                log.info("==adwordsExpandInfo {}", gson.toJson(seoClarityKeywordAdwordsExpandEntity));
            }
        }

    }

    private void sendToQueue(int expansionId, int engineId, int languageId) {
        List<RgRealtimeExpansionKeywordEntity> kwList = realtimeExpansionKeywordEntityDAO.getByExpansionIdAndState(expansionId, RgRealtimeExpansionKeywordEntity.STATE_CANDIDATE_RG_KEYWORD);
        if (kwList == null || kwList.isEmpty()) {
            log.info("====noNeedSendToQueue expansionId:{} engineId:{} languageId:{}", expansionId, engineId, languageId);
            return;
        }
        String desktopQueueUrl = getQueueInfo(QUEUE_NAME_DESKTOP);
        String mobileQueueUrl = getQueueInfo(QUEUE_NAME_MOBILE);

        boolean isChangeFrequency = false;
        if (engineId == 1 && languageId == 1) {
            isChangeFrequency = true;
        }

        String kfkTopicDesktop = getKfkTp(engineId, languageId, DEVICE_DESKTOP);
        String kfkTopicMobile = getKfkTp(engineId, languageId, DEVICE_MOBILE);

        Date specialDate = seoclarity.backend.utils.DateUtils.getSpecialDateOfCurrentMonth(PROCESS_DAY);
        String createDate = FormatUtils.formatDate(specialDate, "yyyyMMdd");
        int sendToQueueDate = Integer.parseInt(createDate);
        List<List<RgRealtimeExpansionKeywordEntity>> subKwLists = CollectionSplitUtils.splitCollectionBySize(kwList, BATCH_QUERY_SIZE);

        for (List<RgRealtimeExpansionKeywordEntity> subKwList : subKwLists) {
            List<KeywordProperty> keywordPropertyList = convertRGKeywordPropertyList(expansionId, engineId, languageId, sendToQueueDate, createDate, subKwList);
            if (!keywordPropertyList.isEmpty()) {
                if (isChangeFrequency) {
                    for (KeywordProperty keywordProperty : keywordPropertyList) {
                        int avgSv = Integer.parseInt(keywordProperty.getSearchVol() + "");
                        int frequency = getFrequency(engineId, languageId, avgSv);
                        keywordProperty.setFrequency(frequency);
                    }
                }
                if (isTest) {
                    log.info("====sendToQueueMsg expansionId:{} msgCnt:{} msgBody:{}", expansionId, keywordPropertyList.size(), gson.toJson(keywordPropertyList));
                } else {
                    log.info("====sendToQueueMsg expansionId:{} msgCnt:{}", expansionId, keywordPropertyList.size());
                }
                sent(DEVICE_DESKTOP, kfkTopicDesktop, desktopQueueUrl, keywordPropertyList);
                sent(DEVICE_MOBILE, kfkTopicMobile, mobileQueueUrl, keywordPropertyList);
            } else {
                List<Integer> idList = subKwList.stream().map(RgRealtimeExpansionKeywordEntity::getId).collect(Collectors.toList());
                log.info("====noNeedSendToQueue expansionId:{} engineId:{} languageId:{} idInfo:{}", expansionId, engineId, languageId, StringUtils.join(idList, ","));
            }
        }
    }

    private String getKfkTp(int engineId, int languageId, int device) {
        String kfkTp = TOPIC_PREFIX;
        if (engineId == 1 && languageId == 1) {
            kfkTp += TOPIC_US;
        } else {
            kfkTp += TOPIC_INTL;
        }
        if (device == DEVICE_DESKTOP) {
            kfkTp += TOPIC_DESKTOP;
        } else {
            kfkTp += TOPIC_MOBILE;
        }
        kfkTp += TOPIC_SUFFIX;
        return kfkTp;
    }

    private void sent(int device, String kfkTopic, String queueUrl, List<KeywordProperty> keywordPropertyList) {
        Map<String, String> messages = new HashMap<>();
        for (KeywordProperty keywordProperty : keywordPropertyList) {
            keywordProperty.setDevice(device);
            keywordProperty.setKfkTpc(kfkTopic);
            String msg = gson.toJson(keywordProperty);
            if (isTest) {
                log.info("==sentToQueueMsg msg:{}", msg);
            }
            messages.put(Md5Util.Md5(msg), msg);
            if (messages.size() == MSG_BATCH_SIZE) {
                if (!isTest) {
                    try {
                        SQSUtils.sendBatchMessageToQueue(amazonSQS, queueUrl, messages);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                messages.clear();
            }

            if (!messages.isEmpty()) {
                if (!isTest) {
                    try {
                        SQSUtils.sendBatchMessageToQueue(amazonSQS, queueUrl, messages);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                messages.clear();
            }
        }
    }

    private String getQueueInfo(String queueName) {
        String queueUrl = null;
        try {
            GetQueueUrlResult result = amazonSQS.getQueueUrl(new GetQueueUrlRequest(queueName));
            queueUrl = result.getQueueUrl();
        } catch (Exception e) {
            queueUrl = SQSUtils.createQueue(queueName, amazonSQS);
        }
        return queueUrl;
    }

    private List<KeywordProperty> convertRGKeywordPropertyList(int expansionId,int engineId, int languageId, int sendToQDate, String createDate, List<RgRealtimeExpansionKeywordEntity> keywordList) {
        List<KeywordProperty> resultList = new ArrayList<>();
        for (RgRealtimeExpansionKeywordEntity rcKWEntity : keywordList) {
            try {
                KeywordProperty kwProperty = new KeywordProperty();
                if (StringUtils.isBlank(rcKWEntity.getKeywordName())) {
                    System.out.println(" ==Error SkipEmptyRGKW expansionId:" + expansionId + " KID:" + rcKWEntity.getId() + " SE:" + engineId + "_" + languageId);
                    continue;
                }
                kwProperty.setExpansionId(expansionId);
                kwProperty.setId(Integer.parseInt(rcKWEntity.getKeywordId().toString()));
                kwProperty.setKeywordText(rcKWEntity.getKeywordName());
                kwProperty.setSearchEngine(engineId);
                kwProperty.setSearchLanguage(languageId);
                kwProperty.setCreateDate(createDate);
                kwProperty.setSendToQDate(sendToQDate);
                kwProperty.setFrequency(DEFAULT_FREQUENCY);
                resultList.add(kwProperty);
            } catch (Exception exp) {
                exp.printStackTrace();
            }
        }

        // Batch populate SV(SV, cpc, NotRealSearchVolume)
        long x = System.currentTimeMillis();
        scKeywordSearchVolumeManager.getSearchVolumeEntity(resultList, languageId, SeoClarityKeywordSearchEngineDomainRelationEntity.CITY_DEFAULT_ID);
        System.out.println(" SVSQLTime:" + (System.currentTimeMillis() - x) / 1000 + "s");
        return resultList;
    }

    private SVEntity genSVEntity(int kwId, int engineId, int languageId, String decodeKw, String encodeKw, DataSeoResult dataSeoResult) {
        Integer avgSV = null;
        Float cpc = null;
        try {
            avgSV = dataSeoResult.getSearch_volume();
            cpc = dataSeoResult.getCpc();
        } catch (Exception e) {

        }
        if (avgSV == null) {
            log.info("==skipBySvIsNull kwId:{} sKw:{} eKw:{}", kwId, decodeKw, encodeKw);
            return null;
        }
        SVEntity svEntity = new SVEntity();
        String cpcStr = "0";
        if (cpc != null) {
            cpcStr = cpc.toString();
        }


        int startMonth = 0;
        int endMonth = 0;
        Map<Integer, Integer> svMap = new HashMap<>();
        List<DataSeoMonthlySearch> monthlySearches = dataSeoResult.getMonthly_searches();
        if (monthlySearches != null && !monthlySearches.isEmpty()) {
            for (DataSeoMonthlySearch monthlySearch : monthlySearches) {
                int monthlySV = monthlySearch.getSearch_volume();
                int yearMonth;
                int year = monthlySearch.getYear();
                int month = monthlySearch.getMonth();
                if (month <= 9) {
                    yearMonth = Integer.parseInt(year + "0" + month);
                } else {
                    yearMonth = Integer.parseInt(year + "" + month);
                }
                if (startMonth == 0 || yearMonth < startMonth) {
                    startMonth = yearMonth;
                }
                if (endMonth == 0 || yearMonth > endMonth) {
                    endMonth = yearMonth;
                }
                svMap.put(yearMonth, monthlySV);
            }
        }
        if (isTest) {
            log.info("==volumeInfo kwId:{} sKw:{} eKw:{} avgSV:{} cpc:{} startMonth:{} endMonth:{} svMap:{}", kwId, decodeKw, encodeKw, avgSV, cpcStr, startMonth, endMonth, svMap);
        }
        svEntity.setDecodedKW(decodeKw);
        svEntity.setEncodedKW(encodeKw);
        svEntity.setAvgSV(avgSV);
        svEntity.setCpc(Double.parseDouble(cpcStr));
        svEntity.setStartMonth(startMonth);
        svEntity.setEndMonth(endMonth);
        svEntity.setSvMap(svMap);
        svEntity.setRcKeywordId(kwId);
        svEntity.setEngineId(engineId);
        svEntity.setLanguageId(languageId);
        setMonthlySV(endMonth, svMap, svEntity);
        return svEntity;
    }

    private void setMonthlySV(int endMonth, Map<Integer, Integer> svMap, SVEntity targetEntity) {
        try {
            targetEntity.setSv1(0);
            targetEntity.setSv2(0);
            targetEntity.setSv3(0);
            targetEntity.setSv4(0);
            targetEntity.setSv5(0);
            targetEntity.setSv6(0);
            targetEntity.setSv7(0);
            targetEntity.setSv8(0);
            targetEntity.setSv9(0);
            targetEntity.setSv10(0);
            targetEntity.setSv11(0);
            targetEntity.setSv12(0);
            if (svMap == null || svMap.isEmpty()) {
                log.info("==skipBySvMapIsNull kwId:{} sKw:{} eKw:{} endMonth:{}", targetEntity.getRcKeywordId(), targetEntity.getDecodedKW(), targetEntity.getEncodedKW(), endMonth);
                return;
            }
            Map<Integer, Integer> subMap = getMonthMap(endMonth);

            for (Map.Entry<Integer, Integer> entry : svMap.entrySet()) {
                int month = entry.getKey();
                Integer monthlySV = entry.getValue();
                if (subMap.get(month) != null) {
                    int mm = subMap.get(month);
                    switch (mm) {
                        case 1:
                            targetEntity.setSv1(monthlySV);
                            break;
                        case 2:
                            targetEntity.setSv2(monthlySV);
                            break;
                        case 3:
                            targetEntity.setSv3(monthlySV);
                            break;
                        case 4:
                            targetEntity.setSv4(monthlySV);
                            break;
                        case 5:
                            targetEntity.setSv5(monthlySV);
                            break;
                        case 6:
                            targetEntity.setSv6(monthlySV);
                            break;
                        case 7:
                            targetEntity.setSv7(monthlySV);
                            break;
                        case 8:
                            targetEntity.setSv8(monthlySV);
                            break;
                        case 9:
                            targetEntity.setSv9(monthlySV);
                            break;
                        case 10:
                            targetEntity.setSv10(monthlySV);
                            break;
                        case 11:
                            targetEntity.setSv11(monthlySV);
                            break;
                        case 12:
                            targetEntity.setSv12(monthlySV);
                            break;
                    }
                }
            }
        } catch (Exception exp) {
            exp.printStackTrace();
        }
    }

    private Map<Integer, Integer> getMonthMap(int lastRefreshMonth) throws Exception {
        Map<Integer, Integer> subMap = new HashMap<Integer, Integer>();
        Date lastMonth = DateUtils.parseDate(lastRefreshMonth + "01", "yyyyMMdd");
        for (int k = 0; k < 12; k++) {
            subMap.put(Integer.parseInt(org.apache.commons.lang3.time.DateFormatUtils.format(lastMonth, "yyyyMM")), Integer.parseInt(org.apache.commons.lang3.time.DateFormatUtils.format(lastMonth, "MM")));
            lastMonth = org.apache.commons.lang.time.DateUtils.addMonths(lastMonth, -1);
        }
        return subMap;
    }

    @Data
    @NoArgsConstructor
    @EqualsAndHashCode
    private class SVEntity{
        private String encodedKW;
        private String decodedKW;
        private int avgSV;
        private Double cpc;
        private Map<Integer, Integer> svMap;
        private String category;
        private Integer startMonth;
        private Integer endMonth;
        private int engineId;
        private int languageId;
        private int rcKeywordId;
        private int sv1;
        private int sv2;
        private int sv3;
        private int sv4;
        private int sv5;
        private int sv6;
        private int sv7;
        private int sv8;
        private int sv9;
        private int sv10;
        private int sv11;
        private int sv12;
    }

    private SeoClarityKeywordAdwordsEntity createAdwordsEntity(int languageId, SVEntity svEntity, Date createDate, int createDateInt, int uploadInfoId) {
        SeoClarityKeywordAdwordsEntity newAdwordsEntity = new SeoClarityKeywordAdwordsEntity();
        newAdwordsEntity.setKeywordId(svEntity.getRcKeywordId());
        newAdwordsEntity.setCityId(CITY_ID);
        newAdwordsEntity.setSearchEngineId(SeoClarityKeywordAdwordsEntity.FIXED_ENGINE_ID);
        newAdwordsEntity.setLanguageId(languageId);
        newAdwordsEntity.setAvgMonthlySearchVolume(svEntity.getAvgSV());
        newAdwordsEntity.setMonthlySearchVolume1(svEntity.getSv1());
        newAdwordsEntity.setMonthlySearchVolume2(svEntity.getSv2());
        newAdwordsEntity.setMonthlySearchVolume3(svEntity.getSv3());
        newAdwordsEntity.setMonthlySearchVolume4(svEntity.getSv4());
        newAdwordsEntity.setMonthlySearchVolume5(svEntity.getSv5());
        newAdwordsEntity.setMonthlySearchVolume6(svEntity.getSv6());
        newAdwordsEntity.setMonthlySearchVolume7(svEntity.getSv7());
        newAdwordsEntity.setMonthlySearchVolume8(svEntity.getSv8());
        newAdwordsEntity.setMonthlySearchVolume9(svEntity.getSv9());
        newAdwordsEntity.setMonthlySearchVolume10(svEntity.getSv10());
        newAdwordsEntity.setMonthlySearchVolume11(svEntity.getSv11());
        newAdwordsEntity.setMonthlySearchVolume12(svEntity.getSv12());
        newAdwordsEntity.setStartMonth(svEntity.getStartMonth());
        newAdwordsEntity.setEndMonth(svEntity.getEndMonth());
        newAdwordsEntity.setMonthlyRelationshipCreateDate(createDateInt);
        newAdwordsEntity.setCostPerClick(svEntity.getCpc());
        newAdwordsEntity.setCategory(svEntity.getCategory());
        newAdwordsEntity.setUploadInfoId(uploadInfoId);
        newAdwordsEntity.setCreateDate(createDate);
        return newAdwordsEntity;
    }

    /*public static List<String> wordTokenizerWithFullLanguage(String text, String languageName) {
        List<String> results = new ArrayList<String>();
        Analyzer sa;
        try {
            sa = new Analyzer() {
                @Override
                protected TokenStreamComponents createComponents(String fieldName) {
                    StandardTokenizer source = new StandardTokenizer();
                    TokenStream result = new ClassicFilter(source);
                    try {
                        TokenStream sownballfilter = new SnowballFilter(result, languageName);
                        return new TokenStreamComponents(source, sownballfilter);
                    } catch (Exception e) {
                        TokenStream sownballfilter = new SnowballFilter(result, "English");
                        return new TokenStreamComponents(source, sownballfilter);
                    }
                }
            };

            if (languageName.equalsIgnoreCase("Japanese")) {
                sa = new JapaneseAnalyzer();
            }
            TokenStream ts = sa.tokenStream("field", text);
            CharTermAttribute ch = ts.addAttribute(CharTermAttribute.class);
            ts.reset();
            while (ts.incrementToken()) {
                String str = ch.toString();
                results.add(str);
            }
            ts.end();
            ts.close();
            sa.close();
        } catch (Exception ex) {
            ex.printStackTrace();
        }

        return results;
    }*/


    private void processQc() {
        List<RgRealtimeExpansionEntity> expansionList = realtimeExpansionEntityDAO.getUnfinishedExpansions();
        if (expansionList == null || expansionList.isEmpty()) {
            log.info("====nothingRun time:{}", DateFormatUtils.format(new Date(), dataFormatter));
            return;
        }

        int dateMonthInt = Integer.parseInt(FormatUtils.formatDate(new Date(), "yyyyMM"));

        for (RgRealtimeExpansionEntity expansion : expansionList) {
            int id = expansion.getId();
            log.info("====begin process expansionId:{} time:{}", id, DateFormatUtils.format(new Date(), dataFormatter));
            startProcessQc(dateMonthInt, expansion);
            log.info("====end process expansionId:{} time:{}", id, DateFormatUtils.format(new Date(), dataFormatter));
        }
    }

    private void startProcessQc(int dateMonthInt, RgRealtimeExpansionEntity expansion) {
        int expansionId = expansion.getId();
        int engineId = expansion.getEngineId();
        int languageId = expansion.getLanguageId();

        List<RgRealtimeExpansionKeywordEntity> expansionKwList = realtimeExpansionKeywordEntityDAO.getByExpansionIdAndState(expansionId, RgRealtimeExpansionKeywordEntity.STATE_CANDIDATE_RG_KEYWORD);
        if (expansionKwList == null || expansionKwList.isEmpty()) {
            realtimeExpansionEntityDAO.updateStatusById(expansionId, RgRealtimeExpansionEntity.STATUS_PROCESS_ERROR);
            log.info("==emptyExpansionKw expansionId:{} dateMonth:{} time:{}", expansionId, dateMonthInt, DateFormatUtils.format(new Date(), dataFormatter));
            return;
        }
        int totalKwCnt = expansionKwList.size();
        Map<Integer, List<RgRealtimeExpansionKeywordEntity>> uploadStatusGroup = expansionKwList.stream().collect(Collectors.groupingBy(RgRealtimeExpansionKeywordEntity::getUploadStatus));
        if (!uploadStatusGroup.containsKey(RgRealtimeExpansionKeywordEntity.UPLOAD_STATUS_SUCCESS)) {
            log.info("==skipByNoUploadKw expansionId:{} dateMonth:{} totalKwCnt:{} time:{}", expansionId, dateMonthInt, totalKwCnt, DateFormatUtils.format(new Date(), dataFormatter));
            return;
        }

        List<RgRealtimeExpansionKeywordEntity> uploadKwList = uploadStatusGroup.get(RgRealtimeExpansionKeywordEntity.UPLOAD_STATUS_SUCCESS);
        if (uploadKwList == null || uploadKwList.isEmpty()) {
            log.info("==skipByUploadKwEmpty expansionId:{} dateMonth:{} totalKwCnt:{} time:{}", expansionId, dateMonthInt, totalKwCnt, DateFormatUtils.format(new Date(), dataFormatter));
            return;
        }
        int uploadCnt = uploadKwList.size();
        int kwMonthlyRelCnt = 0;
        int kwCdbDesktopRankCnt = 0;
        int kwCdbMobileRankCnt = 0;

        List<Long> kwIdList = uploadKwList.stream().map(RgRealtimeExpansionKeywordEntity::getKeywordId).collect(Collectors.toList());
        List<Integer> kwIdIntList = kwIdList.stream().map(var -> Integer.parseInt(String.valueOf(var))).collect(Collectors.toList());
        List<List<Integer>> kwIdSplitList = CollectionSplitUtils.splitCollectionBySize(kwIdIntList, BATCH_QUERY_SIZE);
        for (List<Integer> subKwIdList : kwIdSplitList) {
            List<SeoClarityKeywordMonthlySearchEngineRelationEntity> relList = seoClarityKeywordMonthlySearchEngineRelationEntityDAO.getBySeAndKwIdList(engineId, languageId, subKwIdList);
            if (relList != null && !relList.isEmpty()) {
                kwMonthlyRelCnt += relList.size();
            }


            int desktopRankCnt = rankingInfoDao.getKwCntByIds(expansionId, dateMonthInt, engineId, languageId, DomainSearchEngineRelEntity.DEVICE_DESKTOP, subKwIdList);
            kwCdbDesktopRankCnt += desktopRankCnt;

            int mobileRankCnt = rankingInfoDao.getKwCntByIds(expansionId, dateMonthInt, engineId, languageId, DomainSearchEngineRelEntity.DEVICE_MOBILE, subKwIdList);
            kwCdbMobileRankCnt += mobileRankCnt;

            try {
                TimeUnit.MILLISECONDS.sleep(200);
            } catch (InterruptedException e) {
                e.printStackTrace();
                Thread.interrupted();
            }
        }

        double expandKwRate = getPercent(uploadCnt, totalKwCnt);
        double relRate = getPercent(kwMonthlyRelCnt, totalKwCnt);
        double rankDesktopRate = getPercent(kwCdbDesktopRankCnt, totalKwCnt);
        double rankMobileRate = getPercent(kwCdbMobileRankCnt, totalKwCnt);
        double qcRate = 0.0;

        if (expandKwRate != relRate || expandKwRate != rankDesktopRate || expandKwRate != rankMobileRate) {
            // 如果不相等 取最小的rate 来判断是否过了qc
            qcRate = Math.min(
                    Math.min(expandKwRate, relRate),
                    Math.min(rankDesktopRate, rankMobileRate)
            );
        } else {
            qcRate = expandKwRate;
        }

        log.info("==qcCntInfo: expansionId:{} totalKwCnt:{} uploadedCnt:{} relCnt:{} rankDesktopCnt:{} rankMobileCnt:{} time:{}", expansionId, totalKwCnt, uploadCnt, kwMonthlyRelCnt, kwCdbDesktopRankCnt, kwCdbMobileRankCnt, DateFormatUtils.format(new Date(), dataFormatter));
        log.info("==qcRateInfo: expansionId:{} usedRate:{} uploadedRate:{} relRate:{} rankDesktopRate:{} rankMobileRate:{} time:{}", expansionId, qcRate, expandKwRate, relRate, rankDesktopRate, rankMobileRate, DateFormatUtils.format(new Date(), dataFormatter));

        if (qcRate < QC_PASS_RATE) {
            resendToQueue();
            return;
        }

        List<SearchVolumeV2Entity> addList = new ArrayList<>();
        List<RgRealtimeExpansionKeywordEntity> updateExpandsionKwList = new ArrayList<>();
        saveCdbSv(expansion, addList, updateExpandsionKwList);
    }

    private double getPercent(int a, int b) {
        double tmpValue = b * 1.0;
        return BigDecimal.valueOf((a / tmpValue) * 100).setScale(2, RoundingMode.HALF_UP).doubleValue();
    }

    private void resendToQueue() {

    }

    private void processTopicExplore() {
        String startDate = FormatUtils.formatDate(org.apache.commons.lang.time.DateUtils.truncate(org.apache.commons.lang.time.DateUtils.addDays(new Date(), CHECK_DAY_RANGE), Calendar.DAY_OF_MONTH), "yyyy-MM-dd HH:mm:ss");
        List<Integer> expansionIdList = realtimeExpansionKeywordEntityDAO.getTopicExplorerExpansionIdList(isTest, startDate);
        if (expansionIdList == null || expansionIdList.isEmpty()) {
            log.info("====nothingRunTopicExplore time:{}", DateFormatUtils.format(new Date(), dataFormatter));
            return;
        }

        int lastRefreshMonth = searchVolume1Dao.getLastRefreshMonth();
        List<SearchVolumeV2Entity> addList = new ArrayList<>();
        List<RgRealtimeExpansionKeywordEntity> updateExpandsionKwList = new ArrayList<>();

        log.info("==processTopicExplore expansionIdCnt:{} idInfo:{} time:{}", expansionIdList.size(), expansionIdList, DateFormatUtils.format(new Date(), dataFormatter));
        for (Integer expansionId : expansionIdList) {
            RgRealtimeExpansionEntity expansion = realtimeExpansionEntityDAO.getById(expansionId);
            saveCdbSv(expansion, addList, updateExpandsionKwList);
        }

        if (!addList.isEmpty()) {
            List<List<SearchVolumeV2Entity>> lists = CollectionSplitUtils.splitCollectionBySize(addList, BATCH_QUERY_SIZE);
            for (List<SearchVolumeV2Entity> list : lists) {
                if (!isTest) {
                    try {
                        searchVolume1Dao.insertBatch(list, lastRefreshMonth);
                    } catch (Exception e) {
                        log.info("==errorInsertSvDao1 addInfo:{} time:{}", gson.toJson(list), DateFormatUtils.format(new Date(), dataFormatter));
                        e.printStackTrace();
                    }
                    try {
                        searchVolume2Dao.insertBatch(list, lastRefreshMonth);
                    } catch (Exception e) {
                        log.info("==errorInsertSvDao2 addInfo:{} time:{}", gson.toJson(list), DateFormatUtils.format(new Date(), dataFormatter));
                        e.printStackTrace();
                    }
                } else {
                    log.info("==testInsertSvDao perAddCnt:{} addInfo:{}", list.size(), gson.toJson(list));
                }
            }
        }

        if (!updateExpandsionKwList.isEmpty()) {
            List<List<RgRealtimeExpansionKeywordEntity>> lists = CollectionSplitUtils.splitCollectionBySize(updateExpandsionKwList, BATCH_QUERY_SIZE);
            for (List<RgRealtimeExpansionKeywordEntity> list : lists) {
                if (!isTest) {
                    realtimeExpansionKeywordEntityDAO.updateTopicStatus(RgRealtimeExpansionKeywordEntity.EXPLORER_TOPIC_SUCCESS, list);
                } else {
                    log.info("==testUpdateExpansionKw perUpdateCnt:{} updateInfo:{}", list.size(), gson.toJson(list));
                }
            }
        }

        log.info("==endProcessTopicExplore addSvCnt:{} updateExpansionKwCnt:{} time:{}", addList.size(), updateExpandsionKwList.size(), DateFormatUtils.format(new Date(), dataFormatter));
    }

    private void saveCdbSv(RgRealtimeExpansionEntity expansion, List<SearchVolumeV2Entity> addList, List<RgRealtimeExpansionKeywordEntity> updateExpandsionKwList) {
        int expansionId = expansion.getId();
        int engineId = expansion.getEngineId();
        int languageId = expansion.getLanguageId();
        List<Integer> processKwIdList = new ArrayList<>();
        String languageName = getLanguageName(engineId, languageId, false);
        String fullLanguageName = getLanguageName(engineId, languageId, true);
        if (StringUtils.isEmpty(languageName)) {
            log.info("==skipSaveCdbSvByLanguageNameEmpty expansionId:{} engineId:{} languageId:{} languageName:{} fullLanguageName:{} time:{}", expansionId, engineId, languageId, languageName, fullLanguageName, DateFormatUtils.format(new Date(), dataFormatter));
            String subject = EMAIL_SUBJECT_PREFIX + "miss languageName";
            String content = "process TopicExplore not found language info, expansionId: " + expansionId + " engineId:" + engineId + " languageId:" + languageId + " languageName:" + languageName + " fullLanguageName:" + fullLanguageName;
            sendErrorAlertEmail(subject, content);
            return;
        }

        if (StringUtils.isEmpty(fullLanguageName)) {
            if (StringUtils.equalsIgnoreCase(languageName, "en")) {
                log.info("==missLanguageFullNameUseDefault expansionId:{} engineId:{} languageId:{} languageName:{}", expansionId, engineId, languageId, languageName);
                fullLanguageName = "English";
            } else {
                log.info("==skipSaveCdbSvByLanguageNameEmpty expansionId:{} engineId:{} languageId:{} languageName:{} fullLanguageName:{} time:{}", expansionId, engineId, languageId, languageName, fullLanguageName, DateFormatUtils.format(new Date(), dataFormatter));
                String subject = EMAIL_SUBJECT_PREFIX + "miss fullLanguageName";
                String content = "process TopicExplore not found language info, expansionId: " + expansionId + " engineId:" + engineId + " languageId:" + languageId + " languageName:" + languageName + " fullLanguageName:" + fullLanguageName;
                sendErrorAlertEmail(subject, content);
                return;
            }
        }
        log.info("==saveCdbSvLanguageInfo expansionId:{} engineId:{} languageId:{} languageName:{} fullLanguageName:{} time:{}", expansionId, engineId, languageId, languageName, fullLanguageName, DateFormatUtils.format(new Date(), dataFormatter));

        List<RgRealtimeExpansionKeywordEntity> kwList = realtimeExpansionKeywordEntityDAO.getKwListByExpansionIdAndState(expansionId, RgRealtimeExpansionKeywordEntity.STATE_CANDIDATE_RG_KEYWORD, RgRealtimeExpansionKeywordEntity.UPLOAD_STATUS_SUCCESS, RgRealtimeExpansionKeywordEntity.EXPLORER_TOPIC_NOT_PROCESS, isTest);
        if (kwList == null || kwList.isEmpty()) {
            log.info("==nothingToProcess expansionId:{} time:{}", expansionId, DateFormatUtils.format(new Date(), dataFormatter));
            return;
        }
        updateExpandsionKwList.addAll(kwList);
        List<Integer> kwIdList = kwList.stream().map(var -> Integer.parseInt(String.valueOf(var.getKeywordId()))).collect(Collectors.toList());
        List<List<Integer>> kwIdSplitList = CollectionSplitUtils.splitCollectionBySize(kwIdList, BATCH_QUERY_SIZE);
        int existKwCnt = 0;
        for (List<Integer> idList : kwIdSplitList) {
            List<Integer> existKwList = searchVolume1Dao.checkKeywordExists(engineId, languageId, idList);
            if (existKwList == null || existKwList.isEmpty()) {
                processKwIdList.addAll(idList);
            } else {
                existKwCnt += existKwList.size();
                List<Integer> notExistKwList = idList.stream().filter(var -> !existKwList.contains(var)).collect(Collectors.toList());
                processKwIdList.addAll(notExistKwList);
            }
        }
        if (processKwIdList.isEmpty()) {
            log.info("==skipProcessCdbSv2 expansionId:{} sKwCnt:{} existKwCnt:{} time:{}", expansionId, kwList.size(), existKwCnt, DateFormatUtils.format(new Date(), dataFormatter));
            return;
        }
        log.info("==processKwInfo expansionId:{} sKwCnt:{} processKwCnt:{} existKwCnt:{} time:{}", expansionId, kwList.size(), processKwIdList.size(), existKwCnt, DateFormatUtils.format(new Date(), dataFormatter));

        List<SeoClarityKeywordEntity> processList = new ArrayList<>();
        processExpandSvInfo(expansionId, languageId, kwList.size(), existKwCnt, processKwIdList, processList);

        if (processList.isEmpty()) {
            log.info("==skipProcessCdbSv1 expansionId:{} sKwCnt:{} existKwCnt:{} time:{}", expansionId, kwList.size(), existKwCnt, DateFormatUtils.format(new Date(), dataFormatter));
            return;
        }

        if (processList.size() != processKwIdList.size()) {
            log.info("===cntNotMatch expansionId:{} needProcessKwCnt:{} resKwCnt:{} existKwCnt:{} time:{}", expansionId, processKwIdList.size(), processList.size(), existKwCnt, DateFormatUtils.format(new Date(), dataFormatter));
        }

        processAdwordsSvInfo(expansionId, engineId, languageId, fullLanguageName, languageName, processKwIdList, processList, addList);

        if (addList.isEmpty()) {
            log.info("==skipProcessCdbSv3 expansionId:{} sKwCnt:{} existKwCnt:{} time:{}", expansionId, kwList.size(), existKwCnt, DateFormatUtils.format(new Date(), dataFormatter));
            return;
        }

        log.info("==processCdbSvInfo expansionId:{} addSvCnt:{} sKwCnt:{} processKwCnt:{} existKwCnt:{} time:{}", expansionId, addList.size(), kwList.size(), processKwIdList.size(), existKwCnt, DateFormatUtils.format(new Date(), dataFormatter));
    }

    private void processExpandSvInfo(int expansionId, int languageId, int totalKwCnt, int existKwCnt, List<Integer> processKwIdList, List<SeoClarityKeywordEntity> processList) {
        List<SeoClarityKeywordAdwordsExpandEntity> expandSvList = new ArrayList<>();
        List<List<Integer>> processSplitList = CollectionSplitUtils.splitCollectionBySize(processKwIdList, BATCH_QUERY_SIZE);
        List<Integer> notExistKwList = new ArrayList<>();
        for (List<Integer> idList : processSplitList) {
            List<SeoClarityKeywordAdwordsExpandEntity> existList = seoClarityKeywordAdwordsExpandDAO.getExistMonthList(SeoClarityKeywordAdwordsEntity.FIXED_ENGINE_ID, languageId, SeoClarityKeywordAdwordsExpandEntity.DEFAULT_CITY_ID, idList);
            if (existList != null && !existList.isEmpty()) {
                expandSvList.addAll(existList);
                List<Integer> existKwIdList = existList.stream().map(SeoClarityKeywordAdwordsExpandEntity::getKeywordId).collect(Collectors.toList());
                List<Integer> notExistKwIdList = idList.stream().filter(var -> !existKwIdList.contains(var)).collect(Collectors.toList());
                notExistKwList.addAll(notExistKwIdList);
            } else {
                notExistKwList.addAll(idList);
            }
        }

        if (!expandSvList.isEmpty()) {
            Map<Integer, List<SeoClarityKeywordAdwordsExpandEntity>> kwGroup = expandSvList.stream().collect(Collectors.groupingBy(SeoClarityKeywordAdwordsExpandEntity::getKeywordId));
            for (Integer kwId : kwGroup.keySet()) {
                List<SeoClarityKeywordAdwordsExpandEntity> adwordsExpandList = kwGroup.get(kwId);
                adwordsExpandList.sort((o1, o2) -> {
                    if (o1.getMonth() > o2.getMonth()) {
                        return 1;
                    } else if (o1.getMonth() < o2.getMonth()) {
                        return -1;
                    }
                    return 0;
                });

                List<SeoClarityKeywordEntity.MonthlySearchVolume> svList = new ArrayList<>(0);
                SeoClarityKeywordEntity kw = new SeoClarityKeywordEntity();

                for (SeoClarityKeywordAdwordsExpandEntity adwordsExpand : adwordsExpandList) {
                    int month = adwordsExpand.getMonth();
                    int monthly_search_volume = adwordsExpand.getMonthlySearchVolume();
                    String cpc = "0";
                    // todo cpc is zero???
                    /*Double costPerClick = adwordsExpand.getCostPerClick();
                    if (costPerClick != null) {
                        cpc = costPerClick.toString();
                    }*/
                    SeoClarityKeywordEntity.MonthlySearchVolume sv = kw.new MonthlySearchVolume(month, 0, monthly_search_volume, cpc);
                    svList.add(sv);
                }

                kw.setId(Integer.valueOf(kwId));
                kw.setMonthlySearchvolumeTrends(svList);
                processList.add(kw);
            }
        }

        if (!notExistKwList.isEmpty()) {
            for (Integer kwId : notExistKwList) {
                SeoClarityKeywordEntity kw = new SeoClarityKeywordEntity();
                kw.setId(kwId);
                kw.setMonthlySearchvolumeTrends(new ArrayList<>(0));
                processList.add(kw);
            }
        }

        log.info("==processExpandSvInfo expansionId:{} sKwCnt:{} processKwCnt:{} resKwCnt:{} existKwCnt:{} time:{}", expansionId, totalKwCnt, processKwIdList.size(), processList.size(), existKwCnt, DateFormatUtils.format(new Date(), dataFormatter));
    }


    private void processAdwordsSvInfo(int expansionId, int engineId, int languageId, String fullLanguageName, String languageName,
                                      List<Integer> processKwIdList, List<SeoClarityKeywordEntity> kwList, List<SearchVolumeV2Entity> addList) {
        List<List<Integer>> kwIdLists = CollectionSplitUtils.splitCollectionBySize(processKwIdList, BATCH_QUERY_SIZE);
        Map<Integer, KeywordAdwordsEntity> svMap = new HashMap<>();
        for (List<Integer> kwIdList : kwIdLists) {
            svMap.putAll(getSvMap(languageId, kwIdList));
        }
        log.info("==processAdwordsSvInfoRes expansionId:{} sKwCnt:{} adwordsExpandCnt:{} adwordsSvMapCnt:{} time:{}", expansionId, processKwIdList.size(), kwList.size(), svMap.size(), DateFormatUtils.format(new Date(), dataFormatter));
        parseKeywordTokenizerSvEntityList(expansionId, engineId, languageId, languageName, fullLanguageName,svMap, kwList, addList);
        processTrueDemand(expansionId, engineId, languageId, addList);
    }

    private Map<Integer, KeywordAdwordsEntity> getSvMap(int language, List<Integer> queryList) {
        Map<Integer, KeywordAdwordsEntity> svMap = new HashMap<>();
        if (!queryList.isEmpty()) {
            List<Map<String, Object>> mapList = keywordEntityDAO.getMonthlyRelationsKeywordsWithAdwordsData(SeoClarityKeywordAdwordsEntity.FIXED_ENGINE_ID, language, queryList);;
            if (isTest) {
                log.info("==getAdwordsSvMapPerInfo queryCnt:{} resCnt:{} kwId:{} time:{}", queryList.size(), mapList.size(), queryList, DateFormatUtils.format(new Date(), dataFormatter));
            }
            for (Map<String, Object> map : mapList) {
                try {
                    int kwId = Integer.valueOf(map.get("kwId").toString());
                    String keyword_text = map.get("keyword_text") == null ? "" : map.get("keyword_text").toString();
                    String category = map.get("category") == null ? "" : map.get("category").toString();
                    int monthly_relationship_create_date = Integer.valueOf(map.get("monthly_relationship_create_date") == null ? "0" : map.get("monthly_relationship_create_date").toString());
                    int avg_monthly_search_volume = Integer.valueOf(map.get("avg_monthly_search_volume") != null ? map.get("avg_monthly_search_volume").toString() : "-1");
                    String cost_per_click = map.get("cost_per_click") == null ? "0" : map.get("cost_per_click").toString();
                    int monthly_search_volume1 = Integer.valueOf(map.get("monthly_search_volume1") == null ? "0" : map.get("monthly_search_volume1").toString());
                    int monthly_search_volume2 = Integer.valueOf(map.get("monthly_search_volume2") == null ? "0" : map.get("monthly_search_volume2").toString());
                    int monthly_search_volume3 = Integer.valueOf(map.get("monthly_search_volume3") == null ? "0" : map.get("monthly_search_volume3").toString());
                    int monthly_search_volume4 = Integer.valueOf(map.get("monthly_search_volume4") == null ? "0" : map.get("monthly_search_volume4").toString());
                    int monthly_search_volume5 = Integer.valueOf(map.get("monthly_search_volume5") == null ? "0" : map.get("monthly_search_volume5").toString());
                    int monthly_search_volume6 = Integer.valueOf(map.get("monthly_search_volume6") == null ? "0" : map.get("monthly_search_volume6").toString());
                    int monthly_search_volume7 = Integer.valueOf(map.get("monthly_search_volume7") == null ? "0" : map.get("monthly_search_volume7").toString());
                    int monthly_search_volume8 = Integer.valueOf(map.get("monthly_search_volume8") == null ? "0" : map.get("monthly_search_volume8").toString());
                    int monthly_search_volume9 = Integer.valueOf(map.get("monthly_search_volume9") == null ? "0" : map.get("monthly_search_volume9").toString());
                    int monthly_search_volume10 = Integer.valueOf(map.get("monthly_search_volume10") == null ? "0" : map.get("monthly_search_volume10").toString());
                    int monthly_search_volume11 = Integer.valueOf(map.get("monthly_search_volume11") == null ? "0" : map.get("monthly_search_volume11").toString());
                    int monthly_search_volume12 = Integer.valueOf(map.get("monthly_search_volume12") == null ? "0" : map.get("monthly_search_volume12").toString());

                    KeywordAdwordsEntity ads = new KeywordAdwordsEntity();
                    ads.setKeywordId(kwId);
                    ads.setKeywordName(keyword_text);
                    ads.setCategory(category);
                    ads.setAvgMonthlySearchVolume(avg_monthly_search_volume);
                    ads.setCostPerClick(Double.valueOf(cost_per_click));
                    ads.setMonthlyRelationshipCreateDate(monthly_relationship_create_date);
                    ads.setMonthlySearchVolume1(monthly_search_volume1);
                    ads.setMonthlySearchVolume2(monthly_search_volume2);
                    ads.setMonthlySearchVolume3(monthly_search_volume3);
                    ads.setMonthlySearchVolume4(monthly_search_volume4);
                    ads.setMonthlySearchVolume5(monthly_search_volume5);
                    ads.setMonthlySearchVolume6(monthly_search_volume6);
                    ads.setMonthlySearchVolume7(monthly_search_volume7);
                    ads.setMonthlySearchVolume8(monthly_search_volume8);
                    ads.setMonthlySearchVolume9(monthly_search_volume9);
                    ads.setMonthlySearchVolume10(monthly_search_volume10);
                    ads.setMonthlySearchVolume11(monthly_search_volume11);
                    ads.setMonthlySearchVolume12(monthly_search_volume12);
                    svMap.put(ads.getKeywordId(), ads);
                } catch (Exception e) {
                    System.out.println("===parse ads failed. map:" + map + ", language:" + language);
                    e.printStackTrace();
                }
            }
        }
        return svMap;
    }

    private void parseKeywordTokenizerSvEntityList(int expansionId, int engineId, int languageId, String languageName, String fullLanguageName, Map<Integer, KeywordAdwordsEntity> svMap, List<SeoClarityKeywordEntity> kwList, List<SearchVolumeV2Entity> addList) {
        for (SeoClarityKeywordEntity kw : kwList) {
            try {
                // get sv
                KeywordAdwordsEntity sv = null;
                if (!svMap.containsKey(kw.getId())) {
                    log.info("=noSvfound, expansionId:{} engine:{} language:{} kid:{}", expansionId, engineId, languageId, kw.getId());
                    continue;
                }
                sv = svMap.get(kw.getId());
                if (sv == null) {
                    System.out.println("=Wrong kw, expansionId:" + expansionId + ", engine:" + engineId + ", language:" + languageId + ", kid:" + kw.getId()
                            + ", kw:" + (sv == null ? null : sv.getKeywordName()) + ", sv:" + (sv == null ? null : sv.getAvgMonthlySearchVolume())
                            + ", sv trend:" + (kw.getMonthlySearchvolumeTrends() == null ? 0 : kw.getMonthlySearchvolumeTrends().size()));
                    continue;
                }

                KeywordRankVO vo = new KeywordRankVO();
                vo.setKeyword(StringUtils.lowerCase(sv.getKeywordName()));
                String kwName = vo.getKeywordForHtml().toLowerCase();

                if (StringUtils.isBlank(kwName)) {
                    System.out.println("Skip empty keyword, engine:" + engineId + ", language:" + languageId + ", kwName:" + kwName + ", kid:" + kw.getId());
                    continue;
                }

                // check unsupport characters
                if (MonthlyKeywordTokenizerUploadV2.existsExcludeChar(kwName)) {
                    System.out.println("Skip un-support character keyword, engine:" + engineId + ", language:" + languageId + ", kwName:" + kwName + ", kid:" + kw.getId());
                    continue;
                }
                SearchVolumeV2Entity entity = new SearchVolumeV2Entity();
                entity.setEngineId(engineId);
                entity.setLanguageId(languageId);
                entity.setKeywordRankcheckId(Long.valueOf(kw.getId()));
                entity.setKeywordName(kwName);
                entity.setLocationId(0);

                // load 24 month
                List<SeoClarityKeywordEntity.MonthlySearchVolume> svList = kw.getMonthlySearchvolumeTrends();
                List<Integer> monthList = new ArrayList<Integer>();
                List<Integer> svTrendList = new ArrayList<Integer>();
                if (svList != null) {
                    for (SeoClarityKeywordEntity.MonthlySearchVolume msv : svList) {
                        monthList.add(msv.getMonth());
                        svTrendList.add(msv.getMonthlySearchVolume());
                    }
                }
                entity.setMonthlySvAttrKeys(monthList);
                entity.setMonthlySvAttrValues(svTrendList);

                List<String> word = new ArrayList<String>();
                List<String> stream = new ArrayList<String>();

                List<String> keywordVariationOneword = new ArrayList<String>();
                List<String> keywordVariationNgram = new ArrayList<String>();

                word.addAll(SnowBallAndNgramForForeignLanguages.wordTokenizer(kwName, languageName));
                stream.addAll(SnowBallAndNgramForForeignLanguages.wordStemmerTokenizer(kwName, fullLanguageName));
                keywordVariationOneword.addAll(SnowBallAndNgramForForeignLanguages.oneWordNgramTokenizer(kwName));
                keywordVariationNgram.addAll(SnowBallAndNgramForForeignLanguages.wordNgramTokenizer(kwName, false));

                entity.setWord(word);
                entity.setStream(stream);

                entity.setKeywordVariationOneword(keywordVariationOneword);
                entity.setKeywordVariationNgram(MonthlyKeywordTokenizerUploadV2.setPlaceHolder(keywordVariationNgram));
                int searchVolume = sv == null || sv.getAvgMonthlySearchVolume() == null ? -1: sv.getAvgMonthlySearchVolume();
                entity.setHasSv(searchVolume >= 0);
                entity.setAvgSearchVolume(Math.max(searchVolume, 0));
                if (searchVolume < 0) {
                    entity.setMonthlySvAttrKeys(new ArrayList<>(0));
                    entity.setMonthlySvAttrValues(new ArrayList<>(0));
                    System.out.println("===not has sv kw, kid:" + kw.getId() + ", engine:" + engineId + ", language:" + languageId + ", searchVolume:" + searchVolume);
                }
                entity.setMonthlySearchVolume1(sv == null || sv.getMonthlySearchVolume1() == null ? 0 : sv.getMonthlySearchVolume1());
                entity.setMonthlySearchVolume2(sv == null || sv.getMonthlySearchVolume2() == null ? 0 : sv.getMonthlySearchVolume2());
                entity.setMonthlySearchVolume3(sv == null || sv.getMonthlySearchVolume3() == null ? 0 : sv.getMonthlySearchVolume3());
                entity.setMonthlySearchVolume4(sv == null || sv.getMonthlySearchVolume4() == null ? 0 : sv.getMonthlySearchVolume4());
                entity.setMonthlySearchVolume5(sv == null || sv.getMonthlySearchVolume5() == null ? 0 : sv.getMonthlySearchVolume5());
                entity.setMonthlySearchVolume6(sv == null || sv.getMonthlySearchVolume6() == null ? 0 : sv.getMonthlySearchVolume6());
                entity.setMonthlySearchVolume7(sv == null || sv.getMonthlySearchVolume7() == null ? 0 : sv.getMonthlySearchVolume7());
                entity.setMonthlySearchVolume8(sv == null || sv.getMonthlySearchVolume8() == null ? 0 : sv.getMonthlySearchVolume8());
                entity.setMonthlySearchVolume9(sv == null || sv.getMonthlySearchVolume9() == null ? 0 : sv.getMonthlySearchVolume9());
                entity.setMonthlySearchVolume10(sv == null || sv.getMonthlySearchVolume10() == null ? 0 : sv.getMonthlySearchVolume10());
                entity.setMonthlySearchVolume11(sv == null || sv.getMonthlySearchVolume11() == null ? 0 : sv.getMonthlySearchVolume11());
                entity.setMonthlySearchVolume12(sv == null || sv.getMonthlySearchVolume12() == null ? 0 : sv.getMonthlySearchVolume12());
                entity.setCpc(sv == null || sv.getCostPerClick() == null ? 0 : Float.valueOf(String.valueOf(sv.getCostPerClick())));
                if (sv == null || sv.getCategoryList() == null || sv.getCategoryList().isEmpty()) {
//						System.out.println("Keyword category not exists, kid:" + kw.getId() + ", engine:" + engine + ", language:" + language);
                } else {
                    entity.setCategory(sv.getCategoryList());
                }
                entity.setSign(1);
                entity.setVersioning(0);

                addList.add(entity);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private void processTrueDemand(int expansionId, int engineId, int languageId, List<SearchVolumeV2Entity> addList) {
        String countryCode = "";
        List<String> countryCodeList = keywordStreamDAO.getCountryCode(engineId, languageId);
        if (countryCodeList != null && !countryCodeList.isEmpty()) {
            countryCode = countryCodeList.get(0);
        }

        boolean isBigV2 = false;
        if (DISTINCT_COUNTRY_CODE_IN_KEYWORD_SUMMARY_ANNUAL_BIG_TABLE.contains(countryCode)) {
            isBigV2 = true;
        }

        Map<Integer, String> kwIdPHashMap = new HashMap<>();
        Map<Integer, String> kwIdPMurHashMap = new HashMap<>();
        Map<String, SearchVolumeV2Entity> kwHashPKwIdPMap = new HashMap<>();
        List<Integer> notInFileKwIdList = new ArrayList<>();
        int haveTrueDemandCnt = 0;
        int notHaveTrueDemandCnt = 0;

        for (SearchVolumeV2Entity rawKw : addList) {
            int kwId = Integer.parseInt(rawKw.getKeywordRankcheckId().toString());
            String keywordName = rawKw.getKeywordName();
            String kwHash = CityHashUtil.getUnsignedUrlHash(keywordName);
            String murmurHash = MurmurHashUtils.getMurmurHash3_64(keywordName);

            kwIdPHashMap.put(kwId, kwHash);
            kwIdPMurHashMap.put(kwId, murmurHash);
            kwHashPKwIdPMap.put(kwHash, rawKw);
            notInFileKwIdList.add(kwId);
        }

        List<String> queryParamList = new ArrayList<>();
        String queryParam = "";


        for (Integer kwId : notInFileKwIdList) {
            if (isBigV2) {
                queryParam = keywordSummaryAnnualBigV2DAO.getQueryParam(kwIdPHashMap.get(kwId), kwIdPMurHashMap.get(kwId), countryCode);
            } else {
                queryParam = keywordSummaryAnnualNormalV2DAO.getQueryParam(kwIdPHashMap.get(kwId), kwIdPMurHashMap.get(kwId), countryCode);
            }
            if (StringUtils.isNotBlank(queryParam)) {
                queryParamList.add(queryParam);
            }
        }

        List<KeywordStreamEntity> keywordStreamEntityList = new ArrayList<>();
        if (!queryParamList.isEmpty()) {
            List<List<String>> queryParamsInSummaryList = CollectionSplitUtils.splitCollectionBySize(queryParamList, BATCH_QUERY_SIZE);
            for (List<String> paramsInSummaryNormalList : queryParamsInSummaryList) {
                if (isBigV2) {
                    List<KeywordStreamEntity> keywordStreamList = keywordSummaryAnnualBigV2DAO.getByParamList(paramsInSummaryNormalList);
                    if (keywordStreamList != null && !keywordStreamList.isEmpty()) {
                        keywordStreamEntityList.addAll(keywordStreamList);
                    }
                } else {
                    List<KeywordStreamEntity> keywordStreamList = keywordSummaryAnnualNormalV2DAO.getByParamList(paramsInSummaryNormalList);
                    if (keywordStreamList != null && !keywordStreamList.isEmpty()) {
                        keywordStreamEntityList.addAll(keywordStreamList);
                    }
                }
            }
        }

        if (!keywordStreamEntityList.isEmpty()) {
            Map<String, KeywordStreamEntity> hashPCustomSvMap = keywordStreamEntityList.stream().collect(Collectors.toMap(KeywordStreamEntity::getKeywordHash, var2 -> var2, (var3, var4) -> var3));
            for (SearchVolumeV2Entity tokenizer : addList) {
                int kwId = Integer.parseInt(tokenizer.getKeywordRankcheckId().toString());
                String kwHash = kwIdPHashMap.get(kwId);
                if (hashPCustomSvMap.containsKey(kwHash)) {
                    KeywordStreamEntity keywordStreamEntity = hashPCustomSvMap.get(kwHash);

                    Float avgSearchVolume = keywordStreamEntity.getAvgSearchVolume();
                    long searchVolume = CustomSearchVolumeEntity.SvRound(avgSearchVolume.longValue());
                    List<Integer> svAttrIntKey = keywordStreamEntity.getSvAttrIntKey();
                    List<Integer> svAttrIntValue = keywordStreamEntity.getSvAttrIntValue();

                    tokenizer.setTrueDemand(searchVolume);
                    tokenizer.setTrueDemandAttrKeys(svAttrIntKey);
                    tokenizer.setTrueDemandAttrValues(svAttrIntValue);
                    haveTrueDemandCnt++;
                } else {
                    notHaveTrueDemandCnt++;
                }
            }
        }
        log.info("==trueDemandInfo expansionId:{} engineId:{} languageId:{} totalCnt:{} haveTrueDemandCnt:{} notHaveTrueDemandCnt:{}", expansionId, engineId, languageId, addList.size(), haveTrueDemandCnt, notHaveTrueDemandCnt);
    }

}
