
package seoclarity.backend.cronjob.tiktok;

import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Properties;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.consumer.Consumer;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.common.config.SaslConfigs;
import org.apache.kafka.common.serialization.StringDeserializer;

import com.google.gson.Gson;

import seoclarity.backend.utils.FormatUtils;

// mvn exec:java -Dexec.mainClass="seoclarity.backend.upload.sitemap.SitemapConsumer" -Dexec.args=""
public class TiktokConsumer {
	
	//  internal IP should update to 9092
	private final static String BOOTSTRAP_SERVERS = "************:9093,************:9093,************:9093";
	
	public static String crawlingFolderPath = "/home/<USER>/tiktok/crawling";
    public static String needUploadFolderPath = "/home/<USER>/tiktok/needUpload";
    
    // tiktok engineId = 114, so it will be always intl
//	public static final String TIKTOK_US_TOPIC = "ritiktok_7_us_d_nat";
	public static final String TIKTOK_INTL_TOPIC = "ritiktok_7_intl_d_nat";
    
	public TiktokConsumer() {
		
	}
    
	private static Gson gson = new Gson();
	
	public static final char FILE_SPLIT = '\t';
	public static final String ENCODING = "UTF-8";
	
	public final static String TIKTOK_COUNTRY_US = "us";
	public final static String TIKTOK_COUNTRY_INTL = "intl";
	
	public final static Integer TIKTOK_COUNTRY_US_INT = 1;
	public final static Integer TIKTOK_COUNTRY_INTL_INT = 2;

	public static void main(String[] args) throws Exception {
		
		TiktokConsumer internalLinkCrawlerV4 = new TiktokConsumer();
		internalLinkCrawlerV4.runConsumer();
	}

	private static Consumer<Long, String> createConsumer() {
		final Properties props = new Properties();
		props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, BOOTSTRAP_SERVERS);
		props.put(ConsumerConfig.GROUP_ID_CONFIG, "tiktok_consumer_group_20250409");
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
        
		props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "earliest");
        props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false);
        
     // site clarity kakfa
        props.put(CommonClientConfigs.SECURITY_PROTOCOL_CONFIG, "SASL_PLAINTEXT");
        props.put(SaslConfigs.SASL_JAAS_CONFIG, "org.apache.kafka.common.security.plain.PlainLoginModule required username=se0Clarity99 password=LmNoPq123rStUv456WxYzAbC789dEfGhIjKlMnOpQrStUvWxYzAbCdEfGhIjKlMnOpQrStUvWxYzAbCdEfGhIjKlMnOpOSOFJI0k;");
        props.put(SaslConfigs.SASL_MECHANISM, "PLAIN");
        
		// Create the consumer using props.
		final Consumer<Long, String> consumer = new KafkaConsumer<>(props);

		// Subscribe to the topic.
		consumer.subscribe(Collections.singletonList(TIKTOK_INTL_TOPIC));
		return consumer;
	}
	
	private void runConsumer() throws InterruptedException {
		
		final Consumer<Long, String> consumer = createConsumer();
		List<String> entityList = new ArrayList<String>();
		
		File crawlingFolder = new File(crawlingFolderPath);
		if (crawlingFolder == null || !crawlingFolder.exists() || !crawlingFolder.isDirectory()) {
			
			System.out.println("folder crawlering is not exist, creating now!! ");
			crawlingFolder.mkdirs();
		}
		
		File needUploadFolder = new File(needUploadFolderPath);
		if (needUploadFolder == null || !needUploadFolder.exists() || !needUploadFolder.isDirectory()) {
			
			System.out.println("folder needUpload is not exist, creating now!! ");
			needUploadFolder.mkdirs();
		}
		
	
		try {
			int recordCnt = 0;
			
			String prefix = "tiktok_" + FormatUtils.formatDateToYyyyMmDd(new Date()) + "_";
			File tempfile = File.createTempFile(prefix, ".processing", crawlingFolder);
			System.out.println("Create file:" + tempfile.getAbsolutePath());
			
			Integer num = 0;
			while (true) {
				ConsumerRecords<Long, String> consumerRecords = consumer.poll(5 * 1000);
				
				if (consumerRecords.count() == 0) {
					
					try {
						System.out.println("sleep 5 sec!");
						//sleep 10 second if no incoming
						Thread.sleep(5 * 1000);
						
						num++;
						if (num >= 50) {
							recordCnt = recordCnt + entityList.size();
							
							System.out.println("adding records count:" + entityList.size() + ", total records ： " + recordCnt);
							output(tempfile, entityList);
							
							moveFileFromCrawlingToNeedUpload(tempfile);
							
							break;
						}
					} catch (Exception e) {
						e.printStackTrace();
					}

				} else {
					num = 0;
				}
				System.out.println("polled " + consumerRecords.count() + " records!");
				
				for(ConsumerRecord<Long, String> record : consumerRecords) {
					
					entityList.add(record.value());
				}
				
				if (entityList.size() > 0) {
					
					recordCnt = recordCnt + entityList.size();
					
					System.out.println("adding records count:" + entityList.size() + ", total records ： " + recordCnt);
					output(tempfile, entityList);
					
					entityList.clear();
				}
				
				consumer.commitAsync((offsets, exception) -> {
                    if (exception != null) {
                        System.out.println("commit failed for offsets: " + offsets + "\n exp: " + exception);
                    }
                    System.out.println("commit successful for offsets: " + offsets);
                });
				
				if (recordCnt >= 200) {
					
					//rename file from .processing to .txt and transfor to needUpload folder
					moveFileFromCrawlingToNeedUpload(tempfile);
					
					//create a new temp file
					tempfile = File.createTempFile(prefix, ".processing", crawlingFolder);
					System.out.println(" ======= store in new file:" + tempfile.getAbsolutePath());
					recordCnt = 0;
				}
			}
			
		} catch (Exception e) {
			e.printStackTrace();
		}
		
	}
	
	private static void output(File file, List<String> outLines) throws Exception{
		
		FileUtils.writeLines(file, "utf-8", outLines, true);
	}
	
	
	public static void moveFileFromCrawlingToNeedUpload(File tempfile) {
		try {
			if (tempfile != null && tempfile.exists() && tempfile.isFile()) {
				
				String targetFileName = StringUtils.replace(tempfile.getName(), ".processing", ".txt");
				
				System.out.println("targetFileName:" + targetFileName);
				
					
				File doneFolder = new File(needUploadFolderPath);
				if (doneFolder == null || !doneFolder.isDirectory()) {
					System.out.println("Target folder is not exist!!! folder:" + needUploadFolderPath);
					doneFolder.mkdirs();
				}
				
				File targetFile = new File(needUploadFolderPath + "/" + targetFileName);
				
				FileUtils.moveFile(tempfile, targetFile);
				
				System.out.println("Rename file from " + doneFolder.getAbsolutePath() + " to " + targetFile.getAbsolutePath());
				
			}
			
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
}
