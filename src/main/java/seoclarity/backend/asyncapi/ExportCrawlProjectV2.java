package seoclarity.backend.asyncapi;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import org.testcontainers.shaded.okhttp3.*;
import seoclarity.backend.dao.actonia.CrawlRequestLogDAO;
import seoclarity.backend.entity.actonia.ApiTaskInfoEntity;
import seoclarity.backend.utils.EmailSenderComponent;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.utils.ZipFileUtils;

import java.io.BufferedReader;
import java.io.File;
import java.io.InputStreamReader;
import java.net.SocketTimeoutException;
import java.net.URLEncoder;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static seoclarity.backend.asyncapi.AsynGetCheckidV3.getAlphaNumericString;

//last version
//mvn exec:java -Dexec.mainClass="seoclarity.backend.asyncapi.ExportCrawlProjectV2" -Dexec.cleanupDaemonThreads=false
public class ExportCrawlProjectV2 {
    private static final int MAX_LIST_SIZE = 1000;
    private static final int GET_TIME_OUT = 10 * 1000;
    private static final String END_POINT = "https://event.seoclarity.workers.dev";
    private static final String LIST_BY_KEY_WITH_CURSOR = "/cursor/{cursor}?{prefix}";
    private static final String GET_BY_KEY = "/key/{task_id}";
    private static final String LIST_BY_KEY = "/list/{prefix}";
    private static final String zeroRanksql = "as csvurl, '-' as url0,'-' rank0  format JSONEachRow";

    //***************************************************
    public static final String BASEPREFIX = "asyncdownload_contentgap_estdTrafficKWDetailNew_ownCustom_";
    private static final String LOCAL_STORE_DIR = "/tmp/testMarven/";
    //***************************************************


    private static final String DELETE_BY_KEY = "/delete/{task_id}";
    public static final int DOWNLOADMAXCOUNT = 10;
    private static  List<String> listCache = null;
    private static final String HEADER_TOKEN_KEY = "seoclarity-internal-token";
    private static final String HEADER_TOKEN_VALUE = "6603b708dd0bf24b0e7a1e68408c454e";
    private static Integer READ_TIMEOUT = 1800;
    public static int HTTP_EXPORT_RESPONSE_SCUCCESS=200;
    public static int HTTP_EXPORT_RESPONSE_ERROR=400;
    public static int HTTP_EXPORT_CLARITYDB_TO_FILE_FAILD=-2;
    public static int HTTP_EXPORT_CLARITYDB_TO_FILE_EXCEPTION=-1;
    private static final Map<String, String> CACHE_HEADER_MAP = new HashMap<>();
    static {
        CACHE_HEADER_MAP.put(HEADER_TOKEN_KEY, HEADER_TOKEN_VALUE);
    }
    public static String DBURL = "http://184.154.118.206:8123";
    public static String USER = "default";
    public static String PSW = "clarity99!";
    public static String FILE_FORMATE_CSV = "CSV";// "format": "CSV",    -- optional            [CSV,JSON]
    public static String FILE_FORMATE_JSON = "JSON";
    public static String FAILED_TO_STORAGE_MESSAGE = "Failed to connect to specified server, upload to default server";
    // https://www.wrike.com/open.htm?id=908593946
    private final static String TASK_NAME_CONTENTGAP_ESTDTRAFFIC = "ContentGap_EstdTraffic";
    // https://www.wrike.com/open.htm?id=913173890
    private final static String TASK_NAME_CONTENTGAP_COMPARERANK = "ContentGap_CompareRank";
    private final static int INTERVAL = 50000;

    // https://www.wrike.com/open.htm?id=919475248
    private final static String TASK_NAME_RESEARCHGRID_KEYWORDDETAIL = "ResearchGrid_KeywordDetail";
    // https://www.wrike.com/open.htm?id=922405732
    private final static String TASKNAME_RESEARCHGRID_TOPPAGES = "ResearchGrid_TopPages";
    // https://www.wrike.com/open.htm?id=922405358
    private final static String TASKNAME_RESEARCHGRID_RANKSUMMARY = "ResearchGrid_RankSummary";
    // https://www.wrike.com/open.htm?id=922406027
    private final static String TASKNAME_RESEARCHGRID_PAGESTREND = "ResearchGrid_PageTrend";
    private List<ApiTaskInfoEntity> apiTaskInfoEntitys;
    // https://www.wrike.com/open.htm?id=908593946
    public final static String TASKNAME_CONTENTFUSION_KEYWORD = "Contentfusion_Keyword";
    public final static String TASKNAME_CONTENTFUSION_KEYWORD_REALTIME = "Contentfusion_Keyword_Realtime";
    private static String TARGETPATH = "/home/<USER>/public_html/tasks/";
    private static DateFormat SDF = new SimpleDateFormat("yyyy-MM-dd");

    private static String URL = "http://downloads.seoclarity.net/tasks";
    private static String URL_SEPARATOR = "/";
    private static String NOTASK = "Task is not ready";
    private  static int GETTASKINFOFLAG = 0;
    private static String LOG_DIR = "/tmp/contentGap/log/";
    private static String FAILED_SQL_LOG_NAME = "failed_sql.log";
    private static String SEND_TO = "<EMAIL>";
    private static String[] CC_TOS = new String[]{"<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"};

    private EmailSenderComponent emailSenderComponent;

    private static CrawlRequestLogDAO crawlRequestLogDAO;
    private List<String> taskGroups = new ArrayList<>();


    public static final String ESCAPED_FW_SLASH = "\\";
    public static final String SQL_ESCAPED_FW_SLASH = "\\\\";
    public static final String SQL_ESCAPED_SINGLE_QUOTE = "\\'";
    public static final String NULL_CHARACTER = "\0";
    public static final String SQL_ESCAPED_NULL_CHARACTER = "\\0";
    public static final String LINE_FEED_CHARACTER = "\n";
    public static final String SQL_ESCAPED_LINE_FEED = "\\n";
    public static final String CARRIAGE_RETURN_CHARACTER = "\r";
    public static final String SQL_ESCAPED_CARRIAGE_RETURN = "\\r";
    public static final String ESCAPED_DOUBLE_QUOTES = "\"";
    public static final String SQL_ESCAPED_DOUBLE_QUOTES = "\\\"";
    public static final String SUBSTITUTE_CHARACTER = "\\x1a";
    public static final String SQL_ESCAPED_SUBSTITUTE_CHARACTER = "\\Z";

    public ExportCrawlProjectV2() {
        //emailSenderComponent = SpringBeanFactory.getBean("emailSenderComponent");
        crawlRequestLogDAO = SpringBeanFactory.getBean("crawlRequestLogDAO");
        if(crawlRequestLogDAO==null){
            System.out.println("=============");
        }
        File path = new File(LOG_DIR);
        if (!path.exists()) path.mkdirs();
        path = new File(LOCAL_STORE_DIR);
        if (!path.exists()) path.mkdirs();
    }

   static String getQuery(String crawlid,String domainid){
        StringBuffer buffer =new StringBuffer();
        buffer.append("select count(distinct(url)) crawlPagesCount,min(request_time) crawl_start ,max(request_time) crawl_end    from dis_site_crawl_doc where domain_id  = "+domainid+" and crawl_request_id=  "+crawlid
                +"  format JSONEachRow");
       System.out.println("sql:"+buffer.toString());
                return buffer.toString();
    }

    private  static void outPutfileHeader(File fullFileName) {
        try {
            //File file = new File(fullFileName);
            if(fullFileName.exists()){
                fullFileName.delete();
            }
            StringBuilder sb = new StringBuilder();
            sb.append("Domain ID").append("\t")
                    .append("Crawl_ID").append("\t")
                    .append("crawlPagesCount").append("\t")
                    .append("Crawl start date").append("\t")
                    .append("min(request_time)").append("\t")
                    .append("max(request_time)").append("\t")
                    .append("Crawl Project id").append("\t")
                    .append("Crawl Project name").append("\t")
                    .append("Crawl_Type").append("\t")
                    .append("crawl_schedule").append("\t");

            System.out.println("header:"+sb.toString());
            List<String> list = new ArrayList<>();
            list.add(sb.toString());
            org.apache.commons.io.FileUtils.writeLines(fullFileName, "UTF-8", list, true);
        }catch (Exception ex){
            ex.printStackTrace();
        }
    }
    private static boolean ownCustomExport(String baseLocalStore,String fullFileName,String date) {
        try {
            if(crawlRequestLogDAO==null){
                crawlRequestLogDAO = SpringBeanFactory.getBean("crawlRequestLogDAO");
                //System.out.println("==========11111");
            }
            fullFileName = baseLocalStore+"/" +fullFileName;
            System.out.println("out put file:"+fullFileName);
            //outPutfileHeader(new File(fullFileName));
            Integer rowCount = 0;
            boolean isSucc = false;
            String[] err = new String[]{""};
                // System.out.println("###startexecSql>>>taskInstanceName:" + apiTaskInstanceEntity.getTaskInstanceName() + " key:" + cacheKey + " sql:" + result.get("sql"));
                long start = System.currentTimeMillis() / 1000;
                List<HashMap<String,Object>> list = crawlRequestLogDAO.getfinishedProjectList(date);
            List<String> listresult = new ArrayList<>();
            for (HashMap<String,Object> map:list) {
                List<String> listout =  httpExportClarityDBToFileV3(DBURL, USER, PSW, getQuery(map.get("crawl_id").toString(),map.get("own_domain_id").toString()) + "",fullFileName, true,1, true,err);
               if(listout!=null && listout.size()>0){
                   Map cntMap = makeCompititorv2(listout.get(0));
                   System.out.println(cntMap);
                   map.put("crawled_pages",cntMap.get("crawlPagesCount").toString());
                   map.put("crawl_start",cntMap.get("crawl_start").toString());
                   map.put("crawl_end",cntMap.get("crawl_end").toString());
                   listresult.add(JSONUtil.toJsonStr(map));
               }
            }
            isSucc=outPutfile(false,fullFileName,listresult);
            return  isSucc;
        } catch (Exception ex) {
            //System.out.println("*******Failed url is "+map.get("url")+"====");
            ex.printStackTrace();
            return  false;
        }
    }

    private static Map makeCompititorv2(String jsonstr){
        try{
            JSONObject json = JSONUtil.parseObj(jsonstr);
            HashMap map = new HashMap();
            map.put("crawlPagesCount",json.getByPath("crawlPagesCount"));
            map.put("crawl_start",json.getByPath("crawl_start"));
            map.put("crawl_end", json.getByPath("crawl_end"));
            return map;

        }catch (Exception ex){



            ex.printStackTrace();
            return null;
        }
    }

    public static final String SINGLE_QUOTE = "'";
    /**url: for example  http://*************:8123
     * isZip:true zipFileName =fullFileName+"zip" fullFileName will dele
     * */
    private  static boolean outPutfile(boolean iszip,String fullFileName,List<String> outputList) {
        try {
            File file = new File(fullFileName);
            int k = outputList.size();
            if (k < 100000) {
                org.apache.commons.io.FileUtils.writeLines(file, "UTF-8", outputList, true);
            }else{
                System.out.println("=====outputfile row count more than 10000 error!=====");
                return  false;
            }
            if (iszip) {
                String fullNameZip = String.join(".", fullFileName, "zip");
                List<File> srcFiles = new ArrayList<>();
                srcFiles.add(file);
                ZipFileUtils.zipFile(fullNameZip, srcFiles);
                file.delete();
                return true;
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            return false;
        }
        return true;
    }

    public static List<String> httpExportClarityDBToFileV3(String url, String user, String password, String sql, String fullFileName,boolean haveFileHeader,int urlsCount,
                                                      boolean isZip,String[]err) throws Exception {
        String emptyTempete = "{\"csvurl\":\"?\",\"compitors\":[]}";
        List<String> outputList = new ArrayList<String>();
        String url0 = url + "/?database=prod&enable_http_compression=1&user=" + user + "&password=" + password + "&query=" + sql;
        //System.out.println("=========URL0:" + url0);
        url=url + "/?database=prod&enable_http_compression=1&user=" +URLEncoder.encode(user,"UTF-8")  + "&password=" +URLEncoder.encode(password,"UTF-8")
                + "&query=" +URLEncoder.encode(sql,"UTF-8") ;
        //System.out.println("=========URL1:" + url);
        int retryCount = 0;
        while (true) {
            long start = System.currentTimeMillis() / 1000;
            Integer readTimeOut = (retryCount + 1) * READ_TIMEOUT;
            OkHttpClient.Builder builder = new OkHttpClient.Builder();
            OkHttpClient client = builder.readTimeout(readTimeOut, TimeUnit.SECONDS).build();
            MediaType mediaType = MediaType.parse("text/plain");
            RequestBody body = RequestBody.create(mediaType, "");
            Request request = new Request.Builder().url(url).method("POST", body)/*.addHeader("Accept-Encoding", "gzip")*/.build();

            try (Response response = client.newCall(request).execute()) {
                File file = new File(fullFileName);
                BufferedReader br = new BufferedReader(new InputStreamReader(response.body().byteStream()));
                String str = null;
                int k = 0;
                boolean append = false;
                outputList = new ArrayList<String>();
                while ((str = br.readLine()) != null) {
                    if(response.code()>=HTTP_EXPORT_RESPONSE_ERROR)
                    {
                        err[0]=err[0]+"\r\n"+str;
                        continue;
                    }
                    k++;
                    //String temp = makeCompititorv2(str,urlsCount);
                    //temp = StringEscapeUtils.unescapeJava(temp);
                    outputList.add(str);
                }
                if(response.code()>=HTTP_EXPORT_RESPONSE_ERROR)
                {
                    System.out.println("HTTP_EXPORT_RESPONSE_ERROR");
                    //return false;
                }

                return outputList;
            } catch (SocketTimeoutException ex) {
                ex.printStackTrace();
                retryCount++;

                Thread.sleep(10000);
            } catch (Exception e) {
                e.printStackTrace();

                return  outputList;
                //return false;
            }
        }
    }


    public static void main(String args[]) {
        try{
             Thread thread = new Thread() {
                public void run() {
                    try {
                        String strNum = getAlphaNumericString(10);
                        ownCustomExport( "/home/<USER>/task","finishedCrawlListExport_"+strNum+".txt","20230101");
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            };
            thread.start();
        }catch (Exception ex){
            ex.printStackTrace();
            System.out.println("err");
        }

    }

}

