package seoclarity.backend.asyncapi;

import cn.hutool.json.JSONUtil;
import org.apache.commons.lang.StringUtils;
import org.testcontainers.shaded.okhttp3.*;
import seoclarity.backend.dao.actonia.CrawlRequestLogDAO;
import seoclarity.backend.entity.actonia.ApiTaskInfoEntity;
import seoclarity.backend.entity.actonia.CrawlRequestLog;
import seoclarity.backend.utils.EmailSenderComponent;
import seoclarity.backend.utils.HttpRequestUtils;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.utils.ZipFileUtils;

import java.io.BufferedReader;
import java.io.File;
import java.io.InputStreamReader;
import java.net.SocketTimeoutException;
import java.net.URLEncoder;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

//mvn exec:java -Dexec.mainClass="seoclarity.backend.asyncapi.AsynTaskProcessor" -Dexec.args="102"
public class ExportCrawlProjectGetStatus {

    private static final String LOCAL_STORE_DIR = "/tmp/testMarven/";
    //***************************************************


    private static CrawlRequestLogDAO crawlRequestLogDAO;
    public ExportCrawlProjectGetStatus() {

    }
    private static String checkCrawlLogFinished(){
        try{
            File file = new File("/home/<USER>/outfiles/check_202400507.log");
            if(crawlRequestLogDAO==null){
                crawlRequestLogDAO = crawlRequestLogDAO = SpringBeanFactory.getBean("crawlRequestLogDAO");
            }
            List<Integer> list = Arrays.asList(
                    9976738,
                    9976737,
                    9976686,
                    9976637,
                    9976588,
                    9976579,
                    9976352,
                    9976351,
                    9976295,
                    9975932,
                    9975920,
                    9975814,
                    9975741,
                    9975724,
                    9975707,
                    9975295,
                    9975107,
                    9974976,
                    9974357,
                    9973948,
                    9973832,
                    9973192,
                    9973189,
                    9972977,
                    9972952,
                    9972875,
                    9972844,
                    9972821,
                    9972654,
                    9972375,
                    9971553,
                    9971392,
                    9971391,
                    9970995,
                    9970948,
                    9970852,
                    9970190,
                    9969930,
                    9969786,
                    9969510,
                    9969433,
                    9969340,
                    9969183,
                    9968996,
                    9968991,
                    9968986,
                    9968761,
                    9968392,
                    9968362,
                    9968361,
                    9965868,
                    9965554,
                    9965117,
                    9964644,
                    9964643,
                    9963655,
                    9963479,
                    9962710,
                    9962709,
                    9962380,
                    9962261,
                    9961926,
                    9961812,
                    9958248,
                    9956557,
                    9950365,
                    9950364,
                    9945900,
                    9945234,
                    9944345,
                    9943270,
                    9933494);
            for (Integer logid: list) {
                CrawlRequestLog log = crawlRequestLogDAO.getById(logid);
                String isCluster = "clusterCrawl=";
                String requestId ="crawl_request_log_id_i=";
                if(log.getCrawlSpeed()>8){
                    isCluster+= "true";
                }else{
                    isCluster+= "false";
                }
                requestId += log.getId();
                String requestUrl = "http://169.60.132.27:8182/seoClarity/siteClarity/getCrawlerStats?"+requestId+"&" + isCluster+"&accessToken=c09yxv13-opr3-d745-9734-8pu48420nj67";
                System.out.println("=====requestUrl:"+requestUrl);
                String result = "";
                int tryInt = 0;
                while(tryInt <5) {
                    try {
                        result = HttpRequestUtils.simpleGet(requestUrl, null, 100000, null);
                        cn.hutool.json.JSONObject obj = JSONUtil.parseObj(result);
                        if(StringUtils.equals(obj.get("completed").toString(),"true")) {
                            String temp = "=====crawlId:" + log.getId().toString()+",domainid:"+  log.getOwnDomainId() ;
                            List<String> listLine = new ArrayList<>();
                            listLine.add(temp);
                            org.apache.commons.io.FileUtils.writeLines(file, "UTF-8", listLine, true);
                        }
                        break;
                    } catch (Exception ex) {
                        Thread.sleep(2000);
                        System.out.println("=====try again " + log.getId());
                        tryInt++;
                    }
                }
                System.out.println("=====result:"+result);
            }
        }catch (Exception ex){
            ex.printStackTrace();
            return "" ;
        }
        return "";
    }
    public static void main(String args[]) {
        try{
            checkCrawlLogFinished();
        }catch (Exception ex){
            ex.printStackTrace();
            System.out.println("err");
        }


    }







}

