package seoclarity.backend.entity;// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: google_ved.proto

public final class GoogleVed {
  private GoogleVed() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface VedOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Ved)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * i
     * </pre>
     *
     * <code>optional uint64 hveid = 1;</code>
     */
    boolean hasHveid();
    /**
     * <pre>
     * i
     * </pre>
     *
     * <code>optional uint64 hveid = 1;</code>
     */
    long getHveid();

    /**
     * <pre>
     * t
     * </pre>
     *
     * <code>optional uint64 type = 2;</code>
     */
    boolean hasType();
    /**
     * <pre>
     * t
     * </pre>
     *
     * <code>optional uint64 type = 2;</code>
     */
    long getType();

    /**
     * <pre>
     * (zero based)
     * </pre>
     *
     * <code>optional uint64 sub_link_position = 5;</code>
     */
    boolean hasSubLinkPosition();
    /**
     * <pre>
     * (zero based)
     * </pre>
     *
     * <code>optional uint64 sub_link_position = 5;</code>
     */
    long getSubLinkPosition();

    /**
     * <pre>
     * r (zero based)
     * </pre>
     *
     * <code>optional uint64 result_position = 6;</code>
     */
    boolean hasResultPosition();
    /**
     * <pre>
     * r (zero based)
     * </pre>
     *
     * <code>optional uint64 result_position = 6;</code>
     */
    long getResultPosition();

    /**
     * <pre>
     * s (zero based)
     * </pre>
     *
     * <code>optional uint64 start_page = 7;</code>
     */
    boolean hasStartPage();
    /**
     * <pre>
     * s (zero based)
     * </pre>
     *
     * <code>optional uint64 start_page = 7;</code>
     */
    long getStartPage();
  }
  /**
   * <pre>
   *enum Type
   *{
   *WEB   = 22;
   *IMAGE = 429;
   *VIDEO = 311;
   *AD    = 1617; // an ad blended into the search results
   *};
   * </pre>
   *
   * Protobuf type {@code Ved}
   */
  public  static final class Ved extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:Ved)
      VedOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Ved.newBuilder() to construct.
    private Ved(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Ved() {
    }

    @Override
    @SuppressWarnings({"unused"})
    protected Object newInstance(
        UnusedPrivateParameter unused) {
      return new Ved();
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Ved(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              hveid_ = input.readUInt64();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              type_ = input.readUInt64();
              break;
            }
            case 40: {
              bitField0_ |= 0x00000004;
              subLinkPosition_ = input.readUInt64();
              break;
            }
            case 48: {
              bitField0_ |= 0x00000008;
              resultPosition_ = input.readUInt64();
              break;
            }
            case 56: {
              bitField0_ |= 0x00000010;
              startPage_ = input.readUInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return GoogleVed.internal_static_Ved_descriptor;
    }

    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return GoogleVed.internal_static_Ved_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              Ved.class, Builder.class);
    }

    private int bitField0_;
    public static final int HVEID_FIELD_NUMBER = 1;
    private long hveid_;
    /**
     * <pre>
     * i
     * </pre>
     *
     * <code>optional uint64 hveid = 1;</code>
     */
    public boolean hasHveid() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * i
     * </pre>
     *
     * <code>optional uint64 hveid = 1;</code>
     */
    public long getHveid() {
      return hveid_;
    }

    public static final int TYPE_FIELD_NUMBER = 2;
    private long type_;
    /**
     * <pre>
     * t
     * </pre>
     *
     * <code>optional uint64 type = 2;</code>
     */
    public boolean hasType() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * t
     * </pre>
     *
     * <code>optional uint64 type = 2;</code>
     */
    public long getType() {
      return type_;
    }

    public static final int SUB_LINK_POSITION_FIELD_NUMBER = 5;
    private long subLinkPosition_;
    /**
     * <pre>
     * (zero based)
     * </pre>
     *
     * <code>optional uint64 sub_link_position = 5;</code>
     */
    public boolean hasSubLinkPosition() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * (zero based)
     * </pre>
     *
     * <code>optional uint64 sub_link_position = 5;</code>
     */
    public long getSubLinkPosition() {
      return subLinkPosition_;
    }

    public static final int RESULT_POSITION_FIELD_NUMBER = 6;
    private long resultPosition_;
    /**
     * <pre>
     * r (zero based)
     * </pre>
     *
     * <code>optional uint64 result_position = 6;</code>
     */
    public boolean hasResultPosition() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * r (zero based)
     * </pre>
     *
     * <code>optional uint64 result_position = 6;</code>
     */
    public long getResultPosition() {
      return resultPosition_;
    }

    public static final int START_PAGE_FIELD_NUMBER = 7;
    private long startPage_;
    /**
     * <pre>
     * s (zero based)
     * </pre>
     *
     * <code>optional uint64 start_page = 7;</code>
     */
    public boolean hasStartPage() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <pre>
     * s (zero based)
     * </pre>
     *
     * <code>optional uint64 start_page = 7;</code>
     */
    public long getStartPage() {
      return startPage_;
    }

    private byte memoizedIsInitialized = -1;
    @Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeUInt64(1, hveid_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeUInt64(2, type_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeUInt64(5, subLinkPosition_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeUInt64(6, resultPosition_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeUInt64(7, startPage_);
      }
      unknownFields.writeTo(output);
    }

    @Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(1, hveid_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(2, type_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(5, subLinkPosition_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(6, resultPosition_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(7, startPage_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof Ved)) {
        return super.equals(obj);
      }
      Ved other = (Ved) obj;

      if (hasHveid() != other.hasHveid()) return false;
      if (hasHveid()) {
        if (getHveid()
            != other.getHveid()) return false;
      }
      if (hasType() != other.hasType()) return false;
      if (hasType()) {
        if (getType()
            != other.getType()) return false;
      }
      if (hasSubLinkPosition() != other.hasSubLinkPosition()) return false;
      if (hasSubLinkPosition()) {
        if (getSubLinkPosition()
            != other.getSubLinkPosition()) return false;
      }
      if (hasResultPosition() != other.hasResultPosition()) return false;
      if (hasResultPosition()) {
        if (getResultPosition()
            != other.getResultPosition()) return false;
      }
      if (hasStartPage() != other.hasStartPage()) return false;
      if (hasStartPage()) {
        if (getStartPage()
            != other.getStartPage()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasHveid()) {
        hash = (37 * hash) + HVEID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getHveid());
      }
      if (hasType()) {
        hash = (37 * hash) + TYPE_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getType());
      }
      if (hasSubLinkPosition()) {
        hash = (37 * hash) + SUB_LINK_POSITION_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getSubLinkPosition());
      }
      if (hasResultPosition()) {
        hash = (37 * hash) + RESULT_POSITION_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getResultPosition());
      }
      if (hasStartPage()) {
        hash = (37 * hash) + START_PAGE_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getStartPage());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static Ved parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static Ved parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static Ved parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static Ved parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static Ved parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static Ved parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static Ved parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static Ved parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static Ved parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static Ved parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static Ved parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static Ved parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(Ved prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     *enum Type
     *{
     *WEB   = 22;
     *IMAGE = 429;
     *VIDEO = 311;
     *AD    = 1617; // an ad blended into the search results
     *};
     * </pre>
     *
     * Protobuf type {@code Ved}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Ved)
        VedOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return GoogleVed.internal_static_Ved_descriptor;
      }

      @Override
      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return GoogleVed.internal_static_Ved_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                Ved.class, Builder.class);
      }

      // Construct using GoogleVed.Ved.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @Override
      public Builder clear() {
        super.clear();
        hveid_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        type_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        subLinkPosition_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000004);
        resultPosition_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000008);
        startPage_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000010);
        return this;
      }

      @Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return GoogleVed.internal_static_Ved_descriptor;
      }

      @Override
      public Ved getDefaultInstanceForType() {
        return Ved.getDefaultInstance();
      }

      @Override
      public Ved build() {
        Ved result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @Override
      public Ved buildPartial() {
        Ved result = new Ved(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.hveid_ = hveid_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.type_ = type_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.subLinkPosition_ = subLinkPosition_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.resultPosition_ = resultPosition_;
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.startPage_ = startPage_;
          to_bitField0_ |= 0x00000010;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @Override
      public Builder clone() {
        return super.clone();
      }
      @Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return super.setField(field, value);
      }
      @Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return super.addRepeatedField(field, value);
      }
      @Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof Ved) {
          return mergeFrom((Ved)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(Ved other) {
        if (other == Ved.getDefaultInstance()) return this;
        if (other.hasHveid()) {
          setHveid(other.getHveid());
        }
        if (other.hasType()) {
          setType(other.getType());
        }
        if (other.hasSubLinkPosition()) {
          setSubLinkPosition(other.getSubLinkPosition());
        }
        if (other.hasResultPosition()) {
          setResultPosition(other.getResultPosition());
        }
        if (other.hasStartPage()) {
          setStartPage(other.getStartPage());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @Override
      public final boolean isInitialized() {
        return true;
      }

      @Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        Ved parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (Ved) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long hveid_ ;
      /**
       * <pre>
       * i
       * </pre>
       *
       * <code>optional uint64 hveid = 1;</code>
       */
      public boolean hasHveid() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * i
       * </pre>
       *
       * <code>optional uint64 hveid = 1;</code>
       */
      public long getHveid() {
        return hveid_;
      }
      /**
       * <pre>
       * i
       * </pre>
       *
       * <code>optional uint64 hveid = 1;</code>
       */
      public Builder setHveid(long value) {
        bitField0_ |= 0x00000001;
        hveid_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * i
       * </pre>
       *
       * <code>optional uint64 hveid = 1;</code>
       */
      public Builder clearHveid() {
        bitField0_ = (bitField0_ & ~0x00000001);
        hveid_ = 0L;
        onChanged();
        return this;
      }

      private long type_ ;
      /**
       * <pre>
       * t
       * </pre>
       *
       * <code>optional uint64 type = 2;</code>
       */
      public boolean hasType() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * t
       * </pre>
       *
       * <code>optional uint64 type = 2;</code>
       */
      public long getType() {
        return type_;
      }
      /**
       * <pre>
       * t
       * </pre>
       *
       * <code>optional uint64 type = 2;</code>
       */
      public Builder setType(long value) {
        bitField0_ |= 0x00000002;
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * t
       * </pre>
       *
       * <code>optional uint64 type = 2;</code>
       */
      public Builder clearType() {
        bitField0_ = (bitField0_ & ~0x00000002);
        type_ = 0L;
        onChanged();
        return this;
      }

      private long subLinkPosition_ ;
      /**
       * <pre>
       * (zero based)
       * </pre>
       *
       * <code>optional uint64 sub_link_position = 5;</code>
       */
      public boolean hasSubLinkPosition() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * (zero based)
       * </pre>
       *
       * <code>optional uint64 sub_link_position = 5;</code>
       */
      public long getSubLinkPosition() {
        return subLinkPosition_;
      }
      /**
       * <pre>
       * (zero based)
       * </pre>
       *
       * <code>optional uint64 sub_link_position = 5;</code>
       */
      public Builder setSubLinkPosition(long value) {
        bitField0_ |= 0x00000004;
        subLinkPosition_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * (zero based)
       * </pre>
       *
       * <code>optional uint64 sub_link_position = 5;</code>
       */
      public Builder clearSubLinkPosition() {
        bitField0_ = (bitField0_ & ~0x00000004);
        subLinkPosition_ = 0L;
        onChanged();
        return this;
      }

      private long resultPosition_ ;
      /**
       * <pre>
       * r (zero based)
       * </pre>
       *
       * <code>optional uint64 result_position = 6;</code>
       */
      public boolean hasResultPosition() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * r (zero based)
       * </pre>
       *
       * <code>optional uint64 result_position = 6;</code>
       */
      public long getResultPosition() {
        return resultPosition_;
      }
      /**
       * <pre>
       * r (zero based)
       * </pre>
       *
       * <code>optional uint64 result_position = 6;</code>
       */
      public Builder setResultPosition(long value) {
        bitField0_ |= 0x00000008;
        resultPosition_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * r (zero based)
       * </pre>
       *
       * <code>optional uint64 result_position = 6;</code>
       */
      public Builder clearResultPosition() {
        bitField0_ = (bitField0_ & ~0x00000008);
        resultPosition_ = 0L;
        onChanged();
        return this;
      }

      private long startPage_ ;
      /**
       * <pre>
       * s (zero based)
       * </pre>
       *
       * <code>optional uint64 start_page = 7;</code>
       */
      public boolean hasStartPage() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <pre>
       * s (zero based)
       * </pre>
       *
       * <code>optional uint64 start_page = 7;</code>
       */
      public long getStartPage() {
        return startPage_;
      }
      /**
       * <pre>
       * s (zero based)
       * </pre>
       *
       * <code>optional uint64 start_page = 7;</code>
       */
      public Builder setStartPage(long value) {
        bitField0_ |= 0x00000010;
        startPage_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * s (zero based)
       * </pre>
       *
       * <code>optional uint64 start_page = 7;</code>
       */
      public Builder clearStartPage() {
        bitField0_ = (bitField0_ & ~0x00000010);
        startPage_ = 0L;
        onChanged();
        return this;
      }
      @Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:Ved)
    }

    // @@protoc_insertion_point(class_scope:Ved)
    private static final Ved DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new Ved();
    }

    public static Ved getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @Deprecated public static final com.google.protobuf.Parser<Ved>
        PARSER = new com.google.protobuf.AbstractParser<Ved>() {
      @Override
      public Ved parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Ved(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Ved> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<Ved> getParserForType() {
      return PARSER;
    }

    @Override
    public Ved getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Ved_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_Ved_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    String[] descriptorData = {
      "\n\020google_ved.proto\"j\n\003Ved\022\r\n\005hveid\030\001 \001(\004" +
      "\022\014\n\004type\030\002 \001(\004\022\031\n\021sub_link_position\030\005 \001(" +
      "\004\022\027\n\017result_position\030\006 \001(\004\022\022\n\nstart_page" +
      "\030\007 \001(\004"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_Ved_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_Ved_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_Ved_descriptor,
        new String[] { "Hveid", "Type", "SubLinkPosition", "ResultPosition", "StartPage", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
