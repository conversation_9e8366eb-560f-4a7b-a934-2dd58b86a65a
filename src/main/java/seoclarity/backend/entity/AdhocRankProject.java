package seoclarity.backend.entity;

import java.sql.Timestamp;
import java.util.Date;

/**
 * hourly_adhoc_rank_project
 *
 * <AUTHOR>
 * @version 1.0.0 2020-08-31
 */
public class AdhocRankProject implements java.io.Serializable {
    /** 版本号 */
    private static final long serialVersionUID = 2725653932564721809L;

    public static final int STATUS_CREATEING_PROJECT = 1;
    public static final int STATUS_COMPLETE = 2;

    /* This code was generated by TableGo tools, mark 1 begin. */

    /** db46 auto_adhoc_rank_project.id */
    private Integer id;

    /** 0: disabled, 1: enabled */
    private Boolean enabled;

    /** ownDomainId */
    private Integer ownDomainId;

    /** projectName */
    private String projectName;

    /** yyyyMMdd rankDate */
    private Integer createDate;

    /** createUserId */
    private Integer createUserId;

    /** engine_country_language_mapping.countryQueryName */
    private String country;

    /** engine_country_language_mapping.engineQueryName */
    private String engine;

    /** engine_country_language_mapping.languageQueryName */
    private String language;

    /** searchEngineId */
    private Integer searchEngineId;

    /** languageId */
    private Integer languageId;

    /** resultNum */
    private Boolean resultNum;

    /** city id for geo rank, 0: non-city rank */
    private Integer cityId;

    /** 1: desktop, 2: mobile, 3: tablet */
    private Boolean device;

    /** criteriaId */
    private Integer criteriaId;

    /** query name */
    private String cityName;

    /** lat */
    private String lat;

    /** lng */
    private String lng;

    private Integer fromHour;

    private Integer toHour;

    /** canonicalName */
    private String canonicalName;

    /** 1: creating project 2: complete(ready to rank) */
    private Boolean status;

    /** createdAt */
    private Timestamp createdAt;

    /** startDate */
    private Date startDate;

    /** endDate */
    private Date endDate;


    /* This code was generated by TableGo tools, mark 1 end. */

    /* This code was generated by TableGo tools, mark 2 begin. */

    /**
     *  auto_adhoc_rank_project.id
     *
     * @return db46 auto_adhoc_rank_project
     */
    public Integer getId() {
        return this.id;
    }

    /**
     *  auto_adhoc_rank_project.id
     *
     * @param id
     *          db46 auto_adhoc_rank_project
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     *  disabled, 1: enabled
     *
     * @return 0: disabled
     */
    public Boolean getEnabled() {
        return this.enabled;
    }

    /**
     * 0: disabled, 1: enabled
     *
     * @param enabled
     *          0: disabled
     */
    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    /**
     * ownDomainId
     *
     * @return ownDomainId
     */
    public Integer getOwnDomainId() {
        return this.ownDomainId;
    }

    /**
     * ownDomainId
     *
     * @param ownDomainId
     */
    public void setOwnDomainId(Integer ownDomainId) {
        this.ownDomainId = ownDomainId;
    }

    /**
     * projectName
     *
     * @return projectName
     */
    public String getProjectName() {
        return this.projectName;
    }

    /**
     * projectName
     *
     * @param projectName
     */
    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    /**
     * yyyyMMdd rankDate
     *
     * @return yyyyMMdd rankDate
     */
    public Integer getCreateDate() {
        return this.createDate;
    }

    /**
     * yyyyMMdd rankDate
     *
     * @param createDate
     *          yyyyMMdd rankDate
     */
    public void setCreateDate(Integer createDate) {
        this.createDate = createDate;
    }

    /**
     * createUserId
     *
     * @return createUserId
     */
    public Integer getCreateUserId() {
        return this.createUserId;
    }

    /**
     * createUserId
     *
     * @param createUserId
     */
    public void setCreateUserId(Integer createUserId) {
        this.createUserId = createUserId;
    }

    /**
     * engine_country_language_mapping.countryQueryName
     *
     * @return engine_country_language_mapping
     */
    public String getCountry() {
        return this.country;
    }

    /**
     * engine_country_language_mapping.countryQueryName
     *
     * @param country
     *          engine_country_language_mapping
     */
    public void setCountry(String country) {
        this.country = country;
    }

    /**
     * engine_country_language_mapping.engineQueryName
     *
     * @return engine_country_language_mapping
     */
    public String getEngine() {
        return this.engine;
    }

    /**
     * engine_country_language_mapping.engineQueryName
     *
     * @param engine
     *          engine_country_language_mapping
     */
    public void setEngine(String engine) {
        this.engine = engine;
    }

    /**
     * engine_country_language_mapping.languageQueryName
     *
     * @return engine_country_language_mapping
     */
    public String getLanguage() {
        return this.language;
    }

    /**
     * engine_country_language_mapping.languageQueryName
     *
     * @param language
     *          engine_country_language_mapping
     */
    public void setLanguage(String language) {
        this.language = language;
    }

    /**
     * searchEngineId
     *
     * @return searchEngineId
     */
    public Integer getSearchEngineId() {
        return this.searchEngineId;
    }

    /**
     * searchEngineId
     *
     * @param searchEngineId
     */
    public void setSearchEngineId(Integer searchEngineId) {
        this.searchEngineId = searchEngineId;
    }

    /**
     * languageId
     *
     * @return languageId
     */
    public Integer getLanguageId() {
        return this.languageId;
    }

    /**
     * languageId
     *
     * @param languageId
     */
    public void setLanguageId(Integer languageId) {
        this.languageId = languageId;
    }

    /**
     * resultNum
     *
     * @return resultNum
     */
    public Boolean getResultNum() {
        return this.resultNum;
    }

    /**
     * resultNum
     *
     * @param resultNum
     */
    public void setResultNum(Boolean resultNum) {
        this.resultNum = resultNum;
    }

    /**
     * city id for geo rank, 0: non-city rank
     *
     * @return city id for geo rank
     */
    public Integer getCityId() {
        return this.cityId;
    }

    /**
     * city id for geo rank, 0: non-city rank
     *
     * @param cityId
     *          city id for geo rank
     */
    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    /**
     * 1: desktop, 2: mobile, 3: tablet
     *
     * @return 1: desktop
     */
    public Boolean getDevice() {
        return this.device;
    }

    /**
     * 1: desktop, 2: mobile, 3: tablet
     *
     * @param device
     *          1: desktop
     */
    public void setDevice(Boolean device) {
        this.device = device;
    }

    /**
     * criteriaId
     *
     * @return criteriaId
     */
    public Integer getCriteriaId() {
        return this.criteriaId;
    }

    /**
     * criteriaId
     *
     * @param criteriaId
     */
    public void setCriteriaId(Integer criteriaId) {
        this.criteriaId = criteriaId;
    }

    /**
     * query name
     *
     * @return query name
     */
    public String getCityName() {
        return this.cityName;
    }

    /**
     * query name
     *
     * @param cityName
     *          query name
     */
    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    /**
     * lat
     *
     * @return lat
     */
    public String getLat() {
        return this.lat;
    }

    /**
     * lat
     *
     * @param lat
     */
    public void setLat(String lat) {
        this.lat = lat;
    }

    /**
     * lng
     *
     * @return lng
     */
    public String getLng() {
        return this.lng;
    }

    /**
     * lng
     *
     * @param lng
     */
    public void setLng(String lng) {
        this.lng = lng;
    }

    /**
     * canonicalName
     *
     * @return canonicalName
     */
    public String getCanonicalName() {
        return this.canonicalName;
    }

    /**
     * canonicalName
     *
     * @param canonicalName
     */
    public void setCanonicalName(String canonicalName) {
        this.canonicalName = canonicalName;
    }

    /**
     * 1: creating project 2: complete(ready to rank)
     *
     * @return 1: creating project 2: complete(ready to rank)
     */
    public Boolean getStatus() {
        return this.status;
    }

    /**
     * 1: creating project 2: complete(ready to rank)
     *
     * @param status
     *          1: creating project 2: complete(ready to rank)
     */
    public void setStatus(Boolean status) {
        this.status = status;
    }

    /**
     * 0 - 23
     *
     * @return 0 - 23
     */
    public Integer getFromHour() {
        return this.fromHour;
    }

    /**
     * 0 - 23
     *
     * @param fromHour
     *          0 - 23
     */
    public void setFromHour(Integer fromHour) {
        this.fromHour = fromHour;
    }

    /**
     * 0 - 23
     *
     * @return 0 - 23
     */
    public Integer getToHour() {
        return this.toHour;
    }

    /**
     * 0 - 23
     *
     * @param toHour
     *          0 - 23
     */
    public void setToHour(Integer toHour) {
        this.toHour = toHour;
    }

    /**
     * createdAt
     *
     * @return createdAt
     */
    public Timestamp getCreatedAt() {
        return this.createdAt;
    }

    /**
     * createdAt
     *
     * @param createdAt
     */
    public void setCreatedAt(Timestamp createdAt) {
        this.createdAt = createdAt;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    /* This code was generated by TableGo tools, mark 2 end. */

}