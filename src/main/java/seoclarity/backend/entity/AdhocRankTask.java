package seoclarity.backend.entity;

import java.sql.Timestamp;

/**
 * hourly_adhoc_rank_task
 *
 * <AUTHOR>
 * @version 1.0.0 2020-08-31
 */
public class AdhocRankTask implements java.io.Serializable {
    /** 版本号 */
    private static final long serialVersionUID = -5004825835750661031L;

    public static final int EXTRACT_RANK_STSTUS_NOT_STARTED = 0;
    public static final int EXTRACT_RANK_STSTUS_PROCESSING = 1;
    public static final int EXTRACT_RANK_STSTUS_COMPLETED_WITHOUT_ERROR = 2;
    public static final int EXTRACT_RANK_STSTUS_ERROR = 3;

    /* This code was generated by TableGo tools, mark 1 begin. */

    /** id */
    private Integer id;

    /** hourly_adhoc_rank_project.id */
    private Integer projectId;

    /** yyyyMMdd */
    private Integer startDay;

    /** Hour: 0 - 24 */
    private Integer startHour;

    /** ec2 region code */
    private String ec2region;

    /** ec2 Spot Id */
    private String ec2id;

    /** 0: not started, 1: started 2: completed */
    private Integer status;

    /** startedec2instances */
    private Integer startedec2instances;

    /** startedec2times */
    private Boolean startedec2times;

    /** createdAt */
    private Timestamp createdAt;

    private String ec2SpotId;

    private Integer extractStatus;

    /* This code was generated by TableGo tools, mark 1 end. */

    /* This code was generated by TableGo tools, mark 2 begin. */

    /**
     * id
     *
     * @return id
     */
    public Integer getId() {
        return this.id;
    }

    /**
     * id
     *
     * @param id
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * hourly_adhoc_rank_project.id
     *
     * @return hourly_adhoc_rank_project
     */
    public Integer getProjectId() {
        return this.projectId;
    }

    /**
     * hourly_adhoc_rank_project.id
     *
     * @param projectId
     *          hourly_adhoc_rank_project
     */
    public void setProjectId(Integer projectId) {
        this.projectId = projectId;
    }

    /**
     * yyyyMMdd
     *
     * @return yyyyMMdd
     */
    public Integer getStartDay() {
        return this.startDay;
    }

    /**
     * yyyyMMdd
     *
     * @param startDay
     *          yyyyMMdd
     */
    public void setStartDay(Integer startDay) {
        this.startDay = startDay;
    }

    /**
     * Hour: 0 - 24
     *
     * @return Hour
     */
    public Integer getStartHour() {
        return this.startHour;
    }

    /**
     * Hour: 0 - 24
     *
     * @param startHour
     *          Hour
     */
    public void setStartHour(Integer startHour) {
        this.startHour = startHour;
    }

    /**
     * ec2 region code
     *
     * @return ec2 region code
     */
    public String getEc2region() {
        return this.ec2region;
    }

    /**
     * ec2 region code
     *
     * @param ec2region
     *          ec2 region code
     */
    public void setEc2region(String ec2region) {
        this.ec2region = ec2region;
    }

    /**
     * ec2 Spot Id
     *
     * @return ec2 Spot Id
     */
    public String getEc2id() {
        return this.ec2id;
    }

    /**
     * ec2 Spot Id
     *
     * @param ec2id
     *          ec2 Spot Id
     */
    public void setEc2id(String ec2id) {
        this.ec2id = ec2id;
    }

    /**
     * 0: not started, 1: started 2: completed
     *
     * @return 0: not started
     */
    public Integer getStatus() {
        return this.status;
    }

    /**
     * 0: not started, 1: started 2: completed
     *
     * @param status
     *          0: not started
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * startedec2instances
     *
     * @return startedec2instances
     */
    public Integer getStartedec2instances() {
        return this.startedec2instances;
    }

    /**
     * startedec2instances
     *
     * @param startedec2instances
     */
    public void setStartedec2instances(Integer startedec2instances) {
        this.startedec2instances = startedec2instances;
    }

    /**
     * startedec2times
     *
     * @return startedec2times
     */
    public Boolean getStartedec2times() {
        return this.startedec2times;
    }

    /**
     * startedec2times
     *
     * @param startedec2times
     */
    public void setStartedec2times(Boolean startedec2times) {
        this.startedec2times = startedec2times;
    }

    /**
     * createdAt
     *
     * @return createdAt
     */
    public Timestamp getCreatedAt() {
        return this.createdAt;
    }

    /**
     * createdAt
     *
     * @param createdAt
     */
    public void setCreatedAt(Timestamp createdAt) {
        this.createdAt = createdAt;
    }

    public String getEc2SpotId() {
        return ec2SpotId;
    }

    public void setEc2SpotId(String ec2SpotId) {
        this.ec2SpotId = ec2SpotId;
    }

    /* This code was generated by TableGo tools, mark 2 end. */

    public Integer getExtractStatus() {
        return extractStatus;
    }

    public void setExtractStatus(Integer extractStatus) {
        this.extractStatus = extractStatus;
    }
}