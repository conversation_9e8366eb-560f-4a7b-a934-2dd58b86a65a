/**
 *
 */
package seoclarity.backend.entity.rankcheck;

import java.util.List;

import seoclarity.backend.utils.RankCheckUtils;

import com.amazonaws.services.sqs.model.Message;

/**
 * com.actonia.subserver.entity.SeoClarityKeywordEntity.java
 * <p/>
 * for seoclarity new rank check database
 * 
 * <AUTHOR>
 * @version $Revision: 90199 $ $Author: xuc<PERSON>jie@SHINETECHCHINA $
 */
public class SeoClarityKeywordEntity {
	public static final int QUERY_STATE_SUCCESS = 101;

	public static final int QUERY_STATE_LESS_RESULT = 200;

	public static final int QUERY_STATE_DID_YOU_MEAN = 250;

	public static final int QUERY_STATE_GOOGLE_BLOCK = 400;

	public static final int QUERY_STATE_PARSER_ERROR = 500;

	public static final int QUERY_STATE_LESS_RESULT_ZERO = 205;

	public static final int SEARCH_ENGINE_GOOGLE = 1;

	public static final int SEARCH_ENGINE_GOOGLE_AU = 2;

	public static final int SEARCH_ENGINE_GOOGLE_CA = 3;

	public static final int SEARCH_ENGINE_GOOGLE_FR = 4;

	public static final int SEARCH_ENGINE_GOOGLE_UK = 5;

	public static final int SEARCH_ENGINE_GOOGLE_UK_ALL = 6;

	public static final int SEARCH_ENGINE_GOOGLE_FR_ALL = 7;

	public static final int SEARCH_ENGINE_BAIDU_CN = 150;

	public static final int SEARCH_ENGINE_HAOSOU_CN = 151;

	// https://www.wrike.com/open.htm?id=183810131
	public static final int SEARCH_ENGINE_BAIDU_CN_MOBILE = 155;

	public static final int SEARCH_ENGINE_GOOGLE_IT = 8;

	public static final int SEARCH_ENGINE_GOOGLE_DK = 9;

	public static final int SEARCH_ENGINE_GOOGLE_FI = 10;

	public static final int SEARCH_ENGINE_GOOGLE_COM_MX = 11;

	public static final int SEARCH_ENGINE_GOOGLE_NO = 12;

	public static final int SEARCH_ENGINE_GOOGLE_SE = 13;

	public static final int SEARCH_ENGINE_GOOGLE_DE = 14;

	public static final int SEARCH_ENGINE_GOOGLE_COM_BR = 15;

	public static final int SEARCH_ENGINE_GOOGLE_ES = 16;

	public static final int SEARCH_ENGINE_GOOGLE_NL = 17;

	public static final int SEARCH_ENGINE_GOOGLE_JP = 18;

	public static final int SEARCH_ENGINE_GOOGLE_PT = 19;

	public static final int SEARCH_ENGINE_GOOGLE_IE = 20;
	public static final int SEARCH_ENGINE_GOOGLE_BE = 21;
	public static final int SEARCH_ENGINE_GOOGLE_CH = 22;
	public static final int SEARCH_ENGINE_GOOGLE_CO_KR = 23;
	public static final int SEARCH_ENGINE_GOOGLE_CO_IN = 24;

	public static final int SEARCH_ENGINE_GOOGLE_COM_AR = 25;
	public static final int SEARCH_ENGINE_GOOGLE_CL = 26;
	public static final int SEARCH_ENGINE_GOOGLE_COM_CO = 27;
	public static final int SEARCH_ENGINE_GOOGLE_COM_PR = 28;
	public static final int SEARCH_ENGINE_GOOGLE_AT = 29;
	public static final int SEARCH_ENGINE_GOOGLE_COM_HK = 30;

	public static final int SEARCH_ENGINE_GOOGLE_CO_ID = 31;
	public static final int SEARCH_ENGINE_GOOGLE_COM_MY = 32;
	public static final int SEARCH_ENGINE_GOOGLE_COM_PH = 33;
	public static final int SEARCH_ENGINE_GOOGLE_COM_TW = 34;
	public static final int SEARCH_ENGINE_GOOGLE_CO_TH = 35;
	public static final int SEARCH_ENGINE_GOOGLE_COM_VN = 36;
	public static final int SEARCH_ENGINE_GOOGLE_COM_SG = 37;
	public static final int SEARCH_ENGINE_GOOGLE_CO_NZ = 38;
	public static final int SEARCH_ENGINE_GOOGLE_RU = 39;

	//https://www.wrike.com/open.htm?id=9415299
	public static final int SEARCH_ENGINE_GOOGLE_CZ = 40;
	public static final int SEARCH_ENGINE_GOOGLE_HU = 41;
	public static final int SEARCH_ENGINE_GOOGLE_PL = 42;

	public static final int SEARCH_ENGINE_GOOGLE_COM_SA = 43;
	public static final int SEARCH_ENGINE_GOOGLE_COM_PE = 44;
	public static final int SEARCH_ENGINE_GOOGLE_AE = 45;
	public static final int SEARCH_ENGINE_GOOGLE_COM_TR = 46;

	public static final int SEARCH_ENGINE_GOOGLE_CO_ZA = 47;

	public static final int SEARCH_ENGINE_GOOGLE_CO_KE = 48;

	// https://www.wrike.com/open.htm?id=36219549
	public static final int SEARCH_ENGINE_GOOGLE_SK = 49;
	public static final int SEARCH_ENGINE_GOOGLE_CO_IL = 50;
	public static final int SEARCH_ENGINE_GOOGLE_COM_EC = 51;
	public static final int SEARCH_ENGINE_GOOGLE_CO_VE = 52;
	public static final int SEARCH_ENGINE_GOOGLE_CO_CR = 53;

	// https://www.wrike.com/open.htm?id=46752792
	public static final int SEARCH_ENGINE_GOOGLE_COM_UA = 54;
	public static final int SEARCH_ENGINE_GOOGLE_COM_UY = 55;
	// https://www.wrike.com/open.htm?id=53458412
	public static final int SEARCH_ENGINE_GOOGLE_MV = 56;


	// https://www.wrike.com/open.htm?id=58651708
	// https://www.wrike.com/open.htm?id=58651849
	// https://www.wrike.com/open.htm?id=58778920
	// https://www.wrike.com/open.htm?id=58779062
	public static final int SEARCH_ENGINE_GOOGLE_COM_BD = 57;
	public static final int SEARCH_ENGINE_GOOGLE_COM_KH = 58;
	public static final int SEARCH_ENGINE_GOOGLE_COM_FJ = 59;
	public static final int SEARCH_ENGINE_GOOGLE_WS = 60;

	// https://www.wrike.com/open.htm?id=60576663
	public static final int SEARCH_ENGINE_GOOGLE_GR = 61;

	// https://www.wrike.com/open.htm?id=63075948
	public static final int SEARCH_ENGINE_GOOGLE_COM_NG = 62;

	// https://www.wrike.com/open.htm?id=66773684
	public static final int SEARCH_ENGINE_GOOGLE_COM_DO = 63;

	// https://www.wrike.com/open.htm?id=70234779
	public static final int SEARCH_ENGINE_GOOGLE_COM_PK = 64;
	public static final int SEARCH_ENGINE_GOOGLE_COM_LB = 65;

	// https://www.wrike.com/open.htm?id=72516644
	public static final int SEARCH_ENGINE_GOOGLE_COM_QA = 66;
	public static final int SEARCH_ENGINE_GOOGLE_COM_PA = 67;
	public static final int SEARCH_ENGINE_GOOGLE_JO = 68;
	public static final int SEARCH_ENGINE_GOOGLE_HR = 69;
	public static final int SEARCH_ENGINE_GOOGLE_DZ = 70;

	// https://www.wrike.com/open.htm?id=76941846
	public static final int SEARCH_ENGINE_GOOGLE_RO = 71;

	//Leo - https://www.wrike.com/open.htm?id=102415378
	public static final int SEARCH_ENGINE_GOOGLE_AL = 72;
	public static final int SEARCH_ENGINE_GOOGLE_AZ = 73;
	public static final int SEARCH_ENGINE_GOOGLE_BG = 74;
	public static final int SEARCH_ENGINE_GOOGLE_COM_BH = 75;
	public static final int SEARCH_ENGINE_GOOGLE_COM_CY = 76;
	public static final int SEARCH_ENGINE_GOOGLE_DJ = 77;
	public static final int SEARCH_ENGINE_GOOGLE_COM_EG = 78;
	public static final int SEARCH_ENGINE_GOOGLE_COM_ET = 79;
	public static final int SEARCH_ENGINE_GOOGLE_GA = 80;
	public static final int SEARCH_ENGINE_GOOGLE_GE = 81;
	public static final int SEARCH_ENGINE_GOOGLE_COM_KW = 82;
	public static final int SEARCH_ENGINE_GOOGLE_CO_MA = 83;
	public static final int SEARCH_ENGINE_GOOGLE_MC = 84;
	public static final int SEARCH_ENGINE_GOOGLE_COM_MT = 85;
	public static final int SEARCH_ENGINE_GOOGLE_MU = 86;
	public static final int SEARCH_ENGINE_GOOGLE_COM_OM = 87;
	public static final int SEARCH_ENGINE_GOOGLE_RS = 88;
	public static final int SEARCH_ENGINE_GOOGLE_SC = 89;
	public static final int SEARCH_ENGINE_GOOGLE_COM_TJ = 90;
	public static final int SEARCH_ENGINE_GOOGLE_TN = 91;
	public static final int SEARCH_ENGINE_GOOGLE_CO_UG = 92;
	//Leo - https://www.wrike.com/open.htm?id=151468121
	public static final int SEARCH_ENGINE_GOOGLE_COM_SV = 93;
	//Leo - https://www.wrike.com/open.htm?id=162032768
	public static final int SEARCH_ENGINE_GOOGLE_LU = 94;
	//Sunny - https://www.wrike.com/open.htm?id=194110395
	public static final int SEARCH_ENGINE_GOOGLE_LT = 95;
	// Meo - https://www.wrike.com/open.htm?id=151728885
	public static final int SEARCH_ENGINE_GOOGLE_MM = 96;
	// Meo
	public static final int SEARCH_ENGINE_GOOGLE_EE = 97;
	// Meo
	public static final int SEARCH_ENGINE_GOOGLE_LV = 98;

	public static final int SEARCH_ENGINE_GOOGLE_US_SZ = 181;

	// Meo Uzbekistan (UZ)
	public static final int SEARCH_ENGINE_GOOGLE_UZ = 201;
	
	// https://www.wrike.com/open.htm?id=374140477
	public static final int SEARCH_ENGINE_GOOGLE_JM = 202;
	public static final int SEARCH_ENGINE_GOOGLE_KG = 203;
	public static final int SEARCH_ENGINE_GOOGLE_NP = 204;
	public static final int SEARCH_ENGINE_GOOGLE_NI = 205;
	public static final int SEARCH_ENGINE_GOOGLE_TZ = 206;
	public static final int SEARCH_ENGINE_GOOGLE_HN = 207;
	
	// https://www.wrike.com/open.htm?id=396753192
	public static final int SEARCH_ENGINE_GOOGLE_AM = 208;
	public static final int SEARCH_ENGINE_GOOGLE_GT = 209;
	
	// https://www.wrike.com/open.htm?id=427448309
	// only for google.es 16 -17
	public static final int SEARCH_ENGINE_GOOGLE_ES_MADRID  = 211;
	// https://www.wrike.com/open.htm?id=434654914
	public static final int SEARCH_ENGINE_GOOGLE_BR_SAOPAULO  = 212;
	public static final int SEARCH_ENGINE_GOOGLE_DE_BERLIN  = 213;
	public static final int SEARCH_ENGINE_GOOGLE_IT_ROME  = 214;
	public static final int SEARCH_ENGINE_GOOGLE_FR_PARIS  = 215;
	public static final int SEARCH_ENGINE_GOOGLE_UK_LONDON  = 216;
	public static final int SEARCH_ENGINE_GOOGLE_US_NEWYORK  = 217;
	public static final int SEARCH_ENGINE_GOOGLE_JP_TOKYO  = 218;
	public static final int SEARCH_ENGINE_GOOGLE_CA_TORONTO  = 219;
	public static final int SEARCH_ENGINE_GOOGLE_IN_MUMBAI  = 220;
	public static final int SEARCH_ENGINE_GOOGLE_AU_SYDNEY  = 221;
	public static final int SEARCH_ENGINE_GOOGLE_MX_MEXICO  = 222;
	public static final int SEARCH_ENGINE_GOOGLE_US_LOSANGELES  = 223;
	public static final int SEARCH_ENGINE_GOOGLE_CA_MONTREAL  = 210;
	
	// https://www.wrike.com/open.htm?id=436571823
	public static final int SEARCH_ENGINE_GOOGLE_MK_NORTHMACEDONIA  = 224;
	public static final int SEARCH_ENGINE_GOOGLE_SI_SLOVENIA  = 225;
	
	// https://www.wrike.com/open.htm?id=448545610
	public static final int SEARCH_ENGINE_GOOGLE_KZ  = 226;
	
	// https://www.wrike.com/open.htm?id=432727497
	public static final int SEARCH_ENGINE_GOOGLE_VG  = 227;
	public static final int SEARCH_ENGINE_GOOGLE_VI  = 228;
	
	// https://www.wrike.com/open.htm?id=474953414
	public static final int SEARCH_ENGINE_GOOGLE_UAE_DUBAI  = 229;
	public static final int SEARCH_ENGINE_GOOGLE_TR_ISTANBUL  = 230;
	public static final int SEARCH_ENGINE_GOOGLE_NL_AMSTERDAM  = 231;
	public static final int SEARCH_ENGINE_GOOGLE_COM_SA_RIYADH  = 232; // https://www.wrike.com/open.htm?id=504368907

	public static final int SEARCH_ENGINE_GOOGLE_BA = 233; // https://www.wrike.com/open.htm?id=526970866
	
	public static final int SEARCH_ENGINE_GOOGLE_SE_STOCKHOLM = 234; // https://www.wrike.com/open.htm?id=559978290
	
	// https://www.wrike.com/open.htm?id=630673915
	public static final int SEARCH_ENGINE_GOOGLE_PL_WARSAW  = 235;

	public static final int SEARCH_ENGINE_GOOGLE_GH = 242;
	public static final int SEARCH_ENGINE_GOOGLE_BS = 243; // https://www.wrike.com/open.htm?id=1163434912
	public static final int SEARCH_ENGINE_GOOGLE_TT = 244;

	public static final int SEARCH_ENGINE_GOOGLE_NA = 245; // https://www.wrike.com/open.htm?id=1301270306
	public static final int SEARCH_ENGINE_GOOGLE_MW = 246; // https://www.wrike.com/open.htm?id=1304006040

	public static final int SEARCH_ENGINE_GOOGLE_CO_BW = 247; // https://www.wrike.com/open.htm?id=1310012938
	public static final int SEARCH_ENGINE_GOOGLE_CO_AO = 248;

	public static final int SEARCH_ENGINE_GOOGLE_CO_ZW = 249; // https://www.wrike.com/open.htm?id=1316168238
	public static final int SEARCH_ENGINE_GOOGLE_CD= 250; // https://www.wrike.com/open.htm?id=1321550449
	public static final int SEARCH_ENGINE_GOOGLE_CO_LS= 251; // https://www.wrike.com/open.htm?id=1321557385
	public static final int SEARCH_ENGINE_GOOGLE_CO_ZM = 252; // https://www.wrike.com/open.htm?id=1326159997
	public static final int SEARCH_ENGINE_GOOGLE_CO_MZ = 253; // https://www.wrike.com/open.htm?id=1354980477
	
	public static final int SEARCH_BING = 255;

	public static final int SEARCH_YAHOO = 100;

	public static final int SEARCH_ENGINE_360_SEARCH = 170;

	public static final int SEARCH_ENGINE_SOGOU = 180;

	// public static final int SEARCH_NAVER = 102
	// maybe some error;
	public static final int SEARCH_NAVER = 160;
	public static final int SEARCH_YANDEX = 120;

	// https://www.wrike.com/open.htm?id=230189025
	public static final int SEARCH_ENGINE_APPLE = 110;

	// https://www.wrike.com/open.htm?id=238463089
	public static final int SEARCH_ENGINE_JOB = 119;
	
	public static final int SEARCH_ENGINE_GOOGLE_TREND = 254;
	
	// https://www.wrike.com/open.htm?id=531635620
	public static final int SEARCH_ENGINE_YOUTUBE = 130;
	
	// https://www.wrike.com/open.htm?id=638327786
	public static final int SEARCH_ENGINE_GOOGLE_IMAGE = 236;
	public static final int SEARCH_ENGINE_GOOGLE_SHOPPING = 237;

	public static final int LANGUAGE_US_EN = 1;

	public static final int LANGUAGE_US_ES = 2;

	public static final int LANGUAGE_CA_EN = 3;

	public static final int LANGUAGE_CA_FR = 4;

	public static final int LANGUAGE_AU_EN = 5;

	public static final int LANGUAGE_CN_ZH = 6;

	public static final int LANGUAGE_FR_FR = 7;

	public static final int LANGUAGE_UK_EN = 8;

	public static final int LANGUAGE_IT_IT = 9;

	public static final int LANGUAGE_DK_DA = 10;

	public static final int LANGUAGE_FI_FI = 11;

	public static final int LANGUAGE_MX_ES = 12;

	public static final int LANGUAGE_NO_NO = 13;

	public static final int LANGUAGE_SE_SV = 14;

	public static final int LANGUAGE_DE_DE = 15;

	public static final int LANGUAGE_BR_PT = 16;

	public static final int LANGUAGE_ES_ES = 17;

	public static final int LANGUAGE_NL_NL = 18;

	public static final int LANGUAGE_JP_JA = 19;

	public static final int LANGUAGE_PT_PT = 20;

	public static final int LANGUAGE_IE_EN = 21;
	public static final int LANGUAGE_BE_NL = 22;
	public static final int LANGUAGE_CH_DE = 23;
	public static final int LANGUAGE_KR_KO = 24;
	public static final int LANGUAGE_IN_EN = 25;

	public static final int LANGUAGE_AR_ES = 26;
	public static final int LANGUAGE_CL_ES = 27;
	public static final int LANGUAGE_CO_ES = 28;
	public static final int LANGUAGE_PR_ES = 29;
	public static final int LANGUAGE_AT_DE = 30;

	public static final int LANGUAGE_ID_ID = 31;
	public static final int LANGUAGE_MY_EN = 32;
	public static final int LANGUAGE_PH_TL = 33;
	public static final int LANGUAGE_TW_ZH = 34;
	public static final int LANGUAGE_TH_TH = 35;
	public static final int LANGUAGE_VN_VI = 36;
	public static final int LANGUAGE_SG_EN = 37;
	public static final int LANGUAGE_NZ_EN = 38;
	public static final int LANGUAGE_RU_RU = 39;

	//https://www.wrike.com/open.htm?id=9415299
	public static final int LANGUAGE_CZ_CS = 40;
	public static final int LANGUAGE_HU_HU = 41;
	public static final int LANGUAGE_PL_PL = 42;

	public static final int LANGUAGE_SA_AR = 43;
	public static final int LANGUAGE_PE_ES = 44;
	public static final int LANGUAGE_AE_AR = 45;
	public static final int LANGUAGE_AE_EN = 46;
	public static final int LANGUAGE_TR_TR = 47;
	public static final int LANGUAGE_ZA_EN = 48;
	public static final int LANGUAGE_KE_EN = 49;

	//https://www.wrike.com/open.htm?id=36219549
	public static final int LANGUAGE_SK_SK = 50;
	public static final int LANGUAGE_HE_IL = 51;
	public static final int LANGUAGE_ES_EC = 52;
	public static final int LANGUAGE_ES_VE = 53;
	public static final int LANGUAGE_ES_CR = 54;

	// https://www.wrike.com/open.htm?id=46752792
	public static final int LANGUAGE_UK_UA = 55;
	public static final int LANGUAGE_ES_UY = 56;

	// https://www.wrike.com/open.htm?id=53458412
	public static final int LANGUAGE_MV_EN = 57;

	// https://www.wrike.com/open.htm?id=58651708
	// https://www.wrike.com/open.htm?id=58651849
	// https://www.wrike.com/open.htm?id=58778920
	// https://www.wrike.com/open.htm?id=58779062
	public static final int LANGUAGE_DB_BN = 58;
	public static final int LANGUAGE_KH_KM = 59;
	public static final int LANGUAGE_FJ_EN = 60;
	public static final int LANGUAGE_WS_EN = 61;

	// https://www.wrike.com/open.htm?id=60576663
	public static final int LANGUAGE_GR_EL = 62;

	// https://www.wrike.com/open.htm?id=63075948
	public static final int LANGUAGE_NG_EN = 63;

	// https://www.wrike.com/open.htm?id=66773684
	public static final int LANGUAGE_DO_ES = 64;

	// https://www.wrike.com/open.htm?id=70234779
	public static final int LANGUAGE_PK_EN = 65;
	public static final int LANGUAGE_LB_AR = 66;

	// https://www.wrike.com/open.htm?id=72516644
	public static final int LANGUAGE_AR_QA = 67;
	public static final int LANGUAGE_ES_PA = 68;
	public static final int LANGUAGE_AR_JO = 69;
	public static final int LANGUAGE_HR_HR = 70;
	public static final int LANGUAGE_AR_DZ = 71;

	// https://www.wrike.com/open.htm?id=76941846
	public static final int LANGUAGE_RO_RO = 72;

	//Leo - https://www.wrike.com/open.htm?id=102415378
	public static final int LANGUAGE_SQ_AL = 73;
	public static final int LANGUAGE_AZ_AZ = 74;
	public static final int LANGUAGE_BG_BG = 75;
	public static final int LANGUAGE_AR_BH = 76;
	public static final int LANGUAGE_EL_CY = 77;
	public static final int LANGUAGE_FR_DJ = 78;
	public static final int LANGUAGE_AR_EG = 79;
	public static final int LANGUAGE_AM_ET = 80;
	public static final int LANGUAGE_FR_GA = 81;
	public static final int LANGUAGE_KA_GE = 82;
	public static final int LANGUAGE_AR_KW = 83;
	public static final int LANGUAGE_AR_MA = 84;
	public static final int LANGUAGE_FR_MC = 85;
	public static final int LANGUAGE_MT_MT = 86;
	public static final int LANGUAGE_EN_MU = 87;
	public static final int LANGUAGE_AR_OM = 88;
	public static final int LANGUAGE_SR_RS = 89;
	public static final int LANGUAGE_FR_SC = 90;
	public static final int LANGUAGE_TG_TJ = 91;
	public static final int LANGUAGE_AR_TN = 92;
	public static final int LANGUAGE_SW_UG = 93;
	public static final int LANGUAGE_EN_SC = 94;
	//Leo - https://www.wrike.com/open.htm?id=151468121
	public static final int LANGUAGE_ES_SV = 95;
	//Leo - https://www.wrike.com/open.htm?id=162045003
	public static final int LANGUAGE_BE_FR = 96;
	public static final int LANGUAGE_CH_FR = 97;
	public static final int LANGUAGE_CN_EN = 98;
	public static final int LANGUAGE_TH_EN = 99;
	//Leo - https://www.wrike.com/open.htm?id=162032768
	public static final int LANGUAGE_LU_LB = 100;
	//Sunny - https://www.wrike.com/open.htm?id=163324308
	public static final int LANGUAGE_PH_EN = 101;

	//scott - https://www.wrike.com/open.htm?id=179555118
	public static final int LANGUAGE_FI_SV = 102;

	//scott - https://www.wrike.com/open.htm?id=179555118
	public static final int LANGUAGE_LU_FR = 103;

	//Sunny - https://www.wrike.com/open.htm?id=187691124
	public static final int LANGUAGE_RU_UK = 104;

	//Sunny - https://www.wrike.com/open.htm?id=194110395
	public static final int LANGUAGE_LT_LT = 105;

	// Meo - https://www.wrike.com/open.htm?id=203789852
	public static final int LANGUAGE_EN_KW = 106;

	// Meo - https://www.wrike.com/open.htm?id=203789852
	public static final int LANGUAGE_EN_BH = 107;

	// Meo - https://www.wrike.com/open.htm?id=151728885
	public static final int LANGUAGE_MY_MM = 108;

	// Meo - https://www.wrike.com/open.htm?id=226088963
	public static final int LANGUAGE_MS_MY = 109;

	// Meo - https://www.wrike.com/open.htm?id=240393199
	public static final int LANGUAGE_SA_EN = 110;

	// Meo - https://www.wrike.com/open.htm?id=240393199
	public static final int LANGUAGE_EG_EN = 111;

	// Meo
	public static final int LANGUAGE_EE_ET = 112;

	// Meo
	public static final int LANGUAGE_LV_LV = 113;
	
	// Meo
	public static final int LANGUAGE_BE_EN = 114;

	// Meo
	public static final int LANGUAGE_UZ_UZ = 115;

	// https://www.wrike.com/open.htm?id=349497633
	public static final int LANGUAGE_EE_RU = 116;
	public static final int LANGUAGE_LV_RU = 117;
	
	// https://www.wrike.com/open.htm?id=374140477
	public static final int LANGUAGE_CR_ES = 118;
	public static final int LANGUAGE_SV_ES = 119;
	public static final int LANGUAGE_JM_EN = 120;
	public static final int LANGUAGE_KG_RU = 121;
	public static final int LANGUAGE_NP_NE = 122;
	public static final int LANGUAGE_NI_ES = 123;
	public static final int LANGUAGE_PA_ES = 124;
	public static final int LANGUAGE_TZ_SW = 125;
	public static final int LANGUAGE_HN_ES = 126;
	
	// https://www.wrike.com/open.htm?id=391372442
	public static final int LANGUAGE_ID_EN = 127;
	public static final int LANGUAGE_VN_EN = 128;
	
	// https://www.wrike.com/open.htm?id=396753192
	public static final int LANGUAGE_AM_EN = 129;
	public static final int LANGUAGE_UY_EN = 130;
	public static final int LANGUAGE_TJ_EN = 131;
	public static final int LANGUAGE_GT_EN = 132;
	
	// https://www.wrike.com/open.htm?id=399805965
	public static final int LANGUAGE_TW_EN = 133;
	public static final int LANGUAGE_PR_EN = 134;
	public static final int LANGUAGE_CR_EN = 135;
	
	// https://www.wrike.com/open.htm?id=437041808
	public static final int LANGUAGE_DK_EN = 136;
	
	// https://www.wrike.com/open.htm?id=437635887
	public static final int LANGUAGE_TR_EN = 137;
	
	// https://www.wrike.com/open.htm?id=436571823
	public static final int LANGUAGE_MK_MK = 138;
	public static final int LANGUAGE_SI_SL = 139;
	
	// https://www.wrike.com/open.htm?id=439147650
	public static final int LANGUAGE_SE_EN = 140;
	
	// https://www.wrike.com/open.htm?id=440408814
	public static final int LANGUAGE_CZ_EN = 141;
	public static final int LANGUAGE_FI_EN = 142;
	public static final int LANGUAGE_IL_EN = 143;
	public static final int LANGUAGE_NO_EN = 144;
	public static final int LANGUAGE_PL_EN = 145;
	public static final int LANGUAGE_PT_EN = 146;
	
	public static final int LANGUAGE_QA_EN = 147;
	public static final int LANGUAGE_LU_DE = 148;
	public static final int LANGUAGE_UY_ES = 149;
	public static final int LANGUAGE_OM_EN = 150;
	
	// https://www.wrike.com/open.htm?id=448545610
	public static final int LANGUAGE_KZ_KK = 151;

	// https://www.wrike.com/open.htm?id=432727497
	public static final int LANGUAGE_VG_EN = 152;
	public static final int LANGUAGE_VI_EN = 153;
	
	// https://www.wrike.com/open.htm?id=468009489
	public static final int LANGUAGE_MA_FR = 154;
	
	// https://www.wrike.com/open.htm?id=472018122
	public static final int LANGUAGE_CO_EN = 155;
	
	public static final int LANGUAGE_NL_EN = 156;
	
	public static final int LANGUAGE_DE_EN = 157; // https://www.wrike.com/open.htm?id=496872580
	public static final int LANGUAGE_JP_EN = 158; // https://www.wrike.com/open.htm?id=495788984
	
	public static final int LANGUAGE_BR_EN = 159; // https://www.wrike.com/open.htm?id=505280035
	public static final int LANGUAGE_ES_EN = 160;
	public static final int LANGUAGE_IN_HI = 161; // https://www.wrike.com/open.htm?id=512011267
	
	public static final int LANGUAGE_BA_BS = 162; // https://www.wrike.com/open.htm?id=526970866
	
	// https://www.wrike.com/open.htm?id=557269627
	public static final int LANGUAGE_CH_IT = 163;
	public static final int LANGUAGE_CH_EN = 164;
	public static final int LANGUAGE_FR_EN = 165;
	public static final int LANGUAGE_IT_EN = 166;
	public static final int LANGUAGE_AT_EN = 176;
	public static final int LANGUAGE_KZ_EN = 177;
	public static final int LANGUAGE_AZ_EN = 178;
	public static final int LANGUAGE_GH_EN = 179;

	public static final int LANGUAGE_US_ZH_CN = 180; // https://www.wrike.com/open.htm?id=1163459615
	public static final int LANGUAGE_BB_EN = 181; // https://www.wrike.com/open.htm?id=1163434912
	public static final int LANGUAGE_TT_EN = 182;
	public static final int LANGUAGE_GR_EN = 183; // https://www.wrike.com/open.htm?id=1163459615
	public static final int LANGUAGE_NA_EN = 184;
	public static final int LANGUAGE_MW_EN = 185;
	public static final int LANGUAGE_BW_EN = 186;
	public static final int LANGUAGE_AO_EN = 187;
	public static final int LANGUAGE_ZW_EN = 188;
	public static final int LANGUAGE_AO_PT = 189; // https://www.wrike.com/open.htm?id=1316911874
	public static final int LANGUAGE_CD_EN = 190;
	public static final int LANGUAGE_LS_EN = 191;

	public static final int LANGUAGE_TZ_EN = 192;
	public static final int LANGUAGE_ZM_EN = 193;

	public static final int LANGUAGE_UG_EN = 194; // https://www.wrike.com/open.htm?id=1327124211
	public static final int LANGUAGE_CL_EN = 195; // https://www.wrike.com/open.htm?id=1344460856
	public static final int LANGUAGE_MZ_EN = 196; // https://www.wrike.com/open.htm?id=1354980477
	public static final int LANGUAGE_SZ_EN = 197; // https://www.wrike.com/open.htm?id=1315718692
	public static final int LANGUAGE_MZ_PT = 198; // https://www.wrike.com/open.htm?id=1527113075

	public static final int LANGUAGE_NO_NB = 199; // https://www.wrike.com/open.htm?id=1571104531

	public static final int LANGUAGE_AZ_RU = 200; // https://www.wrike.com/open.htm?id=1621191022
	public static final int LANGUAGE_GE_RU = 201;
	public static final int LANGUAGE_KZ_RU = 202;

	private int id;

	private String keywordText;

	private String queryDate;

	private Message message;

	private String cityName;
	
	private String createDate;
	
	private String domainList;
	
	private String uule; 
	
	private String lat;

	private String lng;
	
	// by Meo
	private Long searchVolume;
	private Float costPerClick;
	private Integer cityId;
	private Long relId;

	private boolean newly;

	private List<MonthlySearchVolume> monthlySearchvolumeTrends;

	public String getCreateDate() {
		return createDate;
	}

	public void setCreateDate(String createDate) {
		this.createDate = createDate;
	}

	private boolean hasBlocked;

	private List<String> additionalParameters;

	public List<String> getAdditionalParameters() {
		return additionalParameters;
	}

	public void setAdditionalParameters(List<String> additionalParameters) {
		this.additionalParameters = additionalParameters;
	}

	public Long getRelId() {
		return relId;
	}

	public void setRelId(Long relId) {
		this.relId = relId;
	}

	public String getQueryPrameter() {
		if (additionalParameters != null && additionalParameters.size() > 0) {
			StringBuffer parameter = new StringBuffer();
			for (String para : additionalParameters) {
				parameter.append(para);
			}
			return parameter.toString();
		} else {
			return "";
		}
	}
	
	public Message getMessage() {
		return message;
	}

	public void setMessage(Message message) {
		this.message = message;
	}

	public boolean isHasBlocked() {
		return hasBlocked;
	}

	public void setHasBlocked(boolean hasBlocked) {
		this.hasBlocked = hasBlocked;
	}

	public String getQueryDate() {
		return queryDate;
	}

	public void setQueryDate(String queryDate) {
		this.queryDate = queryDate;
	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getKeywordText() {
		return keywordText;
	}

	public void setKeywordText(String keywordText) {
		this.keywordText = keywordText;
	}

	public String getDecoderKeyword() {
		return RankCheckUtils.decoderString(getKeywordText());
	}

	public String getCityName() {
		return cityName;
	}

	public void setCityName(String cityName) {
		this.cityName = cityName;
	}

	public String getDomainList() {
		return domainList;
	}

	public void setDomainList(String domainList) {
		this.domainList = domainList;
	}

	public Long getSearchVolume() {
		return searchVolume;
	}

	public void setSearchVolume(Long searchVolume) {
		this.searchVolume = searchVolume;
	}
	
	public Float getCostPerClick() {
		return costPerClick;
	}

	public void setCostPerClick(Float costPerClick) {
		this.costPerClick = costPerClick;
	}

	public String getUule() {
		return uule;
	}

	public void setUule(String uule) {
		this.uule = uule;
	}

	public Integer getCityId() {
		return cityId;
	}

	public void setCityId(Integer cityId) {
		this.cityId = cityId;
	}
	
	public List<MonthlySearchVolume> getMonthlySearchvolumeTrends() {
		return monthlySearchvolumeTrends;
	}

	public void setMonthlySearchvolumeTrends(List<MonthlySearchVolume> monthlySearchvolumeTrends) {
		this.monthlySearchvolumeTrends = monthlySearchvolumeTrends;
	}

	public class MonthlySearchVolume {
		private int month;
		private int avgMonthlySearchVolume;
		private int monthlySearchVolume;
		private String cpc;
		public MonthlySearchVolume(int month, int avgMonthlySearchVolume, int monthlySearchVolume, String cpc) {
			this.month = month;
			this.avgMonthlySearchVolume = avgMonthlySearchVolume;
			this.monthlySearchVolume = monthlySearchVolume;
			this.cpc = cpc;
		}
		public int getMonth() {
			return month;
		}
		public void setMonth(int month) {
			this.month = month;
		}
		public int getAvgMonthlySearchVolume() {
			return avgMonthlySearchVolume;
		}
		public void setAvgMonthlySearchVolume(int avgMonthlySearchVolume) {
			this.avgMonthlySearchVolume = avgMonthlySearchVolume;
		}
		public int getMonthlySearchVolume() {
			return monthlySearchVolume;
		}
		public void setMonthlySearchVolume(int monthlySearchVolume) {
			this.monthlySearchVolume = monthlySearchVolume;
		}
		public String getCpc() {
			return cpc;
		}
		public void setCpc(String cpc) {
			this.cpc = cpc;
		}
		
		
	}

	public String getLat() {
		return lat;
	}

	public void setLat(String lat) {
		this.lat = lat;
	}

	public String getLng() {
		return lng;
	}

	public void setLng(String lng) {
		this.lng = lng;
	}

	public boolean isNewly() {
		return newly;
	}

	public void setNewly(boolean newly) {
		this.newly = newly;
	}

	@Override
	public String toString() {
		return "SeoClarityKeywordEntity{" +
				"id=" + id +
				", keywordText='" + keywordText + '\'' +
				", createDate='" + createDate + '\'' +
				", newly=" + newly +
				'}';
	}
}
