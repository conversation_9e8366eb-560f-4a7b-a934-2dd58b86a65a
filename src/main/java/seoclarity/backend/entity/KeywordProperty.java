package seoclarity.backend.entity;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang.StringUtils;

import com.amazonaws.services.sqs.model.Message;

@Getter @Setter
public class KeywordProperty {
	private String keywordText;
	private String cityName;
	private String country;
	private Integer searchEngine;
	private Integer searchLanguage;
	private boolean hasBlocked;
	private Integer firstPageSize;
	private Message message;
	private String scribeIpAndPort;
	private Integer id;
	private Integer dealyTime;
	private Integer keywordRankType;
	private String knogTag;
	private List<String> thingsTodo;
	private int sendToQDate;
	private String lat;
	private String lng;

	// below 4 properties for adhoc
	private Integer ownDomainId;
	private Long keywordId;
	private boolean needRankCheck;
	private Integer resourceAddDetailId;
	private String createDate;

	private long searchVol;
	private float cpc;

	private String[] domainList;

	private String plaFlg;
	private String ppcFlg;
	private String llFlg;
	private String topPpcCnt;
	
	private String uule;
	private List<String> localListing;
	private Integer noResultCount;
	
	private boolean notRealSearchVolume;
	
	private int cityId;
	private Integer useSendToQDateForS3;
	private String s3BucketName;
	private String s3queryDate;
	private Integer frequency;
	private Integer projectId;
	private Integer device;
	private Integer processMonth;
	
	private List<SeoKeywordAdsEntity> adsList;

	private String clarityDBKeywordHash;
	
	private String googleRecommend; // https://www.wrike.com/open.htm?id=592861808

	private Integer resultNumber;
	private String provider;
	private String scribePath;
	private String s3KeyPath;
	private String keywordQueueName;
	
	public List<String> getLocalListing() {
		if (localListing == null) {
			localListing = new ArrayList<String>();
		} else {
			Map<String, String> uniquell = new LinkedHashMap<String, String>();
			for (String llInfo : localListing) {
				// String ll[] = llInfo.split("!_!");
				String ll[] = StringUtils.splitByWholeSeparatorPreserveAllTokens(llInfo, "!_!");
				if (ll.length == 3) {
					if (!uniquell.containsKey(ll[0])) {
						uniquell.put(ll[0], llInfo);
					}
				}
			}
			localListing = new ArrayList<String>();
			for (String ll : uniquell.values()) {
				localListing.add(ll);
			}
		}
		return localListing;
	}

	public String getCityName() {
		if (StringUtils.isNotBlank(cityName) && StringUtils.containsIgnoreCase(cityName, " ")) {
			return cityName.replaceAll(" ", "%20");
		} else if (StringUtils.isNotBlank(cityName) && StringUtils.containsIgnoreCase(cityName, "20%")) {
			return cityName.replaceAll("20%", "%20");
		}
		return cityName;
	}
	
	

}
