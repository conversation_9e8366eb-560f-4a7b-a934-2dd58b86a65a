package seoclarity.backend.entity.actonia.cloudvo;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

@Getter @Setter
public class KeywordRankVO implements Serializable {

	private static final long serialVersionUID = 1351401185672317490L;
	private String country;
	private String language;
	private String knogTag;
	private String engine;
	private Integer searchEngine;
	private Integer searchLanguage;
	private String keyword;
	private String queryDate;
	private int qeuryState;
	private Integer firstPageSize;
	private String cityName;
	private Integer cityId;
	private String label;
	private String authorNm;
	private String authorLink;
	private String plaFlg;
	private String ppcFlg;
	private String llFlg;
	private String hotelFlg;
	private String researchFlg;
	private String flightSearchFlg;
	private String commercialFlg;
	private String topPpcCnt;
	private String createDate;
	private String lpu;
	private long searchVol;
	private float cpc;
	private String questions;

	private List<String> questionList;
	private String additionalQuestions;

	private List<Map<String, String>> additionalQuestionList;
	private String[] domainList;
	private Integer id;
	private String answerBox;
	private String appFlg;
	private List<String> appList;
	//did you mean
	private String googleRecommend;
	//showing result for
	private String googleSearchInstead;
	//Including results for
	private String googleSearchInclude;
	private String notGreatMatch;
	private String googleResultCnt;
	private List<String> relatedSearch;
	private String additionalAnswerBox;
	private String socialInKg;
	private int sendToQDate;
	private String quickLinksFlg;
	private String jobLink;
	private String refineBy;
	// https://www.wrike.com/open.htm?id=217643921
	private List<String> linksInKnog;
	//Leo - https://www.wrike.com/open.htm?id=276422139
	private Integer frequency;
	private Integer projectId;
	private Integer processMonth;
	private Integer device;
	private boolean emptyHtml = false;
	private boolean notRealSearchVolume = false;
	private String clarityDBKeywordHash;
	private String rightFreeShop;
	private List rightFreeShopList;

	private HashSet<String> triggerCodes;
	private HashSet<String> triggerClasses;
	private JSONObject vedInfo;
	private JSONArray productList;
	private JSONArray localListingV2;
	private JSONArray hotelListing;
	private Integer nearYou;
	private List<KeywordPixelRankEntityVO> pixelRankList;
	private String ossKeyPath;
	private String scribePath;
	private List<String> suggestList;

	private List<KeywordRankEntityVO> keywordRankEntityVOs;
	private List<SeoKeywordAdsEntity> allAds;
	private List<SeoKeywordPlaEntity> plaList;
	private List<String> things;
	private List<String> inDepthArt;
	private List<String> localListing;
	private List<LocalListingEntity> localListingEntityVOs;
	private List<JobEntity> jobEntityVOs;

	private Boolean fuleCostCal;

	private JSONArray appbarKeywords;

	private List<String> kgEntityValues;

	private Boolean mediaApp;

	private List<JSONObject> aiGeneratedCarousal;
	private List<JSONObject> aiGeneratedContent;

	private Map<String, String> buyingGuides;

	private List<ThingsToKnow> thingsToKnow;

	private Boolean discussionsAndForums;

	private List<Map<String, String>> discussionsEntities;

	private Boolean exploreBrandsFlg;
	private List<KeywordSubRankEntityVO> aiGenaiSearchLinks;

	private String uid = IdUtil.fastUUID();
}
