package seoclarity.backend.entity.actonia.adhoc;

import java.util.*;


public class AutoAdhocRankProjectEntity {

    public static final int ENABLED = 1;
    public static final int DISABLED = 0;

    public static final int RETRIEVE_TYPE_RANKCHECK_ONLY = 1;
    public static final int RETRIEVE_GOOGLE_SV_ONLY = 2;
    public static final int RETRIEVE_BING_SV_ONLY = 3;
    public static final int RETRIEVE_RANKCHECK_AND_GOOGLE_SV = 4;
    public static final int RETRIEVE_TYPE_JOB_RANKCHECK = 10; // https://www.wrike.com/open.htm?id=1082205455
    public static final int RETRIEVE_TYPE_AIO_RANKCHECK = 11; // https://www.wrike.com/open.htm?id=1424821200

    public static final int RETRIEVE_TYPE_ADS_RANKCHECK = 12; // https://www.wrike.com/open.htm?id=1628510591

    public static final int FREQUENCY_TYPE_HOURLY = 0;
    public static final int FREQUENCY_TYPE_DAILY = 1;
    public static final int FREQUENCY_TYPE_WEEKLY = 7;
    public static final int FREQUENCY_TYPE_MONTHLY = 30;

    public static final int PRIORITY_LOW = 1;
    public static final int PRIORITY_MEDIUM = 2;
    public static final int PRIORITY_HIGH = 3;
    public static final int PRIORITY_URGENT = 4;
    
    public static final int DEVICE_DESKTOP = 1;
    public static final int DEVICE_MOBILE = 2;
    public static final int DEVICE_TABLET = 3;

    public static final int STATUS_NOT_PASS_CHECK = 1000;
    public static final int STATUS_FILE_UPLOAD_TO_FTP = 1001;
    public static final int STATUS_PROCESSING = 1002;
    public static final int STATUS_COMPLETED_SUCCESSFULLY = 1003;
    public static final int STATUS_COMPLETED_WITH_ERROR = 1004;
    public static final int STATUS_SAVE_KEYWORDS_TO_DB_ERROR = 1005; 
    public static final int STATUS_RANKCHECK_ERROR = 1006;
    public static final int STATUS_PARTIALLY_COMPLETED = 1007;
    public static final int STATUS_SKIP_INACTIVE_DOMAIN = 1008;
    public static final int STATUS_CANCELLED = 1009;

    public static final int RANK_STATUS_NOT_STARTED = 0;
    public static final int RANK_STATUS_PROCESSING = 1;
    public static final int RANK_STATUS_COMPLETEED_WITHOUT_ERROR = 2;
    public static final int RANK_STATUS_ERROR = 3;
    public static final int RANK_STATUS_NO_NEED_RANKCHECK = 4;
    public static final int RANK_STATUS_SENT_TO_S3_FAILURE = 5;
    public static final int RANK_STATUS_SENT_TO_SQS_FAILURE = 6; 
    public static final int RANK_STATUS_SENT_WITHOUT_STARTING_ENOUGH_EC2 = 7;

    public static final int RETRIEVESV_STATUS_NOT_STARTED = 0;
    public static final int RETRIEVESV_STATUS_PROCESSING = 1;
    public static final int RETRIEVESV_STATUS_COMPLETEED_WITHOUT_ERROR = 2;
    public static final int RETRIEVESV_STATUS_ERROR = 3;
    public static final int RETRIEVESV_STATUS_NO_NEED_RETRIEVE_SV = 4;
    public static final int RETRIEVESV_STATUS_SKIP_NO_VALID_ADWORDS_GEO_ID = 5;
    public static final int RETRIEVESV_STATUS_SKIP_FOR_INVALID_CHARACTERSET = 6;
    public static final int RETRIEVESV_STATUS_ADHOC_JOB_CRAWLER = 10;

    public static final int RANK_UPLOAD_STATUS_NOT_STARTED = 0;
    public static final int RANK_UPLOAD_STATUS_PROCESSING = 1;
    public static final int RANK_UPLOAD_STATUS_COMPLETED_WITHOUT_ERROR = 2;
    public static final int RANK_UPLOAD_STATUS_ERROR = 3;

    public static final int EXPORT_RANK_STSTUS_NOT_STARTED = 0;
    public static final int EXPORT_RANK_STSTUS_PROCESSING = 1;
    public static final int EXPORT_RANK_STSTUS_COMPLETED_WITHOUT_ERROR = 2;
    public static final int EXPORT_RANK_STSTUS_ERROR = 3;
    public static final int UPLOAD_SV_STSTUS_COMPLETED_WITHOUT_ERROR = 2;
    public static final int UPLOAD_SV_STATUS_COMPLETED_WITH_ERROR = 3;

    public static final int KEYWORD_TYPE_NATIONAL = 0;
    public static final int KEYWORD_TYPE_GEO = 1;

    public static final int GEO_PATTERN_ONLY_KEYWORD_IN_FILE = 1;
    public static final int GEO_PATTERN_BOTH_GEO_ID_AND_KEYWORD_IN_FILE = 2;

    public static final int SEED_KEYWORD_TYPE_FILE = 1;
    public static final int SEED_KEYWORD_TYPE_SAVED_LIST = 2;

    private Integer id;
    private Integer enabled;
    private Integer keywordType;
    private Integer geoPattern;
    private Integer seedKeywordType;
    private Integer ownDomainId;
    private String projectName;
    private Integer createDate;
    private Integer createUserId;
    private Integer retrieveType;
    private String country;
    private String engine;
    private String language;
    private Integer searchEngineId;
    private Integer languageId;
    private Integer cityId;
    private String cityName;
    private String lat;
    private String lng;
    private Integer resultNum;
    private Integer topXRankCluster;
    private Integer kewordClusterStatus;
    private Date kewordClusterUpdateDate;
    private Integer device;     //'1: desktop, 2: mobile, 3: tablet'
    private Integer criteriaId;
    private String canonicalName;
	private Integer frequencyType;
    private Integer frequencyValue;
    private Integer priority;
    private String ftpFullPathFilename;
    private int status;

    private Integer isRerun;
    private String s3FullPathFilename;
    private Integer keywordCountInFile;
    private Integer uniqueKeywordCount;
    private String rankQueueName;
    private String rankDeadQueueName;
    private Integer rankDeadMessageCount;
	private Date sendToRankQueueStartTime;
    private Date sendToRankQueueEndTime;
    private Integer rankStatus;
    private Integer startedEC2Instances;
    private Integer startedEC2Times;
	private String rankErrorMsg;
    private String retrieveSVQueueName;
    private Date sendToSVQueueStartTime;
    private Date sendToSVQueueEndTime;
    private Integer retrieveSVStatus;
    private String retrieveSVErrorMsg;;
    private Date createdAt;

    private Date rankCompleteTime;

    private Integer fromHour;
    private Integer toHour;
    private Date startDate;
    private Date endDate;
    private Long additionalExtractConfigId;
    private Integer visible;
    private Integer rankUploadStatus;
    private Integer completedKeywordCount;

    public static final List<Integer> RETRIEVE_TYPE_SV = new ArrayList<>();
    static {
        RETRIEVE_TYPE_SV.add(RETRIEVE_GOOGLE_SV_ONLY);
        RETRIEVE_TYPE_SV.add(RETRIEVE_BING_SV_ONLY);
    }


    public Integer getKeywordType() {
        return keywordType;
    }

    public void setKeywordType(Integer keywordType) {
        this.keywordType = keywordType;
    }

    public Integer getGeoPattern() {
        return geoPattern;
    }

    public void setGeoPattern(Integer geoPattern) {
        this.geoPattern = geoPattern;
    }

    public Integer getSeedKeywordType() {
        return seedKeywordType;
    }

    public void setSeedKeywordType(Integer seedKeywordType) {
        this.seedKeywordType = seedKeywordType;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getEnabled() {
        return enabled;
    }

    public void setEnabled(Integer enabled) {
        this.enabled = enabled;
    }

    public Integer getOwnDomainId() {
        return ownDomainId;
    }

    public void setOwnDomainId(Integer ownDomainId) {
        this.ownDomainId = ownDomainId;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public Integer getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Integer createDate) {
        this.createDate = createDate;
    }

    public Integer getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Integer createUserId) {
        this.createUserId = createUserId;
    }

    public Integer getRetrieveType() {
        return retrieveType;
    }

    public void setRetrieveType(Integer retrieveType) {
        this.retrieveType = retrieveType;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getEngine() {
        return engine;
    }

    public void setEngine(String engine) {
        this.engine = engine;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public Integer getSearchEngineId() {
        return searchEngineId;
    }

    public void setSearchEngineId(Integer searchEngineId) {
        this.searchEngineId = searchEngineId;
    }

    public Integer getLanguageId() {
        return languageId;
    }

    public void setLanguageId(Integer languageId) {
        this.languageId = languageId;
    }

    public Integer getFrequencyType() {
        return frequencyType;
    }

    public void setFrequencyType(Integer frequencyType) {
        this.frequencyType = frequencyType;
    }

    public Integer getFrequencyValue() {
        return frequencyValue;
    }

    public void setFrequencyValue(Integer frequencyValue) {
        this.frequencyValue = frequencyValue;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public String getFtpFullPathFilename() {
        return ftpFullPathFilename;
    }

    public void setFtpFullPathFilename(String ftpFullPathFilename) {
        this.ftpFullPathFilename = ftpFullPathFilename;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getS3FullPathFilename() {
        return s3FullPathFilename;
    }

    public void setS3FullPathFilename(String s3FullPathFilename) {
        this.s3FullPathFilename = s3FullPathFilename;
    }

    public Integer getKeywordCountInFile() {
        return keywordCountInFile;
    }

    public void setKeywordCountInFile(Integer keywordCountInFile) {
        this.keywordCountInFile = keywordCountInFile;
    }

    public Integer getUniqueKeywordCount() {
        return uniqueKeywordCount;
    }

    public void setUniqueKeywordCount(Integer uniqueKeywordCount) {
        this.uniqueKeywordCount = uniqueKeywordCount;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getLat() {
        return lat;
    }

    public void setLat(String lat) {
        this.lat = lat;
    }

    public String getLng() {
        return lng;
    }

    public void setLng(String lng) {
        this.lng = lng;
    }

    public Integer getResultNum() {
        return resultNum;
    }

    public void setResultNum(Integer resultNum) {
        this.resultNum = resultNum;
    }

    public Integer getDevice() {
        return device;
    }

    public void setDevice(Integer device) {
        this.device = device;
    }
    
    public Integer getCriteriaId() {
		return criteriaId;
	}

	public void setCriteriaId(Integer criteriaId) {
		this.criteriaId = criteriaId;
	}

	public String getCanonicalName() {
		return canonicalName;
	}

	public void setCanonicalName(String canonicalName) {
		this.canonicalName = canonicalName;
	}

    public Integer getIsRerun() {
        return isRerun;
    }

    public void setIsRerun(Integer isRerun) {
        this.isRerun = isRerun;
    }

    public String getRankQueueName() {
        return rankQueueName;
    }

    public void setRankQueueName(String rankQueueName) {
        this.rankQueueName = rankQueueName;
    }
    
    public String getRankDeadQueueName() {
		return rankDeadQueueName;
	}

	public void setRankDeadQueueName(String rankDeadQueueName) {
		this.rankDeadQueueName = rankDeadQueueName;
	}

	public Integer getRankDeadMessageCount() {
		return rankDeadMessageCount;
	}

	public void setRankDeadMessageCount(Integer rankDeadMessageCount) {
		this.rankDeadMessageCount = rankDeadMessageCount;
	}

    public Date getSendToRankQueueStartTime() {
        return sendToRankQueueStartTime;
    }

    public void setSendToRankQueueStartTime(Date sendToRankQueueStartTime) {
        this.sendToRankQueueStartTime = sendToRankQueueStartTime;
    }

    public Date getSendToRankQueueEndTime() {
        return sendToRankQueueEndTime;
    }

    public void setSendToRankQueueEndTime(Date sendToRankQueueEndTime) {
        this.sendToRankQueueEndTime = sendToRankQueueEndTime;
    }

    public Integer getRankStatus() {
        return rankStatus;
    }

    public void setRankStatus(Integer rankStatus) {
        this.rankStatus = rankStatus;
    }
    
    public Integer getStartedEC2Instances() {
		return startedEC2Instances;
	}

	public void setStartedEC2Instances(Integer startedEC2Instances) {
		this.startedEC2Instances = startedEC2Instances;
	}
	
	public Integer getStartedEC2Times() {
		return startedEC2Times;
	}

	public void setStartedEC2Times(Integer startedEC2Times) {
		this.startedEC2Times = startedEC2Times;
	}

    public String getRankErrorMsg() {
        return rankErrorMsg;
    }

    public void setRankErrorMsg(String rankErrorMsg) {
        this.rankErrorMsg = rankErrorMsg;
    }

    public String getRetrieveSVQueueName() {
        return retrieveSVQueueName;
    }

    public void setRetrieveSVQueueName(String retrieveSVQueueName) {
        this.retrieveSVQueueName = retrieveSVQueueName;
    }

    public Date getSendToSVQueueStartTime() {
        return sendToSVQueueStartTime;
    }

    public void setSendToSVQueueStartTime(Date sendToSVQueueStartTime) {
        this.sendToSVQueueStartTime = sendToSVQueueStartTime;
    }

    public Date getSendToSVQueueEndTime() {
        return sendToSVQueueEndTime;
    }

    public void setSendToSVQueueEndTime(Date sendToSVQueueEndTime) {
        this.sendToSVQueueEndTime = sendToSVQueueEndTime;
    }

    public Integer getRetrieveSVStatus() {
        return retrieveSVStatus;
    }

    public void setRetrieveSVStatus(Integer retrieveSVStatus) {
        this.retrieveSVStatus = retrieveSVStatus;
    }

    public String getRetrieveSVErrorMsg() {
        return retrieveSVErrorMsg;
    }

    public void setRetrieveSVErrorMsg(String retrieveSVErrorMsg) {
        this.retrieveSVErrorMsg = retrieveSVErrorMsg;
    }

    public Integer getTopXRankCluster() {
        return topXRankCluster;
    }

    public void setTopXRankCluster(Integer topXRankCluster) {
        this.topXRankCluster = topXRankCluster;
    }

    public Integer getKewordClusterStatus() {
        return kewordClusterStatus;
    }

    public void setKewordClusterStatus(Integer kewordClusterStatus) {
        this.kewordClusterStatus = kewordClusterStatus;
    }

    public Date getKewordClusterUpdateDate() {
        return kewordClusterUpdateDate;
    }

    public void setKewordClusterUpdateDate(Date kewordClusterUpdateDate) {
        this.kewordClusterUpdateDate = kewordClusterUpdateDate;
    }

    public static String getFrequencyName(int frequencyType){

        if(frequencyType == FREQUENCY_TYPE_DAILY){
            return "daily";
        }else if(frequencyType == FREQUENCY_TYPE_WEEKLY){
            return "monthly";
        }else if(frequencyType == FREQUENCY_TYPE_MONTHLY){
            return "weekly";
        }else {
            return null;
        }

    }

    public Date getRankCompleteTime() {
        return rankCompleteTime;
    }

    public void setRankCompleteTime(Date rankCompleteTime) {
        this.rankCompleteTime = rankCompleteTime;
    }

    public Integer getFromHour() {
        return fromHour;
    }

    public void setFromHour(Integer fromHour) {
        this.fromHour = fromHour;
    }

    public Integer getToHour() {
        return toHour;
    }

    public void setToHour(Integer toHour) {
        this.toHour = toHour;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Long getAdditionalExtractConfigId() {
        return additionalExtractConfigId;
    }

    public void setAdditionalExtractConfigId(Long additionalExtractConfigId) {
        this.additionalExtractConfigId = additionalExtractConfigId;
    }

    public Integer getVisible() {
        return visible;
    }

    public void setVisible(Integer visible) {
        this.visible = visible;
    }

    public Integer getRankUploadStatus() {
        return rankUploadStatus;
    }

    public void setRankUploadStatus(Integer rankUploadStatus) {
        this.rankUploadStatus = rankUploadStatus;
    }

    public Integer getCompletedKeywordCount() {
        return completedKeywordCount;
    }

    public void setCompletedKeywordCount(Integer completedKeywordCount) {
        this.completedKeywordCount = completedKeywordCount;
    }
}
