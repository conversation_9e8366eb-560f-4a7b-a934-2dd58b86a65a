package seoclarity.backend.entity.actonia.politecrawl;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PoliteCrawlRankingUrl {
    private Long id;
    private Integer ownDomainId;
    private String url;
    private String urlMurmur3Hash; // murmurHash3_64(url)
    private Date createdAt;
}
