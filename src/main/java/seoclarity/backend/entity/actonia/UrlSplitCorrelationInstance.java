package seoclarity.backend.entity.actonia;

import lombok.Data;
import lombok.Getter;
import seoclarity.backend.onetime.SplitTest;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
public class UrlSplitCorrelationInstance {

    /**
     * ID, primary key with auto-increment.
     */
    private int id;

    /**
     * Enabled status: 0 (disabled), 1 (enabled).
     */
    private int enabled;

    /**
     * page_optimization.id
     */
    private long groupId;

    /**
     * Own domain ID.
     */
    private int ownDomainId;

    /**
     * Metrics type, either GA or GSC.
     */
    private String metricsType;

    /**
     * End date for GA/GSC data.
     */
    private LocalDate metricsEndDate;

    /**
     * Backwards days from metricsEndDate for GA/GSC data.
     */
    private int metricsDays;

    /**
     * Loop times.
     */
    private int loopTimes;

    /**
     * correlation score.
     */
    private double correlationScore;
    /**
     * Full path file name for the FTP file.
     */
    private String ftpFileName;

    /**
     * SQL scriptlet, to filter URLs from GA/GSC cdb.
     */
    private String filterSqlCondition;

    /**
     * Status of the process:
     * 0 (not started), 1 (processing), 2 (completed successfully), 3 (process error).
     */
    private Status status;

    /**
     * Full path file name for the result file.
     */
    private String resultFileName;

    /**
     * Creation date, default to current timestamp.
     */
    private LocalDateTime createDate;

    public SplitTest.MetricsParams createMetricsParams() {
        final SplitTest.MetricsParams metricsParams = new SplitTest.MetricsParams();
        metricsParams.setLoopCount(this.loopTimes);
        metricsParams.setDomainId(this.ownDomainId);
        metricsParams.setEndDate(this.metricsEndDate);
        metricsParams.setMetricsType(this.metricsType.equals("GSC") ? 1 : 2);
        return metricsParams;
    }

    @Getter
    public enum Status {
        NOT_STARTED(0),
        PROCESSING(1),
        COMPLETED_SUCCESSFULLY(2),
        PROCESS_ERROR(3);

        private final int value;

        Status(int value) {
            this.value = value;
        }

        public static Status fromValue(int value) {
            switch (value) {
                case 0:
                    return NOT_STARTED;
                case 1:
                    return PROCESSING;
                case 2:
                    return COMPLETED_SUCCESSFULLY;
                case 3:
                    return PROCESS_ERROR;
                default:
                    return null;
            }
        }
    }
}

