package seoclarity.backend.entity.actonia;

import java.util.Date;
import java.util.Objects;

public class ResourceBatchDetailEntity {

    public static final int STATUS_CREATED = 0;
    public static final int STATUS_FINISHED = 2;
    public static final int STATUS_ERROR = 3;
    public static final int STATUS_INVALID = 4;

    private Long id;
    private Integer actionType;
    private Long infoId;
    private Integer ownDomainId;
    private String resourceMain;
    private String resourceSubordinate;
    private String resourceAdditional;
    private Integer resourceCategory;
    private String resourceSearchengines;
    private Long resourceId;
    private Long resourceSubId;
    private String resourceMd5;
    private Integer createDate;
    private Integer status;
    private Date processDate;
    private String errorMessage;
    private Integer statusRank;
    private Date processDateRank;
    private String errorMessageRank;
    private Integer statusSync;
    private Date processDateSync;
    private String errorMessageSync;
    private String url;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getActionType() {
        return actionType;
    }

    public void setActionType(Integer actionType) {
        this.actionType = actionType;
    }

    public Long getInfoId() {
        return infoId;
    }

    public void setInfoId(Long infoId) {
        this.infoId = infoId;
    }

    public Integer getOwnDomainId() {
        return ownDomainId;
    }

    public void setOwnDomainId(Integer ownDomainId) {
        this.ownDomainId = ownDomainId;
    }

    public String getResourceMain() {
        return resourceMain;
    }

    public void setResourceMain(String resourceMain) {
        this.resourceMain = resourceMain;
    }

    public String getResourceSubordinate() {
        return resourceSubordinate;
    }

    public void setResourceSubordinate(String resourceSubordinate) {
        this.resourceSubordinate = resourceSubordinate;
    }

    public String getResourceAdditional() {
        return resourceAdditional;
    }

    public void setResourceAdditional(String resourceAdditional) {
        this.resourceAdditional = resourceAdditional;
    }

    public Integer getResourceCategory() {
        return resourceCategory;
    }

    public void setResourceCategory(Integer resourceCategory) {
        this.resourceCategory = resourceCategory;
    }

    public String getResourceSearchengines() {
        return resourceSearchengines;
    }

    public void setResourceSearchengines(String resourceSearchengines) {
        this.resourceSearchengines = resourceSearchengines;
    }

    public Long getResourceId() {
        return resourceId;
    }

    public void setResourceId(Long resourceId) {
        this.resourceId = resourceId;
    }

    public Long getResourceSubId() {
        return resourceSubId;
    }

    public void setResourceSubId(Long resourceSubId) {
        this.resourceSubId = resourceSubId;
    }

    public String getResourceMd5() {
        return resourceMd5;
    }

    public void setResourceMd5(String resourceMd5) {
        this.resourceMd5 = resourceMd5;
    }

    public Integer getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Integer createDate) {
        this.createDate = createDate;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getProcessDate() {
        return processDate;
    }

    public void setProcessDate(Date processDate) {
        this.processDate = processDate;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public Integer getStatusRank() {
        return statusRank;
    }

    public void setStatusRank(Integer statusRank) {
        this.statusRank = statusRank;
    }

    public Date getProcessDateRank() {
        return processDateRank;
    }

    public void setProcessDateRank(Date processDateRank) {
        this.processDateRank = processDateRank;
    }

    public String getErrorMessageRank() {
        return errorMessageRank;
    }

    public void setErrorMessageRank(String errorMessageRank) {
        this.errorMessageRank = errorMessageRank;
    }

    public Integer getStatusSync() {
        return statusSync;
    }

    public void setStatusSync(Integer statusSync) {
        this.statusSync = statusSync;
    }

    public Date getProcessDateSync() {
        return processDateSync;
    }

    public void setProcessDateSync(Date processDateSync) {
        this.processDateSync = processDateSync;
    }

    public String getErrorMessageSync() {
        return errorMessageSync;
    }

    public void setErrorMessageSync(String errorMessageSync) {
        this.errorMessageSync = errorMessageSync;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    @Override
    public String toString() {
        return "ResourceBatchDetailEntity{" +
                "id=" + id +
                ", actionType=" + actionType +
                ", infoId=" + infoId +
                ", ownDomainId=" + ownDomainId +
                ", resourceMain='" + resourceMain + '\'' +
                ", resourceSubordinate='" + resourceSubordinate + '\'' +
                ", resourceAdditional='" + resourceAdditional + '\'' +
                ", resourceCategory=" + resourceCategory +
                ", resourceSearchengines='" + resourceSearchengines + '\'' +
                ", resourceId=" + resourceId +
                ", resourceSubId=" + resourceSubId +
                ", resourceMd5='" + resourceMd5 + '\'' +
                ", createDate=" + createDate +
                ", status=" + status +
                ", processDate=" + processDate +
                ", errorMessage='" + errorMessage + '\'' +
                ", statusRank=" + statusRank +
                ", processDateRank=" + processDateRank +
                ", errorMessageRank='" + errorMessageRank + '\'' +
                ", statusSync=" + statusSync +
                ", processDateSync=" + processDateSync +
                ", errorMessageSync='" + errorMessageSync + '\'' +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof ResourceBatchDetailEntity)) return false;
        ResourceBatchDetailEntity that = (ResourceBatchDetailEntity) o;
        return Objects.equals(actionType, that.actionType) &&
                Objects.equals(infoId, that.infoId) &&
                Objects.equals(ownDomainId, that.ownDomainId) &&
                Objects.equals(resourceMain, that.resourceMain) &&
                Objects.equals(resourceSubordinate, that.resourceSubordinate) &&
                Objects.equals(resourceAdditional, that.resourceAdditional) &&
                Objects.equals(resourceCategory, that.resourceCategory) &&
                Objects.equals(resourceSearchengines, that.resourceSearchengines) &&
                Objects.equals(resourceId, that.resourceId) &&
                Objects.equals(resourceSubId, that.resourceSubId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(actionType, infoId, ownDomainId, resourceMain, resourceSubordinate, resourceAdditional, resourceCategory, resourceSearchengines, resourceId, resourceSubId);
    }
}
