package seoclarity.backend.entity.actonia;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ContentAssistant {
    private Long id;
    private Integer ownDomainId;
    private Integer fixType;
    private String name;
    private Integer enabled;
    private Integer patternType;
    private Integer createUserId;
    private LocalDateTime createTime;
    private Integer state;
    private Integer qaState;
    private Integer qaEnabled;
    private Integer qaUserId;
    private String qaUrl;
    private String qaUrlHash;
    private LocalDateTime qaApproveTime;
    private LocalDateTime qaPublishTime;
    private Integer qaPublishUserId;
    private String url;
    private String urlHash;
    private String urlMurmur3Hash;
    private String version;
    private String fixIds;
    private String ruleJson;
    private String note;
    private LocalDateTime publishTime;
    private Integer publishUserId;
    private LocalDateTime updateTime;
    private Integer updateUserId;

    public static final int STATE_CREATE = 1000;
    public static final int FIX_TYPE_PAGE_OPTIMIZATION_NORMAL = 11;
    public static final long DEFAULT_VERSION = 0;
}
