package seoclarity.backend.entity.actonia;


public class GWMDomainRel {
	private Integer id;
	private Integer ownDomainId;
	private String gwmDomainName;
	private String gwmCountryCode;
	private Integer defaultDomain;
	private Integer dataSource;

	//from gsc_multi_domain_profile
	private Integer baseProfileId;
	private Integer baseDomainId;
	private Integer referProfileId;
  

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getOwnDomainId() {
		return ownDomainId;
	}

	public void setOwnDomainId(Integer ownDomainId) {
		this.ownDomainId = ownDomainId;
	}

	public String getGwmDomainName() {
		return gwmDomainName;
	}

	public void setGwmDomainName(String gwmDomainName) {
		this.gwmDomainName = gwmDomainName;
	}

	public String getGwmCountryCode() {
		return gwmCountryCode;
	}

	public void setGwmCountryCode(String gwmCountryCode) {
		this.gwmCountryCode = gwmCountryCode;
	}

	public Integer getDefaultDomain() {
		return defaultDomain;
	}

	public void setDefaultDomain(Integer defaultDomain) {
		this.defaultDomain = defaultDomain;
	}

	public Integer getDataSource() {
		return dataSource;
	}

	public void setDataSource(Integer dataSource) {
		this.dataSource = dataSource;
	}

	public Integer getBaseProfileId() {
		return baseProfileId;
	}

	public void setBaseProfileId(Integer baseProfileId) {
		this.baseProfileId = baseProfileId;
	}

	public Integer getBaseDomainId() {
		return baseDomainId;
	}

	public void setBaseDomainId(Integer baseDomainId) {
		this.baseDomainId = baseDomainId;
	}

	public Integer getReferProfileId() {
		return referProfileId;
	}

	public void setReferProfileId(Integer referProfileId) {
		this.referProfileId = referProfileId;
	}
}
