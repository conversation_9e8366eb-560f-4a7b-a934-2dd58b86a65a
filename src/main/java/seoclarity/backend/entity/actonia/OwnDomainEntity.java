package seoclarity.backend.entity.actonia;

import java.util.Date;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;

public class OwnDomainEntity {

	public static final int KEYWORD_RANK_FREQUENCY_DAILY = 1;
	public static final int KEYWORD_RANK_FREQUENCY_WEEKLY = 2;
	public static final int KEYWORD_RANK_FREQUENCY_MONTHLY = 3;
	public static final int KEYWORD_RANK_FREQUENCY_BI_WEEKLY = 14;

	public static final int ASSOCIATE_URL_RANK_NO = 0;
	public static final int ASSOCIATE_URL_RANK_YES = 1;

	public static final int POPULATE_SEMRUSH_KEYWORD_ENABLED = 1;
	public static final int POPULATE_SEMRUSH_KEYWORD_COMPLETED = 2;

	public static final int UPDATE_KEYWORD_RANK_TREND_HISTORY_INACTIVE = 0;
	public static final int UPDATE_KEYWORD_RANK_TREND_HISTORY_ACTIVE = 1;

	public static final int DISABLE_OWN_GA_TABLE = 0;
	public static final int ENABLE_OWN_GA_TABLE = 1;

	public static final int MOBILE_DOMAIN = 1;
	
	public static final int EXCLUDE_100_PLUS_AVG_RANK = 0;
	public static final int INCLUDE_100_PLUS_AVG_RANK = 1;

	public static final int GOOGLE_ANALYTICS_VER3 = 3;
	public static final int GOOGLE_ANALYTICS_VER4 = 4;
	
	/**
	 * 
	 * exact match domain name
	 * 
	 * <pre>
	 * if domain name is www.xyz.com, and match principle is EXACT.
	 * then only match www.xyz.com
	 * not match mail.xyz.xom, image.xyz.com and so on
	 * </pre>
	 */
	public static final int MATCH_EXACT = 1;

	/**
	 * 
	 * broad match domain name
	 * 
	 * <pre>
	 * if domain name is www.xyz.com, and match principle is BROAD.
	 * then will match www.xyz.com
	 * and match mail.xyz.xom, image.xyz.com and so on
	 * </pre>
	 */
	public static final int MATCH_BROAD = 0;

	public static final int STATE_SUSPEND = 2;

	public static final int STATE_ACTIVE = 1;

	public static final int REMOVE_TRAILING_SLASHES = 0;

	public static final int KEEP_TRAILING_SLASHES = 1;
	private Integer id;
	private String domain;
	private String subFolder;
	private String subFolderFragment;

	private Date createDate;
	private String name;

	private String friendlyName;

	private Integer status;
	private String gmailAccount;
	private String gmailPassword;
	private String googleAnalyticsId;
	private Integer googleAnalyticsVersion;
	private String gaSessionToken;
	private Integer targeturlLimit;
	private Integer partnerurlLimit;
	private String subscription;

	private Integer dateUnit;
	private Integer comparisonPeriod;

	private Integer showGoogleWebmasterPartnerUrl;

	private Integer ecommerceConversion;

	private Integer goal1_conversion;
	private Integer goal2_conversion;
	private Integer goal3_conversion;
	private Integer goal4_conversion;
	private Integer goal5_conversion;
	private Integer goal6_conversion;
	private Integer goal7_conversion;
	private Integer goal8_conversion;
	private Integer goal9_conversion;
	private Integer goal10_conversion;
	private Integer minHistorySumEntrances;

	private Integer upWarningValue;

	private String yahooAppid;

	private String gaDefaultPage;

	private String exactMatch;

	private Date importAnalyticsCsvDate;

	private String searchEngine;

	private String searchEngineCountry;

	private String language;

	private String mozAccessId;

	private String mozSecretKey;

	private Integer associateKeywordTopx;

	private Integer associateCompetitorTopx;

	private Integer associateTargeturlTopx;

	private Integer universalCompetitorAutoAssociate;

	private Integer linkRating;

	private Integer startDayOfWeek;

	private Integer keepTrailingSlashes;

	private Integer protocol;

	private Integer rankFrom;

	private String adwordsEmail;

	private String adwordsDevelopertoken;

	private String adwordsPassword;

	private String adwordsUseragent;

	private String majesticApiKey;

	private String majesticAccessToken;

	private Integer majesticDatasource;

	private Integer rankCalculation;

	private Integer scoreKeyword;

	private Integer scoreTargeturl;

	private Integer scoreKeywordTargeturl;

	private Integer keywordRankFrequency;

	private String currencySymbol;

	private String ga_oauth2_access_token;

	private String ga_oauth2_refresh_token;

	private Date lastMajesticDomainProcessDate;

	private Date lastMajesticUrlProcessDate;

	private Integer associateUrlRank;

	private Integer rssLimitTargeturl;
	private Integer rssLimitPartnerurl;
	private Integer rssLimitCompetitorurl;

	private String solrUrl;

	private Integer mobileDomainFlg;

	private Integer groupId;

	private String buzzstreamConsumerKey;

	private String buzzstreamConsumerSecret;

	private Integer populateGwm;
	private Integer populateSemrushKeyword;
	private Integer crawlDomain;
	private String gaWebPropertyId;

	private Integer avg_rank_include101;

	private Integer updateKeywordRankTrendHistory;

	private Integer managedLinksCount;

	private String urlCrawlParameters;

	// https://www.wrike.com/open.htm?id=27802049
	private Integer enableOwnGaTable;

	private String pagespeedClientApikey;

	public String getFriendlyName() {
		return friendlyName;
	}

	public void setFriendlyName(String friendlyName) {
		this.friendlyName = friendlyName;
	}

	public Integer getAvg_rank_include101() {
		return avg_rank_include101;
	}

	public void setAvg_rank_include101(Integer avg_rank_include101) {
		this.avg_rank_include101 = avg_rank_include101;
	}

	public String getGaWebPropertyId() {
		return gaWebPropertyId;
	}

	public void setGaWebPropertyId(String gaWebPropertyId) {
		this.gaWebPropertyId = gaWebPropertyId;
	}

	public Integer getPopulateSemrushKeyword() {
		return populateSemrushKeyword;
	}

	public void setPopulateSemrushKeyword(Integer populateSemrushKeyword) {
		this.populateSemrushKeyword = populateSemrushKeyword;
	}

	public Integer getCrawlDomain() {
		return crawlDomain;
	}

	public void setCrawlDomain(Integer crawlDomain) {
		this.crawlDomain = crawlDomain;
	}

	public Integer getPopulateGwm() {
		return populateGwm;
	}

	public void setPopulateGwm(Integer populateGwm) {
		this.populateGwm = populateGwm;
	}

	public Integer getGroupId() {
		return groupId;
	}

	public void setGroupId(Integer groupId) {
		this.groupId = groupId;
	}

	public Integer getRssLimitTargeturl() {
		return rssLimitTargeturl;
	}

	public void setRssLimitTargeturl(Integer rssLimitTargeturl) {
		this.rssLimitTargeturl = rssLimitTargeturl;
	}

	public Integer getRssLimitPartnerurl() {
		return rssLimitPartnerurl;
	}

	public void setRssLimitPartnerurl(Integer rssLimitPartnerurl) {
		this.rssLimitPartnerurl = rssLimitPartnerurl;
	}

	public Integer getRssLimitCompetitorurl() {
		return rssLimitCompetitorurl;
	}

	public void setRssLimitCompetitorurl(Integer rssLimitCompetitorurl) {
		this.rssLimitCompetitorurl = rssLimitCompetitorurl;
	}

	public String getGa_oauth2_access_token() {
		return ga_oauth2_access_token;
	}

	public void setGa_oauth2_access_token(String gaOauth2AccessToken) {
		ga_oauth2_access_token = gaOauth2AccessToken;
	}

	public String getGa_oauth2_refresh_token() {
		return ga_oauth2_refresh_token;
	}

	public void setGa_oauth2_refresh_token(String gaOauth2RefreshToken) {
		ga_oauth2_refresh_token = gaOauth2RefreshToken;
	}

	public String getCurrencySymbol() {
		return currencySymbol;
	}

	public void setCurrencySymbol(String currencySymbol) {
		this.currencySymbol = currencySymbol;
	}

	public String getMajesticAccessToken() {
		return majesticAccessToken;
	}

	public void setMajesticAccessToken(String majesticAccessToken) {
		this.majesticAccessToken = majesticAccessToken;
	}

	public Integer getKeywordRankFrequency() {
		return keywordRankFrequency;
	}

	public void setKeywordRankFrequency(Integer keywordRankFrequency) {
		this.keywordRankFrequency = keywordRankFrequency;
	}

	public Integer getScoreKeyword() {
		return scoreKeyword;
	}

	public void setScoreKeyword(Integer scoreKeyword) {
		this.scoreKeyword = scoreKeyword;
	}

	public Integer getScoreTargeturl() {
		return scoreTargeturl;
	}

	public void setScoreTargeturl(Integer scoreTargeturl) {
		this.scoreTargeturl = scoreTargeturl;
	}

	public Integer getScoreKeywordTargeturl() {
		return scoreKeywordTargeturl;
	}

	public void setScoreKeywordTargeturl(Integer scoreKeywordTargeturl) {
		this.scoreKeywordTargeturl = scoreKeywordTargeturl;
	}

	public Integer getMajesticDatasource() {
		return majesticDatasource;
	}

	public void setMajesticDatasource(Integer majesticDatasource) {
		this.majesticDatasource = majesticDatasource;
	}

	public Integer getRankCalculation() {
		return rankCalculation;
	}

	public void setRankCalculation(Integer rankCalculation) {
		this.rankCalculation = rankCalculation;
	}

	public String getMajesticApiKey() {
		return majesticApiKey;
	}

	public void setMajesticApiKey(String majesticApiKey) {
		this.majesticApiKey = majesticApiKey;
	}

	public String getAdwordsEmail() {
		return adwordsEmail;
	}

	public void setAdwordsEmail(String adwordsEmail) {
		this.adwordsEmail = adwordsEmail;
	}

	public String getAdwordsDevelopertoken() {
		return adwordsDevelopertoken;
	}

	public void setAdwordsDevelopertoken(String adwordsDevelopertoken) {
		this.adwordsDevelopertoken = adwordsDevelopertoken;
	}

	public String getAdwordsPassword() {
		return adwordsPassword;
	}

	public void setAdwordsPassword(String adwordsPassword) {
		this.adwordsPassword = adwordsPassword;
	}

	public String getAdwordsUseragent() {
		return adwordsUseragent;
	}

	public void setAdwordsUseragent(String adwordsUseragent) {
		this.adwordsUseragent = adwordsUseragent;
	}

	public Integer getKeepTrailingSlashes() {
		return keepTrailingSlashes;
	}

	public void setKeepTrailingSlashes(Integer keepTrailingSlashes) {
		this.keepTrailingSlashes = keepTrailingSlashes;
	}

	public Integer getLinkRating() {
		return linkRating;
	}

	public void setLinkRating(Integer linkRating) {
		this.linkRating = linkRating;
	}

	public Integer getUniversalCompetitorAutoAssociate() {
		return universalCompetitorAutoAssociate;
	}

	public void setUniversalCompetitorAutoAssociate(Integer universalCompetitorAutoAssociate) {
		this.universalCompetitorAutoAssociate = universalCompetitorAutoAssociate;
	}

	public Integer getAssociateKeywordTopx() {
		return associateKeywordTopx;
	}

	public void setAssociateKeywordTopx(Integer associateKeywordTopx) {
		this.associateKeywordTopx = associateKeywordTopx;
	}

	public Integer getAssociateCompetitorTopx() {
		return associateCompetitorTopx;
	}

	public void setAssociateCompetitorTopx(Integer associateCompetitorTopx) {
		this.associateCompetitorTopx = associateCompetitorTopx;
	}

	public Integer getAssociateTargeturlTopx() {
		return associateTargeturlTopx;
	}

	public void setAssociateTargeturlTopx(Integer associateTargeturlTopx) {
		this.associateTargeturlTopx = associateTargeturlTopx;
	}

	public String getMozAccessId() {
		return mozAccessId;
	}

	public void setMozAccessId(String mozAccessId) {
		this.mozAccessId = mozAccessId;
	}

	public String getMozSecretKey() {
		return mozSecretKey;
	}

	public void setMozSecretKey(String mozSecretKey) {
		this.mozSecretKey = mozSecretKey;
	}

	public String getExactMatch() {
		return exactMatch;
	}

	public void setExactMatch(String exactMatch) {
		this.exactMatch = exactMatch;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getDomain() {
		return domain;
	}

	public void setDomain(String domain) {
		this.domain = domain;
	}

	public String getSubFolderFragment() {
		return subFolderFragment;
	}

	public void setSubFolderFragment(String subFolderFragment) {
		this.subFolderFragment = subFolderFragment;
	}

	public String getSubFolder() {
		return subFolder;
	}

	public void setSubFolder(String subFolder) {
		this.subFolder = subFolder;
	}

	public Date getCreateDate() {
		return createDate;
	}

	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public String getGmailAccount() {
		return gmailAccount;
	}

	public void setGmailAccount(String gmailAccount) {
		this.gmailAccount = gmailAccount;
	}

	public String getGmailPassword() {
		return gmailPassword;
	}

	public void setGmailPassword(String gmailPassword) {
		this.gmailPassword = gmailPassword;
	}

	public String getGoogleAnalyticsId() {
		return googleAnalyticsId;
	}

	public void setGoogleAnalyticsId(String googleAnalyticsId) {
		this.googleAnalyticsId = googleAnalyticsId;
	}

	public Integer getGoogleAnalyticsVersion() {
		return googleAnalyticsVersion;
	}

	public void setGoogleAnalyticsVersion(Integer googleAnalyticsVersion) {
		this.googleAnalyticsVersion = googleAnalyticsVersion;
	}

	public String getGaSessionToken() {
		return gaSessionToken;
	}

	public void setGaSessionToken(String gaSessionToken) {
		this.gaSessionToken = gaSessionToken;
	}

	public Integer getTargeturlLimit() {
		return targeturlLimit;
	}

	public void setTargeturlLimit(Integer targeturlLimit) {
		this.targeturlLimit = targeturlLimit;
	}

	public Integer getPartnerurlLimit() {
		return partnerurlLimit;
	}

	public void setPartnerurlLimit(Integer partnerurlLimit) {
		this.partnerurlLimit = partnerurlLimit;
	}

	public String getSubscription() {
		return subscription;
	}

	public void setSubscription(String subscription) {
		this.subscription = subscription;
	}

	public Integer getDateUnit() {
		return dateUnit;
	}

	public void setDateUnit(Integer dateUnit) {
		this.dateUnit = dateUnit;
	}

	public Integer getComparisonPeriod() {
		return comparisonPeriod;
	}

	public void setComparisonPeriod(Integer comparisonPeriod) {
		this.comparisonPeriod = comparisonPeriod;
	}

	public Integer getShowGoogleWebmasterPartnerUrl() {
		return showGoogleWebmasterPartnerUrl;
	}

	public void setShowGoogleWebmasterPartnerUrl(Integer showGoogleWebmasterPartnerUrl) {
		this.showGoogleWebmasterPartnerUrl = showGoogleWebmasterPartnerUrl;
	}

	public Integer getEcommerceConversion() {
		return ecommerceConversion;
	}

	public void setEcommerceConversion(Integer ecommerceConversion) {
		this.ecommerceConversion = ecommerceConversion;
	}

	public Integer getMinHistorySumEntrances() {
		return minHistorySumEntrances;
	}

	public void setMinHistorySumEntrances(Integer minHistorySumEntrances) {
		this.minHistorySumEntrances = minHistorySumEntrances;
	}

	public Integer getUpWarningValue() {
		return upWarningValue;
	}

	public void setUpWarningValue(Integer upWarningValue) {
		this.upWarningValue = upWarningValue;
	}

	public String getYahooAppid() {
		return yahooAppid;
	}

	public void setYahooAppid(String yahooAppid) {
		this.yahooAppid = yahooAppid;
	}

	public String getGaDefaultPage() {
		return gaDefaultPage;
	}

	public void setGaDefaultPage(String gaDefaultPage) {
		this.gaDefaultPage = gaDefaultPage;
	}

	public Date getImportAnalyticsCsvDate() {
		return importAnalyticsCsvDate;
	}

	public void setImportAnalyticsCsvDate(Date importAnalyticsCsvDate) {
		this.importAnalyticsCsvDate = importAnalyticsCsvDate;
	}

	public Integer getGoal1_conversion() {
		return goal1_conversion;
	}

	public void setGoal1_conversion(Integer goal1Conversion) {
		goal1_conversion = goal1Conversion;
	}

	public Integer getGoal2_conversion() {
		return goal2_conversion;
	}

	public void setGoal2_conversion(Integer goal2Conversion) {
		goal2_conversion = goal2Conversion;
	}

	public Integer getGoal4_conversion() {
		return goal4_conversion;
	}

	public void setGoal4_conversion(Integer goal4Conversion) {
		goal4_conversion = goal4Conversion;
	}

	public Integer getGoal3_conversion() {
		return goal3_conversion;
	}

	public void setGoal3_conversion(Integer goal3Conversion) {
		goal3_conversion = goal3Conversion;
	}

	public Integer getGoal5_conversion() {
		return goal5_conversion;
	}

	public void setGoal5_conversion(Integer goal5Conversion) {
		goal5_conversion = goal5Conversion;
	}

	public Integer getGoal6_conversion() {
		return goal6_conversion;
	}

	public void setGoal6_conversion(Integer goal6Conversion) {
		goal6_conversion = goal6Conversion;
	}

	public Integer getGoal7_conversion() {
		return goal7_conversion;
	}

	public void setGoal7_conversion(Integer goal7Conversion) {
		goal7_conversion = goal7Conversion;
	}

	public Integer getGoal8_conversion() {
		return goal8_conversion;
	}

	public void setGoal8_conversion(Integer goal8Conversion) {
		goal8_conversion = goal8Conversion;
	}

	public Integer getGoal9_conversion() {
		return goal9_conversion;
	}

	public void setGoal9_conversion(Integer goal9Conversion) {
		goal9_conversion = goal9Conversion;
	}

	public Integer getGoal10_conversion() {
		return goal10_conversion;
	}

	public void setGoal10_conversion(Integer goal10Conversion) {
		goal10_conversion = goal10Conversion;
	}

	public String getSearchEngine() {
		return searchEngine;
	}

	public void setSearchEngine(String searchEngine) {
		this.searchEngine = searchEngine;
	}

	public String getSearchEngineCountry() {
		return searchEngineCountry;
	}

	public void setSearchEngineCountry(String searchEngineCountry) {
		this.searchEngineCountry = searchEngineCountry;
	}

	public String getLanguage() {
		return language;
	}

	public void setLanguage(String language) {
		this.language = language;
	}

	public Integer getStartDayOfWeek() {
		return startDayOfWeek;
	}

	public void setStartDayOfWeek(Integer startDayOfWeek) {
		this.startDayOfWeek = startDayOfWeek;
	}

	public Integer getProtocol() {
		return protocol;
	}

	public void setProtocol(Integer protocol) {
		this.protocol = protocol;
	}

	public Integer getRankFrom() {
		return rankFrom;
	}

	public void setRankFrom(Integer rankFrom) {
		this.rankFrom = rankFrom;
	}

	public Integer getAssociateUrlRank() {
		return associateUrlRank;
	}

	public void setAssociateUrlRank(Integer associateUrlRank) {
		this.associateUrlRank = associateUrlRank;
	}

	public boolean isAssociatedUrlOnly() {
		if (getAssociateUrlRank() == null || getAssociateUrlRank().intValue() == 0) {
			return false;
		}
		return associateUrlRank == ASSOCIATE_URL_RANK_YES;
	}

	public boolean isBroadMatch() {
		if (getExactMatch() == null) {
			// default is Broad
			return true;
		}
		int matchFlag = NumberUtils.toInt(getExactMatch());
		return matchFlag == MATCH_BROAD;
	}

	public boolean isLinkRatingActive() {
		if (getLinkRating() == null) {
			// default is inactive
			return false;
		}
		return getLinkRating().shortValue() > 0;
	}

	public String getDomainProtocol() {
		if (protocol == null || protocol.intValue() == 0) {
			return "http";
		} else if (protocol.intValue() == 1) {
			return "https";
		}
		return "http";
	}

	public boolean isScoreKeywordActive() {
		if (scoreKeyword != null && scoreKeyword.intValue() > 0) {
			return true;
		}
		return false;
	}

	public boolean isScoreTargeturlActive() {
		if (scoreTargeturl != null && scoreTargeturl.intValue() > 0) {
			return true;
		}
		return false;
	}

	public boolean isScoreKeywordTargeturlActive() {
		if (scoreKeywordTargeturl != null && scoreKeywordTargeturl.intValue() > 0) {
			return true;
		}
		return false;
	}

	public boolean isEnableEcommerceConversion() {
		if (ecommerceConversion != null && ecommerceConversion.intValue() > 0) {
			return true;
		}
		if (goal1_conversion != null && goal1_conversion.intValue() > 0) {
			return true;
		}
		if (goal2_conversion != null && goal2_conversion.intValue() > 0) {
			return true;
		}
		if (goal3_conversion != null && goal3_conversion.intValue() > 0) {
			return true;
		}
		if (goal4_conversion != null && goal4_conversion.intValue() > 0) {
			return true;
		}
		return false;
	}

	@Override
	public String toString() {
		return "OwnDomainEntity{" + "id=" + id + ", domain='" + domain + '\'' + '}';
	}

	public Date getLastMajesticDomainProcessDate() {
		return lastMajesticDomainProcessDate;
	}

	public void setLastMajesticDomainProcessDate(Date lastMajesticDomainProcessDate) {
		this.lastMajesticDomainProcessDate = lastMajesticDomainProcessDate;
	}

	public Date getLastMajesticUrlProcessDate() {
		return lastMajesticUrlProcessDate;
	}

	public void setLastMajesticUrlProcessDate(Date lastMajesticUrlProcessDate) {
		this.lastMajesticUrlProcessDate = lastMajesticUrlProcessDate;
	}

	public String getSolrUrl() {
		return solrUrl;
	}

	public void setSolrUrl(String solrUrl) {
		this.solrUrl = solrUrl;
	}

	public Integer getMobileDomainFlg() {
		return mobileDomainFlg;
	}

	public void setMobileDomainFlg(Integer mobileDomainFlg) {
		this.mobileDomainFlg = mobileDomainFlg;
	}

	public boolean isMobileDomain() {
		if (getMobileDomainFlg() != null && getMobileDomainFlg().intValue() == 1) {
			return true;
		}
		return false;
	}

	public boolean isSeoMozActive() {
		if (StringUtils.isBlank(getMozAccessId())) {
			return false;
		}
		if (StringUtils.isBlank(getMozSecretKey())) {
			return false;
		}
		return true;
	}

	public String getBuzzstreamConsumerKey() {
		return buzzstreamConsumerKey;
	}

	public void setBuzzstreamConsumerKey(String buzzstreamConsumerKey) {
		this.buzzstreamConsumerKey = buzzstreamConsumerKey;
	}

	public String getBuzzstreamConsumerSecret() {
		return buzzstreamConsumerSecret;
	}

	public void setBuzzstreamConsumerSecret(String buzzstreamConsumerSecret) {
		this.buzzstreamConsumerSecret = buzzstreamConsumerSecret;
	}

	public boolean isAvgRankCalculatingInclude101() {
		if (getAvg_rank_include101() == null) {
			return false;
		}
		return getAvg_rank_include101().intValue() > 0;
	}

	public Integer getUpdateKeywordRankTrendHistory() {
		return updateKeywordRankTrendHistory;
	}

	public void setUpdateKeywordRankTrendHistory(Integer updateKeywordRankTrendHistory) {
		this.updateKeywordRankTrendHistory = updateKeywordRankTrendHistory;
	}

	public Integer getManagedLinksCount() {
		return managedLinksCount;
	}

	public void setManagedLinksCount(Integer managedLinksCount) {
		this.managedLinksCount = managedLinksCount;
	}

	// https://www.wrike.com/open.htm?id=14229227
	public String getPrimarySearchEngine() {
		if (StringUtils.isBlank(searchEngineCountry)) {
			return "www.google.com";
		}
		if (StringUtils.equalsIgnoreCase("FR", searchEngineCountry)) {
			return "www.google.fr";
		}
		if (StringUtils.equalsIgnoreCase("UK", searchEngineCountry)) {
			return "www.google.co.uk";
		}
		if (StringUtils.equalsIgnoreCase("AU", searchEngineCountry)) {
			return "www.google.com.au";
		}
		if (StringUtils.equalsIgnoreCase("CA", searchEngineCountry)) {
			return "www.google.ca";
		}
		if (StringUtils.equalsIgnoreCase("IT", searchEngineCountry)) {
			return "www.google.it";
		}
		if (StringUtils.equalsIgnoreCase("CN", searchEngineCountry)) {
			if (StringUtils.containsIgnoreCase(searchEngine, "baidu")) {
				return "www.baidu.com";
			} else {
				return "www.google.com.hk";
			}
		}
		return "www.google.com";
	}

	public String getUrlCrawlParameters() {
		return urlCrawlParameters;
	}

	public void setUrlCrawlParameters(String urlCrawlParameters) {
		this.urlCrawlParameters = urlCrawlParameters;
	}

	public Integer getEnableOwnGaTable() {
		return enableOwnGaTable;
	}

	public void setEnableOwnGaTable(Integer enableOwnGaTable) {
		this.enableOwnGaTable = enableOwnGaTable;
	}

	public String getDomainDisplayName() {
		String displayName = domain;
		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "CN") && StringUtils.endsWith(searchEngine, ".hk")) {
			displayName = domain + " (HK)";
		} else {
			displayName = domain + " (" + searchEngineCountry + ")";
		}

		if (StringUtils.isNotBlank(friendlyName)) {
			displayName = displayName + " - " + friendlyName;
		}
		return displayName;
	}
	
	public boolean isWebRankDefault() {
	    if (getRankCalculation() != null && 
	            getRankCalculation() == 1) {
            return true;
        }
	    return false;
	}

	public String getPagespeedClientApikey() {
		return pagespeedClientApikey;
	}

	public void setPagespeedClientApikey(String pagespeedClientApikey) {
		this.pagespeedClientApikey = pagespeedClientApikey;
	}

	public boolean isWeeklyRankFrequency() {
		if (keywordRankFrequency != null) {
			return keywordRankFrequency == KEYWORD_RANK_FREQUENCY_WEEKLY;
		}
		return false;
	}

	public boolean isBiWeeklyRankFrequency() {
		if (keywordRankFrequency != null) {
			return keywordRankFrequency == KEYWORD_RANK_FREQUENCY_BI_WEEKLY;
		}
		return false;
	}

}
