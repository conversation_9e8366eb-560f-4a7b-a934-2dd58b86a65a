package seoclarity.backend.entity.clickhouse.internallink;

public class InternalLinkParserVO {
	
	private String follow_flg;
	private Integer domain_id_i;
	private String analyzed_url_s;
	private String source_domain;
	private String canonical_type;
	private String canonical_flg;
	private String anchor_text;
//	private String destination_domain;
//	private String destination_root_domain;
	private Long crawl_date_long;
	private String url;
	private String index_flg;
	private String archive_flg;
//	private String destination_url;
//	private String[] title_md5;
	private Integer popularity;
//	private String title;
	private String canonical;
//	private String source_root_domain;
	private String analyzed_url_flg_s;
	private String title_flg;
	private Integer crawl_request_log_id_i;
	private String canonical_string;
//	private String destination_folder_level_1;
//	private String destination_folder_level_2;
	private String folder_level_1;
	private String folder_level_2;
//	private String today;
	private Integer status;
	private String es_index_type;
	private Boolean indexable;
	private String redirect_final_url;
	
	private InternalLinkPagelinkVO[] page_link;


	public Integer getDomain_id_i() {
		return domain_id_i;
	}

	public void setDomain_id_i(Integer domain_id_i) {
		this.domain_id_i = domain_id_i;
	}

	public String getAnalyzed_url_s() {
		return analyzed_url_s;
	}

	public void setAnalyzed_url_s(String analyzed_url_s) {
		this.analyzed_url_s = analyzed_url_s;
	}

	public String getSource_domain() {
		return source_domain;
	}

	public void setSource_domain(String source_domain) {
		this.source_domain = source_domain;
	}

	public String getCanonical_type() {
		return canonical_type;
	}

	public void setCanonical_type(String canonical_type) {
		this.canonical_type = canonical_type;
	}

	public String getCanonical_flg() {
		return canonical_flg;
	}

	public void setCanonical_flg(String canonical_flg) {
		this.canonical_flg = canonical_flg;
	}

	public String getAnchor_text() {
		return anchor_text;
	}

	public void setAnchor_text(String anchor_text) {
		this.anchor_text = anchor_text;
	}

	public Long getCrawl_date_long() {
		return crawl_date_long;
	}

	public void setCrawl_date_long(Long crawl_date_long) {
		this.crawl_date_long = crawl_date_long;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public String getIndex_flg() {
		return index_flg;
	}

	public void setIndex_flg(String index_flg) {
		this.index_flg = index_flg;
	}

	public String getArchive_flg() {
		return archive_flg;
	}

	public void setArchive_flg(String archive_flg) {
		this.archive_flg = archive_flg;
	}

	public Integer getPopularity() {
		return popularity;
	}

	public void setPopularity(Integer popularity) {
		this.popularity = popularity;
	}

	public String getCanonical() {
		return canonical;
	}

	public void setCanonical(String canonical) {
		this.canonical = canonical;
	}

	public String getAnalyzed_url_flg_s() {
		return analyzed_url_flg_s;
	}

	public void setAnalyzed_url_flg_s(String analyzed_url_flg_s) {
		this.analyzed_url_flg_s = analyzed_url_flg_s;
	}

	public String getTitle_flg() {
		return title_flg;
	}

	public void setTitle_flg(String title_flg) {
		this.title_flg = title_flg;
	}

	public String getCanonical_string() {
		return canonical_string;
	}

	public void setCanonical_string(String canonical_string) {
		this.canonical_string = canonical_string;
	}

	public String getFolder_level_1() {
		return folder_level_1;
	}

	public void setFolder_level_1(String folder_level_1) {
		this.folder_level_1 = folder_level_1;
	}

	public String getFolder_level_2() {
		return folder_level_2;
	}

	public void setFolder_level_2(String folder_level_2) {
		this.folder_level_2 = folder_level_2;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public String getFollow_flg() {
		return follow_flg;
	}

	public void setFollow_flg(String follow_flg) {
		this.follow_flg = follow_flg;
	}

	public Integer getCrawl_request_log_id_i() {
		return crawl_request_log_id_i;
	}

	public void setCrawl_request_log_id_i(Integer crawl_request_log_id_i) {
		this.crawl_request_log_id_i = crawl_request_log_id_i;
	}

	public InternalLinkPagelinkVO[] getPage_link() {
		return page_link;
	}

	public void setPage_link(InternalLinkPagelinkVO[] page_link) {
		this.page_link = page_link;
	}

	public String getEs_index_type() {
		return es_index_type;
	}

	public void setEs_index_type(String es_index_type) {
		this.es_index_type = es_index_type;
	}

	public Boolean getIndexable() {
		return indexable;
	}

	public void setIndexable(Boolean indexable) {
		this.indexable = indexable;
	}

	public String getRedirect_final_url() {
		return redirect_final_url;
	}

	public void setRedirect_final_url(String redirect_final_url) {
		this.redirect_final_url = redirect_final_url;
	}


}
