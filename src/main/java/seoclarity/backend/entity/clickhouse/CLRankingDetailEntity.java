package seoclarity.backend.entity.clickhouse;

import org.apache.commons.lang.StringUtils;

import java.util.List;

public class CLRankingDetailEntity {

	private Long keywordRankcheckId;
	private String keywordName;
	private Integer rankDate;
	private Integer ownDomainId;
	private Integer engine;
	private Integer language;
	private Integer locationId;
	private String domainReverse;
	private String rootDomainReverse;
	private Integer trueRank;
	private Integer webRank;
	private Integer hrd;
	private Integer count;
	private Integer rank;
	private Long avgSearchVolume;
	private Float cpc;

	private String uri;
	private String url;
	private String urlhash;
	private Integer protocol;
	private Integer type;
	private Integer hrdis;
	private Integer hrrd;
	private Integer tagId;
	private Integer competitorId;

	private String label;
	private String meta;
	private String date;
	private String domain;
	private String country;
	private String searchEngine;

	private Double estdTraffic;
	private String answerBoxFlg;
	private String rating;
	private String ratingNumber;
	private String llFlg;
	private String peopleAlsoAskFlg;
	private String imgFlg;
	private String newsFlg;
	private String videoFlg;
	private String tagNames;
	private Integer subRank;
	private String typeName;
    private String rankingDate;


    private String subRankUrl;
    private Integer subRankProtocol;
    private String cityName;
    private String additionalAb;
    private String googleRecommend;
    private String additionalQuestions;
    private String jobLink;
    private String appList;
    private String refineBy;
    private String ampFlg;
    private String appFlg;
    private String ppcFlg;
    private String commercialFlg;
    private String flightSearchFlg;
    private String researchFlg;
    private String social_in_kg;
    private String pla_flg;
    private String knog_flg;
    private String notRealSearchVolume;
    private String topPpcCnt;
    private String answerboxUrl;
    private String questions;
    private String subRankLabel;

    private String subDomainRev;
    private String subRootDomainRev;

    private Integer fps;
	// eg: 2,3
    private String typeArray;
	// eg: 22,1
    private String rankArray;
    private String[] dateArray;

	private String qaFlg;
	private String stockFlg;
	private String priceFlg;

	private Integer cnt;
	private String device;

	private String locationName;

	private Float shareOfVoice;
	private String plpUrl;

	private Integer googleProductsInTop3;
	private Integer uniqueAmazonOccurrence;

	private String tagNameList;

	private String tagName;

	private Double avgRank;

	private Float shareOfMarket;

	private String freeProductListingFlag;
	private String jobFlg;
	private String productFlg;
	private String shoppingFlg;
	private Integer youtubeRank;
	private String videoId;

	private Integer rankingType;
	private Integer urlType;

	private Integer questionCount;
	private String createDate;

	private List<String> tagList;

	public String getQuestions() {
		return questions;
	}

	public void setQuestions(String questions) {
		this.questions = questions;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public Integer getHrdis() {
		return hrdis;
	}

	public void setHrdis(Integer hrdis) {
		this.hrdis = hrdis;
	}

	public String getUri() {
		return uri;
	}

	public void setUri(String uri) {
		this.uri = uri;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public Integer getProtocol() {
		return protocol;
	}

	public void setProtocol(Integer protocol) {
		this.protocol = protocol;
	}

	public Long getAvgSearchVolume() {
		return avgSearchVolume;
	}

	public void setAvgSearchVolume(Long avgSearchVolume) {
		this.avgSearchVolume = avgSearchVolume;
	}

	public String getKeywordName() {
		return keywordName;
	}

	public void setKeywordName(String keywordName) {
		this.keywordName = keywordName;
	}

	public Integer getRankDate() {
		return rankDate;
	}

	public void setRankDate(Integer rankDate) {
		this.rankDate = rankDate;
	}

	public Integer getEngine() {
		return engine;
	}

	public void setEngine(Integer engine) {
		this.engine = engine;
	}

	public Integer getLanguage() {
		return language;
	}

	public void setLanguage(Integer language) {
		this.language = language;
	}

	public String getDomainReverse() {
		return domainReverse;
	}

	public void setDomainReverse(String domainReverse) {
		this.domainReverse = domainReverse;
	}

	public Integer getTrueRank() {
		return trueRank;
	}

	public void setTrueRank(Integer trueRank) {
		this.trueRank = trueRank;
	}

	public Integer getWebRank() {
		return webRank;
	}

	public void setWebRank(Integer webRank) {
		this.webRank = webRank;
	}

	public Integer getHrd() {
		return hrd;
	}

	public void setHrd(Integer hrd) {
		this.hrd = hrd;
	}

	public Integer getCount() {
		return count;
	}

	public void setCount(Integer count) {
		this.count = count;
	}

	public Integer getTagId() {
		return tagId;
	}

	public void setTagId(Integer tagId) {
		this.tagId = tagId;
	}

	public Integer getCompetitorId() {
		return competitorId;
	}

	public void setCompetitorId(Integer competitorId) {
		this.competitorId = competitorId;
	}


	public String getLabel() {
		if (StringUtils.isBlank(label) || StringUtils.equals(label, "-") || StringUtils.equalsIgnoreCase(label, "null")) {
			return "";
		}
		return label;
	}

	public void setLabel(String label) {
		this.label = label;
	}

	public String getMeta() {
		if (StringUtils.isBlank(meta) || StringUtils.equals(meta, "-") || StringUtils.equalsIgnoreCase(meta, "null")) {
			return "";
		}
		return meta;
	}

	public void setMeta(String meta) {
		this.meta = meta;
	}

	public String getDate() {
		return date;
	}

	public void setDate(String date) {
		this.date = date;
	}

	public String getDomain() {
		return domain;
	}

	public void setDomain(String domain) {
		this.domain = domain;
	}

	public String getCountry() {
		return country;
	}

	public void setCountry(String country) {
		this.country = country;
	}

	public String getSearchEngine() {
		return searchEngine;
	}

	public void setSearchEngine(String searchEngine) {
		this.searchEngine = searchEngine;
	}


	public Double getEstdTraffic() {
		return estdTraffic;
	}

	public void setEstdTraffic(Double estdTraffic) {
		this.estdTraffic = estdTraffic;
	}


	public String getAnswerBoxFlg() {
		return answerBoxFlg;
	}

	public void setAnswerBoxFlg(String answerBoxFlg) {
		this.answerBoxFlg = answerBoxFlg;
	}

	public String getRatingNumber() {
		return ratingNumber;
	}

	public void setRatingNumber(String ratingNumber) {
		this.ratingNumber = ratingNumber;
	}

	public String getLlFlg() {
		return llFlg;
	}

	public void setLlFlg(String llFlg) {
		this.llFlg = llFlg;
	}

	public String getPeopleAlsoAskFlg() {
		return peopleAlsoAskFlg;
	}

	public void setPeopleAlsoAskFlg(String peopleAlsoAskFlg) {
		this.peopleAlsoAskFlg = peopleAlsoAskFlg;
	}

	public String getImgFlg() {
		return imgFlg;
	}

	public void setImgFlg(String imgFlg) {
		this.imgFlg = imgFlg;
	}

	public String getNewsFlg() {
		return newsFlg;
	}

	public void setNewsFlg(String newsFlg) {
		this.newsFlg = newsFlg;
	}

	public Integer getLocationId() {
		if (locationId == null) {
			return 0;
		}
		return locationId;
	}

	public void setLocationId(Integer locationId) {
		this.locationId = locationId;
	}

	public Float getCpc() {
		return cpc;
	}

	public void setCpc(Float cpc) {
		this.cpc = cpc;
	}

    public String getTagNames() {
        return tagNames;
    }

    public void setTagNames(String tagNames) {
        this.tagNames = tagNames;
    }

	public Integer getSubRank() {
		return subRank;
	}

	public void setSubRank(Integer subRank) {
		this.subRank = subRank;
	}

	public Long getKeywordRankcheckId() {
		return keywordRankcheckId;
	}

	public void setKeywordRankcheckId(Long keywordRankcheckId) {
		this.keywordRankcheckId = keywordRankcheckId;
	}

	public String getTypeName() {
		return typeName;
	}

	public void setTypeName(String typeName) {
		this.typeName = typeName;
	}

	public String getUrlhash() {
		return urlhash;
	}

	public void setUrlhash(String urlhash) {
		this.urlhash = urlhash;
	}

    public String getRankingDate() {
        return rankingDate;
    }

    public void setRankingDate(String rankingDate) {
        this.rankingDate = rankingDate;
    }

	public Integer getOwnDomainId() {
		return ownDomainId;
	}

	public void setOwnDomainId(Integer ownDomainId) {
		this.ownDomainId = ownDomainId;
	}

	public String getDomainName() {
		if (StringUtils.isBlank(domainReverse)) {
			return "";
		}
		String domainname = StringUtils.reverseDelimited(domainReverse, '.');
		return domainname;
	}

	public Integer getFps() {
		return fps;
	}

	public void setFps(Integer fps) {
		this.fps = fps;
	}

	public String getTypeArray() {
		return typeArray;
	}

	public void setTypeArray(String typeArray) {
		this.typeArray = typeArray;
	}

	public String getRankArray() {
		return rankArray;
	}

	public void setRankArray(String rankArray) {
		this.rankArray = rankArray;
	}

	public String getRootDomainReverse() {
		return rootDomainReverse;
	}

	public void setRootDomainReverse(String rootDomainReverse) {
		this.rootDomainReverse = rootDomainReverse;
	}

	public Integer getHrrd() {
		return hrrd;
	}

	public void setHrrd(Integer hrrd) {
		this.hrrd = hrrd;
	}


	public String getSubRankUrl() {
		return subRankUrl;
	}

	public void setSubRankUrl(String subRankUrl) {
		this.subRankUrl = subRankUrl;
	}

	public Integer getSubRankProtocol() {
		return subRankProtocol;
	}

	public void setSubRankProtocol(Integer subRankProtocol) {
		this.subRankProtocol = subRankProtocol;
	}


	public String getCityName() {
		return cityName;
	}

	public void setCityName(String cityName) {
		this.cityName = cityName;
	}

	public String getAdditionalAb() {
		if (StringUtils.isBlank(additionalAb) || StringUtils.equals(additionalAb, "-") || StringUtils.equalsIgnoreCase(additionalAb, "null")) {
			return "";
		}
		return additionalAb;
	}

	public void setAdditionalAb(String additionalAb) {
		this.additionalAb = additionalAb;
	}

	public String getGoogleRecommend() {
		if (StringUtils.equals(googleRecommend, "-")) {
			return "";
		}
		return googleRecommend;
	}

	public void setGoogleRecommend(String googleRecommend) {
		this.googleRecommend = googleRecommend;
	}

	public String getAdditionalQuestions() {
		if (StringUtils.equals(additionalQuestions, "-")) {
			return "";
		}
		return additionalQuestions;
	}

	public void setAdditionalQuestions(String additionalQuestions) {
		this.additionalQuestions = additionalQuestions;
	}

	public String getAppList() {
		return appList;
	}

	public void setAppList(String appList) {
		this.appList = appList;
	}

	public String getRefineBy() {
		if (StringUtils.equals(refineBy, "-")) {
			return "";
		}
		return refineBy;
	}

	public void setRefineBy(String refineBy) {
		this.refineBy = refineBy;
	}


	public String getAmpFlg() {
		return ampFlg;
	}

	public void setAmpFlg(String ampFlg) {
		this.ampFlg = ampFlg;
	}

	public String getAppFlg() {
		return appFlg;
	}

	public void setAppFlg(String appFlg) {
		this.appFlg = appFlg;
	}

	public String getPpcFlg() {
		return ppcFlg;
	}

	public void setPpcFlg(String ppcFlg) {
		this.ppcFlg = ppcFlg;
	}

	public String getCommercialFlg() {
		return commercialFlg;
	}

	public void setCommercialFlg(String commercialFlg) {
		this.commercialFlg = commercialFlg;
	}

	public String getFlightSearchFlg() {
		return flightSearchFlg;
	}

	public void setFlightSearchFlg(String flightSearchFlg) {
		this.flightSearchFlg = flightSearchFlg;
	}

	public String getResearchFlg() {
		return researchFlg;
	}

	public void setResearchFlg(String researchFlg) {
		this.researchFlg = researchFlg;
	}

	public String getSocial_in_kg() {
		return social_in_kg;
	}

	public void setSocial_in_kg(String social_in_kg) {
		this.social_in_kg = social_in_kg;
	}

	public String getPla_flg() {
		return pla_flg;
	}

	public void setPla_flg(String pla_flg) {
		this.pla_flg = pla_flg;
	}

	public String getKnog_flg() {
		return knog_flg;
	}

	public void setKnog_flg(String knog_flg) {
		this.knog_flg = knog_flg;
	}

	public String getTopPpcCnt() {
		return topPpcCnt;
	}

	public void setTopPpcCnt(String topPpcCnt) {
		this.topPpcCnt = topPpcCnt;
	}

	public String getAnswerboxUrl() {
		if (StringUtils.isBlank(answerboxUrl) || StringUtils.equals(answerboxUrl, "-") || StringUtils.equalsIgnoreCase(answerboxUrl, "null")) {
			return "";
		}
		return answerboxUrl;
	}

	public void setAnswerboxUrl(String answerboxUrl) {
		this.answerboxUrl = answerboxUrl;
	}

	public String getJobLink() {
		if (StringUtils.isBlank(jobLink) || StringUtils.equals(jobLink, "-") || StringUtils.equalsIgnoreCase(jobLink, "null")) {
			return "";
		}
		return jobLink;
	}

	public void setJobLink(String jobLink) {
		this.jobLink = jobLink;
	}

	public String getSubDomainRev() {
		return subDomainRev;
	}

	public void setSubDomainRev(String subDomainRev) {
		this.subDomainRev = subDomainRev;
	}

	public String getSubRootDomainRev() {
		return subRootDomainRev;
	}

	public void setSubRootDomainRev(String subRootDomainRev) {
		this.subRootDomainRev = subRootDomainRev;
	}

	public String getSubRankLabel() {
		if (StringUtils.equals(subRankLabel, "-")) {
			return "";
		}
		return subRankLabel;
	}

	public void setSubRankLabel(String subRankLabel) {
		this.subRankLabel = subRankLabel;
	}

	public Integer getRank() {
		return rank;
	}

	public void setRank(Integer rank) {
		this.rank = rank;
	}

	public String getRating() {
		return rating;
	}

	public void setRating(String rating) {
		this.rating = rating;
	}

	public String getNotRealSearchVolume() {
		return notRealSearchVolume;
	}

	public void setNotRealSearchVolume(String notRealSearchVolume) {
		this.notRealSearchVolume = notRealSearchVolume;
	}

	public String getVideoFlg() {
		return videoFlg;
	}

	public void setVideoFlg(String videoFlg) {
		this.videoFlg = videoFlg;
	}

	public String getQaFlg() {
		return qaFlg;
	}

	public void setQaFlg(String qaFlg) {
		this.qaFlg = qaFlg;
	}

	public String getStockFlg() {
		return stockFlg;
	}

	public void setStockFlg(String stockFlg) {
		this.stockFlg = stockFlg;
	}

	public String getPriceFlg() {
		return priceFlg;
	}

	public void setPriceFlg(String priceFlg) {
		this.priceFlg = priceFlg;
	}

	public String getLocationName() {
		return locationName;
	}

	public void setLocationName(String locationName) {
		this.locationName = locationName;
	}

	public Integer getCnt() {
		return cnt;
	}

	public void setCnt(Integer cnt) {
		this.cnt = cnt;
	}


	public Float getShareOfVoice() {
		return shareOfVoice;
	}

	public void setShareOfVoice(Float shareOfVoice) {
		this.shareOfVoice = shareOfVoice;
	}

	public String getDevice() {
		return device;
	}

	public void setDevice(String device) {
		this.device = device;
	}

	public String getPlpUrl() {
		return plpUrl;
	}

	public void setPlpUrl(String plpUrl) {
		this.plpUrl = plpUrl;
	}

	public Integer getGoogleProductsInTop3() {
		return googleProductsInTop3;
	}

	public void setGoogleProductsInTop3(Integer googleProductsInTop3) {
		this.googleProductsInTop3 = googleProductsInTop3;
	}

	public Integer getUniqueAmazonOccurrence() {
		return uniqueAmazonOccurrence;
	}

	public void setUniqueAmazonOccurrence(Integer uniqueAmazonOccurrence) {
		this.uniqueAmazonOccurrence = uniqueAmazonOccurrence;
	}

	public String getTagNameList() {
		return tagNameList;
	}

	public void setTagNameList(String tagNameList) {
		this.tagNameList = tagNameList;
	}

	public String getTagName() {
		return tagName;
	}

	public void setTagName(String tagName) {
		this.tagName = tagName;
	}

	public Double getAvgRank() {
		return avgRank;
	}

	public void setAvgRank(Double avgRank) {
		this.avgRank = avgRank;
	}

	public Float getShareOfMarket() {
		return shareOfMarket;
	}

	public void setShareOfMarket(Float shareOfMarket) {
		this.shareOfMarket = shareOfMarket;
	}

	public String[] getDateArray() {
		return dateArray;
	}

	public void setDateArray(String[] dateArray) {
		this.dateArray = dateArray;
	}

	public String getFreeProductListingFlag() {
		return freeProductListingFlag;
	}

	public void setFreeProductListingFlag(String freeProductListingFlag) {
		this.freeProductListingFlag = freeProductListingFlag;
	}

	public String getJobFlg() {
		return jobFlg;
	}

	public void setJobFlg(String jobFlg) {
		this.jobFlg = jobFlg;
	}

	public String getProductFlg() {
		return productFlg;
	}

	public void setProductFlg(String productFlg) {
		this.productFlg = productFlg;
	}

	public String getShoppingFlg() {
		return shoppingFlg;
	}

	public void setShoppingFlg(String shoppingFlg) {
		this.shoppingFlg = shoppingFlg;
	}

	public Integer getYoutubeRank() {
		return youtubeRank;
	}

	public void setYoutubeRank(Integer youtubeRank) {
		this.youtubeRank = youtubeRank;
	}

	public String getVideoId() {
		return videoId;
	}

	public void setVideoId(String videoId) {
		this.videoId = videoId;
	}

	public Integer getRankingType() {
		return rankingType;
	}

	public void setRankingType(Integer rankingType) {
		this.rankingType = rankingType;
	}

	public Integer getUrlType() {
		return urlType;
	}

	public void setUrlType(Integer urlType) {
		this.urlType = urlType;
	}

	public Integer getQuestionCount() {
		return questionCount;
	}

	public void setQuestionCount(Integer questionCount) {
		this.questionCount = questionCount;
	}

	public String getCreateDate() {
		return createDate;
	}

	public void setCreateDate(String createDate) {
		this.createDate = createDate;
	}

	public List<String> getTagList() {
		return tagList;
	}

	public void setTagList(List<String> tagList) {
		this.tagList = tagList;
	}
}
