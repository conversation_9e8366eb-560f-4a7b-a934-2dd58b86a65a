package seoclarity.backend.entity.clickhouse;

import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;

import com.google.common.collect.ImmutableMap;

import seoclarity.backend.entity.KeywordRankEntityVO;
import seoclarity.backend.entity.actonia.extract.rankingextractjson.RankExtractJsonSerpSubRanks;

public class CLRankingDetailEntity {
	
	public static Integer SUBRANK_FLAG = 1;
	
	public static final Map<Integer, String> SERP_UNIVERSAL_NAME_MAP = ImmutableMap.<Integer, String>builder()
			.put(KeywordRankEntityVO.TYPE_IMGAGE, "Image")
			.put(KeywordRankEntityVO.TYPE_INTERESTING_FINDS, "Interesting Finds")
			.put(KeywordRankEntityVO.TYPE_ADDRESS, "Address")
			.put(KeywordRankEntityVO.TYPE_VIDEO, "Video")
			.put(KeywordRankEntityVO.TYPE_NEWS, "News")
			.put(KeywordRankEntityVO.TYPE_SHOPPING, "Shopping (Ads)")
			.put(KeywordRankEntityVO.TYPE_LOCALLISTING, "Local Listing")
			.put(KeywordRankEntityVO.TYPE_TWITTER, "Twitter")
			.put(KeywordRankEntityVO.TYPE_JOB_URL, "Job Pack")
			.put(KeywordRankEntityVO.TYPE_KNOWLEDGE, "Knowledge Graph")
			.put(KeywordRankEntityVO.TYPE_FLIGHTS, "Flight")
			.put(KeywordRankEntityVO.TYPE_FINANCE, "Google Finance")
			.put(KeywordRankEntityVO.TYPE_EVENT, "Google Events")
			.put(KeywordRankEntityVO.TYPE_FINDRESULTSON, "Find Results On")
			.put(KeywordRankEntityVO.TYPE_SHORT_VIDEO, "Google Short Video")
			.put(KeywordRankEntityVO.TYPE_SALARY_ESTIMATES, "Google Salary Estimate")
			.put(KeywordRankEntityVO.TYPE_NEAR_YOU, "Google Near You")
			.put(KeywordRankEntityVO.TYPE_COMPARISON, "Comparison")
			.put(KeywordRankEntityVO.TYPE_HOTEL, "Hotel")
			.put(KeywordRankEntityVO.TYPE_POPULARDESTINATIONS, "Popular Destinations")
			.put(KeywordRankEntityVO.TYPE_DISCUSSIONSANDFORUMS, "Discussions And Forums")
			.put(KeywordRankEntityVO.TYPE_TOPSIGHTS, "Top Sights")
			.put(KeywordRankEntityVO.TYE_RECIPES, "Recipes")
			.put(KeywordRankEntityVO.TYPE_ANSWERBOX, "Answer Box")
			.put(KeywordRankEntityVO.TYPE_POPULAR_PRODUCTS, "Products")
			.put(KeywordRankEntityVO.TYPE_FROM_SOURCES_ACROSS_THE_WEB, "From Sources Across The Web")
			.put(KeywordRankEntityVO.TYPE_AIO, "AI Overview")
			.build();

	private static String EMPTY_VALUE = "-";
	
	private Long keywordRankcheckId;
	private String keywordName;
	private Integer rankDate;
	private Integer ownDomainId;
	private Integer engine;
	private Integer language;
	private Integer locationId;
	private String domainReverse;
	private String rootDomainReverse;
	private Integer trueRank;
	private Integer webRank;
	private Integer hrd;
	private Integer count;
	private Integer rank;
	private Long avgSearchVolume;
	private Float cpc;

	private String uri;
	private String url;
	private String urlhash;
	private Integer protocol;
	private Integer type;
	private Integer hrdis;
	private Integer hrrd;
	private Integer tagId;
	private Integer competitorId;

	private String label;
	private String meta;
	private String date;
	private String domain;
	private String country;
	private String searchEngine;
	
	private String appbarDisplayKeywords;
	private String appbarSearchedKeywords;
	
	private Integer isSubRank;

	private Double estdTraffic;
	private String answerBoxFlg;
	private String rating;
	private String ratingNumber;
	private String llFlg;
	private String peopleAlsoAskFlg;
	private String imgFlg;
	private String newsFlg;
	private String videoFlg;
	private String tagNames;
	private Integer subRank;
	private String typeName;
    private String rankingDate;

    private String subRankUrl;
    private Integer subRankProtocol;
    private String cityName;
    private String additionalAb;
    private String googleRecommend;
    private String additionalQuestions;
    private String jobLink;
    private String appList;
    private String refineBy;
    private String ampFlg;
    private String appFlg;
    private String ppcFlg;
    private String commercialFlg;
    private String flightSearchFlg;
    private String researchFlg;
    private String social_in_kg;
    private String pla_flg;
    private String knog_flg;
    private String notRealSearchVolume;
    private String topPpcCnt;
    private String answerboxUrl;
    private String questions;
    private String subRankLabel;

    private String subDomainRev;
    private String subRootDomainRev;

    private Integer fps;
	// eg: 2,3
    private String typeArray;
	// eg: 22,1
    private String rankArray;
    private String[] dateArray;

	private String qaFlg;
	private String stockFlg;
	private String priceFlg;

	private Integer cnt;
	private String device;

	private String locationName;

	private Float shareOfVoice;
	private String plpUrl;

	private Integer googleProductsInTop3;
	private Integer uniqueAmazonOccurrence;

	private String tagNameList;

	private String tagName;

	private Double avgRank;

	private Float shareOfMarket;

	private String freeProductListingFlag;
	private String jobFlg;
	private String productFlg;
	private String shoppingFlg;
	private Integer youtubeRank;
	private String videoId;

	private Integer rankingType;
	private Integer urlType;

	private Integer questionCount;
	private String createDate;

	private List<String> tagList;
	
	private Integer trueDemand; // https://www.wrike.com/open.htm?id=1359315662
	
	private String thingsToKnow;

	private String subrankRating;
	private String subrankPrice;
	private String subrankLable;
	private String subrankRatingNumber;
	private String subrankBrand;
	private String subrankTagName;

	private Float wtdAvgRank;

	private Integer totalCnt;
	private Integer noSubrankCnt;

	private String languageName;

	private String hotelFlag;
	private String fromsourcesacrossthewebFlag;
	private String findresultsonFlag;
	private String populardestinationsFlag;
	private String aioFlag;
	private String popularRecipesFlag;
	private String popularStoreFlag;
	private String discussionsandforumsFlag;
	private String buyingguideFlag;
	private String peopleAlsoAskTitle;
	private String peopleAlsoAskUrl;
	private String refineByTitle;
	private List<String> refineByDetails;
	private String priceNumber;
	private String couponFlag;
	private String subRankMeta;

	private List<String> serpFilterButtonText;
	private List<String> serpFilterKeywords;
	private List<String> peopleAlsoAskList;

	private String serpFilterButtonTextStr;
	private String serpFilterKeywordsStr;

	private Double trueRankAvgRank;
	private Double trueRankEstdTraffic;
	private Float trueRankWtdAvgRank;
	private Float trueRankShareOfVoice;
	private Float trueRankShareOfMarket;
	private String totalResults;

	private List<List<Integer>> tagIdHierarchy;
	private List<String> plpUrlList;

	private Integer visualRank;
	private Integer pixelDepth;

	private Map<String, List<RankExtractJsonSerpSubRanks>> brandMap;
	private Map<String, List<RankExtractJsonSerpSubRanks>> nonbrandMap;

	// https://www.wrike.com/open.htm?id=1500665126
	private List<String> relatedSearches;

	private String keywordHash;

	private String plaPrice;
	private String plaRating;
	private String plaLabel;
	private String plaUrl;
	private String plaDomain;
	private String plaPos;
	private String plaRank;
	private String plaBrandName;

	private String adsDisplayUrl;
	private String adsPos;
	private String adsRank;
	private String adsAnchorText;
	private String adsDesc;
	private String adsSiteLinkFlag;
	private String adsDomain;

	public String getQuestions() {
		return questions;
	}

	public void setQuestions(String questions) {
		this.questions = questions;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public Integer getHrdis() {
		return hrdis;
	}

	public void setHrdis(Integer hrdis) {
		this.hrdis = hrdis;
	}

	public String getUri() {
		return uri;
	}

	public void setUri(String uri) {
		this.uri = uri;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public Integer getProtocol() {
		return protocol;
	}

	public void setProtocol(Integer protocol) {
		this.protocol = protocol;
	}

	public Long getAvgSearchVolume() {
		return avgSearchVolume;
	}

	public void setAvgSearchVolume(Long avgSearchVolume) {
		this.avgSearchVolume = avgSearchVolume;
	}

	public String getKeywordName() {
		return keywordName;
	}

	public void setKeywordName(String keywordName) {
		this.keywordName = keywordName;
	}

	public Integer getRankDate() {
		return rankDate;
	}

	public void setRankDate(Integer rankDate) {
		this.rankDate = rankDate;
	}

	public Integer getEngine() {
		return engine;
	}

	public void setEngine(Integer engine) {
		this.engine = engine;
	}

	public Integer getLanguage() {
		return language;
	}

	public void setLanguage(Integer language) {
		this.language = language;
	}

	public String getDomainReverse() {
		return domainReverse;
	}

	public void setDomainReverse(String domainReverse) {
		this.domainReverse = domainReverse;
	}

	public Integer getTrueRank() {
		return trueRank;
	}

	public void setTrueRank(Integer trueRank) {
		this.trueRank = trueRank;
	}

	public Integer getWebRank() {
		return webRank;
	}

	public void setWebRank(Integer webRank) {
		this.webRank = webRank;
	}

	public Integer getHrd() {
		return hrd;
	}

	public void setHrd(Integer hrd) {
		this.hrd = hrd;
	}

	public Integer getCount() {
		return count;
	}

	public void setCount(Integer count) {
		this.count = count;
	}

	public Integer getTagId() {
		return tagId;
	}

	public void setTagId(Integer tagId) {
		this.tagId = tagId;
	}

	public Integer getCompetitorId() {
		return competitorId;
	}

	public void setCompetitorId(Integer competitorId) {
		this.competitorId = competitorId;
	}


	public String getLabel() {
		if (StringUtils.isBlank(label) || StringUtils.equals(label, "-") || StringUtils.equalsIgnoreCase(label, "null")) {
			return null;
		}
		return label;
	}

	public void setLabel(String label) {
		this.label = label;
	}

	public String getMeta() {
		if (StringUtils.isBlank(meta) || StringUtils.equals(meta, "-") || StringUtils.equalsIgnoreCase(meta, "null")) {
			return null;
		}
		return meta;
	}

	public void setMeta(String meta) {
		this.meta = meta;
	}

	public String getDate() {
		return date;
	}

	public void setDate(String date) {
		this.date = date;
	}

	public String getDomain() {
		return domain;
	}

	public void setDomain(String domain) {
		this.domain = domain;
	}

	public String getCountry() {
		return country;
	}

	public void setCountry(String country) {
		this.country = country;
	}

	public String getSearchEngine() {
		return searchEngine;
	}

	public void setSearchEngine(String searchEngine) {
		this.searchEngine = searchEngine;
	}


	public Double getEstdTraffic() {
		return estdTraffic;
	}

	public void setEstdTraffic(Double estdTraffic) {
		this.estdTraffic = estdTraffic;
	}


	public String getAnswerBoxFlg() {
		return answerBoxFlg;
	}

	public void setAnswerBoxFlg(String answerBoxFlg) {
		this.answerBoxFlg = answerBoxFlg;
	}

	public String getRatingNumber() {
		if (StringUtils.equalsIgnoreCase(ratingNumber, EMPTY_VALUE)) {
			return null;
		}
		return ratingNumber;
	}

	public void setRatingNumber(String ratingNumber) {
		this.ratingNumber = ratingNumber;
	}

	public String getLlFlg() {
		return llFlg;
	}

	public void setLlFlg(String llFlg) {
		this.llFlg = llFlg;
	}

	public String getPeopleAlsoAskFlg() {
		return peopleAlsoAskFlg;
	}

	public void setPeopleAlsoAskFlg(String peopleAlsoAskFlg) {
		this.peopleAlsoAskFlg = peopleAlsoAskFlg;
	}

	public String getImgFlg() {
		return imgFlg;
	}

	public void setImgFlg(String imgFlg) {
		this.imgFlg = imgFlg;
	}

	public String getNewsFlg() {
		return newsFlg;
	}

	public void setNewsFlg(String newsFlg) {
		this.newsFlg = newsFlg;
	}

	public Integer getLocationId() {
		if (locationId == null) {
			return 0;
		}
		return locationId;
	}

	public void setLocationId(Integer locationId) {
		this.locationId = locationId;
	}

	public Float getCpc() {
		return cpc;
	}

	public void setCpc(Float cpc) {
		this.cpc = cpc;
	}

    public String getTagNames() {
        return tagNames;
    }

    public void setTagNames(String tagNames) {
        this.tagNames = tagNames;
    }

	public Integer getSubRank() {
		return subRank;
	}

	public void setSubRank(Integer subRank) {
		this.subRank = subRank;
	}

	public Long getKeywordRankcheckId() {
		return keywordRankcheckId;
	}

	public void setKeywordRankcheckId(Long keywordRankcheckId) {
		this.keywordRankcheckId = keywordRankcheckId;
	}

	public String getTypeName() {
		return typeName;
	}

	public void setTypeName(String typeName) {
		this.typeName = typeName;
	}

	public String getUrlhash() {
		return urlhash;
	}

	public void setUrlhash(String urlhash) {
		this.urlhash = urlhash;
	}

    public String getRankingDate() {
        return rankingDate;
    }

    public void setRankingDate(String rankingDate) {
        this.rankingDate = rankingDate;
    }

	public Integer getOwnDomainId() {
		return ownDomainId;
	}

	public void setOwnDomainId(Integer ownDomainId) {
		this.ownDomainId = ownDomainId;
	}

	public String getDomainName() {
		if (StringUtils.isBlank(domainReverse)) {
			return "";
		}
		String domainname = StringUtils.reverseDelimited(domainReverse, '.');
		return domainname;
	}

	public Integer getFps() {
		return fps;
	}

	public void setFps(Integer fps) {
		this.fps = fps;
	}

	public String getTypeArray() {
		return typeArray;
	}

	public void setTypeArray(String typeArray) {
		this.typeArray = typeArray;
	}

	public String getRankArray() {
		return rankArray;
	}

	public void setRankArray(String rankArray) {
		this.rankArray = rankArray;
	}

	public String getRootDomainReverse() {
		return rootDomainReverse;
	}

	public void setRootDomainReverse(String rootDomainReverse) {
		this.rootDomainReverse = rootDomainReverse;
	}

	public Integer getHrrd() {
		return hrrd;
	}

	public void setHrrd(Integer hrrd) {
		this.hrrd = hrrd;
	}


	public String getSubRankUrl() {
		return subRankUrl;
	}

	public void setSubRankUrl(String subRankUrl) {
		this.subRankUrl = subRankUrl;
	}

	public Integer getSubRankProtocol() {
		return subRankProtocol;
	}

	public void setSubRankProtocol(Integer subRankProtocol) {
		this.subRankProtocol = subRankProtocol;
	}


	public String getCityName() {
		return cityName;
	}

	public void setCityName(String cityName) {
		this.cityName = cityName;
	}

	public String getAdditionalAb() {
		if (StringUtils.isBlank(additionalAb) || StringUtils.equals(additionalAb, "-") || StringUtils.equalsIgnoreCase(additionalAb, "null")) {
			return "";
		}
		return additionalAb;
	}

	public void setAdditionalAb(String additionalAb) {
		this.additionalAb = additionalAb;
	}

	public String getGoogleRecommend() {
		if (StringUtils.equals(googleRecommend, "-")) {
			return "";
		}
		return googleRecommend;
	}

	public void setGoogleRecommend(String googleRecommend) {
		this.googleRecommend = googleRecommend;
	}

	public String getAdditionalQuestions() {
		if (StringUtils.equals(additionalQuestions, "-")) {
			return "";
		}
		return additionalQuestions;
	}

	public void setAdditionalQuestions(String additionalQuestions) {
		this.additionalQuestions = additionalQuestions;
	}

	public String getAppList() {
		return appList;
	}

	public void setAppList(String appList) {
		this.appList = appList;
	}

	public String getRefineBy() {
		if (StringUtils.equals(refineBy, "-")) {
			return "";
		}
		return refineBy;
	}

	public void setRefineBy(String refineBy) {
		this.refineBy = refineBy;
	}


	public String getAmpFlg() {
		return ampFlg;
	}

	public void setAmpFlg(String ampFlg) {
		this.ampFlg = ampFlg;
	}

	public String getAppFlg() {
		return appFlg;
	}

	public void setAppFlg(String appFlg) {
		this.appFlg = appFlg;
	}

	public String getPpcFlg() {
		return ppcFlg;
	}

	public void setPpcFlg(String ppcFlg) {
		this.ppcFlg = ppcFlg;
	}

	public String getCommercialFlg() {
		return commercialFlg;
	}

	public void setCommercialFlg(String commercialFlg) {
		this.commercialFlg = commercialFlg;
	}

	public String getFlightSearchFlg() {
		return flightSearchFlg;
	}

	public void setFlightSearchFlg(String flightSearchFlg) {
		this.flightSearchFlg = flightSearchFlg;
	}

	public String getResearchFlg() {
		return researchFlg;
	}

	public void setResearchFlg(String researchFlg) {
		this.researchFlg = researchFlg;
	}

	public String getSocial_in_kg() {
		return social_in_kg;
	}

	public void setSocial_in_kg(String social_in_kg) {
		this.social_in_kg = social_in_kg;
	}

	public String getPla_flg() {
		return pla_flg;
	}

	public void setPla_flg(String pla_flg) {
		this.pla_flg = pla_flg;
	}

	public String getKnog_flg() {
		return knog_flg;
	}

	public void setKnog_flg(String knog_flg) {
		this.knog_flg = knog_flg;
	}

	public String getTopPpcCnt() {
		return topPpcCnt;
	}

	public void setTopPpcCnt(String topPpcCnt) {
		this.topPpcCnt = topPpcCnt;
	}

	public String getAnswerboxUrl() {
		if (StringUtils.isBlank(answerboxUrl) || StringUtils.equals(answerboxUrl, "-") || StringUtils.equalsIgnoreCase(answerboxUrl, "null")) {
			return "";
		}
		return answerboxUrl;
	}

	public void setAnswerboxUrl(String answerboxUrl) {
		this.answerboxUrl = answerboxUrl;
	}

	public String getJobLink() {
		if (StringUtils.isBlank(jobLink) || StringUtils.equals(jobLink, "-") || StringUtils.equalsIgnoreCase(jobLink, "null")) {
			return "";
		}
		return jobLink;
	}

	public void setJobLink(String jobLink) {
		this.jobLink = jobLink;
	}

	public String getSubDomainRev() {
		return subDomainRev;
	}

	public void setSubDomainRev(String subDomainRev) {
		this.subDomainRev = subDomainRev;
	}

	public String getSubRootDomainRev() {
		return subRootDomainRev;
	}

	public void setSubRootDomainRev(String subRootDomainRev) {
		this.subRootDomainRev = subRootDomainRev;
	}

	public String getSubRankLabel() {
		if (StringUtils.equals(subRankLabel, "-")) {
			return "";
		}
		return subRankLabel;
	}

	public void setSubRankLabel(String subRankLabel) {
		this.subRankLabel = subRankLabel;
	}

	public Integer getRank() {
		return rank;
	}

	public void setRank(Integer rank) {
		this.rank = rank;
	}

	public String getRating() {
		return rating;
	}

	public void setRating(String rating) {
		this.rating = rating;
	}

	public String getNotRealSearchVolume() {
		return notRealSearchVolume;
	}

	public void setNotRealSearchVolume(String notRealSearchVolume) {
		this.notRealSearchVolume = notRealSearchVolume;
	}

	public String getVideoFlg() {
		return videoFlg;
	}

	public void setVideoFlg(String videoFlg) {
		this.videoFlg = videoFlg;
	}

	public String getQaFlg() {
		return qaFlg;
	}

	public void setQaFlg(String qaFlg) {
		this.qaFlg = qaFlg;
	}

	public String getStockFlg() {
		return stockFlg;
	}

	public void setStockFlg(String stockFlg) {
		this.stockFlg = stockFlg;
	}

	public String getPriceFlg() {
		return priceFlg;
	}

	public void setPriceFlg(String priceFlg) {
		this.priceFlg = priceFlg;
	}

	public String getLocationName() {
		return locationName;
	}

	public void setLocationName(String locationName) {
		this.locationName = locationName;
	}

	public Integer getCnt() {
		return cnt;
	}

	public void setCnt(Integer cnt) {
		this.cnt = cnt;
	}


	public Float getShareOfVoice() {
		return shareOfVoice;
	}

	public void setShareOfVoice(Float shareOfVoice) {
		this.shareOfVoice = shareOfVoice;
	}

	public String getDevice() {
		return device;
	}

	public void setDevice(String device) {
		this.device = device;
	}

	public String getPlpUrl() {
		return plpUrl;
	}

	public void setPlpUrl(String plpUrl) {
		this.plpUrl = plpUrl;
	}

	public Integer getGoogleProductsInTop3() {
		return googleProductsInTop3;
	}

	public void setGoogleProductsInTop3(Integer googleProductsInTop3) {
		this.googleProductsInTop3 = googleProductsInTop3;
	}

	public Integer getUniqueAmazonOccurrence() {
		return uniqueAmazonOccurrence;
	}

	public void setUniqueAmazonOccurrence(Integer uniqueAmazonOccurrence) {
		this.uniqueAmazonOccurrence = uniqueAmazonOccurrence;
	}

	public String getTagNameList() {
		return tagNameList;
	}

	public void setTagNameList(String tagNameList) {
		this.tagNameList = tagNameList;
	}

	public String getTagName() {
		return tagName;
	}

	public void setTagName(String tagName) {
		this.tagName = tagName;
	}

	public Double getAvgRank() {
		return avgRank;
	}

	public void setAvgRank(Double avgRank) {
		this.avgRank = avgRank;
	}

	public Float getShareOfMarket() {
		return shareOfMarket;
	}

	public void setShareOfMarket(Float shareOfMarket) {
		this.shareOfMarket = shareOfMarket;
	}

	public String[] getDateArray() {
		return dateArray;
	}

	public void setDateArray(String[] dateArray) {
		this.dateArray = dateArray;
	}

	public String getFreeProductListingFlag() {
		return freeProductListingFlag;
	}

	public void setFreeProductListingFlag(String freeProductListingFlag) {
		this.freeProductListingFlag = freeProductListingFlag;
	}

	public String getJobFlg() {
		return jobFlg;
	}

	public void setJobFlg(String jobFlg) {
		this.jobFlg = jobFlg;
	}

	public String getProductFlg() {
		return productFlg;
	}

	public void setProductFlg(String productFlg) {
		this.productFlg = productFlg;
	}

	public String getShoppingFlg() {
		return shoppingFlg;
	}

	public void setShoppingFlg(String shoppingFlg) {
		this.shoppingFlg = shoppingFlg;
	}

	public Integer getYoutubeRank() {
		return youtubeRank;
	}

	public void setYoutubeRank(Integer youtubeRank) {
		this.youtubeRank = youtubeRank;
	}

	public String getVideoId() {
		return videoId;
	}

	public void setVideoId(String videoId) {
		this.videoId = videoId;
	}

	public Integer getRankingType() {
		return rankingType;
	}

	public void setRankingType(Integer rankingType) {
		this.rankingType = rankingType;
	}

	public Integer getUrlType() {
		return urlType;
	}

	public void setUrlType(Integer urlType) {
		this.urlType = urlType;
	}

	public Integer getQuestionCount() {
		return questionCount;
	}

	public void setQuestionCount(Integer questionCount) {
		this.questionCount = questionCount;
	}

	public String getCreateDate() {
		return createDate;
	}

	public void setCreateDate(String createDate) {
		this.createDate = createDate;
	}

	public List<String> getTagList() {
		return tagList;
	}

	public void setTagList(List<String> tagList) {
		this.tagList = tagList;
	}
	
	public Integer getTrueDemand() {
		return trueDemand;
	}

	public void setTrueDemand(Integer trueDemand) {
		this.trueDemand = trueDemand;
	}

	public String getSubrankRating() {
		return subrankRating;
	}

	public void setSubrankRating(String subrankRating) {
		this.subrankRating = subrankRating;
	}

	public String getSubrankPrice() {
		return subrankPrice;
	}

	public void setSubrankPrice(String subrankPrice) {
		this.subrankPrice = subrankPrice;
	}

	public String getSubrankLable() {
		return subrankLable;
	}

	public void setSubrankLable(String subrankLable) {
		this.subrankLable = subrankLable;
	}

	public String getSubrankRatingNumber() {
		return subrankRatingNumber;
	}

	public void setSubrankRatingNumber(String subrankRatingNumber) {
		this.subrankRatingNumber = subrankRatingNumber;
	}

	public String getSubrankBrand() {
		return subrankBrand;
	}

	public void setSubrankBrand(String subrankBrand) {
		this.subrankBrand = subrankBrand;
	}

	public Float getWtdAvgRank() {
		return wtdAvgRank;
	}

	public void setWtdAvgRank(Float wtdAvgRank) {
		this.wtdAvgRank = wtdAvgRank;
	}

	public Integer getTotalCnt() {
		return totalCnt;
	}

	public void setTotalCnt(Integer totalCnt) {
		this.totalCnt = totalCnt;
	}

	public Integer getNoSubrankCnt() {
		return noSubrankCnt;
	}

	public void setNoSubrankCnt(Integer noSubrankCnt) {
		this.noSubrankCnt = noSubrankCnt;
	}

	public String getLanguageName() {
		return languageName;
	}

	public void setLanguageName(String languageName) {
		this.languageName = languageName;
	}

	public String getHotelFlag() {
		return hotelFlag;
	}

	public void setHotelFlag(String hotelFlag) {
		this.hotelFlag = hotelFlag;
	}

	public String getFromsourcesacrossthewebFlag() {
		return fromsourcesacrossthewebFlag;
	}

	public void setFromsourcesacrossthewebFlag(String fromsourcesacrossthewebFlag) {
		this.fromsourcesacrossthewebFlag = fromsourcesacrossthewebFlag;
	}

	public String getFindresultsonFlag() {
		return findresultsonFlag;
	}

	public void setFindresultsonFlag(String findresultsonFlag) {
		this.findresultsonFlag = findresultsonFlag;
	}

	public String getPopulardestinationsFlag() {
		return populardestinationsFlag;
	}

	public void setPopulardestinationsFlag(String populardestinationsFlag) {
		this.populardestinationsFlag = populardestinationsFlag;
	}

	public String getAioFlag() {
		return aioFlag;
	}

	public void setAioFlag(String aioFlag) {
		this.aioFlag = aioFlag;
	}

	public String getPopularRecipesFlag() {
		return popularRecipesFlag;
	}

	public void setPopularRecipesFlag(String popularRecipesFlag) {
		this.popularRecipesFlag = popularRecipesFlag;
	}

	public String getPopularStoreFlag() {
		return popularStoreFlag;
	}

	public void setPopularStoreFlag(String popularStoreFlag) {
		this.popularStoreFlag = popularStoreFlag;
	}

	public String getDiscussionsandforumsFlag() {
		return discussionsandforumsFlag;
	}

	public void setDiscussionsandforumsFlag(String discussionsandforumsFlag) {
		this.discussionsandforumsFlag = discussionsandforumsFlag;
	}

	public String getBuyingguideFlag() {
		return buyingguideFlag;
	}

	public void setBuyingguideFlag(String buyingguideFlag) {
		this.buyingguideFlag = buyingguideFlag;
	}

	public String getPeopleAlsoAskTitle() {
		return peopleAlsoAskTitle;
	}

	public void setPeopleAlsoAskTitle(String peopleAlsoAskTitle) {
		this.peopleAlsoAskTitle = peopleAlsoAskTitle;
	}

	public String getPeopleAlsoAskUrl() {
		return peopleAlsoAskUrl;
	}

	public void setPeopleAlsoAskUrl(String peopleAlsoAskUrl) {
		this.peopleAlsoAskUrl = peopleAlsoAskUrl;
	}

	public String getRefineByTitle() {
		return refineByTitle;
	}

	public void setRefineByTitle(String refineByTitle) {
		this.refineByTitle = refineByTitle;
	}

	public List<String> getRefineByDetails() {
		return refineByDetails;
	}

	public void setRefineByDetails(List<String> refineByDetails) {
		this.refineByDetails = refineByDetails;
	}

	public String getPriceNumber() {
		if (StringUtils.equalsIgnoreCase(priceNumber, EMPTY_VALUE)) {
			return null;
		}
		return priceNumber;
	}

	public void setPriceNumber(String priceNumber) {
		this.priceNumber = priceNumber;
	}

	public String getCouponFlag() {
		return couponFlag;
	}

	public void setCouponFlag(String couponFlag) {
		this.couponFlag = couponFlag;
	}

	public String getSubRankMeta() {
		return subRankMeta;
	}

	public void setSubRankMeta(String subRankMeta) {
		this.subRankMeta = subRankMeta;
	}

	public List<String> getSerpFilterButtonText() {
		return serpFilterButtonText;
	}

	public void setSerpFilterButtonText(List<String> serpFilterButtonText) {
		this.serpFilterButtonText = serpFilterButtonText;
	}

	public List<String> getSerpFilterKeywords() {
		return serpFilterKeywords;
	}

	public void setSerpFilterKeywords(List<String> serpFilterKeywords) {
		this.serpFilterKeywords = serpFilterKeywords;
	}

	public List<String> getPeopleAlsoAskList() {
		return peopleAlsoAskList;
	}

	public void setPeopleAlsoAskList(List<String> peopleAlsoAskList) {
		this.peopleAlsoAskList = peopleAlsoAskList;
	}

	public String getSerpFilterButtonTextStr() {
		return serpFilterButtonTextStr;
	}

	public void setSerpFilterButtonTextStr(String serpFilterButtonTextStr) {
		this.serpFilterButtonTextStr = serpFilterButtonTextStr;
	}

	public String getSerpFilterKeywordsStr() {
		return serpFilterKeywordsStr;
	}

	public void setSerpFilterKeywordsStr(String serpFilterKeywordsStr) {
		this.serpFilterKeywordsStr = serpFilterKeywordsStr;
	}
	
	public String getAppbarDisplayKeywords() {
		return appbarDisplayKeywords;
	}

	public void setAppbarDisplayKeywords(String appbarDisplayKeywords) {
		this.appbarDisplayKeywords = appbarDisplayKeywords;
	}

	public String getAppbarSearchedKeywords() {
		return appbarSearchedKeywords;
	}

	public void setAppbarSearchedKeywords(String appbarSearchedKeywords) {
		this.appbarSearchedKeywords = appbarSearchedKeywords;
	}

	public Integer getIsSubRank() {
		return isSubRank;
	}

	public void setIsSubRank(Integer isSubRank) {
		this.isSubRank = isSubRank;
	}

	public Double getTrueRankAvgRank() {
		return trueRankAvgRank;
	}

	public void setTrueRankAvgRank(Double trueRankAvgRank) {
		this.trueRankAvgRank = trueRankAvgRank;
	}

	public Double getTrueRankEstdTraffic() {
		return trueRankEstdTraffic;
	}

	public void setTrueRankEstdTraffic(Double trueRankEstdTraffic) {
		this.trueRankEstdTraffic = trueRankEstdTraffic;
	}

	public Float getTrueRankWtdAvgRank() {
		return trueRankWtdAvgRank;
	}

	public void setTrueRankWtdAvgRank(Float trueRankWtdAvgRank) {
		this.trueRankWtdAvgRank = trueRankWtdAvgRank;
	}

	public Float getTrueRankShareOfVoice() {
		return trueRankShareOfVoice;
	}

	public void setTrueRankShareOfVoice(Float trueRankShareOfVoice) {
		this.trueRankShareOfVoice = trueRankShareOfVoice;
	}

	public Float getTrueRankShareOfMarket() {
		return trueRankShareOfMarket;
	}

	public void setTrueRankShareOfMarket(Float trueRankShareOfMarket) {
		this.trueRankShareOfMarket = trueRankShareOfMarket;
	}

	public String getThingsToKnow() {
		return thingsToKnow;
	}

	public void setThingsToKnow(String thingsToKnow) {
		this.thingsToKnow = thingsToKnow;
	}

	public String getTotalResults() {
		return totalResults;
	}

	public void setTotalResults(String totalResults) {
		this.totalResults = totalResults;
	}

	public List<List<Integer>> getTagIdHierarchy() {
		return tagIdHierarchy;
	}

	public void setTagIdHierarchy(List<List<Integer>> tagIdHierarchy) {
		this.tagIdHierarchy = tagIdHierarchy;
	}

	public List<String> getPlpUrlList() {
		return plpUrlList;
	}

	public void setPlpUrlList(List<String> plpUrlList) {
		this.plpUrlList = plpUrlList;
	}

	public Map<String, List<RankExtractJsonSerpSubRanks>> getBrandMap() {
		return brandMap;
	}

	public void setBrandMap(Map<String, List<RankExtractJsonSerpSubRanks>> brandMap) {
		this.brandMap = brandMap;
	}

	public Map<String, List<RankExtractJsonSerpSubRanks>> getNonbrandMap() {
		return nonbrandMap;
	}

	public void setNonbrandMap(Map<String, List<RankExtractJsonSerpSubRanks>> nonbrandMap) {
		this.nonbrandMap = nonbrandMap;
	}

	public Integer getVisualRank() {
		return visualRank;
	}

	public void setVisualRank(Integer visualRank) {
		this.visualRank = visualRank;
	}

	public Integer getPixelDepth() {
		return pixelDepth;
	}

	public void setPixelDepth(Integer pixelDepth) {
		this.pixelDepth = pixelDepth;
	}

	public String getSubrankTagName() {
		return subrankTagName;
	}

	public void setSubrankTagName(String subrankTagName) {
		this.subrankTagName = subrankTagName;
	}

	public List<String> getRelatedSearches() {
		return relatedSearches;
	}

	public void setRelatedSearches(List<String> relatedSearches) {
		this.relatedSearches = relatedSearches;
	}

	public String getKeywordHash() {
		return keywordHash;
	}

	public void setKeywordHash(String keywordHash) {
		this.keywordHash = keywordHash;
	}

	public String getPlaPrice() {
		return plaPrice;
	}

	public void setPlaPrice(String plaPrice) {
		this.plaPrice = plaPrice;
	}

	public String getPlaRating() {
		return plaRating;
	}

	public void setPlaRating(String plaRating) {
		this.plaRating = plaRating;
	}

	public String getPlaLabel() {
		return plaLabel;
	}

	public void setPlaLabel(String plaLabel) {
		this.plaLabel = plaLabel;
	}

	public String getPlaUrl() {
		return plaUrl;
	}

	public void setPlaUrl(String plaUrl) {
		this.plaUrl = plaUrl;
	}

	public String getPlaDomain() {
		return plaDomain;
	}

	public void setPlaDomain(String plaDomain) {
		this.plaDomain = plaDomain;
	}

	public String getPlaPos() {
		return plaPos;
	}

	public void setPlaPos(String plaPos) {
		this.plaPos = plaPos;
	}

	public String getPlaRank() {
		return plaRank;
	}

	public void setPlaRank(String plaRank) {
		this.plaRank = plaRank;
	}

	public String getPlaBrandName() {
		return plaBrandName;
	}

	public void setPlaBrandName(String plaBrandName) {
		this.plaBrandName = plaBrandName;
	}

	public String getAdsDisplayUrl() {
		return adsDisplayUrl;
	}

	public void setAdsDisplayUrl(String adsDisplayUrl) {
		this.adsDisplayUrl = adsDisplayUrl;
	}

	public String getAdsPos() {
		return adsPos;
	}

	public void setAdsPos(String adsPos) {
		this.adsPos = adsPos;
	}

	public String getAdsRank() {
		return adsRank;
	}

	public void setAdsRank(String adsRank) {
		this.adsRank = adsRank;
	}

	public String getAdsAnchorText() {
		return adsAnchorText;
	}

	public void setAdsAnchorText(String adsAnchorText) {
		this.adsAnchorText = adsAnchorText;
	}

	public String getAdsDesc() {
		return adsDesc;
	}

	public void setAdsDesc(String adsDesc) {
		this.adsDesc = adsDesc;
	}

	public String getAdsSiteLinkFlag() {
		return adsSiteLinkFlag;
	}

	public void setAdsSiteLinkFlag(String adsSiteLinkFlag) {
		this.adsSiteLinkFlag = adsSiteLinkFlag;
	}

	public String getAdsDomain() {
		return adsDomain;
	}

	public void setAdsDomain(String adsDomain) {
		this.adsDomain = adsDomain;
	}
}
