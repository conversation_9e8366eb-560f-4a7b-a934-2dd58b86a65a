package seoclarity.backend.service;

import com.alibaba.fastjson2.JSON;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.stereotype.Component;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.actonia.extract.ExtractScriptConfigDAO;
import seoclarity.backend.dao.actonia.extract.ExtractScriptDetailDAO;
import seoclarity.backend.dao.actonia.extract.ExtractScriptInstanceDAO;
import seoclarity.backend.dao.mdbkeywordsuggest.RankQcInfoEntityDAO;
import seoclarity.backend.entity.actonia.DomainSearchEngineRelEntity;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.actonia.extract.*;
import seoclarity.backend.entity.actonia.extract.rankingextractjson.*;
import seoclarity.backend.entity.actoniamonitor.RankQcInfoEntity;
import seoclarity.backend.entity.clickhouse.CLRankingDetailEntity;
import seoclarity.backend.entity.extract.RankExtractFeatureRankVO;
import seoclarity.backend.utils.*;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.net.InetAddress;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

@Component
@CommonsLog
public class ExtractService {

    protected final static int AUTO_EXTRACT_NORMAL_GROUP_ID = 120;
    protected final static int QUERY_MAX_MISSING_DAY = 7;
    protected final static String PROJECT_NAME = "clarity-backend-scripts";
    private static final String BAD_CHAR = "�";
    @Resource
    private ExtractScriptConfigDAO extractScriptConfigDAO;
    @Resource
    private ExtractScriptInstanceDAO extractScriptInstanceDAO;
    @Resource
    private ExtractScriptDetailDAO extractScriptDetailDAO;
    @Resource
    private OwnDomainEntityDAO ownDomainEntityDAO;
    @Resource
    private ScKeywordRankManager scKeywordRankManager;
    @Resource
    private ZeptoMailSenderComponent zeptoMailSenderComponent;
    @Resource
    private RankQcInfoEntityDAO rankQcInfoEntityDAO;

    public List<ExtractScriptInstanceEntity> getNeedRunExtract(int category, String fullQulifiedClass,
                                                               String specialCategory, int frequency, Date processingDate) {

        log.info("=====getNeedRunExtract category:" + category + ",fullQulifiedClass:" + fullQulifiedClass + ",specialCategory:" + specialCategory + ",frequency:" + frequency + ",processingDate:" + processingDate);

        Calendar calendar = Calendar.getInstance();
        int currentHour = calendar.get(Calendar.HOUR_OF_DAY);

        List<ExtractScriptInstanceEntity> resultList = new ArrayList<>();
        try {
            ExtractScriptConfigEntity extractScriptConfigEntity =
                    extractScriptConfigDAO.getUniqueExtractScriptConfig(category, PROJECT_NAME, fullQulifiedClass, specialCategory, frequency);
            if (extractScriptConfigEntity == null) {
                log.warn("===config not exist!");
                return null;
            }

            int configId = extractScriptConfigEntity.getId();
            int defaultFrequency = extractScriptConfigEntity.getDefaultFrequency();
            int defaultStartDay = extractScriptConfigEntity.getDefaultStartDay();
            int defaultStartProcessHour = extractScriptConfigEntity.getDefaultStartProcessHour();
            int defaultExpectedProcessHour = extractScriptConfigEntity.getDefaultExpectedProcessHour();

            int processFrequency = extractScriptConfigEntity.getDefaultFrequency();
            int processStartDay = extractScriptConfigEntity.getDefaultStartDay();
            int processExpectedProcessHour = extractScriptConfigEntity.getDefaultExpectedProcessHour();

            List<ExtractScriptInstanceEntity> instanceList = extractScriptInstanceDAO.getInstanceByConfig(configId);
            if (CollectionUtils.isEmpty(instanceList)) {
                log.warn("===instance not exist configId:" + configId);
                return null;
            }

            Date yesterday = DateUtils.addDays(processingDate, -1);
            Date targetDate = processingDate;
            for (ExtractScriptInstanceEntity instance : instanceList) {

                int rankDate = FormatUtils.formatDateToYyyyMmDd(processingDate);

                List<ExtractScriptDetailEntity> instanceDetailList = new ArrayList<>();
                int instanceId = instance.getId();
                int domainId = instance.getOwnDomainId();
                OwnDomainEntity ownDomainEntity =ownDomainEntityDAO.getById(domainId);
                if(ownDomainEntity == null){
                    log.info("======domain suspend:" + domainId + ",instanceId:" + instanceId);
                    extractScriptInstanceDAO.updateSuspendDomain(instanceId);
                    String subject = "DISABLE suspend domain instance oid:" + domainId + ",instanceId:" + instanceId;
                    sendMailReport(subject, subject);
                    continue;
                }

                boolean isEnableExtractCurrentDay = instance.getEnableExtractCurrentDay();
                if (isEnableExtractCurrentDay) {
                    ExtractScriptDetailEntity extractScriptDetail = extractScriptDetailDAO.getUniqueExtractScriptDetail(configId, instanceId, rankDate);
                    if (extractScriptDetail != null && extractScriptDetail.getStatus() == ExtractScriptDetailEntity.STATUS_SUCCESS) {
                        log.info("===already extract successful , skip configId:" + configId + ",instanceId:" + instanceId + ",rankDate:" + rankDate);
                        continue;
                    } else if (extractScriptDetail != null) {
                        System.out.println("===rerun today:" + instanceId);
                        //check send email time
                        if (instance.getExpectedProcessHour() != null) {
                            processExpectedProcessHour = instance.getExpectedProcessHour();
                        }
                        boolean isExpectedProcessHour = currentHour >= processExpectedProcessHour ? true : false;
                        extractScriptDetail.setIsExpectedProcessHour(isExpectedProcessHour);
                        instanceDetailList.add(extractScriptDetail);

                        List<ExtractScriptDetailEntity> failedDayList = extractScriptDetailDAO.getFailedDayList(configId, instanceId);
                        if (CollectionUtils.isEmpty(failedDayList)) {

                            Integer maxSuccessDay = extractScriptDetailDAO.getLastSuccessDay(configId, instanceId);
                            if (maxSuccessDay != null) {
                                Date maxSuccessDate = DateUtils.addDays(FormatUtils.toDate(String.valueOf(maxSuccessDay), FormatUtils.DATE_FORMAT_YYYYMMDD), 1);
                                Date compareDate = FormatUtils.toDate(FormatUtils.formatDate(processingDate, FormatUtils.DATE_FORMAT_YYYYMMDD), FormatUtils.DATE_FORMAT_YYYYMMDD);

                                List<ExtractScriptDetailEntity> missingDetailList = insertForMissingDay(maxSuccessDate, compareDate, null, configId, instanceId);
                                if (CollectionUtils.isNotEmpty(missingDetailList)) {
                                    instanceDetailList.addAll(missingDetailList);
                                }

                            }

                            ExtractScriptDetailEntity extractScriptDetailEntity = checkTargetDayAndInsert(extractScriptConfigEntity, instance, processingDate, currentHour, frequency);
                            if (extractScriptDetailEntity == null) {
                                log.info("==not pass check configId:" + configId + ",instanceId:" + instanceId);
                            } else {
                                instanceDetailList.add(extractScriptDetailEntity);
                            }
                        } else {//process error date(auto rerun) skip check
                            for (ExtractScriptDetailEntity extractScriptDetailEntity : failedDayList) {
                                int errorTargetDate = extractScriptDetailEntity.getTargetDate();
                                if (rankDate == errorTargetDate) {
                                    continue;
                                }
                                System.out.println("===rerun error day:" + errorTargetDate);
                                instanceDetailList.addAll(failedDayList);
                            }


//                            Date maxFailedDay = DateUtils.addDays(FormatUtils.toDate(String.valueOf(failedDayList.get(failedDayList.size() - 1).getTargetDate()), FormatUtils.DATE_FORMAT_YYYYMMDD), 1);
//                            Date compareDate = FormatUtils.toDate(FormatUtils.formatDate(processingDate, FormatUtils.DATE_FORMAT_YYYYMMDD), FormatUtils.DATE_FORMAT_YYYYMMDD);
//
//                            List<Integer> successDayList = extractScriptDetailDAO.getSuccessDayList(configId, instanceId, FormatUtils.formatDateToYyyyMmDd(maxFailedDay));
//
//                            List<ExtractScriptDetailEntity> missingDetailList = insertForMissingDay(maxFailedDay, compareDate, successDayList, configId, instanceId);
//                            if (CollectionUtils.isNotEmpty(missingDetailList)) {
//                                instanceDetailList.addAll(missingDetailList);
//                            }
//
//                            ExtractScriptDetailEntity extractScriptDetailEntity = checkTargetDayAndInsert(extractScriptConfigEntity, instance, processingDate, currentHour, frequency);
//                            if (extractScriptDetailEntity == null) {
//                                log.info("==not pass check configId:" + configId + ",instanceId:" + instanceId);
//                            } else {
//                                instanceDetailList.add(extractScriptDetailEntity);
//                            }

                        }

                    } else {

                        List<ExtractScriptDetailEntity> failedDayList = extractScriptDetailDAO.getFailedDayList(configId, instanceId);
                        if (CollectionUtils.isEmpty(failedDayList)) {

                            Integer maxSuccessDay = extractScriptDetailDAO.getLastSuccessDay(configId, instanceId);
                            if (maxSuccessDay != null) {
                                Date maxSuccessDate = DateUtils.addDays(FormatUtils.toDate(String.valueOf(maxSuccessDay), FormatUtils.DATE_FORMAT_YYYYMMDD), 1);
                                Date compareDate = FormatUtils.toDate(FormatUtils.formatDate(processingDate, FormatUtils.DATE_FORMAT_YYYYMMDD), FormatUtils.DATE_FORMAT_YYYYMMDD);

                                List<ExtractScriptDetailEntity> missingDetailList = insertForMissingDay(maxSuccessDate, compareDate, null, configId, instanceId);
                                if (CollectionUtils.isNotEmpty(missingDetailList)) {
                                    instanceDetailList.addAll(missingDetailList);
                                }

                            }

                            ExtractScriptDetailEntity extractScriptDetailEntity = checkTargetDayAndInsert(extractScriptConfigEntity, instance, processingDate, currentHour, frequency);
                            if (extractScriptDetailEntity == null) {
                                log.info("==not pass check configId:" + configId + ",instanceId:" + instanceId);
                            } else {
                                instanceDetailList.add(extractScriptDetailEntity);
                            }


                        } else {//process error date(auto rerun) skip check
                            System.out.println("===rerun error day");
                            instanceDetailList.addAll(failedDayList);

                            Date maxFailedDay = DateUtils.addDays(FormatUtils.toDate(String.valueOf(failedDayList.get(failedDayList.size() - 1).getTargetDate()), FormatUtils.DATE_FORMAT_YYYYMMDD), 1);
                            Date compareDate = FormatUtils.toDate(FormatUtils.formatDate(processingDate, FormatUtils.DATE_FORMAT_YYYYMMDD), FormatUtils.DATE_FORMAT_YYYYMMDD);

                            List<Integer> successDayList = extractScriptDetailDAO.getSuccessDayList(configId, instanceId, FormatUtils.formatDateToYyyyMmDd(maxFailedDay));

                            List<ExtractScriptDetailEntity> missingDetailList = insertForMissingDay(maxFailedDay, compareDate, successDayList, configId, instanceId);
                            if (CollectionUtils.isNotEmpty(missingDetailList)) {
                                instanceDetailList.addAll(missingDetailList);
                            }

                            ExtractScriptDetailEntity extractScriptDetailEntity = checkTargetDayAndInsert(extractScriptConfigEntity, instance, processingDate, currentHour, frequency);
                            if (extractScriptDetailEntity == null) {
                                log.info("==not pass check configId:" + configId + ",instanceId:" + instanceId);
                            } else {
                                instanceDetailList.add(extractScriptDetailEntity);
                            }

                        }

                    }

                } else {

                    targetDate = yesterday;
                    calendar = Calendar.getInstance();
                    currentHour = calendar.get(Calendar.HOUR_OF_DAY);
                    rankDate = FormatUtils.formatDateToYyyyMmDd(targetDate);

                    ExtractScriptDetailEntity extractScriptDetail = extractScriptDetailDAO.getUniqueExtractScriptDetail(configId, instanceId, rankDate);
                    if (extractScriptDetail != null && extractScriptDetail.getStatus() == ExtractScriptDetailEntity.STATUS_SUCCESS) {
                        log.info("===already extract successful , skip configId:" + configId + ",instanceId:" + instanceId + ",rankDate:" + rankDate);
                        continue;
                    } else if (extractScriptDetail != null) {
                        //check send email time
                        if (instance.getExpectedProcessHour() != null) {
                            processExpectedProcessHour = instance.getExpectedProcessHour();
                        }
                        boolean isExpectedProcessHour = currentHour >= processExpectedProcessHour ? true : false;
                        extractScriptDetail.setIsExpectedProcessHour(isExpectedProcessHour);
                        instanceDetailList.add(extractScriptDetail);
                    } else {

                        List<ExtractScriptDetailEntity> failedDayList = extractScriptDetailDAO.getFailedDayList(configId, instanceId);
                        if (CollectionUtils.isEmpty(failedDayList)) {

                            Integer maxSuccessDay = extractScriptDetailDAO.getLastSuccessDay(configId, instanceId);
                            if (maxSuccessDay != null && frequency == ExtractScriptConfigEntity.FREQUENCY_DAILY) {
                                Date maxSuccessDate = DateUtils.addDays(FormatUtils.toDate(String.valueOf(maxSuccessDay), FormatUtils.DATE_FORMAT_YYYYMMDD), 1);
                                Date compareDate = FormatUtils.toDate(FormatUtils.formatDate(targetDate, FormatUtils.DATE_FORMAT_YYYYMMDD), FormatUtils.DATE_FORMAT_YYYYMMDD);

                                List<ExtractScriptDetailEntity> missingDetailList = insertForMissingDay(maxSuccessDate, compareDate, null, configId, instanceId);
                                if (CollectionUtils.isNotEmpty(missingDetailList)) {
                                    instanceDetailList.addAll(missingDetailList);
                                }

                            }

                            ExtractScriptDetailEntity extractScriptDetailEntity = checkTargetDayAndInsert(extractScriptConfigEntity, instance, targetDate, currentHour, frequency);
                            if (extractScriptDetailEntity == null) {
                                log.info("==not pass check configId:" + configId + ",instanceId:" + instanceId);
                            } else {
                                instanceDetailList.add(extractScriptDetailEntity);
                            }


                        } else {//process error date(auto rerun) skip check

                            instanceDetailList.addAll(failedDayList);
                            if (frequency == ExtractScriptConfigEntity.FREQUENCY_DAILY) {
                                Date maxFailedDay = DateUtils.addDays(FormatUtils.toDate(String.valueOf(failedDayList.get(failedDayList.size() - 1).getTargetDate()), FormatUtils.DATE_FORMAT_YYYYMMDD), 1);
                                Date compareDate = FormatUtils.toDate(FormatUtils.formatDate(targetDate, FormatUtils.DATE_FORMAT_YYYYMMDD), FormatUtils.DATE_FORMAT_YYYYMMDD);

                                List<Integer> successDayList = extractScriptDetailDAO.getSuccessDayList(configId, instanceId, FormatUtils.formatDateToYyyyMmDd(maxFailedDay));
                                List<ExtractScriptDetailEntity> missingDetailList = insertForMissingDay(maxFailedDay, compareDate, successDayList, configId, instanceId);
                                if (CollectionUtils.isNotEmpty(missingDetailList)) {
                                    instanceDetailList.addAll(missingDetailList);
                                }
                            }

                            //today
                            ExtractScriptDetailEntity extractScriptDetailEntity = checkTargetDayAndInsert(extractScriptConfigEntity, instance, targetDate, currentHour, frequency);
                            if (extractScriptDetailEntity == null) {
                                log.info("==not pass check configId:" + configId + ",instanceId:" + instanceId);
                            } else {
                                instanceDetailList.add(extractScriptDetailEntity);
                            }

                        }

                    }


                }

                instance.setScriptDetailList(instanceDetailList);

//                if (instance.getExpectedProcessHour() == null) {
//                    instance.setExpectedProcessHour(defaultExpectedProcessHour);
//                } else {
//                    processExpectedProcessHour = instance.getExpectedProcessHour();
//                }
//                boolean isExpectedProcessHour = currentHour >= processExpectedProcessHour ? true : false;
//                instance.setIsExpectedProcessHour(isExpectedProcessHour);

//                ExtractScriptDetailEntity extractScriptDetail = extractScriptDetailDAO.getUniqueExtractScriptDetail(configId, instanceId, rankDate);
//                if (extractScriptDetail != null && extractScriptDetail.getStatus() == ExtractScriptDetailEntity.STATUS_SUCCESS) {
//                    log.info("===already extract successful , skip configId:" + configId + ",instanceId:" + instanceId + ",rankDate:" + rankDate);
//                    continue;
//                } else if (extractScriptDetail != null) {
//                    instance.setScriptDetail(extractScriptDetail);
//                }


                resultList.add(instance);
            }


        } catch (Exception e) {
            e.printStackTrace();
            String subject = "FAILED " + fullQulifiedClass + " frequency:" + frequency + ",processingDate:" + processingDate;
            sendMailReport(subject, subject);
        }

        return resultList;
    }

    private List<ExtractScriptDetailEntity> insertForMissingDay(Date startDay, Date endDay, List<Integer> successDayList, int configId, int instanceId) throws Exception {

        List<ExtractScriptDetailEntity> instanceDetailList = new ArrayList<>();
        while (startDay.compareTo(endDay) < 0) {

            int rankDate = FormatUtils.formatDateToYyyyMmDd(startDay);
            if (CollectionUtils.isNotEmpty(successDayList) && successDayList.contains(rankDate)) {
                log.info("===skip success rankDate:" + rankDate);
                startDay = DateUtils.addDays(startDay, 1);
                continue;
            } else {
                log.info("===missing rankDate:" + rankDate);
            }

            ExtractScriptDetailEntity missingDetail = new ExtractScriptDetailEntity();
            missingDetail.setExtractScriptId(configId);
            missingDetail.setExtractInstanceId(instanceId);
            missingDetail.setTargetDate(rankDate);
            missingDetail.setStatus(ExtractScriptDetailEntity.STATUS_PROCESSING);
            missingDetail.setStartedTime(new Date());
            missingDetail.setServerHostname(InetAddress.getLocalHost().getHostAddress());
            missingDetail.setServerPath(ExtractService.getServerPath());
            missingDetail.setIsExpectedProcessHour(true);

            log.info("===ins rankDate:" + rankDate);
            int detailId = insetDetail(missingDetail);
            missingDetail.setId(detailId);

            startDay = DateUtils.addDays(startDay, 1);
            instanceDetailList.add(missingDetail);
        }
        return instanceDetailList;
    }

    private ExtractScriptDetailEntity checkTargetDayAndInsert(ExtractScriptConfigEntity extractScriptConfigEntity, ExtractScriptInstanceEntity instance,
                                                              Date processingDate, int currentHour, int frequency) throws Exception {

        int defaultFrequency = extractScriptConfigEntity.getDefaultFrequency();
        int defaultStartDay = extractScriptConfigEntity.getDefaultStartDay();
        int defaultStartProcessHour = extractScriptConfigEntity.getDefaultStartProcessHour();
        int defaultExpectedProcessHour = extractScriptConfigEntity.getDefaultExpectedProcessHour();
        int instanceId = instance.getId();
        int processStartDay = defaultStartDay;
        int processExpectedProcessHour = defaultExpectedProcessHour;

        if ((instance.getStartProcessHour() == null && currentHour < defaultStartProcessHour) ||
                (instance.getStartProcessHour() != null && currentHour < instance.getStartProcessHour())) {
            log.info("====not after defaultStartProcessHour,skip!");
            return null;
        }

        if ((instance.getFrequency() == null && defaultFrequency != frequency) ||
                (instance.getFrequency() != null && instance.getFrequency() != frequency)) {
            log.info("====frequency not same,skip !" + instanceId);
            return null;
        }

        if (instance.getStartDay() == null) {
            instance.setStartDay(defaultStartDay);
        } else {
            processStartDay = instance.getStartDay();
        }

        log.info("===checkTargetDayAndInsert:" + instanceId + ",processingDate:" + processingDate);

        //check start day
        if (frequency == ExtractScriptConfigEntity.FREQUENCY_WEEKLY) {

            Calendar cal = Calendar.getInstance();
            cal.setTime(processingDate);
            if (cal.get(Calendar.DAY_OF_WEEK) != processStartDay) {
                log.info("====weekly not in processingDate,skip processingDate:" + processingDate + ",processStartDay:" + processStartDay);
                return null;
            }

        } else if (frequency == ExtractScriptConfigEntity.FREQUENCY_BI_WEEKLY) {

            if (!seoclarity.backend.utils.DateUtils.isBiWeeklyStartDate(processingDate)) {
                log.info("====bi-weekly not in processingDate,skip processingDate:" + processingDate + ",processStartDay:" + processStartDay);
                return null;
            }

        } else if (frequency == ExtractScriptConfigEntity.FREQUENCY_MONTHLY) {
            Calendar cal = Calendar.getInstance();
            cal.setTime(processingDate);
            if (cal.get(Calendar.DAY_OF_MONTH) != processStartDay) {
                log.info("====monthly not in processingDate,skip processingDate:" + processingDate + ",processStartDay:" + processStartDay);
                return null;
            }

        }

        int rankDate = FormatUtils.formatDateToYyyyMmDd(processingDate);
        ExtractScriptDetailEntity extractScriptDetail = new ExtractScriptDetailEntity();
        extractScriptDetail.setExtractScriptId(instance.getExtractScriptId());
        extractScriptDetail.setExtractInstanceId(instance.getId());
        extractScriptDetail.setTargetDate(rankDate);
        extractScriptDetail.setStatus(ExtractScriptDetailEntity.STATUS_PROCESSING);
        extractScriptDetail.setStartedTime(new Date());
        extractScriptDetail.setServerHostname(InetAddress.getLocalHost().getHostAddress());
        extractScriptDetail.setServerPath(ExtractService.getServerPath());
        if (instance.getExpectedProcessHour() != null) {
            processExpectedProcessHour = instance.getExpectedProcessHour();
        }
        boolean isExpectedProcessHour = currentHour >= processExpectedProcessHour ? true : false;
        extractScriptDetail.setIsExpectedProcessHour(isExpectedProcessHour);

        int detailId = insetDetail(extractScriptDetail);
        extractScriptDetail.setId(detailId);

        return extractScriptDetail;
    }

    public boolean isRankQcPassWithCheckEngine(int rankDate, int ownDomainId, int engineId, int languageId, int rankType, String device) {
        OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getById(ownDomainId);
        if (ownDomainEntity == null) {
            log.info("====domain not exist:" + ownDomainId);
            return true;
        }

        int frequence = 0;
        if (ownDomainEntity.getKeywordRankFrequency() != null && ownDomainEntity.getKeywordRankFrequency() == OwnDomainEntity.KEYWORD_RANK_FREQUENCY_DAILY) {
            frequence = 1;
        } else if (ownDomainEntity.getKeywordRankFrequency() != null && ownDomainEntity.getKeywordRankFrequency() == OwnDomainEntity.KEYWORD_RANK_FREQUENCY_WEEKLY) {
            frequence = 7;
            log.info("====not suppose weekly qc now!");
            return true;
        } else {
            log.info("====no logic for qcTable:" + ownDomainId + ",fre:" + ownDomainEntity.getKeywordRankFrequency());
            return true;
        }

        boolean engineExist = false;
        String inputKey = engineId + "_" + languageId + "_" + (device.equalsIgnoreCase("desktop") ? "d" : "m");
        List<DomainSearchEngineRelEntity> domainSearchEngineRelList = scKeywordRankManager.getAllSERels(ownDomainEntity);
        for (DomainSearchEngineRelEntity dser : domainSearchEngineRelList) {
            String relKey = dser.getRankcheckSearchEngineId() + "_" + dser.getRankcheckSearchLanguageid() + "_" + dser.getDevice();
            log.info("====relKey:" + relKey);
            if (relKey.equals(inputKey)) {
                engineExist = true;
                break;
            }
        }

        if (!engineExist) {
            log.info("====engine not exist oid:" + ownDomainId + ",inputKey:" + inputKey);
            return true;
        }

        RankQcStateEntity rankQcStateEntity = new RankQcStateEntity();
        rankQcStateEntity.setRankDate(rankDate);
        rankQcStateEntity.setOwnDomainId(ownDomainId);
        rankQcStateEntity.setEngineId(engineId);
        rankQcStateEntity.setLanguageId(languageId);
        rankQcStateEntity.setDevice(device.equalsIgnoreCase("desktop") ? "d" : "m");
        rankQcStateEntity.setRankType(rankType);
        rankQcStateEntity.setFrequence(frequence);

        if(engineId ==1 && languageId == 1){//https://www.wrike.com/open.htm?id=1481634865
            rankQcStateEntity.setCountryType(RankQcInfoEntity.COUNTRY_US);
        }else {
            rankQcStateEntity.setCountryType(RankQcInfoEntity.COUNTRY_INTL);
        }

        Integer qcStatus = rankQcInfoEntityDAO.getRankQcStatus(rankQcStateEntity);
        if (qcStatus != null && qcStatus == RankQcStateEntity.STATUS_OK) {
            return true;
        } else if (qcStatus == null) {
            log.info("=== no managed keyword,do not extract!");
            return true;
        } else {
            return false;
        }
    }

    public boolean isRankQcPass(int rankDate, int ownDomainId, int engineId, int languageId, String device, int rankType, int frequence) {
        RankQcStateEntity rankQcStateEntity = new RankQcStateEntity();
        rankQcStateEntity.setRankDate(rankDate);
        rankQcStateEntity.setOwnDomainId(ownDomainId);
        rankQcStateEntity.setEngineId(engineId);
        rankQcStateEntity.setLanguageId(languageId);
        rankQcStateEntity.setDevice(device.equalsIgnoreCase("desktop") ? "d" : "m");
        rankQcStateEntity.setRankType(rankType);
        rankQcStateEntity.setFrequence(frequence);

        if(engineId ==1 && languageId == 1){//https://www.wrike.com/open.htm?id=1481634865
            rankQcStateEntity.setCountryType(RankQcInfoEntity.COUNTRY_US);
        }else {
            rankQcStateEntity.setCountryType(RankQcInfoEntity.COUNTRY_INTL);
        }

        Integer qcStatus = rankQcInfoEntityDAO.getRankQcStatus(rankQcStateEntity);
        if (qcStatus != null && qcStatus == RankQcStateEntity.STATUS_OK) {
            return true;
        }  else if (qcStatus == null) {
            log.info("=== no managed keyword,do not extract!");
            return true;
        }
        else {
            return false;
        }
    }

    public int insetDetail(ExtractScriptDetailEntity extractScriptDetail) {
        return extractScriptDetailDAO.insert(extractScriptDetail);
    }

    public void updateForStart(ExtractScriptDetailEntity extractScriptDetail) {
        extractScriptDetailDAO.updateForStart(extractScriptDetail.getStatus(), extractScriptDetail.getStartedTime(), extractScriptDetail.getId());
    }


    public void updateForFailure(ExtractScriptDetailEntity extractScriptDetail) {
        extractScriptDetailDAO.updateForFailure(extractScriptDetail.getStatus(), extractScriptDetail.getElapsedSeconds(),
                extractScriptDetail.getEndedTime(), extractScriptDetail.getId(), extractScriptDetail.getFatalError());
    }

    public void updateForSuccess(ExtractScriptDetailEntity extractScriptDetail) {
        extractScriptDetailDAO.updateForSuccess(extractScriptDetail);
    }

    public static String getServerPath() throws IOException {
        File directory = new File("");
        String courseFile = directory.getCanonicalPath();
        return courseFile;
    }

    public void updateDetailAndSendEmail(ExtractScriptDetailEntity extractScriptDetail, int status, String fatalError, long startTime,
                                         int domainId, String device, boolean success, Date processDate) {

        extractScriptDetail.setStatus(status);
        if (StringUtils.isNotBlank(fatalError)) {
            extractScriptDetail.setFatalError(fatalError);
        }
        extractScriptDetail.setEndedTime(new Date());
        long elapsedSeconds = (System.currentTimeMillis() - startTime) / 1000;
        extractScriptDetail.setElapsedSeconds((int) elapsedSeconds);
        updateForFailure(extractScriptDetail);

        if (extractScriptDetail.getIsExpectedProcessHour()) {
            log.info("======send email alert !!");
            sendEmailAlert(domainId, device, success, processDate);
        } else {
            log.warn("======== not after ExpectedProcessHour, don't send email ");
        }

    }

    public void sendEmailAlert(int domainId, String device, boolean success, Date processDate) {
        String subject = getEmailSubject(domainId, device, success, processDate);
        String message = subject;
        sendMailReport(subject, message);
    }


    private String getEmailSubject(int domainId, String device, boolean success, Date processDate) {

        String subject = "";
        String status = success ? "Success" : "Failed";
        switch (domainId) {
            case 7006:
                subject = status + " Export " + domainId + " " + device + " Top 100 Daily Ranking " + FormatUtils.formatDateToYyyyMmDd(processDate);
                break;
            case 185:
                subject = status + " Export " + domainId + " " + device + " Top 100 Daily Ranking " + FormatUtils.formatDateToYyyyMmDd(processDate);
                break;
            default:
                subject = status + " Export " + domainId + " " + device + " RankExtract " + FormatUtils.formatDateToYyyyMmDd(processDate);
                break;
        }

        return subject;
    }

    public void sendMailReport(String subject, String message) {
        Map<String, Object> reportMap = new HashMap<String, Object>();
        reportMap.put("userName", "Jason"); // TODO
        reportMap.put("successMessage", message);
        String emailTo = "<EMAIL>"; // TODO
        String[] ccTo = new String[]{"<EMAIL>"};
//        String[] ccTo = null;

//        emailSenderComponent.sendMimeMultiPartMailAndBcc(emailTo, ccTo, subject, "mail_resource_operate_report.txt", "mail_resource_operate_report.html", reportMap);
        zeptoMailSenderComponent.sendMimeMultiPartZeptoMailAndBccByFunctionType(emailTo, ccTo, subject, "mail_resource_operate_report.txt", "mail_resource_operate_report.html", reportMap, null,
                ZeptoMailSenderComponent.FUNCTION_TYPE_BACKEND_INTERNAL, null, null);
    }

    public static String formatGoogleUrl(String url) {

        if (StringUtils.isBlank(url)) {
            return null;
        }
        if (url.contains("www.google.com") && url.contains("?")) {
            return url.substring(0, url.indexOf("?"));
        } else if (url.contains("google.com") && url.contains("?")) {
            return url.substring(0, url.indexOf("?"));
        } else {
            return url;
        }
    }


    public List<ExtractScriptDetailEntity> getSimpleNeedRunExtract(int category, String className, String specialCategory, int frequency,
                                                                   Date processingDate, Integer domainId, String device) {

        int rankDate = FormatUtils.formatDateToYyyyMmDd(processingDate);
        log.info("=====getSimpleNeedRunExtract category:" + category + ",fullQulifiedClass:" + className + ",specialCategory:"
                + specialCategory + ",frequency:" + frequency + ",domainId:" + domainId + ",device:" + device + ",processingDate:" + processingDate);

        String country = "";
        List<ExtractScriptDetailEntity> resultList = new ArrayList<>();
        try {
            ExtractScriptConfigEntity extractScriptConfigEntity =
                    extractScriptConfigDAO.getUniqueExtractScriptConfig(category, PROJECT_NAME, className, specialCategory, frequency);
            if (extractScriptConfigEntity == null) {
                log.warn("===config not exist!");
                return null;
            }

            int configId = extractScriptConfigEntity.getId();
            ExtractScriptInstanceEntity instance = extractScriptInstanceDAO.getUniqueInstance(configId, domainId, country,
                    ExtractScriptInstanceEntity.TAG_ID_FOR_DOMAIN_LEVEL, ExtractScriptInstanceEntity.RANK_TYPE_ALL, device, frequency);
            if (instance == null) {
                log.warn("===instance not exist configId:" + configId);
                return null;
            }

            int targetDate = FormatUtils.formatDateToYyyyMmDd(processingDate);
            int instanceId = instance.getId();

            //出错需要重跑的日期
            List<ExtractScriptDetailEntity> failedDayList = extractScriptDetailDAO.getFailedDayList(configId, instanceId);
            for (ExtractScriptDetailEntity extractScriptDetailEntity : failedDayList) {
                int failedTargetDate = extractScriptDetailEntity.getTargetDate();
//                if(rankDate == targetDate){
//                    continue;
//                }
                System.out.println("===rerun error day:" + failedTargetDate);
                resultList.add(extractScriptDetailEntity);
            }
            //丢失需要重跑的日期(包括第一次运行，只跑1天)
            List<ExtractScriptDetailEntity> rerunList = checkLast10DaysExtract(configId, instanceId, targetDate, frequency);
            if (CollectionUtils.isNotEmpty(rerunList)) {
                resultList.addAll(rerunList);
            }

            log.info("resultList size:" + resultList.size());
            return resultList;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("=====FAILED:" + className + " frequency:" + frequency + " " + rankDate);
        }
        return resultList;
    }

    public List<ExtractScriptDetailEntity> checkLast10DaysExtract(int extractScriptId, int extractInstanceId, int targetDay, int frequency) {
        Date endDate = FormatUtils.toDate(String.valueOf(targetDay), FormatUtils.DATE_FORMAT_YYYYMMDD);
        Date startDate = DateUtils.addDays(endDate, -10);
        int startDay = FormatUtils.formatDateToYyyyMmDd(startDate);
        try {
            List<Integer> existDayList = extractScriptDetailDAO.getExistDay(extractScriptId, extractInstanceId, startDay);
            Date firstRunDay = startDate;
            if (CollectionUtils.isNotEmpty(existDayList)) {
                firstRunDay = FormatUtils.toDate(String.valueOf(existDayList.get(0)), FormatUtils.DATE_FORMAT_YYYYMMDD);
            }
            List<ExtractScriptDetailEntity> missingDetailList = insertMissingDay(firstRunDay, endDate, existDayList, extractScriptId, extractInstanceId, frequency);
            if (CollectionUtils.isNotEmpty(missingDetailList)) {
                return missingDetailList;
            } else {
                return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private List<ExtractScriptDetailEntity> insertMissingDay(Date startDay, Date endDay, List<Integer> existDayList, int configId, int instanceId, int frequency) throws Exception {

        List<ExtractScriptDetailEntity> instanceDetailList = new ArrayList<>();
        int startDayInt = FormatUtils.formatDateToYyyyMmDd(startDay);
        if (CollectionUtils.isEmpty(existDayList)) {//first time run
            boolean canBeRun = checkProcessDate(frequency, endDay);
            if(!canBeRun){
                log.error("====can not run this day frequency:" + frequency + ",processDate:" + endDay);
                return instanceDetailList;
            }
            int rankDate = FormatUtils.formatDateToYyyyMmDd(endDay);
            log.info("===first time run, only add one day:" + rankDate);
            ExtractScriptDetailEntity missingDetail = new ExtractScriptDetailEntity();
            missingDetail.setExtractScriptId(configId);
            missingDetail.setExtractInstanceId(instanceId);
            missingDetail.setTargetDate(rankDate);
            missingDetail.setStatus(ExtractScriptDetailEntity.STATUS_PROCESSING);
            missingDetail.setStartedTime(new Date());
            missingDetail.setServerHostname(InetAddress.getLocalHost().getHostAddress());
            missingDetail.setServerPath(ExtractService.getServerPath());
            missingDetail.setIsExpectedProcessHour(true);
            int detailId = insetDetail(missingDetail);
            missingDetail.setId(detailId);
            instanceDetailList.add(missingDetail);
        } else {
            while (startDay.compareTo(endDay) <= 0) {
                boolean canBeRun = checkProcessDate(frequency, startDay);
                if(!canBeRun){
                    log.error("====can not run this day frequency:" + frequency + ",processDate:" + startDay);
                    startDay = DateUtils.addDays(startDay, 1);
                    continue;
                }
                int rankDate = FormatUtils.formatDateToYyyyMmDd(startDay);
                if (CollectionUtils.isNotEmpty(existDayList) && existDayList.contains(rankDate)) {
                    log.info("===skip exist rankDate:" + rankDate);
                    startDay = DateUtils.addDays(startDay, 1);
                    continue;
                } else {
                    log.info("===missing rankDate:" + rankDate);
                }

                ExtractScriptDetailEntity missingDetail = new ExtractScriptDetailEntity();
                missingDetail.setExtractScriptId(configId);
                missingDetail.setExtractInstanceId(instanceId);
                missingDetail.setTargetDate(rankDate);
                missingDetail.setStatus(ExtractScriptDetailEntity.STATUS_PROCESSING);
                missingDetail.setStartedTime(new Date());
                missingDetail.setServerHostname(InetAddress.getLocalHost().getHostAddress());
                missingDetail.setServerPath(ExtractService.getServerPath());
                missingDetail.setIsExpectedProcessHour(true);

                int detailId = insetDetail(missingDetail);
                missingDetail.setId(detailId);

                startDay = DateUtils.addDays(startDay, 1);
                instanceDetailList.add(missingDetail);
            }
        }
        return instanceDetailList;
    }

    /**
     * todo
     * @param frequency
     * @param processDate
     * @return
     */
    private boolean checkProcessDate(int frequency, Date processDate){

        if(frequency == ExtractScriptInstanceEntity.FREQUENCY_DAILY){
            return true;
        }else if(frequency == ExtractScriptInstanceEntity.FREQUENCY_WEEKLY){
            return FormatUtils.isSunday(processDate);//weekly 只导周日数据
        }else {
            log.info("===checkProcessDate FRE:" + frequency);
            return true;
        }
    }

    public List<ExtractScriptInstanceEntity> getNeedExtractDetailList() {
        Date processingDate = new Date();//默认导当天
        Integer rankDate = null;
        Calendar calendar = Calendar.getInstance();

        int currentHour = calendar.get(Calendar.HOUR_OF_DAY);
        Date yesterday = DateUtils.addDays(processingDate, -1);

        List<ExtractScriptInstanceEntity> resultList = new ArrayList<>();
        try {
            List<ExtractScriptConfigEntity> extractScriptConfigList = extractScriptConfigDAO.getConfigList();
            if (CollectionUtils.isEmpty(extractScriptConfigList)) {
                log.warn("===config not exist!");
                return null;
            }

            Integer frequency = null;
            Integer StartProcessHour = null;
            Integer ExpectedProcessHour = null;
            String mailTitle = null;
            String mailTo = null;
            String mailCc = null;

            for(ExtractScriptConfigEntity extractScriptConfig:  extractScriptConfigList){

                int configId = extractScriptConfig.getId();
                frequency = extractScriptConfig.getDefaultFrequency();
                int defaultStartDay = extractScriptConfig.getDefaultStartDay();
                int defaultStartProcessHour = extractScriptConfig.getDefaultStartProcessHour();
                int defaultExpectedProcessHour = extractScriptConfig.getDefaultExpectedProcessHour();
                int processFrequency = extractScriptConfig.getDefaultFrequency();
                int processStartDay = extractScriptConfig.getDefaultStartDay();
                int processExpectedProcessHour = extractScriptConfig.getDefaultExpectedProcessHour();
                String className = extractScriptConfig.getFullQulifiedClass();

                List<ExtractScriptInstanceEntity> instanceList = extractScriptInstanceDAO.getInstanceByConfig(configId);
                if (CollectionUtils.isEmpty(instanceList)) {
                    log.warn("===instance not exist configId:" + configId);
                    continue;
                }

                for (ExtractScriptInstanceEntity instance : instanceList) {

                    int instanceId = instance.getId();
                    int domainId = instance.getOwnDomainId();
                    OwnDomainEntity ownDomainEntity =ownDomainEntityDAO.getById(domainId);
                    if(ownDomainEntity == null){
                        log.info("======domain suspend:" + domainId + ",instanceId:" + instanceId);
                        extractScriptInstanceDAO.updateSuspendDomain(instanceId);
                        String subject = "DISABLE suspend domain instance oid:" + domainId + ",instanceId:" + instanceId;
                        sendMailReport(subject, subject);
                        continue;
                    }

                    if(instance.getFrequency() != null){
                        frequency = instance.getFrequency().intValue();
                    }
//                    if(frequency != ExtractScriptInstanceEntity.FREQUENCY_DAILY){
//                        log.info("====not daily , skip configId:" + configId + ",instanceId:" + instanceId + ",rankDate:" + rankDate);
//                        continue;
//                    }

                    if (instance.getStartDay() == null) {
                        instance.setStartDay(defaultStartDay);
                    } else {
                        processStartDay = instance.getStartDay();
                    }

                    log.info("===checkTargetDayAndInsert:" + instanceId + ",processingDate:" + processingDate);

                    //check start day
                    if (frequency == ExtractScriptConfigEntity.FREQUENCY_WEEKLY) {

                        Calendar cal = Calendar.getInstance();
                        cal.setTime(processingDate);
                        if (cal.get(Calendar.DAY_OF_WEEK) != processStartDay) {
                            log.info("====weekly not in processingDate,skip processingDate:" + processingDate + ",processStartDay:" + processStartDay);
                            continue;
                        }

                    } else if (frequency == ExtractScriptConfigEntity.FREQUENCY_BI_WEEKLY) {

                        if (!seoclarity.backend.utils.DateUtils.isBiWeeklyStartDate(processingDate)) {
                            log.info("====bi-weekly not in processingDate,skip processingDate:" + processingDate + ",processStartDay:" + processStartDay);
                            continue;
                        }

                    } else if (frequency == ExtractScriptConfigEntity.FREQUENCY_MONTHLY) {
                        Calendar cal = Calendar.getInstance();
                        cal.setTime(processingDate);
                        if (cal.get(Calendar.DAY_OF_MONTH) != processStartDay) {
                            log.info("====monthly not in processingDate,skip processingDate:" + processingDate + ",processStartDay:" + processStartDay);
                            continue;
                        }

                    }

                    log.info("==========ST INSTANCE:" + instanceId + ",className:" + className);
                    List<ExtractScriptDetailEntity> instanceDetailList = new ArrayList<>();
                    instance.setFullQulifiedClass(className);
                    boolean isEnableExtractCurrentDay = instance.getEnableExtractCurrentDay();
                    if (isEnableExtractCurrentDay) {
                        processingDate = new Date();
                        rankDate = FormatUtils.formatDateToYyyyMmDd(processingDate);

                        ExtractScriptDetailEntity extractScriptDetail = extractScriptDetailDAO.getUniqueExtractScriptDetail(configId, instanceId, rankDate);
                        if (extractScriptDetail != null && extractScriptDetail.getStatus() == ExtractScriptDetailEntity.STATUS_SUCCESS) {
                            log.info("===already extract successful , skip configId:" + configId + ",instanceId:" + instanceId + ",rankDate:" + rankDate);
                            continue;
                        } else if (extractScriptDetail != null) {//当天失败重跑
                            System.out.println("===rerun today");
                            //check send email time
                            if (instance.getExpectedProcessHour() != null) {
                                processExpectedProcessHour = instance.getExpectedProcessHour();
                            }
                            boolean isExpectedProcessHour = currentHour >= processExpectedProcessHour ? true : false;
                            extractScriptDetail.setIsExpectedProcessHour(isExpectedProcessHour);
                            instanceDetailList.add(extractScriptDetail);

                            List<ExtractScriptDetailEntity> failedDayList = extractScriptDetailDAO.getFailedDayList(configId, instanceId);
                            if (CollectionUtils.isEmpty(failedDayList)) {//没有失败记录，查询最后一次成功日期和当前日期之间的日期有没有没导出的(可能是服务器或脚本失败导致程序没有运行起来的情况)

                                Integer maxSuccessDay = extractScriptDetailDAO.getLastSuccessDay(configId, instanceId);
                                if (maxSuccessDay != null && frequency == ExtractScriptConfigEntity.FREQUENCY_DAILY) {
                                    Date maxSuccessDate = DateUtils.addDays(FormatUtils.toDate(String.valueOf(maxSuccessDay), FormatUtils.DATE_FORMAT_YYYYMMDD), 1);
                                    Date compareDate = FormatUtils.toDate(FormatUtils.formatDate(processingDate, FormatUtils.DATE_FORMAT_YYYYMMDD), FormatUtils.DATE_FORMAT_YYYYMMDD);

                                    List<ExtractScriptDetailEntity> missingDetailList = insertForMissingDay(maxSuccessDate, compareDate, null, configId, instanceId);
                                    if (CollectionUtils.isNotEmpty(missingDetailList)) {
                                        instanceDetailList.addAll(missingDetailList);
                                    }
                                }else{
                                    log.info("===no missing date need to process.");
                                }

                                ExtractScriptDetailEntity extractScriptDetailEntity = checkTargetDayAndInsert(extractScriptConfig, instance, processingDate, currentHour, frequency);
                                if (extractScriptDetailEntity == null) {
                                    log.info("==not pass check configId:" + configId + ",instanceId:" + instanceId);
                                } else {
                                    instanceDetailList.add(extractScriptDetailEntity);
                                }
                            } else {//process error date(auto rerun) skip check 重跑失败的日期
                                for (ExtractScriptDetailEntity extractScriptDetailEntity : failedDayList) {
                                    int targetDate = extractScriptDetailEntity.getTargetDate();
                                    if (rankDate == targetDate) {
                                        continue;
                                    }
                                    System.out.println("===rerun error day:" + targetDate);
                                    instanceDetailList.addAll(failedDayList);
                                }


//                            Date maxFailedDay = DateUtils.addDays(FormatUtils.toDate(String.valueOf(failedDayList.get(failedDayList.size() - 1).getTargetDate()), FormatUtils.DATE_FORMAT_YYYYMMDD), 1);
//                            Date compareDate = FormatUtils.toDate(FormatUtils.formatDate(processingDate, FormatUtils.DATE_FORMAT_YYYYMMDD), FormatUtils.DATE_FORMAT_YYYYMMDD);
//
//                            List<Integer> successDayList = extractScriptDetailDAO.getSuccessDayList(configId, instanceId, FormatUtils.formatDateToYyyyMmDd(maxFailedDay));
//
//                            List<ExtractScriptDetailEntity> missingDetailList = insertForMissingDay(maxFailedDay, compareDate, successDayList, configId, instanceId);
//                            if (CollectionUtils.isNotEmpty(missingDetailList)) {
//                                instanceDetailList.addAll(missingDetailList);
//                            }
//
//                            ExtractScriptDetailEntity extractScriptDetailEntity = checkTargetDayAndInsert(extractScriptConfigEntity, instance, processingDate, currentHour, frequency);
//                            if (extractScriptDetailEntity == null) {
//                                log.info("==not pass check configId:" + configId + ",instanceId:" + instanceId);
//                            } else {
//                                instanceDetailList.add(extractScriptDetailEntity);
//                            }

                            }

                        } else {//导出当天和失败的日期

                            List<ExtractScriptDetailEntity> failedDayList = extractScriptDetailDAO.getFailedDayList(configId, instanceId);
                            if (CollectionUtils.isEmpty(failedDayList)) {
                                Integer maxSuccessDay = extractScriptDetailDAO.getLastSuccessDay(configId, instanceId);
                                if (maxSuccessDay != null) {
                                    Date maxSuccessDate = DateUtils.addDays(FormatUtils.toDate(String.valueOf(maxSuccessDay), FormatUtils.DATE_FORMAT_YYYYMMDD), 1);
                                    Date compareDate = FormatUtils.toDate(FormatUtils.formatDate(processingDate, FormatUtils.DATE_FORMAT_YYYYMMDD), FormatUtils.DATE_FORMAT_YYYYMMDD);

                                    List<ExtractScriptDetailEntity> missingDetailList = insertForMissingDay(maxSuccessDate, compareDate, null, configId, instanceId);
                                    if (CollectionUtils.isNotEmpty(missingDetailList)) {
                                        instanceDetailList.addAll(missingDetailList);
                                    }
                                }

                                ExtractScriptDetailEntity extractScriptDetailEntity = checkTargetDayAndInsert(extractScriptConfig, instance, processingDate, currentHour, frequency);
                                if (extractScriptDetailEntity == null) {
                                    log.info("==not pass check configId:" + configId + ",instanceId:" + instanceId);
                                } else {
                                    instanceDetailList.add(extractScriptDetailEntity);
                                }

                            } else {//process error date(auto rerun) skip check
                                System.out.println("===rerun error day");
                                instanceDetailList.addAll(failedDayList);

                                Date maxFailedDay = DateUtils.addDays(FormatUtils.toDate(String.valueOf(failedDayList.get(failedDayList.size() - 1).getTargetDate()), FormatUtils.DATE_FORMAT_YYYYMMDD), 1);
                                Date compareDate = FormatUtils.toDate(FormatUtils.formatDate(processingDate, FormatUtils.DATE_FORMAT_YYYYMMDD), FormatUtils.DATE_FORMAT_YYYYMMDD);

                                List<Integer> successDayList = extractScriptDetailDAO.getSuccessDayList(configId, instanceId, FormatUtils.formatDateToYyyyMmDd(maxFailedDay));

                                List<ExtractScriptDetailEntity> missingDetailList = insertForMissingDay(maxFailedDay, compareDate, successDayList, configId, instanceId);
                                if (CollectionUtils.isNotEmpty(missingDetailList)) {
                                    instanceDetailList.addAll(missingDetailList);
                                }

                                ExtractScriptDetailEntity extractScriptDetailEntity = checkTargetDayAndInsert(extractScriptConfig, instance, processingDate, currentHour, frequency);
                                if (extractScriptDetailEntity == null) {
                                    log.info("==not pass check configId:" + configId + ",instanceId:" + instanceId);
                                } else {
                                    instanceDetailList.add(extractScriptDetailEntity);
                                }
                            }
                        }

                    } else {//当天导前一天的数据

                        processingDate = yesterday;
                        calendar = Calendar.getInstance();
                        currentHour = calendar.get(Calendar.HOUR_OF_DAY);
                        rankDate = FormatUtils.formatDateToYyyyMmDd(processingDate);

                        ExtractScriptDetailEntity extractScriptDetail = extractScriptDetailDAO.getUniqueExtractScriptDetail(configId, instanceId, rankDate);
                        if (extractScriptDetail != null && extractScriptDetail.getStatus() == ExtractScriptDetailEntity.STATUS_SUCCESS) {
                            log.info("===already extract successful , skip configId:" + configId + ",instanceId:" + instanceId + ",rankDate:" + rankDate);
                            continue;
                        } else if (extractScriptDetail != null) {
                            //check send email time
                            if (instance.getExpectedProcessHour() != null) {
                                processExpectedProcessHour = instance.getExpectedProcessHour();
                            }
                            boolean isExpectedProcessHour = currentHour >= processExpectedProcessHour ? true : false;
                            extractScriptDetail.setIsExpectedProcessHour(isExpectedProcessHour);
                            log.info("==addDetail rankDate:" + rankDate + ",ID:" + extractScriptDetail.getId());
                            instanceDetailList.add(extractScriptDetail);
                        } else {

                            List<ExtractScriptDetailEntity> failedDayList = extractScriptDetailDAO.getFailedDayList(configId, instanceId);
                            if (CollectionUtils.isEmpty(failedDayList)) {

                                Integer maxSuccessDay = extractScriptDetailDAO.getLastSuccessDay(configId, instanceId);
                                if (maxSuccessDay != null && frequency == ExtractScriptConfigEntity.FREQUENCY_DAILY) {
                                    Date maxSuccessDate = DateUtils.addDays(FormatUtils.toDate(String.valueOf(maxSuccessDay), FormatUtils.DATE_FORMAT_YYYYMMDD), 1);
                                    Date compareDate = FormatUtils.toDate(FormatUtils.formatDate(processingDate, FormatUtils.DATE_FORMAT_YYYYMMDD), FormatUtils.DATE_FORMAT_YYYYMMDD);

                                    List<ExtractScriptDetailEntity> missingDetailList = insertForMissingDay(maxSuccessDate, compareDate, null, configId, instanceId);
                                    if (CollectionUtils.isNotEmpty(missingDetailList)) {
                                        instanceDetailList.addAll(missingDetailList);
                                    }

                                }

                                ExtractScriptDetailEntity extractScriptDetailEntity = checkTargetDayAndInsert(extractScriptConfig, instance, processingDate, currentHour, frequency);
                                if (extractScriptDetailEntity == null) {
                                    log.info("==not pass check configId:" + configId + ",instanceId:" + instanceId);
                                } else {
                                    instanceDetailList.add(extractScriptDetailEntity);
                                }


                            } else {//process error date(auto rerun) skip check

                                instanceDetailList.addAll(failedDayList);
                                if (frequency == ExtractScriptConfigEntity.FREQUENCY_DAILY) {
                                    Date maxFailedDay = DateUtils.addDays(FormatUtils.toDate(String.valueOf(failedDayList.get(failedDayList.size() - 1).getTargetDate()), FormatUtils.DATE_FORMAT_YYYYMMDD), 1);
                                    Date compareDate = FormatUtils.toDate(FormatUtils.formatDate(processingDate, FormatUtils.DATE_FORMAT_YYYYMMDD), FormatUtils.DATE_FORMAT_YYYYMMDD);

                                    List<Integer> successDayList = extractScriptDetailDAO.getSuccessDayList(configId, instanceId, FormatUtils.formatDateToYyyyMmDd(maxFailedDay));
                                    List<ExtractScriptDetailEntity> missingDetailList = insertForMissingDay(maxFailedDay, compareDate, successDayList, configId, instanceId);
                                    if (CollectionUtils.isNotEmpty(missingDetailList)) {
                                        instanceDetailList.addAll(missingDetailList);
                                    }
                                }

                                //
                                ExtractScriptDetailEntity extractScriptDetailEntity = checkTargetDayAndInsert(extractScriptConfig, instance, processingDate, currentHour, frequency);
                                if (extractScriptDetailEntity == null) {
                                    log.info("==not pass check configId:" + configId + ",instanceId:" + instanceId);
                                } else {
                                    instanceDetailList.add(extractScriptDetailEntity);
                                }
                            }
                        }
                    }
                    instance.setScriptDetailList(instanceDetailList);
                    resultList.add(instance);
                }
            }

        } catch (Exception e) {
            e.printStackTrace();

        }
        return resultList;
    }

    public String setAdhocAdsJsonResult(CLRankingDetailEntity info, List<CLRankingDetailEntity> detailRankList,
                                       List<CLRankingDetailEntity> subRankList, List<CLRankingDetailEntity> adsRankList, List<CLRankingDetailEntity> plaRankList){
        Gson gson = new GsonBuilder().disableHtmlEscaping().create();
        RankExtractJsonVO rankExtractJsonVO = setExtractJsonVO(info, detailRankList, subRankList, null);

        List<RankExtractJsonPla> plaRankJsonList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(plaRankList)){
            for(CLRankingDetailEntity plaRank : plaRankList){
                RankExtractJsonPla rankExtractJsonPla = new RankExtractJsonPla();
                rankExtractJsonPla.setBrandName(plaRank.getPlaBrandName());
                rankExtractJsonPla.setDomain(plaRank.getPlaDomain());
                rankExtractJsonPla.setLabel(plaRank.getPlaLabel());
                rankExtractJsonPla.setPos(plaRank.getPlaPos());
                rankExtractJsonPla.setRank(plaRank.getPlaRank());
                if(StringUtils.isNotBlank(plaRank.getPlaRating())){
                    rankExtractJsonPla.setRating(plaRank.getPlaRating());
                }
                rankExtractJsonPla.setPrice(plaRank.getPlaPrice());
                rankExtractJsonPla.setUrl(plaRank.getPlaUrl());
                plaRankJsonList.add(rankExtractJsonPla);
            }
        }

        List<RankExtractJsonAds> adsRankJsonList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(adsRankList)){
            for (CLRankingDetailEntity adsRank : adsRankList){
                RankExtractJsonAds rankExtractJsonAds = new RankExtractJsonAds();
                rankExtractJsonAds.setAnchorText(adsRank.getAdsAnchorText());
                rankExtractJsonAds.setDesc(adsRank.getAdsDesc());
                rankExtractJsonAds.setDispalyUrl(adsRank.getAdsDisplayUrl());
                rankExtractJsonAds.setDomain(adsRank.getAdsDomain());
                rankExtractJsonAds.setPos(adsRank.getAdsPos());
                rankExtractJsonAds.setRank(adsRank.getAdsRank());
                rankExtractJsonAds.setSiteLinkFlag(adsRank.getAdsSiteLinkFlag());
                adsRankJsonList.add(rankExtractJsonAds);
            }
        }

        if(CollectionUtils.isNotEmpty(plaRankJsonList)){
            rankExtractJsonVO.setPla(plaRankJsonList);
        }
        if(CollectionUtils.isNotEmpty(adsRankJsonList)){
            rankExtractJsonVO.setAds(adsRankJsonList);
        }

        return gson.toJson(rankExtractJsonVO);
    }

    public String setExtractJsonResult(CLRankingDetailEntity info, List<CLRankingDetailEntity> detailRankList, List<CLRankingDetailEntity> subRankList, Map<Integer, String> tagMap){
        Gson gson = new GsonBuilder().disableHtmlEscaping().create();
        RankExtractJsonVO rankExtractJsonVO = setExtractJsonVO(info, detailRankList, subRankList, tagMap);
        return gson.toJson(rankExtractJsonVO);
    }

    public RankExtractJsonVO setExtractJsonVO(CLRankingDetailEntity info, List<CLRankingDetailEntity> detailRankList, List<CLRankingDetailEntity> subRankList, Map<Integer, String> tagMap){

        RankExtractJsonVO rankExtractJsonVO = new RankExtractJsonVO();

        RankExtractJsonInfo extractJsonInfo = new RankExtractJsonInfo();
        extractJsonInfo.setDate(info.getRankingDate());
        extractJsonInfo.setStatus("success");

        RankExtractJsonSearchParameters rankExtractJsonSearchParameters = new RankExtractJsonSearchParameters();
        rankExtractJsonSearchParameters.setId(info.getKeywordRankcheckId());
        rankExtractJsonSearchParameters.setSearchEngine(info.getSearchEngine());
        rankExtractJsonSearchParameters.setLanguage(info.getLanguageName());
        rankExtractJsonSearchParameters.setCountry(info.getCountry());
        rankExtractJsonSearchParameters.setDevice(info.getDevice());
        rankExtractJsonSearchParameters.setKeyword(info.getKeywordName());
        rankExtractJsonSearchParameters.setLocation(info.getLocationName());

        RankExtractJsonSearchInfo rankExtractJsonSearchInfo = new RankExtractJsonSearchInfo();
        rankExtractJsonSearchInfo.setCpc(info.getCpc());
        rankExtractJsonSearchInfo.setSearchVolume(info.getAvgSearchVolume());
        rankExtractJsonSearchInfo.setTrueDemand(info.getTrueDemand());
        if(StringUtils.isNotBlank(info.getGoogleRecommend())){
            rankExtractJsonSearchInfo.setGoogleRecommend(info.getGoogleRecommend());
        }
        //cancel it https://www.wrike.com/open.htm?id=1611506956
//        if(StringUtils.isNotBlank(info.getTotalResults()) && !info.getTotalResults().equals("-")){
//            rankExtractJsonSearchInfo.setTotalResults(Long.parseLong(info.getTotalResults()));
//        }

        String serpFilterButtonTextStr = info.getSerpFilterButtonTextStr();
        String serpFilterKeywordsStr = info.getSerpFilterKeywordsStr();
        if(StringUtils.isNotBlank(serpFilterButtonTextStr) && !serpFilterButtonTextStr.equals("-")){
            String[] arr = serpFilterButtonTextStr.split("!_!");
            List<String> serpFilterButtonTextList = Arrays.stream(arr).collect(Collectors.toList());
            rankExtractJsonSearchInfo.setSerpFilterButtonText(serpFilterButtonTextList);
        }
        if(StringUtils.isNotBlank(serpFilterKeywordsStr) && !serpFilterKeywordsStr.equals("-")){
            String[] arr = serpFilterKeywordsStr.split("!_!");
            List<String> serpFilterKeywordsList = Arrays.stream(arr).collect(Collectors.toList());
            rankExtractJsonSearchInfo.setSerpFilterKeywords(serpFilterKeywordsList);
        }

        //https://www.wrike.com/open.htm?id=1593439741
        rankExtractJsonSearchInfo.setKeywordDateAdded(info.getCreateDate());
        if(CollectionUtils.isNotEmpty(info.getTagList())){
            rankExtractJsonSearchInfo.setKeywordTags(info.getTagList());
        }

        if(CollectionUtils.isNotEmpty(info.getTagIdHierarchy())){
            List<String> tagHierarchyList = new ArrayList<>();
            for (List<Integer> tagTree: info.getTagIdHierarchy()){
                String tagHierarchy = StringUtils.join(tagTree.stream().map(x->tagMap.get(x)).collect(Collectors.toList()), "->");
//                log.info("======tagHierarchy: " + tagHierarchy);
                if(StringUtils.isNotBlank(tagHierarchy)){
                    tagHierarchyList.add(tagHierarchy);
                }
            }
            if(CollectionUtils.isNotEmpty(tagHierarchyList)){
                rankExtractJsonSearchInfo.setTagHierarchy(tagHierarchyList);
            }
        }

        if(CollectionUtils.isNotEmpty(info.getPlpUrlList())){
            rankExtractJsonSearchInfo.setPreferredPages(info.getPlpUrlList());
        }
        rankExtractJsonSearchInfo.setEstdTraffic(info.getEstdTraffic());
        rankExtractJsonSearchInfo.setShareOfVoice(info.getShareOfVoice());
        rankExtractJsonSearchInfo.setShareOfMarket(info.getShareOfMarket());

        // https://www.wrike.com/open.htm?id=1500665126
        if (CollectionUtils.isNotEmpty(info.getRelatedSearches())) {
            rankExtractJsonSearchInfo.setRelatedSearches(info.getRelatedSearches());
        }

        RankExtractJsonSerpFeaturesFlag rankExtractJsonSerpFeaturesFlag = new RankExtractJsonSerpFeaturesFlag();
        rankExtractJsonSerpFeaturesFlag.setApp(info.getAppFlg());
        rankExtractJsonSerpFeaturesFlag.setImg(info.getImgFlg());
        rankExtractJsonSerpFeaturesFlag.setNews(info.getNewsFlg());
        rankExtractJsonSerpFeaturesFlag.setVideo(info.getVideoFlg());
        rankExtractJsonSerpFeaturesFlag.setLocalListing(info.getLlFlg());
        rankExtractJsonSerpFeaturesFlag.setPpcAds(info.getPpcFlg());
        rankExtractJsonSerpFeaturesFlag.setAnswerBox(info.getAnswerBoxFlg());
        rankExtractJsonSerpFeaturesFlag.setHotel(info.getHotelFlag());
        rankExtractJsonSerpFeaturesFlag.setFlights(info.getFlightSearchFlg());
        rankExtractJsonSerpFeaturesFlag.setPla(info.getPla_flg());
        rankExtractJsonSerpFeaturesFlag.setKnowledgeGraph(info.getKnog_flg());
        rankExtractJsonSerpFeaturesFlag.setFromsourcesacrosstheweb(info.getFromsourcesacrossthewebFlag());
        rankExtractJsonSerpFeaturesFlag.setFindresultson(info.getFindresultsonFlag());
        rankExtractJsonSerpFeaturesFlag.setPopulardestinations(info.getPopulardestinationsFlag());
        rankExtractJsonSerpFeaturesFlag.setAio(info.getAioFlag());
        rankExtractJsonSerpFeaturesFlag.setPopularRecipes(info.getPopularRecipesFlag());
        rankExtractJsonSerpFeaturesFlag.setPopularStore(info.getPopularStoreFlag());
        rankExtractJsonSerpFeaturesFlag.setDiscussionsandforums(info.getDiscussionsandforumsFlag());
        rankExtractJsonSerpFeaturesFlag.setBuyingguide(info.getBuyingguideFlag());

        List<String> rankExtractJsonPeopleAlsoAskStrList = info.getPeopleAlsoAskList();
        List<RankExtractJsonPeopleAlsoAsk> rankExtractJsonPeopleAlsoAskList = new ArrayList<>();
//        log.info("====PAAListSize:" + rankExtractJsonPeopleAlsoAskStrList.size());
        if(CollectionUtils.isNotEmpty(rankExtractJsonPeopleAlsoAskStrList)){
            for(String paaStr: rankExtractJsonPeopleAlsoAskStrList){
                String[] plaArr =  paaStr.split("!_!");
                if(plaArr.length < 2){
//                    log.info("*****************plaArr:" + gson.toJson(plaArr));
                }else {
                    RankExtractJsonPeopleAlsoAsk extractJsonPeopleAlsoAsk = new RankExtractJsonPeopleAlsoAsk();
                    extractJsonPeopleAlsoAsk.setUrl(plaArr[0]);
                    extractJsonPeopleAlsoAsk.setTitle(plaArr[1]);
                    rankExtractJsonPeopleAlsoAskList.add(extractJsonPeopleAlsoAsk);
                }
            }
        }

        //=============== thingsToKnow ====================
        List<RankExtractJsonThingsToKnow> thingsToKnowList = new ArrayList<RankExtractJsonThingsToKnow>();
        if(StringUtils.isNotBlank(info.getThingsToKnow()) && StringUtils.contains(info.getThingsToKnow(), "#_#")) {
            String[] qArr = info.getThingsToKnow().split("@_@");
            if(qArr.length > 0) {
                Integer rank = 1;
                for (String s : qArr) {
                    String[] tArr = s.split("#_#");
                    if(tArr != null && tArr.length >= 2) {
                        RankExtractJsonThingsToKnow rankExtractJsonThingsToKnow = new RankExtractJsonThingsToKnow();

                        rankExtractJsonThingsToKnow.setTitle(getStringValue(tArr[0]));
                        rankExtractJsonThingsToKnow.setUrl(StringUtils.substringBefore(getStringValue(tArr[1]), "#"));
                        rankExtractJsonThingsToKnow.setRank(rank);

                        rank++;
                        thingsToKnowList.add(rankExtractJsonThingsToKnow);
                    }
                }
            }
        }

        if(CollectionUtils.isNotEmpty(thingsToKnowList)){
            rankExtractJsonVO.setThingsToKnow(thingsToKnowList);
        }

        if(StringUtils.isNotBlank(info.getRefineByTitle()) ||
                CollectionUtils.isNotEmpty(info.getRefineByDetails())){
            RankExtractJsonRefineBy rankExtractJsonRefineBy = new RankExtractJsonRefineBy();
            rankExtractJsonRefineBy.setTitle(info.getRefineByTitle());
            rankExtractJsonRefineBy.setDetail(info.getRefineByDetails());
            rankExtractJsonVO.setRefineBy(rankExtractJsonRefineBy);
        }

        List<RankExtractJsonRankings> rankExtractJsonRankingsList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(detailRankList)){
            for(CLRankingDetailEntity detailRank: detailRankList){
                RankExtractJsonRankings rankExtractJsonRankings = new RankExtractJsonRankings();
                rankExtractJsonRankings.setUrl(StringUtils.isBlank(detailRank.getUrl()) ? "" : ExtractService.formatGoogleUrl(detailRank.getUrl()));
                rankExtractJsonRankings.setType(RankTypeManager.getUrlTypeName(detailRank.getType()));
                Integer trueRank = 0;
                if (detailRank.getTrueRank().equals(0)) {
                    trueRank = 101;
                } else {
                    trueRank = detailRank.getTrueRank();
                }
                Integer webRank = 0;
                if (detailRank.getWebRank().equals(0)) {
                    webRank = 101;
                } else {
                    webRank = detailRank.getWebRank();
                }
                rankExtractJsonRankings.setTrueRank(trueRank);
                if(webRank != 101){
                    rankExtractJsonRankings.setWebRank(webRank);
                }

                if(detailRank.getVisualRank() != null && detailRank.getVisualRank().intValue() != 0){
                    rankExtractJsonRankings.setVisualRank(detailRank.getVisualRank());
                }
                if(detailRank.getPixelDepth() != null){
                    rankExtractJsonRankings.setPixelDepth(detailRank.getPixelDepth());
                }

                String title = StringUtils.isBlank(detailRank.getLabel()) ? "-" : CommonUtils.formatTitleMeta(detailRank.getLabel());
                String meta = StringUtils.isBlank(detailRank.getMeta()) ? "-" : CommonUtils.formatTitleMeta(detailRank.getMeta());

                RankExtractJsonUrlInformation rankExtractJsonUrlInformation = new RankExtractJsonUrlInformation();
                if(StringUtils.isNotBlank(title) && !title.equals("-")){
                    rankExtractJsonUrlInformation.setTitle(title);
                }
                if(StringUtils.isNotBlank(meta) && !meta.equals("-")){
                    rankExtractJsonUrlInformation.setMeta(meta);
                }

//                rankExtractJsonUrlInformation.setPrice(detailRank.getPriceNumber());
//                if(!detailRank.getRatingNumber().equals("0")){
//                    rankExtractJsonUrlInformation.setRating(detailRank.getRatingNumber());
//                }

                if(StringUtils.isNotBlank(detailRank.getPriceNumber())){
                    rankExtractJsonUrlInformation.setPriceFlag("y");
                }else {
                    rankExtractJsonUrlInformation.setPriceFlag("n");
                }
                if(!detailRank.getRatingNumber().equals("0")){
                    rankExtractJsonUrlInformation.setRatingFlag("y");
                }else {
                    rankExtractJsonUrlInformation.setRatingFlag("n");
                }

                rankExtractJsonUrlInformation.setCouponFlag(detailRank.getCouponFlag());
                rankExtractJsonRankings.setUrlInformation(rankExtractJsonUrlInformation);

                //subranks
                if(CollectionUtils.isNotEmpty(subRankList)){
                    List<RankExtractJsonSubRanks> subRanksList = new ArrayList<>();

                    for(CLRankingDetailEntity subrank: subRankList){

                        int subTrueRank = subrank.getTrueRank();
                        if(trueRank != subTrueRank){
                            continue;
                        }
                        RankExtractJsonSubRanks rankExtractJsonSubRanks = new RankExtractJsonSubRanks();
                        rankExtractJsonSubRanks.setSubrank(subrank.getSubRank());
                        String subRankUrl = StringUtils.isBlank(subrank.getSubRankUrl()) ? "" : ExtractService.formatGoogleUrl(subrank.getSubRankUrl());
                        if(StringUtils.isNotBlank(subRankUrl)){
                            rankExtractJsonSubRanks.setUrl(subRankUrl);
                        }

                        String subTitle = StringUtils.isBlank(subrank.getSubRankLabel()) ? "-" : CommonUtils.formatTitleMeta(subrank.getSubRankLabel());
                        String subMeta = StringUtils.isBlank(subrank.getSubRankMeta()) ? "-" : CommonUtils.formatTitleMeta(subrank.getSubRankMeta());
                        if(StringUtils.isNotBlank(subTitle) && !subTitle.equals("-")){
                            rankExtractJsonSubRanks.setTitle(subTitle);
                        }
                        if(StringUtils.isNotBlank(subMeta) && !subMeta.equals("-")){
                            rankExtractJsonSubRanks.setMeta(subMeta);
                        }

                        //https://www.wrike.com/open.htm?id=1610617922
                        if(StringUtils.isNotBlank(subrank.getSubrankRating())){
                            rankExtractJsonSubRanks.setRating(subrank.getSubrankRating());
                        }
                        if(StringUtils.isNotBlank(subrank.getSubrankPrice())){
                            rankExtractJsonSubRanks.setPrice(subrank.getSubrankPrice());
                        }
                        if(StringUtils.isNotBlank(subrank.getSubrankTagName())){
                            rankExtractJsonSubRanks.setTagName(subrank.getSubrankTagName());
                        }

                        subRanksList.add(rankExtractJsonSubRanks);
                    }
                    if(CollectionUtils.isNotEmpty(subRanksList)){
                        rankExtractJsonRankings.setSubRanks(subRanksList);
                    }

                }
                rankExtractJsonRankingsList.add(rankExtractJsonRankings);
                rankExtractJsonVO.setRankings(rankExtractJsonRankingsList);
            }
        }

        //
        rankExtractJsonVO.setInfo(extractJsonInfo);
        rankExtractJsonVO.setSearchParameters(rankExtractJsonSearchParameters);
        rankExtractJsonVO.setSearchInfo(rankExtractJsonSearchInfo);
        rankExtractJsonVO.setSerpFeaturesFlag(rankExtractJsonSerpFeaturesFlag);
        if(CollectionUtils.isNotEmpty(rankExtractJsonPeopleAlsoAskList)){
            rankExtractJsonVO.setPeopleAlsoAsk(rankExtractJsonPeopleAlsoAskList);
        }

        if (info.getBrandMap() != null && !info.getBrandMap().isEmpty()) {
            rankExtractJsonVO.setBrandSerpFeatures(Arrays.asList(info.getBrandMap()));
        }
        if (info.getNonbrandMap() != null && !info.getNonbrandMap().isEmpty()) {
            rankExtractJsonVO.setSerpFeatures(Arrays.asList(info.getNonbrandMap()));
        }

        return rankExtractJsonVO;
    }


    private String getStringValue(Object str) {
        return (str == null || StringUtils.isBlank(String.valueOf(str))) ? "" : str.toString().replaceAll(BAD_CHAR, "");
    }


    /**
     * 通用于所有类型导出，groupId设置为120
     * domainId
     * device
     * 导出类型
     *
     *
     *
     *
     *
     * @return
     */
    public List<ExtractScriptInstanceEntity> getNeedExtractListGroup120() {

//        Date processingDate = new Date();
        LocalDate currentDate = LocalDate.now();
        Date processingDate = Date.from(currentDate.atStartOfDay(ZoneId.systemDefault()).toInstant());//程序运行日期,去掉时分秒

        Date extractDate = null;//实际导出日期
        Integer extractDateInt = null;//实际导出日期

        Calendar calendar = Calendar.getInstance();
        int currentHour = calendar.get(Calendar.HOUR_OF_DAY);

        Date yesterday = DateUtils.addDays(processingDate, -1);

        List<ExtractScriptInstanceEntity> resultList = new ArrayList<>();
        try {
            List<ExtractScriptConfigEntity> extractScriptConfigList = extractScriptConfigDAO.getConfigListWithGroupId(AUTO_EXTRACT_NORMAL_GROUP_ID);
            if (CollectionUtils.isEmpty(extractScriptConfigList)) {
                log.warn("===config not exist!");
                return null;
            }

            Integer frequency = null;
            Integer StartProcessHour = null;
            Integer ExpectedProcessHour = null;
            String mailTitle = null;
            String mailTo = null;
            String mailCc = null;

            for(ExtractScriptConfigEntity extractScriptConfig:  extractScriptConfigList){

                int configId = extractScriptConfig.getId();
                frequency = extractScriptConfig.getDefaultFrequency();
                int defaultStartDay = extractScriptConfig.getDefaultStartDay();
                int defaultStartProcessHour = extractScriptConfig.getDefaultStartProcessHour();
                int defaultExpectedProcessHour = extractScriptConfig.getDefaultExpectedProcessHour();
                int processFrequency = extractScriptConfig.getDefaultFrequency();
                int processStartDay = extractScriptConfig.getDefaultStartDay();
                int processExpectedProcessHour = extractScriptConfig.getDefaultExpectedProcessHour();
                String className = extractScriptConfig.getFullQulifiedClass();

                List<ExtractScriptInstanceEntity> instanceList = extractScriptInstanceDAO.getInstanceByConfig(configId);
                if (CollectionUtils.isEmpty(instanceList)) {
                    log.warn("===instance not exist configId:" + configId);
                    continue;
                }

                for (ExtractScriptInstanceEntity instance : instanceList) {

                    int instanceId = instance.getId();
                    int domainId = instance.getOwnDomainId();

                    if(!checkDomainActive(domainId, instanceId)){
                        continue;
                    }

                    if(instance.getFrequency() != null){
                        frequency = instance.getFrequency().intValue();
                    }

                    if (instance.getStartDay() == null) {
                        instance.setStartDay(defaultStartDay);
                    } else {
                        processStartDay = instance.getStartDay();
                    }

                    if(instance.getStartProcessHour() == null){
                        instance.setStartProcessHour(defaultStartProcessHour);
                    }

                    log.info("===checkTargetDayAndInsert:" + instanceId + ",processingDate:" + processingDate);

                    //check start day
                    if(!checkProcessDate(frequency, processingDate, processStartDay)){
                        continue;
                    }

//                    log.info("==========ST INSTANCE:" + instanceId + ",className:" + className);
                    List<ExtractScriptDetailEntity> instanceDetailList = new ArrayList<>();
                    instance.setFullQulifiedClass(className);
                    if (instance.getExpectedProcessHour() != null) {
                        processExpectedProcessHour = instance.getExpectedProcessHour();
                    }

                    boolean isEnableExtractCurrentDay = instance.getEnableExtractCurrentDay();
                    if (frequency == ExtractScriptConfigEntity.FREQUENCY_DAILY && isEnableExtractCurrentDay) {//daily 当天导出
                        extractDate = processingDate;
                    }else if(frequency == ExtractScriptConfigEntity.FREQUENCY_DAILY && !isEnableExtractCurrentDay){//daily 当天导前一天的数据
                        extractDate = yesterday;
                    } else {
                        if (frequency == ExtractScriptConfigEntity.FREQUENCY_WEEKLY) {
                            extractDate = FormatUtils.getLastSundayForWeeklyDomainExtract(processingDate);
                        } else if (frequency == ExtractScriptConfigEntity.FREQUENCY_BI_WEEKLY) {//BI weekly 导出日期固定为周日
                            extractDate = DateUtils.addDays(processingDate, -14);
                        } else if (frequency == ExtractScriptConfigEntity.FREQUENCY_MONTHLY) {
                            extractDate = processingDate;
                        }else {//todo
                            log.error("******NOTCatch, Add NEW Logic!!");
                        }

                    }
                    extractDateInt = FormatUtils.formatDateToYyyyMmDd(extractDate);
                    log.info("======EDI:" + extractDateInt);

                    //当前天
                    checkExtractDate(configId, instanceId, extractDate, instanceDetailList, currentHour, instance.getStartProcessHour(), processExpectedProcessHour);
                    //除了当天失败的日期
                    getFailedDayList(configId, instanceId, extractDateInt, instanceDetailList);
                    //除了当天丢失的日期
                    getMissingDayList(configId, instanceId, extractDate, instanceDetailList, frequency);

                    instance.setScriptDetailList(instanceDetailList);
                    resultList.add(instance);
                }
            }

        } catch (Exception e) {
            e.printStackTrace();

        }
        return resultList;
    }


    public boolean checkDomainActive(int domainId, int instanceId){
        OwnDomainEntity ownDomainEntity =ownDomainEntityDAO.getById(domainId);
        if(ownDomainEntity == null){
            log.info("======domain suspend:" + domainId + ",instanceId:" + instanceId);
            extractScriptInstanceDAO.updateSuspendDomain(instanceId);
            String subject = "DISABLE suspend domain instance oid:" + domainId + ",instanceId:" + instanceId;
            sendMailReport(subject, subject);
            return false;
        }else {
            return true;
        }
    }

    private boolean checkProcessDate(int frequency, Date processingDate, int processStartDay) {
        if (frequency == ExtractScriptConfigEntity.FREQUENCY_WEEKLY) {

            Calendar cal = Calendar.getInstance();
            cal.setTime(processingDate);
            if (cal.get(Calendar.DAY_OF_WEEK) != processStartDay) {
                log.info("====weekly not in processingDate,skip processingDate:" + processingDate + ",processStartDay:" + processStartDay);
                return false;
            }

        } else if (frequency == ExtractScriptConfigEntity.FREQUENCY_BI_WEEKLY) {

            if (!seoclarity.backend.utils.DateUtils.isBiWeeklyDate(processingDate)) {
                log.info("====bi-weekly not in processingDate,skip processingDate:" + processingDate + ",processStartDay:" + processStartDay);
                return false;
            }

        } else if (frequency == ExtractScriptConfigEntity.FREQUENCY_MONTHLY) {
            Calendar cal = Calendar.getInstance();
            cal.setTime(processingDate);
            if (cal.get(Calendar.DAY_OF_MONTH) != processStartDay) {
                log.info("====monthly not in processingDate,skip processingDate:" + processingDate + ",processStartDay:" + processStartDay);
                return false;
            }
        }
        return true;
    }


    private void checkExtractDate(int configId, int instanceId, Date extractDate, List<ExtractScriptDetailEntity> instanceDetailList, int currentHour,int startProcessHour, int processExpectedProcessHour) throws Exception{
        int extractDateInt = FormatUtils.formatDateToYyyyMmDd(extractDate);
        ExtractScriptDetailEntity extractScriptDetail = extractScriptDetailDAO.getUniqueExtractScriptDetail(configId, instanceId, extractDateInt);
        if (extractScriptDetail != null && extractScriptDetail.getStatus() == ExtractScriptDetailEntity.STATUS_SUCCESS) {
            log.info("===already extract extractDate Suc,skip configId:" + configId + ",instanceId:" + instanceId + ",extractDate:" + extractDateInt);
        } else if (extractScriptDetail != null) {//当天失败重跑
            log.info("===rerun extractDate:" + extractDate);
            boolean isExpectedProcessHour = currentHour >= processExpectedProcessHour ? true : false;
            extractScriptDetail.setIsExpectedProcessHour(isExpectedProcessHour);

            instanceDetailList.add(extractScriptDetail);
        } else {//导出当天
            if (currentHour < startProcessHour) {
                log.info("====not after defaultStartProcessHour,skip!");
            }else {
                log.info("====addcurrentDay:" + extractDate);
                Date endDay = DateUtils.addDays(extractDate, 1);
                List<ExtractScriptDetailEntity> currentDayDetailList = insertForMissingDay(extractDate, endDay, null, configId, instanceId);
                instanceDetailList.addAll(currentDayDetailList);
            }
        }

    }


    /**
     * Obtains the failed dates excluding today (from the latest 30 records).
     * 获取除了当天失败的日期（最近30条记录）
     * @param configId
     * @param instanceId
     * @param extractDateInt
     * @param instanceDetailList
     * @throws Exception
     */
    private void getFailedDayList(int configId, int instanceId, int extractDateInt, List<ExtractScriptDetailEntity> instanceDetailList){
        List<ExtractScriptDetailEntity> failedDayList = extractScriptDetailDAO.getFailedDayListExceptTargetDate(configId, instanceId, extractDateInt);
        if(CollectionUtils.isNotEmpty(failedDayList)){
            log.info("=======failedDayList:"  + JSON.toJSONString(failedDayList));
            instanceDetailList.addAll(failedDayList);
        }else {
            log.info("===no failed date need to process.");
        }
    }

    /**
     * Obtain the missing dates within the last 7 records
     * 获取丢失的日期，查找7次记录内
     * @param configId
     * @param instanceId
     * @param extractDate
     * @param instanceDetailList
     * @throws Exception
     */
    private void getMissingDayList(int configId, int instanceId, Date extractDate, List<ExtractScriptDetailEntity> instanceDetailList, int frequency) throws Exception{
        int extractDateInt = FormatUtils.formatDateToYyyyMmDd(extractDate);
        int minDate = FormatUtils.formatDateToYyyyMmDd(DateUtils.addDays(extractDate, (QUERY_MAX_MISSING_DAY * -1)));
        List<Integer> existRecentDayList = extractScriptDetailDAO.getRecentDetailListByInstanceId(configId, instanceId, extractDateInt, minDate);
        if(CollectionUtils.isEmpty(existRecentDayList)){//最近没有一次记录判定为首次导出，不补充历史记录
            log.info("===========FirstExtract, do not add missingDay instanceId:" + instanceId);
        }else {
            Date startCheckDay = DateUtils.addDays(extractDate, (QUERY_MAX_MISSING_DAY * -1));
            log.info("========existRecentDayList:" + JSON.toJSON(existRecentDayList) + ",startCheckDay:" + startCheckDay);
            Integer minExistDate = getMinElementByList(existRecentDayList);
            if(minExistDate != null && minExistDate > FormatUtils.formatDateToYyyyMmDd(startCheckDay)){
                startCheckDay = FormatUtils.toDate(minExistDate.toString(), FormatUtils.DATE_FORMAT_YYYYMMDD);
            }
            List<ExtractScriptDetailEntity> missingDetailList = insertForMissingDayByFrequency(startCheckDay, extractDate, existRecentDayList, configId, instanceId, frequency);
            if (CollectionUtils.isNotEmpty(missingDetailList)) {
                instanceDetailList.addAll(missingDetailList);
            }
        }
    }

    private List<ExtractScriptDetailEntity> insertForMissingDayByFrequency(Date startDay, Date endDay, List<Integer> successDayList, int configId, int instanceId, int frequency) throws Exception {
        log.info("======startDay:" + startDay + ",endDay:" + endDay);
        List<ExtractScriptDetailEntity> instanceDetailList = new ArrayList<>();

        int addDayAmount = 1;
        if(frequency == ExtractScriptConfigEntity.FREQUENCY_DAILY){
            addDayAmount = 1;
        } else if (frequency == ExtractScriptConfigEntity.FREQUENCY_WEEKLY) {
            addDayAmount = 7;
        } else if (frequency == ExtractScriptConfigEntity.FREQUENCY_BI_WEEKLY) {
            addDayAmount = 14;
        }

        while (startDay.compareTo(endDay) < 0) {

            int rankDate = FormatUtils.formatDateToYyyyMmDd(startDay);
            if (CollectionUtils.isNotEmpty(successDayList) && successDayList.contains(rankDate)) {
                log.info("===skip success rankDate:" + rankDate);

                if(frequency == ExtractScriptConfigEntity.FREQUENCY_MONTHLY){
                    startDay = FormatUtils.addOneMonthToDate(startDay);
                }else {
                    startDay = DateUtils.addDays(startDay, addDayAmount);
                }
                continue;
            } else {
                log.info("===missing rankDate:" + rankDate);
            }

            ExtractScriptDetailEntity missingDetail = new ExtractScriptDetailEntity();
            missingDetail.setExtractScriptId(configId);
            missingDetail.setExtractInstanceId(instanceId);
            missingDetail.setTargetDate(rankDate);
            missingDetail.setStatus(ExtractScriptDetailEntity.STATUS_PROCESSING);
            missingDetail.setStartedTime(new Date());
            missingDetail.setServerHostname(InetAddress.getLocalHost().getHostAddress());
            missingDetail.setServerPath(ExtractService.getServerPath());
            missingDetail.setIsExpectedProcessHour(true);

            int detailId = insetDetail(missingDetail);
            missingDetail.setId(detailId);

            if(frequency == ExtractScriptConfigEntity.FREQUENCY_MONTHLY){
                startDay = FormatUtils.addOneMonthToDate(startDay);
            }else {
                startDay = DateUtils.addDays(startDay, addDayAmount);
            }
            instanceDetailList.add(missingDetail);
        }
        return instanceDetailList;
    }

    private Integer getMinElementByList(List<Integer> list){
        // 使用 Stream API 获取最小元素
        Optional<Integer> minElement = list.stream()
                .min(Integer::compareTo);

        // 检查是否找到最小元素
        if (minElement.isPresent()) {
            Integer minValue = minElement.get();
            log.info("minEleIs: " + minValue);
            return minValue;
        } else {
            log.info("list empty，No minEleIs");
            return null;
        }
    }

}
