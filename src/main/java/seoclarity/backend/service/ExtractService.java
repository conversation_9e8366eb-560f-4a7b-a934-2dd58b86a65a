package seoclarity.backend.service;

import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.stereotype.Component;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.actonia.extract.ExtractScriptConfigDAO;
import seoclarity.backend.dao.actonia.extract.ExtractScriptDetailDAO;
import seoclarity.backend.dao.actonia.extract.ExtractScriptInstanceDAO;
import seoclarity.backend.dao.actonia.extract.RankQcStateDAO;
import seoclarity.backend.entity.actonia.DomainSearchEngineRelEntity;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.actonia.extract.*;
import seoclarity.backend.utils.*;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.net.InetAddress;
import java.util.*;

@Component
@CommonsLog
public class ExtractService {

    protected final static String PROJECT_NAME = "clarity-backend-scripts";

    @Resource
    private ExtractScriptConfigDAO extractScriptConfigDAO;
    @Resource
    private ExtractScriptInstanceDAO extractScriptInstanceDAO;
    @Resource
    private ExtractScriptDetailDAO extractScriptDetailDAO;
    @Resource
    private RankQcStateDAO qcStateDAO;
    @Resource
    private EmailSenderComponent emailSenderComponent;
    @Resource
    private OwnDomainEntityDAO ownDomainEntityDAO;
    @Resource
    private ScKeywordRankManager scKeywordRankManager;
    @Resource
    private ZeptoMailSenderComponent zeptoMailSenderComponent;

    public List<ExtractScriptInstanceEntity> getNeedRunExtract(int category, String fullQulifiedClass,
                                                               String specialCategory, int frequency, Date processingDate) {

        log.info("=====getNeedRunExtract category:" + category + ",fullQulifiedClass:" + fullQulifiedClass + ",specialCategory:" + specialCategory + ",frequency:" + frequency + ",processingDate:" + processingDate);

        Calendar calendar = Calendar.getInstance();
        int currentHour = calendar.get(Calendar.HOUR_OF_DAY);

        List<ExtractScriptInstanceEntity> resultList = new ArrayList<>();
        try {
            ExtractScriptConfigEntity extractScriptConfigEntity =
                    extractScriptConfigDAO.getUniqueExtractScriptConfig(category, PROJECT_NAME, fullQulifiedClass, specialCategory, frequency);
            if (extractScriptConfigEntity == null) {
                log.warn("===config not exist!");
                return null;
            }

            int configId = extractScriptConfigEntity.getId();
            int defaultFrequency = extractScriptConfigEntity.getDefaultFrequency();
            int defaultStartDay = extractScriptConfigEntity.getDefaultStartDay();
            int defaultStartProcessHour = extractScriptConfigEntity.getDefaultStartProcessHour();
            int defaultExpectedProcessHour = extractScriptConfigEntity.getDefaultExpectedProcessHour();

            int processFrequency = extractScriptConfigEntity.getDefaultFrequency();
            int processStartDay = extractScriptConfigEntity.getDefaultStartDay();
            int processExpectedProcessHour = extractScriptConfigEntity.getDefaultExpectedProcessHour();

            List<ExtractScriptInstanceEntity> instanceList = extractScriptInstanceDAO.getInstanceByConfig(configId);
            if (CollectionUtils.isEmpty(instanceList)) {
                log.warn("===instance not exist configId:" + configId);
                return null;
            }

            Date yesterday = DateUtils.addDays(processingDate, -1);
            Date targetDate = processingDate;
            for (ExtractScriptInstanceEntity instance : instanceList) {

                int rankDate = FormatUtils.formatDateToYyyyMmDd(processingDate);

                List<ExtractScriptDetailEntity> instanceDetailList = new ArrayList<>();
                int instanceId = instance.getId();
                int domainId = instance.getOwnDomainId();
                OwnDomainEntity ownDomainEntity =ownDomainEntityDAO.getById(domainId);
                if(ownDomainEntity == null){
                    log.info("======domain suspend:" + domainId + ",instanceId:" + instanceId);
                    extractScriptInstanceDAO.updateSuspendDomain(instanceId);
                    String subject = "DISABLE suspend domain instance oid:" + domainId + ",instanceId:" + instanceId;
                    sendMailReport(subject, subject);
                    continue;
                }

                boolean isEnableExtractCurrentDay = instance.getEnableExtractCurrentDay();
                if (isEnableExtractCurrentDay) {
                    ExtractScriptDetailEntity extractScriptDetail = extractScriptDetailDAO.getUniqueExtractScriptDetail(configId, instanceId, rankDate);
                    if (extractScriptDetail != null && extractScriptDetail.getStatus() == ExtractScriptDetailEntity.STATUS_SUCCESS) {
                        log.info("===already extract successful , skip configId:" + configId + ",instanceId:" + instanceId + ",rankDate:" + rankDate);
                        continue;
                    } else if (extractScriptDetail != null) {
                        System.out.println("===rerun today:" + instanceId);
                        //check send email time
                        if (instance.getExpectedProcessHour() != null) {
                            processExpectedProcessHour = instance.getExpectedProcessHour();
                        }
                        boolean isExpectedProcessHour = currentHour >= processExpectedProcessHour ? true : false;
                        extractScriptDetail.setIsExpectedProcessHour(isExpectedProcessHour);
                        instanceDetailList.add(extractScriptDetail);

                        List<ExtractScriptDetailEntity> failedDayList = extractScriptDetailDAO.getFailedDayList(configId, instanceId);
                        if (CollectionUtils.isEmpty(failedDayList)) {

                            Integer maxSuccessDay = extractScriptDetailDAO.getLastSuccessDay(configId, instanceId);
                            if (maxSuccessDay != null) {
                                Date maxSuccessDate = DateUtils.addDays(FormatUtils.toDate(String.valueOf(maxSuccessDay), FormatUtils.DATE_FORMAT_YYYYMMDD), 1);
                                Date compareDate = FormatUtils.toDate(FormatUtils.formatDate(processingDate, FormatUtils.DATE_FORMAT_YYYYMMDD), FormatUtils.DATE_FORMAT_YYYYMMDD);

                                List<ExtractScriptDetailEntity> missingDetailList = insertForMissingDay(maxSuccessDate, compareDate, null, configId, instanceId);
                                if (CollectionUtils.isNotEmpty(missingDetailList)) {
                                    instanceDetailList.addAll(missingDetailList);
                                }

                            }

                            ExtractScriptDetailEntity extractScriptDetailEntity = checkTargetDayAndInsert(extractScriptConfigEntity, instance, processingDate, currentHour, frequency);
                            if (extractScriptDetailEntity == null) {
                                log.info("==not pass check configId:" + configId + ",instanceId:" + instanceId);
                            } else {
                                instanceDetailList.add(extractScriptDetailEntity);
                            }
                        } else {//process error date(auto rerun) skip check
                            for (ExtractScriptDetailEntity extractScriptDetailEntity : failedDayList) {
                                int errorTargetDate = extractScriptDetailEntity.getTargetDate();
                                if (rankDate == errorTargetDate) {
                                    continue;
                                }
                                System.out.println("===rerun error day:" + errorTargetDate);
                                instanceDetailList.addAll(failedDayList);
                            }


//                            Date maxFailedDay = DateUtils.addDays(FormatUtils.toDate(String.valueOf(failedDayList.get(failedDayList.size() - 1).getTargetDate()), FormatUtils.DATE_FORMAT_YYYYMMDD), 1);
//                            Date compareDate = FormatUtils.toDate(FormatUtils.formatDate(processingDate, FormatUtils.DATE_FORMAT_YYYYMMDD), FormatUtils.DATE_FORMAT_YYYYMMDD);
//
//                            List<Integer> successDayList = extractScriptDetailDAO.getSuccessDayList(configId, instanceId, FormatUtils.formatDateToYyyyMmDd(maxFailedDay));
//
//                            List<ExtractScriptDetailEntity> missingDetailList = insertForMissingDay(maxFailedDay, compareDate, successDayList, configId, instanceId);
//                            if (CollectionUtils.isNotEmpty(missingDetailList)) {
//                                instanceDetailList.addAll(missingDetailList);
//                            }
//
//                            ExtractScriptDetailEntity extractScriptDetailEntity = checkTargetDayAndInsert(extractScriptConfigEntity, instance, processingDate, currentHour, frequency);
//                            if (extractScriptDetailEntity == null) {
//                                log.info("==not pass check configId:" + configId + ",instanceId:" + instanceId);
//                            } else {
//                                instanceDetailList.add(extractScriptDetailEntity);
//                            }

                        }

                    } else {

                        List<ExtractScriptDetailEntity> failedDayList = extractScriptDetailDAO.getFailedDayList(configId, instanceId);
                        if (CollectionUtils.isEmpty(failedDayList)) {

                            Integer maxSuccessDay = extractScriptDetailDAO.getLastSuccessDay(configId, instanceId);
                            if (maxSuccessDay != null) {
                                Date maxSuccessDate = DateUtils.addDays(FormatUtils.toDate(String.valueOf(maxSuccessDay), FormatUtils.DATE_FORMAT_YYYYMMDD), 1);
                                Date compareDate = FormatUtils.toDate(FormatUtils.formatDate(processingDate, FormatUtils.DATE_FORMAT_YYYYMMDD), FormatUtils.DATE_FORMAT_YYYYMMDD);

                                List<ExtractScriptDetailEntity> missingDetailList = insertForMissingDay(maxSuccessDate, compareDate, null, configId, instanceId);
                                if (CollectionUtils.isNotEmpty(missingDetailList)) {
                                    instanceDetailList.addAll(missingDetailList);
                                }

                            }

                            ExtractScriptDetailEntity extractScriptDetailEntity = checkTargetDayAndInsert(extractScriptConfigEntity, instance, processingDate, currentHour, frequency);
                            if (extractScriptDetailEntity == null) {
                                log.info("==not pass check configId:" + configId + ",instanceId:" + instanceId);
                            } else {
                                instanceDetailList.add(extractScriptDetailEntity);
                            }


                        } else {//process error date(auto rerun) skip check
                            System.out.println("===rerun error day");
                            instanceDetailList.addAll(failedDayList);

                            Date maxFailedDay = DateUtils.addDays(FormatUtils.toDate(String.valueOf(failedDayList.get(failedDayList.size() - 1).getTargetDate()), FormatUtils.DATE_FORMAT_YYYYMMDD), 1);
                            Date compareDate = FormatUtils.toDate(FormatUtils.formatDate(processingDate, FormatUtils.DATE_FORMAT_YYYYMMDD), FormatUtils.DATE_FORMAT_YYYYMMDD);

                            List<Integer> successDayList = extractScriptDetailDAO.getSuccessDayList(configId, instanceId, FormatUtils.formatDateToYyyyMmDd(maxFailedDay));

                            List<ExtractScriptDetailEntity> missingDetailList = insertForMissingDay(maxFailedDay, compareDate, successDayList, configId, instanceId);
                            if (CollectionUtils.isNotEmpty(missingDetailList)) {
                                instanceDetailList.addAll(missingDetailList);
                            }

                            ExtractScriptDetailEntity extractScriptDetailEntity = checkTargetDayAndInsert(extractScriptConfigEntity, instance, processingDate, currentHour, frequency);
                            if (extractScriptDetailEntity == null) {
                                log.info("==not pass check configId:" + configId + ",instanceId:" + instanceId);
                            } else {
                                instanceDetailList.add(extractScriptDetailEntity);
                            }

                        }

                    }

                } else {

                    targetDate = yesterday;
                    calendar = Calendar.getInstance();
                    currentHour = calendar.get(Calendar.HOUR_OF_DAY);
                    rankDate = FormatUtils.formatDateToYyyyMmDd(targetDate);

                    ExtractScriptDetailEntity extractScriptDetail = extractScriptDetailDAO.getUniqueExtractScriptDetail(configId, instanceId, rankDate);
                    if (extractScriptDetail != null && extractScriptDetail.getStatus() == ExtractScriptDetailEntity.STATUS_SUCCESS) {
                        log.info("===already extract successful , skip configId:" + configId + ",instanceId:" + instanceId + ",rankDate:" + rankDate);
                        continue;
                    } else if (extractScriptDetail != null) {
                        //check send email time
                        if (instance.getExpectedProcessHour() != null) {
                            processExpectedProcessHour = instance.getExpectedProcessHour();
                        }
                        boolean isExpectedProcessHour = currentHour >= processExpectedProcessHour ? true : false;
                        extractScriptDetail.setIsExpectedProcessHour(isExpectedProcessHour);
                        instanceDetailList.add(extractScriptDetail);
                    } else {

                        List<ExtractScriptDetailEntity> failedDayList = extractScriptDetailDAO.getFailedDayList(configId, instanceId);
                        if (CollectionUtils.isEmpty(failedDayList)) {

                            Integer maxSuccessDay = extractScriptDetailDAO.getLastSuccessDay(configId, instanceId);
                            if (maxSuccessDay != null && frequency == ExtractScriptConfigEntity.FREQUENCY_DAILY) {
                                Date maxSuccessDate = DateUtils.addDays(FormatUtils.toDate(String.valueOf(maxSuccessDay), FormatUtils.DATE_FORMAT_YYYYMMDD), 1);
                                Date compareDate = FormatUtils.toDate(FormatUtils.formatDate(targetDate, FormatUtils.DATE_FORMAT_YYYYMMDD), FormatUtils.DATE_FORMAT_YYYYMMDD);

                                List<ExtractScriptDetailEntity> missingDetailList = insertForMissingDay(maxSuccessDate, compareDate, null, configId, instanceId);
                                if (CollectionUtils.isNotEmpty(missingDetailList)) {
                                    instanceDetailList.addAll(missingDetailList);
                                }

                            }

                            ExtractScriptDetailEntity extractScriptDetailEntity = checkTargetDayAndInsert(extractScriptConfigEntity, instance, targetDate, currentHour, frequency);
                            if (extractScriptDetailEntity == null) {
                                log.info("==not pass check configId:" + configId + ",instanceId:" + instanceId);
                            } else {
                                instanceDetailList.add(extractScriptDetailEntity);
                            }


                        } else {//process error date(auto rerun) skip check

                            instanceDetailList.addAll(failedDayList);
                            if (frequency == ExtractScriptConfigEntity.FREQUENCY_DAILY) {
                                Date maxFailedDay = DateUtils.addDays(FormatUtils.toDate(String.valueOf(failedDayList.get(failedDayList.size() - 1).getTargetDate()), FormatUtils.DATE_FORMAT_YYYYMMDD), 1);
                                Date compareDate = FormatUtils.toDate(FormatUtils.formatDate(targetDate, FormatUtils.DATE_FORMAT_YYYYMMDD), FormatUtils.DATE_FORMAT_YYYYMMDD);

                                List<Integer> successDayList = extractScriptDetailDAO.getSuccessDayList(configId, instanceId, FormatUtils.formatDateToYyyyMmDd(maxFailedDay));
                                List<ExtractScriptDetailEntity> missingDetailList = insertForMissingDay(maxFailedDay, compareDate, successDayList, configId, instanceId);
                                if (CollectionUtils.isNotEmpty(missingDetailList)) {
                                    instanceDetailList.addAll(missingDetailList);
                                }
                            }

                            //today
                            ExtractScriptDetailEntity extractScriptDetailEntity = checkTargetDayAndInsert(extractScriptConfigEntity, instance, targetDate, currentHour, frequency);
                            if (extractScriptDetailEntity == null) {
                                log.info("==not pass check configId:" + configId + ",instanceId:" + instanceId);
                            } else {
                                instanceDetailList.add(extractScriptDetailEntity);
                            }

                        }

                    }


                }

                instance.setScriptDetailList(instanceDetailList);

//                if (instance.getExpectedProcessHour() == null) {
//                    instance.setExpectedProcessHour(defaultExpectedProcessHour);
//                } else {
//                    processExpectedProcessHour = instance.getExpectedProcessHour();
//                }
//                boolean isExpectedProcessHour = currentHour >= processExpectedProcessHour ? true : false;
//                instance.setIsExpectedProcessHour(isExpectedProcessHour);

//                ExtractScriptDetailEntity extractScriptDetail = extractScriptDetailDAO.getUniqueExtractScriptDetail(configId, instanceId, rankDate);
//                if (extractScriptDetail != null && extractScriptDetail.getStatus() == ExtractScriptDetailEntity.STATUS_SUCCESS) {
//                    log.info("===already extract successful , skip configId:" + configId + ",instanceId:" + instanceId + ",rankDate:" + rankDate);
//                    continue;
//                } else if (extractScriptDetail != null) {
//                    instance.setScriptDetail(extractScriptDetail);
//                }


                resultList.add(instance);
            }


        } catch (Exception e) {
            e.printStackTrace();
            String subject = "FAILED " + fullQulifiedClass + " frequency:" + frequency + ",processingDate:" + processingDate;
            sendMailReport(subject, subject);
        }

        return resultList;
    }

    private List<ExtractScriptDetailEntity> insertForMissingDay(Date startDay, Date endDay, List<Integer> successDayList, int configId, int instanceId) throws Exception {

        List<ExtractScriptDetailEntity> instanceDetailList = new ArrayList<>();
        while (startDay.compareTo(endDay) < 0) {

            int rankDate = FormatUtils.formatDateToYyyyMmDd(startDay);
            if (CollectionUtils.isNotEmpty(successDayList) && successDayList.contains(rankDate)) {
                log.info("===skip success rankDate:" + rankDate);
                startDay = DateUtils.addDays(startDay, 1);
                continue;
            } else {
                log.info("===missing rankDate:" + rankDate);
            }

            ExtractScriptDetailEntity missingDetail = new ExtractScriptDetailEntity();
            missingDetail.setExtractScriptId(configId);
            missingDetail.setExtractInstanceId(instanceId);
            missingDetail.setTargetDate(rankDate);
            missingDetail.setStatus(ExtractScriptDetailEntity.STATUS_PROCESSING);
            missingDetail.setStartedTime(new Date());
            missingDetail.setServerHostname(InetAddress.getLocalHost().getHostAddress());
            missingDetail.setServerPath(ExtractService.getServerPath());
            missingDetail.setIsExpectedProcessHour(true);

            int detailId = insetDetail(missingDetail);
            missingDetail.setId(detailId);

            startDay = DateUtils.addDays(startDay, 1);
            instanceDetailList.add(missingDetail);
        }
        return instanceDetailList;
    }

    private ExtractScriptDetailEntity checkTargetDayAndInsert(ExtractScriptConfigEntity extractScriptConfigEntity, ExtractScriptInstanceEntity instance,
                                                              Date processingDate, int currentHour, int frequency) throws Exception {

        int defaultFrequency = extractScriptConfigEntity.getDefaultFrequency();
        int defaultStartDay = extractScriptConfigEntity.getDefaultStartDay();
        int defaultStartProcessHour = extractScriptConfigEntity.getDefaultStartProcessHour();
        int defaultExpectedProcessHour = extractScriptConfigEntity.getDefaultExpectedProcessHour();
        int instanceId = instance.getId();
        int processStartDay = defaultStartDay;
        int processExpectedProcessHour = defaultExpectedProcessHour;

        if ((instance.getStartProcessHour() == null && currentHour < defaultStartProcessHour) ||
                (instance.getStartProcessHour() != null && currentHour < instance.getStartProcessHour())) {
            log.info("====not after defaultStartProcessHour,skip!");
            return null;
        }

        if ((instance.getFrequency() == null && defaultFrequency != frequency) ||
                (instance.getFrequency() != null && instance.getFrequency() != frequency)) {
            log.info("====frequency not same,skip !" + instanceId);
            return null;
        }

        if (instance.getStartDay() == null) {
            instance.setStartDay(defaultStartDay);
        } else {
            processStartDay = instance.getStartDay();
        }

        log.info("===checkTargetDayAndInsert:" + instanceId + ",processingDate:" + processingDate);

        //check start day
        if (frequency == ExtractScriptConfigEntity.FREQUENCY_WEEKLY) {

            Calendar cal = Calendar.getInstance();
            cal.setTime(processingDate);
            if (cal.get(Calendar.DAY_OF_WEEK) != processStartDay) {
                log.info("====weekly not in processingDate,skip processingDate:" + processingDate + ",processStartDay:" + processStartDay);
                return null;
            }

        } else if (frequency == ExtractScriptConfigEntity.FREQUENCY_BI_WEEKLY) {

            if (!seoclarity.backend.utils.DateUtils.isBiWeeklyStartDate(processingDate)) {
                log.info("====bi-weekly not in processingDate,skip processingDate:" + processingDate + ",processStartDay:" + processStartDay);
                return null;
            }

        } else if (frequency == ExtractScriptConfigEntity.FREQUENCY_MONTHLY) {
            Calendar cal = Calendar.getInstance();
            cal.setTime(processingDate);
            if (cal.get(Calendar.DAY_OF_MONTH) != processStartDay) {
                log.info("====monthly not in processingDate,skip processingDate:" + processingDate + ",processStartDay:" + processStartDay);
                return null;
            }

        }

        int rankDate = FormatUtils.formatDateToYyyyMmDd(processingDate);
        ExtractScriptDetailEntity extractScriptDetail = new ExtractScriptDetailEntity();
        extractScriptDetail.setExtractScriptId(instance.getExtractScriptId());
        extractScriptDetail.setExtractInstanceId(instance.getId());
        extractScriptDetail.setTargetDate(rankDate);
        extractScriptDetail.setStatus(ExtractScriptDetailEntity.STATUS_PROCESSING);
        extractScriptDetail.setStartedTime(new Date());
        extractScriptDetail.setServerHostname(InetAddress.getLocalHost().getHostAddress());
        extractScriptDetail.setServerPath(ExtractService.getServerPath());
        if (instance.getExpectedProcessHour() != null) {
            processExpectedProcessHour = instance.getExpectedProcessHour();
        }
        boolean isExpectedProcessHour = currentHour >= processExpectedProcessHour ? true : false;
        extractScriptDetail.setIsExpectedProcessHour(isExpectedProcessHour);

        int detailId = insetDetail(extractScriptDetail);
        extractScriptDetail.setId(detailId);

        return extractScriptDetail;
    }

    public boolean isRankQcPassWithCheckEngine(int rankDate, int ownDomainId, int engineId, int languageId, int rankType, String device) {
        OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getById(ownDomainId);
        if (ownDomainEntity == null) {
            log.info("====domain not exist:" + ownDomainId);
            return true;
        }

        int frequence = 0;
        if (ownDomainEntity.getKeywordRankFrequency() != null && ownDomainEntity.getKeywordRankFrequency() == OwnDomainEntity.KEYWORD_RANK_FREQUENCY_DAILY) {
            frequence = 1;
        } else if (ownDomainEntity.getKeywordRankFrequency() != null && ownDomainEntity.getKeywordRankFrequency() == OwnDomainEntity.KEYWORD_RANK_FREQUENCY_WEEKLY) {
            frequence = 7;
            log.info("====not suppose weekly qc now!");
            return true;
        } else {
            log.info("====no logic for qcTable:" + ownDomainId + ",fre:" + ownDomainEntity.getKeywordRankFrequency());
            return true;
        }

        boolean engineExist = false;
        String inputKey = engineId + "_" + languageId + "_" + (device.equalsIgnoreCase("desktop") ? "d" : "m");
        List<DomainSearchEngineRelEntity> domainSearchEngineRelList = scKeywordRankManager.getAllSERels(ownDomainEntity);
        for (DomainSearchEngineRelEntity dser : domainSearchEngineRelList) {
            String relKey = dser.getRankcheckSearchEngineId() + "_" + dser.getRankcheckSearchLanguageid() + "_" + dser.getDevice();
            log.info("====relKey:" + relKey);
            if (relKey.equals(inputKey)) {
                engineExist = true;
                break;
            }
        }

        if (!engineExist) {
            log.info("====engine not exist oid:" + ownDomainId + ",inputKey:" + inputKey);
            return true;
        }

        RankQcStateEntity rankQcStateEntity = new RankQcStateEntity();
        rankQcStateEntity.setRankDate(rankDate);
        rankQcStateEntity.setOwnDomainId(ownDomainId);
        rankQcStateEntity.setEngineId(engineId);
        rankQcStateEntity.setLanguageId(languageId);
        rankQcStateEntity.setDevice(device.equalsIgnoreCase("desktop") ? "d" : "m");
        rankQcStateEntity.setRankType(rankType);
        rankQcStateEntity.setFrequence(frequence);

        Integer qcStatus = qcStateDAO.getRankQcStatus(rankQcStateEntity);
        if (qcStatus != null && qcStatus == RankQcStateEntity.STATUS_OK) {
            return true;
        } else if (qcStatus != null && qcStatus == RankQcStateEntity.STATUS_NO_MANAGED_KEYWORDS) {
            log.info("=== no managed keyword,do not extract!");
            return true;
        } else {
            return false;
        }
    }

    public boolean isRankQcPass(int rankDate, int ownDomainId, int engineId, int languageId, String device, int rankType, int frequence) {
        RankQcStateEntity rankQcStateEntity = new RankQcStateEntity();
        rankQcStateEntity.setRankDate(rankDate);
        rankQcStateEntity.setOwnDomainId(ownDomainId);
        rankQcStateEntity.setEngineId(engineId);
        rankQcStateEntity.setLanguageId(languageId);
        rankQcStateEntity.setDevice(device.equalsIgnoreCase("desktop") ? "d" : "m");
        rankQcStateEntity.setRankType(rankType);
        rankQcStateEntity.setFrequence(frequence);

        Integer qcStatus = qcStateDAO.getRankQcStatus(rankQcStateEntity);
        if (qcStatus != null && qcStatus == RankQcStateEntity.STATUS_OK) {
            return true;
        } else if (qcStatus != null && qcStatus == RankQcStateEntity.STATUS_NO_MANAGED_KEYWORDS) {
            log.info("=== no managed keyword,do not extract!");
            return true;
        } else {
            return false;
        }
    }

    public int insetDetail(ExtractScriptDetailEntity extractScriptDetail) {
        return extractScriptDetailDAO.insert(extractScriptDetail);
    }

    public void updateForStart(ExtractScriptDetailEntity extractScriptDetail) {
        extractScriptDetailDAO.updateForStart(extractScriptDetail.getStatus(), extractScriptDetail.getStartedTime(), extractScriptDetail.getId());
    }


    public void updateForFailure(ExtractScriptDetailEntity extractScriptDetail) {
        extractScriptDetailDAO.updateForFailure(extractScriptDetail.getStatus(), extractScriptDetail.getElapsedSeconds(),
                extractScriptDetail.getEndedTime(), extractScriptDetail.getId(), extractScriptDetail.getFatalError());
    }

    public void updateForSuccess(ExtractScriptDetailEntity extractScriptDetail) {
        extractScriptDetailDAO.updateForSuccess(extractScriptDetail);
    }

    public static String getServerPath() throws IOException {
        File directory = new File("");
        String courseFile = directory.getCanonicalPath();
        return courseFile;
    }

    public void updateDetailAndSendEmail(ExtractScriptDetailEntity extractScriptDetail, int status, String fatalError, long startTime,
                                         int domainId, String device, boolean success, Date processDate) {

        extractScriptDetail.setStatus(status);
        if (StringUtils.isNotBlank(fatalError)) {
            extractScriptDetail.setFatalError(fatalError);
        }
        extractScriptDetail.setEndedTime(new Date());
        long elapsedSeconds = (System.currentTimeMillis() - startTime) / 1000;
        extractScriptDetail.setElapsedSeconds((int) elapsedSeconds);
        updateForFailure(extractScriptDetail);

        if (extractScriptDetail.getIsExpectedProcessHour()) {
            log.info("======send email alert !!");
            sendEmailAlert(domainId, device, success, processDate);
        } else {
            log.warn("======== not after ExpectedProcessHour, don't send email ");
        }

    }

    public void sendEmailAlert(int domainId, String device, boolean success, Date processDate) {
        String subject = getEmailSubject(domainId, device, success, processDate);
        String message = subject;
        sendMailReport(subject, message);
    }


    private String getEmailSubject(int domainId, String device, boolean success, Date processDate) {

        String subject = "";
        String status = success ? "Success" : "Failed";
        switch (domainId) {
            case 7006:
                subject = status + " Export " + domainId + " " + device + " Top 100 Daily Ranking " + FormatUtils.formatDateToYyyyMmDd(processDate);
                break;
            case 185:
                subject = status + " Export " + domainId + " " + device + " Top 100 Daily Ranking " + FormatUtils.formatDateToYyyyMmDd(processDate);
                break;
            default:
                subject = status + " Export " + domainId + " " + device + " RankExtract " + FormatUtils.formatDateToYyyyMmDd(processDate);
                break;
        }

        return subject;
    }

    public void sendMailReport(String subject, String message) {
        Map<String, Object> reportMap = new HashMap<String, Object>();
        reportMap.put("userName", "Jason"); // TODO
        reportMap.put("successMessage", message);
        String emailTo = "<EMAIL>"; // TODO
        String[] ccTo = new String[]{"<EMAIL>"};
//        String[] ccTo = null;

//        emailSenderComponent.sendMimeMultiPartMailAndBcc(emailTo, ccTo, subject, "mail_resource_operate_report.txt", "mail_resource_operate_report.html", reportMap);
        zeptoMailSenderComponent.sendMimeMultiPartZeptoMailAndBccByFunctionType(emailTo, ccTo, subject, "mail_resource_operate_report.txt", "mail_resource_operate_report.html", reportMap, null,
                ZeptoMailSenderComponent.FUNCTION_TYPE_BACKEND_INTERNAL, null, null);
    }

    public static String formatGoogleUrl(String url) {

        if (StringUtils.isBlank(url)) {
            return null;
        }
        if (url.contains("www.google.com") && url.contains("?")) {
            return url.substring(0, url.indexOf("?"));
        } else if (url.contains("google.com") && url.contains("?")) {
            return url.substring(0, url.indexOf("?"));
        } else {
            return url;
        }
    }


    public List<ExtractScriptDetailEntity> getSimpleNeedRunExtract(int category, String className, String specialCategory, int frequency,
                                                                   Date processingDate, Integer domainId, String device) {

        int rankDate = FormatUtils.formatDateToYyyyMmDd(processingDate);
        log.info("=====getSimpleNeedRunExtract category:" + category + ",fullQulifiedClass:" + className + ",specialCategory:"
                + specialCategory + ",frequency:" + frequency + ",domainId:" + domainId + ",device:" + device + ",processingDate:" + processingDate);

        String country = "";
        List<ExtractScriptDetailEntity> resultList = new ArrayList<>();
        try {
            ExtractScriptConfigEntity extractScriptConfigEntity =
                    extractScriptConfigDAO.getUniqueExtractScriptConfig(category, PROJECT_NAME, className, specialCategory, frequency);
            if (extractScriptConfigEntity == null) {
                log.warn("===config not exist!");
                return null;
            }

            int configId = extractScriptConfigEntity.getId();
            ExtractScriptInstanceEntity instance = extractScriptInstanceDAO.getUniqueInstance(configId, domainId, country,
                    ExtractScriptInstanceEntity.TAG_ID_FOR_DOMAIN_LEVEL, ExtractScriptInstanceEntity.RANK_TYPE_ALL, device, frequency);
            if (instance == null) {
                log.warn("===instance not exist configId:" + configId);
                return null;
            }

            int targetDate = FormatUtils.formatDateToYyyyMmDd(processingDate);
            int instanceId = instance.getId();

            //出错需要重跑的日期
            List<ExtractScriptDetailEntity> failedDayList = extractScriptDetailDAO.getFailedDayList(configId, instanceId);
            for (ExtractScriptDetailEntity extractScriptDetailEntity : failedDayList) {
                int failedTargetDate = extractScriptDetailEntity.getTargetDate();
//                if(rankDate == targetDate){
//                    continue;
//                }
                System.out.println("===rerun error day:" + failedTargetDate);
                resultList.add(extractScriptDetailEntity);
            }
            //丢失需要重跑的日期(包括第一次运行，只跑1天)
            List<ExtractScriptDetailEntity> rerunList = checkLast10DaysExtract(configId, instanceId, targetDate, frequency);
            if (CollectionUtils.isNotEmpty(rerunList)) {
                resultList.addAll(rerunList);
            }

            log.info("resultList size:" + resultList.size());
            return resultList;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("=====FAILED:" + className + " frequency:" + frequency + " " + rankDate);
        }
        return resultList;
    }

    public List<ExtractScriptDetailEntity> checkLast10DaysExtract(int extractScriptId, int extractInstanceId, int targetDay, int frequency) {
        Date endDate = FormatUtils.toDate(String.valueOf(targetDay), FormatUtils.DATE_FORMAT_YYYYMMDD);
        Date startDate = DateUtils.addDays(endDate, -10);
        int startDay = FormatUtils.formatDateToYyyyMmDd(startDate);
        try {
            List<Integer> existDayList = extractScriptDetailDAO.getExistDay(extractScriptId, extractInstanceId, startDay);
            Date firstRunDay = startDate;
            if (CollectionUtils.isNotEmpty(existDayList)) {
                firstRunDay = FormatUtils.toDate(String.valueOf(existDayList.get(0)), FormatUtils.DATE_FORMAT_YYYYMMDD);
            }
            List<ExtractScriptDetailEntity> missingDetailList = insertMissingDay(firstRunDay, endDate, existDayList, extractScriptId, extractInstanceId, frequency);
            if (CollectionUtils.isNotEmpty(missingDetailList)) {
                return missingDetailList;
            } else {
                return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private List<ExtractScriptDetailEntity> insertMissingDay(Date startDay, Date endDay, List<Integer> existDayList, int configId, int instanceId, int frequency) throws Exception {

        List<ExtractScriptDetailEntity> instanceDetailList = new ArrayList<>();
        int startDayInt = FormatUtils.formatDateToYyyyMmDd(startDay);
        if (CollectionUtils.isEmpty(existDayList)) {//first time run
            boolean canBeRun = checkProcessDate(frequency, endDay);
            if(!canBeRun){
                log.error("====can not run this day frequency:" + frequency + ",processDate:" + endDay);
                return instanceDetailList;
            }
            int rankDate = FormatUtils.formatDateToYyyyMmDd(endDay);
            log.info("===first time run, only add one day:" + rankDate);
            ExtractScriptDetailEntity missingDetail = new ExtractScriptDetailEntity();
            missingDetail.setExtractScriptId(configId);
            missingDetail.setExtractInstanceId(instanceId);
            missingDetail.setTargetDate(rankDate);
            missingDetail.setStatus(ExtractScriptDetailEntity.STATUS_PROCESSING);
            missingDetail.setStartedTime(new Date());
            missingDetail.setServerHostname(InetAddress.getLocalHost().getHostAddress());
            missingDetail.setServerPath(ExtractService.getServerPath());
            missingDetail.setIsExpectedProcessHour(true);
            int detailId = insetDetail(missingDetail);
            missingDetail.setId(detailId);
            instanceDetailList.add(missingDetail);
        } else {
            while (startDay.compareTo(endDay) <= 0) {
                boolean canBeRun = checkProcessDate(frequency, startDay);
                if(!canBeRun){
                    log.error("====can not run this day frequency:" + frequency + ",processDate:" + startDay);
                    startDay = DateUtils.addDays(startDay, 1);
                    continue;
                }
                int rankDate = FormatUtils.formatDateToYyyyMmDd(startDay);
                if (CollectionUtils.isNotEmpty(existDayList) && existDayList.contains(rankDate)) {
                    log.info("===skip exist rankDate:" + rankDate);
                    startDay = DateUtils.addDays(startDay, 1);
                    continue;
                } else {
                    log.info("===missing rankDate:" + rankDate);
                }

                ExtractScriptDetailEntity missingDetail = new ExtractScriptDetailEntity();
                missingDetail.setExtractScriptId(configId);
                missingDetail.setExtractInstanceId(instanceId);
                missingDetail.setTargetDate(rankDate);
                missingDetail.setStatus(ExtractScriptDetailEntity.STATUS_PROCESSING);
                missingDetail.setStartedTime(new Date());
                missingDetail.setServerHostname(InetAddress.getLocalHost().getHostAddress());
                missingDetail.setServerPath(ExtractService.getServerPath());
                missingDetail.setIsExpectedProcessHour(true);

                int detailId = insetDetail(missingDetail);
                missingDetail.setId(detailId);

                startDay = DateUtils.addDays(startDay, 1);
                instanceDetailList.add(missingDetail);
            }
        }
        return instanceDetailList;
    }

    /**
     * todo
     * @param frequency
     * @param processDate
     * @return
     */
    private boolean checkProcessDate(int frequency, Date processDate){

        if(frequency == ExtractScriptInstanceEntity.FREQUENCY_DAILY){
            return true;
        }else if(frequency == ExtractScriptInstanceEntity.FREQUENCY_WEEKLY){
            return FormatUtils.isSunday(processDate);//weekly 只导周日数据
        }else {
            log.info("===checkProcessDate FRE:" + frequency);
            return true;
        }
    }

    public List<ExtractScriptInstanceEntity> getNeedExtractDetailList() {
        Date processingDate = new Date();//默认导当天
        Integer rankDate = null;
        Calendar calendar = Calendar.getInstance();

        int currentHour = calendar.get(Calendar.HOUR_OF_DAY);
        Date yesterday = DateUtils.addDays(processingDate, -1);

        List<ExtractScriptInstanceEntity> resultList = new ArrayList<>();
        try {
            List<ExtractScriptConfigEntity> extractScriptConfigList = extractScriptConfigDAO.getConfigList();
            if (CollectionUtils.isEmpty(extractScriptConfigList)) {
                log.warn("===config not exist!");
                return null;
            }

            Integer frequency = null;
            Integer StartProcessHour = null;
            Integer ExpectedProcessHour = null;
            String mailTitle = null;
            String mailTo = null;
            String mailCc = null;

            for(ExtractScriptConfigEntity extractScriptConfig:  extractScriptConfigList){

                int configId = extractScriptConfig.getId();
                frequency = extractScriptConfig.getDefaultFrequency();
                int defaultStartDay = extractScriptConfig.getDefaultStartDay();
                int defaultStartProcessHour = extractScriptConfig.getDefaultStartProcessHour();
                int defaultExpectedProcessHour = extractScriptConfig.getDefaultExpectedProcessHour();
                int processFrequency = extractScriptConfig.getDefaultFrequency();
                int processStartDay = extractScriptConfig.getDefaultStartDay();
                int processExpectedProcessHour = extractScriptConfig.getDefaultExpectedProcessHour();
                String className = extractScriptConfig.getFullQulifiedClass();

                List<ExtractScriptInstanceEntity> instanceList = extractScriptInstanceDAO.getInstanceByConfig(configId);
                if (CollectionUtils.isEmpty(instanceList)) {
                    log.warn("===instance not exist configId:" + configId);
                    return null;
                }

                for (ExtractScriptInstanceEntity instance : instanceList) {

                    int instanceId = instance.getId();
                    int domainId = instance.getOwnDomainId();
                    OwnDomainEntity ownDomainEntity =ownDomainEntityDAO.getById(domainId);
                    if(ownDomainEntity == null){
                        log.info("======domain suspend:" + domainId + ",instanceId:" + instanceId);
                        extractScriptInstanceDAO.updateSuspendDomain(instanceId);
                        String subject = "DISABLE suspend domain instance oid:" + domainId + ",instanceId:" + instanceId;
                        sendMailReport(subject, subject);
                        continue;
                    }

                    if(instance.getFrequency() != null){
                        frequency = instance.getFrequency().intValue();
                    }
                    if(frequency != ExtractScriptInstanceEntity.FREQUENCY_DAILY){
                        log.info("====not daily , skip configId:" + configId + ",instanceId:" + instanceId + ",rankDate:" + rankDate);
                        continue;
                    }

                    List<ExtractScriptDetailEntity> instanceDetailList = new ArrayList<>();
                    instance.setFullQulifiedClass(className);
                    boolean isEnableExtractCurrentDay = instance.getEnableExtractCurrentDay();
                    if (isEnableExtractCurrentDay) {
                        processingDate = new Date();
                        rankDate = FormatUtils.formatDateToYyyyMmDd(processingDate);

                        ExtractScriptDetailEntity extractScriptDetail = extractScriptDetailDAO.getUniqueExtractScriptDetail(configId, instanceId, rankDate);
                        if (extractScriptDetail != null && extractScriptDetail.getStatus() == ExtractScriptDetailEntity.STATUS_SUCCESS) {
                            log.info("===already extract successful , skip configId:" + configId + ",instanceId:" + instanceId + ",rankDate:" + rankDate);
                            continue;
                        } else if (extractScriptDetail != null) {//当天失败重跑
                            System.out.println("===rerun today");
                            //check send email time
                            if (instance.getExpectedProcessHour() != null) {
                                processExpectedProcessHour = instance.getExpectedProcessHour();
                            }
                            boolean isExpectedProcessHour = currentHour >= processExpectedProcessHour ? true : false;
                            extractScriptDetail.setIsExpectedProcessHour(isExpectedProcessHour);
                            instanceDetailList.add(extractScriptDetail);

                            List<ExtractScriptDetailEntity> failedDayList = extractScriptDetailDAO.getFailedDayList(configId, instanceId);
                            if (CollectionUtils.isEmpty(failedDayList)) {//没有失败记录，查询最后一次成功日期和当前日期之间的日期有没有没导出的(可能是服务器或脚本失败导致程序没有运行起来的情况)

                                Integer maxSuccessDay = extractScriptDetailDAO.getLastSuccessDay(configId, instanceId);
                                if (maxSuccessDay != null) {
                                    Date maxSuccessDate = DateUtils.addDays(FormatUtils.toDate(String.valueOf(maxSuccessDay), FormatUtils.DATE_FORMAT_YYYYMMDD), 1);
                                    Date compareDate = FormatUtils.toDate(FormatUtils.formatDate(processingDate, FormatUtils.DATE_FORMAT_YYYYMMDD), FormatUtils.DATE_FORMAT_YYYYMMDD);

                                    List<ExtractScriptDetailEntity> missingDetailList = insertForMissingDay(maxSuccessDate, compareDate, null, configId, instanceId);
                                    if (CollectionUtils.isNotEmpty(missingDetailList)) {
                                        instanceDetailList.addAll(missingDetailList);
                                    }
                                }else{
                                    log.info("===no missing date need to process.");
                                }

                                ExtractScriptDetailEntity extractScriptDetailEntity = checkTargetDayAndInsert(extractScriptConfig, instance, processingDate, currentHour, frequency);
                                if (extractScriptDetailEntity == null) {
                                    log.info("==not pass check configId:" + configId + ",instanceId:" + instanceId);
                                } else {
                                    instanceDetailList.add(extractScriptDetailEntity);
                                }
                            } else {//process error date(auto rerun) skip check 重跑失败的日期
                                for (ExtractScriptDetailEntity extractScriptDetailEntity : failedDayList) {
                                    int targetDate = extractScriptDetailEntity.getTargetDate();
                                    if (rankDate == targetDate) {
                                        continue;
                                    }
                                    System.out.println("===rerun error day:" + targetDate);
                                    instanceDetailList.addAll(failedDayList);
                                }


//                            Date maxFailedDay = DateUtils.addDays(FormatUtils.toDate(String.valueOf(failedDayList.get(failedDayList.size() - 1).getTargetDate()), FormatUtils.DATE_FORMAT_YYYYMMDD), 1);
//                            Date compareDate = FormatUtils.toDate(FormatUtils.formatDate(processingDate, FormatUtils.DATE_FORMAT_YYYYMMDD), FormatUtils.DATE_FORMAT_YYYYMMDD);
//
//                            List<Integer> successDayList = extractScriptDetailDAO.getSuccessDayList(configId, instanceId, FormatUtils.formatDateToYyyyMmDd(maxFailedDay));
//
//                            List<ExtractScriptDetailEntity> missingDetailList = insertForMissingDay(maxFailedDay, compareDate, successDayList, configId, instanceId);
//                            if (CollectionUtils.isNotEmpty(missingDetailList)) {
//                                instanceDetailList.addAll(missingDetailList);
//                            }
//
//                            ExtractScriptDetailEntity extractScriptDetailEntity = checkTargetDayAndInsert(extractScriptConfigEntity, instance, processingDate, currentHour, frequency);
//                            if (extractScriptDetailEntity == null) {
//                                log.info("==not pass check configId:" + configId + ",instanceId:" + instanceId);
//                            } else {
//                                instanceDetailList.add(extractScriptDetailEntity);
//                            }

                            }

                        } else {//导出当天和失败的日期

                            List<ExtractScriptDetailEntity> failedDayList = extractScriptDetailDAO.getFailedDayList(configId, instanceId);
                            if (CollectionUtils.isEmpty(failedDayList)) {
                                Integer maxSuccessDay = extractScriptDetailDAO.getLastSuccessDay(configId, instanceId);
                                if (maxSuccessDay != null) {
                                    Date maxSuccessDate = DateUtils.addDays(FormatUtils.toDate(String.valueOf(maxSuccessDay), FormatUtils.DATE_FORMAT_YYYYMMDD), 1);
                                    Date compareDate = FormatUtils.toDate(FormatUtils.formatDate(processingDate, FormatUtils.DATE_FORMAT_YYYYMMDD), FormatUtils.DATE_FORMAT_YYYYMMDD);

                                    List<ExtractScriptDetailEntity> missingDetailList = insertForMissingDay(maxSuccessDate, compareDate, null, configId, instanceId);
                                    if (CollectionUtils.isNotEmpty(missingDetailList)) {
                                        instanceDetailList.addAll(missingDetailList);
                                    }
                                }

                                ExtractScriptDetailEntity extractScriptDetailEntity = checkTargetDayAndInsert(extractScriptConfig, instance, processingDate, currentHour, frequency);
                                if (extractScriptDetailEntity == null) {
                                    log.info("==not pass check configId:" + configId + ",instanceId:" + instanceId);
                                } else {
                                    instanceDetailList.add(extractScriptDetailEntity);
                                }

                            } else {//process error date(auto rerun) skip check
                                System.out.println("===rerun error day");
                                instanceDetailList.addAll(failedDayList);

                                Date maxFailedDay = DateUtils.addDays(FormatUtils.toDate(String.valueOf(failedDayList.get(failedDayList.size() - 1).getTargetDate()), FormatUtils.DATE_FORMAT_YYYYMMDD), 1);
                                Date compareDate = FormatUtils.toDate(FormatUtils.formatDate(processingDate, FormatUtils.DATE_FORMAT_YYYYMMDD), FormatUtils.DATE_FORMAT_YYYYMMDD);

                                List<Integer> successDayList = extractScriptDetailDAO.getSuccessDayList(configId, instanceId, FormatUtils.formatDateToYyyyMmDd(maxFailedDay));

                                List<ExtractScriptDetailEntity> missingDetailList = insertForMissingDay(maxFailedDay, compareDate, successDayList, configId, instanceId);
                                if (CollectionUtils.isNotEmpty(missingDetailList)) {
                                    instanceDetailList.addAll(missingDetailList);
                                }

                                ExtractScriptDetailEntity extractScriptDetailEntity = checkTargetDayAndInsert(extractScriptConfig, instance, processingDate, currentHour, frequency);
                                if (extractScriptDetailEntity == null) {
                                    log.info("==not pass check configId:" + configId + ",instanceId:" + instanceId);
                                } else {
                                    instanceDetailList.add(extractScriptDetailEntity);
                                }
                            }
                        }

                    } else {//当天导前一天的数据

                        processingDate = yesterday;
                        calendar = Calendar.getInstance();
                        currentHour = calendar.get(Calendar.HOUR_OF_DAY);
                        rankDate = FormatUtils.formatDateToYyyyMmDd(processingDate);

                        ExtractScriptDetailEntity extractScriptDetail = extractScriptDetailDAO.getUniqueExtractScriptDetail(configId, instanceId, rankDate);
                        if (extractScriptDetail != null && extractScriptDetail.getStatus() == ExtractScriptDetailEntity.STATUS_SUCCESS) {
                            log.info("===already extract successful , skip configId:" + configId + ",instanceId:" + instanceId + ",rankDate:" + rankDate);
                            continue;
                        } else if (extractScriptDetail != null) {
                            //check send email time
                            if (instance.getExpectedProcessHour() != null) {
                                processExpectedProcessHour = instance.getExpectedProcessHour();
                            }
                            boolean isExpectedProcessHour = currentHour >= processExpectedProcessHour ? true : false;
                            extractScriptDetail.setIsExpectedProcessHour(isExpectedProcessHour);
                            instanceDetailList.add(extractScriptDetail);
                        } else {

                            List<ExtractScriptDetailEntity> failedDayList = extractScriptDetailDAO.getFailedDayList(configId, instanceId);
                            if (CollectionUtils.isEmpty(failedDayList)) {

                                Integer maxSuccessDay = extractScriptDetailDAO.getLastSuccessDay(configId, instanceId);
                                if (maxSuccessDay != null && frequency == ExtractScriptConfigEntity.FREQUENCY_DAILY) {
                                    Date maxSuccessDate = DateUtils.addDays(FormatUtils.toDate(String.valueOf(maxSuccessDay), FormatUtils.DATE_FORMAT_YYYYMMDD), 1);
                                    Date compareDate = FormatUtils.toDate(FormatUtils.formatDate(processingDate, FormatUtils.DATE_FORMAT_YYYYMMDD), FormatUtils.DATE_FORMAT_YYYYMMDD);

                                    List<ExtractScriptDetailEntity> missingDetailList = insertForMissingDay(maxSuccessDate, compareDate, null, configId, instanceId);
                                    if (CollectionUtils.isNotEmpty(missingDetailList)) {
                                        instanceDetailList.addAll(missingDetailList);
                                    }

                                }

                                ExtractScriptDetailEntity extractScriptDetailEntity = checkTargetDayAndInsert(extractScriptConfig, instance, processingDate, currentHour, frequency);
                                if (extractScriptDetailEntity == null) {
                                    log.info("==not pass check configId:" + configId + ",instanceId:" + instanceId);
                                } else {
                                    instanceDetailList.add(extractScriptDetailEntity);
                                }


                            } else {//process error date(auto rerun) skip check

                                instanceDetailList.addAll(failedDayList);
                                if (frequency == ExtractScriptConfigEntity.FREQUENCY_DAILY) {
                                    Date maxFailedDay = DateUtils.addDays(FormatUtils.toDate(String.valueOf(failedDayList.get(failedDayList.size() - 1).getTargetDate()), FormatUtils.DATE_FORMAT_YYYYMMDD), 1);
                                    Date compareDate = FormatUtils.toDate(FormatUtils.formatDate(processingDate, FormatUtils.DATE_FORMAT_YYYYMMDD), FormatUtils.DATE_FORMAT_YYYYMMDD);

                                    List<Integer> successDayList = extractScriptDetailDAO.getSuccessDayList(configId, instanceId, FormatUtils.formatDateToYyyyMmDd(maxFailedDay));
                                    List<ExtractScriptDetailEntity> missingDetailList = insertForMissingDay(maxFailedDay, compareDate, successDayList, configId, instanceId);
                                    if (CollectionUtils.isNotEmpty(missingDetailList)) {
                                        instanceDetailList.addAll(missingDetailList);
                                    }
                                }

                                //
                                ExtractScriptDetailEntity extractScriptDetailEntity = checkTargetDayAndInsert(extractScriptConfig, instance, processingDate, currentHour, frequency);
                                if (extractScriptDetailEntity == null) {
                                    log.info("==not pass check configId:" + configId + ",instanceId:" + instanceId);
                                } else {
                                    instanceDetailList.add(extractScriptDetailEntity);
                                }
                            }
                        }
                    }
                    instance.setScriptDetailList(instanceDetailList);
                    resultList.add(instance);
                }
            }

        } catch (Exception e) {
            e.printStackTrace();

        }
        return resultList;
    }

}
