package seoclarity.backend.service;

import java.util.ArrayList;
import java.util.List;
import java.util.Properties;
import java.util.Set;

import org.apache.commons.lang.StringUtils;

import seoclarity.backend.dao.actonia.CompetitorDomainDailySummaryEntityDAO;
import seoclarity.backend.dao.actonia.CompetitorEntityDAO;
import seoclarity.backend.dao.actonia.DomainRankDailySummaryEntityDAO;
import seoclarity.backend.dao.actonia.DomainSearchEngineRelJdbcDAO;
import seoclarity.backend.dao.actonia.GroupTagCompetitorDailySummaryEntityDAO;
import seoclarity.backend.dao.actonia.GroupTagEntityDAO;
import seoclarity.backend.dao.actonia.GrouptagCompetitorRelDAO;
import seoclarity.backend.dao.actonia.KeywordEntityDAO;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.actonia.OwnDomainSettingEntityDAO;
import seoclarity.backend.dao.actonia.RankIndexParamEntityDAO;
import seoclarity.backend.dao.actonia.extract.ExtractScriptConfigDAO;
import seoclarity.backend.dao.actonia.extract.ExtractScriptDetailDAO;
import seoclarity.backend.dao.actonia.extract.ExtractScriptInstanceDAO;
import seoclarity.backend.dao.actonia.extract.RankQcStateDAO;
import seoclarity.backend.dao.clickhouse.kpcold.ClColdDailyRankingEntityDao;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.utils.CommonUtils;
import seoclarity.backend.utils.SpringBeanFactory;

public abstract class BaseService {

	protected GrouptagCompetitorRelDAO grouptagCompetitorRelDAO;

	protected OwnDomainEntityDAO ownDomainEntityDAO;

	protected KeywordEntityDAO keywordEntityDAO;

	protected DomainSearchEngineRelJdbcDAO domainSearchEngineRelJdbcDAO;

	protected CompetitorEntityDAO competitorEntityDAO;

//	protected MongoService mongoService;

	protected ScKeywordRankManager scKeywordRankManager;

	protected OwnDomainSettingEntityDAO ownDomainSettingEntityDAO;

	protected RankIndexParamEntityDAO rankIndexParamEntityDAO;
	
	protected GroupTagEntityDAO groupTagEntityDAO;
	
	protected ClColdDailyRankingEntityDao clColdDailyRankingEntityDao;
	
	protected ExtractService extractService;
	
	protected RankQcStateDAO rankQcStateDAO;
	
	protected ExtractScriptDetailDAO extractScriptDetailDAO;
	
	protected ExtractScriptConfigDAO extractScriptConfigDAO;

	protected ExtractScriptInstanceDAO extractScriptInstanceDAO;
	
	protected CompetitorDomainDailySummaryEntityDAO competitorDomainDailySummaryEntityDAO;

	protected GroupTagCompetitorDailySummaryEntityDAO groupTagCompetitorDailySummaryEntityDAO;

	protected DomainRankDailySummaryEntityDAO domainRankDailySummaryEntityDAO;
	
	
	
	public BaseService() {
		super();
		grouptagCompetitorRelDAO = SpringBeanFactory.getBean("grouptagCompetitorRelDAO");
		ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
		keywordEntityDAO = SpringBeanFactory.getBean("keywordEntityDAO");
		domainSearchEngineRelJdbcDAO = SpringBeanFactory.getBean("domainSearchEngineRelJdbcDAO");
		grouptagCompetitorRelDAO = SpringBeanFactory.getBean("grouptagCompetitorRelDAO");
		competitorEntityDAO = SpringBeanFactory.getBean("competitorEntityDAO");
//		mongoService = SpringBeanFactory.getBean("mongoService");
		scKeywordRankManager = SpringBeanFactory.getBean("scKeywordRankManager");
		ownDomainSettingEntityDAO = SpringBeanFactory.getBean("ownDomainSettingEntityDAO");
		rankIndexParamEntityDAO = SpringBeanFactory.getBean("rankIndexParamEntityDAO");
		groupTagEntityDAO = SpringBeanFactory.getBean("groupTagEntityDAO");
		clColdDailyRankingEntityDao = SpringBeanFactory.getBean("clColdDailyRankingEntityDao");
		extractService = SpringBeanFactory.getBean("extractService");
		rankQcStateDAO = SpringBeanFactory.getBean("rankQcStateDAO");
		extractScriptDetailDAO = SpringBeanFactory.getBean("extractScriptDetailDAO");
		extractScriptConfigDAO = SpringBeanFactory.getBean("extractScriptConfigDAO");
		extractScriptInstanceDAO = SpringBeanFactory.getBean("extractScriptInstanceDAO");
		competitorDomainDailySummaryEntityDAO = SpringBeanFactory.getBean("competitorDomainDailySummaryEntityDAO");
		groupTagCompetitorDailySummaryEntityDAO = SpringBeanFactory.getBean("groupTagCompetitorDailySummaryEntityDAO");
		domainRankDailySummaryEntityDAO = SpringBeanFactory.getBean("domainRankDailySummaryEntityDAO");
	}

	public abstract void process(OwnDomainEntity ownDomainEntity) throws Exception;

	/**
	 * fetch all domain should be processed
	 * 
	 * @throws Exception
	 */
	public void init() throws Exception {
		init(true);
	}
	
	public void init(boolean isDesc) throws Exception {
		Properties prop = new Properties();
		try {
			prop.load(BaseService.class.getResourceAsStream("/domain.properties"));
		} catch (Exception e) {
			System.out.println("no properties file found");
		}

		String execDomainIds = prop.getProperty("exec.domain");
		String notExecDomainIds = prop.getProperty("notexec.domain");
		
		List<OwnDomainEntity> allDomains = new ArrayList<OwnDomainEntity>();
		if (isDesc) {
			allDomains = ownDomainEntityDAO.getByIdDesc();
		} else {
			allDomains = ownDomainEntityDAO.getByIdAsc();
		}

		if (StringUtils.isNotBlank(execDomainIds)) {
			Set<Integer> domainSet = CommonUtils.getDomainSet(execDomainIds);
			for (OwnDomainEntity ownDomainEntity : allDomains) {
				if (domainSet.contains(ownDomainEntity.getId())) {
					System.out.println("Processing: " + ownDomainEntity.getDomain() + " ,OID: "
							+ ownDomainEntity.getId());

					process(ownDomainEntity);
				}
			}
		} else if (StringUtils.isNotBlank(notExecDomainIds)) {
			Set<Integer> domainSet = CommonUtils.getDomainSet(notExecDomainIds);
			for (OwnDomainEntity ownDomainEntity : allDomains) {
				if (!domainSet.contains(ownDomainEntity.getId())) {
					System.out.println("Processing: " + ownDomainEntity.getDomain() + " ,OID: "
							+ ownDomainEntity.getId());

					process(ownDomainEntity);
				}
			}
		} else {
			for (OwnDomainEntity ownDomainEntity : allDomains) {
				System.out.println("Processing: " + ownDomainEntity.getDomain() + " ,OID: " + ownDomainEntity.getId());
				process(ownDomainEntity);
			}
		}
	}
}
