package seoclarity.backend.service;

import org.apache.commons.lang.StringUtils;

import seoclarity.backend.entity.KeywordRankEntityVO;

public class RankTypeManager {

    public static final int TYPE_WEB_RESOURCE = 1;

    public static final int TYPE_IMGAGE = 2;

    public static final int TYPE_ADDRESS = 3;

    public static final int TYPE_VIDEO = 4;

    public static final int TYPE_NEWS = 5;

    public static final int TYPE_SHOPPING = 6;

    public static final int TYPE_LOCALLISTING = 7;

    public static final int TYPE_TWITTER = 8;

    public static final int TYPE_ANSWERBOX = 9;

    public static final int TYPE_JOB_URL = 10;

    public static final int TYPE_KNOWLEDGE = 11;

    public static final int TYPE_FLIGHTS = 12;

    public static final int TYPE_INTERESTING_FINDS = 13;

    public static final int TYPE_PAA = 14;

    public static final int TYPE_POPULAR_PRODUCTS = 15;

    public static final int TYPE_PPC = 16;

    public static final int TYPE_FINANCE = 17;

    public static final int TYPE_EVENT = 22;


    public static String getRankType(String url, int urlType) {

        if (StringUtils.isBlank(url)) {
            return "Web";
        }

        // maps.google.it/
        if (urlType == TYPE_ADDRESS || StringUtils.containsIgnoreCase(url, "maps.google.com")
                || StringUtils.containsIgnoreCase(url, "maps.google.")
                || StringUtils.containsIgnoreCase(url, "www.google.com/maps/")) {
            return "Local";
        } else if (urlType == TYPE_IMGAGE || StringUtils.containsIgnoreCase(url, "images.google.com")
                || StringUtils.containsIgnoreCase(url, "images.google.")) {
            return "Image";
        } else if (urlType == TYPE_NEWS || StringUtils.containsIgnoreCase(url, "news.google.com")
                || StringUtils.containsIgnoreCase(url, "news.google.")) {
            return "News";
        } else if (urlType == TYPE_SHOPPING
                || StringUtils.containsIgnoreCase(url, "www.google.com/shopping")
                || (StringUtils.containsIgnoreCase(url, "www.google.") && StringUtils.containsIgnoreCase(
                url, "/shopping"))) {
            return "Shopping";
        } else if (urlType == TYPE_VIDEO || StringUtils.containsIgnoreCase(url, "www.google.com/videohp")) {
            return "Video";
        } else if (urlType == TYPE_LOCALLISTING
                || (StringUtils.containsIgnoreCase(url, "www.google.") && StringUtils.containsIgnoreCase(
                url, "tbm=lcl"))) {
            return "LocalListing";
        } else if (StringUtils.containsIgnoreCase(url, "www.google.com")
                || StringUtils.containsIgnoreCase(url, "www.google.")
                || urlType == TYPE_TWITTER
//                || urlType == TYPE_ANSWERBOX
                || urlType == TYPE_JOB_URL
                ) {
            return "Universal";
        } else if (StringUtils.containsIgnoreCase(url, "flights.google.com") || StringUtils.containsIgnoreCase(url, "hotels.google.com")) {
            return "Universal";
        }

        return "Web";

    }

    public static String convertToNewDomainName(String domainName, int type, String uri) {
        String newDomainName = "";
        if (StringUtils.containsIgnoreCase(domainName, ".google.www")
                && type != KeywordRankEntityVO.TYPE_WEB_RESOURCE) {
            if (type == KeywordRankEntityVO.TYPE_ADDRESS) {
                newDomainName = "com.google.maps";
            } else if (type == KeywordRankEntityVO.TYPE_IMGAGE) {
                newDomainName = "com.google.images";
            } else if (type == KeywordRankEntityVO.TYPE_NEWS) {
                newDomainName = "com.google.news";
            } else if (type == KeywordRankEntityVO.TYPE_SHOPPING) {
                newDomainName = "com.google.shopping";
            } else if (type == KeywordRankEntityVO.TYPE_VIDEO) {
                newDomainName = "com.google.video";
            } else if (type == KeywordRankEntityVO.TYPE_JOB_URL) {
                newDomainName = "com.google.jobs";
            } else if (type == KeywordRankEntityVO.TYPE_LOCALLISTING) {
                newDomainName = "com.google.ll";
            } else {
                newDomainName = domainName;
            }
        } else {
            newDomainName = domainName;
        }

        // https://www.wrike.com/open.htm?id=*********
        if (StringUtils.containsIgnoreCase(newDomainName, ".google.www") || StringUtils.equals(newDomainName, "com.google.ll")) {
            if (StringUtils.contains(uri, "/travel/hotels/")) {
                newDomainName = "com.google.hotels";
            } else if (StringUtils.containsIgnoreCase(newDomainName, ".google.www")) {
                if (StringUtils.contains(uri, "/flights?") || StringUtils.endsWithIgnoreCase(uri, "/flights")) {
                    newDomainName = "com.google.flights";
                }
            }
        }

        return newDomainName;
    }

    public static boolean isWebRank(String url, int urlType) {

        return StringUtils.equals(getRankType(url, urlType), "Web");

    }

    public static String getUrlTypeName(int type) {
        switch (type) {
            case TYPE_WEB_RESOURCE:
                return "Web";
            case TYPE_IMGAGE:
                return "Image";
            case TYPE_ADDRESS:
                return "Address";
            case TYPE_VIDEO:
                return "Video";
            case TYPE_NEWS:
                return "News";
            case TYPE_SHOPPING:
                return "Shopping";
            case TYPE_LOCALLISTING:
                return "Locallisting";
            case TYPE_TWITTER:
                return "Twitter";
            case TYPE_ANSWERBOX:
                return "Answerbox";
            case TYPE_JOB_URL:
                return "Job";
            case TYPE_KNOWLEDGE:
                return "Knowledge Graph";
            case TYPE_FLIGHTS:
                return "Flight";
//            case TYPE_INTERESTING_FINDS:
//                return "Job";
//            case TYPE_PAA:
//                return "People Also Ask";
            case TYPE_POPULAR_PRODUCTS:
                return "Popular Products";
//            case TYPE_PPC:
//                return "PPC";
            case TYPE_FINANCE:
                return "Finance";
            case TYPE_EVENT:
                return "Event";
            default:
                return "Web";
        }

    }

    public static void main(String[] args) {
        // TODO Auto-generated method stub

    }

}