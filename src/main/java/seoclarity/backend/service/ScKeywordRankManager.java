package seoclarity.backend.service;

import java.net.URLDecoder;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;

import javax.annotation.Resource;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.stereotype.Component;

import seoclarity.backend.dao.actonia.*;
import seoclarity.backend.dao.rankcheck.KeywordCityAdwordsDataEntityDAO;
import seoclarity.backend.dao.rankcheck.SeoClarityKeywordEntityDAO;
import seoclarity.backend.dao.rankcheck.SeoClaritySearchEngineEntityDAO;
import seoclarity.backend.entity.EngineCountryLanguageMappingEntity;
import seoclarity.backend.entity.EngineLanguageErrorLogEntity;
import seoclarity.backend.entity.actonia.*;
import seoclarity.backend.entity.rankcheck.SeoClarityKeywordEntity;
import seoclarity.backend.entity.rankcheck.SeoClarityKeywordSearchEngineDomainRelationEntity;
import seoclarity.backend.onetime.MonthlyKeywordCleanUpCommand;
import seoclarity.backend.utils.EmailSenderComponent;
import seoclarity.backend.utils.SpringBeanFactory;

@Component
public class ScKeywordRankManager {

	private static final SimpleDateFormat SDF_YYYYMMDDHHMMSS = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	public static final int SEARCH_ENGINE_BING = 255;
	public static final int SEARCH_ENGINE_BING_MOBILE = 195;

	public static final int SEARCH_ENGINE_YAHOO = 100;

	public static final int SEARCH_ENGINE_YANDEX = 120;

	public static final int SEARCH_ENGINE_BAIDU = 150;

	public static final int SEARCH_ENGINE_BAIDUMOBILE = 155;

	public static final int SEARCH_ENGINE_NAVER = 160;
	public static final int SEARCH_ENGINE_NAVER_MOBILE = 165;

	public static final int SEARCH_ENGINE_SO = 170; //Leo - https://www.wrike.com/open.htm?id=94990663

	public static final int SEARCH_ENGINE_GOOGLEMOBILE = 999;

	public static final int SEARCH_ENGINE_GOOGLE_IMAGE = 888;

	public static final String SEARCH_ENGINE = "google";

	public static final String SEARCH_ENGINE_BING_NAME = "Bing";

	public static final String SEARCH_ENGINE_YAHOO_NAME = "Yahoo";

	public static final String SEARCH_ENGINE_NAVER_NAME = "Naver";

	public static final String SEARCH_ENGINE_GOOGLEMOBILE_NAME = "Google Mobile";

	public static final String SEARCH_ENGINE_SO_NAME = "360 search"; //Leo - https://www.wrike.com/open.htm?id=94990663

	public static final String SEARCH_ENGINE_YANDEX_NAME = "Yandex";

	public static final String SEARCH_ENGINE_BAIDU_NAME = "Baidu";

	public static final String SEARCH_ENGINE_GOOGLE_IMAGE_NAME = "images.google";

	public static final String SEARCH_ENGINE_GOOGLE_MOBILE = "m.google.com";
    
    private Map<String,List<SearchEngineContinentRelEntity>> searchEngineContinentRelMap;
    
    @Resource
    private SearchEngineContinentRelDAO searchEngineContinentRelDAO;
    @Resource
	private DomainSearchEngineRelJdbcDAO domainSearchEngineRelJdbcDAO;
    @Resource
	private VirtualSearchEngineRelDAO virtualSearchEngineRelDAO;
    @Resource
    private SeoClarityKeywordEntityDAO seoClarityKeywordEntityDAO;
    @Resource
    private KeywordNameRelService keywordNameRelService;
    @Resource
    private KeywordCityAdwordsDataEntityDAO keywordCityAdwordsDataEntityDAO; //scKeywordCityAdwordsDataEntityDAO;

	public static Map<String, String> AlternateDomainNameMap = new HashMap<String, String>();

    private static String KEY_SPLIT = "!_!";
    private static int ENGINE_LANGUAGE_ID_NOT_FOUND = -1;
    private static int DOMAIN_ID_NO = 0;
    private static EngineCountryLanguageMappingEntityDAO engineCountryLanguageMappingEntityDAO;
    private static EngineLanguageErrorLogEntityDAO engineLanguageErrorLogEntityDAO;
	private static SeoClaritySearchEngineEntityDAO seoClaritySearchEngineEntityDAO;
    private static Map<String, EngineCountryLanguageMappingEntity> engineCountryLanguageMap;
	private static EmailSenderComponent emailSenderComponent;

	private static final int RETRY_COUNT = 3;
	private static final String KEY_ENGINE_LANGUAGE_DELIMITER = "_";
	// https://www.wrike.com/open.htm?id=378332473
	private Map<Integer, List<DomainSearchEngineRelEntity>> domainSearchEngineRelMap;
	private Map<Integer, VirtualSearchEngineRelEntity> virtualSearchEngineRelMap;

	private static final int SEARCH_ENGINE_TYPE_REFRESH_INTERVAL_SECONDS = 600; //TODO
	private static Map<Integer, String> searchEngineTypeMap; // Key: searchEngineId, Value: searchEngineType("google", "bing", ...)
	private static Date searchEngineTypeLastRefreshTime = new Date();

    static {
    	engineCountryLanguageMappingEntityDAO = SpringBeanFactory.getBean("engineCountryLanguageMappingEntityDAO");
    	engineLanguageErrorLogEntityDAO = SpringBeanFactory.getBean("engineLanguageErrorLogEntityDAO");
		seoClaritySearchEngineEntityDAO = SpringBeanFactory.getBean("seoClaritySearchEngineEntityDAO");
		emailSenderComponent = SpringBeanFactory.getBean("emailSenderComponent");
    	retrieveEngineCountryLanguageMappings(false);
    }
    
    private static void retrieveEngineCountryLanguageMappings(boolean refreshCache) {
    	if (refreshCache || engineCountryLanguageMap == null || engineCountryLanguageMap.size() == 0) {
    		List<EngineCountryLanguageMappingEntity> engineCountryLanguageMappingList = engineCountryLanguageMappingEntityDAO.getAll();
    		System.out.println("===RetrieveEngineCountryLanguageMap size:" + engineCountryLanguageMappingList.size() + " refreshCache:" + refreshCache);
    		engineCountryLanguageMap = new HashMap<String, EngineCountryLanguageMappingEntity>();
    		if (engineCountryLanguageMappingList != null && engineCountryLanguageMappingList.size() > 0) {
    			for (EngineCountryLanguageMappingEntity entity : engineCountryLanguageMappingList) {
    				String countryQueryName = entity.getCountryQueryName();
    				String engineQueryName = entity.getEngineQueryName();
    				String languageQueryName = entity.getLanguageQueryName();
    				int rankFrom = entity.getRankFrom().intValue();
    				StringBuffer sb = new StringBuffer();
    				sb.append(countryQueryName.toLowerCase()).append(KEY_SPLIT);
    				sb.append(engineQueryName.toLowerCase()).append(KEY_SPLIT);
    				sb.append(languageQueryName.toLowerCase()).append(KEY_SPLIT);
    				sb.append(rankFrom);
    				engineCountryLanguageMap.put(sb.toString(), entity);
    				
    				System.out.println("==MAPID:" + entity.getId() + " country:\"" + countryQueryName + "\" engine:\"" + engineQueryName + 
    					"\" lang:\"" + languageQueryName + "\" rankFrom:" + rankFrom + " engineId:" + entity.getEngineId() + 
    					" langId:" + entity.getLanguageId());
                }
    		}
    	}
    }

	public static String getFormattedSEList(List<DomainSearchEngineRelEntity> reRelList) {
		if (reRelList != null && reRelList.size() > 0) {
			StringBuffer resultBuffer = new StringBuffer();
			for (int k = 0; k < reRelList.size(); k++) {
				DomainSearchEngineRelEntity seRelEntity = reRelList.get(k);
				resultBuffer.append(seRelEntity.getRankcheckSearchEngineId()).append("-");
				resultBuffer.append(seRelEntity.getRankcheckSearchLanguageid()).append("-");
				resultBuffer.append(seRelEntity.getDevice());
				if (k < reRelList.size() - 1) {
					resultBuffer.append(",");
				}
			}
			return resultBuffer.toString();
		}
		return "";
	}

    public List<DomainSearchEngineRelEntity> getDomainSearchEngineRelsForLegacyMobile(OwnDomainEntity ownDomainEntity) {
    	List<DomainSearchEngineRelEntity> searchEngineRelList = getDomainSearchEngineRels(ownDomainEntity);
    	if (searchEngineRelList != null && searchEngineRelList.size() > 0 && virtualSearchEngineRelMap != null && virtualSearchEngineRelMap.size() > 0) {
    		for (DomainSearchEngineRelEntity relEntity : searchEngineRelList) {
        		if (DomainSearchEngineRelEntity.DEVICE_MOBILE.equalsIgnoreCase(relEntity.getDevice())) {
        			int searchEngineId = relEntity.getSearchEngineId();
        			Set<Integer> keySet = virtualSearchEngineRelMap.keySet();
        			if (keySet.contains(searchEngineId) == false) {
        				String searchEngine = relEntity.getSearchEngine();
            			for(VirtualSearchEngineRelEntity virtualEntity : virtualSearchEngineRelMap.values()) {
            				String virtualSearchEngine = virtualEntity.getSearchEngine();
            				int revertSearchEnigneId = 0;
            				if (StringUtils.containsIgnoreCase(searchEngine, SEARCH_ENGINE_GOOGLE_IMAGE_NAME) 
            						&& StringUtils.containsIgnoreCase(virtualSearchEngine, SEARCH_ENGINE_GOOGLE_IMAGE_NAME) 
            						&& searchEngineId != virtualEntity.getVirtualSearchEngineId()) {
            					System.out.println("  ==RevertGImgEngineId OID:" + ownDomainEntity.getId() +  " rel:" + relEntity.getId() + " engine:" + searchEngine + " " + 
            						searchEngineId +  "-->" + virtualEntity.getVirtualSearchEngineId() + " " + relEntity.getDevice());
            					revertSearchEnigneId = virtualEntity.getVirtualSearchEngineId();
            				} else if (searchEngine.equalsIgnoreCase(virtualSearchEngine) && virtualEntity.getRankcheckSearchEngineId() > 0 
            						&& searchEngineId != virtualEntity.getVirtualSearchEngineId()) { // TODO
            					System.out.println("  ==RevertEngineId OID:" + ownDomainEntity.getId() +  " rel:" + relEntity.getId() + " engine:" + searchEngine + " " + 
            						searchEngineId +  "-->" + virtualEntity.getVirtualSearchEngineId() + " " + relEntity.getDevice());
            					revertSearchEnigneId = virtualEntity.getVirtualSearchEngineId();
            				} else if (StringUtils.containsIgnoreCase(searchEngine, SEARCH_ENGINE_GOOGLE_MOBILE) && StringUtils.containsIgnoreCase(virtualSearchEngine, SEARCH_ENGINE_GOOGLE_MOBILE)
            						&& searchEngineId != virtualEntity.getVirtualSearchEngineId()) {
            					System.out.println("  ==RevertGoogleMEngineId OID:" + ownDomainEntity.getId() +  " rel:" + relEntity.getId() + " engine:" + searchEngine + " " + 
            						searchEngineId +  "-->" + virtualEntity.getVirtualSearchEngineId() + " " + relEntity.getDevice());
            					revertSearchEnigneId = virtualEntity.getVirtualSearchEngineId();
            				} else if (StringUtils.containsIgnoreCase(searchEngine, SEARCH_ENGINE) && StringUtils.containsIgnoreCase(virtualSearchEngine, SEARCH_ENGINE)
            						&& searchEngineId != virtualEntity.getVirtualSearchEngineId()) {
            					System.out.println("  ==RevertGoogleEngineId OID:" + ownDomainEntity.getId() +  " rel:" + relEntity.getId() + " engine:" + searchEngine + " " + 
            						searchEngineId +  "-->" + virtualEntity.getVirtualSearchEngineId() + " " + relEntity.getDevice());
            					revertSearchEnigneId = virtualEntity.getVirtualSearchEngineId();
            				}
            				if (revertSearchEnigneId > 0) {
            					relEntity.setSearchEngineId(virtualEntity.getVirtualSearchEngineId());
            					break;
            				}
            			}
        			}
        		}
        	}
    	}
    	
    	return searchEngineRelList;
    }

	// https://www.wrike.com/open.htm?id=696909015
	private List<DomainSearchEngineRelEntity> excludePausedSERels(OwnDomainEntity ownDomainEntity, List<DomainSearchEngineRelEntity> seRelList) {
		if (seRelList == null || seRelList.size() == 0) {
			return seRelList;
		}

		List<DomainSearchEngineRelEntity> resultList = new ArrayList<DomainSearchEngineRelEntity>();
		boolean pausedSEExists = false;
		for (DomainSearchEngineRelEntity relEntity : seRelList) {
			// Active SEs: domain_search_engine_rel.(pause_flg=0 or pause_flg is null) or (pause_flg=2 and pause_status=2)
			// Skip SEs:   pause_flg>0 and (pause_flg!=2 or pause_status!=2)
			if (relEntity.getPauseFlg() == null || relEntity.getPauseFlg().intValue() == DomainSearchEngineRelEntity.PAUSE_FLAG_DEFAULT
					|| (relEntity.getPauseFlg().intValue() == DomainSearchEngineRelEntity.PAUSE_FLAG_RESUME
					&& relEntity.getPauseStatus() != null && relEntity.getPauseStatus().intValue() == DomainSearchEngineRelEntity.PAUSE_STATUS_FINISH_WITHOUT_ERROR)) {
				resultList.add(relEntity);
			} else {
				pausedSEExists = true;
				System.out.println(" ==OID:" + ownDomainEntity.getId() + " SkipPausedSE:" + relEntity.getRankcheckSearchEngineId() + "-" +
						relEntity.getRankcheckSearchLanguageid() + "-" + relEntity.getDevice());
			}
		}

		if (pausedSEExists) {
			System.out.println(" ==OID:" + ownDomainEntity.getId() + " ActiveSEs:" + getFormattedSEList(seRelList) + "->" + getFormattedSEList(resultList));
		}
		return resultList;
	}

    public List<DomainSearchEngineRelEntity> getDomainSearchEngineRels(OwnDomainEntity ownDomainEntity) {
		List<DomainSearchEngineRelEntity> seRelList = getDomainSearchEngineRels(ownDomainEntity, false);
		return excludePausedSERels(ownDomainEntity, seRelList);
    }

	public List<DomainSearchEngineRelEntity> getDomainSearchEngineRelsForSpecialEngine(OwnDomainEntity ownDomainEntity, String engineShortName, boolean forceRefreshSERelMap) {
		List<DomainSearchEngineRelEntity> list = getDomainSearchEngineRels(ownDomainEntity, forceRefreshSERelMap);
		if (StringUtils.isBlank(engineShortName)) {
			return list;
		}

		List<DomainSearchEngineRelEntity> resultList = new ArrayList<DomainSearchEngineRelEntity>();
		for (DomainSearchEngineRelEntity relEntity : list) {
			if (StringUtils.containsIgnoreCase(relEntity.getSearchEngine(), engineShortName)) {
				resultList.add(relEntity);
			}
		}
		return excludePausedSERels(ownDomainEntity, resultList);
	}

	public List<DomainSearchEngineRelEntity> getDomainSearchEngineRels(OwnDomainEntity ownDomainEntity, boolean forceRefreshSERelMap) {
    	int ownDomainId = ownDomainEntity.getId();
    	List<DomainSearchEngineRelEntity> relList = getDomainSearchEngineRelsFromDB(ownDomainId, forceRefreshSERelMap);
    	int primarySearchEngineId = getSearchEngineId(ownDomainEntity);
    	int primarySearchLanguageid = getSearchLanguageId(ownDomainEntity);
    	
    	String primaryDevice = DomainSearchEngineRelEntity.DEVICE_DESKTOP;
    	if (ownDomainEntity.isMobileDomain()) {
    		primaryDevice = DomainSearchEngineRelEntity.DEVICE_MOBILE; // TODO skip 888
    	}
    	// System.out.println("  ==PrimaryEngine OID:" + ownDomainId + " " + primarySearchEngineId + "/" + primarySearchLanguageid + " " + primaryDevice);
    	
    	// boolean containsDefaultMobileRel = false;
    	List<DomainSearchEngineRelEntity> resultList = new ArrayList<DomainSearchEngineRelEntity>();
    	if (relList != null && relList.size() > 0) {
    		for (DomainSearchEngineRelEntity domainSearchEngineRelEntity: relList) {
    			int searchEngineId = domainSearchEngineRelEntity.getSearchEngineId();
    			String device = domainSearchEngineRelEntity.getDevice();
    			if (device == null || StringUtils.isBlank(device)) {
    				// TODO skip 119(job.com)
    				System.out.println("  ==SkipSEId:" + searchEngineId +  " OID:" + ownDomainId);
    				continue;
    			}
    			
    			int searchLanguageId = domainSearchEngineRelEntity.getSearchEngineLanguageid();
    			int rankcheckSearchEngineId = searchEngineId;
    			int rankcheckSearchLanguageid = searchLanguageId;
    			VirtualSearchEngineRelEntity virtualSearchEngineRelEntity = virtualSearchEngineRelMap.get(searchEngineId);
    			if (virtualSearchEngineRelEntity != null) {
    				rankcheckSearchEngineId = virtualSearchEngineRelEntity.getRankcheckSearchEngineId();
    				if (rankcheckSearchEngineId == 0) {
    					rankcheckSearchEngineId = primarySearchEngineId;
    				}
    			}
    			if (rankcheckSearchLanguageid == 0) {
					rankcheckSearchLanguageid = primarySearchLanguageid;
				}
    			
    			domainSearchEngineRelEntity.setRankcheckSearchEngineId(rankcheckSearchEngineId);
    			domainSearchEngineRelEntity.setRankcheckSearchLanguageid(rankcheckSearchLanguageid);
//    			System.out.println("  ==ConvSE OID:" + ownDomainId +  " rel:" + domainSearchEngineRelEntity.getId() + " " + searchEngineId + "/" + searchLanguageId + 
//    				"-->" + rankcheckSearchEngineId + "/" + rankcheckSearchLanguageid + " " + device);
//    			if (primarySearchEngineId == rankcheckSearchEngineId && primarySearchLanguageid == rankcheckSearchLanguageid && 
//    				DomainSearchEngineRelEntity.DEVICE_MOBILE.equals(device)) {
//    				containsDefaultMobileRel = true;
//    			}
    			resultList.add(domainSearchEngineRelEntity);
    		}
    	}
    	
       
    	// enable mobile
//    	if (ownDomainEntity.isMobileDomain() == false && isMobileEnabled(ownDomainId) == true && containsDefaultMobileRel == false) {
//    		if (primarySearchEngineId != SEARCH_ENGINE_GOOGLEIMAGE) {
//        		DomainSearchEngineRelEntity defaultMobileRel = constructDefaultMobileRel(ownDomainId, primarySearchEngineId, primarySearchLanguageid, DomainSearchEngineRelEntity.DEVICE_MOBILE);
//        		resultList.add(defaultMobileRel);
//        		System.out.println("  ==AddDefaultMobileRel OID:" + ownDomainId + " " + primarySearchEngineId + "/" + primarySearchLanguageid + " " + DomainSearchEngineRelEntity.DEVICE_MOBILE);
//    		}
//    	}
    	
    	checkRels(ownDomainId, resultList, primarySearchEngineId, primarySearchLanguageid, primaryDevice);
    	
    	return resultList;
    }

	private void checkRels(int ownDomainId, List<DomainSearchEngineRelEntity> paramList, int primarySearchEngineId, int primarySearchLanguageid, String primaryDevice) {
		int relId = 0;
		try {
			if (paramList != null && paramList.size() > 0) {
				Set<String> relSet = new HashSet<String>();
				StringBuffer primaryEngineKeyBuffer = new StringBuffer();
				primaryEngineKeyBuffer.append(primarySearchEngineId).append(KEY_ENGINE_LANGUAGE_DELIMITER).append(primarySearchLanguageid)
						.append(KEY_ENGINE_LANGUAGE_DELIMITER).append(primaryDevice);
				String primaryEngineKey = primaryEngineKeyBuffer.toString();

				for (int i = 0; i < paramList.size(); i++) {
					DomainSearchEngineRelEntity relEntity = paramList.get(i);
					relId = relEntity.getId();

					// TODO check enigneId/languageId 0, null device
					if (relEntity.getRankcheckSearchEngineId() == null || relEntity.getRankcheckSearchEngineId().intValue() == 0 ||
							relEntity.getRankcheckSearchLanguageid() == null || relEntity.getRankcheckSearchLanguageid().intValue() == 0 ||
							StringUtils.isBlank(relEntity.getDevice())) {
						System.out.println("  ======RemoveBadDomainSearchEngineRel OID:" + ownDomainId + " rel:" + relEntity.getId() + " " +
								relEntity.getSearchEngineId() + "/" + relEntity.getSearchEngineLanguageid() + " " + relEntity.getDevice());
						// TODO skip 119(job.com)
						if ("".equals(relEntity.getDevice()) == false || relEntity.getSearchEngineId() != SeoClarityKeywordEntity.SEARCH_ENGINE_JOB) {
							sendEmailReport(new Date(), ownDomainId, relEntity.getId(), "DomainSearchEngineRel error", "DomainSearchEngineRel ERROR");
						}
						paramList.remove(i);
						i--;
						continue;
					}

					// TODO remove relation that enigneId,languageId,device is the same as primary engine
					StringBuffer bufferKey = new StringBuffer();
					bufferKey.append(relEntity.getRankcheckSearchEngineId()).append(KEY_ENGINE_LANGUAGE_DELIMITER).append(relEntity.getRankcheckSearchLanguageid())
							.append(KEY_ENGINE_LANGUAGE_DELIMITER).append(relEntity.getDevice());
					if (primaryEngineKey.equalsIgnoreCase(bufferKey.toString())) {
						System.out.println("  ======RemoveDomainSearchEngineRelSameAsPrimary OID:" + ownDomainId + " rel:" + relEntity.getId() + " " +
								relEntity.getSearchEngineId() + "/" + relEntity.getSearchEngineLanguageid() + " " + relEntity.getDevice());
						sendEmailReport(new Date(), ownDomainId, relEntity.getId(), "DomainSearchEngineRel SameAsPrimary error", "DomainSearchEngineRel SameAsPrimary ERROR");
						paramList.remove(i);
						i--;
						continue;
					}

					// TODO remove duplicated relations
					if (relSet.add(bufferKey.toString()) == false) {
						System.out.println("  ======RemoveDupDomainSearchEngineRel OID:" + ownDomainId + " rel:" + relEntity.getId() + " " +
								relEntity.getSearchEngineId() + "/" + relEntity.getSearchEngineLanguageid() + " " + relEntity.getDevice());
						sendEmailReport(new Date(), ownDomainId, relEntity.getId(), "DomainSearchEngineRel DupDomainRel error", "DomainSearchEngineRel DupDomainRel ERROR");
						paramList.remove(i);
						i--;
						continue;
					}
				}
			}
		} catch (Exception exp) {
			exp.printStackTrace();
			sendEmailReport(new Date(), ownDomainId, relId, "DomainSearchEngineRel checkRels error", "DomainSearchEngineRel checkRels ERROR");
		}
	}

	private List<DomainSearchEngineRelEntity> getDomainSearchEngineRelsFromDB(int oid, boolean forceRefreshSERelMap) {
    	if (forceRefreshSERelMap) {
    		reloadDomainSearchEngineRelMap(true);
    		reloadVirtualSearchEngineRelMap(true);
    	} else {
    		if (domainSearchEngineRelMap == null || domainSearchEngineRelMap.size() == 0) {
        		reloadDomainSearchEngineRelMap(true);
        	}
        	if (virtualSearchEngineRelMap == null || virtualSearchEngineRelMap.size() == 0) {
        		reloadVirtualSearchEngineRelMap(true);
        	}
    	}
		return domainSearchEngineRelMap.get(oid);
	}
	
	private void reloadDomainSearchEngineRelMap(boolean forceRefresh) {
    	if (forceRefresh) {
    		domainSearchEngineRelMap = domainSearchEngineRelJdbcDAO.getRelationMap();
    		if (domainSearchEngineRelMap != null && domainSearchEngineRelMap.size() > 0) {
    			System.out.println("==ForceFetchDomainSERels:" + domainSearchEngineRelMap.size());
    		} else {
    			System.out.println("==FailedFetchDomainSERels");
    	    	for (int k = 0; k < RETRY_COUNT; k++) {
    	    		try {
    	    			Thread.sleep(60 * 1000);
    	    		} catch (Exception exp) {
    	    			exp.printStackTrace();
    	    		}
    	    		domainSearchEngineRelMap = domainSearchEngineRelJdbcDAO.getRelationMap();
    	    		System.out.println("==RetryeFetchDomainSERels:" + (k + 1));
    	    		if (domainSearchEngineRelMap != null && domainSearchEngineRelMap.size() > 0) {
    	    			break;
    	    		}
    	    	}
    		}
    		
        	if (domainSearchEngineRelMap == null || domainSearchEngineRelMap.size() == 0) {
        		System.out.println("==================Error:ExitForStillFailedFetchDomainSERels after 3 retries");
        		System.exit(1);
        	}
    	}
    }
    
    private void reloadVirtualSearchEngineRelMap(boolean forceRefresh) {
    	if (forceRefresh) {
    		virtualSearchEngineRelMap = virtualSearchEngineRelDAO.getRelationMap();
    		if (virtualSearchEngineRelMap != null && virtualSearchEngineRelMap.size() > 0) {
    			System.out.println("==FetchVirtualSERels:" + virtualSearchEngineRelMap.size());
    		} else {
    			System.out.println("==FailedFetchVirtualSERels");
    		}
    	}
    }
    
	public static void sendEmailReport(Date startTime, int ownDomainId, int errorType, String mailSubject, String mailTitle) {
		try {
			Map<String, Object> reportMap = new HashMap<String, Object>();
			reportMap.put("userName", "Mitul");
			reportMap.put("dateString", DateFormatUtils.format(new Date(), "MM/dd/yyyy"));
			reportMap.put("startTime", DateFormatUtils.format(startTime, "MM/dd/yyyy HH:mm:ss"));
			reportMap.put("endTime", DateFormatUtils.format(new Date(), "MM/dd/yyyy HH:mm:ss"));
			reportMap.put("info", new ArrayList<String>());
			reportMap.put("title", mailTitle);
			reportMap.put("errormessage", "OID:" + ownDomainId + " errorType:" + errorType);

			String emailTo = "<EMAIL>";
			String[] ccTo = new String[] { "<EMAIL>" };
			String subject = mailSubject;
			emailSenderComponent.sendMimeMultiPartMail(emailTo, ccTo, subject, "mail_common.txt", "mail_common.html", reportMap);
		} catch (Exception exp) {
			exp.printStackTrace();
		}
	}

//	 TODO objs = default value
//	public static int getSearchLanguageId(OwnDomainEntity ownDomain, int... objs) {
//
//		String language = ownDomain.getLanguage();
//		String searchEngineCountry = ownDomain.getSearchEngineCountry();
//
//		if (StringUtils.isBlank(searchEngineCountry) || StringUtils.equalsIgnoreCase(searchEngineCountry, "US")) {
//			if (StringUtils.isBlank(language) || StringUtils.equalsIgnoreCase(language, "en")) {
//				return 1; // us_EN
//			}
//			if (StringUtils.equalsIgnoreCase(language, "es")) {
//				return 2; // us_ES
//			}
//		}
//
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "AU")) {
//			if (StringUtils.isBlank(language) || StringUtils.equalsIgnoreCase(language, "en")) {
//				return 5; // au_EN
//			}
//		}
//
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "CA")) {
//			if (StringUtils.isBlank(language) || StringUtils.equalsIgnoreCase(language, "en")) {
//				return 3; // ca_EN
//			}
//			if (StringUtils.equalsIgnoreCase(language, "fr")) {
//				return 4; // ca_FR
//			}
//		}
//
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "FR")) {
//			if (StringUtils.isBlank(language) || StringUtils.equalsIgnoreCase(language, "fr")) {
//				return 7; // fr_FR
//			}
//		}
//
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "CN")) {
//			// if (StringUtils.isBlank(language)) {
//			return 6; // cn_ZH
//			// }
//		}
//
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "UK")) {
//			return 8; // uk_EN
//		}
//
//		// #761
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "IT")) {
//			return 9; // it_IT
//		}
//
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "DK")) {
//			return 10; // dk_DA
//		}
//
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "FI")) {
//			return 11; // fi_FI
//		}
//
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "MX")) {
//			return 12; // mx_ES
//		}
//
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "NO")) {
//			return 13; // no_N
//		}
//
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "SE")) {
//			return 14; // se_SV
//		}
//
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "DE")) {
//			return 15; // de_DE
//		}
//
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "BR")) {
//			return 16; // br_PT
//		}
//
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "ES")) {
//			return 17; // es_ES
//		}
//
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "NL")) {
//			return 18; // nl_NL
//		}
//
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "JP")) {
//			return 19; // ja_JP
//		}
//
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "PT")) {
//			return 20; // pt_PT
//		}
//
//		// ticket 818
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "IE")) {
//			return 21; // ie_EN
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "BE")) {
//			return 22; // be_NL
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "CH")) {
//			return 23; // ch_DE
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "KR")) {
//			return 24; // kr_KO
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "IN")) {
//			return 25; // in_EN
//		}
//
//		// Latin america countries
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "AR")) {
//			return 26; // ar_ES
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "CL")) {
//			return 27; // cl_ES
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "CO")) {
//			return 28; // co_ES
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "PR")) {
//			return 29; // pr_ES
//		}
//
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "AT")) {
//			return 30; // at_DE
//		}
//
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "ID")) {
//			return 31; // id_ID
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "MY")) {
//			return 32; // my_EN
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "PH")) {
//			return 33; // ph_TL
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "TW")) {
//			return 34; // tw_ZH-TW
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "TH")) {
//			return 35; // th_TH
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "VN")) {
//			return 36; // vn_VI
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "SG")) {
//			return 37; // sg_EN
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "NZ")) {
//			return 38; // nz_EN
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "RU")) {
//			return 39; // nz_EN
//		}
//
//		// https://www.wrike.com/open.htm?id=9415299
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "CZ")) {
//			return 40; // cz_CS
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "HU")) {
//			return 41; // hu_HU
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "PL")) {
//			return 42; // pl_PL
//		}
//
//		// new rank
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "SA")) {
//			return 43; // sa_ar
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "PE")) {
//			return 44; // PE_ES
//		}
//
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "AE")) {
//			if (StringUtils.isBlank(language) || StringUtils.equalsIgnoreCase(language, "en")) {
//				return 46; // ae_EN
//			}
//			if (StringUtils.equalsIgnoreCase(language, "ar")) {
//				return 45; // ae_ARR
//			}
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "TR")) {
//			return 47; // tr_TR
//		}
//
//		// https://www.wrike.com/open.htm?id=21126507
//		// by cee
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "ZA")) {
//			return 48; // za_EN
//		}
//
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "KE")) {
//			return 49; // ke_EN
//		}
//
//		// https://www.wrike.com/open.htm?id=36219549
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "SK")) {
//			return 50; // sk-SK
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "IL")) {
//			return 51; // he-IL
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "EC")) {
//			return 52; // es-EC
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "VE")) {
//			return 53; // es-VE
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "CR")) {
//			return 54; // es-CR
//		}
//
//		// https://www.wrike.com/open.htm?id=46752792
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "UA")) {
//			return 55; // uk-UA
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "UY")) {
//			return 56; // es-UY
//		}
//
//		// https://www.wrike.com/open.htm?id=53458412
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "MV")) {
//			return 57; // mv-MV
//		}
//
//		// https://www.wrike.com/open.htm?id=58651708
//		// https://www.wrike.com/open.htm?id=58651849
//		// https://www.wrike.com/open.htm?id=58778920
//		// https://www.wrike.com/open.htm?id=58779062
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "BD")) {
//			return 58; // db_BN
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "KH")) {
//			return 59; // kh_KM
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "FJ")) {
//			return 60; // fj_EN
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "WS")) {
//			return 61; // ws_EN
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "GR")) {
//			return 62; // gr_El
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "NG")) {
//			return 63; // ng_EN
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "DO")) {
//			return 64; // do_ES
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "PK")) {
//			return 65; // pk_EN
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "LB")) {
//			return 66; // lb_AR
//		}
//
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "QA")) {
//			return 67; // ar-QA
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "PA")) {
//			return 68; // es-PA
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "JO")) {
//			return 69; // ar-JO
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "HR")) {
//			return 70; // hr-HR
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "DZ")) {
//			return 71; // ar-DZ
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "RO")) {
//			return 72; // ro-RO
//		}
//		// by zhangwd
//		if (objs != null && objs.length > 0) {
//			return objs[0];
//		}
//
//		return 1; // default us_EN
//	}
//
//	/**
//	 * @param ownDomain
//	 * @return
//	 */
//	// TODO
//	public static int getSearchEngineId(OwnDomainEntity ownDomain) {
//
//		String searchEngine = ownDomain.getSearchEngine();
//		String searchEngineCountry = ownDomain.getSearchEngineCountry();
//
//		if (StringUtils.isBlank(searchEngineCountry) || StringUtils.equalsIgnoreCase(searchEngineCountry, "US")) {
//			return 1; // google.com
//		}
//
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "AU")) {
//			return 2; // google.com.au
//		}
//
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "CA")) {
//			return 3; // google.ca
//		}
//
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "CN")) {
//			if (StringUtils.containsIgnoreCase(searchEngine, "baidu")) {
//				return 150; // baidu.com
//			} else if (StringUtils.containsIgnoreCase(searchEngine, "haosou")) {
//				return 151; // haosou.com
//			} else {
//				return 30; // google.com.hk
//			}
//		}
//
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "FR")) {
//			if (ownDomain.getRankFrom() == null || ownDomain.getRankFrom().intValue() == 0) {
//				return 4; // google.fr - all
//			} else {
//				return 7; // google.fr - local
//			}
//		}
//
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "UK")) {
//			if (ownDomain.getRankFrom() == null || ownDomain.getRankFrom().intValue() == 0) {
//				return 6; // google.co.uk - all
//			} else {
//				return 5; // google.co.uk - local
//			}
//		}
//
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "IT")) {
//			return 8; // google.it
//		}
//
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "DK")) {
//			return 9; // google.dk
//		}
//
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "FI")) {
//			return 10; // google.fi
//		}
//
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "MX")) {
//			return 11; // google.com.mx
//		}
//
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "NO")) {
//			return 12; // google.no
//		}
//
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "SE")) {
//			return 13; // google.se
//		}
//
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "DE")) {
//			return 14; // google.de
//		}
//
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "BR")) {
//			return 15; // google.com.br
//		}
//
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "ES")) {
//			return 16; // google.es
//		}
//
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "NL")) {
//			return 17; // google.nl
//		}
//
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "JP")) {
//			return 18; // google.jp
//		}
//
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "PT")) {
//			return 19; // google.pt
//		}
//		// ticket 818
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "IE")) {
//			return 20; // google.ie
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "BE")) {
//			return 21; // google.be
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "CH")) {
//			return 22; // google.ch
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "KR")) {
//			if (StringUtils.containsIgnoreCase(searchEngine, "naver")) {
//				return 160; // naver.com
//			} else {
//				return 23; // google.co.kr
//			}
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "IN")) {
//			return 24; // google.co.in
//		}
//
//		// Latin america countries
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "AR")) {
//			return 25; // google.com.ar
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "CL")) {
//			return 26; // google.cl
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "CO")) {
//			return 27; // google.com.co
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "PR")) {
//			return 28; // google.com.pr
//		}
//
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "AT")) {
//			return 29; // google.at
//		}
//
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "ID")) {
//			return 31; // google.co.id
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "MY")) {
//			return 32; // google.com.my
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "PH")) {
//			return 33; // google.com.ph
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "TW")) {
//			return 34; // google.com.tw
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "TH")) {
//			return 35; // google.co.th
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "VN")) {
//			return 36; // google.com.vn
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "SG")) {
//			return 37; // google.com.sg
//		}
//
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "NZ")) {
//			return 38; // google.co.nz
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "RU")) {
//			if (StringUtils.containsIgnoreCase(searchEngine, "yandex")) {
//				return 120; // yandex.ru
//			} else {
//				return 39; // google.ru
//			}
//		}
//
//		// https://www.wrike.com/open.htm?id=9415299
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "CZ")) {
//			return 40; // google.cz
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "HU")) {
//			return 41; // google.hu
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "PL")) {
//			return 42; // google.pl
//		}
//
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "SA")) {
//			return 43; // www.google.com.sa
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "PE")) {
//			return 44; // www.google.com.pe
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "AE")) {
//			return 45; // www.google.ae
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "TR")) {
//			return 46; // www.google.com.tr
//		}
//
//		// https://www.wrike.com/open.htm?id=21126507
//		// by cee
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "ZA")) {
//			return 47; // google.co.za
//		}
//
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "KE")) {
//			return 48; // google.co.ke
//		}
//
//		// https://www.wrike.com/open.htm?id=36219549
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "SK")) {
//			return 49; // google.sk
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "IL")) {
//			return 50; // google.co.il
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "EC")) {
//			return 51; // google.com.ec
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "VE")) {
//			return 52; // google.co.ve
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "CR")) {
//			return 53; // google.co.cr
//		}
//		// https://www.wrike.com/open.htm?id=46752792
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "UA")) {
//			return 54; // google.com.ua
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "UY")) {
//			return 55; // google.com.uy
//		}
//		// https://www.wrike.com/open.htm?id=53458412
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "MV")) {
//			return 56; // google.mv
//		}
//		// https://www.wrike.com/open.htm?id=58651708
//		// https://www.wrike.com/open.htm?id=58651849
//		// https://www.wrike.com/open.htm?id=58778920
//		// https://www.wrike.com/open.htm?id=58779062
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "BD")) {
//			return 57; // google.com.bd
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "KH")) {
//			return 58; // google.com.kh
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "FJ")) {
//			return 59; // google.com.fj
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "WS")) {
//			return 60; // google.ws
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "GR")) {
//			return 61; // google.gr
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "NG")) {
//			return 62; // google.com.ng
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "DO")) {
//			return 63; // google.com.do
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "PK")) {
//			return 64; // google.com.pk
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "LB")) {
//			return 65; // google.com.lb
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "QA")) {
//			return 66; // google.com.qa
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "PA")) {
//			return 67; // google.com.pa
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "JO")) {
//			return 68; // google.jo
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "HR")) {
//			return 69; // google.hr
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "DZ")) {
//			return 70; // google.dz
//		}
//		if (StringUtils.equalsIgnoreCase(searchEngineCountry, "RO")) {
//			return 71; // google.ro
//		}
//		return 1; // default google.com
//	}

//	public static int getBingSearchEngineId(OwnDomainEntity ownDomain) {
//
//		// String searchEngine = ownDomain.getSearchEngine();
//		String searchEngineCountry = ownDomain.getSearchEngineCountry();
//
//		if (StringUtils.isBlank(searchEngineCountry) || StringUtils.equalsIgnoreCase(searchEngineCountry, "US")) {
//			return 255; // bing.com
//		}
//
//		// if (StringUtils.equalsIgnoreCase(searchEngineCountry, "AU")) {
//		// return 254; // bing.au
//		// }
//		//
//		// if (StringUtils.equalsIgnoreCase(searchEngineCountry, "CA")) {
//		// return 253; // bing.ca
//		// }
//
//		return 255; // default bing.com
//	}

//	public static int getYahooSearchEngineId(OwnDomainEntity ownDomain) {
//		return 100;
//	}
//
//	public static int getYandexSearchEngineId(OwnDomainEntity ownDomain) {
//		return 39;
//	}
//
//	// https://www.wrike.com/open.htm?id=64688891
//	// by sunny
//	private int getHaosouSearchEngineId(OwnDomainEntity ownDomain) {
//		return 151; // default haosou.com
//	}

	// by sunny
	public static String getCountryCode(int engine, int language) {
		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE
				&& (language == SeoClarityKeywordEntity.LANGUAGE_US_EN || language == SeoClarityKeywordEntity.LANGUAGE_US_ES)) {
			return "US";
		}

		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_AU
				&& language == SeoClarityKeywordEntity.LANGUAGE_AU_EN) {
			return "AU";
		}

		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_CA
				&& (language == SeoClarityKeywordEntity.LANGUAGE_CA_EN || language == SeoClarityKeywordEntity.LANGUAGE_CA_FR)) {
			return "CA";
		}

		if ((engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_COM_HK || engine == SeoClarityKeywordEntity.SEARCH_ENGINE_BAIDU_CN)
				&& language == SeoClarityKeywordEntity.LANGUAGE_CN_ZH) {
			return "CN";
		}

		if ((engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_FR || engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_FR_ALL)
				&& language == SeoClarityKeywordEntity.LANGUAGE_FR_FR) {
			return "FR";
		}

		if ((engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_UK || engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_UK_ALL)
				&& language == SeoClarityKeywordEntity.LANGUAGE_UK_EN) {
			return "UK";
		}

		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_IT
				&& language == SeoClarityKeywordEntity.LANGUAGE_IT_IT) {
			return "IT";
		}

		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_DK
				&& language == SeoClarityKeywordEntity.LANGUAGE_DK_DA) {
			return "DK";
		}

		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_FI
				&& language == SeoClarityKeywordEntity.LANGUAGE_FI_FI) {
			return "FI";
		}

		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_COM_MX
				&& language == SeoClarityKeywordEntity.LANGUAGE_MX_ES) {
			return "MX";
		}

		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_NO
				&& language == SeoClarityKeywordEntity.LANGUAGE_NO_NO) {
			return "NO";
		}

		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_SE
				&& language == SeoClarityKeywordEntity.LANGUAGE_SE_SV) {
			return "SE";
		}

		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_DE
				&& language == SeoClarityKeywordEntity.LANGUAGE_DE_DE) {
			return "DE";
		}

		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_COM_BR
				&& language == SeoClarityKeywordEntity.LANGUAGE_BR_PT) {
			return "BR";
		}

		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_ES
				&& language == SeoClarityKeywordEntity.LANGUAGE_ES_ES) {
			return "ES";
		}

		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_NL
				&& language == SeoClarityKeywordEntity.LANGUAGE_NL_NL) {
			return "NL";
		}

		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_JP
				&& language == SeoClarityKeywordEntity.LANGUAGE_JP_JA) {
			return "JP";
		}

		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_PT
				&& language == SeoClarityKeywordEntity.LANGUAGE_PT_PT) {
			return "PT";
		}

		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_IE
				&& language == SeoClarityKeywordEntity.LANGUAGE_IE_EN) {
			return "IE";
		}
		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_BE
				&& language == SeoClarityKeywordEntity.LANGUAGE_BE_NL) {
			return "BE";
		}
		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_CH
				&& language == SeoClarityKeywordEntity.LANGUAGE_CH_DE) {
			return "CH";
		}

		if ((engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_CO_KR || engine == SeoClarityKeywordEntity.SEARCH_NAVER)
				|| language == SeoClarityKeywordEntity.LANGUAGE_KR_KO) {
			return "KR";
		}

		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_CO_IN
				&& language == SeoClarityKeywordEntity.LANGUAGE_IN_EN) {
			return "IN";
		}

		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_COM_AR
				&& language == SeoClarityKeywordEntity.LANGUAGE_AR_ES) {
			return "AR";
		}
		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_CL
				&& language == SeoClarityKeywordEntity.LANGUAGE_CL_ES) {
			return "CL";
		}
		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_COM_CO
				&& language == SeoClarityKeywordEntity.LANGUAGE_CO_ES) {
			return "CO";
		}
		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_COM_PR
				&& language == SeoClarityKeywordEntity.LANGUAGE_PR_ES) {
			return "PR";
		}
		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_AT
				&& language == SeoClarityKeywordEntity.LANGUAGE_AT_DE) {
			return "AT";
		}

		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_CO_ID
				&& language == SeoClarityKeywordEntity.LANGUAGE_ID_ID) {
			return "ID";
		}
		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_COM_MY
				&& language == SeoClarityKeywordEntity.LANGUAGE_MY_EN) {
			return "MY";
		}
		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_COM_PH
				&& language == SeoClarityKeywordEntity.LANGUAGE_PH_TL) {
			return "PH";
		}
		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_COM_TW
				&& language == SeoClarityKeywordEntity.LANGUAGE_TW_ZH) {
			return "TW";
		}
		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_CO_TH
				&& language == SeoClarityKeywordEntity.LANGUAGE_TH_TH) {
			return "TH";
		}
		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_COM_VN
				&& language == SeoClarityKeywordEntity.LANGUAGE_VN_VI) {
			return "VN";
		}
		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_COM_SG
				&& language == SeoClarityKeywordEntity.LANGUAGE_SG_EN) {
			return "SG";
		}
		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_CO_NZ
				&& language == SeoClarityKeywordEntity.LANGUAGE_NZ_EN) {
			return "NZ";
		}

		if ((engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_RU || engine == SeoClarityKeywordEntity.SEARCH_YANDEX)
				&& language == SeoClarityKeywordEntity.LANGUAGE_RU_RU) {
			return "RU";
		}

		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_CZ
				&& language == SeoClarityKeywordEntity.LANGUAGE_CZ_CS) {
			return "CZ";
		}
		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_HU
				&& language == SeoClarityKeywordEntity.LANGUAGE_HU_HU) {
			return "HU";
		}
		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_PL
				&& language == SeoClarityKeywordEntity.LANGUAGE_PL_PL) {
			return "PL";
		}

		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_COM_SA
				&& language == SeoClarityKeywordEntity.LANGUAGE_SA_AR) {
			return "SA";
		}
		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_COM_PE
				&& language == SeoClarityKeywordEntity.LANGUAGE_PE_ES) {
			return "PE";
		}

		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_AE
				&& language == SeoClarityKeywordEntity.LANGUAGE_AE_AR) {
			return "AE";
		}

		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_COM_TR
				&& language == SeoClarityKeywordEntity.LANGUAGE_TR_TR) {
			return "TR";
		}

		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_CO_ZA
				&& language == SeoClarityKeywordEntity.LANGUAGE_ZA_EN) {
			return "ZA";
		}

		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_CO_KE
				&& language == SeoClarityKeywordEntity.LANGUAGE_KE_EN) {
			return "KE";
		}

		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_SK
				&& language == SeoClarityKeywordEntity.LANGUAGE_SK_SK) {
			return "SK";
		}
		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_CO_IL
				&& language == SeoClarityKeywordEntity.LANGUAGE_HE_IL) {
			return "IL";
		}
		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_COM_EC
				&& language == SeoClarityKeywordEntity.LANGUAGE_ES_EC) {
			return "EC";
		}
		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_CO_VE
				&& language == SeoClarityKeywordEntity.LANGUAGE_ES_VE) {
			return "VE";
		}
		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_CO_CR
				&& language == SeoClarityKeywordEntity.LANGUAGE_ES_CR) {
			return "CR";
		}

		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_COM_UA
				&& language == SeoClarityKeywordEntity.LANGUAGE_UK_UA) {
			return "UA";
		}
		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_COM_UY
				&& language == SeoClarityKeywordEntity.LANGUAGE_ES_UY) {
			return "UY";
		}

		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_MV
				&& language == SeoClarityKeywordEntity.LANGUAGE_MV_EN) {
			return "MV";
		}

		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_COM_BD
				&& language == SeoClarityKeywordEntity.LANGUAGE_DB_BN) {
			return "BD";
		}
		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_COM_KH
				&& language == SeoClarityKeywordEntity.LANGUAGE_KH_KM) {
			return "KH";
		}
		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_COM_FJ
				&& language == SeoClarityKeywordEntity.LANGUAGE_FJ_EN) {
			return "FJ";
		}
		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_WS
				&& language == SeoClarityKeywordEntity.LANGUAGE_WS_EN) {
			return "WS";
		}
		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_GR
				&& language == SeoClarityKeywordEntity.LANGUAGE_GR_EL) {
			return "GR";
		}
		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_COM_NG
				&& language == SeoClarityKeywordEntity.LANGUAGE_NG_EN) {
			return "NG";
		}

		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_COM_DO
				&& language == SeoClarityKeywordEntity.LANGUAGE_DO_ES) {
			return "DO";
		}

		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_COM_PK
				&& language == SeoClarityKeywordEntity.LANGUAGE_PK_EN) {
			return "PK";
		}

		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_COM_LB
				&& language == SeoClarityKeywordEntity.LANGUAGE_LB_AR) {
			return "LB";
		}

		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_COM_QA
				&& language == SeoClarityKeywordEntity.LANGUAGE_AR_QA) {
			return "QA";
		}

		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_COM_PA
				&& language == SeoClarityKeywordEntity.LANGUAGE_ES_PA) {
			return "PA";
		}

		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_JO
				&& language == SeoClarityKeywordEntity.LANGUAGE_AR_JO) {
			return "JO";
		}

		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_HR
				&& language == SeoClarityKeywordEntity.LANGUAGE_HR_HR) {
			return "HR";
		}

		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_DZ
				&& language == SeoClarityKeywordEntity.LANGUAGE_AR_DZ) {
			return "DZ";
		}

		if (engine == SeoClarityKeywordEntity.SEARCH_ENGINE_GOOGLE_RO
				&& language == SeoClarityKeywordEntity.LANGUAGE_RO_RO) {
			return "RO";
		}

		return null;
	}

	//https://www.wrike.com/open.htm?id=26774704 by sunny
	public static String getSearchEngineNameByEngineId(int searchEngineId, String defaultEngineName) {
		if (searchEngineId == SEARCH_ENGINE_YANDEX) {
			return SEARCH_ENGINE_YANDEX_NAME;
		}

		if (isGoogleEngine(searchEngineId)) {
			return SEARCH_ENGINE;
		}

		if (searchEngineId == SEARCH_ENGINE_YAHOO) {
			return SEARCH_ENGINE_YAHOO_NAME;
		}

		if (searchEngineId == SEARCH_ENGINE_BING) {
			return SEARCH_ENGINE_BING_NAME;
		}

		if (searchEngineId == SEARCH_ENGINE_NAVER) {
			return SEARCH_ENGINE_NAVER_NAME;
		}

		if (searchEngineId == SEARCH_ENGINE_GOOGLEMOBILE) {
			return SEARCH_ENGINE_GOOGLEMOBILE_NAME;
		}

		//Leo - https://www.wrike.com/open.htm?id=94990663
		if (searchEngineId == SEARCH_ENGINE_SO) {
			return SEARCH_ENGINE_SO_NAME;
		}

		//https://www.wrike.com/open.htm?id=151646917
		//By Sunny
		if (searchEngineId == SEARCH_ENGINE_BAIDU) {
			return SEARCH_ENGINE_BAIDU_NAME;
		}

		return defaultEngineName;
	}


	
//	// by Meo
//	public static String getCountryCodeForOtherEngine(int engine, int language) {
//		if (language == SeoClarityKeywordEntity.LANGUAGE_CN_ZH) {
//			return "CN";
//		}
//
//		if (language == SeoClarityKeywordEntity.LANGUAGE_RU_RU) {
//			return "RU";
//		}
//
//		if (language == SeoClarityKeywordEntity.LANGUAGE_KR_KO) {
//			return "KR";
//		}
//
//		if (language == SeoClarityKeywordEntity.LANGUAGE_US_EN) {
//			return "US";
//		}
//
//		if (language == SeoClarityKeywordEntity.LANGUAGE_JP_JA) {
//			return "JP";
//		}
//
//		if (language == SeoClarityKeywordEntity.LANGUAGE_TW_ZH) {
//			return "TW";
//		}
//
//		// bing
//		if (language == SeoClarityKeywordEntity.LANGUAGE_CA_EN) {
//			return "CA";
//		}
//		if (language == SeoClarityKeywordEntity.LANGUAGE_AR_ES) {
//			return "AR";
//		}
//		if (language == SeoClarityKeywordEntity.LANGUAGE_BR_PT) {
//			return "BR";
//		}
//		if (language == SeoClarityKeywordEntity.LANGUAGE_CL_ES) {
//			return "CL";
//		}
//
//		if (language == SeoClarityKeywordEntity.LANGUAGE_DE_DE) {
//			return "DE";
//		}
//		if (language == SeoClarityKeywordEntity.LANGUAGE_ES_ES) {
//			return "ES";
//		}
//		if (language == SeoClarityKeywordEntity.LANGUAGE_FR_FR) {
//			return "FR";
//		}
//		if (language == SeoClarityKeywordEntity.LANGUAGE_IN_EN) {
//			return "IN";
//		}
//		if (language == SeoClarityKeywordEntity.LANGUAGE_IT_IT) {
//			return "IT";
//		}
//
//		if (language == SeoClarityKeywordEntity.LANGUAGE_UK_EN) {
//			return "UK";
//		}
//
//		if (language == SeoClarityKeywordEntity.LANGUAGE_CO_ES) {
//			return "CO";
//		}
//		if (language == SeoClarityKeywordEntity.LANGUAGE_PR_ES) {
//			return "PR";
//		}
//		if (language == SeoClarityKeywordEntity.LANGUAGE_AU_EN) {
//			return "AU";
//		}
//		if (language == SeoClarityKeywordEntity.LANGUAGE_ID_ID) {
//			return "ID";
//		}
//		if (language == SeoClarityKeywordEntity.LANGUAGE_MY_EN) {
//			return "MY";
//		}
//		if (language == SeoClarityKeywordEntity.LANGUAGE_PH_TL) {
//			return "PH";
//		}
//		if (language == SeoClarityKeywordEntity.LANGUAGE_TH_TH) {
//			return "TH";
//		}
//
//		if (language == SeoClarityKeywordEntity.LANGUAGE_VN_VI) {
//			return "VN";
//		}
//		if (language == SeoClarityKeywordEntity.LANGUAGE_SG_EN) {
//			return "SG";
//		}
//
//		if (language == SeoClarityKeywordEntity.LANGUAGE_SE_SV) {
//			return "SE";
//		}
//		if (language == SeoClarityKeywordEntity.LANGUAGE_NO_NO) {
//			return "NO";
//		}
//		if (language == SeoClarityKeywordEntity.LANGUAGE_NL_NL) {
//			return "NL";
//		}
//		if (language == SeoClarityKeywordEntity.LANGUAGE_FI_FI) {
//			return "FI";
//		}
//		if (language == SeoClarityKeywordEntity.LANGUAGE_DK_DA) {
//			return "DK";
//		}
//
//		if (language == SeoClarityKeywordEntity.LANGUAGE_MX_ES) {
//			return "MX";
//		}
//		if (language == SeoClarityKeywordEntity.LANGUAGE_AT_DE) {
//			return "AT";
//		}
//		if (language == SeoClarityKeywordEntity.LANGUAGE_PT_PT) {
//			return "PT";
//		}
//
//		return null;
//	}
    
    public boolean searchEngineLanguageBelongToContinent(int searchEngineId, int searchLanguageId, String continent) {
    	if (searchEngineContinentRelMap == null || searchEngineContinentRelMap.size() == 0) {
    		searchEngineContinentRelMap = searchEngineContinentRelDAO.getSearchEngineContinentRels();
    		System.out.println("==searchEngineContinentRelDAO.getSearchEngineContinentRels");
    	}
    	
    	List<SearchEngineContinentRelEntity> relList = searchEngineContinentRelMap.get(continent);
    	if (relList != null && relList.size() > 0) {
    		for (SearchEngineContinentRelEntity entity : relList) {
    			if (entity.getSearchEngineId() == searchEngineId && entity.getLanguageId() == searchLanguageId) {
    				return true;
    			}
    		}
    	}
    	
    	return false;
    }
    
    public static int getSecondaryLanguageId(int languageId, OwnDomainEntity ownDomain) {
		
		if (languageId == 0) {
			languageId = getSearchLanguageId(ownDomain);
		}
    	
		return languageId;
    }

//	public SearchEngineContinentRelDAO getSearchEngineContinentRelDAO() {
//		return searchEngineContinentRelDAO;
//	}
//
//	public void setSearchEngineContinentRelDAO(
//			SearchEngineContinentRelDAO searchEngineContinentRelDAO) {
//		this.searchEngineContinentRelDAO = searchEngineContinentRelDAO;
//	}
    
 // Wilber https://www.wrike.com/open.htm?id=220634717
    public static int getSearchEngineId(OwnDomainEntity ownDomain) {
    	return getEngineLanguageId(ownDomain, true);
    }
    
    public static int getSearchLanguageId(OwnDomainEntity ownDomain, int...objs) {
    	int languageId = getEngineLanguageId(ownDomain, false);
    	if (languageId == ENGINE_LANGUAGE_ID_NOT_FOUND && objs != null && objs.length > 0) {
            return objs[0];
    	}
    	
    	return languageId;
    }
    
    private static void logError(int ownDomainId, int errorType) {
    	try {
        	EngineLanguageErrorLogEntity errorLogEntity = new EngineLanguageErrorLogEntity();
    		errorLogEntity.setOwnDomainId(ownDomainId);
    		errorLogEntity.setErrorType(errorType);
    		errorLogEntity.setProcessDate(Integer.parseInt(DateFormatUtils.format(new Date(), "yyyyMMdd")));
    		
    		EngineLanguageErrorLogEntity dbEntity = engineLanguageErrorLogEntityDAO.checkExist(errorLogEntity.getOwnDomainId(), 
    				errorLogEntity.getErrorType(), errorLogEntity.getProcessDate());
    		if (dbEntity == null) {
    			engineLanguageErrorLogEntityDAO.insert(errorLogEntity);
    		} else {
    			engineLanguageErrorLogEntityDAO.updateDate(dbEntity.getId());
    		}
    	} catch (Exception e) {
    		e.printStackTrace();
    	}
    }
    
    private static int getEngineLanguageId(OwnDomainEntity ownDomain, boolean isEngine) {
    	if (ownDomain == null) {
    		System.out.println("===ERROR: ownDomain is null.");
    		logError(DOMAIN_ID_NO, EngineLanguageErrorLogEntity.ERROR_TYPE_NULL_DOMAIN);
    		return ENGINE_LANGUAGE_ID_NOT_FOUND;
    	}
    	
    	// TopXKeywordRankingExportDailyExpedia OwnDomainEntity no id
    	int oid = DOMAIN_ID_NO;
    	if (ownDomain.getId() != null) {
    		oid = ownDomain.getId().intValue();
    	}
    	
    	String searchEngineCountry = ownDomain.getSearchEngineCountry();
    	if (StringUtils.isBlank(searchEngineCountry)) {
    		System.out.println("===ERROR: empty searchEngineCountry. OID:" + oid);
    		logError(oid, EngineLanguageErrorLogEntity.ERROR_TYPE_EMPTY_COUNTRY);
    		return ENGINE_LANGUAGE_ID_NOT_FOUND;
    	}
    	
    	String searchEngine = ownDomain.getSearchEngine();
    	if (StringUtils.isBlank(searchEngine)) {
    		System.out.println("===ERROR: empty searchEngine. OID:" + oid);
    		logError(oid, EngineLanguageErrorLogEntity.ERROR_TYPE_EMPTY_ENGINE);
    		return ENGINE_LANGUAGE_ID_NOT_FOUND;
    	} else {
    		searchEngine = searchEngine.toLowerCase();
    		if (StringUtils.containsIgnoreCase(searchEngine, "yandex") && !StringUtils.equals(searchEngine, "yandex.ru")) {
    			System.out.println("==yandex search engine " + " engineQueryName:" + searchEngine + " OID:" + oid);
    			searchEngine = "yandex.ru";
    			System.out.println("==yandexAppend '.ru' search engine " + " engineQueryName:" + searchEngine + " OID:" + oid);
    		}
    	}
    	
        String language = ownDomain.getLanguage();
    	if (StringUtils.isBlank(language)) {
    		System.out.println("===ERROR: empty language. OID:" + oid);
    		logError(oid, EngineLanguageErrorLogEntity.ERROR_TYPE_EMPTY_LANGUAGE);
    		return ENGINE_LANGUAGE_ID_NOT_FOUND;
    	} else {
    		language = language.toLowerCase();
    	}
        
        Integer rankFromObj = ownDomain.getRankFrom();
        int rankFrom = EngineCountryLanguageMappingEntity.DISABLED;
        if (rankFromObj != null) {
        	rankFrom = rankFromObj.intValue();
        }
        
        StringBuffer sb = new StringBuffer();
        sb.append(searchEngineCountry.toLowerCase()).append(KEY_SPLIT);
		sb.append(searchEngine.toLowerCase()).append(KEY_SPLIT);
		sb.append(language.toLowerCase()).append(KEY_SPLIT);
		sb.append(rankFrom);
    	String key = sb.toString();
    	
    	EngineCountryLanguageMappingEntity entity = engineCountryLanguageMap.get(key);
    	if (entity == null) {
    		retrieveEngineCountryLanguageMappings(true);
    		entity = engineCountryLanguageMap.get(key);
    	}
    	
    	return getEngineLanguageId(oid, entity, isEngine);
    }
   
    private static int getEngineLanguageId(int oid, EngineCountryLanguageMappingEntity mappingEntity, boolean isEngine) {
    	if (mappingEntity == null) {
    		System.out.println("===ERROR: EngineCountryLanguageMapping not found. OID:" + oid);
    		logError(oid, EngineLanguageErrorLogEntity.ERROR_TYPE_MAPPING_NOT_FOUND);
    		return ENGINE_LANGUAGE_ID_NOT_FOUND; // TODO
    	}
    	
    	if (isEngine) {
    		return mappingEntity.getEngineId().intValue();
    	} else {
    		return mappingEntity.getLanguageId().intValue();
    	}
    }

	/**
	 * check a domain is a regional domain or not
	 * <AUTHOR>
	 * */
	public static boolean isRegionDomain(OwnDomainEntity ownDomain, OwnDomainSettingEntity ownDomainSettingEntity) {
		boolean isRegion = false;
		if (ownDomain != null && ownDomainSettingEntity != null) {
//		    isRegion = !ownDomain.isMobileDomain()
//				    && (ownDomainSettingEntity.getRegionId() != null )
//				    && (ownDomainSettingEntity.getRegionId().intValue() > 0);
			isRegion = (ownDomainSettingEntity.getRegionId() != null )
					&& (ownDomainSettingEntity.getRegionId().intValue() > 0);
		}
		int searchEngineId = ScKeywordRankManager.getSearchEngineId(ownDomain);
		return isRegion && (isGoogleEngine(searchEngineId) || searchEngineId == ScKeywordRankManager.SEARCH_ENGINE_GOOGLEMOBILE);
	}

	private static void refreshSearchEngineTypeMap(boolean forceRefresh) {
		boolean needRefresh = forceRefresh;
		if (!forceRefresh) {
			Date now = new Date();
			needRefresh = now.compareTo(DateUtils.addSeconds(searchEngineTypeLastRefreshTime, SEARCH_ENGINE_TYPE_REFRESH_INTERVAL_SECONDS)) >= 0;
		}
		if (needRefresh) {
			searchEngineTypeMap = new HashMap<Integer, String>();
			List<SeoClaritySearchEngineEntity> searchEngineList = seoClaritySearchEngineEntityDAO.getSearchEngines();
			if (searchEngineList != null) {
				for (SeoClaritySearchEngineEntity seEntity : searchEngineList) {
					searchEngineTypeMap.put(seEntity.getId(), seEntity.getSearchEngineType());
				}
			}
			searchEngineTypeLastRefreshTime = new Date();
			System.out.println(" ===refreshSETypeMap ses:" + (searchEngineList != null ? searchEngineList.size() : 0) + " map:" + searchEngineTypeMap.size() +
					" at " + SDF_YYYYMMDDHHMMSS.format(searchEngineTypeLastRefreshTime));
		}
	}

	public static boolean isGoogleEngine(int searchEngineId) { // https://www.wrike.com/open.htm?id=1326206020
		if (searchEngineTypeMap == null || searchEngineTypeMap.size() == 0) {
			refreshSearchEngineTypeMap(true);
		}
		String searchEngineType = null;
		if (searchEngineTypeMap != null) {
			searchEngineType = searchEngineTypeMap.get(searchEngineId);
		}
		if (searchEngineType == null) {
			refreshSearchEngineTypeMap(false);
			if (searchEngineTypeMap != null) {
				searchEngineType = searchEngineTypeMap.get(searchEngineId);
			}
		}
		if (searchEngineType != null) {
			return SEARCH_ENGINE.equalsIgnoreCase(searchEngineType);
		} else {
			sendEmailReport(new Date(), 0, 0, "NotFoundSE ERROR", "NotFoundSearchEngineType ERROR(engineId:" + searchEngineId);
			return isGoogleEngineByHardCode(searchEngineId); // TODO
		}
	}
	//https://www.wrike.com/open.htm?id=261735000 by Sunny
//	private static Map<Integer,String> engineNameMap = new HashMap<Integer,String>();
	public static boolean isGoogleEngineByHardCode(int searchEngineId) {
		if (searchEngineId < 100 || (searchEngineId > 200 && searchEngineId < 255)) {
			return true;
		} else {
			return false;
		}

//		if (engineNameMap == null || engineNameMap.isEmpty()) {
//			List<SeoClaritySearchEngineEntity> engineNameList = seoClaritySearchEngineEntityDAO.getSearchEngines();
//			System.out.println("query db all!");
//			for (SeoClaritySearchEngineEntity entity : engineNameList) {
//				engineNameMap.put(entity.getId(), entity.getSearchEngine());
//			}
//		}
//		String engineName = engineNameMap.get(searchEngineId);
//		if (StringUtils.isBlank(engineName)) {
//			engineName = seoClaritySearchEngineEntityDAO.getEngineNameById(searchEngineId);
//			System.out.println("query db one!");
//			engineNameMap.put(searchEngineId, engineName);
//		}
//		if (StringUtils.isBlank(engineName)) {
//			engineNameMap.put(searchEngineId, "not found");
//			return false;
//		}
//		return StringUtils.containsIgnoreCase(engineName, "google");
	}
	public static String getAlternateDomainName(String domainName) {
		String alterName = AlternateDomainNameMap.get(domainName);
		return alterName == null ? domainName : alterName;
	}
	
	public List<DomainSearchEngineRelEntity> getPrimarySearchEngineList(OwnDomainEntity ownDomainEntity) {
    	List<DomainSearchEngineRelEntity> seRelList = new ArrayList<DomainSearchEngineRelEntity>();
		seRelList.add(getPrimarySearchEngine(ownDomainEntity));
    	return seRelList;
    }
	
	public DomainSearchEngineRelEntity getPrimarySearchEngine(OwnDomainEntity ownDomainEntity) {
    	int searchEngineId = ScKeywordRankManager.getSearchEngineId(ownDomainEntity);
    	
        int languageId = ScKeywordRankManager.getSearchLanguageId(ownDomainEntity);
        String device = SeoClarityKeywordSearchEngineDomainRelationEntity.DEVICE_NOT_MOBILE;
        if (ownDomainEntity.isMobileDomain()) {
        	device = SeoClarityKeywordSearchEngineDomainRelationEntity.DEVICE_MOBILE;
        }
        DomainSearchEngineRelEntity resultEntity = new DomainSearchEngineRelEntity();
        resultEntity.setRankcheckSearchEngineId(searchEngineId);
        resultEntity.setRankcheckSearchLanguageid(languageId);
        resultEntity.setDevice(device);
        return resultEntity;
    }
	
	 /**
     * get the last searchVolume for specified  keywordList
     * @param isCity check  from keyword_city_adwords_data_entity or keyword_adwords_data_entity
     * */
    public void findSearchVolV2(List<SeoClarityKeywordEntity> keywordList, int languageId, boolean isCity) {
	    if (keywordList == null || keywordList.size() == 0) {
			return;
	    }
	    int searchEngineId = 99;
	    KeywordEntity keywordEntity = new KeywordEntity();
	    for (SeoClarityKeywordEntity entity : keywordList) {
	    	try {
			    keywordEntity.setKeywordName(entity.getKeywordText());
			    Long sov = null;
			    Float cpc = null;
			    Map<String, Object> map = null;
			    try {
				    if (isCity) {
					    map = getAvgSearchVolumeAndCPCForCleanedKeyword(searchEngineId, languageId, keywordEntity, entity.getCityId());
				    } else {
					    map = getAvgSearchVolumeAndCPCForCleanedKeyword(searchEngineId, languageId, keywordEntity);
				    }
			    } catch (Exception e) {
			    	try {
			    		Thread.sleep(100);
					    if (isCity) {
						    map = getAvgSearchVolumeAndCPCForCleanedKeyword(searchEngineId, languageId, keywordEntity, entity.getCityId());
					    } else {
						    map = getAvgSearchVolumeAndCPCForCleanedKeyword(searchEngineId, languageId, keywordEntity);
					    }
			    	} catch (Exception e1) {
			    		e1.printStackTrace();
					}
				}
			    if (map != null) {
				    sov = map.get("avg_monthly_search_volume") == null ? null : Long.valueOf(map.get("avg_monthly_search_volume").toString());
				    cpc = map.get("cost_per_click") == null ? null : Float.valueOf(map.get("cost_per_click").toString());
			    }
	//		    System.out.println("KID:" + entity.getId() + ", city_id:" + entity.getCityId() + ", languageId:" + languageId + ", sov:" + sov + ", cpc:" + cpc);
			    entity.setSearchVolume(sov);
			    entity.setCostPerClick(cpc);
	    	} catch (Exception e) {
	    		e.printStackTrace();
			}
	    }
    }
    
    private Map<String, Object> getAvgSearchVolumeAndCPCForCleanedKeyword(int searchEngineId, int languageId, KeywordEntity keywordEntity,int cityID) {
    	String keywordName = keywordNameRelService.getKeywordName(keywordEntity);
    	String encodeAlterText = null;
		try {
			String originalText = URLDecoder.decode(keywordName, "utf-8");
			String alterText = originalText;
			int tmp = 0;
			do {
				Map<String, String> processInfo = MonthlyKeywordCleanUpCommand.cleanupKeyowrds(alterText);
				if (StringUtils.equalsIgnoreCase(processInfo.get("needReprocess"), "false")) {
					break;
				}
				alterText = processInfo.get("canonicalkeyword");
				tmp++;
				if (tmp > 100)
					System.out.println("----------------------ERROR Orig Text:" + originalText
							+ "----------------------------");
			} while (true);

			encodeAlterText = URLEncoder.encode(alterText, "utf-8");
		} catch (Exception e) {
			e.printStackTrace();
		}
		
		if(encodeAlterText == null) {
			System.out.println("keyword_entity can not find keyword, orginal : " + keywordName);
			return null;
		}

		SeoClarityKeywordEntity scKeywordEntity = seoClarityKeywordEntityDAO.getByKeyword(encodeAlterText);
		if (scKeywordEntity == null) {
			System.out.println("keyword_entity can not find keyword :, encodeAlterText " + encodeAlterText);
			scKeywordEntity = seoClarityKeywordEntityDAO.getByKeyword(keywordName);
			if (scKeywordEntity == null) {
				System.out.println("keyword_entity can not find keyword :, keywordName " + keywordName);
				return null;
			}
		}
		List<Map<String, Object>> res = keywordCityAdwordsDataEntityDAO.getAvgSearchVolumeAndCPC(scKeywordEntity.getId(), languageId, searchEngineId, cityID);
		if (res != null  && res.size() > 0) {
			return res.get(0);
		} else {
			return null;
		}
    }
    
    private Map<String, Object> getAvgSearchVolumeAndCPCForCleanedKeyword(int searchEngineId, int languageId, KeywordEntity keywordEntity) {
    	String keywordName = keywordNameRelService.getKeywordName(keywordEntity);
    	String encodeAlterText = null;
		try {
			String originalText = URLDecoder.decode(keywordName, "utf-8");
			String alterText = originalText;
			int tmp = 0;
			do {
				Map<String, String> processInfo = MonthlyKeywordCleanUpCommand.cleanupKeyowrds(alterText);
				if (StringUtils.equalsIgnoreCase(processInfo.get("needReprocess"), "false")) {
					break;
				}
				alterText = processInfo.get("canonicalkeyword");
				tmp++;
				if (tmp > 100)
					System.out.println("----------------------ERROR Orig Text:" + originalText
							+ "----------------------------");
			} while (true);

			encodeAlterText = URLEncoder.encode(alterText, "utf-8");
		} catch (Exception e) {
			e.printStackTrace();
		}
		
		if(encodeAlterText == null) {
			System.out.println("keyword_entity can not find keyword, orginal : " + keywordName);
			return null;
		}

		SeoClarityKeywordEntity scKeywordEntity = seoClarityKeywordEntityDAO.getByKeyword(encodeAlterText);
		if (scKeywordEntity == null) {
			System.out.println("keyword_entity can not find keyword :, encodeAlterText " + encodeAlterText);
			return null;
		}
		List<Map<String, Object>> list = keywordCityAdwordsDataEntityDAO.getAvgSearchVolumeAndCPC(scKeywordEntity.getId(), languageId, searchEngineId);
		if (list != null && list.size() > 0) {
			return list.get(0);
		}
		return  null;
    }

	public DomainSearchEngineRelEntity getPrimarySE(OwnDomainEntity ownDomainEntity) {
		int searchEngineId = ScKeywordRankManager.getSearchEngineId(ownDomainEntity);
		int languageId = ScKeywordRankManager.getSearchLanguageId(ownDomainEntity);
		String device = SeoClarityKeywordSearchEngineDomainRelationEntity.DEVICE_NOT_MOBILE;
		if (ownDomainEntity.isMobileDomain()) {
			device = SeoClarityKeywordSearchEngineDomainRelationEntity.DEVICE_MOBILE;
		}
		DomainSearchEngineRelEntity resultEntity = new DomainSearchEngineRelEntity();
		resultEntity.setRankcheckSearchEngineId(searchEngineId);
		resultEntity.setRankcheckSearchLanguageid(languageId);
		resultEntity.setDevice(device);
		return resultEntity;
	}

	public List<DomainSearchEngineRelEntity> getAllSERels(OwnDomainEntity ownDomainEntity) {
		List<DomainSearchEngineRelEntity> sERelList = new ArrayList<DomainSearchEngineRelEntity>();
		sERelList.add(getPrimarySE(ownDomainEntity));

		List<DomainSearchEngineRelEntity> domainSERelList = getDomainSearchEngineRels(ownDomainEntity);
		if (domainSERelList != null && domainSERelList.size() > 0) {
			sERelList.addAll(domainSERelList);
		}
		return sERelList;
	}

}
