package seoclarity.backend.upload;

import com.alibaba.fastjson.JSON;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3Client;
import com.amazonaws.services.s3.model.*;
import com.google.gson.Gson;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.hadoop.fs.Path;
import org.apache.parquet.example.data.Group;
import org.apache.parquet.hadoop.ParquetReader;
import org.apache.parquet.hadoop.example.GroupReadSupport;
import seoclarity.backend.dao.actonia.GscClickstreamUploadLogDAO;
import seoclarity.backend.dao.actoniamonitor.UploadFileMonitorDAO;
import seoclarity.backend.dao.clickhouse.gscclicksteam.GscClickSteamDAO;
import seoclarity.backend.dao.clickhouse.gscclicksteam.KeywordStreamDAO;
import seoclarity.backend.entity.actoniamonitor.UploadFileMonitorEntity;
import seoclarity.backend.entity.clickhouse.gscclicksteam.GscClickSteamEntity;
import seoclarity.backend.entity.clickhouse.gscclicksteam.GscClickstreamUploadLogEntity;
import seoclarity.backend.entity.clickhouse.gscclicksteam.KeywordStreamEntity;
import seoclarity.backend.multithread.core.modlefactory.CacheModleFactory;
import seoclarity.backend.multithread.core.thread.command.BaseThreadCommand;
import seoclarity.backend.multithread.core.thread.threadpool.ThreadPoolManager;
import seoclarity.backend.service.ScKeywordSearchVolumeManager;
import seoclarity.backend.utils.*;
import seoclarity.backend.utils.keywordTokenizer.SnowBallAndNgramForForeignLanguages;

import java.io.*;
import java.util.*;

import static seoclarity.backend.entity.clickhouse.gscclicksteam.GscClickstreamUploadLogEntity.MONTH_DATA_FULLY_UPLOADED;

@CommonsLog
public class GSCClickSteamUpload {

    private static final String S3_ACCESS_KEY = "********************";
    private static final String S3_SECRET_KEY = "nb8g5EAGmRjLqiq6lP5L4CkGd1rvuk8w5KOZb48S";
    private static final String S3_BUCKET_NAME = "pd-delivery-eu-central-1";
    private static final String DAILY_S3_BUCKET_FILE_PATH = "search-console/v1/";
    private static final String WEEKLY_S3_BUCKET_FILE_PATH = "search-console-sampled/v1/";

    private static final String FILE_FOLDER = "/home/<USER>/";
    private static final int LOAD_COUNT = 50000;
    private static final int THREAD_COUNT = 5;
    private static final int RETRY_TIMES = 3;

    private static int version = 1;
    private static int sign = 1;
    private static int frequency = 1;//daily

    public static ThreadPoolManager threadPool = ThreadPoolManager.getInstance();

    private Set<String> notFindCountrySet = new HashSet<>();

    private long totalRowCount = 0;
    private Long uploadLogId;

    private ScKeywordSearchVolumeManager scKeywordSearchVolumeManager;
    private GscClickSteamDAO gscClickSteamDAO;
    private UploadFileMonitorDAO uploadFileMonitorDAO;
    private KeywordStreamDAO keywordStreamDAO;
    private GscClickstreamUploadLogDAO gscClickstreamUploadLogDAO;
    private EmailSenderComponent emailSenderComponent;
    private ZeptoMailSenderComponent zeptoMailSenderComponent;

    public GSCClickSteamUpload() {
        scKeywordSearchVolumeManager = SpringBeanFactory.getBean("scKeywordSearchVolumeManager");
        gscClickSteamDAO = SpringBeanFactory.getBean("gscClickSteamDAO");
        uploadFileMonitorDAO = SpringBeanFactory.getBean("uploadFileMonitorDAO");
        keywordStreamDAO = SpringBeanFactory.getBean("keywordStreamDAO");
        gscClickstreamUploadLogDAO = SpringBeanFactory.getBean("gscClickstreamUploadLogDAO");
        emailSenderComponent = SpringBeanFactory.getBean("emailSenderComponent");
        zeptoMailSenderComponent = SpringBeanFactory.getBean("zeptoMailSenderComponent");
    }

    public static void main(String args[]) throws Exception {

        CommonUtils.initThreads(THREAD_COUNT);
//        testParquetReader();
//        GSCClickSteamUpload gscClickSteamUpload = new GSCClickSteamUpload();
//        gscClickSteamUpload.process("/home/<USER>/2020-12-05/66f2ca6d.042dab22ef3af8f6fe2ddefe7fda439f.parquet");

        if (args != null && args.length >= 1 && StringUtils.isNotBlank(args[0])) {
            if (args[0].equalsIgnoreCase("daily")) {
                frequency = UploadFileMonitorEntity.FREQUENCY_DAILY;
            } else if (args[0].equalsIgnoreCase("weekly")) {
                frequency = UploadFileMonitorEntity.FREQUENCY_WEEKLY;
            }
        }

        String processDate = "";

        if (args != null && args.length >= 2) {
            processDate = args[1];
        } else {
            processDate = FormatUtils.formatDate(DateUtils.addDays(new Date(), -7), FormatUtils.DATE_PATTERN_2);
        }

        GSCClickSteamUpload gscClickSteamUpload = new GSCClickSteamUpload();
        //process upload
        Integer startFileNo = null;
        Integer endFileNo = null;
        if(args != null && args.length >= 4){
            startFileNo = Integer.parseInt(args[2]);
            endFileNo = Integer.parseInt(args[3]);
        }

        gscClickSteamUpload.downloadFileFromS3(processDate, startFileNo, endFileNo);

//        gscClickSteamUpload.uploadKeywordStream(Integer.parseInt(args[0]), Integer.parseInt(args[1]), args[2], args[3]);
//
//        for(String filePath: fileList){
//            try {
//                gscClickSteamUpload.process(filePath);
//            }catch (Exception e){
//                e.printStackTrace();
//                continue;
//            }
//        }

    }

    private List<String> downloadFileFromS3(String fileDate, Integer startFileNo, Integer endFileNo) {

        log.info("===downloadFile fileDate: " + fileDate + ",startFileNo:" + startFileNo + ",endFileNo:" + endFileNo);
        List<String> resultList = new ArrayList<>();
        File downloadFileFolder = new File(FILE_FOLDER);
        if (!downloadFileFolder.exists()) {
            downloadFileFolder.mkdirs();
        }
        try {
            AWSCredentials credentials = new BasicAWSCredentials(S3_ACCESS_KEY, S3_SECRET_KEY);
            AmazonS3 s3client = new AmazonS3Client(credentials);
            String s3Folder = "";
            if (frequency == UploadFileMonitorEntity.FREQUENCY_DAILY) {
                s3Folder = DAILY_S3_BUCKET_FILE_PATH + "dt=" + fileDate + "/";
            } else if (frequency == UploadFileMonitorEntity.FREQUENCY_WEEKLY) {
                s3Folder = WEEKLY_S3_BUCKET_FILE_PATH + "dt=" + fileDate + "/";
            }

            List<String> keyList = new ArrayList<>();
            ObjectListing objectListing = objectListing = s3client.listObjects(S3_BUCKET_NAME, s3Folder);
            List<S3ObjectSummary> s3ObjectSummaryList = objectListing.getObjectSummaries();
            if (!CollectionUtils.isEmpty(s3ObjectSummaryList)) {
                for (S3ObjectSummary s3ObjectSummary : s3ObjectSummaryList) {
                    String key = s3ObjectSummary.getKey();
//                    if(!key.contains("66f2ca6d.042dab22ef3af8f6fe2ddefe7fda439f.parquet")){
//                        continue;
//                    }
                    if (!key.contains(".parquet")) {
                        continue;
                    }
                    //key:search-console/v1/dt=2021-06-12/part-00000-0450d061-1622-4be3-b716-7c41653cfedc-c000.snappy.parquet
                    if(startFileNo != null && startFileNo != null){
                        String s3FileName = key.replaceAll(s3Folder, "");
                        String[] s3FileNameSplit = s3FileName.split("-");
                        int fileNo = Integer.parseInt(s3FileNameSplit[1]);
                        if(fileNo < startFileNo || fileNo > endFileNo){
                            log.info("====skip fileNo:" + fileNo);
                            continue;
                        }
                    }

                    if (key.startsWith(s3Folder) && StringUtils.isNotEmpty(key.replaceAll(s3Folder, ""))) {
                        log.info("===add key:" + s3ObjectSummary.getKey());
                        keyList.add(key);
                    }
                }
            }
            //分页
            while(objectListing.isTruncated()){
                objectListing = s3client.listNextBatchOfObjects(objectListing);
                s3ObjectSummaryList = objectListing.getObjectSummaries();
                if (!CollectionUtils.isEmpty(s3ObjectSummaryList)) {
                    for (S3ObjectSummary s3ObjectSummary : s3ObjectSummaryList) {
                        String key = s3ObjectSummary.getKey();
//                    if(!key.contains("66f2ca6d.042dab22ef3af8f6fe2ddefe7fda439f.parquet")){
//                        continue;
//                    }
                        if (!key.contains(".parquet")) {
                            continue;
                        }
                        //key:search-console/v1/dt=2021-06-12/part-00000-0450d061-1622-4be3-b716-7c41653cfedc-c000.snappy.parquet
                        if(startFileNo != null && startFileNo != null){
                            String s3FileName = key.replaceAll(s3Folder, "");
                            String[] s3FileNameSplit = s3FileName.split("-");
                            int fileNo = Integer.parseInt(s3FileNameSplit[1]);
                            if(fileNo < startFileNo || fileNo > endFileNo){
                                log.info("====skip fileNo:" + fileNo);
                                continue;
                            }
                        }

                        if (key.startsWith(s3Folder) && StringUtils.isNotEmpty(key.replaceAll(s3Folder, ""))) {
                            log.info("===add key:" + s3ObjectSummary.getKey());
                            keyList.add(key);
                        }
                    }
                }

            }
            log.info("===keyList size:" + keyList.size());
            if (!CollectionUtils.isEmpty(keyList)) {

                Date weekLastDay = FormatUtils.toDate(fileDate, FormatUtils.DATE_PATTERN_2);
                Date weekStartDay = DateUtils.addDays(weekLastDay, -6);
                Calendar cal = Calendar.getInstance();
                cal.setTime(weekStartDay);
                int month = NumberUtils.toInt(FormatUtils.formatDate(cal.getTime(), "yyyyMM"));
                Integer maxWeekIndexInMonth = gscClickstreamUploadLogDAO.getMaxWeekIndexInMonth(frequency, month);
                if(maxWeekIndexInMonth == null){
                    maxWeekIndexInMonth = 1;
                }else {
                    maxWeekIndexInMonth ++;
                }

                int weekStartDayInt = FormatUtils.formatDateToYyyyMmDd(weekStartDay);
                int weekLastDayInt = FormatUtils.formatDateToYyyyMmDd(weekLastDay);


                GscClickstreamUploadLogEntity gscClickstreamUploadLogEntity = gscClickstreamUploadLogDAO.getByStartDayEndDay(frequency, month, weekStartDayInt, weekLastDayInt);
                if(gscClickstreamUploadLogEntity == null){
                    gscClickstreamUploadLogEntity = new GscClickstreamUploadLogEntity();
                    gscClickstreamUploadLogEntity.setDataType(frequency);
                    gscClickstreamUploadLogEntity.setMonth(month);
                    gscClickstreamUploadLogEntity.setWeekIndexInMonth(maxWeekIndexInMonth);
                    gscClickstreamUploadLogEntity.setWeekStartDate(weekStartDayInt);
                    gscClickstreamUploadLogEntity.setWeekEndDate(weekLastDayInt);
                    gscClickstreamUploadLogEntity.setStatus(GscClickstreamUploadLogEntity.UPLOAD_DIS_GSC_CLICK_STREAM_STATUS_PROCESSING);
                    gscClickstreamUploadLogEntity.setUploadStartTime(new Date());

                    gscClickstreamUploadLogDAO.insert(gscClickstreamUploadLogEntity);
                    GscClickstreamUploadLogEntity insertEntity = gscClickstreamUploadLogDAO.getByUniqueKey(frequency, month, maxWeekIndexInMonth);
                    if(insertEntity == null){
                        log.error("===insert log error:dataType:" + frequency + ",month:" + month + ",maxWeekIndexInMonth:" + maxWeekIndexInMonth);
                        return null;
                    }
                    uploadLogId = insertEntity.getId();
                    log.info("inset log:" + frequency + "," + month + "," + weekStartDay + "," + weekLastDay);
                }else {
                    log.info("===loadLog exist:" + frequency + "," + month + "," + weekStartDayInt + "," + weekLastDayInt);
                    uploadLogId = gscClickstreamUploadLogEntity.getId();
                }


                for (String key : keyList) {
                    String fileName = key.replaceAll(s3Folder, "");
                    String uploadFilePath = fileDate + File.separator + fileName;
                    UploadFileMonitorEntity uploadFileMonitorEntity = uploadFileMonitorDAO.getUploadFileByUniqueKey(UploadFileMonitorEntity.UPLOAD_TYPE_GSC_CLICK_STEAM, uploadFilePath, version, frequency);
                    if (uploadFileMonitorEntity != null) {
                        log.error("===file uploaded,skip:" + uploadFilePath);
                        continue;
                    }

                    GetObjectRequest rangeObjectRequest = new GetObjectRequest(S3_BUCKET_NAME, key);
                    S3Object objectPortion = s3client.getObject(rangeObjectRequest);

                    File folder = new File(FILE_FOLDER + frequency + File.separator + fileDate);
                    if (!folder.exists()) {
                        folder.mkdirs();
                    }
                    File outFile = new File(FILE_FOLDER + frequency + File.separator + uploadFilePath);
                    if (objectPortion != null && objectPortion.getObjectContent() != null) {
                        InputStream objectData = objectPortion.getObjectContent();
                        FileOutputStream fos = new FileOutputStream(outFile);
                        byte[] readBuf = new byte[1024];
                        int readLen;
                        while ((readLen = objectData.read(readBuf)) > 0) {
                            fos.write(readBuf, 0, readLen);
                        }
                        objectData.close();
                        fos.close();

                        System.out.println("===downloadFile success : " + outFile.getAbsolutePath());
                        resultList.add(outFile.getAbsolutePath());
                        //upload
                        try {
                            process(outFile.getAbsolutePath());
                        } catch (Exception e) {
                            e.printStackTrace();
                            log.error("===load error : " + outFile.getAbsolutePath());
                            String message = "GscClickStream load Error: " +  outFile.getAbsolutePath();
                            sendMailReport(message);
                        }
                    }

                }
//                totalRowCount = 4200000000l;
                gscClickstreamUploadLogDAO.updateById(uploadLogId, GscClickstreamUploadLogEntity.UPLOAD_DIS_GSC_CLICK_STREAM_STATUS_SUCCESS, new Date(), totalRowCount, null);

                int monthLastDay = FormatUtils.formatDateToYyyyMmDd(FormatUtils.getMonthLastDay(weekStartDay));
                if(weekLastDayInt >= monthLastDay){
                    log.info("===updateMonthDataFullyUploaded:" + month);
                    gscClickstreamUploadLogDAO.updateMonthDataFullyUploaded(uploadLogId, MONTH_DATA_FULLY_UPLOADED);
                }

            }


        } catch (Exception e1) {
            e1.printStackTrace();
            return null;
        }
        log.info("===resultList: " + JSON.toJSONString(resultList));
        return resultList;
    }

    private void process(String filePath) throws Exception {

        System.out.println("=== filePath:" + filePath);
        String uploadFilePath = filePath.replaceAll(FILE_FOLDER + frequency + File.separator, "");
        File file = new File(filePath);
        try {
            UploadFileMonitorEntity uploadFileMonitorEntity = uploadFileMonitorDAO.getUploadFileByUniqueKey(UploadFileMonitorEntity.UPLOAD_TYPE_GSC_CLICK_STEAM, uploadFilePath, version, frequency);
            if (uploadFileMonitorEntity != null) {
                log.error("===file already uploaded,skip:" + uploadFilePath);
                file.delete();
                return;
            } else {
                uploadFileMonitorEntity = new UploadFileMonitorEntity();
                uploadFileMonitorEntity.setFileName(uploadFilePath);
                uploadFileMonitorEntity.setFrequency(frequency);
                uploadFileMonitorEntity.setUploadType(UploadFileMonitorEntity.UPLOAD_TYPE_GSC_CLICK_STEAM);
                uploadFileMonitorEntity.setVersion(version);
                uploadFileMonitorDAO.insert(uploadFileMonitorEntity);
            }

            Long startTime = System.currentTimeMillis();
            //read file
            loadParquetFile(filePath);
            log.info("===cost time:" + (System.currentTimeMillis() - startTime) / (1000));
            uploadFileMonitorDAO.updateStatus(UploadFileMonitorEntity.UPLOAD_TYPE_GSC_CLICK_STEAM, uploadFilePath, version, UploadFileMonitorEntity.UPLOAD_STATUS_SUCCESS, frequency);
            file.delete();

        } catch (Exception e) {
            uploadFileMonitorDAO.updateStatus(UploadFileMonitorEntity.UPLOAD_TYPE_GSC_CLICK_STEAM, uploadFilePath, version, UploadFileMonitorEntity.UPLOAD_STATUS_ERROR, frequency);
            gscClickstreamUploadLogDAO.updateById(uploadLogId, GscClickstreamUploadLogEntity.UPLOAD_DIS_GSC_CLICK_STREAM_STATUS_FAILURE, new Date(), totalRowCount, "load error1");
            String message = "GscClickStream load Error: " + filePath;
            sendMailReport(message);
            throw e;
        }

    }

    private void loadParquetFile(String filePath) throws Exception {
        threadPool.init();
//        String uploadFilePath = filePath.replaceAll(FILE_FOLDER, "");

        Path file = new Path(filePath);
        ParquetReader.Builder<Group> builder = ParquetReader.builder(new GroupReadSupport(), file);
        ParquetReader<Group> reader = null;
        try {
            reader = builder.build();
            Group line = null;
            List<Group> groupList = new ArrayList<>();
            while ((line = reader.read()) != null) {

                totalRowCount++;
                groupList.add(line);

                if (groupList != null && groupList.size() >= LOAD_COUNT) {

                    System.out.println("==== uploading ===== size :" + groupList.size());
                    try {
                        processUpload(groupList);
                    } catch (Exception e) {
                        throw e;
                    }

                    groupList = new ArrayList<>();
                }

            }

            if (!CollectionUtils.isEmpty(groupList)) {
                try {
                    processUpload(groupList);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            do {
                try {
                    Thread.sleep(100);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            } while (threadPool.getThreadPool().getActiveCount() > 0);
            threadPool.destroy();

        } catch (IOException e) {
            e.printStackTrace();
            throw e;
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    private void loadCsvFile(String filePath) {

        String uploadFilePath = filePath.replaceAll(FILE_FOLDER, "");

        List<GscClickSteamEntity> insertList = new ArrayList<GscClickSteamEntity>();
        GscClickSteamEntity gscClickSteamEntity;
        int num = 0;
        String content = "";

        try {

            BufferedReader bf = new BufferedReader(new FileReader(filePath));
            while (content != null) {
                content = bf.readLine();

                if (content == null) {
                    bf.close();
                    break;
                }

                if (StringUtils.startsWith(content, "device_id")) {
                    System.out.println("Skiped for header!!!!!");
                    continue;
                }

                num++;

                String[] parts;

                if (StringUtils.isBlank(content)) {
                    System.out.println("this line is empty:" + content);
                    continue;
                }

                if (num == 1 && content.contains("siteurl") || content.contains("page") || content.contains("query")) {
                    System.out.println("skip header");
                    continue;
                }

                CSVParser csvParser = new CSVParser(',');
                try {
                    parts = csvParser.parseLine(content);
//                        parts = content.split("\u0001");
//                        parts = content.split(",");
                } catch (Exception e) {
                    System.out.println("======parse again!!!");

                    try {
//							parts = csvParser.parseLine(new String(content.getBytes(),"utf-8"));
                        parts = StringUtils.split(new String(content.getBytes(), "utf-8"), "^A");
                    } catch (Exception e2) {
                        System.out.println("======skiped!!!" + content);
                        continue;
                    }
                }

                System.out.println(new Gson().toJson(parts));
//                    if (num % 50000 == 0) {
//                        System.out.println("processing on line : " + num);
//
//                        System.out.println(new Gson().toJson(parts));
//                    }
//
                if (parts == null || parts.length != 11) {
                    System.out.println(new Gson().toJson(parts));
                    System.out.println("column size can not matched !!! parts:" + parts.length + ", " + content);
                } else {

                    gscClickSteamEntity = new GscClickSteamEntity();

                    String keyword = StringUtils.lowerCase(parts[2]);

                    gscClickSteamEntity.setSiteurl(parts[0]);
                    gscClickSteamEntity.setClicks(Float.parseFloat(parts[6]));
                    gscClickSteamEntity.setCtr(Float.parseFloat(parts[7]));
                    gscClickSteamEntity.setImpressions(Float.parseFloat(parts[8]));
                    gscClickSteamEntity.setCountryCd(parts[4]);
                    gscClickSteamEntity.setDevice(parts[5]);
                    gscClickSteamEntity.setKeywordName(parts[2]);
                    gscClickSteamEntity.setUrl(parts[1]);
                    gscClickSteamEntity.setLogDate(FormatUtils.toDate(parts[3], FormatUtils.DATE_PATTERN_2));
                    gscClickSteamEntity.setPosition(Float.parseFloat(parts[9]));

                    String[] steam = scKeywordSearchVolumeManager.getWordSteamByCountryAlpha(keyword, parts[4]);
                    log.info("==steam:" + JSON.toJSONString(steam));
                    gscClickSteamEntity.setStream(steam);
                    gscClickSteamEntity.setVersioning(version);
                    gscClickSteamEntity.setSign(sign);
                    gscClickSteamEntity.setAttrsKeyArray(new String[]{});
                    gscClickSteamEntity.setAttrsValueArray(new String[]{});
                    insertList.add(gscClickSteamEntity);
                }

                if (insertList != null && insertList.size() >= 100000) {
                    int retry = 0;
                    while (true){
                        try {
                            gscClickSteamDAO.insertForBatch(insertList, frequency);
                            break;
                        }catch (Exception e){
                            e.printStackTrace();
                            if(retry >= RETRY_TIMES){
                                log.info("===OVERREARY:" + retry);
                                throw e;
                            }
                            try {
                                Thread.sleep(1000 * 60);
                            } catch (Exception ex) {
                                ex.printStackTrace();
                            }
                            retry ++;
                        }
                    }
                    System.out.println("==== uploading ===== size :" + insertList.size());
                    insertList = new ArrayList<>();
                }
            }

            bf.close();

            if (insertList != null && insertList.size() >= 0) {
                System.out.println("==== uploading ===== size :" + insertList.size());
                int retry = 0;
                while (true){
                    try {
                        gscClickSteamDAO.insertForBatch(insertList, frequency);
                        break;
                    }catch (Exception e){
                        e.printStackTrace();
                        if(retry >= RETRY_TIMES){
                            log.info("===OVERREARY:" + retry);
                            throw e;
                        }
                        try {
                            Thread.sleep(1000 * 60);
                        } catch (Exception ex) {
                            ex.printStackTrace();
                        }
                        retry ++;
                    }
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
            uploadFileMonitorDAO.updateStatus(UploadFileMonitorEntity.UPLOAD_TYPE_GSC_CLICK_STEAM, uploadFilePath, version, UploadFileMonitorEntity.UPLOAD_STATUS_ERROR, frequency);
        }

    }


    private void processUpload(List<Group> groupList) throws Exception{
        String ipAddress = CacheModleFactory.getInstance().getAliveIpAddress();
        while (ipAddress == null) {
            try {
                Thread.sleep(100);
                ipAddress = CacheModleFactory.getInstance().getAliveIpAddress();
            } catch (Exception e) {
                throw e;
            }
        }
        GSCClickSteamUpload.Command cmd = new GSCClickSteamUpload.Command(ipAddress, groupList);
        cmd.setStatus(true);
        try {
            threadPool.execute(cmd);
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    class Command extends BaseThreadCommand {
        private String ipAddress;
        private List<Group> groupList;
        private GscClickSteamDAO gscClickSteamDAO;

        public Command(String ipAddress, List<Group> groupList) {
            this.ipAddress = ipAddress;
            this.groupList = groupList;
            gscClickSteamDAO = SpringBeanFactory.getBean("gscClickSteamDAO");
        }

        @Override
        protected void execute() throws Exception {
            long a = System.currentTimeMillis();

            Set<String> existKwSet = new HashSet<>();
            List<GscClickSteamEntity> processList = new ArrayList<>();
//            List<KeywordStreamEntity> keywordStreamInsetList = new ArrayList<>();
            log.info("====execute gscClickSteamList:" + groupList.size());
            for (Group line : groupList) {
                try {

//                    if (line.getString("date", 0).equalsIgnoreCase("2021-03-29")) {
//                        continue;
//                    }

                    GscClickSteamEntity gscClickSteamEntity = new GscClickSteamEntity();

                    String keyword = StringUtils.lowerCase(line.getString("query", 0));
                    String countryCode = line.getString("country", 0);
                    gscClickSteamEntity.setSiteurl(line.getString("siteUrl", 0));
                    gscClickSteamEntity.setClicks(NumberUtils.toFloat(String.valueOf(line.getLong("clicks", 0))));
                    gscClickSteamEntity.setCtr(line.getFloat("ctr", 0));
                    gscClickSteamEntity.setImpressions(NumberUtils.toFloat(String.valueOf(line.getLong("impressions", 0))));
                    gscClickSteamEntity.setCountryCd(countryCode);
                    gscClickSteamEntity.setDevice(line.getString("device", 0).toLowerCase());
                    gscClickSteamEntity.setKeywordName(keyword);
                    gscClickSteamEntity.setUrl(line.getString("siteUrl", 0));
                    gscClickSteamEntity.setLogDate(FormatUtils.toDate(line.getString("date", 0), FormatUtils.DATE_PATTERN_2));
                    gscClickSteamEntity.setPosition(line.getFloat("position", 0));

                    String[] steam = null;
                    try {
                        steam = scKeywordSearchVolumeManager.getWordSteamByCountryAlpha(keyword, countryCode);
                    } catch (Exception e) {
                        steam = new String[]{};
                    }
//                    log.info("==steam:" + JSON.toJSONString(steam));
                    gscClickSteamEntity.setStream(steam);
                    gscClickSteamEntity.setVersioning(version);
                    gscClickSteamEntity.setSign(sign);
                    gscClickSteamEntity.setAttrsKeyArray(new String[]{});
                    gscClickSteamEntity.setAttrsValueArray(new String[]{});

                    processList.add(gscClickSteamEntity);

                    //keyword stream
//                    String key = countryCode + "!_!" + keyword;
//                    if(!existKwSet.contains(key)){
//                        existKwSet.add(key);
//                        if(setKeywordStream(countryCode, keyword) != null){
//                            keywordStreamInsetList.add(setKeywordStream(countryCode, keyword));
//                        }
//
//                    }else {
////                        log.info("SKIPEXISTKw:" + key);
//                    }

                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            log.info("existKwSet size:" + existKwSet.size());
//            System.out.println("=kwList:" + kwList.length + ", processList:" + processList.size());

            // upload
            processUpload(processList);
//            processUploadKeywordStream(keywordStreamInsetList);

            long b = System.currentTimeMillis();
            System.out.println("ipAddress" + ipAddress + ",End command Cost time: " + (b - a) * 1.0 / 1000 + "s");
            CacheModleFactory.getInstance().setAliveIpAddress(ipAddress);
        }

        private void processUpload(List<GscClickSteamEntity> processList) {

            // insert
            try {
                if (CollectionUtils.isNotEmpty(processList)) {
                    log.info("====insert processList:" + processList.size());
                    int retry = 0;
                    while (true){
                        try {
                            gscClickSteamDAO.insertForBatch(processList, frequency);
                            break;
                        }catch (Exception e){
                            e.printStackTrace();
                            if(retry >= RETRY_TIMES){
                                log.info("===OVERREARY:" + retry);
                                throw e;
                            }
                            try {
                                Thread.sleep(1000 * 60);
                            } catch (Exception ex) {
                                ex.printStackTrace();
                            }
                            retry ++;
                        }
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        private KeywordStreamEntity setKeywordStream(String countryCode, String keyword){

            if (!ClickStreamUtils.COUNTRY_MAP.containsKey(countryCode)) {
//                        log.error("==notFindCountry:" + country);
                notFindCountrySet.add(countryCode);
                return null;
            }
            String[] res = ClickStreamUtils.format(keyword);
            if (res == null) {
//                        log.error("===formatKwnNULL:" + kw);
                return null;
            }
            String[] params = ClickStreamUtils.COUNTRY_MAP.get(countryCode);
            String code = params[0];
            String languageName = params[1];
            int engineId = Integer.valueOf(params[2]);
            int languageId = Integer.valueOf(params[3]);

            String kw = res[0];
            String decodedKw = res[1];

            String kwName = ClickStreamUtils.formatForAnalyzer(decodedKw);

            KeywordStreamEntity insetEntity = new KeywordStreamEntity();
            insetEntity.setCountryCd(countryCode);
            insetEntity.setEngineId(engineId);
            insetEntity.setLanguageId(languageId);
            insetEntity.setKeywordName(kw);

            ////分词
            List<String> word = new ArrayList<String>();
            List<String> stream = new ArrayList<String>();

            List<String> keywordVariationOneword = new ArrayList<String>();
            List<String> keywordVariationNgram = new ArrayList<String>();

            if (StringUtils.equalsIgnoreCase(languageName, "Arabic")) {
                word.addAll(SnowBallAndNgramForForeignLanguages.wordTokenizer(kwName, "ar"));
            } else {
                word.addAll(SnowBallAndNgramForForeignLanguages.wordTokenizer(kwName, code));
            }
            stream.addAll(SnowBallAndNgramForForeignLanguages.wordStemmerTokenizer(kwName, languageName));
            keywordVariationOneword.addAll(SnowBallAndNgramForForeignLanguages.oneWordNgramTokenizer(kwName));
            keywordVariationNgram.addAll(SnowBallAndNgramForForeignLanguages.wordNgramTokenizer(kwName, false));

            insetEntity.setWord(word);
            insetEntity.setStream(stream);

            if (stream.size() == 0) {
                stream.add(kwName);
            }

            insetEntity.setKeywordVariationOneword(keywordVariationOneword);
            insetEntity.setKeywordVariationNgram(MonthlyKeywordTokenizerUploadV2.setPlaceHolder(keywordVariationNgram));

            return insetEntity;
        }

        private void processUploadKeywordStream(List<KeywordStreamEntity> processList) {

            // insert
            try {
                if (CollectionUtils.isNotEmpty(processList)) {
                    log.info("====ins KStream processList:" + processList.size());
                    keywordStreamDAO.insertForBatch(processList);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        @Override
        protected void undo() throws Exception {
        }

    }

    private static void testParquetReader() throws IOException {
        Path file = new Path(
                "/home/<USER>/66f2ca6d.042dab22ef3af8f6fe2ddefe7fda439f.parquet");
        ParquetReader.Builder<Group> builder = ParquetReader.builder(new GroupReadSupport(), file);

        ParquetReader<Group> reader = null;
        try {
            reader = builder.build();
            Group line = null;
            int i = 0;
            while ((line = reader.read()) != null && i < 10) {
//            System.out.println(line.toString());
                log.info("====1: " + line.getString("siteUrl", 0));
                log.info("====2: " + line.getString("page", 0));
                log.info("====3: " + line.getString("query", 0));
                log.info("====4: " + line.getString("date", 0));
                log.info("====5: " + line.getString("country", 0));
                log.info("====6: " + line.getString("device", 0));
                log.info("====7: " + line.getLong("clicks", 0));
                log.info("====8: " + line.getFloat("ctr", 0));
                log.info("====9: " + line.getLong("impressions", 0));
                log.info("====10: " + line.getFloat("position", 0));
                i++;
            }

        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }

    }


    private void uploadKeywordStream(int startNo, int endNo, String queryTableName, String insertTableName) {
        log.info("============================================startNo:" + startNo + ",endNo:" + endNo);
        threadPool.init();

        File file = new File("/home/<USER>/gscClickStream/notFindCountry" + FormatUtils.formatDate(new Date(), "yyyyMMddHHmmss") + ".txt");


        int startHashNo = 1000;
        int maxHashNo = 9999;
        for (int i = startNo; i <= endNo; i++) {
            log.info("===startHashNo:" + i);

            try {

                List<KeywordStreamEntity> keywordStreamList = keywordStreamDAO.getKeywordsByHash(i);
                log.info("===keywordStreamList size:" + keywordStreamList.size());

                processUploadKeywordStream(keywordStreamList);

                log.info("===notFindCountrySet size:" + notFindCountrySet.size());
                log.info("===notFindCountrySet:" + new Gson().toJson(notFindCountrySet));


            } catch (Exception e) {
                e.printStackTrace();
                continue;
            }
        }

        try {
            FileUtils.writeLines(file, "UTF-8", notFindCountrySet, true);
            notFindCountrySet.clear();
        }catch (Exception e){
            e.printStackTrace();
        }

        do {
            try {
                Thread.sleep(100);
            } catch (Exception e) {
                e.printStackTrace();
            }
        } while (threadPool.getThreadPool().getActiveCount() > 0);

        threadPool.destroy();
    }

    private void processUploadKeywordStream(List<KeywordStreamEntity> keywordStreamList) {
        String ipAddress = CacheModleFactory.getInstance().getAliveIpAddress();
        while (ipAddress == null) {
            try {
                Thread.sleep(100);
                ipAddress = CacheModleFactory.getInstance().getAliveIpAddress();
            } catch (Exception e) {
            }
        }
        GSCClickSteamUpload.KeywordStreamCommand cmd = new GSCClickSteamUpload.KeywordStreamCommand(ipAddress, keywordStreamList);
        cmd.setStatus(true);
        try {
            threadPool.execute(cmd);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    class KeywordStreamCommand extends BaseThreadCommand {
        private String ipAddress;
        private List<KeywordStreamEntity> keywordStreamList = new ArrayList<>();
        private KeywordStreamDAO keywordStreamDAO;

        public KeywordStreamCommand(String ipAddress, List<KeywordStreamEntity> keywordStreamList) {
            this.ipAddress = ipAddress;
            this.keywordStreamList = keywordStreamList;
            keywordStreamDAO = SpringBeanFactory.getBean("keywordStreamDAO");
        }

        @Override
        protected void execute() throws Exception {
            long a = System.currentTimeMillis();

            log.info("====execute keywordStreamList:" + keywordStreamList.size());
            if (CollectionUtils.isNotEmpty(keywordStreamList)) {
                List<KeywordStreamEntity> insetList = new ArrayList<>();

                for (KeywordStreamEntity keywordStreamEntity : keywordStreamList) {

                    String country = keywordStreamEntity.getCountryCd();
                    if (!ClickStreamUtils.COUNTRY_MAP.containsKey(country)) {
//                        log.error("==notFindCountry:" + country);
                        notFindCountrySet.add(country);
                        continue;
                    }

                    String kw = keywordStreamEntity.getKeywordName();
                    String[] res = ClickStreamUtils.format(kw);
                    if (res == null) {
//                        log.error("===formatKwnNULL:" + kw);
                        continue;
                    }

                    String[] params = ClickStreamUtils.COUNTRY_MAP.get(country);
                    String code = params[0];
                    String languageName = params[1];
                    int engineId = Integer.valueOf(params[2]);
                    int languageId = Integer.valueOf(params[3]);

                    kw = res[0];
                    String decodedKw = res[1];

                    String kwName = ClickStreamUtils.formatForAnalyzer(decodedKw);

                    KeywordStreamEntity insetEntity = new KeywordStreamEntity();
                    insetEntity.setCountryCd(keywordStreamEntity.getCountryCd());
                    insetEntity.setEngineId(engineId);
                    insetEntity.setLanguageId(languageId);
                    insetEntity.setKeywordName(kw);

                    ////分词
                    List<String> word = new ArrayList<String>();
                    List<String> stream = new ArrayList<String>();

                    List<String> keywordVariationOneword = new ArrayList<String>();
                    List<String> keywordVariationNgram = new ArrayList<String>();

                    if (StringUtils.equalsIgnoreCase(languageName, "Arabic")) {
                        word.addAll(SnowBallAndNgramForForeignLanguages.wordTokenizer(kwName, "ar"));
                    } else {
                        word.addAll(SnowBallAndNgramForForeignLanguages.wordTokenizer(kwName, code));
                    }
                    stream.addAll(SnowBallAndNgramForForeignLanguages.wordStemmerTokenizer(kwName, languageName));
                    keywordVariationOneword.addAll(SnowBallAndNgramForForeignLanguages.oneWordNgramTokenizer(kwName));
                    keywordVariationNgram.addAll(SnowBallAndNgramForForeignLanguages.wordNgramTokenizer(kwName, false));

                    insetEntity.setWord(word);
                    insetEntity.setStream(stream);

                    if (stream.size() == 0) {
                        stream.add(kwName);
                    }

                    insetEntity.setKeywordVariationOneword(keywordVariationOneword);
                    insetEntity.setKeywordVariationNgram(MonthlyKeywordTokenizerUploadV2.setPlaceHolder(keywordVariationNgram));

                    insetList.add(insetEntity);

                    if (insetList.size() >= 50000) {
                        log.info("====insert insetList:" + insetList.size());
                        int retry = 0;
                        while (true){
                            try {
                                keywordStreamDAO.insertForBatch(insetList);
                                break;
                            }catch (Exception e){
                                e.printStackTrace();
                                if(retry >= RETRY_TIMES){
                                    log.info("===OVERREARY:" + retry);
                                    throw e;
                                }
                                try {
                                    Thread.sleep(1000 * 60);
                                } catch (Exception ex) {
                                    ex.printStackTrace();
                                }
                                retry ++;
                            }
                        }
                        insetList = new ArrayList<>();
                    }
                }

                if (CollectionUtils.isNotEmpty(insetList)) {
                    log.info("====insert2 insetList:" + insetList.size());
                    int retry = 0;
                    while (true){
                        try {
                            keywordStreamDAO.insertForBatch(insetList);
                            break;
                        }catch (Exception e){
                            e.printStackTrace();
                            if(retry >= RETRY_TIMES){
                                log.info("===OVERREARY:" + retry);
                                throw e;
                            }
                            try {
                                Thread.sleep(1000 * 60);
                            } catch (Exception ex) {
                                ex.printStackTrace();
                            }
                            retry ++;
                        }
                    }
                }


                long b = System.currentTimeMillis();
                System.out.println("ipAddress" + ipAddress + ",End command Cost time: " + (b - a) * 1.0 / 1000 + "s");
                CacheModleFactory.getInstance().setAliveIpAddress(ipAddress);
            }


        }

        @Override
        protected void undo() throws Exception {

        }

    }

    private void sendMailReport(String message) {
        Map<String, Object> reportMap = new HashMap<String, Object>();
        reportMap.put("userName", "Jason");
        reportMap.put("successMessage", message);
        String subject = "Error GscClickStream Load";
        String emailTo = "<EMAIL>";
        String[] ccTo = new String[]{"<EMAIL>"};
//        emailSenderComponent.sendMimeMultiPartMailAndBcc(emailTo, ccTo, subject, "mail_resource_operate_report.txt", "mail_resource_operate_report.html", reportMap);
        zeptoMailSenderComponent.sendMimeMultiPartZeptoMailAndBccByFunctionType(emailTo, ccTo, subject, "mail_resource_operate_report.txt","mail_resource_operate_report.html", reportMap, null,
                ZeptoMailSenderComponent.FUNCTION_TYPE_BACKEND_INTERNAL, null, null);
    }

}
