package seoclarity.backend.upload;

import com.google.gson.Gson;
import org.apache.log4j.Logger;
import seoclarity.backend.dao.actonia.TargetUrlEntityDAO;
import seoclarity.backend.dao.actonia.TargeturlRecommendedEntityDAO;
import seoclarity.backend.entity.actonia.TargetUrlEntity;
import seoclarity.backend.entity.actonia.TargeturlRecommendedEntity;
import seoclarity.backend.utils.CollectionUtils;
import seoclarity.backend.utils.SpringBeanFactory;

import java.util.*;
import java.util.stream.Collectors;

public class TargeturlRecommendedInsert {

    private static final Logger log = Logger.getLogger(TargeturlRecommendedInsert.class);

    public TargeturlRecommendedEntityDAO targeturlRecommendedEntityDAO;
    public TargetUrlEntityDAO targetUrlEntityDAO;
    public static final int DB_QUERY_STR = 500;
    public static final int DB_INSERT_ENTITY = 10000;

    public TargeturlRecommendedInsert() {
        targeturlRecommendedEntityDAO = SpringBeanFactory.getBean("targeturlRecommendedEntityDAO");
        targetUrlEntityDAO = SpringBeanFactory.getBean("targetUrlEntityDAO");
    }

    /**
     * mvn -f pom.xml exec:java -Dexec.mainClass="seoclarity.backend.upload.TargeturlRecommendedInsert" -Dexec.args="" -Dexec.cleanupDaemonThreads=false
     * @param args
     */
    public static void main(String[] args) {
        new TargeturlRecommendedInsert().processTest();
    }

    private void processTest() {
        List<TargeturlRecommendedEntity> list =new ArrayList<>();
        TargeturlRecommendedEntity entity = new TargeturlRecommendedEntity();
        entity.setOwnDomainId(4);
        entity.setContentType(2);
        entity.setContentTypeCust("");
        entity.setContent("test");
        entity.setUpdateDate(new Date());
        entity.setAemStatusCd(1);
        entity.setTargeturl("https://www.petvacationhomes.com/edwin/massive/test911");
        list.add(entity);
        insertBatch(4, list);
    }

    public void insertBatch(int ownDomainId, List<TargeturlRecommendedEntity> list) {
        List<String> urls = list.stream().map(TargeturlRecommendedEntity::getUrlMurmur3Hash).collect(Collectors.toList());
        List<List<String>> urlLists = CollectionUtils.splitCollectionBySizeWithStream(urls, DB_QUERY_STR);

        List<TargetUrlEntity> targetUrlEntities = new ArrayList<>();
        for (List<String> urlList : urlLists) {
            List<TargetUrlEntity> targetUrlEntityList = targetUrlEntityDAO.getUrlList(ownDomainId, urlList);
            if (targetUrlEntityList.size() > 0) {
                targetUrlEntities.addAll(targetUrlEntityList);
            }
        }
        Map<String, Long> urlStrPIdMap = targetUrlEntities.stream().collect(Collectors.toMap(TargetUrlEntity::getUrl, TargetUrlEntity::getId, (var3, var4) -> var3));
        List<TargeturlRecommendedEntity> existUrlEntities = list.stream().filter(var -> {
            if (urlStrPIdMap.containsKey(var.getTargeturl())) {
                return true;
            } else {
                log.info("Not Manager URL: " + var.getTargeturl() + ", OID: " + ownDomainId);
                return false;
            }
        }).map(var -> {
            var.setTargeturlId(urlStrPIdMap.get(var.getTargeturl()));
            return var;
        }).collect(Collectors.toList());

        List<Long> urlIdList = new ArrayList<>();
        Map<Long, TargeturlRecommendedEntity> targeturlRecommendedMap = new HashMap<>();
        for(TargeturlRecommendedEntity targeturlRecommended: existUrlEntities){
            urlIdList.add(targeturlRecommended.getTargeturlId());
            targeturlRecommendedMap.put(targeturlRecommended.getTargeturlId(), targeturlRecommended);
        }

        int domainId = existUrlEntities.get(0).getOwnDomainId();
        int contentType = existUrlEntities.get(0).getContentType();
        List<Long> existUrlIdList = targeturlRecommendedEntityDAO.getExistUrlIdList(domainId, contentType, urlIdList);
        log.info("===existUrlIdList:" + existUrlIdList.size());

        if(org.apache.commons.collections.CollectionUtils.isNotEmpty(existUrlIdList)){
            List<TargeturlRecommendedEntity> needUpdateList = new ArrayList<>();
            for(Long targetUrlId: existUrlIdList){
                needUpdateList.add(targeturlRecommendedMap.get(targetUrlId));
                targeturlRecommendedMap.remove(targetUrlId);
            }
            log.info("===needUpdateList size:" + needUpdateList.size());
            targeturlRecommendedEntityDAO.updateBatch(needUpdateList);
        }

        List<TargeturlRecommendedEntity> needInsertEntities = new ArrayList<>();
        for(Long key: targeturlRecommendedMap.keySet()){
            needInsertEntities.add(targeturlRecommendedMap.get(key));
        }
        log.info("===needInsertEntities size:" + needInsertEntities.size());
        List<List<TargeturlRecommendedEntity>> needInsertEntityLists = CollectionUtils.splitCollectionBySizeWithStream(needInsertEntities, DB_INSERT_ENTITY);
        for (List<TargeturlRecommendedEntity> needInsertEntityList : needInsertEntityLists) {
            targeturlRecommendedEntityDAO.insertBatch(needInsertEntityList);
        }

    }
}
