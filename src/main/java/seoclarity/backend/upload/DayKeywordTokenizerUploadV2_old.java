package seoclarity.backend.upload;

import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import seoclarity.backend.dao.actonia.KeywordStreamSearchengineCountryMappingEntityDAO;
import seoclarity.backend.dao.actonia.MonthlySenderInfoEntityDAO;
import seoclarity.backend.dao.actonia.ResourceSyncInfoEntityDAO;
import seoclarity.backend.dao.clickhouse.kp207.DisManagedSearchvolumeDAO;
import seoclarity.backend.dao.rankcheck.KeywordAdwordsExpandedEntityNewDAO;
import seoclarity.backend.dao.rankcheck.KeywordAdwordsExpandedLogDAO;
import seoclarity.backend.dao.rankcheck.SeoClarityKeywordEntityDAO;
import seoclarity.backend.entity.actonia.ResourceSyncInfoEntity;
import seoclarity.backend.entity.KeywordStreamSearchengineCountryMappingEntity;
import seoclarity.backend.entity.rankcheck.retrievesv.DisManagedSearchvolume;
import seoclarity.backend.entity.rankcheck.retrievesv.KeywordAdwordsExpandedEntityNew;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.utils.keywordTokenizer.SnowBallAndNgramForForeignLanguages;

import java.io.File;
import java.io.FileWriter;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: backend
 * @description:
 * @packagename: seoclarity.backend.upload
 * @author: cil
 * @date: 2021-06-17 15:51
 **/
public class DayKeywordTokenizerUploadV2_old {
    private SeoClarityKeywordEntityDAO seoClarityKeywordEntityDAO;
    // private SeoClarityKeywordEntityDAOBackup seoClarityKeywordEntityDAOBackup;
    private MonthlySenderInfoEntityDAO monthlySenderInfoEntityDAO;

    private KeywordAdwordsExpandedEntityNewDAO keywordAdwordsExpandedEntityNewDAO;
    private KeywordStreamSearchengineCountryMappingEntityDAO keywordStreamSearchengineCountryMappingEntityDAO;
    private DisManagedSearchvolumeDAO disManagedSearchvolumeDAO;
    private ResourceSyncInfoEntityDAO resourceSyncInfoEntityDAO;
    private KeywordAdwordsExpandedLogDAO keywordAdwordsExpandedLogDAO;
    //search order ：EngineId => LanguageId
    private static Map<Integer, Map<Integer, KeywordStreamSearchengineCountryMappingEntity>> countryAndLanguageFullNameMap = new HashMap(128);
    private static List<DisManagedSearchvolume> INSERT_LIST = Collections.synchronizedList(new LinkedList<>());
    private static final String spilt = "!_!";
    private static final SimpleDateFormat YYYYMMDD = new SimpleDateFormat("yyyyMMdd");

    private static int RESOURCE_TYPE = 603;
    private static final int INSERT_SIZE = 2000;
    private static final int SKIP_SIZE = 5000;
    private static final int INSERT_LOG_SIZE = 1000000;
    private static ThreadLocal<Long> threadLocal = new ThreadLocal<>();
    //if no log this is minId
    private static final long minId = 132223000;

    public DayKeywordTokenizerUploadV2_old() {
        seoClarityKeywordEntityDAO = SpringBeanFactory.getBean("seoClarityKeywordEntityDAO");
//		seoClarityKeywordEntityDAOBackup = SpringBeanFactory.getBean("seoClarityKeywordEntityDAOBackup");
        monthlySenderInfoEntityDAO = SpringBeanFactory.getBean("monthlySenderInfoEntityDAO");
        resourceSyncInfoEntityDAO = SpringBeanFactory.getBean("resourceSyncInfoEntityDAO");
        keywordAdwordsExpandedEntityNewDAO = SpringBeanFactory.getBean("keywordAdwordsExpandedEntityNewDAO");
        disManagedSearchvolumeDAO = SpringBeanFactory.getBean("disManagedSearchvolumeDAO");
        keywordStreamSearchengineCountryMappingEntityDAO = SpringBeanFactory.getBean("keywordStreamSearchengineCountryMappingEntityDAO");
        keywordAdwordsExpandedLogDAO = SpringBeanFactory.getBean("keywordAdwordsExpandedLogDAO");
    }

    public static void main(String[] args) {
        //init countryAndLanguageFullNameMap
        DayKeywordTokenizerUploadV2_old ins = new DayKeywordTokenizerUploadV2_old();
        List<KeywordStreamSearchengineCountryMappingEntity> countryAndLanguageFullName = ins.getCountryAndLanguageFullName();
        for (KeywordStreamSearchengineCountryMappingEntity entity :
                countryAndLanguageFullName) {
            if (countryAndLanguageFullNameMap.get(entity.getEngineId()) == null) {
                Map<Integer, KeywordStreamSearchengineCountryMappingEntity> map = new HashMap<>();
                map.put(entity.getLanguageId(), entity);
                countryAndLanguageFullNameMap.put(entity.getEngineId(), map);
            } else {
                countryAndLanguageFullNameMap.get(entity.getEngineId()).put(entity.getLanguageId(), entity);
            }
        }
        System.out.println("===== countryAndLanguageFullNameMap size ==== " + countryAndLanguageFullNameMap.size());
        System.out.println("===== countryAndLanguageFullName size = " + countryAndLanguageFullName.size());
        System.out.println("begin load data ===================================");

        Date date = new Date();
        int logDate = Integer.valueOf(YYYYMMDD.format(date));
//        Long maxId =3912352771L+10000;// 3912707110L; //   ins.getMaxId();
//        Long pocMinId =3912352771L;// ins.getMinId();
        Long maxId = 3912707326L;
        Long pocMinId = 3912707326L-100000;

//        Long pocMinId = 134501059L;
        System.out.println("=======maxId = " + maxId);
        System.out.println("=======pocMinId = " + pocMinId);
        ResourceSyncInfoEntity entity = new ResourceSyncInfoEntity();
        entity.setResourceType(RESOURCE_TYPE);
        entity.setResourceAddDate(logDate);
        entity.setFromLogId(Long.MAX_VALUE);
        entity.setToLogId(0);
        entity.setStatus(2);
        entity.setLogCnt(0);
        entity.setLoadedCnt(0);

        entity.setProcessEndDate(date);
        entity.setCreateDate(date);

        ins.process(ins, 1, maxId, pocMinId, logDate, entity);
        ins.process(ins, 2, maxId, pocMinId, logDate, entity);
    }


    private void insertLog(ResourceSyncInfoEntity entity) {
        writeLog(entity);
    }


    private void insertLog(int logDate, Long fromId, Long toLogId, int logCnt, int loadedCnt) {
        ResourceSyncInfoEntity entity = new ResourceSyncInfoEntity();
        entity.setResourceType(RESOURCE_TYPE);
        entity.setResourceAddDate(logDate);
        entity.setFromLogId(fromId);
        entity.setToLogId(toLogId);
        entity.setStatus(2);
        entity.setLogCnt(logCnt);
        entity.setLoadedCnt(loadedCnt);
        Date date = new Date();
        entity.setProcessEndDate(date);
        entity.setCreateDate(date);

        writeLog(entity);
    }


    private List<KeywordAdwordsExpandedEntityNew> findToday(long minId, long maxId, int keywordType) {
        return keywordAdwordsExpandedEntityNewDAO.findToday(minId, maxId, keywordType);
    }

    private List<KeywordStreamSearchengineCountryMappingEntity> getCountryAndLanguageFullName() {
        return keywordStreamSearchengineCountryMappingEntityDAO.findAllEnabledEngineCountryMapping();
    }


    private void process(DayKeywordTokenizerUploadV2_old ins, int keywordType, Long pocMaxId, Long pocMinId,
                         int logDate, ResourceSyncInfoEntity syncEntity) {
        List<DisManagedSearchvolume> allDatas = Collections.synchronizedList(new LinkedList<>());
        long currentTimeMillis = System.currentTimeMillis();
//        int logDate = Integer.valueOf(YYYYMMDD.format(new Date()));
        int insertCount = 0;
        int totalCount=0;
        threadLocal.set(currentTimeMillis);
        long oldId = pocMinId;
        int logCnt = 0;
        long thisTurnMaxId;
        List<KeywordAdwordsExpandedEntityNew> entityList;

//        pocMaxId = getMaxId(logDate);
//        System.out.println("this turn  ---------   max id = " + pocMaxId + " min id = " + pocMinId);
        if (pocMaxId < 1) {
            System.out.println("today don't have any log ! ========================");
            return;
        }
        do {
            thisTurnMaxId = pocMinId + SKIP_SIZE;
            System.out.println("===== this turn max id = " + thisTurnMaxId);
            entityList = ins.findToday(pocMinId, thisTurnMaxId, keywordType);
            System.out.println(" ===== entityList size = " + entityList.size());
            logCnt += entityList.size();
            // distinct
            Map<String, HashSet<Integer>> keywordAdwordsExpandedMap = new HashMap<>();
            for (KeywordAdwordsExpandedEntityNew entityNew :
                    entityList) {
                //decode
                try {
                    entityNew.setKeyword_name(StringEscapeUtils.unescapeHtml(StringEscapeUtils.escapeHtml(URLDecoder.decode(entityNew.getKeyword_name(), "UTF-8"))));
                } catch (UnsupportedEncodingException e) {
                    e.printStackTrace();
                }
                if (entityNew.getKeyword_name().contains("\t")) {
                    System.out.println("skip key word =====" + entityNew.getKeyword_name() + "  id = " + entityNew.getKeywordId());
                    continue;
                }

                StringBuffer sb = new StringBuffer();
                sb.append(entityNew.getSearchEngineId()).append("-").append(entityNew.getLanguageId())
                        .append("-").append(entityNew.getLocationId());
                String key = sb.toString();
                if (keywordAdwordsExpandedMap.get(key) != null) {
                    keywordAdwordsExpandedMap.get(key).add(entityNew.getKeywordId());
                } else {
                    HashSet<Integer> entityNewList = new HashSet<>();
                    entityNewList.add(entityNew.getKeywordId());
                    keywordAdwordsExpandedMap.put(key, entityNewList);
                }
            }
            // compile with database
            if (keywordAdwordsExpandedMap.size() < 1) {
                System.out.println("===== keywordAdwordsExpandedMap.size() < 1");
                pocMinId = thisTurnMaxId;
                continue;
            }

            List<Integer> distinctId = distinctId(keywordAdwordsExpandedMap);
            //distinct
            distinct(entityList, distinctId);


            if (entityList.size() < 1) {
                System.out.println("===== entityList.size() < 1");
                pocMinId = thisTurnMaxId;
                continue;
            }
            //put to map
            Map<String, List<KeywordAdwordsExpandedEntityNew>> keyWordMap = new HashMap(128);
            for (KeywordAdwordsExpandedEntityNew entityNew :
                    entityList) {
                StringBuffer sb = new StringBuffer();
                sb.append(entityNew.getSearchEngineId()).append(spilt).append(entityNew.getLanguageId())
                        .append(spilt).append(entityNew.getLocationId()).append(spilt).append(entityNew.getKeywordId()).append(spilt).append(entityNew.getKeyword_name());
                String key = sb.toString();
                if (keyWordMap.get(key) != null && keyWordMap.get(key).size() >= 1) {
                    keyWordMap.get(key).add(entityNew);
                } else {
                    List entityNewList = new LinkedList();
                    if (entityNew.getMonth() != null) {
                        entityNewList.add(entityNew);
                    }

                    keyWordMap.put(key, entityNewList);
                }
            }
            //if map is null, continue
            if (keyWordMap.entrySet() == null) {
                System.out.println("===== keyWordMap.entrySet() == null");
                continue;
            }
            //loop map
            for (Map.Entry<String, List<KeywordAdwordsExpandedEntityNew>> entry :
                    keyWordMap.entrySet()) {
                DisManagedSearchvolume entity;

                if (entry.getValue() != null && entry.getValue().size() >= 1) {
                    entity = ins.getDisManagedSearchvolume(entry.getValue());
                } else {
                    entity = ins.getDisManagedSearchvolume(entry.getKey());
                }
                if (entity == null) {
                    System.out.println("===== entity == null");
                    continue;
                }
                if (entity.getMonthlySvAttr_value().length < 1) {
                    System.out.println("===== entity.getMonthlySvAttr_value().length < 1");
                    continue;
                }
//                System.out.println("entity ==== " + entity.getKeyword_name());
//                System.out.println("Engine_id ====== " + entity.getEngine_id());
                KeywordStreamSearchengineCountryMappingEntity keywordStreamSearchengineCountryMappingEntity;
                try {
                    if(countryAndLanguageFullNameMap.get(entity.getEngine_id())==null)
                        continue;
                    keywordStreamSearchengineCountryMappingEntity = countryAndLanguageFullNameMap.get(entity.getEngine_id()).get(entity.getLanguage_id());
                    if (keywordStreamSearchengineCountryMappingEntity == null) {
                        System.out.println("===== keywordStreamSearchengineCountryMappingEntity == null");
                        continue;
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    continue;
                }
                ins.getWord(entity, keywordStreamSearchengineCountryMappingEntity.getLanguageFullName(), keywordStreamSearchengineCountryMappingEntity.getCountryCode());
                synchronized (INSERT_LIST) {
                    INSERT_LIST.add(entity);
                }
            }
//            setMinId1(entityList.get(entityList.size() - 1).getKeywordId());
            System.out.println("============   this turn max id = " + thisTurnMaxId + " this turn list size = " + entityList.size());
            System.out.println("============   insertList size = " + INSERT_LIST.size());
            synchronized (INSERT_LIST) {
                if (INSERT_LIST.size() >= INSERT_SIZE) {
                    totalCount+=INSERT_LIST.size();
                    ins.insert(INSERT_LIST, 0);
                    allDatas.addAll(INSERT_LIST);
                    insertCount += INSERT_LIST.size();
                    System.out.println(" =============== now logCnt = " + logCnt + "            now insertCount = " + insertCount);
                    if (insertCount >= INSERT_LOG_SIZE) {
                        insertLog(logDate, oldId, thisTurnMaxId, logCnt, insertCount);
                        oldId = thisTurnMaxId + 1;
                        logCnt = 0;
                        insertCount = 0;
                    }
                    INSERT_LIST = Collections.synchronizedList(new LinkedList<>());
                }
            }
            System.out.println(" ues time ===== " + ((System.currentTimeMillis() - threadLocal.get()) / 1000 + "s !"));

            pocMinId = thisTurnMaxId;
            System.out.println("===== thisTurnMaxId = " + thisTurnMaxId + "   pocMaxId = " + pocMaxId + " pocMinId = " + pocMinId);
        } while (thisTurnMaxId < pocMaxId);

        synchronized (INSERT_LIST) {
            totalCount+=INSERT_LIST.size();
            ins.insert(INSERT_LIST, 0);
            allDatas.addAll(INSERT_LIST);
            syncEntity.setFromLogId(Long.min(syncEntity.getFromLogId(), oldId));
            syncEntity.setToLogId(Long.max(syncEntity.getToLogId(), pocMaxId));
            syncEntity.setLogCnt(syncEntity.getLogCnt() + logCnt);
            syncEntity.setLoadedCnt(syncEntity.getLoadedCnt() + INSERT_LIST.size());
            INSERT_LIST = Collections.synchronizedList(new LinkedList<>());
            insertLog(syncEntity);
        }
        System.out.println("finish# keywordType:"+keywordType+" totalCount:"+totalCount);
        writeAllDataToFile(keywordType,allDatas);

    }
    private void writeAllDataToFile(int keywordType, List<DisManagedSearchvolume> allDatas )
    {
        String path="/home/<USER>/clarity-backend-scripts/";
        String fullFileName=path+keywordType+"_old.txt";
        File file=new File(fullFileName);
        try {
            if(!file.exists())
                file.createNewFile();
            allDatas.sort(new Comparator<DisManagedSearchvolume>() {
                @Override
                public int compare(DisManagedSearchvolume o1, DisManagedSearchvolume o2) {
                    String key1=String.join("_",o1.getKeyword_rankcheck_id()+"",o1.getEngine_id()+"",
                            o1.getLocation_id()+"",o1.getLanguage_id()+"");
                    String key2=String.join("_",o2.getKeyword_rankcheck_id()+"",o2.getEngine_id()+"",
                            o2.getLocation_id()+"",o2.getLanguage_id()+"");
                    return key1.compareTo(key2);

                }
            });
            StringBuilder sb=new StringBuilder();
            sb.append("Keyword_rankcheck_id\tEngine_id\tLocation_id\tLanguage_id\tAvg_search_volume\r\n");
            allDatas.forEach(e->{
                sb.append(e.getKeyword_rankcheck_id()+"\t").append(e.getEngine_id()+"\t")
                        .append(e.getLocation_id()+"\t").append(e.getLanguage_id()+"\t").append(e.getAvg_search_volume()+"\r\n");
            });
            FileWriter fw = new FileWriter(file, false);
            fw.write(sb.toString());
            fw.close();
        }catch (Exception ex)
        {

        }



    }


    //    @Nullable
//    private DisManagedSearchvolume getDisManagedSearchvolume(DayKeywordTokenizerUploadV2 ins, Map.Entry<String, List<KeywordAdwordsExpandedEntityNew>> entry) {
//        return ins.getDisManagedSearchvolume(entry.getValue());
//    }
    private DisManagedSearchvolume getDisManagedSearchvolume(String str) {
        System.out.println("str ============:    " + str);
        String[] split = str.split(spilt);
        String keywordName = split[4];
        int locationId = Integer.valueOf(split[2]);
        int keywordId = Integer.valueOf(split[3]);
        int searchEngineId = Integer.valueOf(split[0]);
        int languageId = Integer.valueOf(split[1]);
        System.out.println("===== keywordName : " + keywordName);
        DisManagedSearchvolume entity = new DisManagedSearchvolume();
        entity.setKeyword_name(keywordName);
        entity.setHas_sv(0);
        entity.setCpc(0f);
        entity.setAvg_search_volume(0);
        entity.setLocation_id(locationId);
        entity.setKeyword_rankcheck_id(keywordId);
        entity.setSign(1);
        entity.setVersioning(0);
        entity.setEngine_id(searchEngineId);
        entity.setLanguage_id(languageId);

        entity.setMonthly_search_volume1(0);
        entity.setMonthly_search_volume2(0);
        entity.setMonthly_search_volume3(0);
        entity.setMonthly_search_volume4(0);
        entity.setMonthly_search_volume5(0);
        entity.setMonthly_search_volume6(0);
        entity.setMonthly_search_volume7(0);
        entity.setMonthly_search_volume8(0);
        entity.setMonthly_search_volume9(0);
        entity.setMonthly_search_volume10(0);
        entity.setMonthly_search_volume11(0);
        entity.setMonthly_search_volume12(0);


        List<Integer> monthlySvAttr_key = new LinkedList<>();

        monthlySvAttr_key.add(0);
        entity.setMonthlySvAttr_key(monthlySvAttr_key);
        entity.setMonthlySvAttr_value(monthlySvAttr_key);
        entity.setLastRefreshMonth(0);
//        List<Integer> codesInteger = new LinkedList<>();
//        codesInteger.add(-1);
        entity.setCategory(monthlySvAttr_key);

        return entity;

    }

    /**
     * crete DisManagedSearchvolume object
     *
     * @param adwird
     * @return
     */
    private DisManagedSearchvolume getDisManagedSearchvolume(List<KeywordAdwordsExpandedEntityNew> adwird) {
        if (adwird == null || adwird.size() < 1) {
            return null;
        }
        int size = adwird.size();
        Collections.sort(adwird);
        DisManagedSearchvolume entity = new DisManagedSearchvolume();

        KeywordAdwordsExpandedEntityNew lastOne = adwird.get(adwird.size() - 1);
        entity.setHas_sv(1);
        entity.setCpc(lastOne.getCostPerClick() == null ? 0 : lastOne.getCostPerClick());
        entity.setKeyword_name(lastOne.getKeyword_name());
        entity.setAvg_search_volume(lastOne.getAvgMonthlySearchVolume());
        entity.setLocation_id(lastOne.getLocationId());
        entity.setKeyword_rankcheck_id(lastOne.getKeywordId());
        entity.setSign(1);
        entity.setVersioning(0);
        entity.setEngine_id(lastOne.getSearchEngineId());
        entity.setLanguage_id(lastOne.getLanguageId());
        //System.out.println(Thread.currentThread().getName() + " =================   adwird.size() = " + adwird == null ? "adwird is null" : adwird.size());
        try {
            entity.setMonthly_search_volume1(size > 0 ? adwird.get(0).getMonthlySearchVolume() : 0);
        } catch (java.lang.Exception e) {
            entity.setMonthly_search_volume1(0);
        }
        try {
            entity.setMonthly_search_volume2(size > 1 ? adwird.get(1).getMonthlySearchVolume() : 0);
        } catch (java.lang.Exception e) {
            entity.setMonthly_search_volume2(0);
        }
        try {
            entity.setMonthly_search_volume3(size > 2 ? adwird.get(2).getMonthlySearchVolume() : 0);
        } catch (java.lang.Exception e) {
            entity.setMonthly_search_volume3(0);
        }
        try {
            entity.setMonthly_search_volume4(size > 3 ? adwird.get(3).getMonthlySearchVolume() : 0);
        } catch (java.lang.Exception e) {
            entity.setMonthly_search_volume4(0);
        }
        try {
            entity.setMonthly_search_volume5(size > 4 ? adwird.get(4).getMonthlySearchVolume() : 0);
        } catch (java.lang.Exception e) {
            entity.setMonthly_search_volume5(0);
        }
        try {
            entity.setMonthly_search_volume6(size > 5 ? adwird.get(5).getMonthlySearchVolume() : 0);
        } catch (java.lang.Exception e) {
            entity.setMonthly_search_volume6(0);
        }
        try {
            entity.setMonthly_search_volume7(size > 6 ? adwird.get(6).getMonthlySearchVolume() : 0);
        } catch (java.lang.Exception e) {
            entity.setMonthly_search_volume7(0);
        }
        try {
            entity.setMonthly_search_volume8(size > 7 ? adwird.get(7).getMonthlySearchVolume() : 0);
        } catch (java.lang.Exception e) {
            entity.setMonthly_search_volume8(0);
        }
        try {
            entity.setMonthly_search_volume9(size > 8 ? adwird.get(8).getMonthlySearchVolume() : 0);
        } catch (java.lang.Exception e) {
            entity.setMonthly_search_volume9(0);
        }
        try {
            entity.setMonthly_search_volume10(size > 9 ? adwird.get(9).getMonthlySearchVolume() : 0);
        } catch (java.lang.Exception e) {
            entity.setMonthly_search_volume10(0);
        }
        try {
            entity.setMonthly_search_volume11(size > 10 ? adwird.get(10).getMonthlySearchVolume() : 0);
        } catch (java.lang.Exception e) {
            entity.setMonthly_search_volume11(0);
        }
        try {
            entity.setMonthly_search_volume12(size > 11 ? adwird.get(11).getMonthlySearchVolume() : 0);
        } catch (java.lang.Exception e) {
            entity.setMonthly_search_volume12(0);
        }
        List<Integer> monthlySvAttr_key = new LinkedList<>();
        List<Integer> monthlySvAttr_value = new LinkedList<>();
        for (KeywordAdwordsExpandedEntityNew adw :
                adwird) {
            monthlySvAttr_key.add(adw.getMonth() == null ? 0 : adw.getMonth());
            monthlySvAttr_value.add(adw.getMonthlySearchVolume() == null ? 0 : adw.getMonthlySearchVolume());
        }

        entity.setMonthlySvAttr_key(monthlySvAttr_key);
        if (monthlySvAttr_key.size() == 0) {
            System.out.println("monthlySvAttr_key.size = 0 !");
            monthlySvAttr_key.add(0);
            entity.setMonthlySvAttr_key(monthlySvAttr_key);
        }
        entity.setMonthlySvAttr_value(monthlySvAttr_value);
        if (monthlySvAttr_value.size() == 0) {
            System.out.println("monthlySvAttr_value.size = 0 !");
            monthlySvAttr_value.add(0);
            entity.setMonthlySvAttr_key(monthlySvAttr_value);
        }

        entity.setLastRefreshMonth(adwird.get(adwird.size() - 1).getMonth());
        if (lastOne.getCategory() != null) {
            List<String> result = Arrays.asList(lastOne.getCategory().split("#"));
            List<Integer> codesInteger = result.stream().filter(s -> !"".equals(s)).map(Integer::parseInt).collect(Collectors.toList());
            entity.setCategory(codesInteger);
        }

        return entity;

    }

    /**
     * Get repeat id
     *
     * @param keywordAdwordsExpandedMap
     * @return
     */
    private List<Integer> distinctId(Map<String, HashSet<Integer>> keywordAdwordsExpandedMap) {
        if (keywordAdwordsExpandedMap.size() < 1) {
            return null;
        }
        return disManagedSearchvolumeDAO.getDistinctId(keywordAdwordsExpandedMap);

    }

    private long getMaxId() {
        Long maxId = keywordAdwordsExpandedLogDAO.getMaxLogId();
        return maxId == null ? 0L : maxId;
    }

    private void writeLog(ResourceSyncInfoEntity syncEntity) {
        if(1==1)
            return;
        Date date = new Date();
        syncEntity.setCreateDate(date);
        syncEntity.setProcessEndDate(date);
        System.out.println("===== fromLogId  = " + syncEntity.getFromLogId() + " toLogId = " + syncEntity.getToLogId());
        resourceSyncInfoEntityDAO.insert(syncEntity);
    }

    private long getMinId() {
        Long id = resourceSyncInfoEntityDAO.getMaxLogIdByResourceType(RESOURCE_TYPE);
        return id == null || id == 0 ? minId : id + 1;   //need add 1
    }


    private void getWord(DisManagedSearchvolume entity, String languageName, String countryCode) {

        List<String> word = new ArrayList<String>();
        List<String> stream = new ArrayList<String>();

        List<String> keywordVariationOneword = new ArrayList<String>();
        List<String> keywordVariationNgram = new ArrayList<String>();

        if (StringUtils.equalsIgnoreCase(languageName, "Arabic")) {
            word.addAll(SnowBallAndNgramForForeignLanguages.wordTokenizer(entity.getKeyword_name(), "ar"));
        } else {
            word.addAll(SnowBallAndNgramForForeignLanguages.wordTokenizer(entity.getKeyword_name(), countryCode));
        }
        stream.addAll(SnowBallAndNgramForForeignLanguages.wordStemmerTokenizer(entity.getKeyword_name(), languageName));
        keywordVariationOneword.addAll(SnowBallAndNgramForForeignLanguages.wordNgramTokenizer(entity.getKeyword_name(), true));
        keywordVariationNgram.addAll(SnowBallAndNgramForForeignLanguages.wordNgramTokenizer(entity.getKeyword_name(), false));

        entity.setWord(word);
        entity.setStream(stream);

        entity.setKeyword_variation_oneword((keywordVariationOneword));
        entity.setKeyword_variation_ngram(setPlaceHolder(keywordVariationNgram));
    }


    public static List<String> setPlaceHolder(List<String> array) {
        String holder = "_";
        if (array.size() <= 2) {
            return array;
        }
        List<String> result = new ArrayList<String>();

        String startWordStr = array.get(0);
        String endWordStr = array.get(array.size() - 1);

        for (int i = 0; i < array.size(); i++) {
            String words = array.get(i);
            if ((StringUtils.startsWith(startWordStr, words) || StringUtils.startsWith(words, startWordStr)) && (!StringUtils.endsWith(endWordStr, words) && !StringUtils.endsWith(words, endWordStr))) {
                words = words + " " + holder;
                result.add(words);
                continue;
            }
            if (i > 0 && (!StringUtils.startsWith(startWordStr, words) && !StringUtils.startsWith(words, startWordStr)) && (!StringUtils.endsWith(endWordStr, words) && !StringUtils.endsWith(words, endWordStr))) {
                words = holder + " " + words + " " + holder;
                result.add(words);
                continue;
            }
            if (i > 0 && (!StringUtils.startsWith(startWordStr, words) && !StringUtils.startsWith(words, startWordStr)) && (StringUtils.endsWith(endWordStr, words) || StringUtils.endsWith(words, endWordStr))) {
                words = holder + " " + words;
                result.add(words);
                continue;
            }
            result.add(words);
        }
//		System.out.println(array + "->" + result);
        return result;
    }

    private void distinct(List<KeywordAdwordsExpandedEntityNew> entityList, List<Integer> idList) {
        System.out.println("===== old entityList size = " + entityList.size());
        System.out.println("===== database id size = " + idList.size());
        Iterator<KeywordAdwordsExpandedEntityNew> it = entityList.iterator();
        while (it.hasNext()) {
            if (idList.contains(it.next().getKeywordId())) {
                it.remove();
            }
        }
        System.out.println("===== new entityList size = " + entityList.size());
    }

    private int[] insert(List<DisManagedSearchvolume> list, int flag) {
        if(1==1)
            return new int[0];
        System.out.println("===== insert into : " + list.size() + "rows");
        try {
            return disManagedSearchvolumeDAO.insertBatch(list);
        } catch (Exception exception) {
            exception.printStackTrace();
            AllKeywordTokenizerUploadV2.write(list);
            System.out.println(Thread.currentThread().getName() + "===========" + flag + " times try =============== ");
            try {
                Thread.sleep(30000);
                return insert(list, flag + 1);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
        return null;
    }
}




