package seoclarity.backend.upload;

import java.io.File;
import java.net.URLDecoder;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;

import com.amazonaws.regions.Regions;
import com.google.gson.Gson;

import seoclarity.backend.dao.actonia.CommonParamDAO;
import seoclarity.backend.dao.actonia.KeywordEntityDAO;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.rankcheck.SeoClarityKeywordAdwordsEntityDAO;
import seoclarity.backend.dao.rankcheck.SeoClarityKeywordEntityDAO;
import seoclarity.backend.entity.actonia.CommonParamEntity;
import seoclarity.backend.entity.actonia.KeywordEntity;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.rankcheck.SeoClarityKeywordAdwordsEntity;
import seoclarity.backend.entity.rankcheck.SeoClarityKeywordEntity;
import seoclarity.backend.service.CommonDataService;
import seoclarity.backend.service.KeywordNameRelService;
import seoclarity.backend.service.ScKeywordRankManager;
import seoclarity.backend.utils.AmazonS3UploadTool;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.GZipUtil;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.utils.ZeptoMailSenderComponent;
import seoclarity.backend.utils.amazon.AwsCredentialsEnvKeyConstructor;
import seoclarity.backend.utils.loggly.LogglyUtils;
import seoclarity.backend.utils.loggly.LogglyVO;

// https://www.wrike.com/open.htm?id=*********
// https://www.wrike.com/open.htm?id=*********
// mvn exec:java -Dexec.mainClass="seoclarity.backend.upload.ExtractRedVenturesKeywordsForRVOCompany" -Dexec.args=""
public class ExtractRedVenturesKeywordsForRVOCompany {
	private static final SimpleDateFormat SDF_YYYYMMDD = new SimpleDateFormat("yyyyMMdd");
	private static final SimpleDateFormat SDF_YYYY_MM_DD = new SimpleDateFormat("yyyy-MM-dd");
	
	private static final String COMPANY_NAME = "RVO Health, LLC";
	
	private static final String OUTPUT_FOLDER = "/home/<USER>/"; // TODO
	private static final String RV_KEYWORD_OUTPUT_FILENAME_PREFIX = "RVO_UniqueKeywords_"; // TODO
	private static final String RV_KEYWORD_TAG_OUTPUT_FILENAME_PREFIX = "RVO_KeywordTags_"; // TODO
	private static final String RV_DOMAIN_INFO_OUTPUT_FILENAME_PREFIX = "RVO_ManagedDomains_"; // TODO
	private static final String RV_OUTPUT_FILENAME_SUFFFIX = ".txt"; // TODO
	
//	private static final String RV_S3_ACCESS_KEY = "********************";
//	private static final String RV_S3_SECRET_KEY = "wolpnDoS/i/alJgEEANC2/N9QwMWd9Yj1XRBbgsR";
	private static final String RV_S3_BUCKET_KEY_PREFIX = "keyword_tags"; // TODO
	private static final String RV_S3_BUCKET_KEY_PREFIX_FOR_DOMAIN_EXTRACT = "domain_info";
	private static final String KEY_DELIMITER = "/";
	private static final int S3_SESSION_DURATION_SECONDS = 3600;
	private static final int S3_RETRY_COUNT = 10;
	
	private static final int CITY_ID = 0;
	private static final String ENGINE_SPLIT = "!_!";
	private static final String OUTPUT_SPLIT = "\t";
	private  static LogglyVO logglyVO = new LogglyVO();
	private static int totalCnt = 0;
	
	private static final Integer RVO_S3_DOMAIN_ID = 9346;
	// https://www.wrike.com/open.htm?id=478104862

	private OwnDomainEntityDAO ownDomainEntityDAO;
	private KeywordEntityDAO keywordEntityDAO;
	private SeoClarityKeywordEntityDAO seoClarityKeywordEntityDAO;
	private SeoClarityKeywordAdwordsEntityDAO seoClarityKeywordAdwordsEntityDAO;
	private ScKeywordRankManager scKeywordRankManager;
	private KeywordNameRelService keywordNameRelService = new KeywordNameRelService();
	private CommonDataService commonDataService;
	private ZeptoMailSenderComponent zeptoMailSenderComponent;
	private CommonParamDAO commonParamDAO;
	
	public ExtractRedVenturesKeywordsForRVOCompany () {
		ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
		keywordEntityDAO = SpringBeanFactory.getBean("keywordEntityDAO");
		seoClarityKeywordEntityDAO = SpringBeanFactory.getBean("seoClarityKeywordEntityDAO");
		seoClarityKeywordAdwordsEntityDAO = SpringBeanFactory.getBean("seoClarityKeywordAdwordsEntityDAO");
		scKeywordRankManager = SpringBeanFactory.getBean("scKeywordRankManager");
		commonDataService = SpringBeanFactory.getBean("commonDataService");
		zeptoMailSenderComponent = SpringBeanFactory.getBean("zeptoMailSenderComponent");
		commonParamDAO = SpringBeanFactory.getBean("commonParamDAO");
	}
	
	private static final String S3_KEY_PATH_DOMAIN = "domain_info";
	private static final String S3_KEY_PATH_KEYWORD_TAG = "keyword_tags";
	
	public static void main(String[] args) throws Exception {
		ExtractRedVenturesKeywordsForRVOCompany ins = new ExtractRedVenturesKeywordsForRVOCompany();
		String type = null;
		if (args != null && args.length > 0) {
			type = args[0];
		}
		if ("domainInfo".equalsIgnoreCase(type)) {
			ins.exportDomainInfo(S3_KEY_PATH_DOMAIN);
		}else {
			ins.exportDomainInfo(S3_KEY_PATH_DOMAIN);
			ins.exportRedVentureKWTags(S3_KEY_PATH_KEYWORD_TAG);
		}
		// ins.exportUniqueKWs();
	}
	
	// https://www.wrike.com/open.htm?id=*********
	private void exportDomainInfo(String s3Keypath) {
		System.out.println("===========Start to export domain info===========");
		Date now = new Date();
		String currentDateYyyymmdd = SDF_YYYYMMDD.format(now);
//		List<OwnDomainEntity> domainList = ownDomainEntityDAO.getDomainListBasedCompanyName(COMPANY_NAME);
		List<OwnDomainEntity> domainList = ownDomainEntityDAO.getDomainListBasedCompanyName(COMPANY_NAME);
		if(CollectionUtils.isEmpty(domainList)){
			System.out.println("=== company not exist , exit !!" + COMPANY_NAME);
			try {
				sendMailReport("ERROR:Export for inactive Company:" + COMPANY_NAME, "Please disable export for inactive Company:" + COMPANY_NAME + "(" + getClass().getName() + ")");
			} catch (Exception exp) {
				exp.printStackTrace();
			}
			return;
		}
		System.out.println(" ======domainList:" + domainList.size() + ", currentDateYyyymmdd:" + currentDateYyyymmdd);
		List<Integer> idList = new ArrayList<Integer>();
		for (OwnDomainEntity domain : domainList) {
			idList.add(domain.getId());
		}
		
		List<Map<String, Object>> resultList = ownDomainEntityDAO.getDomainCountryInfo(idList);
		
		String outputFileName = OUTPUT_FOLDER + RV_DOMAIN_INFO_OUTPUT_FILENAME_PREFIX + currentDateYyyymmdd + ".txt";
		deleteFileIfExist(outputFileName);
		String mailDescription = "ExtractRedVenturesDomainInfo";
		
		try {
//			OutputStream out = new FileOutputStream(outputFileName);
//			XSSFWorkbook wb = new XSSFWorkbook();
//			List<String[]> rows = new ArrayList<String[]>();
//			
//			rows.add(new String[] {
//					"Domain ID", "Domain Name", "Country provisioned"
//			});
//			for (Map<String, Object> map : resultList) {
//				String id = map.get("id").toString();
//				String domain = map.get("domain").toString();
//				String countryDisplayName = map.get("countryDisplayName").toString();
//				rows.add(new String[] {
//						id, domain, countryDisplayName
//				});
//			}
//			
//			XSSFSheet sheet = wb.createSheet();
//			createXSSFRow(sheet, rows);
//			wb.write(out);
//			out.close();
			
			List<String> rows = new ArrayList<String>();
			rows.add(StringUtils.join(new String[] {
					"Domain ID", "Domain Name", "Country provisioned"
			}, "\t"));
			for (Map<String, Object> map : resultList) {
				String id = map.get("id").toString();
				String domain = map.get("domain").toString();
				String countryDisplayName = map.get("countryDisplayName").toString();
				rows.add(StringUtils.join(new String[] {
						id, domain, countryDisplayName
				}, "\t"));
			}
			FileUtils.writeLines(new File(outputFileName), "UTF-8", rows, false);
			
			System.out.println("=Export file:" + outputFileName);
			saveToClient(currentDateYyyymmdd, outputFileName, s3Keypath);
		} catch (Exception e) {
			e.printStackTrace();
//			EmailSenderComponent.sendEmailReport(new Date(), mailDescription + " Error", mailDescription + " error occurred", e.getMessage());
		}
	}
	
	private void saveToClient(String currentDateYyyymmdd, String outputFileName, String s3Keypath) throws Exception{
		GZipUtil.zip(outputFileName, outputFileName + GZipUtil.GZFile_POSTFIX);
		String fullPathOutputZipFileName = outputFileName + GZipUtil.GZFile_POSTFIX;
		
		boolean savedToS3 = saveFileToS3ByKey(currentDateYyyymmdd, fullPathOutputZipFileName, s3Keypath);
		if (savedToS3) {
			try {
				FileUtils.deleteQuietly(new File(outputFileName));
				FileUtils.deleteQuietly(new File(fullPathOutputZipFileName));
			} catch (Exception e) {
				e.printStackTrace();
			}
		} else {
//			EmailSenderComponent.sendEmailReport(new Date(), mailDescription + " sent file to S3/FTP Error",
//				mailDescription + " send to s3:" + savedToS3 + " send to s3Addtional:" + savedToS3Addtional + ", send to FTP:" + savedToFTP, null);
		}
	}
	
	private void exportRedVentureKWTags(String s3Keypath) throws Exception {
		String mailDescription = "ExtractRedVenturesKeywordTags";
		try {
			List<OwnDomainEntity> domainList = ownDomainEntityDAO.getDomainListBasedCompanyName(COMPANY_NAME);
//			List<OwnDomainEntity> domainList = commonDataService.getRVTargetDomainList();
			System.out.println(" ======RVdomainList:" + domainList.size());
			if(CollectionUtils.isEmpty(domainList)){
				System.out.println("=== company not exist , exit !!" + COMPANY_NAME);
				try {
					sendMailReport("ERROR:Export for inactive Company:" + COMPANY_NAME, "Please disable export for inactive Company:" + COMPANY_NAME + "(" + getClass().getName() + ")");
				} catch (Exception exp) {
					exp.printStackTrace();
				}
				return;
			}

			Date now = new Date();
			String currentDateYyyymmdd = SDF_YYYYMMDD.format(now);
			String outputFileName = OUTPUT_FOLDER + RV_KEYWORD_TAG_OUTPUT_FILENAME_PREFIX + currentDateYyyymmdd + RV_OUTPUT_FILENAME_SUFFFIX;
			deleteFileIfExist(outputFileName);
			int total= 0;
			
			String processDate = SDF_YYYY_MM_DD.format(now);
			writeRedVentureHeaderToFile(outputFileName);
			
			for (OwnDomainEntity ownDomainEntity : domainList) {
				int ownDomainId = ownDomainEntity.getId();

				String pDate = FormatUtils.formatDate(new Date(), FormatUtils.DATE_FORMAT_YYYYMMDD);
				String stTime = FormatUtils.formatDate(new Date(), "yyyyMMdd HH:mm:ss");

				logglyVO.setoId(String.valueOf(ownDomainId));
				logglyVO.setName("ExtractRedVenturesKeywordTags");
				logglyVO.setDevice(ownDomainEntity.isMobileDomain()?"m":"d");

				logglyVO.setpDate(pDate);
				List<String> groupList = new ArrayList<>();
				groupList.add(LogglyVO.GROUP_RANKING_EXTRACT);
				logglyVO.setGroups(groupList);
				List<KeywordEntity> keywordTagList = keywordEntityDAO.geKeywordTagList(ownDomainId);
				System.out.println(" ==ExportOID:" + ownDomainId + " cnt:" + (keywordTagList != null ? keywordTagList.size() : 0));
				if (keywordTagList != null && keywordTagList.size() > 0) {
					List<String> outputList = new ArrayList<String>();
					for (KeywordEntity keywordEntity : keywordTagList) {
						String keywordName = keywordEntity.getKeywordName();
						keywordName = StringEscapeUtils.unescapeHtml(URLDecoder.decode(keywordName, "UTF-8"));
						
						StringBuffer line = new StringBuffer();
						line.append(processDate).append(OUTPUT_SPLIT);
						line.append(ownDomainId).append(OUTPUT_SPLIT);
						line.append(keywordName).append(OUTPUT_SPLIT);
						line.append(keywordEntity.getTagName()).append(OUTPUT_SPLIT);
						line.append(keywordEntity.getRankcheckId()).append(OUTPUT_SPLIT);
						line.append(keywordEntity.getCreateDate() != null ? SDF_YYYY_MM_DD.format(keywordEntity.getCreateDate()) : keywordEntity.getCreateDate());
						outputList.add(line.toString());
						total++;
					}
					FileUtils.writeLines(new File(outputFileName), "UTF-8", outputList, true);
				}
				totalCnt = keywordTagList.size();
				logglyVO.setStatus(LogglyVO.STATUS_OK);
				logglyVO.setsTime(stTime);
				logglyVO.seteTime(FormatUtils.formatDate(new Date(), "yyyyMMdd HH:mm:ss"));
				logglyVO.setRows(String.valueOf(totalCnt));
				String body = new Gson().toJson(logglyVO);
				LogglyUtils.sendLoggly(body, LogglyUtils.EXTRACT_TAG_NAME);
			}
			System.out.println(" ======TotalExportLines:" + totalCnt + " localFile:" + outputFileName + " processDate:" + processDate + 
				" s3Bucket:" + RV_S3_BUCKET_KEY_PREFIX);
			
			System.out.println("=Export file:" + outputFileName);
			saveToClient(currentDateYyyymmdd, outputFileName, s3Keypath);
		} catch (Exception exp) {
			exp.printStackTrace();
			logglyVO.setStatus(LogglyVO.STATUS_NG);
			String body = new Gson().toJson(logglyVO);
			LogglyUtils.sendLoggly(body, LogglyUtils.EXTRACT_TAG_NAME);
//			EmailSenderComponent.sendEmailReport(new Date(), mailDescription + " Error", mailDescription + " error occurred", exp.getMessage());
		}
	}
	
	private void writeRedVentureHeaderToFile(String outputFileName) throws Exception {
		List<String> headerList = new ArrayList<String>();
		StringBuffer line = new StringBuffer();
		line.append("ProcessDate").append(OUTPUT_SPLIT);
		line.append("DomainId").append(OUTPUT_SPLIT);
		line.append("KeywordName").append(OUTPUT_SPLIT);
		line.append("TagName").append(OUTPUT_SPLIT);
		line.append("RankcheckId").append(OUTPUT_SPLIT);
		line.append("CreateDate");
		headerList.add(line.toString());
		FileUtils.writeLines(new File(outputFileName), "UTF-8", headerList, true);
	}
	
	
	public boolean saveFileToS3ByKey(String currentDateYyyymmdd, String fullPathFileName, String s3Keypath) {
		
		
		List<CommonParamEntity> commonEntityList = commonParamDAO.getListByFuncNameAndOwndomainId(RVO_S3_DOMAIN_ID, "setS3");
    	
    	if (CollectionUtils.isEmpty(commonEntityList) || commonEntityList.size() == 0) {
    		System.out.println("Not found S3 setting for OID:" + RVO_S3_DOMAIN_ID);
			return false;
		}

        String paramJson = commonEntityList.get(0).getParamJson();
//            String paramJson = "{\"s3Type\":\"share\",\"accessKey\":\"AKIAVZC7254E6WYK4KHD\",\"secretKey\":\"MX8MKoBS5MkDB7LOiQ6k3ERVKNEwVm92PxXt5GBqmh3dEJnp09e5SwtWux+fYYknoO0uyqXdwUY=\",\"bucket\":\"healthline-seoclarity\",\"path\":\"top_keywords_aio/\"}";

        try {
            Map<String, String> s3Info = new Gson().fromJson(paramJson, Map.class);
            String accessKey = "";
            String secretKey = "";
            String s3Type = s3Info.get("s3Type");
            accessKey = s3Info.get("accessKey");
            if(org.apache.commons.lang.StringUtils.isNotBlank(s3Type) && s3Type.equalsIgnoreCase("share")){
                secretKey = AwsCredentialsEnvKeyConstructor.getInstance().getSQSDecryptedSecretKey();
                System.out.println("===share key:" + accessKey);
            }
            
    		String year = currentDateYyyymmdd.substring(0, 4);
    		String month = currentDateYyyymmdd.substring(4, 6);
    		String day = currentDateYyyymmdd.substring(6);
    		

            String bucketName = s3Info.get("bucket");
            String path = s3Info.get("path");
            if (path.equalsIgnoreCase("/")) {
                path = "";
            }
            
            String fileName = fullPathFileName.substring(fullPathFileName.lastIndexOf(File.separator) + 1);
            
            String s3Key = s3Keypath + KEY_DELIMITER + year + KEY_DELIMITER + month + KEY_DELIMITER + day + KEY_DELIMITER + fileName;


            AmazonS3UploadTool.sendFileToS3ByTransferManager(accessKey, secretKey, Regions.US_EAST_1, bucketName, s3Key, fullPathFileName, 3600, 10);
            System.out.println("S3 eekey: " + path + " , file: " + fullPathFileName);
            return true;
//            ExtractRedVenturesKeywords.saveFileToS3ByKeyByTransferManager(file.getName(), file.getAbsolutePath(), RV_S3_BUCKET, accessKey, secretKey);
	
        } catch (Exception e) {
            throw new RuntimeException("Failed to upload file to SetS3,  , error message:"+e.getMessage());
        }
        
	}
	
	
	
	
	private void createXSSFRow(XSSFSheet sheet, List<String[]> rows) {
		for (int rowIdx = 0; rowIdx < rows.size(); rowIdx++) {
			XSSFRow row = sheet.createRow(rowIdx);
			int cellIdx = 0;
			XSSFCell cell = row.createCell(cellIdx);
			cell.setCellValue(rows.get(rowIdx)[cellIdx]);
			cellIdx++;
			
			XSSFCell cell2 = row.createCell(cellIdx);
			cell2.setCellValue(rows.get(rowIdx)[cellIdx]);
			cellIdx++;
			
			XSSFCell cell3 = row.createCell(cellIdx);
			cell3.setCellValue(rows.get(rowIdx)[cellIdx]);
			cellIdx++;
		}
	}
	
	private void exportUniqueKWs() throws Exception {
		List<OwnDomainEntity> domainList = ownDomainEntityDAO.getDomainListBasedCompanyName(COMPANY_NAME);
//		List<OwnDomainEntity> domainList = commonDataService.getRVTargetDomainList();
		Map<String, List<Integer>> engineDomainMap = new HashMap<String, List<Integer>>();
		for (OwnDomainEntity ownDomainEntity : domainList) {
			String engine = ownDomainEntity.getSearchEngine();
			String language = ownDomainEntity.getLanguage();
			int oid = ownDomainEntity.getId();
			int engineId = scKeywordRankManager.getSearchEngineId(ownDomainEntity);
			int languageId = scKeywordRankManager.getSearchLanguageId(ownDomainEntity);
			System.out.println(" OID:" + oid + " engine:" + engine + " language:" + language + " engineId:" + engineId + " languageId:" + languageId);
			
			String mapKey = engineId + ENGINE_SPLIT + languageId;
			List<Integer> oidList = engineDomainMap.get(mapKey);
			if (oidList == null) {
				oidList = new ArrayList<Integer>();
				engineDomainMap.put(mapKey, oidList);
			}
			oidList.add(oid);
		}
		
		System.out.println(" ======domainCnt:" + domainList.size() + " engineDomainMap:" + engineDomainMap.size());
		String currentDate = SDF_YYYYMMDD.format(new Date());
		
		for(String key : engineDomainMap.keySet()) {
			List<Integer> oidList = engineDomainMap.get(key);
			// TODO
			StringBuffer sb = new StringBuffer();
			for (Integer oid : oidList) {
				sb.append(oid).append(",");
			}
			System.out.println(" ===key:" + key + " oidList:" + sb.toString());
			
			String[] arr = key.split(ENGINE_SPLIT);
			int engineId = Integer.parseInt(arr[0]);
			int languageId = Integer.parseInt(arr[1]);
			String outputFileName = OUTPUT_FOLDER + RV_KEYWORD_OUTPUT_FILENAME_PREFIX + engineId + "_" + languageId + "_" + currentDate;
			exportKWs(engineId, languageId, oidList, currentDate, outputFileName);	
		}
	}
	
	private void exportKWs(int engineId, int languageId, List<Integer> domainIdList, String currentDate, String outputFileName) throws Exception {
		System.out.println("  ===exportKWs engine:" + engineId + " language:" + languageId + " oidCnt:" + domainIdList.size());
		
		// TODO page query
		List<KeywordEntity> keywordList = keywordEntityDAO.geKeywordByDomainList(domainIdList);
		
		if (keywordList != null && keywordList.size() > 0) {
			System.out.println("  ==engine:" + engineId + " language:" + languageId + " KWCnt:" + keywordList.size());
			Set<String> uniqueKWSet = new HashSet<String>();
			for (KeywordEntity keywordEntity : keywordList) {
				KeywordEntity newEntity = getKeywordByKeywordName(keywordEntity.getKeywordName(), keywordEntity.getOwnDomainId());
				String kwName = newEntity.getKeywordName();
				System.out.println("  OID:" + keywordEntity.getOwnDomainId() + " KW:" + keywordEntity.getKeywordName() + "-->" + kwName); // TODO
				uniqueKWSet.add(kwName);
			}
			
			List<String> outputList = new ArrayList<String>();
			Iterator<String> ite = uniqueKWSet.iterator();
			while (ite.hasNext()) {
				String kwName = ite.next();
				SeoClarityKeywordEntity seoClarityKeywordEntity = seoClarityKeywordEntityDAO.getByKeyword(kwName);
				if (seoClarityKeywordEntity != null) {
					int rankcheckId = seoClarityKeywordEntity.getId();
					int avgSv = 0;
					double cpc = 0.0d;
					SeoClarityKeywordAdwordsEntity adwordsEntity = seoClarityKeywordAdwordsEntityDAO.getExistedEntity(rankcheckId, engineId, languageId, CITY_ID);
					if (adwordsEntity != null) {
						avgSv = adwordsEntity.getAvgMonthlySearchVolume() != null ? adwordsEntity.getAvgMonthlySearchVolume().intValue() : 0;
						cpc = adwordsEntity.getCostPerClick() != null ? adwordsEntity.getCostPerClick().doubleValue() : 0.0d;
					}
					StringBuffer line = new StringBuffer();
					line.append(kwName).append(OUTPUT_SPLIT);
					line.append(avgSv).append(OUTPUT_SPLIT);
					line.append(cpc).append(OUTPUT_SPLIT);
					line.append(rankcheckId);
					outputList.add(line.toString());
				} else {
					System.out.println(" Abnormal NoRankcheckKW:" + kwName);
				}
			}
			
			System.out.println("  ==engine:" + engineId + " lang:" + languageId + " uniqueKW:" + uniqueKWSet.size() + " outputList:" + outputList.size() + 
				" outputFileName:" + outputFileName);
			FileUtils.writeLines(new File(outputFileName), "UTF-8", outputList, true);
			
		} else {
			System.out.println(" Abnormal NoKWFor engine:" + engineId + " language:" + languageId + " oidList:" + domainIdList.size());	
		}
	}
	
    private KeywordEntity getKeywordByKeywordName(String keywordName, int ownDomainId) {
        KeywordEntity keywordEntity = keywordEntityDAO.findFirstByKeywordNameAndRanked(keywordName, ownDomainId);
        if (keywordEntity == null) {
            keywordEntity = keywordEntityDAO.findFirstByKeywordName(keywordName, ownDomainId);
        }
        if (keywordEntity == null) {
            keywordEntity = keywordNameRelService.findFirstByKeywordName(keywordName, ownDomainId);
        }
        return keywordEntity;
    }
    
    private void deleteFileIfExist(String localFullPathFileName) {
    	try {
    		File file = new File(localFullPathFileName);
    		if (file.exists()) {
    			System.out.println("==DelLocalFile:" + localFullPathFileName);
    			file.delete();
    		}
    	} catch (Exception exp) {
    		exp.printStackTrace();
    	}
	}

	public void sendMailReport(String subject, String message) {
		Map<String, Object> reportMap = new HashMap<String, Object>();
		reportMap.put("userName", "Jason"); // TODO
		reportMap.put("successMessage", message);
		String emailTo = "<EMAIL>"; // TODO
		String[] ccTo = new String[]{"<EMAIL>"};
		zeptoMailSenderComponent.sendMimeMultiPartZeptoMailAndBccByFunctionType(emailTo, ccTo, subject, "mail_resource_operate_report.txt", "mail_resource_operate_report.html", reportMap, null,
				ZeptoMailSenderComponent.FUNCTION_TYPE_BACKEND_INTERNAL, null, null);
	}
}