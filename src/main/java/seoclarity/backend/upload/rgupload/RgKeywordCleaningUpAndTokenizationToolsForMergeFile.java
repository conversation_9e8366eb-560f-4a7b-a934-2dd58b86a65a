package seoclarity.backend.upload.rgupload;

import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.LineIterator;
import org.apache.commons.lang.StringUtils;
import org.apache.lucene.analysis.Analyzer;
import org.apache.lucene.analysis.TokenStream;
import org.apache.lucene.analysis.classic.ClassicFilter;
import org.apache.lucene.analysis.ja.JapaneseAnalyzer;
import org.apache.lucene.analysis.snowball.SnowballFilter;
import org.apache.lucene.analysis.standard.StandardTokenizer;
import org.apache.lucene.analysis.tokenattributes.CharTermAttribute;
import seoclarity.backend.dao.actonia.EngineCountryLanguageMappingEntityDAO;
import seoclarity.backend.dao.actonia.KeywordStreamSearchengineCountryMappingEntityDAO;
import seoclarity.backend.dao.rankcheck.KeywordMonthlyRecommendDAO;
import seoclarity.backend.dao.rankcheck.SeoClarityKeywordMonthlySearchEngineRelationEntityDAO;
import seoclarity.backend.entity.EngineCountryLanguageMappingEntity;
import seoclarity.backend.entity.rankcheck.KeywordMonthlyRecommend;
import seoclarity.backend.entity.rankcheck.SeoClarityKeywordMonthlySearchEngineRelationEntity;
import seoclarity.backend.keywordexpand.utils.KeywordCleanUpUtils;
import seoclarity.backend.service.CommonDataService;
import seoclarity.backend.utils.CollectionSplitUtils;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.utils.keywordTokenizer.SnowBallAndNgramForForeignLanguages;
import seoclarity.backend.utils.keywordTokenizer.StopKeywordsConstants;

import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <br>
 * 此脚本输入一个文件夹或者多个文件, 按批读取文件中的内容并处理数据, 使用文件内容中的 engineId, languageId
 * 此脚本可以处理大文件
 *  在使用此脚本之前, 必须先将文件排重, 否则将无法处理重复数据 !!
 *  文件内容 split 前两列必须是 engineId, languageId !! 如果是单个不存在engine的文件, args[1] 必须提供engine(eg: 1_1).  如果args[1] 为 `-_-`, 那么文件中就必须提供engine!!.
 * <br/>
 *
 * 20240320
 * nohup mvn exec:java -Dexec.mainClass="seoclarity.backend.upload.rgupload.RgKeywordCleaningUpAndTokenizationToolsForMergeFile" -Dexec.cleanupDaemonThreads=false -Dexec.args="PROD 1_1 /home/<USER>/rg-file/20240411_us FILE /home/<USER>/rg-file/20240411_us/dfs_us_sv_1kabove_good_keywords_list_v2.txt" > log/RgKeywordCleaningUpAndTokenizationTools_us-20240411-02.log 2>&1 &
 */
@CommonsLog
public class RgKeywordCleaningUpAndTokenizationToolsForMergeFile {

    private static final String SPLIT_TAB = "	";
    private static final String SPLIT_UNDERSCORE = "_";
    private static final String SPLIT_DOT = ".";
    private static final String SPLIT_DOT_FOR_SPLIT = "\\.";
    private static final String ENVI_TEST = "TEST";
    private static final String ENVI_PROD = "PROD";

    // params
    private boolean IS_ENVI_TEST;
    private String OUT_FILE_BASE_FOLDER;
    private List<String> IN_FILE_NAME_LIST = new ArrayList<>();
    private Map<String, String> engineStrPLanguageFullNameMap;
    private Set<String> stopKeywordSet;
    private int DEFAULT_ENGINE_ID;
    private int DEFAULT_LANGUAGE_ID;

    // out
    private static final String CLEANUP_OK_KW_FILE = "TR-cleanUp-ok.txt";
    private static final String CLEANUP_SKIP_KW_FILE = "TR-cleanUp-skip.txt";
    private static final String CLEANUP_DUP_KW_FILE = "TR-cleanUp-dup.txt";
    private static final String RG_EXIST_KW_FILE = "TR-rg-exist.txt";
    private static final String RG_OK_KW_FILE = "TR-rg-ok.txt";
    private static final String TOKENIZED_OK_KW_FILE = "TR-tokenize-ok.txt";

    private static final Set<String> missLanguageFullNameEngineSet = new HashSet<>();
    private static final Set<String> missTokenizerLanguageFullNameSet = new HashSet<>();

    private final SeoClarityKeywordMonthlySearchEngineRelationEntityDAO seoClarityKeywordMonthlySearchEngineRelationEntityDAO;
    private final KeywordMonthlyRecommendDAO keywordMonthlyRecommendDAO;
    private final KeywordStreamSearchengineCountryMappingEntityDAO keywordStreamSearchengineCountryMappingEntityDAO;
    private final EngineCountryLanguageMappingEntityDAO mappingEntityDAO;

    public RgKeywordCleaningUpAndTokenizationToolsForMergeFile() {
        seoClarityKeywordMonthlySearchEngineRelationEntityDAO = SpringBeanFactory.getBean("seoClarityKeywordMonthlySearchEngineRelationEntityDAO");
        keywordMonthlyRecommendDAO = SpringBeanFactory.getBean("keywordMonthlyRecommendDAO");
        keywordStreamSearchengineCountryMappingEntityDAO = SpringBeanFactory.getBean("keywordStreamSearchengineCountryMappingEntityDAO");
        mappingEntityDAO = SpringBeanFactory.getBean("engineCountryLanguageMappingEntityDAO");
    }

    public static void main(String[] args) {
        RgKeywordCleaningUpAndTokenizationToolsForMergeFile tool = new RgKeywordCleaningUpAndTokenizationToolsForMergeFile();
        if (args.length < 3) {
            log.error("Invalid parameters, exiting..");
        }
        tool.process(args);
        log.info(" =====missLanguageFullNameEngineSet: " + missLanguageFullNameEngineSet);
        log.info(" =====missTokenizerLanguageFullNameSet: " + missTokenizerLanguageFullNameSet);
    }

    private void process(String[] args) {

        // init
        initGlobalParams(args);

        // loop file
        for (String infileName : IN_FILE_NAME_LIST) {
            log.info("Start process file: " + infileName);
            List<String> items = new ArrayList<>();
            LineIterator it = null;

            int processCnt = 0;
            String currKey = null;
            String engineIdStr = null;
            String languageIdStr = null;
            try {
                it = FileUtils.lineIterator(new File(infileName), "UTF-8");
                while (it.hasNext()) {
                    String line = it.nextLine();
                    if (line.contains("\t")) {
                        String[] split = line.split("\t");
                        engineIdStr = split[0].trim();
                        languageIdStr = split[1].trim();
                    } else {
                        if (DEFAULT_ENGINE_ID == -1 || DEFAULT_LANGUAGE_ID == -1) {
                            throw new RuntimeException("file not contains engine language but params not provide them..");
                        }
                        engineIdStr = String.valueOf(DEFAULT_ENGINE_ID);
                        languageIdStr = String.valueOf(DEFAULT_LANGUAGE_ID);
                    }

                    String nextKey = engineIdStr + "_" + languageIdStr;

                    if (currKey != null && !nextKey.equalsIgnoreCase(currKey)) {
                        log.info("DIFF KEY, Start Process file: " + infileName  + ", nextKey: " + nextKey + ", need processed currKey:" + currKey + ", Process CNT: " + items.size() + ", Total CNT: " + processCnt);
                        String[] split = currKey.split("_");
                        process(items, split[0], split[1]);
                        log.info("DIFF KEY, Process END");
                        items.clear();
                    }

                    items.add(line);
                    currKey = nextKey;
                    processCnt++;

                    if (items.size() == CollectionSplitUtils.BATCH_MEMORY_LIMIT_SIZE) {
                        log.info("OVER LIMIT, Process file: " + infileName  + ", nextKey: " + nextKey + ", need processed currKey:" + currKey + ", Process CNT: " + items.size() + ", Total CNT: " + processCnt);
                        process(items, engineIdStr, languageIdStr);
                        log.info("OVER LIMIT, Process END");
                        items.clear();
                    }
                }
            } catch (IOException e) {
                log.info("Line iterator process failed, please check...");
                throw new RuntimeException(e);
            } finally {
                try {
                    if (it != null) {
                        it.close();
                    }
                } catch (IOException e) {
                    log.info("Close line iterator failed, please check...");
                }
            }

            if (!items.isEmpty()) {
                log.info("Process file: " + infileName  +  ", currKey:" + currKey + ", Process CNT: " + items.size() + ", Total CNT: " + processCnt);
                process(items, engineIdStr, languageIdStr);
                log.info("Process END");
                items.clear();
            }
        }
    }

    private void process(List<String> lines, String engineIdStr, String languageIdStr) {

        Integer engineId = Integer.parseInt(engineIdStr);
        Integer languageId = Integer.parseInt(languageIdStr);

        List<String> cleanUpOkKwList = new ArrayList<>();
        Map<String, Integer> kwPImpressionsMap = new HashMap<>();

        // clean up keyword.
        processCleanUp(engineId, languageId, lines, cleanUpOkKwList, kwPImpressionsMap);

        // check if exists in rg.
        List<String> rgOkKwList = new ArrayList<>();
        processRgRelationAndRecommendExist(engineId, languageId, cleanUpOkKwList, rgOkKwList);

        // tokenize
        processTokens(engineId, languageId, rgOkKwList, kwPImpressionsMap);
        collect();
    }


    private void initGlobalParams(String[] args) {
        if (args[0].equals(ENVI_TEST)) {
            IS_ENVI_TEST = true;
        } else if (args[0].equals(ENVI_PROD)) {
            IS_ENVI_TEST = false;
        } else {
            throw new RuntimeException("Invalid envi parameter: " + args[0] + "!");
        }

        String engineLanguage = args[1];
        String[] split = engineLanguage.split("_");
        if (split[0].equalsIgnoreCase("-") || split[1].equalsIgnoreCase("-")) {
            DEFAULT_ENGINE_ID = -1;
            DEFAULT_LANGUAGE_ID = -1;
        } else {
            DEFAULT_ENGINE_ID = Integer.parseInt(split[0]);
            DEFAULT_LANGUAGE_ID = Integer.parseInt(split[1]);
        }

        OUT_FILE_BASE_FOLDER = args[2];

        String inFileType = args[3];

        if (inFileType.equalsIgnoreCase("FILE")) {
            List<String> fileNames = Arrays.asList(args).subList(4, args.length);
            if (!fileNames.isEmpty()) {
                IN_FILE_NAME_LIST.addAll(fileNames);
            } else {
                throw new RuntimeException("param 4 is not folder.");
            }
        } else if (inFileType.equalsIgnoreCase("FOLDER")){
            String folderName = args[4];
            File folder = new File(folderName);
            if (folder.isDirectory()) {
                File[] files = folder.listFiles();
                assert files != null;
                List<String> filenames = Arrays.stream(files).map(File::getAbsolutePath).collect(Collectors.toList());
                if (!filenames.isEmpty()) {
                    IN_FILE_NAME_LIST.addAll(filenames);
                } else {
                    throw new RuntimeException("param 4 is not folder.");
                }
            } else {
                throw new RuntimeException("param 4 is not folder.");
            }
        }

        log.info("Init params completed, IS_ENVI_TEST: " + IS_ENVI_TEST + ", OUT_FILE_BASE_FOLDER: " + OUT_FILE_BASE_FOLDER + ", inFileType: " + inFileType + ", IN_FILE_NAME_LIST: " + IN_FILE_NAME_LIST);

        // init engineId language map
        // 1-1->English
        initEngineCountryCodeMap();

        //init stop keyword set
        stopKeywordSet = new HashSet<>(Arrays.asList(StopKeywordsConstants.STOP_WORDS_ARR));
    }

    private void initEngineCountryCodeMap() {
        // 语言全名
        /*List<KeywordStreamSearchengineCountryMappingEntity> allEnabledEngineCountryMapping = keywordStreamSearchengineCountryMappingEntityDAO.findAllEnabledEngineCountryMapping();
        engineStrPLanguageFullNameMap = allEnabledEngineCountryMapping.stream()
                .filter(var -> var.getMappingType() == KeywordStreamSearchengineCountryMappingEntity.MAPPING_TYPE_CODE_FROM_ENGINE_ID)
                .collect(Collectors.toMap(var1 -> var1.getEngineId() + SPLIT_UNDERSCORE + var1.getLanguageId(), KeywordStreamSearchengineCountryMappingEntity::getLanguageFullName));*/

        // 语言缩写
        List<EngineCountryLanguageMappingEntity> allEnabledEngineCountryMapping = mappingEntityDAO.getAll();
        engineStrPLanguageFullNameMap = allEnabledEngineCountryMapping.stream()
                .filter(var -> var.getRankFrom() == EngineCountryLanguageMappingEntity.RANK_FROM_ALL)
                .collect(Collectors.toMap(var1 -> var1.getEngineId() + SPLIT_UNDERSCORE + var1.getLanguageId(), EngineCountryLanguageMappingEntity::getLanguageQueryName));
    }

    private void processCleanUp(Integer engineId, Integer languageId, List<String> lines,
                                List<String> cleanUpOkKwList, Map<String, Integer> kwPImpressionsMap) {
        List<String> cleanUpSkipKwList = new ArrayList<>();
        List<String> cleanUpDupKwList = new ArrayList<>();

        int skipWordCntMoreThan5 = 0;
        int skipCharsCntMoreThan60 = 0;
        for (String line : lines) {
            String[] lineSplit = line.trim().split(SPLIT_TAB);
            Integer engineIdFromFile = null;
            Integer languageIdFromFile = null;
            String countryCode = null;
            String languageCode = null;
            String keywordName;
            int impressions = 0;

            //parse from line
            if (lineSplit.length == 5) {
                String engineIdStrInFile = lineSplit[0];
                String languageIdStrInFile = lineSplit[1];
                if (StringUtils.isNumeric(engineIdStrInFile)) {
                    engineIdFromFile = Integer.parseInt(engineIdStrInFile);
                }
                if (StringUtils.isNumeric(languageIdStrInFile)) {
                    languageIdFromFile = Integer.parseInt(languageIdStrInFile);
                }
                countryCode  = lineSplit[2].trim();
                languageCode  = lineSplit[3].trim();
                keywordName = lineSplit[4].trim();
            } else if (lineSplit.length == 4) {
                String engineIdStrInFile = lineSplit[0];
                String languageIdStrInFile = lineSplit[1];
                if (StringUtils.isNumeric(engineIdStrInFile)) {
                    engineIdFromFile = Integer.parseInt(engineIdStrInFile);
                }
                if (StringUtils.isNumeric(languageIdStrInFile)) {
                    languageIdFromFile = Integer.parseInt(languageIdStrInFile);
                }
                keywordName = lineSplit[2].trim();
                String impressionsStr = lineSplit[3].trim().split(SPLIT_DOT_FOR_SPLIT)[0];
                if (StringUtils.isNumeric(impressionsStr)) {
                    impressions = Integer.parseInt(impressionsStr);
                } else {
                    log.error("The parsing of impressions has failed, please check. line: " + line);
                    continue;
                }
            } else if (lineSplit.length == 3) {
                String engineIdStrInFile = lineSplit[0];
                String languageIdStrInFile = lineSplit[1];
                if (StringUtils.isNumeric(engineIdStrInFile)) {
                    engineIdFromFile = Integer.parseInt(engineIdStrInFile);
                }
                if (StringUtils.isNumeric(languageIdStrInFile)) {
                    languageIdFromFile = Integer.parseInt(languageIdStrInFile);
                }
                keywordName = lineSplit[2].trim();

//                keywordName = lineSplit[1].trim();
//                String impressionsWithDotStr = lineSplit[2].trim();
//                String impressionsStr;
//                if (impressionsWithDotStr.contains(SPLIT_DOT)) {
//                    impressionsStr = impressionsWithDotStr.split(SPLIT_DOT_FOR_SPLIT)[0];
//                } else {
//                    impressionsStr = impressionsWithDotStr;
//                }
//                if (StringUtils.isNumeric(impressionsStr)) {
//                    impressions = Integer.parseInt(impressionsStr);
//                } else {
//                    log.error("The parsing of impressions has failed, please check. line: " + line);
//                    continue;
//                }
            } else if (lineSplit.length == 2) {
                keywordName = lineSplit[0].trim();
                String impressionsWithDotStr = lineSplit[1].trim();
                String impressionsStr;
                if (impressionsWithDotStr.contains(SPLIT_DOT)) {
                    impressionsStr = impressionsWithDotStr.split(SPLIT_DOT_FOR_SPLIT)[0];
                } else {
                    impressionsStr = impressionsWithDotStr;
                }
                if (StringUtils.isNumeric(impressionsStr)) {
                    impressions = Integer.parseInt(impressionsStr);
                } else {
                    log.error("The parsing of impressions has failed, please check. line: " + line);
                    continue;
                }
            } else if (lineSplit.length == 1) {
                keywordName = lineSplit[0].trim();
            } else {
                log.error("Parsing row data error, please check. line: " + line);
                throw new RuntimeException();
            }

            // clean up
            if (keywordName.contains("...")) {
                cleanUpSkipKwList.add(keywordName);
                continue;
            }

            if (keywordName.contains("\t")) {
                cleanUpSkipKwList.add(keywordName);
                continue;
            }

            if (keywordName.contains("#NAME")) {
                cleanUpSkipKwList.add(keywordName);
                continue;
            }

            String cleanUpKw = null;
            try {
                cleanUpKw = KeywordCleanUpUtils.cleanUp(keywordName);
            } catch (Exception e) {
                log.info("====== keywordName: " + keywordName + ", cleanUpKw: " + cleanUpKw);
                throw new RuntimeException(e);
            }
            if (cleanUpKw != null) {
                // TODO: 2024/8/28 Skip use  ILLEGAL_CHAR_ARRAY for stand bank
//                if (StringUtils.containsAny(cleanUpKw, KeywordCleanUpUtils.ILLEGAL_CHAR_ARRAY)) {
//                    cleanUpSkipKwList.add(cleanUpKw);
////                    log.info("Skip keyword with bad char, keywordName" + keywordName + ", cleanUpKw: " + cleanUpKw);
//                    continue;
//                }

                if (kwPImpressionsMap.containsKey(cleanUpKw)) {
                    Integer impressionsExisting = kwPImpressionsMap.get(cleanUpKw);
                    if (impressions > impressionsExisting) {
                        kwPImpressionsMap.put(cleanUpKw, impressions);
                    }
//                    log.info("Find dup keyword, keywordName: " + keywordName + ", cleanUpKw: " + cleanUpKw);
                    cleanUpDupKwList.add(keywordName);
                } else {
                    kwPImpressionsMap.put(cleanUpKw, impressions);
                    cleanUpOkKwList.add(cleanUpKw);
                }

            } else {
                cleanUpSkipKwList.add(keywordName);
            }
        }

        log.info("======= little batch cleanUp processing completed, init size: " + lines.size() + ", cleanUpOkKwList size: " + cleanUpOkKwList.size() + ", cleanUpSkipKwList size: " + cleanUpSkipKwList.size() + ", cleanUpDupKwList: " + cleanUpDupKwList.size() + ", skipWordCntMoreThan5: " + skipWordCntMoreThan5 + ", skipCharsCntMoreThan60: " + skipCharsCntMoreThan60);

        try {
            FileUtils.writeLines(new File(OUT_FILE_BASE_FOLDER + "/" + CLEANUP_OK_KW_FILE), "UTF-8", cleanUpOkKwList, true);
            FileUtils.writeLines(new File(OUT_FILE_BASE_FOLDER + "/" + CLEANUP_SKIP_KW_FILE), "UTF-8", cleanUpSkipKwList, true);
            FileUtils.writeLines(new File(OUT_FILE_BASE_FOLDER + "/" + CLEANUP_DUP_KW_FILE), "UTF-8", cleanUpDupKwList, true);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    private void processRgRelationAndRecommendExist(Integer engineId, Integer languageId, List<String> cleanUpOkKwList, List<String> rgOkKwList) {

        Map<String, String> encodeKeywordNamePCleanUpKwMap = new HashMap<>();
        for (String cleanUpOkKw : cleanUpOkKwList) {
            String encodeKw = null;
            try {
                encodeKw = CommonDataService.encodeQueueBaseKeyword(cleanUpOkKw);
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
                log.info("Find special kw when encode, cleanUpOkKw: " + cleanUpOkKw + ", -encodeKw: " + null);
            }
            if (encodeKw != null) {
                encodeKeywordNamePCleanUpKwMap.put(encodeKw, cleanUpOkKw);
            } else {
                log.info("Find special kw when encode, cleanUpOkKw: " + cleanUpOkKw + ", encodeKw: " + null);
            }
        }

        Set<String> encodeKeywordNameSet = encodeKeywordNamePCleanUpKwMap.keySet();
        List<SeoClarityKeywordMonthlySearchEngineRelationEntity> existInSeRel = new ArrayList<>();
        List<KeywordMonthlyRecommend> existInKMR = new ArrayList<>();

        List<List<String>> encodeKeywordNameLists = CollectionSplitUtils.splitCollectionBySizeWithStream(new ArrayList<>(encodeKeywordNameSet), CommonDataService.MYSQL_DB_QUERY_WITH_STRING);
        for (List<String> encodeKeywordNameListChild : encodeKeywordNameLists) {
            List<SeoClarityKeywordMonthlySearchEngineRelationEntity> seoClarityKeywordMonthlySearchEngineRelationEntities = seoClarityKeywordMonthlySearchEngineRelationEntityDAO.checkExist(engineId, languageId, encodeKeywordNameListChild);
            if (!seoClarityKeywordMonthlySearchEngineRelationEntities.isEmpty()) {
                existInSeRel.addAll(seoClarityKeywordMonthlySearchEngineRelationEntities);
            }
            List<KeywordMonthlyRecommend> keywordMonthlyRecommends = keywordMonthlyRecommendDAO.checkExists(engineId, languageId, encodeKeywordNameListChild);
            if (!encodeKeywordNameListChild.isEmpty()) {
                existInKMR.addAll(keywordMonthlyRecommends);
            }
        }

        Set<String> _rgExistKeywordSet = existInSeRel.stream().map(var->var.getKeywordText().toLowerCase()).collect(Collectors.toSet());
        Set<String> _kmrExistKeywordSet = existInKMR.stream().map(var->var.getKeywordText().toLowerCase()).collect(Collectors.toSet());
        encodeKeywordNamePCleanUpKwMap.forEach((encodeKw, originalKw)->{
            if (_rgExistKeywordSet.contains(encodeKw.toLowerCase())) {
//                log.info("keyword exists in rg rel tbl, engineId: " + engineId + ", languageId: " + languageId + ", kw: " + originalKw);
            } else if (_kmrExistKeywordSet.contains(encodeKw.toLowerCase())) {
//                log.info("keyword exists in rg recommend tbl, engineId: " + engineId + ", languageId: " + languageId + ", kw: " + originalKw);
            } else {
                rgOkKwList.add(originalKw);
            }
        });

        Set<String> rgExistsKwSet = new HashSet<>();
        rgExistsKwSet.addAll(_rgExistKeywordSet);
        rgExistsKwSet.addAll(_kmrExistKeywordSet);

        log.info("======= little batch rgCheck processing completed, init(cleanUpOkKwList) size: " + cleanUpOkKwList.size() + ", _rgExistKeywordSet size: " + _rgExistKeywordSet.size() + ", _kmrExistKeywordSet size: " + _kmrExistKeywordSet.size() + ", rgExistsKwSet: " + rgExistsKwSet.size() + ", rgOkKwList size: " + rgOkKwList.size());

        try {
            FileUtils.writeLines(new File(OUT_FILE_BASE_FOLDER + "/" + RG_OK_KW_FILE), "UTF-8", rgOkKwList, true);
            FileUtils.writeLines(new File(OUT_FILE_BASE_FOLDER + "/" + RG_EXIST_KW_FILE), "UTF-8", rgExistsKwSet, true);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    private void processTokens(Integer engineId, Integer languageId, List<String> rgOkKwList, Map<String, Integer> kwPImpressionsMap) {
        // default: English
        String fullLanguageName = "en";
        String engineStr = engineId + SPLIT_UNDERSCORE + languageId;
        if (engineStrPLanguageFullNameMap.containsKey(engineStr)) {
            fullLanguageName = engineStrPLanguageFullNameMap.get(engineStr);
        } else {
            missLanguageFullNameEngineSet.add(engineStr);
            log.info("Miss LanguageFullName from map");
        }

        List<String> tokenizedKwList = new ArrayList<>();
        for (String keywordName : rgOkKwList) {
//            List<String> tokenizedPhrase = wordTokenizerWithFullLanguage(keywordName.toLowerCase(), fullLanguageName); // 还原时态分词
            List<String> tokenizedPhrase = SnowBallAndNgramForForeignLanguages.wordTokenizer(keywordName.toLowerCase(), fullLanguageName); // 直接拆分 支持语言多
            List<String> cleanedTokenizedPhraseList = new ArrayList<>();
            if (!tokenizedPhrase.isEmpty()) {
                for (String word : tokenizedPhrase) {
                    if (word.contains("'")) {
                        word = word.replaceAll("'", "");
                    }
                    if (word.contains("	")) {
                        word = word.replaceAll("	", "");
                    }
                    cleanedTokenizedPhraseList.add(word);
                }
            }

            if (kwPImpressionsMap.containsKey(keywordName)) {
                Integer impressions = kwPImpressionsMap.get(keywordName);
                StringBuilder sbd = new StringBuilder();
                sbd.append(engineId).append(SPLIT_TAB).append(languageId).append(SPLIT_TAB).append(keywordName).append(SPLIT_TAB);
                sbd.append("['").append(StringUtils.join(cleanedTokenizedPhraseList, "','")).append("']").append(SPLIT_TAB);
                sbd.append(impressions);
                tokenizedKwList.add(sbd.toString());
            } else {
                log.error("Unable to retrieve impressions, engine: " + engineId + ", language: " + languageId + ", keywordName: " + keywordName);
                throw new RuntimeException();
            }
        }

        try {
            FileUtils.writeLines(new File(OUT_FILE_BASE_FOLDER + "/" + TOKENIZED_OK_KW_FILE), "UTF-8", tokenizedKwList, true);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public List<String> wordTokenizerWithFullLanguage(String text, String languageName) {
        List<String> results = new ArrayList<String>();
        Analyzer sa;
        try {
            sa = new Analyzer() {
                @Override
                protected TokenStreamComponents createComponents(String fieldName) {
                    StandardTokenizer source = new StandardTokenizer();
                    TokenStream result = new ClassicFilter(source);
                    try {
                        TokenStream sownballfilter = new SnowballFilter(result, languageName);
                        return new TokenStreamComponents(source, sownballfilter);
                    } catch (Exception e) {
                        missTokenizerLanguageFullNameSet.add(languageName);
                        TokenStream sownballfilter = new SnowballFilter(result, "English");
                        return new TokenStreamComponents(source, sownballfilter);
                    }
                }
            };
            // TODO: 2024/9/20 Special Language
            if (languageName.equalsIgnoreCase("Japanese")) {
                sa = new JapaneseAnalyzer();
            }
            TokenStream ts = sa.tokenStream("field", text);
            CharTermAttribute ch = ts.addAttribute(CharTermAttribute.class);
            ts.reset();
            while (ts.incrementToken()) {
                String str = ch.toString();
                results.add(str);
            }
            ts.end();
            ts.close();
            sa.close();
        } catch (Exception ex) {
            ex.printStackTrace();
        }

        return results;
    }

    private void collect() {

    }

    private static boolean isLatin(final String str) {
        return str.equals(new String(str.getBytes(StandardCharsets.ISO_8859_1), StandardCharsets.ISO_8859_1));
    }

    private static boolean isLatin2(final String str) {
        return StandardCharsets.ISO_8859_1.newEncoder().canEncode(str);
    }

    public static void test() {
//        System.out.println(SnowBallAndNgramForForeignLanguages.wordStemmerTokenizer("I've already opened it's", "English"));
//        System.out.println(SnowBallAndNgramForForeignLanguages.wordStemmerTokenizer("I already went home yesterday", "English"));
//        System.out.println(SnowBallAndNgramForForeignLanguages.wordStemmerTokenizer("They will be taking a plane and leaving tomorrow", "English"));
//        System.out.println(SnowBallAndNgramForForeignLanguages.wordStemmerTokenizer("Have you ever bragged", "English"));
//        System.out.println(SnowBallAndNgramForForeignLanguages.wordStemmerTokenizer("flying", "English"));
//
//        System.out.println(SnowBallAndNgramForForeignLanguages.wordTokenizer("flying", "English"));

        String[] split = "1013.0".split(SPLIT_DOT);
        for (String s : split) {
            System.out.println(s);
        }

        System.out.println("1013.0".contains("\\."));
    }
}
