package seoclarity.backend.upload;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.auth.BasicSessionCredentials;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.amazonaws.services.securitytoken.AWSSecurityTokenServiceClient;
import com.amazonaws.services.securitytoken.model.AssumeRoleRequest;
import com.amazonaws.services.securitytoken.model.AssumeRoleResult;
import com.google.gson.Gson;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.clickhouse.youtube.YoutubeSummaryDao;
import seoclarity.backend.entity.KeywordRankVO;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.utils.DingTools;
import seoclarity.backend.utils.EmailSenderComponent;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.utils.amazon.AwsCredentialsEnvKeyConstructor;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * <AUTHOR>
 * @create 2018-09-21 17:53
 * vim ./src/main/java/seoclarity/backend/upload/ExtractVAmazonFromScribeForRv.java
 * mvn exec:java -Dexec.mainClass="seoclarity.backend.upload.ExtractVAmazonFromScribeForRv" -Dexec.cleanupDaemonThreads=false -Dexec.args=""
 **/
public class ExtractVAmazonFromScribeForRv {

    private static final String YOUTUBE_S3_ACCESS_KEY = AwsCredentialsEnvKeyConstructor.getInstance().getPlainTextS3AccessKey();
    private static final String YOUTUBE_S3_SECRET_KEY = AwsCredentialsEnvKeyConstructor.getInstance().getS3DecryptedSecretKey();
    private static final String YOUTUBE_S3_ROLE_ARN ="arn:aws:iam::054715827583:role/opticon_seoclarity_export_s3_role";
    private static final String YOUTUBE_S3_BUCKET_NAME = "opticon-seoclarity-export-bucket-production-us-east-1";
    private static final int S3_SESSION_DURATION_SECONDS = 3600;
    private static final int S3_RETRY_COUNT = 10;
    private static final String SCRIBE_PATH = "/disk1/scribeKeyword/amazon_test_commoncrawl_keywordRank_1_1/";


    public static void main(String[] args){
        System.out.println("start process " + new Gson().toJson(args));
        ExtractVAmazonFromScribeForRv extractYoutubeDate = new ExtractVAmazonFromScribeForRv();
        if (args != null && args.length == 1) {
            String[] dateArr = args[0].split(",");
            for (String dateStr : dateArr) {
                try {
                    extractYoutubeDate.process(DateUtil.parse(dateStr, "yyyy-MM-dd"));
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        } else if (args == null || args.length == 0) {
            try {
                extractYoutubeDate.process(DateUtil.offsetDay(new Date(), -1));
            } catch (Exception e){
                e.printStackTrace();
            }
        }

    }

    public void process(DateTime dateTime) throws IOException {
        String targetPath = "/home/<USER>/rv_amazon/";
        if(FileUtil.exist(targetPath) == false) {
            FileUtil.mkdir(targetPath);
        }

        String dateStr = dateTime.toString("yyyy-MM-dd");
        Map<String, JSONObject> resultMap = new HashMap<>();
        File[] files = FileUtil.ls(SCRIBE_PATH);
        for (File file : files) {
            if(StrUtil.containsIgnoreCase(file.getName(), dateStr) == false) {
                continue;
            }
            System.out.println("Processing : "+file.getName());
            List<String> lines = FileUtil.readLines(file, StandardCharsets.UTF_8);
            for (String line : lines) {
                JSONObject jsonObject = JSONUtil.parseObj(line);
                String keyword = jsonObject.getStr("keyword");
                JSONArray ranks = jsonObject.getJSONArray("keywordRankEntityVOs");
                JSONObject targetObj = resultMap.get(keyword);
                if(targetObj != null && ranks != null && ranks.size() > 0) {
                    JSONArray targetRanks = targetObj.getJSONArray("keywordRankEntityVOs");
                    if(targetRanks == null || targetRanks.isEmpty()) {
                        resultMap.put(keyword, jsonObject);
                    }
                } else if (targetObj == null) {
                    resultMap.put(keyword, jsonObject);
                }
            }
        }

        if(resultMap.size() > 0) {
            //开始去重
            File outputFile = new File(targetPath+"amazon_search_engine_results_"+dateStr+".txt");
            outputFile.delete();
            for (JSONObject jsonObject : resultMap.values()) {
                FileUtil.appendString(jsonObject.toString()+"\n", outputFile, StandardCharsets.UTF_8);
            }
            saveFilesToDestination(outputFile);
        } else {
            DingTools.sendMessage("[RV监控] Amazon extract size is 0");
        }

    }

    private void saveFilesToDestination(File outputFile) {
        try {
            System.out.println(" ==localOutputFilename:" + outputFile.getAbsolutePath());
            String s3key = "amazon_rankings/"+outputFile.getName();
            System.out.println("s3key："+s3key);
            boolean savedToS3 = saveFileToS3ByKey(outputFile.getAbsolutePath(), s3key);
            if(savedToS3 == false) {
                DingTools.sendMessage("[RV监控] Amazon extract file created but send to s3 filed.");
            }
        } catch (Exception e) {
            e.printStackTrace();

        }

    }

    public static boolean saveFileToS3ByKey(String fullPathFileName, String fileName) {
        System.out.println(" ==Save file to s3 Key:" + fileName + " file:" + fullPathFileName);
        return sendFileToS3WithRoleArn(YOUTUBE_S3_ACCESS_KEY, YOUTUBE_S3_SECRET_KEY, Regions.US_EAST_1, YOUTUBE_S3_ROLE_ARN, YOUTUBE_S3_BUCKET_NAME, fileName,
                fullPathFileName, S3_SESSION_DURATION_SECONDS, S3_RETRY_COUNT);
    }

    public static boolean sendFileToS3WithRoleArn(String accessKey, String secretKey, Regions awsRegion, String roleArn, String bucketName, String s3Key,
                                                  String fullPathFileName, int durationSeconds, int retryCount) {
        int triedCount = 0;
        for (int i = 0; i < retryCount; i++) {
            try {
                AWSCredentials credentials = new BasicAWSCredentials(accessKey, secretKey);
                AWSSecurityTokenServiceClient stsClient = new AWSSecurityTokenServiceClient(credentials);
                AssumeRoleRequest assumeRequest = new AssumeRoleRequest()
                        .withRoleArn(roleArn)
                        .withDurationSeconds(durationSeconds)
                        .withRoleSessionName("seoclarity");
                AssumeRoleResult assumeResult = stsClient.assumeRole(assumeRequest);
                BasicSessionCredentials sessionCredentials = new BasicSessionCredentials(
                        assumeResult.getCredentials().getAccessKeyId(),
                        assumeResult.getCredentials().getSecretAccessKey(),
                        assumeResult.getCredentials().getSessionToken());
                AmazonS3 s3client = AmazonS3ClientBuilder.standard().withRegion(awsRegion).withCredentials(new AWSStaticCredentialsProvider(sessionCredentials)).build();

                s3client.putObject(new PutObjectRequest(bucketName, s3Key, new File(fullPathFileName)));
                System.out.println(" ==SavedFileToS3 bucketName:" + bucketName + " key:" + s3Key + " file:" + fullPathFileName);
                return true;
            } catch (Exception e) {
                triedCount++;
                System.out.println(" #SaveFileToS3Failure,retryCnt:" + triedCount);
                try {
                    Thread.sleep(2 * 60 * 1000);
                } catch (Exception exp) {
                    exp.printStackTrace();
                }
                continue;
            }
        }
        return false;
    }


}
