package seoclarity.backend.upload.qBase;

import org.apache.commons.lang.time.DateFormatUtils;
import seoclarity.backend.dao.actonia.KeywordEntityDAO;
import seoclarity.backend.dao.actonia.ResourceBatchDetailEntityDAO;
import seoclarity.backend.dao.actonia.ResourceBatchInfoEntityDAO;
import seoclarity.backend.entity.actonia.ResourceBatchDetailEntity;
import seoclarity.backend.entity.actonia.ResourceBatchInfoEntity;
import seoclarity.backend.utils.Md5Util;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.*;

/**
 * SaveOnEnergy - 9093 | Remove tag from keywords
 * https://www.wrike.com/open.htm?id=971740600
 * -  hao
 * qbase task
 */
public class RemoveTagFromKeyword9093 {

    private KeywordEntityDAO keywordEntityDAO;
    private ResourceBatchInfoEntityDAO resourceBatchInfoEntityDAO;
    private ResourceBatchDetailEntityDAO resourceBatchDetailEntityDAO;

    public RemoveTagFromKeyword9093() {
        keywordEntityDAO = SpringBeanFactory.getBean("keywordEntityDAO");
        resourceBatchInfoEntityDAO = SpringBeanFactory.getBean("resourceBatchInfoEntityDAO");
        resourceBatchDetailEntityDAO = SpringBeanFactory.getBean("resourceBatchDetailEntityDAO");
    }

    private static final String localFile = "remove list.txt";
    private static int domainId = 9093;
    private static int tagId = 1398744;
    private List<String> errKeywordList = new ArrayList<>();

    public static void main(String[] args) {
        RemoveTagFromKeyword9093 in = new RemoveTagFromKeyword9093();
        in.process();
    }

    private void process() {

        List<String> lineList = getLineList();

        List<Integer> keywordIdList = getKeywordIdList(lineList);
        System.out.println( " keywordIdList : " + keywordIdList.size());

        getQBaseTast(keywordIdList);

        if (errKeywordList.size() > 0) {
            System.out.println(" errKeList : " + errKeywordList.size());
            for (String kw : errKeywordList) {
                System.out.println(" errkw : " + kw);
            }
        }

    }


    private List<Integer> getKeywordIdList(List<String> lineList) {
        List<Integer> keywordIds = new ArrayList<>();
        for (String kw : lineList) {
            String kwDecode = "";
            try {
                kwDecode = URLEncoder.encode(kw, "UTF-8").toLowerCase();
                System.out.println(" kw : " + kw + " kwDecode : " + kwDecode);
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
            Integer keywordId = keywordEntityDAO.getKeywordIdByKeywordName(kwDecode, domainId);
            if (null !=keywordId && keywordId > 0) {
                keywordIds.add(keywordId);
            } else {
                errKeywordList.add(kw);
            }
        }
        return keywordIds;
    }

    private List<String> getLineList() {
        List<String> lineList = new ArrayList<>();
        try {
            File file = new File(localFile);
            System.out.println(" ****************** gogogo ********************** ");
            System.out.println(file.getName());
            BufferedReader bf = new BufferedReader(new FileReader(file));
            String line = null;
            while ((line = bf.readLine()) != null) {
                if (!"".equals(line)) {
                    lineList.add(line);
                }
            }
            bf.readLine();
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println(" ************* lineList :************" + lineList.size());
        return lineList;
    }

    private void getQBaseTast(List<Integer> keywordIdList) {

        int actionType = ResourceBatchInfoEntity.TYPE_DELETE;
        int operationType = ResourceBatchInfoEntity.OPERATION_TYPE_BATCH_DISASSOCIATE_KEYWORD_TAG_REL_BY_KEYWORD_ID_AND_TAG_ID;
        Date rbiDate = new Date();

        ResourceBatchInfoEntity resourceBatchInfoEntity = new ResourceBatchInfoEntity();
        resourceBatchInfoEntity.setActionType(actionType);
        resourceBatchInfoEntity.setOwnDomainId(domainId);
        resourceBatchInfoEntity.setOperationType(operationType);
//        resourceBatchInfoEntity.setStatus(ResourceBatchInfoEntity.STATUS_CREATED);
        resourceBatchInfoEntity.setStatus(ResourceBatchInfoEntity.STATUS_FINISH);
        resourceBatchInfoEntity.setUserId(214);
        resourceBatchInfoEntity.setCreateDate(rbiDate);

        long id = resourceBatchInfoEntityDAO.insert(resourceBatchInfoEntity);
        System.out.println("==insert info success, infoId: " + id);

        List<ResourceBatchDetailEntity> resourceBatchDetailEntityList = new ArrayList<>();
        for (Integer kwId : keywordIdList) {
            ResourceBatchDetailEntity rbd = new ResourceBatchDetailEntity();
            rbd.setInfoId(id);
            rbd.setActionType(actionType);
            rbd.setOwnDomainId(domainId);
            rbd.setResourceId(Long.valueOf(kwId));
            rbd.setResourceSubId(Long.valueOf(tagId));
            rbd.setResourceMd5(Md5Util.Md5(Long.valueOf(kwId) + System.nanoTime() + ""));
            rbd.setCreateDate(Integer.parseInt(DateFormatUtils.format(new Date(), "yyyyMMdd")));

            resourceBatchDetailEntityList.add(rbd);
        }
        resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailEntityList);

        resourceBatchInfoEntityDAO.updateStatus(id, ResourceBatchInfoEntity.STATUS_CREATED, ResourceBatchInfoEntity.STATUS_FINISH);
        System.out.println("===insert detail success, size:" + resourceBatchDetailEntityList.size());
    }

}
