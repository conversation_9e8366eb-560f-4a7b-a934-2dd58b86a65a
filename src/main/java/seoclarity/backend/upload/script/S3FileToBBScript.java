package seoclarity.backend.upload.script;

import com.amazonaws.ClientConfiguration;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.S3ResponseMetadata;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.amazonaws.services.s3.model.S3Object;
import com.amazonaws.services.s3.model.S3ObjectInputStream;
import com.amazonaws.services.sqs.model.Message;
import com.amazonaws.util.IOUtils;
import lombok.Data;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.log4j.Logger;
import seoclarity.backend.dao.actonia.BackBlazeKeywordStoreEntityDao;
import seoclarity.backend.dao.actonia.BackBlazeMetaFileEntityDao;
import seoclarity.backend.dao.actonia.BackBlazeMetaFolderEntityDao;
import seoclarity.backend.dao.actonia.BackBlazeSourceFileS3EntityDao;
import seoclarity.backend.entity.BackBlazeKeywordStoreEntity;
import seoclarity.backend.entity.BackBlazeMetaFileEntity;
import seoclarity.backend.entity.BackBlazeMetaFolderEntity;
import seoclarity.backend.entity.BackBlazeSourceFileS3Entity;
import seoclarity.backend.entity.s3tob2.B2FileInfoVo;
import seoclarity.backend.multithread.BackendThreadFactory;
import seoclarity.backend.utils.*;
import seoclarity.backend.utils.cityhash.CityHashUtil;
import seoclarity.backend.utils.murmurhash.MurmurHashUtils;

import java.io.*;
import java.net.URLDecoder;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * https://www.wrike.com/open.htm?id=913196989
 */
public class S3FileToBBScript {


    private static final Logger log = Logger.getLogger(S3FileToBBScript.class);


    /**
     * file path
     */
    //s3 download file and zip file path
    private static String tempFilePath = "/home/<USER>/source/hao/master/clarity-backend-scripts/uploadS3file/";
    private static String localFilelist = "/home/<USER>/source/hao/master/clarity-backend-scripts/dooutfile2/endFilev3.txt";
////    private static String tempFilePath = "i:\\download from s3\\bb\\";
////    private static String localFilelist = "i:\\zzzzz.txt";

//    private static String testdown = "i:\\download from s3\\downbb\\";

    // s3
    private final static String APP_KEY_ID_DO = "GP3XU2L2KJW3SDHJS6YK";
    private final static String APP_KEY_DO = "xRTpHc80yqUaZcIFBwHsSzYyq4ECSEpzT3+L56yMVU8";
    private final static String endpoint_DO = "nyc3.digitaloceanspaces.com";
    private final static String region_DO = "us-east-1";
    //    private static AmazonS3 bb_do;
    private final static String ZIP_CONTENT_TYPE = "application/x-zip-compressed";


    // bb
    private final static String bbBucketName = "serp-html";
    private final static String APP_KEY_ID = "FlIOnZmgKUzDQa2VqdQZNbTw";
    private final static String APP_KEY = "5RloZnhbCoxGrQXTH3yVO2NQhi3l7bozTavvORl";
    private final static String endpoint = "s3.us-west-1.aws.flexify.io";
    private final static String region = "us-east-1";

    // 50个一组 处理
    private static int flg10000 = 50;
    private static int bbZipFileCnt = 50;
    private static int threadCount = 10;
    private static List<String> notfindkw = new ArrayList<>();

    private static ExecutorService threadPool = new ThreadPoolExecutor(threadCount,
            threadCount, 60L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<Runnable>(), new BackendThreadFactory());

    /**
     * upload file to bb status
     */
    // 1: success; 2: error; 3: upload 403 over limit; 4: 2+3;
    private final static int uploadToBBSuccess = 1;
    private final static int uploadToBBError = 2;
    private final static int uploadToBBOverLimit = 3;
    private static final int MYSQL_DB_QUERY_WITH_STRING = 200;
    private static final int MAX_THREAD = 10;


    private EmailSenderComponent emailSenderComponent;
    private BackBlazeKeywordStoreEntityDao backBlazeKeywordStoreEntityDao;
    private BackBlazeMetaFileEntityDao backBlazeMetaFileEntityDao;
    private BackBlazeMetaFolderEntityDao backBlazeMetaFolderEntityDao;
    private BackBlazeSourceFileS3EntityDao backBlazeSourceFileS3EntityDao;

    public S3FileToBBScript() {
        emailSenderComponent = SpringBeanFactory.getBean("emailSenderComponent");
        backBlazeKeywordStoreEntityDao = SpringBeanFactory.getBean("backBlazeKeywordStoreEntityDao");
        backBlazeMetaFileEntityDao = SpringBeanFactory.getBean("backBlazeMetaFileEntityDao");
        backBlazeMetaFolderEntityDao = SpringBeanFactory.getBean("backBlazeMetaFolderEntityDao");
        backBlazeSourceFileS3EntityDao = SpringBeanFactory.getBean("backBlazeSourceFileS3EntityDao");
    }


    public static void main(String[] args) {

        List<String> lineList = new ArrayList<>();
        Map<String, String> linemap = new HashMap<>();
        //设置 一个断点 ，记录下上次中断的位置，下次跑的时候从中断位置开始跑
        boolean memberlastpoint = true;
        String lastpoint = "20180916/17-18/azie+luchthaven+hotels";
        try {
            int cnn = 0;
            File file = new File(localFilelist);
            BufferedReader bf = new BufferedReader(new FileReader(file));
            String line = null;
            while ((line = bf.readLine()) != null) {
                cnn++;
                if (!"".equals(line)) {
                    if (line.contains(lastpoint)) {
                        memberlastpoint = true;
                        System.out.println(" ll， 在第： " + cnn + " 行，从当前位置 开始重新load 数据 ");
                    }
                    if (memberlastpoint) {
                        System.out.println(" line : " + line);
                        String[] name = line.split(",");
                        String n = name[1];
                        linemap.put(n, line);
                    }
                }
            }
            bf.readLine();
        } catch (Exception e) {
            e.printStackTrace();
        }
        for (String str : linemap.keySet()) {
            if (str.equals("20181021/4-7/vols+montpellier+bru")||str.contains("20181021/78-111/chr")) {
                System.out.println(" 找不到key 的 kw  ：" + str + "  === " + linemap.get(str));
            } else {
                lineList.add(linemap.get(str));
            }
        }
        System.out.println(" 去重完成 ： list 大小 ： " + lineList.size());

        // 将文件等分下载
        S3FileToBBScript s3 = new S3FileToBBScript();
        List<List<String>> splitList = splitFile(lineList);
        System.out.println(" 文件等分完成 50个 一组 ：分了 ： " + splitList.size());
        int cnt = 0;
        for (List<String> list : splitList) {
            cnt++;
            System.out.println(" 第" + cnt + " 组 ，读取文件列表  : list0 :" + list.get(0));
//            threadPool.execute(new Runnable() {
//                @Override
//                public void run() {
            long a = System.currentTimeMillis();
            s3.testDownloadFromS3AndBB(list);
            long b = System.currentTimeMillis();
            System.out.println("第 " + cnt + "组线程完成， 耗时 ： :" + (b - a) / 1000 + "s");
//                }
//            });
        }

//        try {
//            threadPool.shutdown();
//        } catch (Exception e) {
//            e.printStackTrace();
//        }

        System.out.println(" notfindkw : " +notfindkw.size());
        if (notfindkw.size()>0){
            for (String str : notfindkw){
                System.out.println(str);
            }
        }

    }

    private static List<List<String>> splitFile(List<String> lineList) {
        List<List<String>> listarr = new ArrayList<>();
        int listsize = lineList.size();
        int pageSize = flg10000;

        for (int i = 0; i < listsize; i += pageSize) {
            int toindex = Math.min(i + pageSize, listsize);
            listarr.add(lineList.subList(i, toindex));
        }
        return listarr;
    }

    private AmazonS3 instantiateDOClient() {
        log.info("$EC=>" + Thread.currentThread().getName() + " Create DO Client.....");
        ClientConfiguration clientConfigurationTemplate = new ClientConfiguration();
        clientConfigurationTemplate.setMaxConnections(50);
        clientConfigurationTemplate.setConnectionMaxIdleMillis(60 * 1000);
        clientConfigurationTemplate.setMaxErrorRetry(5);
        BasicAWSCredentials bbCredentials = new BasicAWSCredentials(APP_KEY_ID_DO, APP_KEY_DO);
        AmazonS3 bb = AmazonS3ClientBuilder.standard()
                .withEndpointConfiguration(new AwsClientBuilder.EndpointConfiguration(endpoint_DO, region_DO))
                .withCredentials(new AWSStaticCredentialsProvider(bbCredentials))
                .withClientConfiguration(clientConfigurationTemplate)
                .build();
        return bb;
    }

    private void testDownloadFromS3AndBB(List<String> lineList) {
        // TODO

//        BasicAWSCredentials bbCredentials = new BasicAWSCredentials(APP_KEY_ID_DO, APP_KEY_DO);
//        bb_do = AmazonS3ClientBuilder.standard().withEndpointConfiguration(new AwsClientBuilder.EndpointConfiguration(endpoint_DO, region_DO))
//                .withCredentials(new AWSStaticCredentialsProvider(bbCredentials)).build();
        AmazonS3 bb_do = instantiateDOClient();//初始化客户端
        Map<String, FileInfo> downLoadFileNamePInfoMap = new LinkedHashMap<>();
        Map<String, String> alternativeNamePRealFileNameMap = new LinkedHashMap<>();
        List<File> downLoadLocalFileList = new ArrayList<>();
//        Map<String, String> key_bucket_map = new HashMap<>();
//        Map<String, String> kwname_bucket_map = new HashMap<>();

        /** 1. download file from s3 */
        System.out.println("  1. download file from s3  ");
        ///** 1. download file from s3 */
        for (String line : lineList) {
            String bucketName = line.split(",")[0];
            String key = line.split(",")[1];
//            String kwName0 = key.substring(key.lastIndexOf("/") + 1);
//            String kwName = "";
////            key_bucket_map.put(key, bucketName);
//            if (kwName0.length() >= 250) {
//                kwName = Md5Util.Md5(kwName0) + ".zip";
//            } else {
//                kwName = kwName0 + ".zip";
//            }
//            kwname_bucket_map.put(kwName, bucketName);
            testDownloadFromB2(bucketName, key, bb_do, downLoadFileNamePInfoMap, downLoadLocalFileList, alternativeNamePRealFileNameMap);

        }

        bb_do.shutdown();
        if (downLoadLocalFileList.size() == 0) {
            //没有文件下载成功
            log.info("$EC=>" + Thread.currentThread().getName() + " downLoad file from s3 all failed! exist!");
            return;
        } else {
            System.out.println(" 文件 下载成功 ： ");
        }

        /** 2. group and compress files */
        System.out.println(" 2. group and compress files ");
        Map<String, List<String>> zipFileNamePLocalFileNameListMap = new HashMap<>();
        LinkedHashMap<String, String> zipFileFullPathPZipFileNameMap = new LinkedHashMap<>();
        List<String> zipFilePathList = new ArrayList<>();

        /***  将 文件 根据 engine date device  每50个 一组 打包成zip  */
//        String flieExp = "i:\\download from s3\\bb\\20171231\\10-11\\4+t%c3%a4hden+hotellit+malaga.zip";
        List<List<File>> downLoadLocalFileListsSpil50 = spiltFile(downLoadLocalFileList);
        System.out.println(" 50个文件打包一组 一共打包了： " + downLoadLocalFileListsSpil50.size() + " 组");
        for (List<File> loadLocalFileList : downLoadLocalFileListsSpil50) {
//            List<String> localFileNameList = loadLocalFileList.stream().map(var -> var.getName()).collect(Collectors.toList());
            List<String> localFileNameList = new ArrayList<>();
            String folderPath = "";
            for (File fi : loadLocalFileList) {
                String filename = fi.getName();
                String spiltFi = fi.getPath();
                if (StringUtils.isBlank(folderPath)) {
                    folderPath = spiltFi.substring(0, spiltFi.lastIndexOf(filename));
                }
                localFileNameList.add(filename);
            }
            Collections.sort(localFileNameList);

            String zipFileName = UUID.randomUUID().toString() + ".zip";
            String zipFileFullPath = folderPath + zipFileName;
            ZipFileUtils.zipFile(zipFileFullPath, loadLocalFileList);
            zipFileNamePLocalFileNameListMap.put(zipFileName, localFileNameList);
            zipFilePathList.add(zipFileFullPath);
            zipFileFullPathPZipFileNameMap.put(zipFileFullPath, zipFileName);
//            loadLocalFileList.stream().forEach(var -> var.delete());
            System.out.println("经过 打包后，本组文件包含关键字个数 ： " + loadLocalFileList.size());
        }


        /** 3. save file to backblaze */
        System.out.println("=============== save file to backblaze   =================");
        //存储成功的文件名(zip)
        Set<String> savedZipFileNameSet = new HashSet<>();
        AmazonS3 b2 = instantiateB2Client();//初始化客户端
        int status = saveFileToBackBlaze(savedZipFileNameSet,
                zipFileFullPathPZipFileNameMap,
                zipFileNamePLocalFileNameListMap, b2);
        b2.shutdown();
        zipFilePathList.forEach(var -> new File(var).delete());
        /**  save  to mysql */// todo  保存到bb 代码完成之后这段删除
//        for (String stt : zipFileFullPathPZipFileNameMap.keySet()) {
//            savedZipFileNameSet.add(zipFileFullPathPZipFileNameMap.get(stt));
//        }

        if (status == uploadToBBError || status == uploadToBBOverLimit) {
            //上传文件发生错误, 中断主类
            System.out.println(" 文件上传bb 失败 ，请检查！ ");
        }

        List<B2FileInfoVo> b2FileInfoVos = new ArrayList<>();
        for (String savedZipFileName : savedZipFileNameSet) {
            B2FileInfoVo b2FileInfoVo = new B2FileInfoVo();

            b2FileInfoVo.setZipFileName(savedZipFileName);
            List<String> localFileNameList = zipFileNamePLocalFileNameListMap.get(savedZipFileName);
            String key = "";
            // 获取 文件全路径
            for (String k : zipFileFullPathPZipFileNameMap.keySet()) {
                if (k.contains(savedZipFileName)) {
                    key = k;
                }
            }
            String[] str_path;
            if (key.contains("/")) {
                str_path = key.split("/");
            } else {
                str_path = key.split("\\\\");
            }
//            String[] str_path = key.split("\\\\");
            String fileFolderPath = "";
            for (int i = 0; i < str_path.length - 1; i++) {
                if (!tempFilePath.contains(str_path[i])) {
                    fileFolderPath = fileFolderPath + str_path[i] + "/";
                }
            }
            String bbFullPathFolder = "";
            System.out.println(" fileFolderPath : " + fileFolderPath);
            for (String localFileName : localFileNameList) {
                B2FileInfoVo.Detail detail = b2FileInfoVo.new Detail();
                FileInfo fileInfo = downLoadFileNamePInfoMap.get(localFileName);
                String alternativeName = null;
                Integer renamed = 0;
                if (fileInfo.getIsAlterNative()) {
                    alternativeName = localFileName;
                    localFileName = alternativeNamePRealFileNameMap.get(localFileName);
                    renamed = 1;
                }
                bbFullPathFolder = bbBucketName + "/" + fileFolderPath;
                String s3FullPathFolder = "";
                System.out.println(" localFileName : " + localFileName);
                // example://daily-html-virginia/20200712/1-1/',
//                for (String folder : kwname_bucket_map.keySet()) {
//                    if (folder.contains(localFileName)) {
//                        s3FullPathFolder = kwname_bucket_map.get(localFileName);
//                        break;
//                    }
//                }
                /* s3 file info */
                detail.setFullPathFolder(fileFolderPath);
                detail.setSourceFileName(localFileName);
                detail.setProcessStatus(BackBlazeSourceFileS3Entity.DOWNLOAD_SUCCESS);
                detail.setSourceFileSize(fileInfo.getLength());
                /* new b2 file info */
                detail.setRankDate(fileInfo.getInfoData().getDate());
                detail.setKeywordType(fileInfo.getInfoData().getKeywordType());
                detail.setEngineId(fileInfo.getInfoData().getEngineId());
                detail.setLanguageId(fileInfo.getInfoData().getLanguageId());
                detail.setDevice(fileInfo.getInfoData().getDevice());
                detail.setCityQueryName(fileInfo.getInfoData().getQueryName());
                String keywordName = fileInfo.getInfoData().getKeywordName();
                String rawKeywordName = fileInfo.getInfoData().getRawKeywordName();
                detail.setKeywordName(keywordName.toLowerCase());
                detail.setRawKeywordName(rawKeywordName.toLowerCase());
                detail.setCdbKeywordHash(CityHashUtil.getUrlHashForString(rawKeywordName.toLowerCase()));
                detail.setCdbKeywordMurmur3hash(MurmurHashUtils.getMurmurHash3_64(rawKeywordName.toLowerCase()));
                detail.setCdbEnocdeKeywordHash(CityHashUtil.getUrlHashForString(keywordName.toLowerCase()));
                detail.setFileName(keywordName);
                detail.setAlternativeName(alternativeName);
                detail.setRenamed(renamed);
                b2FileInfoVo.getDetails().add(detail);
            }
            b2FileInfoVo.setFullPathFolder(bbFullPathFolder);
            b2FileInfoVos.add(b2FileInfoVo);
        }


        // save  mysql
        System.out.println("  // save  mysql " + b2FileInfoVos.size());
        for (B2FileInfoVo b2FileInfoVo : b2FileInfoVos) {
            System.out.println(" save to mysql : file ： " + b2FileInfoVo.getFullPathFolder() + b2FileInfoVo.getZipFileName());
            BackBlazeMetaFolderEntity backBlazeMetaFolderEntity = saveBackBlazeMetaFolder(b2FileInfoVo.getFullPathFolder());
            Long backBlazeMetaId = saveBackBlazeMetaFile(b2FileInfoVo.getZipFileName(), backBlazeMetaFolderEntity);
            Map<String, Long> savedBackBlazeSourceFileNamePId = saveBackBlazeSourceFileS3(b2FileInfoVo.getDetails());
            saveBackBlazeKeywordStore(backBlazeMetaId, savedBackBlazeSourceFileNamePId, b2FileInfoVo.getDetails());
        }
    }

    private void saveBackBlazeKeywordStore(Long
                                                   backBlazeMetaId, Map<String, Long> savedBackBlazeSourceFileNamePId, List<B2FileInfoVo.Detail> details) {
        //构建需要插入`backblaze_keyword_store`的列表
        List<BackBlazeKeywordStoreEntity> needInsertBackBlazeKeywordStoreList = new ArrayList<>();

        for (B2FileInfoVo.Detail detail : details) {
            BackBlazeKeywordStoreEntity backBlazeKeywordStoreEntity = new BackBlazeKeywordStoreEntity();
            backBlazeKeywordStoreEntity.setRankDate(detail.getRankDate());
            backBlazeKeywordStoreEntity.setKeywordType(detail.getKeywordType());
            backBlazeKeywordStoreEntity.setEngineId(detail.getEngineId());
            backBlazeKeywordStoreEntity.setLanguageId(detail.getLanguageId());
            backBlazeKeywordStoreEntity.setDevice(detail.getDevice());
            backBlazeKeywordStoreEntity.setCityQueryName(detail.getCityQueryName());
            String keywordName = detail.getKeywordName();
            String rawKeywordName = detail.getRawKeywordName();
            backBlazeKeywordStoreEntity.setKeywordName(keywordName.toLowerCase());
            backBlazeKeywordStoreEntity.setRawKeywordName(rawKeywordName.toLowerCase());
            backBlazeKeywordStoreEntity.setCdbKeywordHash(detail.getCdbKeywordHash());
            backBlazeKeywordStoreEntity.setCdbKeywordMurmur3hash(detail.getCdbKeywordMurmur3hash());
            backBlazeKeywordStoreEntity.setCdbEnocdeKeywordHash(detail.getCdbEnocdeKeywordHash());
            backBlazeKeywordStoreEntity.setMetaFileId(backBlazeMetaId);
            backBlazeKeywordStoreEntity.setFileName(keywordName);
            backBlazeKeywordStoreEntity.setAlternativeName(detail.getAlternativeName());
            backBlazeKeywordStoreEntity.setRenamed(detail.getRenamed());
            backBlazeKeywordStoreEntity.setSourceFileId(savedBackBlazeSourceFileNamePId.get(detail.getSourceFileName()));
            needInsertBackBlazeKeywordStoreList.add(backBlazeKeywordStoreEntity);
        }

        backBlazeKeywordStoreEntityDao.insertBatchIgnoreDup(needInsertBackBlazeKeywordStoreList);

        // TODO: 2022/1/12  logout
//        System.out.println("sout => needInsertBackBlazeKeywordStoreList: " + needInsertBackBlazeKeywordStoreList);
    }

    private BackBlazeMetaFolderEntity saveBackBlazeMetaFolder(String bbFullPathFolder) {
        // //插入表`backblaze_meta_folder`并取回数据
        // folder表不直接指向数据, backblaze_meta_folder表中的数据存在冗余不是问题
        BackBlazeMetaFolderEntity dbBackBlazeMetaFolderEntity = backBlazeMetaFolderEntityDao.findByUniqueKey(bbFullPathFolder);
        if (dbBackBlazeMetaFolderEntity == null) {
            BackBlazeMetaFolderEntity backBlazeMetaFolderEntity = new BackBlazeMetaFolderEntity();
            backBlazeMetaFolderEntity.setFullPathFolder(bbFullPathFolder);
            List<BackBlazeMetaFolderEntity> list = new ArrayList<>();
            list.add(backBlazeMetaFolderEntity);
            backBlazeMetaFolderEntityDao.insertBatchIgnoreDup(list);
            dbBackBlazeMetaFolderEntity = backBlazeMetaFolderEntityDao.findByUniqueKey(bbFullPathFolder);
        }

        // TODO: 2022/1/12  logout
        return dbBackBlazeMetaFolderEntity;
    }

    private Long saveBackBlazeMetaFile(String zipFileName, BackBlazeMetaFolderEntity dbBackBlazeMetaFolderEntity) {
        //构建需要插入`backblaze_meta_file`的列表
        BackBlazeMetaFileEntity backBlazeMetaFileEntity = new BackBlazeMetaFileEntity();
        backBlazeMetaFileEntity.setMetaFolderId(dbBackBlazeMetaFolderEntity.getId());
        backBlazeMetaFileEntity.setZipFileName(zipFileName);
        // TODO: 2022/1/12  logout
        //插入表`backblaze_meta_file`并取回数据
        List<BackBlazeMetaFileEntity> entityList = new ArrayList<>();
        entityList.add(backBlazeMetaFileEntity);
        backBlazeMetaFileEntityDao.insertBatchIgnoreDup(entityList);
        BackBlazeMetaFileEntity existedEntity = backBlazeMetaFileEntityDao.findListByUniqueKey(dbBackBlazeMetaFolderEntity.getId(), zipFileName);
        return existedEntity.getId();
    }

    private Map<String, Long> saveBackBlazeSourceFileS3(List<B2FileInfoVo.Detail> details) {

        if (details == null || details.size() == 0) {
            return null;
        }

        //构建需要插入`backblaze_source_file_s3`的列表
        List<BackBlazeSourceFileS3Entity> needInsertBackBlazeSourceFileS3List = new ArrayList<>();

        String s3FullPathFolder = null;
        for (B2FileInfoVo.Detail detail : details) {
            BackBlazeSourceFileS3Entity backBlazeSourceFileS3Entity = new BackBlazeSourceFileS3Entity();
            backBlazeSourceFileS3Entity.setFullPathFolder(detail.getFullPathFolder());
            backBlazeSourceFileS3Entity.setSourceFileName(detail.getSourceFileName());
            backBlazeSourceFileS3Entity.setProcessStatus(detail.getProcessStatus());
            backBlazeSourceFileS3Entity.setSourceFileSize(detail.getSourceFileSize());
            needInsertBackBlazeSourceFileS3List.add(backBlazeSourceFileS3Entity);
            s3FullPathFolder = detail.getFullPathFolder();
        }

//        System.out.println("sout => needInsertBackBlazeSourceFileS3List: " + needInsertBackBlazeSourceFileS3List);

        //插入表`backblaze_source_file_s3`数据并取回
        backBlazeSourceFileS3EntityDao.insertBatchIgnoreDup(needInsertBackBlazeSourceFileS3List);
        List<String> savedS3SourceFileNameList = needInsertBackBlazeSourceFileS3List.stream().map(var -> var.getSourceFileName()).collect(Collectors.toList());

        List<BackBlazeSourceFileS3Entity> savedBackBlazeSourceFileS3List = new ArrayList<>();
        List<List<String>> savedS3SourceFileNameLists = CollectionUtils.splitCollectionBySize(savedS3SourceFileNameList, MYSQL_DB_QUERY_WITH_STRING);

//        System.out.println("sout => savedS3SourceFileNameList: " + savedS3SourceFileNameList);

        for (List<String> s3SourceFileNameList : savedS3SourceFileNameLists) {
            List<BackBlazeSourceFileS3Entity> result = backBlazeSourceFileS3EntityDao.findListByUniqueKey(s3FullPathFolder, s3SourceFileNameList);
            if (result != null && result.size() > 0) {
                savedBackBlazeSourceFileS3List.addAll(result);
            }
        }

        List<String> collect = savedBackBlazeSourceFileS3List.stream().map(var -> var.getSourceFileName()).collect(Collectors.toList());
        log.info(" s3: " + collect);

        Map<String, Long> savedBackBlazeSourceFileNamePId = savedBackBlazeSourceFileS3List.stream().collect(Collectors.toMap(var1 -> var1.getSourceFileName(), var2 -> var2.getId()));
        return savedBackBlazeSourceFileNamePId;
    }


    private List<List<File>> spiltFile(List<File> downLoadLocalFileList) {
        List<List<File>> downLoadLocalFileListsSpil50 = new ArrayList<>();
        Map<String, List<File>> mp = new HashMap<>();
        for (File fi : downLoadLocalFileList) {
            String path = fi.getPath();
            String folder = "";
            if (path.contains("/")) {
                folder = path.substring(0, path.lastIndexOf("/") + 1);
            } else {
                folder = path.substring(0, path.lastIndexOf("\\") + 1);
            }
//            String folder = path.substring(0, path.lastIndexOf("\\") + 1);
            String name = fi.getName();
//            System.out.println(" path : " + path + " folder :" + folder + " name :" + name);
            if (mp.get(folder) != null) {
                mp.get(folder).add(fi);
            } else {
                List<File> li = new ArrayList<>();
                li.add(fi);
                mp.put(folder, li);
            }
        }
        for (String str : mp.keySet()) {
            if (mp.get(str).size() > bbZipFileCnt) {
                List<List<File>> spilFile = CollectionUtils.splitCollectionBySize(mp.get(str), bbZipFileCnt);
                downLoadLocalFileListsSpil50.addAll(spilFile);
            } else {
                downLoadLocalFileListsSpil50.add(mp.get(str));
            }
        }
        return downLoadLocalFileListsSpil50;
    }


    private void testDownloadFromB2(String bucketName, String key, AmazonS3 bb_do,
                                    Map<String, FileInfo> downLoadFileNamePInfoMap,
                                    List<File> downLoadLocalFileList,
                                    Map<String, String> alternativeNamePRealFileNameMap) {

        // S3Object o = bb.getObject(bbBucketName, "20160403/1-1/76b8a941-195f-4c78-8e57-d51503e6d409.zip");
        FileInfo fileInfo = null;
        try {
            fileInfo = getFileInfo(key);
        } catch (Exception e) {
// 有可能会遇到特别的文件夹名称, 造成解析文件名失败
            log.info("$EC=>" + Thread.currentThread().getName() + " parse filePath failed! key: " + key + ", msg: " + CollectionUtils.getErrorMsg(e));
        }

        S3Object o = null;
//        int reTrycnt = 0;
        int reTrycnt2 = 0;
        try {
            o = bb_do.getObject(bucketName, key);
        } catch (Exception e) {
            System.out.println("  找不到 这个key : bucketname :" + bucketName + "key :   " + key);
            return;
            // 找不到 文件 re- try
//            try {
//                reTrycnt++;
//                Thread.sleep(10 * 1000);
//                System.out.println(" 找不到文件 10秒后 重试 ");
//                if (reTrycnt < 3) {
//                    o = bb_do.getObject(bucketName, key);
//                } else {
//                    String keyReplace = key.replaceAll("%", "%25");
//                    System.out.println(" 尝试 3次 还是拿不到 ，将 % 替换成 25% 重试 ");
//                    o = bb_do.getObject(bucketName, keyReplace);
//                }
//            } catch (InterruptedException interruptedException) {
//                System.out.println(" 最终还是找不到这个文件 整理存到新的文件");
//                notfindkw.add(bucketName + "/" + key);
////                interruptedException.printStackTrace();
//            }
        }

        String localFileFullName;
        if (fileInfo.getIsAlterNative() != null && fileInfo.getIsAlterNative() == true) {
            String alternativeName = Md5Util.Md5(fileInfo.getInfoData().getKeywordName());
            localFileFullName = fileInfo.getFolder() + alternativeName;
            alternativeNamePRealFileNameMap.put(alternativeName, fileInfo.getInfoData().getKeywordName());
        } else {
            localFileFullName = key;
        }
        File localFile;
        try {
            S3ObjectInputStream s3is = o.getObjectContent();
//                 File localFile = new File("i:\\download from s3\\aaa\\10-11\\aaa.zip");
            localFile = new File(tempFilePath + localFileFullName + ".zip");
            System.out.println(" localFile : " + localFile);
            File fileParent = localFile.getParentFile();
            if (!fileParent.exists()) {
                localFile.getParentFile().mkdirs();
            }
            FileOutputStream fos = new FileOutputStream(localFile);
            byte[] read_buf = new byte[1024];
            int read_len = 0;
            while ((read_len = s3is.read(read_buf)) > 0) {
                fos.write(read_buf, 0, read_len);
            }
            s3is.close();
            fos.close();
            fileInfo.setLength(localFile.length());
            downLoadLocalFileList.add(localFile);
            downLoadFileNamePInfoMap.put(localFile.getName(), fileInfo);
            System.out.println(localFile.length());
        } catch (IOException e) {
//            e.printStackTrace();
            log.error("$EC=>" + Thread.currentThread().getName() + " download s3 file exception, : ");
        }

    }


    public FileInfo getFileInfo(String key) throws Exception {

        int i = StringUtils.indexOf(key, '/');
        Integer date = Integer.parseInt(key.substring(0, i));
        String key_remove_dateStr = key.substring(i + 1);

        String keywordName;
        String engineIdStr;
        String languageIdStr;
        String queryName;

        String[] key_remove_dateStrArr = key_remove_dateStr.split("/");
        keywordName = key_remove_dateStrArr[key_remove_dateStrArr.length - 1];
        key_remove_dateStr = key_remove_dateStr.replace("/" + keywordName, "");

        String device = key_remove_dateStr.contains("mobile") ? "m" : "d";
        Integer keywordType = Integer.parseInt(key_remove_dateStr.contains("city") ? "2" : "1");

        String[] engineSpArr = key_remove_dateStrArr[0].split("-");
        boolean notNumFlag = false;
        for (String str : engineSpArr) {
            if (!NumberUtils.isNumber(str)) {
                notNumFlag = true;
            }
        }

        /**
         *     public static int TYPE_FPR_NATIONAL = 3;
         *     public static int TYPE_FPR_GEO = 4;
         *     public static int TYPE_FPR_VED_NATIONAL = 5;
         *     public static int TYPE_FPR_VED_GEO = 6;
         *     public static int TYPE_JOB_NATIONAL = 7;
         *     public static int TYPE_JOB_GEO = 8;
         *     public static int TYPE_LL_NATIONAL = 9;
         *     public static int TYPE_LL_GEO = 10;
         *     public static int TYPE_LL_V2_NATIONAL = 11;
         *     public static int TYPE_LLV2__GEO = 12;
         */
        if (engineSpArr.length == 2 && !notNumFlag) {
            engineIdStr = engineSpArr[0];
            languageIdStr = engineSpArr[1];
            queryName = "0";
        } else if (key_remove_dateStr.startsWith("mobile-") && !key_remove_dateStr.contains("city")) {
            // "20200802/mobile-98-113/2.zip"
            String engine = StringUtils.remove(key_remove_dateStr, "mobile-");
            String[] engineArr = engine.split("-");
            engineIdStr = engineArr[0];
            languageIdStr = engineArr[1];
            queryName = "0";
        } else if (key_remove_dateStr.startsWith("city-")) {
            // "20200802/city-25-9-01028/3.zip" => city-25-9-01028/3.zip
            String engine = StringUtils.remove(key_remove_dateStr, "city-");
            int i1 = engine.indexOf('-');
            engineIdStr = engine.substring(0, i1);
            engine = engine.substring(i1 + 1);
            int i2 = engine.indexOf('-');
            languageIdStr = engine.substring(0, i2);
            queryName = engine.substring(i2 + 1);
        } else if (key_remove_dateStr.startsWith("mobile-city-")) {
            // "20200802/mobile-city-13-16-01850/4.zip"
            String engine = StringUtils.remove(key_remove_dateStr, "mobile-city-");
            int i1 = engine.indexOf('-');
            engineIdStr = engine.substring(0, i1);
            engine = engine.substring(i1 + 1);
            int i2 = engine.indexOf('-');
            languageIdStr = engine.substring(0, i2);
            queryName = engine.substring(i2 + 1);
        } else if (key_remove_dateStr.startsWith("fpr-")) {
            if (key_remove_dateStr.startsWith("fpr-mobile-") && !key_remove_dateStr.contains("city")) {
                // "20200802/fpr-mobile-1-1/6.zip"
                String engine = StringUtils.remove(key_remove_dateStr, "fpr-mobile-");
                String[] engineArr = engine.split("-");
                engineIdStr = engineArr[0];
                languageIdStr = engineArr[1];
                queryName = "0";
                keywordType = BackBlazeKeywordStoreEntity.TYPE_FPR_NATIONAL;
            } else if (key_remove_dateStr.startsWith("fpr-mobile-city-")) {
                // "20200802/fpr-mobile-city-13-16-01850/62.zip"
                String engine = StringUtils.remove(key_remove_dateStr, "fpr-mobile-city-");
                int i1 = engine.indexOf('-');
                engineIdStr = engine.substring(0, i1);
                engine = engine.substring(i1 + 1);
                int i2 = engine.indexOf('-');
                languageIdStr = engine.substring(0, i2);
                queryName = engine.substring(i2 + 1);
                keywordType = BackBlazeKeywordStoreEntity.TYPE_FPR_GEO;
            } else if (key_remove_dateStr.startsWith("fpr-city-")) {
                // "20200802/fpr-city-25-9-01028/61.zip"
                String engine = StringUtils.remove(key_remove_dateStr, "fpr-city-");
                int i1 = engine.indexOf('-');
                engineIdStr = engine.substring(0, i1);
                engine = engine.substring(i1 + 1);
                int i2 = engine.indexOf('-');
                languageIdStr = engine.substring(0, i2);
                queryName = engine.substring(i2 + 1);
                keywordType = BackBlazeKeywordStoreEntity.TYPE_FPR_GEO;
            } else if (key_remove_dateStr.startsWith("fpr-ved-mobile-")) {
                // "20200802/fpr-ved-mobile-5-7/66+city.zip"
                String engine = StringUtils.remove(key_remove_dateStr, "fpr-ved-mobile-");
                String[] engineArr = engine.split("-");
                engineIdStr = engineArr[0];
                languageIdStr = engineArr[1];
                queryName = "0";
                keywordType = BackBlazeKeywordStoreEntity.TYPE_FPR_VED_NATIONAL;
            } else if (key_remove_dateStr.startsWith("fpr-ved-")) {
                // "20200802/fpr-ved-5-7/65+city.zip"
                String engine = StringUtils.remove(key_remove_dateStr, "fpr-ved-");
                String[] engineArr = engine.split("-");
                engineIdStr = engineArr[0];
                languageIdStr = engineArr[1];
                queryName = "0";
                keywordType = BackBlazeKeywordStoreEntity.TYPE_FPR_VED_NATIONAL;
            } else if (key_remove_dateStr.startsWith("fpr-")
                    && !key_remove_dateStr.contains("city")
                    && !key_remove_dateStr.contains("mobile")) {
                // "20200802/fpr-1-1/5.zip"
                String engine = StringUtils.remove(key_remove_dateStr, "fpr-");
                String[] engineArr = engine.split("-");
                engineIdStr = engineArr[0];
                languageIdStr = engineArr[1];
                queryName = "0";
                keywordType = BackBlazeKeywordStoreEntity.TYPE_FPR_NATIONAL;
            } else {
                throw new Exception("parse key exception");
            }
        } else if (key_remove_dateStr.startsWith("job-")) {
            if (key_remove_dateStr.startsWith("job-mobile-") && !key_remove_dateStr.contains("city")) {
                // "20200802/job-mobile-1-1/6.zip"
                String engine = StringUtils.remove(key_remove_dateStr, "job-mobile-");
                String[] engineArr = engine.split("-");
                engineIdStr = engineArr[0];
                languageIdStr = engineArr[1];
                queryName = "0";
                keywordType = BackBlazeKeywordStoreEntity.TYPE_JOB_NATIONAL;
            } else if (key_remove_dateStr.startsWith("job-mobile-") && key_remove_dateStr.contains("city")) {
                // "20200802/job-mobile-city-13-16-01850/62.zip"
                String engine = StringUtils.remove(key_remove_dateStr, "job-mobile-city-");
                int i1 = engine.indexOf('-');
                engineIdStr = engine.substring(0, i1);
                engine = engine.substring(i1 + 1);
                int i2 = engine.indexOf('-');
                languageIdStr = engine.substring(0, i2);
                queryName = engine.substring(i2 + 1);
                keywordType = BackBlazeKeywordStoreEntity.TYPE_JOB_GEO;
            } else if (key_remove_dateStr.startsWith("job-city-")) {
                // "20200802/job-city-25-9-01028/61.zip"
                String engine = StringUtils.remove(key_remove_dateStr, "job-city-");
                int i1 = engine.indexOf('-');
                engineIdStr = engine.substring(0, i1);
                engine = engine.substring(i1 + 1);
                int i2 = engine.indexOf('-');
                languageIdStr = engine.substring(0, i2);
                queryName = engine.substring(i2 + 1);
                keywordType = BackBlazeKeywordStoreEntity.TYPE_JOB_GEO;
            } else if (key_remove_dateStr.startsWith("job-")
                    && !key_remove_dateStr.contains("city")
                    && !key_remove_dateStr.contains("mobile")) {
                // "20200802/job-1-1/5.zip"
                String engine = StringUtils.remove(key_remove_dateStr, "job-");
                String[] engineArr = engine.split("-");
                engineIdStr = engineArr[0];
                languageIdStr = engineArr[1];
                queryName = "0";
                keywordType = BackBlazeKeywordStoreEntity.TYPE_JOB_NATIONAL;
            } else {
                throw new Exception("parse key exception");
            }
        } else if (key_remove_dateStr.startsWith("ll-v2-")) {
            // "20200802/ll-v2-1-1/156995/10.zip"
            String[] strArr = key_remove_dateStr.split("/");
            if (strArr.length == 2) {
                queryName = strArr[1];
                String engine = strArr[0];
                engine = StringUtils.remove(engine, "ll-v2-");
                String[] engineArr = engine.split("-");
                engineIdStr = engineArr[0];
                languageIdStr = engineArr[1];
                if (queryName.equals("0")) {
                    keywordType = BackBlazeKeywordStoreEntity.TYPE_LL_V2_NATIONAL;
                } else {
                    keywordType = BackBlazeKeywordStoreEntity.TYPE_LL_V2_GEO;
                }
            } else {
                String engine = strArr[0];
                engine = StringUtils.remove(engine, "ll-v2-");
                String[] engineArr = engine.split("-");
                engineIdStr = engineArr[0];
                languageIdStr = engineArr[1];
                queryName = "0";
                keywordType = BackBlazeKeywordStoreEntity.TYPE_LL_V2_NATIONAL;
            }
        } else if (key_remove_dateStr.startsWith("ll-") && !key_remove_dateStr.contains("v2")) {
            // "20200802/ll-14-15/11.zip"
            String[] strArr = key_remove_dateStr.split("/");
            if (strArr.length == 2) {
                queryName = strArr[1];
                String engine = strArr[0];
                engine = StringUtils.remove(engine, "ll-");
                String[] engineArr = engine.split("-");
                engineIdStr = engineArr[0];
                languageIdStr = engineArr[1];
                if (queryName.equals("0")) {
                    keywordType = BackBlazeKeywordStoreEntity.TYPE_LL_NATIONAL;
                } else {
                    keywordType = BackBlazeKeywordStoreEntity.TYPE_LL_GEO;
                }
            } else {
                String engine = strArr[0];
                engine = StringUtils.remove(engine, "ll-");
                String[] engineArr = engine.split("-");
                engineIdStr = engineArr[0];
                languageIdStr = engineArr[1];
                queryName = "0";
                keywordType = BackBlazeKeywordStoreEntity.TYPE_LL_NATIONAL;
            }
        } else {
            throw new Exception("parse key exception");
        }

        FileInfo fileInfo = new FileInfo();
        StringBuffer folderBuf = new StringBuffer();
        String[] fullPath_arr = key.split("/");
        for (int m = 0; m < fullPath_arr.length - 1; m++) {
            fileInfo.getPathList().add(fullPath_arr[m]);
            folderBuf.append(fullPath_arr[m]).append("/");
        }
        fileInfo.setFolder(folderBuf.toString());
        Integer engineId = Integer.parseInt(engineIdStr);
        Integer languageId = Integer.parseInt(languageIdStr);

        if (date == null ||
                keywordName == null || keywordName == "" ||
                keywordType == null ||
                engineId == null ||
                languageId == null ||
                device == null || device == "" ||
                queryName == null || queryName == "" ||
                fullPath_arr == null || fullPath_arr.length == 0

        ) {
            System.out.println("无法解析文件信息!");
            throw new Exception("无法解析文件信息!");
        }
        boolean isAlterNative = false;
        if (keywordName.length() >= 250) {
            isAlterNative = true;
        }
        String rawKeywordName = StringEscapeUtils.unescapeHtml(StringEscapeUtils.escapeHtml(URLDecoder.decode(keywordName, "UTF-8")));
        fileInfo.setKey(key);
        fileInfo.setStatus(true);
        fileInfo.setIsAlterNative(isAlterNative);
        FileInfo.InfoData infoData = fileInfo.new InfoData();
        infoData.setDate(date);
        infoData.setKeywordName(keywordName);
        infoData.setRawKeywordName(rawKeywordName);
        infoData.setKeywordType(keywordType);
        infoData.setEngineId(engineId);
        infoData.setLanguageId(languageId);
        infoData.setDevice(device);
        infoData.setQueryName(queryName);
        fileInfo.setInfoData(infoData);
        return fileInfo;
    }

    private AmazonS3 instantiateB2Client() {
//            log.info("$EC=>" + Thread.currentThread().getName() + " Create B2 Client.....");
        ClientConfiguration clientConfigurationTemplate = new ClientConfiguration();
        clientConfigurationTemplate.setMaxConnections(50);
        clientConfigurationTemplate.setConnectionMaxIdleMillis(60 * 1000);
        clientConfigurationTemplate.setMaxErrorRetry(5);
        BasicAWSCredentials bbCredentials = new BasicAWSCredentials(APP_KEY_ID, APP_KEY);
        AmazonS3 bb = AmazonS3ClientBuilder.standard()
                .withEndpointConfiguration(new AwsClientBuilder.EndpointConfiguration(endpoint, region))
                .withCredentials(new AWSStaticCredentialsProvider(bbCredentials))
                .withClientConfiguration(clientConfigurationTemplate)
                .build();
        return bb;
    }


    /**
     * @param savedZipFileNameList 保存成功的zip文件集合
     * @return 1: success; 2: error; 3: upload 403 over limit
     * 如果同时存在 2和 3, 那么返回 4
     */
    private int saveFileToBackBlaze(Set<String> savedZipFileNameList,
                                    LinkedHashMap<String, String> zipFileFullPathPZipFileNameMap,
                                    Map<String, List<String>> zipFileNamePLocalFileNameListMap, AmazonS3 b2) {
//            log.info("$EC=>" + Thread.currentThread().getName() + "=>start save file to b2");
        int result = uploadToBBSuccess;
        flagFor:
        for (String zipFileFullPath : zipFileFullPathPZipFileNameMap.keySet()) {
            String zipFileName = zipFileFullPathPZipFileNameMap.get(zipFileFullPath);
            String[] str_path;
            if (zipFileFullPath.contains("/")) {
                str_path = zipFileFullPath.split("/");
            } else {
                str_path = zipFileFullPath.split("\\\\");
            }

            String fileFolderPath = "";
            for (int i = 0; i < str_path.length - 1; i++) {
                if (!tempFilePath.contains(str_path[i])) {
                    fileFolderPath = fileFolderPath + str_path[i] + "/";
                }
            }
//            System.out.println(" fileFolderPath : " + fileFolderPath);
            PutObjectRequest putObjectRequest = null;
            FileInputStream in = null;
            // TODO: 2022/1/14
            int tryCnt = 80;
            int tryNum = 1;
            long sleepTime = 5;//秒
            flagWh:
            while (tryCnt > 0) {
                try {
                    ObjectMetadata metadata = new ObjectMetadata();
                    in = new FileInputStream(zipFileFullPath);
                    byte[] bytes = IOUtils.toByteArray(in);
                    //需要指定流长度和文件类型并关闭流,不然就会 out of memory.
                    metadata.setContentLength(bytes.length);
                    metadata.setContentType(ZIP_CONTENT_TYPE);
                    ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(bytes);

                    String key = fileFolderPath + zipFileName;
                    putObjectRequest = new PutObjectRequest(bbBucketName, key, byteArrayInputStream, metadata);
//                        log.info("$EC=>" + Thread.currentThread().getName() + "=>start save file: " + zipFileName + " to b2");

                    long saveFileStart = System.currentTimeMillis();
                    b2.putObject(putObjectRequest);
                    savedZipFileNameList.add(zipFileName);
                    long saveFileEnd = System.currentTimeMillis();
//                        log.info("$EC=>" + Thread.currentThread().getName() + "=> end save file to b2, time(ms): " + (saveFileEnd-saveFileStart));
//                        log.info("$EC=>" + Thread.currentThread().getName() + "=>save file: " + zipFileName + " to b2 success");
                    S3ResponseMetadata cachedResponseMetadata = b2.getCachedResponseMetadata(putObjectRequest);
                    String hostId = cachedResponseMetadata.getHostId();
                    // 下载测试 是否正确上传到bb
//                    S3Object s3datacheck = b2.getObject(bbBucketName,key);
//                    S3ObjectInputStream bbo = s3datacheck.getObjectContent();
//                    File localFile;
//                    localFile = new File(testdown + zipFileName);
//                    System.out.println(" localFile : " + localFile);
//                    File fileParent = localFile.getParentFile();
//                    if (!fileParent.exists()) {
//                        localFile.getParentFile().mkdirs();
//                    }
//                    FileOutputStream fos = new FileOutputStream(localFile);
//                    byte[] read_buf = new byte[1024];
//                    int read_len = 0;
//                    while ((read_len = bbo.read(read_buf)) > 0) {
//                        fos.write(read_buf, 0, read_len);
//                    }
//                    bbo.close();
//                    fos.close();

                    // 推数据的时间大于500秒, 打印
                    if ((saveFileEnd - saveFileStart) >= (100 * 1000)) {
                        log.info("$EC=>" + Thread.currentThread().getName() + "=> send file takes too long, time(ms): " + (saveFileEnd - saveFileStart) + ", hostId: " + hostId);
                    }
                    continue flagFor;
                } catch (Exception e) {
                    log.error("$EC=>" + Thread.currentThread().getName() + " b2 error msg: " + e.getMessage());
                    //重新实例化b2客户端
                    b2.shutdown();
                    b2 = instantiateB2Client();
                    if (e.getMessage().contains("Error Code: 403 Forbidden")) {
                        //上传文件达到限定值, 需要等待5s之后重试, 下一次翻倍!
                        sleepTime = sleepTime * (1 << (tryNum - 1));
                        if (sleepTime >= 3600) {
                            //大于1小时就按1小时sleep
                            sleepTime = 3600;
                        }
                        log.error("$EC=>" + Thread.currentThread().getName() + " 403 Forbidden! " + (tryCnt == 0 ? "program is about to exit" : "will wait " + (sleepTime) + "s"));
                        try {
                            Thread.sleep(sleepTime * 1000);
                        } catch (InterruptedException interruptedException) {
                            interruptedException.printStackTrace();
                        }

                        if (sleepTime == 5 || sleepTime > 2000) {
                            String subject = " Transfer Data From S3 To BackBlaze. Error Code: 403 Forbidden ";
                            String msg = " Upload failed! backblaze upload forbidden! Please check backblaze ";
                            String[] emailTo = {"<EMAIL>", "<EMAIL>"};
//                            sendEmail(subject, msg, emailTo);
                        }

                        tryNum++;
                        tryCnt--;
                    } else {
                        //出现上传错误, 等待10秒后重新上传
                        try {
                            log.error("$EC=>" + Thread.currentThread().getName() + " upload to b2 error, file: " + fileFolderPath + zipFileName);
                            Thread.sleep(10 * 1000);
                        } catch (InterruptedException interruptedException) {
                            interruptedException.printStackTrace();
                        }
                        try {
                            b2.putObject(putObjectRequest);
                            continue flagFor;
                        } catch (Exception e1) {
                            log.error("$EC=>" + Thread.currentThread().getName() + " upload to b2 error for retry, msg: " + e.getMessage());
                            log.error("$EC=>" + Thread.currentThread().getName() + " upload to b2 error for retry, file: " + fileFolderPath + zipFileName);
                            return uploadToBBError;
                        }
                    }
                } finally {
                    try {
//                            log.info("$EC=>" + Thread.currentThread().getName() + " 关闭流");
                        in.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }
            log.error("$EC=>" + Thread.currentThread().getName() + " overlimit first file: " + fileFolderPath + zipFileNamePLocalFileNameListMap.get(zipFileName).get(0));
            log.error("$EC=>" + Thread.currentThread().getName() + " current thread first file: " + fileFolderPath + zipFileNamePLocalFileNameListMap.get(zipFileName).get(0));
            return uploadToBBOverLimit;
        }
        System.out.println("文件上传到 bb成功 ！");
        return result;
    }


//    public void sendEmail(String subject, String message, String[] emailTo) {
//        try {
//            Map<String, Object> reportMap = new HashMap<String, Object>();
//            reportMap.put("userName", "all");
//            reportMap.put("dateString", DateFormatUtils.format(new Date(), "MM/dd/yyyy"));
//            reportMap.put("startTime", DateFormatUtils.format(new Date(), "MM/dd/yyyy HH:mm:ss"));
//            reportMap.put("endTime", DateFormatUtils.format(new Date(), "MM/dd/yyyy HH:mm:ss"));
//            reportMap.put("title", " transfer files from DigitalOcean to BB ");
//            reportMap.put("info", "");
//            if (null == message) {
//                message = "success!";
//            }
//            reportMap.put("errormessage", message);
//            emailSenderComponent.sendMimeMultiPartMailAndBcc(emailTo, null, subject, "mail_common.txt", "mail_common.html", reportMap);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }

    @Data
    public class FileInfo {
        //s3文件路径, 不包含桶  eg: 20200802/255-30/online+poker
        private String key;
        //s3文件夹, 不包含桶  eg: 20200802/255-30/
        private String folder;
        private Boolean status;
        //长度超过250做替代(linux不能创建名称长度超过255的文件)
        private Boolean isAlterNative;
        //file size
        private long length;
        //s3文件路径数组,不包含桶, 不包含文件名 eg: ["20200802","255-30"]
        private List<String> pathList = new ArrayList<>();
        private InfoData infoData;

        private Message message;

        @Data
        public class InfoData {
            private int date;
            private String keywordName;
            private String rawKeywordName;
            private int keywordType;
            private int engineId;
            private int languageId;
            private String device;
            private String queryName;
        }
    }

}
