package seoclarity.backend.upload;

import java.io.BufferedReader;
import java.io.FileInputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.lang.StringUtils;

import seoclarity.backend.dao.clickhouse.monthlyranking.CentralKeywordTokenizerDAO;
import seoclarity.backend.dao.rankcheck.SeoClarityKeywordEntityDAO;
import seoclarity.backend.entity.clickhouse.monthlyranking.KeywordTokenizerSearchvolumeEntity;
import seoclarity.backend.entity.rankcheck.SeoClarityKeywordEntity;
import seoclarity.backend.multithread.core.modlefactory.CacheModleFactory;
import seoclarity.backend.multithread.core.thread.command.BaseThreadCommand;
import seoclarity.backend.multithread.core.thread.threadpool.ThreadPoolManager;
import seoclarity.backend.utils.CommonUtils;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.utils.keywordTokenizer.SnowBallAndNgramForForeignLanguages;

/**
 * <AUTHOR>
 * @date 2021-08-04
 * @path seoclarity.backend.upload.MonthlyKeywordNgramTest
 * https://www.wrike.com/open.htm?id=733403625
 */
public class MonthlyKeywordNgramTest {
	private static final int START_KID_RANGE = 200;
	private static final int MIN_REL_ID = 0;
	private static final int PAGE_SIZE = 1000;
	private static final String countryCode = "US";
	private static final String languageName = "English";
	private static final int MAX_KID_RANGE = 100000;
	private static final String DEFAULT_TOPIC_EXPLORER_TABLE = "keyword_searchvolume.keyword_categorize_v2";
	
	public static ThreadPoolManager threadPool = ThreadPoolManager.getInstance();
	private SeoClarityKeywordEntityDAO seoClarityKeywordEntityDAO;
	
	public MonthlyKeywordNgramTest() {
		seoClarityKeywordEntityDAO = SpringBeanFactory.getBean("seoClarityKeywordEntityDAO");
	}
	
	public static void main(String[] args) {
		CommonUtils.initThreads(5);
		MonthlyKeywordNgramTest ins = new MonthlyKeywordNgramTest();
//		ins.process(1, 1);
		ins.processForFile(args[0], 1, 1);
	}
	
	private void processForFile(String file, int engine, int language) {
		System.out.println("=========Start to process file:" + file);
		threadPool.init();
		int totalCnt = 0;
		List<String> tempList = new ArrayList<>(0);
		try {
			BufferedReader reader = new BufferedReader(new InputStreamReader(new FileInputStream(file), "UTF-8"));
			String line = "";
			do {
				line = reader.readLine();
				if (line == null) {
					break;
				}
				tempList.add(line);
				totalCnt++;
				if (tempList.size() >= PAGE_SIZE) {
					System.out.println("===current process engine:" + engine + ", language:" + language + ", kwList:" + tempList.size() + ", totalCnt:" + totalCnt);
					processUpload(engine, language, new ArrayList<>(tempList));
					tempList.clear();
				}
			} while (line != null);
			
			if (tempList.size() > 0) {
				System.out.println("===current process engine:" + engine + ", language:" + language + ", kwList:" + tempList.size() + ", totalCnt:" + totalCnt);
				processUpload(engine, language, new ArrayList<>(tempList));
				tempList.clear();
			}
			
			reader.close();
		} catch (Exception e) {
			e.printStackTrace();
		}
		
		do {
			try {
				Thread.sleep(100);
			} catch (Exception e) {
				e.printStackTrace();
			}
		} while (threadPool.getThreadPool().getActiveCount() > 0);
		threadPool.destroy();
		System.out.println("========Process End, engine:" + engine + ", language:" + language + ", totalCnt:" + totalCnt);
	}
	
	private void process(int engine, int language) {
		threadPool.init();
		int dynamicRange = START_KID_RANGE;
		int startKid = MIN_REL_ID;
		int endKid = MIN_REL_ID + dynamicRange;
		Integer maxKid = seoClarityKeywordEntityDAO.getMaxKidFromMonthRelations(engine, language);
		System.out.println("===Get MAX KID, engine:" + engine + ", language:" + language + ", maxKid:" + maxKid + ", startKid:" + startKid + ", endKid:" + endKid);
		if (maxKid == null || maxKid == 0) {
			return;
		}
		int totalCnt = 0;
		do {
			List<Integer> kwList = getKeywords(engine, language, startKid, endKid);
			if (kwList != null && kwList.size() > 0) {
				totalCnt += kwList.size();
				System.out.println("===current process engine:" + engine + ", language:" + language 
						+ ", kwList:" + kwList.size() + ", totalCnt:" + totalCnt + ", startKid:" + startKid + ", endKid:" + endKid + ", dynamicRange:" + dynamicRange);
				if (kwList.size() <= PAGE_SIZE) {
					processUpload(engine, language, kwList, countryCode, languageName, startKid, endKid);
				} else {
					List<Integer> tempList = new ArrayList<Integer>();
					for (Integer kw : kwList) {
						tempList.add(kw);
						if (tempList.size() >= PAGE_SIZE) {
							processUpload(engine, language, new ArrayList<Integer>(tempList), countryCode, languageName, startKid, endKid);
							tempList.clear();
						}
					}
					if (tempList.size() > 0) {
						processUpload(engine, language, new ArrayList<Integer>(tempList), countryCode, languageName, startKid, endKid);
						tempList.clear();
					}
				}
				if (kwList.size() > PAGE_SIZE * 2) {
					dynamicRange = dynamicRange / 2;
				} else if (kwList.size() < PAGE_SIZE / 3) {
					dynamicRange = dynamicRange * 2;
				}
			} else {
				dynamicRange = dynamicRange * 2;
			}
			dynamicRange = dynamicRange >= MAX_KID_RANGE ? MAX_KID_RANGE : dynamicRange;
			startKid = endKid;
			endKid = endKid + dynamicRange;
		} while (startKid <= maxKid);
		
		do {
			try {
				Thread.sleep(100);
			} catch (Exception e) {
				e.printStackTrace();
			}
		} while (threadPool.getThreadPool().getActiveCount() > 0);
		threadPool.destroy();
		System.out.println("========Process End, engine:" + engine + ", language:" + language + ", totalCnt:" + totalCnt);
		
	}
	
	private List<Integer> getKeywords(int engine, int language, int startKid, int endKid) {
		List<Integer> kwList = null;
		List<Map<String, Object>> relList = seoClarityKeywordEntityDAO.getMonthlyRelationsKeywords(engine, language, startKid, endKid);
		if (relList.size() > 0) {
			kwList = parseToKeywordEntity(relList, engine, language);
		}
		return kwList;
	}
	
	private List<Integer> parseToKeywordEntity(List<Map<String, Object>> relList, int engine, int language) {
		List<Integer> processList = relList.stream().map(x -> {
			int kid = Integer.parseInt(x.get("kwId").toString());
			return kid;
		}).collect(Collectors.toList());
		return processList;
	}
	
	private void processUpload(int engine, int language, List<Integer> kwList, String countryCode, String languageName, int minKid, int maxKid) {
		String ipAddress = CacheModleFactory.getInstance().getAliveIpAddress();
		while(ipAddress == null) {
			try {
				Thread.sleep(1000);
				ipAddress = CacheModleFactory.getInstance().getAliveIpAddress();
			} catch (Exception e) {
			}
		}
		UploadKwCommand cmd = new UploadKwCommand(ipAddress, engine, language, kwList, countryCode, languageName);
		cmd.setStatus(true);
		try {
			threadPool.execute(cmd);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	private void processUpload(int engine, int language, List<String> kwList) {
		String ipAddress = CacheModleFactory.getInstance().getAliveIpAddress();
		while(ipAddress == null) {
			try {
				Thread.sleep(1000);
				ipAddress = CacheModleFactory.getInstance().getAliveIpAddress();
			} catch (Exception e) {
			}
		}
		UploadKwCommand cmd = new UploadKwCommand(ipAddress, engine, language, kwList);
		cmd.setStatus(true);
		try {
			threadPool.execute(cmd);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	class UploadKwCommand extends BaseThreadCommand{
		private String ipAddress;
		private int engine;
		private int language;
		private List<Integer> kwList;
		private List<String> kwStrList;
		private String countryCode, languageName;
		
		private SeoClarityKeywordEntityDAO seoClarityKeywordEntityDAO;
		private CentralKeywordTokenizerDAO centralKeywordTokenizerDAO;
		
		
		public UploadKwCommand(String ipAddress, int engine, int language, List<Integer> kwList, String countryCode, String languageName) {
			this.ipAddress = ipAddress;
			this.engine = engine;
			this.language = language;
			this.kwList = kwList;
			this.countryCode = countryCode;
			this.languageName = languageName;
			seoClarityKeywordEntityDAO = SpringBeanFactory.getBean("seoClarityKeywordEntityDAO");
			centralKeywordTokenizerDAO = SpringBeanFactory.getBean("centralKeywordTokenizerDAO");
		}
		
		public UploadKwCommand(String ipAddress, int engine, int language, List<String> kwStrList) {
			this.ipAddress = ipAddress;
			this.engine = engine;
			this.language = language;
			this.kwStrList = kwStrList;
			seoClarityKeywordEntityDAO = SpringBeanFactory.getBean("seoClarityKeywordEntityDAO");
			centralKeywordTokenizerDAO = SpringBeanFactory.getBean("centralKeywordTokenizerDAO");
		}


		@Override
		protected void execute() throws Exception {
			long a = System.currentTimeMillis();
			
			List<KeywordTokenizerSearchvolumeEntity> processList = null;
			if (kwStrList == null || kwStrList.size() == 0) {
				List<Integer> dityList = checkDirtyKeywords();
				processList = prepareUploadSvKeywords(dityList);
			} else {
				processList = prepareUploadKeywords(kwStrList);
			}
			
			// upload
			processUpload(processList);
			
			long b = System.currentTimeMillis();
			System.out.println("End command IP: " + ipAddress + " ,Cost time: " + (b - a) * 1.0 / 1000 + "s");
			CacheModleFactory.getInstance().setAliveIpAddress(ipAddress);
		}

		private List<Integer> checkDirtyKeywords() {
			List<Integer> dirtyList = seoClarityKeywordEntityDAO.checkExistsInDirtyKeywords(engine, language, kwList);
			return dirtyList;
		}
		
		private List<KeywordTokenizerSearchvolumeEntity> prepareUploadSvKeywords(List<Integer> dityList) {
			List<Integer> processKidList = new ArrayList<>(kwList);
			// remove dirty keyword
			if (dityList != null && dityList.size() > 0) {
				processKidList.removeAll(dityList);
			}
			
//			List<String>existsList = new ArrayList<>(0);
//			if (processKidList.size() > 0) {
//				existsList = keywordTokenizerDAO.checkKeywordExists(engine, language, 0, processKidList, DEFAULT_TOPIC_EXPLORER_TABLE);
//				if (existsList.size() > 0) {
//					processKidList.removeAll(existsList.stream().map(x -> Integer.valueOf(x).intValue()).collect(Collectors.toList()));
//				}
//			}
			
			List<KeywordTokenizerSearchvolumeEntity> processKwList = new ArrayList<KeywordTokenizerSearchvolumeEntity>(0);
			if (processKidList.size() > 0) {
				List<SeoClarityKeywordEntity> list = seoClarityKeywordEntityDAO.getKeywordByIdList(processKidList);
				for (SeoClarityKeywordEntity kw : list) {
					try {
						KeywordTokenizerSearchvolumeEntity entity = new KeywordTokenizerSearchvolumeEntity();
						entity.setKeywordRankcheckId(Long.valueOf(kw.getId()));
						String kwName = kw.getDecoderKeyword().toLowerCase();
						entity.setKeywordName(kwName);
						entity.setEngineId(engine);
						entity.setLanguageId(language);
						
						List<String> keywordVariationOneword = new ArrayList<String>();
						keywordVariationOneword.addAll(SnowBallAndNgramForForeignLanguages.oneWordNgramTokenizer(kwName));
						entity.setKeywordVariationOneword(keywordVariationOneword);
						
						processKwList.add(entity);
					} catch (Exception e) {
						e.printStackTrace();
					}
				}
			}
			System.out.println("=kwList:" + kwList.size() + ", dityList:" + dityList.size() + ", processKwList:" + processKwList.size());
			return processKwList;
		}
		
		private List<KeywordTokenizerSearchvolumeEntity> prepareUploadKeywords(List<String> kwStrList) {
			List<KeywordTokenizerSearchvolumeEntity> processKwList = new ArrayList<KeywordTokenizerSearchvolumeEntity>(0);
			if (kwStrList.size() > 0) {
				for (String kw : kwStrList) {
					try {
						KeywordTokenizerSearchvolumeEntity entity = new KeywordTokenizerSearchvolumeEntity();
						String kwName = StringUtils.trim(kw).toLowerCase();
						entity.setKeywordName(kwName);
						entity.setEngineId(engine);
						entity.setLanguageId(language);
						
						List<String> keywordVariationOneword = new ArrayList<String>();
						keywordVariationOneword.addAll(SnowBallAndNgramForForeignLanguages.oneWordNgramTokenizer(kwName));
						entity.setKeywordVariationOneword(keywordVariationOneword);
						
						processKwList.add(entity);
					} catch (Exception e) {
						e.printStackTrace();
					}
				}
			}
			System.out.println("=kwStrList:" + kwStrList.size() + ", processKwList:" + processKwList.size());
			return processKwList;
		}
		
		private void processUpload(List<KeywordTokenizerSearchvolumeEntity> processList) {
			try {
				if (processList.size() > 0) {
					centralKeywordTokenizerDAO.insertBatch(processList, DEFAULT_TOPIC_EXPLORER_TABLE);
				}
			} catch (Exception e) {
				System.out.println("insert failed, kwList:" + kwList);
				e.printStackTrace();
			}
		}
		
		@Override
		protected void undo() throws Exception {
		}
	}
	
}
