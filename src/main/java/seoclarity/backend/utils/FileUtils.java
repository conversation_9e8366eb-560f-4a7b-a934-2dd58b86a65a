package seoclarity.backend.utils;

import java.io.BufferedOutputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;


public class FileUtils {

	private static final int BUF_SIZE = 8192;

//	public static void removeFileToBackUpFolder(String loc, String filename) {
//		File backUpDir = new File(loc + "backup");
//		if (!backUpDir.exists() || !backUpDir.isDirectory()) {
//			backUpDir.mkdir();
//		}
//		File file = new File(loc + filename);
//		boolean success = file.renameTo(new File(backUpDir, file.getName()));
//		if (!success) {
//			System.out.println("Can't move file to backup directory");
//		}
//	}
//
//	public static void removeFileToBackUpFolder(String loc, String filename, boolean needDeleteWhenError) {
//		File backUpDir = new File(loc + "backup");
//		if (!backUpDir.exists() || !backUpDir.isDirectory()) {
//			backUpDir.mkdir();
//		}
//		File file = new File(loc + filename);
//		boolean success = file.renameTo(new File(backUpDir, file.getName()));
//		if (!success) {
//			if (needDeleteWhenError) {
//				file.delete();
//			} else {
//				System.out.println("Can't move file to backup directory");
//			}
//		}
//	}
//
//	public static void deleteFile(String loc, String filename) {
//		File file = new File(loc + filename);
//		if (file.exists()) {
//			file.delete();
//		}
//	}
//
//	public static File getFile(String directoryLocs, String filename) {
//		File ppcFile = new File(directoryLocs + filename);
//		return ppcFile;
//	}

	public static byte[] getBytesFromFile(File f) {
		if (f == null) {
			return null;
		}
		try {
			FileInputStream stream = new FileInputStream(f);
			ByteArrayOutputStream out = new ByteArrayOutputStream();
			byte[] b = new byte[BUF_SIZE];
			int n;
			while ((n = stream.read(b)) != -1)
				out.write(b, 0, n);
			stream.close();
			out.close();
			return out.toByteArray();
		} catch (IOException e) {
			e.printStackTrace();
		}
		return null;
	}

	public static File getFileFromBytes(byte[] b, String outputFile) {
		BufferedOutputStream stream = null;
		File file = null;
		try {
			file = new File(outputFile);
			FileOutputStream fstream = new FileOutputStream(file);
			stream = new BufferedOutputStream(fstream);
			stream.write(b);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (stream != null) {
				try {
					stream.close();
				} catch (IOException e1) {
					e1.printStackTrace();
				}
			}
		}
		return file;
	}
	
	public static void deleteFile(String loc, String filename) {
		File file = new File(loc + filename);
		if (file.exists()) {
			file.delete();
		}
	}
	
	
	/**
	 * added by zhaozh
	 * @param file
	 */
	public static void deleteFile(File file){ 
		   if(file.exists()){                   
		    if(file.isFile()){                   
		     file.delete();                      
		    }else if(file.isDirectory()){              
		     File files[] = file.listFiles();            
		     for(int i=0;i<files.length;i++){          
		    	 FileUtils.deleteFile(files[i]);      
		     } 
		    } 
		    file.delete(); 
		   }else{ 
		    System.out.println("the file or folder is not exixt!"+file.getName()+'\n'); 
		   } 
	}

	public static File createFile(String filefullname) {
		File file = new File(filefullname);
		if (file.exists()) {
			file.delete();
		} else {
			System.out.println("File not exists, create it ...");
			if (!file.getParentFile().exists()) {
				System.out.println("not exists");
				file.getParentFile().mkdirs();
			}
			try {
				file.createNewFile();

			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		return file;
	}

	public  static boolean outPutfile(File file, List<String> outputList,boolean append) {
		try {
			org.apache.commons.io.FileUtils.writeLines(file, "UTF-8", outputList, append);
		} catch (Exception ex) {
			ex.printStackTrace();
			return false;
		}
		return true;
	}

//	/**
//	 * added by zhaozh
//	 * @param file
//	 */
//	public static void deleteFile(File file){
//		   if(file.exists()){
//		    if(file.isFile()){
//		     file.delete();
//		    }else if(file.isDirectory()){
//		     File files[] = file.listFiles();
//		     for(int i=0;i<files.length;i++){
//		    	 FileUtils.deleteFile(files[i]);
//		     }
//		    }
//		    file.delete();
//		   }else{
//		    System.out.println("the file or folder is not exixt!"+file.getName()+'\n');
//		   }
//	}
//
//
//	public static  List<String> getContentFromZip(String loc) throws Exception {
//		ZipFile zipFile = new ZipFile(new File(loc));
//		List<String> lines = new ArrayList<String>();
//
//		Enumeration emu = zipFile.entries();
//		while (emu.hasMoreElements()) {
//			ZipEntry entry = (ZipEntry) emu.nextElement();
//			InputStream content = zipFile.getInputStream(entry);
//
//			lines = IOUtils.readLines(content);
//			break;
//		}
//
//		return lines;
//	}
//
//	public static  List<String> getContentFromZipUtf8(String loc) throws Exception {
//		ZipFile zipFile = new ZipFile(new File(loc));
//		List<String> lines = new ArrayList<String>();
//
//		Enumeration emu = zipFile.entries();
//		while (emu.hasMoreElements()) {
//			ZipEntry entry = (ZipEntry) emu.nextElement();
//			InputStream content = zipFile.getInputStream(entry);
//
//			lines = IOUtils.readLines(content, "UTF-8");
//			break;
//		}
//
//		return lines;
//	}

	public static void removeFileToDateBackUpFolder(String loc, String filename) {
		File backUpDir = new File(loc + "backup");
		if (!backUpDir.exists() || !backUpDir.isDirectory()) {
			backUpDir.mkdir();
		}

		String date = new SimpleDateFormat("yyyyMMdd").format(new Date());
		File dateDir = new File(backUpDir.getAbsolutePath() +"/" + date);
		if (!dateDir.exists() || !dateDir.isDirectory()) {
			dateDir.mkdir();
		}

		File file = new File(loc + filename);

		File f = new File(dateDir + "/"+ filename);
		if(f.exists()){
			String newFilePath = loc + filename + "-" + new SimpleDateFormat("HHmmss").format(new Date());
			file.renameTo(new File(newFilePath));
			file = new File(newFilePath);
		}

		boolean success = file.renameTo(new File(dateDir, file.getName()));
		if (!success) {
			System.out.println("Can't move file to backup date directory");
		}
		//zip file and delete file
		try {
			GZipUtil.zip(f.getAbsolutePath());
		}catch (Exception e){
			System.out.println(" zip file error : " + f.getAbsolutePath());
			e.printStackTrace();
		}
		f.delete();
	}

	public static int GetFileKBSize(File file){
		int kbSize = 0;
		if(file.exists() && file.isFile()){
			long fileS = file.length();
			kbSize = (int)fileS/1024;
		}
		return kbSize;
	}

	public static long countOfLines(String path) throws Exception{
		long st = System.currentTimeMillis();
		long lines = Files.lines(Paths.get(new File(path).getPath())).count();
		System.out.println("===total lines: " + lines);
		System.out.println("===========time:" + (System.currentTimeMillis() - st) + " ms");
		return lines;
	}

	public static void main(String[] s) throws Exception{
		String path = "D:\\Extract\\1 one time\\8682_RankExtract_20191208_desktop.csv";
		countOfLines(path);
	}
}
