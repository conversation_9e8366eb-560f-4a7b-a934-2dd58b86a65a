package seoclarity.backend.utils;

import com.jcraft.jsch.*;
import lombok.extern.apachecommons.CommonsLog;
import org.slf4j.LoggerFactory;
import seoclarity.backend.export.TopXKeywordRankingExportDailyForApple;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Properties;
import java.util.Vector;

@CommonsLog
public class SFTPUtils {

    private String host;
    private String username;
    private String password;
    private File privateKeyFile;
    private int port = 22;
    private Session sshSession = null;
    private static ChannelSftp sftp;
    private static SFTPUtils instance = null;

    private SFTPUtils() {
    }

    public static SFTPUtils getInstance(String host, int port, String username, String password) {

//        if (instance == null) {
            instance = new SFTPUtils();
            sftp = instance.connect(host, port, username, password);
//        }

        return instance;
    }

    public SFTPUtils(String host, String username, File privateKeyFile) {
        this.host = host;
        this.username = username;
        this.privateKeyFile = privateKeyFile;
    }

    public static class MyLogger implements com.jcraft.jsch.Logger {
        @Override
        public boolean isEnabled(int level) {
            return true; // 返回true表示所有级别的日志都将被记录
        }

        @Override
        public void log(int level, String message) {
            switch (level) {
                case Logger.DEBUG:
                    System.out.println("DEBUG: " + message);
                    break;
                case Logger.INFO:
                    System.out.println("INFO: " + message);
                    break;
                case Logger.WARN:
                    System.err.println("WARN: " + message);
                    break;
                case Logger.ERROR:
                    System.err.println("ERROR: " + message);
                    break;
                default:
                    throw new IllegalStateException("Invalid log level");
            }
        }
    }

    /**
     * 连接sftp服务器
     *
     * @param host     主机
     * @param port     端口
     * @param username 用户名
     * @param password 密码
     * @return
     */
    public ChannelSftp connect(String host, int port, String username, String password) {
        ChannelSftp sftp = null;
        try {
            JSch jsch = new JSch();
            jsch.getSession(username, host, port);
            Session sshSession = jsch.getSession(username, host, port);
            sshSession.setPassword(password);
            Properties sshConfig = new Properties();
            sshConfig.put("StrictHostKeyChecking", "no");
            sshSession.setConfig(sshConfig);
            sshSession.connect();
            log.info("SFTP Session connected.");
            Channel channel = sshSession.openChannel("sftp");
            channel.connect();
            sftp = (ChannelSftp) channel;
            log.info("Connected to " + host);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return sftp;
    }

    /**
     * 上传文件
     *
     * @param directory  上传的目录
     * @param uploadFile 要上传的文件
     */
    public boolean upload(String directory, String uploadFile) {
        try {
            sftp.cd(directory);
            File file = new File(uploadFile);
            FileInputStream fileInputStream = new FileInputStream(file);
            sftp.put(fileInputStream, file.getName());
            fileInputStream.close();
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 下载文件
     *
     * @param directory    下载目录
     * @param downloadFile 下载的文件
     * @param saveFile     存在本地的路径
     */
    public File download(String directory, String downloadFile, String saveFile) {
        try {
            sftp.cd(directory);
            File file = new File(saveFile);
            FileOutputStream fileOutputStream = new FileOutputStream(file);
            sftp.get(downloadFile, fileOutputStream);
            fileOutputStream.close();
            return file;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 下载文件
     *
     * @param downloadFilePath 下载的文件完整目录
     * @param saveFile         存在本地的路径
     */
    public File download(String downloadFilePath, String saveFile) {
        try {
            int i = downloadFilePath.lastIndexOf('/');
            if (i == -1)
                return null;
            sftp.cd(downloadFilePath.substring(0, i));
            File file = new File(saveFile);
            FileOutputStream fileOutputStream = new FileOutputStream(file);
            sftp.get(downloadFilePath.substring(i + 1), fileOutputStream);
            fileOutputStream.close();
            return file;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 删除文件
     *
     * @param directory  要删除文件所在目录
     * @param deleteFile 要删除的文件
     */
    public void delete(String directory, String deleteFile) {
        try {
            sftp.cd(directory);
            sftp.rm(deleteFile);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void disconnect() {
        try {
            sftp.getSession().disconnect();
        } catch (JSchException e) {
            e.printStackTrace();
        }
        sftp.quit();
        sftp.disconnect();
    }

    /**
     * 列出目录下的文件
     *
     * @param directory 要列出的目录
     * @throws SftpException
     */
    public Vector<ChannelSftp.LsEntry> listFiles(String directory) throws SftpException {
        return sftp.ls(directory);
    }

    public void connectByPrivateKey() {
        try {
            JSch jsch = new JSch();
            jsch.addIdentity(privateKeyFile.getAbsolutePath());
            jsch.getSession(username, host, port);
            sshSession = jsch.getSession(username, host, port);
            if (log.isInfoEnabled()) {
                log.info("Session created.");
            }
            Properties sshConfig = new Properties();
            sshConfig.put("StrictHostKeyChecking", "no");
            sshSession.setConfig(sshConfig);
            sshSession.connect();
            if (log.isInfoEnabled()) {
                log.info("Session connected.");
            }
            Channel channel = sshSession.openChannel("sftp");
            channel.connect();
            if (log.isInfoEnabled()) {
                log.info("Opening Channel.");
            }
            sftp = (ChannelSftp) channel;
            if (log.isInfoEnabled()) {
                log.info("Connected to " + host + ".");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    public void connectByPrivateKeyForApple() {
        try {
            JSch jsch = new JSch();
            jsch.addIdentity(privateKeyFile.getAbsolutePath());
            jsch.getSession(username, host, port);
            sshSession = jsch.getSession(username, host, port);
            if (log.isInfoEnabled()) {
                log.info("Session created.");
            }
            Properties sshConfig = new Properties();
            sshConfig.put("StrictHostKeyChecking", "no");

            sshConfig.put("mac.s2c", "hmac-sha2-256");
            sshConfig.put("mac.c2s", "hmac-sha2-256");
//            sshConfig.put("mac.s2c", "<EMAIL>,<EMAIL>,hmac-sha2-512,hmac-sha2-256");
//            sshConfig.put("mac.c2s", "<EMAIL>,<EMAIL>,hmac-sha2-512,hmac-sha2-256");
            sshConfig.put("cipher.s2c", "aes256-ctr");
            sshConfig.put("cipher.c2s", "aes256-ctr");
//            sshConfig.put("cipher.s2c", "<EMAIL>,<EMAIL>,aes256-ctr,aes192-ctr,aes128-ctr");
//            sshConfig.put("cipher.c2s", "<EMAIL>,<EMAIL>,aes256-ctr,aes192-ctr,aes128-ctr");
            sshConfig.put("kex", "ecdh-sha2-nistp521,ecdh-sha2-nistp384,ecdh-sha2-nistp256");
//            sshConfig.put("kex", "ecdh-sha2-nistp256");


            sshSession.setConfig(sshConfig);
            sshSession.connect();
            if (log.isInfoEnabled()) {
                log.info("Session connected.");
            }
            Channel channel = sshSession.openChannel("sftp");
            channel.connect();
            if (log.isInfoEnabled()) {
                log.info("Opening Channel.");
            }
            sftp = (ChannelSftp) channel;
            if (log.isInfoEnabled()) {
                log.info("Connected to " + host + ".");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void main(String[] args) throws IOException {

        String host = "sftp-drop.domo.com";
        int port = 22;
        String userName = "grainger";
        String password = "BiADVV6scNyro0y";

        String directory = "/incoming/seoClarity";

        SFTPUtils sf = SFTPUtils.getInstance(host, port, userName, password);
//        sf.upload(directory, "C:\\Users\\<USER>\\Desktop\\123456.png");    //上传文件

//        sf.download(directory, "2.png", "C:\\Users\\<USER>\\Desktop\\1212.png");
//        File download = sf.download("/home/<USER>", "C:\\Users\\<USER>\\Desktop\\122221.png");

//        sf.delete(directory, deleteFile); //删除文件

        Vector<ChannelSftp.LsEntry> files = null;        //查看文件列表
        try {
            files = sf.listFiles(directory);
        } catch (SftpException e) {
            e.printStackTrace();
        }
        for (ChannelSftp.LsEntry file : files) {
            System.out.println("###\t" + file.getFilename());
        }
        sf.disconnect();
    }


}
