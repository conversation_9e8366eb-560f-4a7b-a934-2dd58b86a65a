package seoclarity.backend.utils;

import com.amazonaws.HttpMethod;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.GeneratePresignedUrlRequest;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.amazonaws.services.s3.transfer.TransferManager;
import com.amazonaws.services.s3.transfer.TransferManagerBuilder;
import com.amazonaws.services.s3.transfer.Upload;
import seoclarity.backend.upload.XferMgrProgress;
import java.io.File;
import java.net.URL;
import java.time.Instant;

public class SeagateUtils { // https://www.wrike.com/open.htm?id=**********

	public static final String SEAGATE_DEFAULT_BUCKET_NAME = "temporary-files"; // TODO
	public static final String SIGNATURE_HOSTNAME = "https://cloud.seoclarity.net"; // TODO
	
    public static final String SEAGATE_SERVICE_ENDPOINT = "https://s3.us-east-1.lyvecloud.seagate.com";
    public static final String SEAGATE_SIGNING_REGION_US_EAST_1 = "us-east-1"; // "seagate"
    
    public static final long DEFAULT_SIGNATURE_DURATION_MILLI_SECONDS = 1000 * 60 * 60 * 24 * 7; // 7 days = 604800 seconds
    public static final int SIGNATURE_DEFAULT_RETRY_COUNT = 3;
    
    private static final String SEAGATE_ACCESS_KEY = "VXXVDNNG14JMNX0R";
    private static final String SEAGATE_SECRET_KEY = "CM0L3YD5F4MSMONWO2EMQ1UYKMU1X03B";
    
    private static final AWSCredentials credentials = new BasicAWSCredentials(SEAGATE_ACCESS_KEY, SEAGATE_SECRET_KEY);
    
    public static void main(String[] args) throws Exception {
    	saveFileToDefaultSeagate(4, "D:\\Doc\\test3.txt");
    	getDefaultSeagatePresignedUrl(4, "test3.txt");
    }
    
    public static boolean saveFileToDefaultSeagate(int ownDomainId, String fullPathFileName) {
    	return saveFileToDefaultSeagate(SEAGATE_DEFAULT_BUCKET_NAME, ownDomainId, fullPathFileName);	
    }
    
    public static boolean saveFileToDefaultSeagate(String bucketName, int ownDomainId, String fullPathFileName) {
    	return saveFileToSeagate(SEAGATE_SERVICE_ENDPOINT, SEAGATE_SIGNING_REGION_US_EAST_1, bucketName, ownDomainId, fullPathFileName, SIGNATURE_DEFAULT_RETRY_COUNT);	
    }
    
    public static String getDefaultSeagatePresignedUrl(int ownDomainId, String fileName) throws Exception {
    	return getDefaultSeagatePresignedUrl(SEAGATE_DEFAULT_BUCKET_NAME, ownDomainId, fileName);
    }
    
    public static String getDefaultSeagatePresignedUrl(int ownDomainId, String fileName, long durationSeconds) throws Exception {
    	return getSeagatePresignedUrl(SIGNATURE_HOSTNAME, SEAGATE_SIGNING_REGION_US_EAST_1, SEAGATE_DEFAULT_BUCKET_NAME, ownDomainId, fileName, durationSeconds, 
    		SIGNATURE_DEFAULT_RETRY_COUNT);
    }
    
    public static String getDefaultSeagatePresignedUrl(String bucketName, int ownDomainId, String fileName) throws Exception {
    	return getSeagatePresignedUrl(SIGNATURE_HOSTNAME, SEAGATE_SIGNING_REGION_US_EAST_1, bucketName, ownDomainId, fileName, 
        	DEFAULT_SIGNATURE_DURATION_MILLI_SECONDS, SIGNATURE_DEFAULT_RETRY_COUNT);
    }
    
    public static boolean saveFileToSeagate(String serviceEndpoint, String signingRegion, String bucketName, int ownDomainId, String fullPathFileName, int retryCount) {
		File tempFile = new File(fullPathFileName);
		String fileName = tempFile.getName();
		String objectKey = ownDomainId + "/" + fileName; // TODO
		for (int triedCount = 0; triedCount < retryCount; triedCount++) {
			try {
				AmazonS3 s3client = AmazonS3ClientBuilder.standard()
      				.withEndpointConfiguration(new AwsClientBuilder.EndpointConfiguration(serviceEndpoint, signingRegion))
      				.withCredentials(new AWSStaticCredentialsProvider(credentials)).build();
				TransferManager transferManager = TransferManagerBuilder.standard().withS3Client(s3client).build();
				Upload uploader = transferManager.upload(new PutObjectRequest(bucketName, objectKey, tempFile));
				// loop with Transfer.isDone()
				XferMgrProgress.showTransferProgress(uploader);
				//  or block with Transfer.waitForCompletion()
				XferMgrProgress.waitForCompletion(uploader);
				transferManager.shutdownNow();
				System.out.println(" ==SavedFileToSeagate bucket:" + bucketName + " objKey:" + objectKey + " file:" + fullPathFileName);
				return true;
			} catch (Exception e) {
				e.printStackTrace();
				System.out.println(" ##FailedToSaveFileToSeagate retryCnt:" + triedCount);
				try {
					Thread.sleep((triedCount + 1) * 60 * 1000);
				} catch (Exception exp) {
					exp.printStackTrace();
				}
			}
		}
		return false;
	}
    
    // Sample parameters: signatureHostName:"https://cloud.seoclarity.net", signingRegion:"us-east-1", bucketName:SEAGATE_DEFAULT_BUCKET_NAME, fileName:"test.txt"
    public static String getSeagatePresignedUrl(String signatureHostName, String signingRegion, String bucketName, int ownDomainId, String fileName, long durationSeconds,
    		int retryCount) throws Exception {
    	int triedCount = 0;
    	while (true) {
	        try {
	            AmazonS3 s3Client = AmazonS3ClientBuilder.standard()
	                .withCredentials(new AWSStaticCredentialsProvider(credentials))
	                .withEndpointConfiguration(new AwsClientBuilder.EndpointConfiguration(signatureHostName, signingRegion)).build();
	            java.util.Date expiration = new java.util.Date();
	            long expTimeMillis = Instant.now().toEpochMilli();
	            expiration.setTime(expTimeMillis + durationSeconds);
	            String key = bucketName + "/" + ownDomainId + "/" + fileName; // TODO
	            System.out.println(" ==CreatingPresignedURL bucket:" + bucketName + " OID:" + ownDomainId + " file:" + fileName + "->objKey:" + key);
	            GeneratePresignedUrlRequest generatePresignedUrlRequest = new GeneratePresignedUrlRequest("", key).withMethod(HttpMethod.GET).withExpiration(expiration);
	            URL url = s3Client.generatePresignedUrl(generatePresignedUrlRequest);
	            System.out.println(" ==CreatedPreSignedURL:" + url.toString());
	            return url.toString();
	        } catch (Exception e) {
	            e.printStackTrace();
	            System.out.println(" ##FailedToGetPresignedUrl retryCnt:" + ++triedCount);
	            if (triedCount > retryCount) {
	            	throw e;
	            }
	            try {
					Thread.sleep((triedCount + 1) * 60 * 1000);
				} catch (Exception exp) {
					exp.printStackTrace();
				}
	        } 
		}
    }
}