package seoclarity.backend.utils.keywordTokenizer;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.lucene.analysis.Analyzer;
import org.apache.lucene.analysis.LowerCaseFilter;
import org.apache.lucene.analysis.TokenStream;
import org.apache.lucene.analysis.miscellaneous.TrimFilter;
import org.apache.lucene.analysis.shingle.ShingleFilterFactory;
import org.apache.lucene.analysis.standard.ClassicFilter;
import org.apache.lucene.analysis.standard.StandardTokenizer;
import org.apache.lucene.analysis.tokenattributes.CharTermAttribute;

public class Ngram_for_dev {
	private final static Map<String, String> shingleParams = new HashMap<String, String>();
	
	public static void main(String[] args) {
		System.out.println(wordTokenizer("aviation inn las vegas"));
		System.out.println(wordTokenizer("adidas's lining nike"));
	}

	public static List<String> wordTokenizer(String text) {
		List<String> results = new ArrayList<String>();

		shingleParams.put("outputUnigrams", "false");
		shingleParams.put("minShingleSize", "2");
		shingleParams.put("maxShingleSize", "5");
		shingleParams.put("fillerToken", "");
		
		try {
			Analyzer sa = new Analyzer() {

				@Override
				protected TokenStreamComponents createComponents(String fieldName) {
					StandardTokenizer source = new StandardTokenizer();
					TokenStream result = new ClassicFilter(source);
					TokenStream lowercaseFilter = new LowerCaseFilter(result);

					TokenStream shingleFilter = new ShingleFilterFactory(shingleParams).create(lowercaseFilter);
					TokenStream trimFilter = new TrimFilter(shingleFilter);
					return new TokenStreamComponents(source, trimFilter);
				}
			};
			TokenStream ts = sa.tokenStream("field", text);

			CharTermAttribute ch = ts.addAttribute(CharTermAttribute.class);

			ts.reset();

			while (ts.incrementToken()) {
				String str = ch.toString();
				results.add(str);
			}

			ts.end();
			ts.close();
			sa.close();
		} catch (Exception ex) {
			ex.printStackTrace();
		}

		return results;
	}
}
