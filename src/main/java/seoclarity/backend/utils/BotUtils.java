package seoclarity.backend.utils;

import org.apache.commons.lang.StringUtils;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2018-09-26 9:26
 **/
public class BotUtils {

    private final static List<String> soOfficialIPs = Arrays.asList("180.153.232.", "180.153.234.", "180.153.236.", "42.236.101.", "42.236.102.", "42.236.103.", "42.236.12.", "42.236.13.", "42.236.14.", "42.236.15.", "42.236.16.", "42.236.17.", "42.236.46.", "42.236.48.", "42.236.49.", "42.236.50.", "42.236.51.", "42.236.52.", "42.236.53.", "42.236.54.", "42.236.55.", "42.236.99.");

    public static final String googleHostRegEx = ".*\\.google(bot)?\\..*$";
    public static final String baiduHostRegEx = ".*\\.crawl.baidu.(com|jp)$";
    public static final String bingHostRegEx = ".*\\.search\\.msn\\.com$";
    public static final String yandexHostRegEx = ".*\\.yandex\\.(ru|net|com)$";
    public static final String yahooHostRegEx = ".*\\.yahoo\\..*$";
    public static final int GOOGLE_TYPE = 1;
    public static final int BING_TYPE = 2;
    public static final int YAHOO_TYPE = 3;
    public static final int BAIDU_TYPE = 4;
    public static final int YANDEX_TYPE = 5;
    public static final int NAVER_TYPE = 6;
    public static final int SO_TYPE = 7;


    public static final int GOOGLE_DESKTOP_GROUP_TYPE = 1;
    public static final int GOOGLE_MOBILE_GROUP_TYPE = 2;
    public static final int BING_GROUP_TYPE = 3;
    public static final int BAIDU_GROUP_TYPE = 4;
    public static final int YANDEX_GROUP_TYPE = 5;
    public static final int OPENAI_GROUP_TYPE = 6;
    public static final int PERPLEXITY_GROUP_TYPE = 7;
    public static final int OTHER_GROUP_TYPE = 8; // https://www.wrike.com/open.htm?id=1576811359
    public static final int UNKNOWN_GROUP_TYPE = -1;

    public static final String SINGLE_TYPE_GOOGLE = "google-bot";
    public static final String SINGLE_TYPE_BING = "bing-bot";
    public static final String SINGLE_TYPE_BAIDU = "baidu-bot";
    public static final String SINGLE_TYPE_YANDEX = "yandex-bot";
    public static final String SINGLE_TYPE_UNKNOWN = "unknown";

    public static final String ACRONYM_TYPE_GOOGLE = "Googlebot";
    public static final String ACRONYM_TYPE_BING = "Bingbot";
    public static final String ACRONYM_TYPE_BAIDU = "Baidubot";
    public static final String ACRONYM_TYPE_YANDEX = "Yandexbot";
    public static final String ACRONYM_TYPE_UNKNOWN = "Unknown";

    public static boolean isGoogleBot(String userAgent) {
        return StringUtils.containsIgnoreCase(userAgent, "googlebot")
                || (StringUtils.containsIgnoreCase(userAgent, "google") && StringUtils.containsIgnoreCase(userAgent, "ads"))
                || StringUtils.containsIgnoreCase(userAgent, "Mediapartners-Google");
    }

    public static boolean isBaiduBot(String userAgent) {
        return StringUtils.containsIgnoreCase(userAgent, "Baiduspider");
    }

    public static boolean isBingBot(String userAgent) {
        return StringUtils.containsIgnoreCase(userAgent, "bingbot");
    }

    public static boolean isYandexBot(String userAgent) {
        return StringUtils.containsIgnoreCase(userAgent, "yandex") && StringUtils.containsIgnoreCase(userAgent, "bot");
    }

    public static boolean isYahooBot(String userAgent) {
        return StringUtils.containsIgnoreCase(userAgent, "Slurp") && StringUtils.containsIgnoreCase(userAgent, "Yahoo");
    }

    public static boolean isNaverBot(String userAgent) {
        return StringUtils.containsIgnoreCase(userAgent, "Yeti");
    }

    public static boolean isSoBot(String userAgent) {
        return StringUtils.containsIgnoreCase(userAgent, "360Spider");
    }

    public static boolean isSoOfficialIPs(String ip) {
        for (String soOfficialIP : soOfficialIPs) {
            if (StringUtils.startsWithIgnoreCase(ip, soOfficialIP)) {
                return true;
            }
        }
        return false;
    }

    public static boolean isBotAgent(String userAgent) {
        return (StringUtils.containsIgnoreCase(userAgent, "googlebot/") && StringUtils.containsIgnoreCase(userAgent, "http://www.google.com/bot.html"))
                || (StringUtils.containsIgnoreCase(userAgent, "bingbot/") && StringUtils.containsIgnoreCase(userAgent, "www.bing.com"))
                || StringUtils.containsIgnoreCase(userAgent, "baiduspider")
                || (StringUtils.containsIgnoreCase(userAgent, "openai.com")
                && (StringUtils.containsIgnoreCase(userAgent, "OAI-SearchBot/")
                || StringUtils.containsIgnoreCase(userAgent, "ChatGPT-User/")
                || StringUtils.containsIgnoreCase(userAgent, "GPTBot/")))
                || (StringUtils.containsIgnoreCase(userAgent, "perplexity.ai") && StringUtils.containsIgnoreCase(userAgent, "PerplexityBot/"));
    }

    // https://www.wrike.com/open.htm?id=1524211372
    public static int getUaGroupIdByUA(String userAgent) {
        if ((StringUtils.containsIgnoreCase(userAgent, "googlebot/") && StringUtils.containsIgnoreCase(userAgent, "http://www.google.com/bot.html"))
                || StringUtils.containsIgnoreCase(userAgent, "GoogleOther")
                || StringUtils.containsIgnoreCase(userAgent, "Google-Extended")) {
            if (StringUtils.containsIgnoreCase(userAgent, "mobile")) {
                return GOOGLE_MOBILE_GROUP_TYPE; // google mobile
            } else {
                return GOOGLE_DESKTOP_GROUP_TYPE; // google desktop
            }
        } else if (StringUtils.containsIgnoreCase(userAgent, "bingbot/") && StringUtils.containsIgnoreCase(userAgent, "www.bing.com")) {
            return BING_GROUP_TYPE;
        } else if (StringUtils.containsIgnoreCase(userAgent, "baiduspider")) {
            return BAIDU_GROUP_TYPE;
        } else if (StringUtils.containsIgnoreCase(userAgent, "openai.com")
                && (StringUtils.containsIgnoreCase(userAgent, "OAI-SearchBot/")
                || StringUtils.containsIgnoreCase(userAgent, "ChatGPT-User/")
                || StringUtils.containsIgnoreCase(userAgent, "GPTBot/"))) {
            return OPENAI_GROUP_TYPE;
        }  else if (StringUtils.containsIgnoreCase(userAgent, "perplexity.ai") && StringUtils.containsIgnoreCase(userAgent, "PerplexityBot/")) {
            return PERPLEXITY_GROUP_TYPE;
        } else if (StringUtils.containsIgnoreCase(userAgent, "anthropic-ai")
                || StringUtils.containsIgnoreCase(userAgent, "Claude-Web")
                || StringUtils.containsIgnoreCase(userAgent, "cohere-ai")
                || StringUtils.containsIgnoreCase(userAgent, "Diffbot")
                || StringUtils.containsIgnoreCase(userAgent, "bot")
                || StringUtils.containsIgnoreCase(userAgent, "spider")
                || StringUtils.containsIgnoreCase(userAgent, "crawler")
                || StringUtils.containsIgnoreCase(userAgent, "archive")
                || StringUtils.containsIgnoreCase(userAgent, "slurp")
                || StringUtils.containsIgnoreCase(userAgent, "fetch")) { // https://www.wrike.com/open.htm?id=1576811359
            return OTHER_GROUP_TYPE;
        } else {
            return UNKNOWN_GROUP_TYPE;
        }
    }

    @Deprecated
    public static String getSingleType(int uaGroupId) {
        if (uaGroupId == BotUtils.GOOGLE_DESKTOP_GROUP_TYPE || uaGroupId == BotUtils.GOOGLE_MOBILE_GROUP_TYPE) {
            return BotUtils.SINGLE_TYPE_GOOGLE;
        } else if (uaGroupId == BotUtils.BING_GROUP_TYPE) {
            return BotUtils.SINGLE_TYPE_BING;
        } else if (uaGroupId == BotUtils.BAIDU_GROUP_TYPE) {
            return BotUtils.SINGLE_TYPE_BAIDU;
        } else if (uaGroupId == BotUtils.YANDEX_GROUP_TYPE) {
            return BotUtils.SINGLE_TYPE_YANDEX;
        } else {
            return BotUtils.SINGLE_TYPE_UNKNOWN;
        }
    }

    @Deprecated
    public static String getAcronym(int uaGroupId) {
        if (uaGroupId == BotUtils.GOOGLE_DESKTOP_GROUP_TYPE || uaGroupId == BotUtils.GOOGLE_MOBILE_GROUP_TYPE) {
            return BotUtils.ACRONYM_TYPE_GOOGLE;
        } else if (uaGroupId == BotUtils.BING_GROUP_TYPE) {
            return BotUtils.ACRONYM_TYPE_BING;
        } else if (uaGroupId == BotUtils.BAIDU_GROUP_TYPE) {
            return BotUtils.ACRONYM_TYPE_BAIDU;
        } else if (uaGroupId == BotUtils.YANDEX_GROUP_TYPE) {
            return BotUtils.ACRONYM_TYPE_YANDEX;
        } else {
            return BotUtils.ACRONYM_TYPE_UNKNOWN;
        }
    }


}
