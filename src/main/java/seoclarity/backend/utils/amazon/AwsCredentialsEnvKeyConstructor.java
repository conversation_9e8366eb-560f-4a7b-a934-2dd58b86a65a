package seoclarity.backend.utils.amazon;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import seoclarity.backend.utils.ScStringEncryptor;

/**
 * ewain
 * 2024/05/27
 */
public class AwsCredentialsEnvKeyConstructor {

    public static final String ENV_AWS_ACCESS_KEY_SQS = "AWS_ACCESS_KEY_SQS";
    public static final String ENV_AWS_ACCESS_KEY_S3 = "AWS_ACCESS_KEY_S3";
    public static final String ENV_AWS_SECRET_KEY_SQS = "AWS_SECRET_KEY_SQS";
    public static final String ENV_AWS_SECRET_KEY_S3 = "AWS_SECRET_KEY_S3";

    public static final String ENV_SEAGATE_ACCESS_KEY_BUCKET_TEMPORARY_FILES = "SEAGATE_ACCESS_KEY_BUCKET_TEMPORARY_FILES";
    public static final String ENV_SEAGATE_SECRET_KEY_BUCKET_TEMPORARY_FILES = "SEAGATE_SECRET_KEY_BUCKET_TEMPORARY_FILES";
    public static final String ENV_SEAGATE_ACCESS_KEY_BUCKET_TEMPORARY_FILES_30 = "SEAGATE_ACCESS_KEY_BUCKET_TEMPORARY_FILES_30";
    public static final String ENV_SEAGATE_SECRET_KEY_BUCKET_TEMPORARY_FILES_30 = "SEAGATE_SECRET_KEY_BUCKET_TEMPORARY_FILES_30";
    public static final String ENV_SEAGATE_ACCESS_KEY_BUCKET_SITE_AUDIT_4XX = "SEAGATE_ACCESS_KEY_BUCKET_SITE_AUDIT_4XX";
    public static final String ENV_SEAGATE_SECRET_KEY_BUCKET_SITE_AUDIT_4XX = "SEAGATE_SECRET_KEY_BUCKET_SITE_AUDIT_4XX";

    private static Logger log = Logger.getLogger(AwsCredentialsEnvKeyConstructor.class);

    public static AwsCredentialsEnvKeyConstructor awsCredentialsEnvKeyConstructor;

    public static AwsCredentialsEnvKeyConstructor getInstance() {
        if (awsCredentialsEnvKeyConstructor == null) {
            awsCredentialsEnvKeyConstructor = new AwsCredentialsEnvKeyConstructor();
        }
        return awsCredentialsEnvKeyConstructor;
    }

    /**
     * Get plaintext SQS ACCESS KEY from environment.
     * @return
     */
    public String getPlainTextSQSAccessKey() {
        String sqsAccessKey = System.getenv(ENV_AWS_ACCESS_KEY_SQS);
        if (StringUtils.isEmpty(sqsAccessKey)) {
            throw new RuntimeException("Can not get SQS encrypted access key from environment.");
        }
        return sqsAccessKey;
    }

    /**
     * Get plaintext S3 ACCESS KEY from environment.
     * @return
     */
    public String getPlainTextS3AccessKey() {
        String s3AccessKey = System.getenv(ENV_AWS_ACCESS_KEY_S3);
        if (StringUtils.isEmpty(s3AccessKey)) {
            throw new RuntimeException("Can not get S3 encrypted access key from environment.");
        }
        return s3AccessKey;
    }

    /**
     * Get encrypted sqs access key from environment and decrypt it.
     * @return
     */
    public String getSQSDecryptedAccessKey() {
        String sqsAccessKey = System.getenv(ENV_AWS_ACCESS_KEY_SQS);
        if (StringUtils.isEmpty(sqsAccessKey)) {
            throw new RuntimeException("Can not get sqs decrypted access key from environment.");
        }
        return ScStringEncryptor.decrypt(sqsAccessKey);
    }

    /**
     * Get encrypted s3 access key from environment and decrypt it.
     * @return
     */
    public String getS3DecryptedAccessKey() {
        String s3AccessKey = System.getenv(ENV_AWS_ACCESS_KEY_S3);
        if (StringUtils.isEmpty(s3AccessKey)) {
            throw new RuntimeException("Can not get s3 decrypted access key from environment.");
        }
        return ScStringEncryptor.decrypt(s3AccessKey);
    }

    /**
     * Get encrypted sqs secret key from environment and decrypt it.
     * @return
     */
    public String getSQSDecryptedSecretKey() {
        String sqsSecretKey = System.getenv(ENV_AWS_SECRET_KEY_SQS);
        if (StringUtils.isEmpty(sqsSecretKey)) {
            throw new RuntimeException("Can not get sqs decrypted secret key from environment.");
        }
        return ScStringEncryptor.decrypt(sqsSecretKey);
    }

    /**
     * Get encrypted s3 secret key from environment and decrypt it.
     * @return
     */
    public String getS3DecryptedSecretKey() {
        String s3SecretKey = System.getenv(ENV_AWS_SECRET_KEY_S3);
        if (StringUtils.isEmpty(s3SecretKey)) {
            throw new RuntimeException("Can not get s3 decrypted secret key from environment.");
        }
        return ScStringEncryptor.decrypt(s3SecretKey);
    }

    /**
     * Get plaintext Seagate ACCESS KEY from environment.
     * @return
     */
    public String getSeagateTemporaryFilesAccessKey() {
        String seagateAccessKey = System.getenv(ENV_SEAGATE_ACCESS_KEY_BUCKET_TEMPORARY_FILES);
        if (StringUtils.isEmpty(seagateAccessKey)) {
            throw new RuntimeException("Can not get Seagate TemporaryFiles access key from environment.");
        }
        return seagateAccessKey;
    }

    public String getSeagateTemporaryFiles30AccessKey() {
        String seagateAccessKey = System.getenv(ENV_SEAGATE_ACCESS_KEY_BUCKET_TEMPORARY_FILES_30);
        if (StringUtils.isEmpty(seagateAccessKey)) {
            throw new RuntimeException("Can not get Seagate TemporaryFiles30 access key from environment.");
        }
        return seagateAccessKey;
    }

    public String getSeagateSiteAudit4xxAccessKey() {
        String seagateAccessKey = System.getenv(ENV_SEAGATE_ACCESS_KEY_BUCKET_SITE_AUDIT_4XX);
        if (StringUtils.isEmpty(seagateAccessKey)) {
            throw new RuntimeException("Can not get Seagate SiteAudit4xx access key from environment.");
        }
        return seagateAccessKey;
    }

    /**
     * Get encrypted sqs Seagate key from environment and decrypt it.
     * @return
     */
    public String getSeagateTemporaryFilesDecryptedSecretKey() {
        String seagateSecretKey = System.getenv(ENV_SEAGATE_SECRET_KEY_BUCKET_TEMPORARY_FILES);
        if (StringUtils.isEmpty(seagateSecretKey)) {
            throw new RuntimeException("Can not get Seagate TemporaryFiles secret key from environment.");
        }
        return ScStringEncryptor.decrypt(seagateSecretKey);
    }

    public String getSeagateTemporaryFiles30DecryptedSecretKey() {
        String seagateSecretKey = System.getenv(ENV_SEAGATE_SECRET_KEY_BUCKET_TEMPORARY_FILES_30);
        if (StringUtils.isEmpty(seagateSecretKey)) {
            throw new RuntimeException("Can not get Seagate TemporaryFiles30 secret key from environment.");
        }
        return ScStringEncryptor.decrypt(seagateSecretKey);
    }

    public String getSeagateSiteAudit4xxDecryptedSecretKey() {
        String seagateSecretKey = System.getenv(ENV_SEAGATE_SECRET_KEY_BUCKET_SITE_AUDIT_4XX);
        if (StringUtils.isEmpty(seagateSecretKey)) {
            throw new RuntimeException("Can not get Seagate SiteAudit4xx secret key from environment.");
        }
        return ScStringEncryptor.decrypt(seagateSecretKey);
    }

}
