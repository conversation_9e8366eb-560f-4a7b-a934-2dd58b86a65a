package seoclarity.backend.utils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 * @date 2021/5/5
 * @path com.actonia.webservice.tools.RegexFilterOptimizeUtil
 */
public class RegexFilterOptimizeUtil {
    private static final String LPARENT = "(";
    private static final String RPARENT = ")";
    private static final String singleQuote = "'";
    private static final String BLANK = " ";
    private static final List<String> LOGIC_OPERATION = Arrays.asList("and", "or", "not");

    private static final List<String> NO_TERMINAL_SET = Arrays.asList(LPARENT, singleQuote, BLANK);
    private static final List<String> TERMINAL_SET = Arrays.asList(RPARENT, singleQuote, BLANK);

    private static final Map<String, String> TERMINAL_MAP = new HashMap<>();
    private static final List<String> ALPHABET_LIST = new ArrayList<>(1);
    // private static final List<String> ALPHABET_LIST_FOR_FUNC = new ArrayList<>(Arrays.asList(new String[]{"and", "or", "like", "match", "=", "!=", "not"}));

    private static final String MATCH_SENTENCE_PATTERN_NOT_LIKE = "\\(.* not like ?'.*' ?\\)+";
    private static final String MATCH_SENTENCE_PATTERN_LIKE = "\\(.* ?like ?'%.*%' ?\\)+";
    private static final String MATCH_SENTENCE_PATTERN_NOTMATCH = "\\(*(and ?)?\\(* ?not match\\(.*,'.*'\\)\\)*\\)*";
    private static final String MATCH_SENTENCE_PATTERN2_MATCH = "\\(*(or ?)?\\(* ?(?!not )match\\(.*,'.*'\\)\\)*\\)*";
    private static final List<String> SPEC_REGEX_LIST = Arrays.asList("(", ")", ".", "*","?","[", "]","^", "$","+","|","{","}");
    static {
        for (String str : TERMINAL_SET) {
            String val = null;
            switch (str) {
                case RPARENT :
                    val = LPARENT;
                    break;
                case singleQuote :
                    val = singleQuote;
                    break;
                default:
            }
            if (val != null) {
                TERMINAL_MAP.put(str, val);
            }
        }
        ALPHABET_LIST.addAll(NO_TERMINAL_SET);
        ALPHABET_LIST.addAll(TERMINAL_SET);
    }

    public static void main(String[] args) {
        RegexFilterOptimizeUtil ins = new RegexFilterOptimizeUtil();
        // String sql = ins.processOpimize("( (NOT match(url, 'samsung.com/uk/|samsung.com/de/|samsung.com/fr/|samsung.com/es/|samsung.com/it/|samsung.com/nl/|samsung.com/be/|samsung.com/be_fr/|samsung.com/se/|samsung.com/no/|samsung.com/fi/|samsung.com/dk/|samsung.com/at/|samsung.com/ee/|samsung.com/lv/|samsung.com/lt/|samsung.com/cz/|samsung.com/sk/|samsung.com/pl/|samsung.com/ro/'))  AND (NOT match(decodeURLComponent(url), 'samsung.com/uk/|samsung.com/de/|samsung.com/fr/|samsung.com/es/|samsung.com/it/|samsung.com/nl/|samsung.com/be/|samsung.com/be_fr/|samsung.com/se/|samsung.com/no/|samsung.com/fi/|samsung.com/dk/|samsung.com/at/|samsung.com/ee/|samsung.com/lv/|samsung.com/lt/|samsung.com/cz/|samsung.com/sk/|samsung.com/pl/|samsung.com/ro/')) )");
        // ins.processOpimize("     match(decodeURLComponent(url), 'samsung.com/al/|samsung.com/hr/|samsung.com/rs/|samsung.com/si/|samsung.com/ba/|samsung.com/mk/')      OR match(decodeURLComponent(url), 'samsung.com/at/|samsung.com/ch/|samsung.com/ch_fr/')      OR match(decodeURLComponent(url), 'samsung.com/ee/|samsung.com/lv/|samsung.com/lt/')      OR match(decodeURLComponent(url), 'samsung.com/nl/|samsung.com/be/|samsung.com/be_fr/')      OR match(decodeURLComponent(url), 'samsung.com/cz/|samsung.com/sk/')      OR match(decodeURLComponent(url), 'samsung.com/es/|samsung.com/pt/')      OR match(decodeURLComponent(url), 'samsung.com/se/|samsung.com/no/|samsung.com/dk/|samsung.com/fi/')      OR match(decodeURLComponent(url), 'samsung.com/bg/|samsung.com/ro/')      OR match(decodeURLComponent(url), 'samsung.com/uk/|samsung.com/ie/')      OR (lower(decodeURLComponent(url)) LIKE '%samsung.com/fr/%')      OR (lower(decodeURLComponent(url)) LIKE '%samsung.com/de/%')      OR (lower(decodeURLComponent(url)) LIKE '%samsung.com/gr/%')      OR (lower(decodeURLComponent(url)) LIKE '%samsung.com/hu/%')      OR (lower(decodeURLComponent(url)) LIKE '%samsung.com/it/%')      OR (lower(decodeURLComponent(url)) LIKE '%samsung.com/pl/%')      OR match(decodeURLComponent(url), 'samsung.com/al/|samsung.com/hr/|samsung.com/rs/|samsung.com/si/') ");
        // String sql = ins.processOpimize("match(decodeURLComponent(url), 'samsung.com/al/|samsung.com/hr/|samsung.com/rs/|samsung.com/si/|samsung.com/ba/|samsung.com/mk/')      OR match(decodeURLComponent(url), 'samsung.com/at/|samsung.com/ch/|samsung.com/ch_fr/')      OR match(decodeURLComponent(url), 'samsung.com/ee/|samsung.com/lv/|samsung.com/lt/')      OR match(decodeURLComponent(url), 'samsung.com/nl/|samsung.com/be/|samsung.com/be_fr/')      OR match(decodeURLComponent(url), 'samsung.com/cz/|samsung.com/sk/')      OR (match(decodeURLComponent(url), 'samsung.com/es/|samsung.com/pt/')      AND match(decodeURLComponent(url), 'samsung.com/se/|samsung.com/no/|samsung.com/dk/|samsung.com/fi/')      AND match(decodeURLComponent(url), 'samsung.com/bg/|samsung.com/ro/')      AND match(decodeURLComponent(url), 'samsung.com/uk/|samsung.com/ie/'))     OR (lower(decodeURLComponent(url)) LIKE '%samsung.com/fr/%')      OR (lower(decodeURLComponent(url)) LIKE '%samsung.com/de/%')      OR ((lower(decodeURLComponent(url)) LIKE '%samsung.com/gr/%')      OR (lower(decodeURLComponent(url)) LIKE '%samsung.com/hu/%')      OR (lower(decodeURLComponent(url)) LIKE '%samsung.com/it/%')      OR (lower(decodeURLComponent(url)) LIKE '%samsung.com/pl/%'))     OR match(decodeURLComponent(url), 'samsung.com/al/|samsung.com/hr/|samsung.com/rs/|samsung.com/si/')");
        // String sql = ins.processOpimize("AND (((url not like '%a%') and (url not like '%b%')) and ((url not like '%c%') and (url not like '%d%')))");
        // String sql = ins.processOpimize(" match(decodeURLComponent(url), 'samsung.com/al/|samsung.com/hr/|samsung.com/rs/|samsung.com/si/|samsung.com/ba/|samsung.com/mk/')   OR match(decodeURLComponent(url), 'samsung.com/at/|samsung.com/ch/|samsung.com/ch_fr/')    OR (     match(decodeURLComponent(url), 'samsung.com/ee/|samsung.com/lv/|samsung.com/lt/')       OR match(decodeURLComponent(url), 'samsung.com/nl/|samsung.com/be/|samsung.com/be_fr/')       OR match(decodeURLComponent(url), 'samsung.com/cz/|samsung.com/sk/')  )    OR (     match(decodeURLComponent(url), 'samsung.com/es/|samsung.com/pt/')       AND match(decodeURLComponent(url), 'samsung.com/se/|samsung.com/no/|samsung.com/dk/|samsung.com/fi/')       AND match(decodeURLComponent(url), 'samsung.com/bg/|samsung.com/ro/')       AND match(decodeURLComponent(url), 'samsung.com/uk/|samsung.com/ie/')  )   OR (lower(decodeURLComponent(url)) LIKE '%samsung.com/fr/%')    OR (lower(decodeURLComponent(url)) LIKE '%samsung.com/de/%')    OR (     (lower(decodeURLComponent(url)) LIKE '%samsung.com/gr/%')       OR (lower(decodeURLComponent(url)) LIKE '%samsung.com/hu/%')       OR (lower(decodeURLComponent(url)) LIKE '%samsung.com/it/%')       OR (lower(decodeURLComponent(url)) LIKE '%samsung.com/pl/%')  )   OR match(decodeURLComponent(url), 'samsung.com/al/|samsung.com/hr/|samsung.com/rs/|samsung.com/si/')");
        // String sql = ins.processOpimize("(lower(url) like '%samsung.com/pl/%') or (lower(decodeurlcomponent(url)) like '%samsung.com/pl/%')");
        // String sql = ins.processOpimize("((lower(url)like'%/home)%') OR (lower(decodeURLComponent(url))like'%/home)%'))");
        // String sql = ins.processOpimize("AND (   (  (not match(url, 'samsung.com/uk/|samsung.com/ie/|samsung.com/de/|samsung.com/fr/|samsung.com/es/|samsung.com/pt/|samsung.com/it/|samsung.com/nl/|samsung.com/be/|samsung.com/be_fr/|samsung.com/se/|samsung.com/no/|samsung.com/fi/|samsung.com/dk/'))  AND  (not match(decodeURLComponent(url), 'samsung.com/uk/|samsung.com/ie/|samsung.com/de/|samsung.com/fr/|samsung.com/es/|samsung.com/pt/|samsung.com/it/|samsung.com/nl/|samsung.com/be/|samsung.com/be_fr/|samsung.com/se/|samsung.com/no/|samsung.com/fi/|samsung.com/dk/'))  )  OR   (  (not match(url, 'samsung.com/uk/|samsung.com/de/|samsung.com/fr/|samsung.com/es/|samsung.com/it/|samsung.com/nl/|samsung.com/be/|samsung.com/be_fr/|samsung.com/se/|samsung.com/no/|samsung.com/fi/|samsung.com/dk/|samsung.com/at/|samsung.com/ee/|samsung.com/lv/|samsung.com/lt/|samsung.com/cz/|samsung.com/sk/|samsung.com/pl/|samsung.com/ro/'))  AND  (not match(decodeURLComponent(url), 'samsung.com/uk/|samsung.com/de/|samsung.com/fr/|samsung.com/es/|samsung.com/it/|samsung.com/nl/|samsung.com/be/|samsung.com/be_fr/|samsung.com/se/|samsung.com/no/|samsung.com/fi/|samsung.com/dk/|samsung.com/at/|samsung.com/ee/|samsung.com/lv/|samsung.com/lt/|samsung.com/cz/|samsung.com/sk/|samsung.com/pl/|samsung.com/ro/'))  )  ) ");
        // String sql = ins.processOpimize("AND (   (   (  (lower(url) like '%/home%')  OR  (lower(decodeURLComponent(url)) like '%/home%')  )  )  OR   (  (lower(url) like '%rank%')  OR  (lower(decodeURLComponent(url)) like '%rank%')  )  )");
        // String sql = ins.processOpimize("AND (    (     (  (lower(url) = 'https://zoom.us/voip-service')  OR  (lower(decodeURLComponent(url)) = 'https://zoom.us/voip-service')  )        or  (         (  (lower(url) like '%business-phone-systems%')  OR  (lower(decodeURLComponent(url)) like '%business-phone-systems%')  )            or  (  (lower(url) like '%affordable-business-voip%')  OR  (lower(decodeURLComponent(url)) like '%affordable-business-voip%')  )            or  (  (lower(url) like '%multi-line-phone-system%')  OR  (lower(decodeURLComponent(url)) like '%multi-line-phone-system%')  )            or  (  (lower(url) like '%fixed-voip%')  OR  (lower(decodeURLComponent(url)) like '%fixed-voip%')  )       )        or  (         (  (lower(url) like '%voip-small-business%')  OR  (lower(decodeURLComponent(url)) like '%voip-small-business%')  )            or  (  (lower(url) like '%virtual-phone-number%')  OR  (lower(decodeURLComponent(url)) like '%virtual-phone-number%')  )            or  (  (lower(url) like '%virtual-phone%')  OR  (lower(decodeURLComponent(url)) like '%virtual-phone%')  )            or  (  (lower(url) like '%voip-number%')  OR  (lower(decodeURLComponent(url)) like '%voip-number%')  )            or  (  (lower(url) like '%voip-service-providers%')  OR  (lower(decodeURLComponent(url)) like '%voip-service-providers%')  )       )        or  (         (  (lower(url) like '%what-is-voip-phone%')  OR  (lower(decodeURLComponent(url)) like '%what-is-voip-phone%')  )            or  (  (lower(url) like '%enterprise-phone-systems%')  OR  (lower(decodeURLComponent(url)) like '%enterprise-phone-systems%')  )            or  (  (lower(url) like '%voip-providers%')  OR  (lower(decodeURLComponent(url)) like '%voip-providers%')  )            or  (  (lower(url) like '%small-business-phone-service%')  OR  (lower(decodeURLComponent(url)) like '%small-business-phone-service%')  )            or  (  (lower(url) like '%internet-phone-service%')  OR  (lower(decodeURLComponent(url)) like '%internet-phone-service%')  )       )      )   ) ");
        String sql = ins.processOpimize("AND (          (         (  (lower(url) like '%business-phone-systems%')  OR  (lower(decodeURLComponent(url)) like '%business-phone-systems%')  )            or  (  (lower(url) like '%affordable-business-voip%')  OR  (lower(decodeURLComponent(url)) like '%affordable-business-voip%')  )            or  (  (lower(url) like '%multi-line-phone-system%')  OR  (lower(decodeURLComponent(url)) like '%multi-line-phone-system%')  )            or  (  (lower(url) like '%fixed-voip%')  OR  (lower(decodeURLComponent(url)) like '%fixed-voip%')  )       )        or  (         (  (lower(url) like '%voip-small-business%')  OR  (lower(decodeURLComponent(url)) like '%voip-small-business%')  )            or  (  (lower(url) like '%virtual-phone-number%')  OR  (lower(decodeURLComponent(url)) like '%virtual-phone-number%')  )            or  (  (lower(url) like '%virtual-phone%')  OR  (lower(decodeURLComponent(url)) like '%virtual-phone%')  )            or  (  (lower(url) like '%voip-number%')  OR  (lower(decodeURLComponent(url)) like '%voip-number%')  )            or  (  (lower(url) like '%voip-service-providers%')  OR  (lower(decodeURLComponent(url)) like '%voip-service-providers%')  )       )        or  (         (  (lower(url) like '%what-is-voip-phone%')  OR  (lower(decodeURLComponent(url)) like '%what-is-voip-phone%')  )            or  (  (lower(url) like '%enterprise-phone-systems%')  OR  (lower(decodeURLComponent(url)) like '%enterprise-phone-systems%')  )            or  (  (lower(url) like '%voip-providers%')  OR  (lower(decodeURLComponent(url)) like '%voip-providers%')  )            or  (  (lower(url) like '%small-business-phone-service%')  OR  (lower(decodeURLComponent(url)) like '%small-business-phone-service%')  )            or  (  (lower(url) like '%internet-phone-service%')  OR  (lower(decodeURLComponent(url)) like '%internet-phone-service%')  )       )         ) ");
        System.out.println("===sql:");
        System.out.println(sql);
    }

    /**
     * @param filter  example: (expression1 and expression2) or expression3 or expression4...
     */
    public String processOpimize(String filter) {
        System.out.println("============start to optimize sql:" + filter);
        String str = filter.trim();try {
            if (StringUtils.startsWithIgnoreCase(str, "and")) {
                str = StringUtils.removeStartIgnoreCase(str, "and");
                str = str.trim();
                str = StringUtils.removeStart(str, "(");
                str = StringUtils.removeEnd(str, ")");
                str = str.trim();
            }
            List<Token> tokenStack = new ArrayList<>();

            for (int i = 0; i < str.length(); i++) {
                Token token = getToken(str, i);
                // token.print();
                tokenStack.add(token);
                if (token == null) {
                    break;
                } else {
                    if (token.getToken().equals(singleQuote)) {
                        Token strToken = getStringToken(str, token);
                        tokenStack.add(strToken);
                        // get next quote
                        token = getToken(str, strToken.getIdx() + 1);
                        tokenStack.add(token);
                    }
                    i = token.getIdx();
                }
            }
            // merge "not" "match" -> "not match"
            for (int i = 0; i < tokenStack.size(); i++) {
                Token curr = tokenStack.get(i);
                if (curr.getToken().equalsIgnoreCase("match") && i > 0) {
                    Token prev = tokenStack.get(i - 1);
                    if (prev.getToken().equalsIgnoreCase("not")) {
                        prev.setToken(prev.getToken() + " " + curr.getToken());
                        tokenStack.remove(i);
                        i--;
                    }
                }
            }
            // tokenStack.stream().forEach(x -> x.print());
            List<List<Token>> sentenceList = getSentence(tokenStack);

            System.out.println("====================");
            String optimizedSql = optimizeSentenceToSql(sentenceList, filter);
            System.out.println("===origin sql:" + filter);
            System.out.println("===optimized sql:" + optimizedSql);
            return optimizedSql;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return filter;
    }
    /**
     *  process like '"((a like b) and/or (c like d))"
     *  :( ( match ( url, ' samsung.com/bg/|samsung.com/ro/ ' ) ) OR ( match ( decodeURLComponent ( url ) , ' samsung.com/bg/|samsung.com/ro/ ' ) ) )
     *  */
    private List<List<Token>> splitSentence(List<Token> sentence) {
        String tempSql = parseSentenceToSql(sentence);
        if (!StringUtils.containsIgnoreCase(tempSql, " or ") && !StringUtils.containsIgnoreCase(tempSql, " and ")) {
            List<List<Token>> list = new ArrayList<>();
            list.add(sentence);
            return list;
        } else if (sentence.get(0).getToken().equals(LPARENT) && sentence.get(sentence.size() - 1).getToken().equals(RPARENT)) {
            // TODO
            sentence.remove(0);
            sentence.remove(sentence.size() - 1);
            return getSentence(sentence);
        }
        return Collections.singletonList(sentence);
    }

    private String optimizeSentenceToSql(List<List<Token>>  sentenceList, String originFilter) {
        List<List<Token>> skipSentenceList = new ArrayList<>();
        List<List<Token>> notMatchList = new ArrayList<>();
        List<List<Token>> matchList = new ArrayList<>();
        List<List<Token>> likeList = new ArrayList<>();
        StringBuffer sql = new StringBuffer();

        for (List<Token> sentence : sentenceList) {
            if (StringUtils.equalsIgnoreCase("or", sentence.get(0).getToken())) {
                sentence.remove(0);
            } else if (StringUtils.equalsIgnoreCase("and", sentence.get(0).getToken())) {
                skipSentenceList.add(sentence);
                String tempSql = parseSentenceToSql(sentence).toLowerCase();
                System.out.println("=skip and sentence:" + tempSql);
                continue;
            }
            String tempSql = parseSentenceToSql(sentence).toLowerCase();
            if (tempSql.matches(MATCH_SENTENCE_PATTERN_NOT_LIKE)) {
                skipSentenceList.add(sentence);
                System.out.println("=skip not like sentence:" + tempSql);
            } else if (tempSql.matches(MATCH_SENTENCE_PATTERN_LIKE) && !tempSql.contains(" and ") && !tempSql.matches(".*(\\=|\\!|has\\().*")) {
                // xxx like '%....%
                likeList.add(sentence);
            } else if (tempSql.matches(MATCH_SENTENCE_PATTERN_NOTMATCH) && !tempSql.contains(" or ") && !tempSql.contains(" and ") && !tempSql.matches(".*(\\=|\\!|has\\().*")){
                // "not match(...)"
                notMatchList.add(sentence);
            } else if (tempSql.matches(MATCH_SENTENCE_PATTERN2_MATCH) && !tempSql.contains(" and ") && !tempSql.matches(".*(\\=|\\!|has\\().*")) {
                // "match(...)"
                matchList.add(sentence);
            } else {
                skipSentenceList.add(sentence);
                System.out.println("=skip other sentence:" + tempSql);
            }
        }

        if (notMatchList.size() == 0 && matchList.size() == 0 && likeList.size() == 0) {
            return originFilter;
        }
        try {
            if (likeList.size() > 0 || matchList.size() > 0) {
                Map<String, List<String>> map = new LinkedHashMap<>();
                Map<String, List<String>> likeMap = mergeLikeSubSentence(likeList, skipSentenceList);
                Map<String, List<String>> matchMap = mergeSubSentenceToSql(matchList, "match", skipSentenceList);
                if (likeMap != null && likeMap.size() > 0) {
                    map.putAll(likeMap);
                }
                if (matchMap.size() > 0) {
                    map.putAll(matchMap);
                }
                String str = mergeSubSentenceToSql(map, "OR", "match");
                if (StringUtils.isNotBlank(str)) {
                    sql.append(str);
                }
            }
            if (notMatchList.size() > 0) {
                Map<String, List<String>> mergeMap = mergeSubSentenceToSql(notMatchList, "not match", skipSentenceList);
                String str = mergeSubSentenceToSql(mergeMap, "AND", "not match");
                if (StringUtils.isNotBlank(str)) {
                    sql.append(str);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (StringUtils.isBlank(sql.toString())) {
            return originFilter;
        } else {
            skipSentenceList.forEach(sentence -> {
                String str = parseSentenceToSql(sentence);
                sql.append(" OR (").append(str).append(") ");
            });
        }
        return " AND (" + StringUtils.removeStartIgnoreCase(sql.toString().trim(), "OR ") + " ) ";
    }

    /**
     * xxx like '%.abc% convert to match(xxx, 'abc')
     */
    private Map<String, List<String>> mergeLikeSubSentence(List<List<Token>> sentenceList, List<List<Token>> failedSentenceList) {
        if (sentenceList == null || sentenceList.size() == 0) {
            return null;
        }
        Map<String, List<String>> columnPatternMap = new LinkedHashMap<>();
        for (List<Token> sentence : sentenceList) {
            try {
                List<List<Token>> subSentenceList = splitSentence(sentence);
                System.out.println("------------type: like----------------------");
                System.out.println("origin:");
                printSentence(sentence);
                System.out.println("split:");
                subSentenceList.forEach(this::printSentence);
                if (subSentenceList.size() == 1) {
                    String tempSql = parseSentenceToSql(subSentenceList.get(0));
                    if (StringUtils.containsIgnoreCase(tempSql, " and ") || StringUtils.containsIgnoreCase(tempSql, " or ")) {
                        System.out.println("===split failed. sentence:" + parseSentenceToSql(sentence));
                        failedSentenceList.add(sentence);
                        continue;
                    }
                }
                System.out.println("---------------------------------------------");
                for(List<Token> subSentence : subSentenceList) {
                    if (StringUtils.equalsIgnoreCase(subSentence.get(0).getToken(), "and") || StringUtils.equalsIgnoreCase(subSentence.get(0).getToken(), "or")) {
                        subSentence.remove(0);
                    }
                    if (subSentence.get(0).getToken().equals(LPARENT) && subSentence.get(subSentence.size() - 1).getToken().equals(RPARENT)) {
                        subSentence.remove(0);
                        subSentence.remove(subSentence.size() - 1);
                    }
                    String originSql = parseSentenceToSql(subSentence);
                    List<Token> column = new ArrayList<>();
                    int i = 0;
                    // get columnName
                    for (; i < subSentence.size() - 1; i++) {
                        Token curr = subSentence.get(i);
                        if (StringUtils.equalsIgnoreCase(curr.getToken(), "like")) {
                            break;
                        } else {
                            column.add(curr);
                        }
                    }
                    // TODO
                    String columnName = parseSentenceToSql(column);
                    // get column faild.
                    // it means column contains more than 2 sentence
                    // this should be split
                    if (LOGIC_OPERATION.stream().filter(x -> StringUtils.contains(columnName, x.toUpperCase())).collect(Collectors.toList()).size() > 0) {
                        throw new Exception("column parse failed. column:" + columnName);
                        // failedSentenceList.add(sentence);
                        // break;
                    }
                    // get pattern
                    String pattern = "";
                    for (; i < subSentence.size() - 1; i++) {
                        Token curr = subSentence.get(i);
                        if (curr.getToken().equals("'")) {
                            pattern = subSentence.get(i + 1).getToken();
                            break;
                        }
                    }
                    // check pattern
                    // skip pattern like "%abc%defg%"
                    pattern = StringUtils.removeStart(pattern, "%");
                    pattern = StringUtils.removeEnd(pattern, "%");
                    if (StringUtils.contains(pattern, "%")) {
                        failedSentenceList.add(sentence);
                        break;
                    }
                    for (String str : SPEC_REGEX_LIST) {
                        if (StringUtils.contains(pattern, str)) {
                            pattern = StringUtils.replace(pattern, str, "\\\\" + str);
                        }
                    }
                    if (columnPatternMap.containsKey(columnName)) {
                        columnPatternMap.get(columnName).add(pattern);
                    } else {
                        List<String> list = new ArrayList<>();
                        list.add(pattern);
                        columnPatternMap.put(columnName, list);
                    }
                    System.out.println("===convert like sql,  originSql:" + originSql + ", columnName:" + columnName + ", pattern:" + pattern);
                }
            } catch (Exception e) {
                e.printStackTrace();
                failedSentenceList.add(sentence);
            }
        }
        return columnPatternMap;
    }

    // private static final Map<String, String> replacementMap = new LinkedHashMap<>();
    // static {
    //     replacementMap.put("lower(decodeURLComponent(url))", "decodeURLComponent(url)");
    //     replacementMap.put("lower(url)", "url");
    // }
    private String mergeSubSentenceToSql(Map<String, List<String>> columnPatternMap, String relationTokenStr, String matchToken) {
        StringBuffer sql = new StringBuffer();
        StringBuffer subSentenceSql = new StringBuffer();
        for(String key : columnPatternMap.keySet()) {
            List<String> patternList = columnPatternMap.get(key);
            String columnName = StringUtils.endsWith(key.trim(), ",") ? key : key + ", ";
            // TODO for samsung
            // for (String col : replacementMap.keySet()){
            //     if (StringUtils.contains(columnName, col)) {
            //         columnName = StringUtils.replace(columnName, col, replacementMap.get(col));
            //         break;
            //     }
            // };
            if ((columnName.contains("lower(url)") && !columnName.contains("lower(decodeURLComponent(url))")) || (columnName.contains("url") && !columnName.contains("decodeURLComponent(url)"))) {
                System.out.println("=====skip query, column:" + columnName + ", pattern:" + columnPatternMap.get(key));
                continue;
            }
            subSentenceSql.append(relationTokenStr).append(" (").append(matchToken).append("(").append(columnName).append("'(" + StringUtils.join(patternList, ")|(") + ")'").append("))");
        }

        if (StringUtils.isNotBlank(subSentenceSql.toString().trim())) {
            sql.append(" OR ( ");
            sql.append(StringUtils.removeStartIgnoreCase(subSentenceSql.toString().trim(), relationTokenStr).trim());
            sql.append(" )");
        }
        return sql.toString();
    }

    private Map<String, List<String>> mergeSubSentenceToSql(List<List<Token>> sentenceList, String matchToken, List<List<Token>> failedSentenceList) throws Exception{
        // {column : [pattern1, pattern2, ...]}
        Map<String, List<String>> columnPatternMap = new LinkedHashMap<>();
        // 1. get the func name, exp: match/not match
        // 2. get the filter column name, exp: url/decodeURLComponent ( lower ( url ) )
        // 3. get the regex pattern, exp: '............'
        for (List<Token> sentence : sentenceList) {
            try {
                List<List<Token>> subSentenceList = splitSentence(sentence);

                System.out.println("------------type:" + matchToken + "----------------------");
                System.out.println("origin:");
                printSentence(sentence);
                System.out.println("split:");
                subSentenceList.forEach(this::printSentence);
                System.out.println("--------------------------------------------------------------");

                for (List<Token>subSentence : subSentenceList) {
                    if (StringUtils.equalsIgnoreCase(subSentence.get(0).getToken(), "and") || StringUtils.equalsIgnoreCase(subSentence.get(0).getToken(), "or")) {
                        subSentence.remove(0);
                    }
                    if (subSentence.get(0).getToken().equals(LPARENT) && subSentence.get(subSentence.size() - 1).getToken().equals(RPARENT)) {
                        subSentence.remove(0);
                        subSentence.remove(subSentence.size() - 1);
                    }
                    for (int i = 0; i < subSentence.size(); i++) {
                        Token curr = subSentence.get(i);
                        // 1. get the func name, exp: match/not match
                        if (StringUtils.equalsIgnoreCase(curr.getToken(), matchToken)) {
                            // 2. get the filter column name, exp: url/decodeURLComponent ( lower ( url ) )
                            int j = i + 2;
                            List<Token> column = new ArrayList<>();
                            for (; j < subSentence.size(); j++) {
                                Token x = subSentence.get(j);
                                if (!x.getToken().equals("'")) {
                                    column.add(x);
                                } else {
                                    break;
                                }
                            }
                            // TODO
                            String columnName = parseSentenceToSql(column);
                            // get column faild.
                            // it means column contains more than 2 sentence
                            // this should be split
                            if (LOGIC_OPERATION.stream().filter(x -> StringUtils.contains(columnName, x.toUpperCase())).collect(Collectors.toList()).size() > 0) {
                                throw new Exception("column parse failed. column:" + columnName);
                            }
                            // 3. get the regex pattern, exp: '............'
                            String pattern = "";
                            for (; j < subSentence.size(); j++) {
                                Token x = subSentence.get(j);
                                if (x.getToken().equals("'")) {
                                    pattern = subSentence.get(j + 1).getToken();
                                    break;
                                }
                            }
                            if (columnPatternMap.containsKey(columnName)) {
                                columnPatternMap.get(columnName).add(pattern);
                            } else {
                                List<String> list = new ArrayList<>();
                                list.add(pattern);
                                columnPatternMap.put(columnName, list);
                            }
                            break;
                        }
                    }
                };
          } catch (Exception e) {
                System.out.println("===Failed to merge sentence:");
                printSentence(sentence);
                throw new Exception(e);
            }
        }
        // System.out.println("===merged sql:" + sql.toString());
        return columnPatternMap;
    }

    private String parseSentenceListToSql(List<List<Token>> sentenceList) {
        StringBuffer sql = new StringBuffer();
        sentenceList.forEach(x ->
            sql.append(parseSentenceToSql(x))
        );
        if (StringUtils.startsWithIgnoreCase(sql.toString().trim(), "or")) {
            return StringUtils.removeStartIgnoreCase(sql.toString().trim(), "or").trim();
        }
        return sql.toString();
    }

    private String parseSentenceToSql(List<Token> sentence) {
        StringBuffer sql = new StringBuffer();
        sentence.forEach(y -> {
            boolean isLogicOperation = LOGIC_OPERATION.contains(y.getToken().toLowerCase());
            sql.append(isLogicOperation ? " " : "").append(y.getToken()).append(isLogicOperation ? " " : "");
        });
        if (StringUtils.startsWithIgnoreCase(sql.toString().trim(), "or")) {
            return StringUtils.removeStartIgnoreCase(sql.toString().trim(), "or").trim();
        }
        return sql.toString();
    }

    private List<List<Token>> getSentence(List<Token> stack) {
        List<Token> sentenceStack = new ArrayList<>(1);
        List<List<Token>> sentenceList = new ArrayList<>();
        for (int i = 0; i < stack.size(); i++) {
            Token token = stack.get(i);
            if (NO_TERMINAL_SET.contains(token.getToken())) {
                sentenceStack.add(token);
            } else if (TERMINAL_MAP.containsKey(token.getToken())) {
                List<Token> sentence = pop(sentenceStack, token);
                Collections.reverse(sentence);
                // when made current sentence, need check prev sentence
                // stack is not empty, it means current sentence is not finished
                if (sentenceList.size() > 0) {
                    mergeSentence(sentenceList, sentence);
                }
                // printSentence(sentence);
                sentenceList.add(sentence);
            } else {
                sentenceStack.add(token);
            }
        }

        // sentenceList.stream().forEach(x -> printSentence(x));
        return sentenceList;
    }

    /**
     *  analyze the current sentence
     */
    private void mergeSentence(List<List<Token>> sentenceList, List<Token> currentSentence, Integer... processIdx) {
        int currentEndIdx =  (processIdx == null || processIdx.length == 0) ? currentSentence.size() - 1 : processIdx[0];
        for (int i = currentEndIdx; i >= 0; i--) {
            Token curr = currentSentence.get(i);
            if (curr.getToken().equals(RPARENT)) {
                Token prev = currentSentence.get(i - 1);
                //      [or/and/(]      )
                switch (prev.getToken().toLowerCase()) {
                    case LPARENT:
                    case "or" :
                    case "and" :
                        currentSentence.addAll(i, sentenceList.remove(sentenceList.size() - 1));
                        break;
                    default:
                }
            } else if (curr.getToken().equals(LPARENT)) {
                int currentSentenceCnt = currentSentence.size();
                Token next = currentSentence.get(i + 1);
                    switch (next.getToken().toLowerCase()) {
                        case "or":
                        case "and":
                        case "like":
                        case "not":
                        case "=":
                        case "!=":
                        case "<>":
                            if (sentenceList.size() > 0) {
                                currentSentence.addAll(i + 1, sentenceList.remove(sentenceList.size() - 1));
                            }
                            break;
                        default:
                    }
                if(i > 0) {
                    Token prev = currentSentence.get(i - 1);
                    if ((prev.getToken().equalsIgnoreCase("match") || prev.getToken().equalsIgnoreCase("not match")) && next.getToken().startsWith(",")) {
                        currentSentence.addAll(i + 1, sentenceList.remove(sentenceList.size() - 1));
                    }
                }
                // means the current sentence has been merged
                // need check again
                if (currentSentenceCnt != currentSentence.size()) {
                    mergeSentence(sentenceList, currentSentence, i);
                }
            }
        }
    }

    private void printSentence(List<Token> list) {
        StringBuilder str = new StringBuilder();
        list.forEach(x -> str.append(x.getToken()).append(" "));
        System.out.println("===sentence:" + str.toString());
    }

    private List<Token> pop(List<Token> sentenceStack, Token terminalToken) {
        String no_terminal_char = terminalToken != null ? TERMINAL_MAP.get(terminalToken.getToken()) : null;
        List<Token> sentence = new ArrayList<>();
        if (terminalToken != null) {
            sentence.add(terminalToken);
        }
        int j = sentenceStack.size() - 1;
        while(true) {
            if (no_terminal_char != null) {
                Token temp = sentenceStack.remove(j);
                j--;
                sentence.add(temp);
                if (no_terminal_char.equalsIgnoreCase(temp.getToken())) {
                    break;
                }
            } else if (sentenceStack.size() > 0){
                Token temp = sentenceStack.get(sentenceStack.size() - 1);
                if (!ALPHABET_LIST.contains(temp.getToken())) {
                    sentenceStack.remove(temp);
                    sentence.add(temp);
                } else {
                    break;
                }
            } else {
                break;
            }
        }
        if (sentenceStack.size() > 0 && terminalToken != null) {
            List<Token> prevList = pop(sentenceStack, null);
            if (prevList.size() == 0) {
                return sentence;
            } else {
                sentence.addAll(prevList);
            }
        }
        return sentence;
    }

    private Token getToken(String str, int idx) {
        if (idx == str.length()) {
            return null;
        }
        String token = "";
        for (int i = idx; i < str.length(); i++) {
            String c = String.valueOf(str.charAt(i));
            if (StringUtils.isBlank(token) && ALPHABET_LIST.contains(c)) {
                if (c.equals(" ")) {
                    continue;
                }
                return new Token(c, i);
            } else if (StringUtils.isNotBlank(token) && ALPHABET_LIST.contains(c)){
                return new Token(token, i - 1);
            }  else if (!ALPHABET_LIST.contains(c)){
                token += c;
            }
        }
        return null;
    }

    private Token getStringToken(String str, Token prevToken) {
        String token = "";
        if (prevToken.getToken().equals(singleQuote)) {
            String preBackSlash = "";
            for (int i = prevToken.getIdx() + 1; i < str.length(); i++) {
                String c = String.valueOf(str.charAt(i));
                token += c;
                if (c.equals("\\")) {
                    preBackSlash += c;
                } else if (c.equals(singleQuote)) {
                    if (preBackSlash.length() % 2 == 0) {
                        token = token.substring(0, token.length() - 1);
                        return new Token(token, i - 1);
                    } else {
                        preBackSlash = "";
                    }
                } else {
                    preBackSlash = "";
                }
            }
        }
        return null;
    }

    class Token{
        String token;
        int idx;
        public Token(String token, int idx) {
            this.token = token;
            this.idx = idx;
        }
        public String getToken() {
            return token;
        }
        public void setToken(String token) {
            this.token = token;
        }
        public int getIdx() {
            return idx;
        }
        public void setIdx(int idx) {
            this.idx = idx;
        }
        public void print() {
            System.out.println("token:" + this.token + ", idx:" + idx);
        }
    }
}
