package seoclarity.backend.utils;

import com.google.api.gax.paging.Page;
import com.google.auth.Credentials;
import com.google.auth.oauth2.ServiceAccountCredentials;
import com.google.cloud.WriteChannel;
import com.google.cloud.storage.*;
import com.google.gson.Gson;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;

import java.io.*;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Google Cloud Storage utils
 */
@CommonsLog
public class GCSUtils {


    public static void main(String[] args) throws Exception {
        String path = "";
        if (args.length > 0 && StringUtils.isNotBlank(args[0])) {
            System.out.println("path:" + args[0]);
            path = args[0];
        }
        System.out.println("=====check file exist on server begin :");
        String paramJson = "{\"type\":\"share\",\"iamUser\":\"<EMAIL>\",\"credentialsFilePath\":\"seoclarity.net_seoclarity-f842f0ae9df4.json\",\"credentialsFileContent\":\"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\",\"bucket\":\"wmt-seoclarity-dev-api\",\"path\":\"/\"}";
        //getFileList(paramJson,"wmt-seoclarity-dev-api");
        listFileByParamJson(paramJson,path);

        //boolean isExist  = getFileByParamJson(paramJson,"test/researchGrid_keywordDetail_task_id_79c3ed8e65e38ae11f37eab4dd922227_20240119061955.csv.gz");
        //System.out.println("check result :" + isExist);
        //        System.out.println("=====check file exist on server end.");
//        String objectName = "seoclarityTestBigFile_0108.txt";
//        String objectName = "seoclarityTest";
////        String localFilePath = "/home/<USER>/seoclarityTest_0108.txt";
//        String localFilePath = "/home/<USER>/seoclarityTestSmallFile_0108.txt";
////        putFileByParamJson(paramJson, localFilePath, objectName);
////        getFileByParamJson(paramJson, objectName);
//        listFileByParamJson(paramJson, objectName);
    }

    public static String putFileByParamJson(String paramJson, String localFilePath, String gscFilePath) throws Exception {
        System.out.println("=====putFileByParamJson started");
        //throw new RuntimeException("paramJson is null ,please check.");
        if(StringUtils.isBlank(paramJson)){
            throw new RuntimeException("paramJson is null ,please check.");
        }
        if(StringUtils.isBlank(localFilePath)){
            throw new RuntimeException("localFilePath is null ,please check.");
        }
        if(StringUtils.isBlank(gscFilePath)){
            throw new RuntimeException("gscFilePath is null ,please check.");
        }

        Gson gson = new Gson();
        Map<String, String> gcsInfo = gson.fromJson(paramJson, Map.class);
        String credentialsFileContent = gcsInfo.get("credentialsFileContent");
        if(StringUtils.isBlank(credentialsFileContent)){
            throw new RuntimeException("credentialsFileContent is null ,please check.");
        }
        String bucketName = gcsInfo.get("bucket");
        if(StringUtils.isBlank(bucketName)){
            throw new RuntimeException("bucket is null ,please check.");
        }
        String path = gcsInfo.get("path");
        File jsonKeyFile = getJsonKeyFile(credentialsFileContent);
        putFile(jsonKeyFile.getAbsolutePath(), bucketName,  gscFilePath, localFilePath);
        return bucketName + "/" + gscFilePath;

    }

    public static boolean getFileByParamJson(String paramJson, String gscFilePath)  {
        try {
            if (StringUtils.isBlank(paramJson)) {
                throw new RuntimeException("paramJson is null ,please check.");
            }
            if (StringUtils.isBlank(gscFilePath)) {
                throw new RuntimeException("gscFilePath null ,please check.");
            }

            Gson gson = new Gson();
            Map<String, String> gcsInfo = gson.fromJson(paramJson, Map.class);
            String credentialsFileContent = gcsInfo.get("credentialsFileContent");
            if (StringUtils.isBlank(credentialsFileContent)) {
                throw new RuntimeException("credentialsFileContent is null ,please check.");
            }
            String bucketName = gcsInfo.get("bucket");
            if (StringUtils.isBlank(bucketName)) {
                throw new RuntimeException("bucket is null ,please check.");
            }
            String path = gcsInfo.get("path");
            File jsonKeyFile = getJsonKeyFile(credentialsFileContent);
            return getFile(jsonKeyFile.getAbsolutePath(), bucketName, gscFilePath);
        }catch (Exception ex){
            //ex.printStackTrace();
            return false;
        }

    }

    public static void listFileByParamJson(String paramJson, String gscFilePath) throws Exception {
        if(StringUtils.isBlank(paramJson)){
            throw new RuntimeException("paramJson is null ,please check.");
        }

        try {
            Gson gson = new Gson();
            Map<String, String> gcsInfo = gson.fromJson(paramJson, Map.class);
            String credentialsFileContent = gcsInfo.get("credentialsFileContent");
            if(StringUtils.isBlank(credentialsFileContent)){
                throw new RuntimeException("credentialsFileContent is null ,please check.");
            }
            String bucketName = gcsInfo.get("bucket");
            if(StringUtils.isBlank(bucketName)){
                throw new RuntimeException("bucket is null ,please check.");
            }
            String path = gcsInfo.get("path");
            File jsonKeyFile = getJsonKeyFile(credentialsFileContent);
            listFile(jsonKeyFile.getAbsolutePath(), bucketName, gscFilePath);
        } catch (Exception e) {
            throw e;
        }
    }

    private static File getJsonKeyFile(String credentialsFileContent) throws Exception {
        String de = ScStringEncryptor.decrypt(credentialsFileContent);
//        log.info(de);
        File jsonKeyFile = new File(Paths.get("").toAbsolutePath().toString() + File.separator + "jsonKey.json");
        if (jsonKeyFile.exists()) {
            jsonKeyFile.delete();
        }
        //log.info("jsonKeyPath:" + jsonKeyFile.getAbsoluteFile());
        try {
            FileUtils.writeStringToFile(jsonKeyFile, de);
        } catch (Exception e) {
            throw e;
        }
        return jsonKeyFile;
    }

    public static void putFile(String jsonKeyFilePath, String bucketName, String objectName, String localFilePath) throws Exception{
        Storage storage = StorageOptions.newBuilder()
                .setCredentials(ServiceAccountCredentials.fromStream(
                        new FileInputStream(jsonKeyFilePath))).build().getService();

        BlobId blobId = BlobId.of(bucketName, objectName);
        BlobInfo blobInfo = BlobInfo.newBuilder(blobId).build();


        File tempFile = new File(localFilePath);
        if(tempFile.length() < 1_000_000){
            log.info("====UploadSmallFile:" + localFilePath);
            byte[] bytes = Files.readAllBytes(Paths.get(localFilePath));
            storage.create(blobInfo, bytes);
        }else {
            log.info("====UploadBigFile:" + localFilePath);
            // For big files:
            // When content is not available or large (1MB or more) it is recommended to write it in chunks via the blob's channel writer.
            try (WriteChannel writer = storage.writer(blobInfo)) {
                byte[] buffer = new byte[10_240];
                try (InputStream input = Files.newInputStream(Paths.get(localFilePath))) {
                    int limit;
                    while ((limit = input.read(buffer)) >= 0) {
                        writer.write(ByteBuffer.wrap(buffer, 0, limit));
                    }
                }
            }
        }
        Blob exists = storage.get(bucketName, objectName);
        log.info("====upload success:" + exists.exists());
    }

    public static boolean getFile(String jsonKeyFilePath, String bucketName, String objectName) throws Exception{
        Storage storage = StorageOptions.newBuilder()
                .setCredentials(ServiceAccountCredentials.fromStream(
                        new FileInputStream(jsonKeyFilePath))).build().getService();

        Blob exists = storage.get(bucketName ,objectName);
        //System.out.println("====isExists:" + exists.exists());
        log.info("====isExists:" + exists.exists());
        return exists.exists();
    }

    public static List<String> getFileList(String paramJson, String bucketName) throws Exception{
        Gson gson = new Gson();
        Map<String, String> gcsInfo = gson.fromJson(paramJson, Map.class);
        String credentialsFileContent = gcsInfo.get("credentialsFileContent");
        if(StringUtils.isBlank(credentialsFileContent)){
            throw new RuntimeException("credentialsFileContent is null ,please check.");
        }

        File jsonKeyFile = getJsonKeyFile(credentialsFileContent);
        Storage storage = StorageOptions.newBuilder()
                .setCredentials(ServiceAccountCredentials.fromStream(
                        new FileInputStream(jsonKeyFile.getAbsolutePath()))).build().getService();

        Bucket bucket = storage.get(bucketName );
        Page<Blob> blobs = bucket.list();
        for (Blob blob : blobs.iterateAll()) {
            String fileName = blob.getName();
            //long sizeInBytes = blob.getSize();

            System.out.println("file name:"+ fileName+","+blob.getSize());
        }
        //System.out.println("====isExists:" + exists.exists());
        //log.info("====isExists:" + exists.exists());
        return null;
    }


    public static boolean getFilev1(String jsonKeyFilePath, String bucketName, String objectName) throws Exception{
        Storage storage = StorageOptions.newBuilder()
                .setCredentials(ServiceAccountCredentials.fromStream(
                        new FileInputStream(jsonKeyFilePath))).build().getService();
        Blob exists = storage.get(bucketName, objectName);
        log.info("====isExists:" + exists.exists());
        return exists.exists();
    }

    public static void listFile(String jsonKeyFilePath, String bucketName, String objectName) throws Exception{
        Storage storage = StorageOptions.newBuilder()
                .setCredentials(ServiceAccountCredentials.fromStream(
                        new FileInputStream(jsonKeyFilePath))).build().getService();

        // Blob exists = storage.get(bucketName ,objectName);
        log.info("====bucketName:" + bucketName + ",objectName:" + objectName);
//        Page<Blob> blobPage = storage.list(bucketName, Storage.BlobListOption.currentDirectory());
        Page<Blob> blobPage = storage.list(bucketName, Storage.BlobListOption.prefix(objectName));
        for(Blob blob: blobPage.iterateAll()){
            if(blob.getName().contains(objectName)){
                SimpleDateFormat sdf = new SimpleDateFormat("dd-MM-yyyy HH:mm:ss");
                String date = sdf.format(new Date(blob.getCreateTime()));
                log.info("===CreateTime:" + date );
                log.info("===name:" + blob.getName() );
                log.info("===size:" + blob.getSize());
            }
        }
    }

    public static void deleteFileParam(String paramJson, String bucketName, String objectName,String history) throws Exception{

        Gson gson = new Gson();
        Map<String, String> gcsInfo = gson.fromJson(paramJson, Map.class);
        String credentialsFileContent = gcsInfo.get("credentialsFileContent");
        if(StringUtils.isBlank(credentialsFileContent)){
            throw new RuntimeException("credentialsFileContent is null ,please check.");
        }

        //String bucketName = gcsInfo.get("bucket");
        if(StringUtils.isBlank(bucketName)){
            throw new RuntimeException("bucket is null ,please check.");
        }

        //String path = gcsInfo.get("path");
        File jsonKeyFile = getJsonKeyFile(credentialsFileContent);
        delFile1(jsonKeyFile.getPath(),bucketName,objectName,history);
    }

    public static void toHistoryParam(String paramJson, String bucketName, String objectName,String history) throws Exception{

        Gson gson = new Gson();
        Map<String, String> gcsInfo = gson.fromJson(paramJson, Map.class);
        String credentialsFileContent = gcsInfo.get("credentialsFileContent");
        if(StringUtils.isBlank(credentialsFileContent)){
            throw new RuntimeException("credentialsFileContent is null ,please check.");
        }

        //String bucketName = gcsInfo.get("bucket");
        if(StringUtils.isBlank(bucketName)){
            throw new RuntimeException("bucket is null ,please check.");
        }

        //String path = gcsInfo.get("path");
        File jsonKeyFile = getJsonKeyFile(credentialsFileContent);
        toHistryFile1(jsonKeyFile.getPath(),bucketName,objectName,history);
    }

    public static void toHistryFile1(String jsonKeyFilePath, String bucketName, String objectName,String history) throws Exception{
        System.out.println("=====toHistryFile1.bucketName:" + bucketName + ",history:"+history+",objectName:"+objectName);
        Storage storage = StorageOptions.newBuilder()
                .setCredentials(ServiceAccountCredentials.fromStream(
                        new FileInputStream(jsonKeyFilePath))).build().getService();
        // Blob exists = storage.get(bucketName ,objectName);
        //log.info("====bucketName:" + bucketName + ",objectName:" + objectName);
        //history = "HistoricalFiles";
//        Page<Blob> blobPage = storage.list(bucketName, Storage.BlobListOption.currentDirectory());
        Page<Blob> blobPage = storage.list("wmt-seoclarity-dev-api", Storage.BlobListOption.prefix(objectName));
        //Map<String, String> gcsInfo = gson.fromJson(paramJson, Map.class);
        //String credentialsFileContent = gcsInfo.get("credentialsFileContent");
        //storage.delete();
        for(Blob blob: blobPage.iterateAll()){

            if(!StringUtils.equals("latest/",blob.getName())){
                String delblob = blob.getName();
                System.out.println("=====blob.getName():"+blob.getName());
                String newBlobPath =history ;
                blob.copyTo("wmt-seoclarity-dev-api",newBlobPath);
            }
        }
    }

    public static void delFile1(String jsonKeyFilePath, String bucketName, String objectName,String history) throws Exception{
        Storage storage = StorageOptions.newBuilder()
                .setCredentials(ServiceAccountCredentials.fromStream(
                        new FileInputStream(jsonKeyFilePath))).build().getService();
        // Blob exists = storage.get(bucketName ,objectName);
        log.info("====bucketName:" + bucketName + ",objectName:" + objectName);
//        Page<Blob> blobPage = storage.list(bucketName, Storage.BlobListOption.currentDirectory());
        Page<Blob> blobPage = storage.list("wmt-seoclarity-dev-api", Storage.BlobListOption.prefix(objectName));
        //Map<String, String> gcsInfo = gson.fromJson(paramJson, Map.class);
        //String credentialsFileContent = gcsInfo.get("credentialsFileContent");
        //storage.delete();
        for(Blob blob: blobPage.iterateAll()){

            if(!StringUtils.equals("latest/",blob.getName())){
                String delblob = blob.getName();
                System.out.println("=====blob.getName():"+blob.getName());
//                String newBlobPath =history + "/" + blob.getName().replace("latest/","");
//                    blob.copyTo("wmt-seoclarity-dev-api",newBlobPath);
                //System.out.println("=====newBlob:"+newBlobPath);
                if(blob.delete()){
                    System.out.println("====="+delblob + " is deleted");
                }

                //
                //blob.copyTo(destinationBlob.getBlobId(), Blob.BlobSourceOption.decryptionKey("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"));
                //break;
//                log.info("===id:" + blob.getBlobId() );
//                log.info("===name:" + blob.getName() );
            }
        }
    }

}
