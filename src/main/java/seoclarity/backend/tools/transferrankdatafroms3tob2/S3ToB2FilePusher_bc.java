package seoclarity.backend.tools.transferrankdatafroms3tob2;

import com.alibaba.fastjson.JSONObject;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.model.*;
import com.amazonaws.services.sqs.AmazonSQS;
import com.amazonaws.services.sqs.model.DeleteQueueRequest;
import com.amazonaws.services.sqs.model.Message;
import com.amazonaws.util.IOUtils;
import lombok.Data;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.log4j.Logger;
import seoclarity.backend.dao.actonia.BackBlazeKeywordStoreEntityDao;
import seoclarity.backend.dao.actonia.BackBlazeMetaFileEntityDao;
import seoclarity.backend.dao.actonia.BackBlazeMetaFolderEntityDao;
import seoclarity.backend.dao.actonia.BackBlazeSourceFileS3EntityDao;
import seoclarity.backend.entity.BackBlazeKeywordStoreEntity;
import seoclarity.backend.entity.BackBlazeMetaFileEntity;
import seoclarity.backend.entity.BackBlazeMetaFolderEntity;
import seoclarity.backend.entity.BackBlazeSourceFileS3Entity;
import seoclarity.backend.multithread.BackendThreadFactory;
import seoclarity.backend.utils.*;
import seoclarity.backend.utils.amazon.SQSUtils;
import seoclarity.backend.utils.cityhash.CityHashUtil;
import seoclarity.backend.utils.murmurhash.MurmurHashUtils;

import java.io.*;
import java.net.URLDecoder;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * seoclarity.backend.transferrankdatafroms3tob2.TransferRankingDataFromAmazonS3ToBackBlaze
 *
 * @Date 20200106
 * <AUTHOR>
 * <p>
 * transfer files from S3 to BB
 * https://www.wrike.com/open.htm?id=820871750
 * <p>
 * exec: test in s18
 * git pull; mvn compile;
 * mvn exec:java -Dexec.mainClass="seoclarity.backend.transferrankdatafroms3tob2.TransferRankingDataFromAmazonS3ToBackBlaze" -Dexec.cleanupDaemonThreads=false -Dexec.args="/home/<USER>/source/ewain/dev-clarity-backend-scripts/local_file/s3tobb/test.csv"
 * nohup mvn exec:java -Dexec.mainClass="seoclarity.backend.transferrankdatafroms3tob2.TransferRankingDataFromAmazonS3ToBackBlaze" -Dexec.cleanupDaemonThreads=false -Dexec.args="/home/<USER>/source/ewain/dev-clarity-backend-scripts/local_file/s3tobb/test.csv" >> log/transferRankingDataFromAmazonS3ToBackBlaze-20200113-01.log 2>&1 &
 */
public class S3ToB2FilePusher_bc {

    private static final Logger log = Logger.getLogger(S3ToB2FilePusher_bc.class);

    // TODO: 2022/1/27
    private static int threadCount = 5;
    private static ExecutorService threadPool = new ThreadPoolExecutor(threadCount, threadCount, 60L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<Runnable>(), new BackendThreadFactory());
    // lock: keep write line order
    private static Object lock = new Object();

    /**
     * amazon s3
     */
    private final static String s3BucketName = "daily-html-virginia";
//    private final static String s3SummaryFileBucketName = "alps-distcp";
    private final static String s3AccessKey = "********************";
    private final static String s3SecretKey = "v7Qts+qswF9qDojbKZQ1BofRbWcQLJu8CLM+eXrf";

    /**
     * backblaze
     */
    private final static String bbBucketName = "serp-html";
    private final static String APP_KEY_ID = "FlIOnZmgKUzDQa2VqdQZNbTw";
    private final static String APP_KEY = "5RloZnhbCoxGrQXTH3yVO2NQhi3l7bozTavvORl";
    private final static String endpoint = "s3.us-east-1.aws.flexify.io";
    private final static String region = "us-east-1";

    private final static String ZIP_CONTENT_TYPE = "application/x-zip-compressed";
    /**
     * file path
     */
    //s3 download file and zip file path
    private static String tempFilePath = "/home/<USER>/s3ToBB/zipTmp/";
    //s3 key list gz file
    // TODO: 2022/1/18
    private static String s3SummaryFilePath = "/home/<USER>/s3ToBB/s3Summary/"/* "E:\\work\\work-seoclarity\\download from s3\\transfer ranking data to bb\\sum"*/; // TODO: 2022/1/18
    // TODO: 2022/1/14
    private static String uploadFailedFileFolder = "/home/<USER>/s3ToBB/catch";
//    private static String uploadFailedFileFolder = "/home/<USER>/source/ewain/dev-clarity-backend-scripts/local_file/s3tobb/catch";
    //uploadToBBError file path
    private final static String uploadToBBErrorFilePath = uploadFailedFileFolder + "/uploadToBBError.txt";
    //uploadToBBOverLimit file path
    private final static String uploadToBBOverLimitFilePath = uploadFailedFileFolder + "/uploadToBBOverLimit.txt";
    //parse file folder error
    private final static String parserFolderErrorFilePath = uploadFailedFileFolder + "/parserFolderError.txt";
    //save file info to db exception
    private final static String saveFileInfoErrorFilePath = uploadFailedFileFolder + "/saveFileInfoError.txt";
    //get S3 file folder error
    private final static String getFolderErrorFilePath = uploadFailedFileFolder + "/getFolderError.txt";

    //sqs q name file
//    private final String SQS_NAME_FILE = "";
    // can not file new q name! sleep 1 hour
//    private final long SQS_NAME_SLEEP = 1 * 60 * 60 * 1000;
    // 每次拿消息的数量
    private final int message_max_size = 10;
    // sqs visibilityTimeOut
    // visibility_timeout = message_size_thread * 处理每条数据消耗的时间 * 1.2(系数)
    // TODO: 2022/1/27  8 * 60
    private final int visibility_timeout = 150;

    // TODO: 2022/1/18

    /**
     *  线程拿到 message_size_thread条消息后, 处理文件夹差异, 同一个文件处理到 s3ZipFileCnt条消息时, 送到压缩方法, 将 bbZipFileCnt条小心压缩到一起上传到 B2
     */
    /**
     * 线程每次拿到的全部消息数量 todo 2000
     */
    private final int message_size_thread = 300;
    /**
     * 同一个文件夹下将 s3ZipFileCnt条数据送到压缩程序分组处理
     */
    private static final int s3ZipFileCnt = 30;
    /**
     * 将 bbZipFileCnt条数据压缩为一个文件
     */
    private static final int bbZipFileCnt = 30;

    //主线程读取sqs首个Q之后等待的时间
    // todo 10 * 60 * 1000
    private final long THREAD_MAIN_GET_SQS_WAIT_TIME = 30 * 1000;

    private final String b2FileInfoQPrefix = "S3TOB2_B2_FILE_INFO_Q";
    private static final String SQS_NAME_PREFIX = "S3TOB2_S3_FILE_INFO_";

    /* 重试获取q次数 */
    private final int TRY_CNT = 3;

    /**
     * upload file to bb status
     */
    // 1: success; 2: error; 3: upload 403 over limit; 4: 2+3;
    private final static int uploadToBBSuccess = 1;
    private final static int uploadToBBError = 2;
    private final static int uploadToBBOverLimit = 3;
    private final static int uploadToBBAllEX = 3;

    /**
     * if upload to bb error or overLimit, whether to interrupt the main class
     */
    private static boolean interruptStatus = false;

    private static final int MYSQL_DB_QUERY_WITH_STRING = 200;

//    private final CSVFormat csvFormat = CSVFormat.DEFAULT.withDelimiter(',');

    private static AmazonS3 s3;
    private static AmazonS3 bb;
    private static AmazonSQS amazonSQS;


    static {
        /*连接 s3 */
        BasicAWSCredentials awsCredentials = new BasicAWSCredentials(s3AccessKey, s3SecretKey);
        s3 = AmazonS3ClientBuilder
                .standard()
                .withCredentials(new AWSStaticCredentialsProvider(awsCredentials)).withRegion("us-east-1").build();

        BasicAWSCredentials bbCredentials = new BasicAWSCredentials(APP_KEY_ID, APP_KEY);
        bb = AmazonS3ClientBuilder.standard()
                .withEndpointConfiguration(new AwsClientBuilder.EndpointConfiguration(endpoint, region))
                .withCredentials(new AWSStaticCredentialsProvider(bbCredentials))
                .build();

        amazonSQS = SQSUtils.getAmazonSQS();
    }

    private BackBlazeKeywordStoreEntityDao backBlazeKeywordStoreEntityDao;
    private BackBlazeMetaFileEntityDao backBlazeMetaFileEntityDao;
    private BackBlazeMetaFolderEntityDao backBlazeMetaFolderEntityDao;
    private BackBlazeSourceFileS3EntityDao backBlazeSourceFileS3EntityDao;
    private EmailSenderComponent emailSenderComponent;

    public S3ToB2FilePusher_bc() {
        backBlazeKeywordStoreEntityDao = SpringBeanFactory.getBean("backBlazeKeywordStoreEntityDao");
        backBlazeMetaFileEntityDao = SpringBeanFactory.getBean("backBlazeMetaFileEntityDao");
        backBlazeMetaFolderEntityDao = SpringBeanFactory.getBean("backBlazeMetaFolderEntityDao");
        backBlazeSourceFileS3EntityDao = SpringBeanFactory.getBean("backBlazeSourceFileS3EntityDao");
        emailSenderComponent = SpringBeanFactory.getBean("emailSenderComponent");
    }

    /**
     * @Deprecated!!!!!!!
     *
     * manifestFilePath: manifest.json 文件全路径
     *
     *   四个断点参数:
     *      startPointKeyForSummaryFile: json文件中 gz文件起点(包含)
     *      endPointKeyForSummaryFile: json文件中 gz文件终点(包含)
     *      startPointKeyForKeyListFile: csv文件中 key的起点(包含)
     *      endPointKeyForKeyListFile: csv文件中 key的终点(包含)
     *
     *      处在中间不需要提供的参数需要用 NULL替代
     *      *** 程序运行结束会打出最后一个 summary文件名, 下一个 summary文件名, 最后一个 key, 下一个 key
     *
     */

    public static void main(String[] args) {
        // TODO: 2022/1/18
//        String manifestFilePath = null;
//        String startPointKeyForSummaryFile = null;
//        String endPointKeyForSummaryFile = null;
//        String startPointKeyForKeyListFile = null;
//        String endPointKeyForKeyListFile = null;
//        if (args.length > 0) {
//            manifestFilePath = args[0];
//        } else {
//            log.error("$EC=>parameter error!, please provide a correct file path");
//            return;
//        }
//
//        if (args.length > 1) {
//            startPointKeyForSummaryFile = args[1];
//            if (startPointKeyForSummaryFile.equals("NULL")) {
//                startPointKeyForSummaryFile = null;
//            }
//        }
//
//        if (args.length > 2) {
//            endPointKeyForSummaryFile = args[2];
//            if (endPointKeyForSummaryFile.equals("NULL")) {
//                endPointKeyForSummaryFile = null;
//            }
//        }
//
//        if (args.length > 3) {
//            startPointKeyForKeyListFile = args[3];
//            if (startPointKeyForKeyListFile.equals("NULL")) {
//                startPointKeyForKeyListFile = null;
//            }
//        }
//
//        if (args.length > 4) {
//            endPointKeyForKeyListFile = args[4];
//            if (endPointKeyForKeyListFile.equals("NULL")) {
//                endPointKeyForKeyListFile = null;
//            }
//        }
        S3ToB2FilePusher_bc tool = new S3ToB2FilePusher_bc();
//        tool.processSummaryFile("C:\\Users\\<USER>\\Desktop\\manifest111.json",
//                "daily-html-virginia/key/data/8cfcdc29-b8d5-4ce5-94c4-a211ecefd400.csv.gz",
//                "daily-html-virginia/key/data/8cfcdc29-b8d5-4ce5-94c4-a211ecefd400.csv.gz",
//                "20200322/city-1-1-goshen%2C%2520ny/dr%2Bemily%2Bgordon",
//                "20200322/city-1-1-granada%2520hills%2C%2520ca/cat%2Bsitting");

//        log.info("$EC=>process param=> " +
//                "manifestFilePath: " + manifestFilePath + ", " +
//                "startPointKeyForSummaryFile: " + startPointKeyForSummaryFile + ", " +
//                "endPointKeyForSummaryFile: " + endPointKeyForSummaryFile + ", " +
//                "startPointKeyForKeyListFile: " + startPointKeyForKeyListFile + ", " +
//                "endPointKeyForKeyListFile: " + endPointKeyForKeyListFile);
        long start = System.currentTimeMillis();
//        tool.processSummaryFile(manifestFilePath, startPointKeyForSummaryFile, endPointKeyForSummaryFile, startPointKeyForKeyListFile, endPointKeyForKeyListFile);

        // TODO: 2022/1/18  for test
        // manifestFilePath 替代使用 csv文件, 测试上传
//        tool.processSingleFile(manifestFilePath, null, null);
        tool.processBySqs();
        long end = System.currentTimeMillis();
        log.info("$EC=> exec time: " + (end - start)/1000 + "s");
    }

    private void processBySqs() {
//        FileInputStream inputStream = null;
//        BufferedReader bufferedReader = null;
//        try {
//            inputStream = new FileInputStream(SQS_NAME_FILE);
//            bufferedReader = new BufferedReader(new InputStreamReader(inputStream));
//            String line;
//            while (true) {
//                if ((line = bufferedReader.readLine()) != null) {
//                    if (line.equals("")) {
//                        continue;
//                    }
//
//                }else {
//                    try {
//                        Thread.sleep(SQS_NAME_SLEEP);
//                    } catch (InterruptedException e) {
//                        e.printStackTrace();
//                    }
//                }
//            }
//        } catch (IOException e) {
//            e.printStackTrace();
//        } finally {
//            try {
//                inputStream.close();
//                bufferedReader.close();
//            } catch (IOException e) {
//                e.printStackTrace();
//            }
//        }


        long start = System.currentTimeMillis();
        //
        flagWh: while (true) {
            if(interruptStatus) {
                log.error("$EC=>processBySqs error");
                break;
            }
            // 每次处理按时间排序的第一条Q, 处理完成之后停一段时间再次读取第一条Q
            int aliveCount = ((ThreadPoolExecutor) threadPool).getActiveCount();
            if (aliveCount < threadCount) {
                List<String> queueUrls = SQSUtils.getListQueues(amazonSQS, SQS_NAME_PREFIX);
                System.out.println("all q: " + queueUrls);
                if (queueUrls.size() > 0) {
                    List<String> qList = queueUrls.stream().sorted(String::compareTo).collect(Collectors.toList());
                    String queryUrl = qList.get(0);
                    int tryCnt = TRY_CNT;
                    flagFor2: for (int i = 0; i < tryCnt; i++) {
                        Integer messageCnt = SQSUtils.getMessageNumberInQueue(amazonSQS, queryUrl);
                        if (messageCnt != null && messageCnt > 0) {
                            createThread(amazonSQS, queryUrl);
                            continue flagWh;
                        } else {
                            try {
                                log.info("$EC=>approximateNumberOfMessages is 0, will wait 30s and try again.");
                                Thread.sleep(30 * 1000);
                            } catch (InterruptedException e) {
                                e.printStackTrace();
                            }
                        }
                    }
                    log.info("$EC=>queryUrl: " + queryUrl + ", Q message is null. will del this Q!");
                    amazonSQS.deleteQueue(new DeleteQueueRequest(queryUrl));
                    long end = System.currentTimeMillis();
                    System.out.println("process time: " + (end-start)/1000);
                    try {
                        //delete q 之后几秒可能还可以检查到该Q,需要Sleep
                        Thread.sleep(15 * 1000);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }else {
                    try {
                        log.info("$EC=>can not get any q, sleep 30s");
                        Thread.sleep(THREAD_MAIN_GET_SQS_WAIT_TIME);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
            }else {
                try {
                    System.out.println("aliveCount: " + aliveCount + ", sleep 30s ");
                    Thread.sleep(THREAD_MAIN_GET_SQS_WAIT_TIME);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }
    }


    /**
     * 解析 manifest文件,下载 keylist文件并解压
     *
     * @param manifestFilePath
     * @param startingPointKeyForSummaryFile
     * @param startingPointKeyForKeyListFile
     */
//    @Deprecated
//    private void processSummaryFile(String manifestFilePath,
//                                    String startingPointKeyForSummaryFile,
//                                    String endPointKeyForSummaryFile,
//                                    String startingPointKeyForKeyListFile,
//                                    String endPointKeyForKeyListFile) {
//        File manifestFile = new File(manifestFilePath);
//        Manifest manifest = null;
//        try {
//            manifest = JSONObject.parseObject(FileUtils.readFileToByteArray(manifestFile), Manifest.class);
//        } catch (IOException e) {
//            log.error("$EC=>parse manifest error, msg: " + CollectionUtils.getErrorMsg(e));
//            return;
//        }
//
//        if (manifest == null || manifest.getFiles().size() == 0) {
//            return;
//        }
//
//        List<String> keyList = manifest.getFiles().stream().map(var -> var.getKey()).collect(Collectors.toList());
//        Integer start = null;
//        if (startingPointKeyForSummaryFile != null) {
//            start = keyList.indexOf(startingPointKeyForSummaryFile);
//        }
//        Integer end = null;
//        if (endPointKeyForSummaryFile != null) {
//            end = keyList.indexOf(endPointKeyForSummaryFile);
//        }
//
//        flagFor:
//        for (int i = 0; i < keyList.size(); i++) {
//            String gzFilePath = keyList.get(i);
//            if (start != null && i < start.intValue()) {
//                log.info("$EC=>skip processed summary file: " + gzFilePath);
//                continue;
//            }
//            if (end != null && i > end.intValue()) {
//                log.info("$EC=>end process summary file, last summary file: " + endPointKeyForSummaryFile + ", next summary file: " + gzFilePath);
//                return;
//            }
//            File localFile = null;
//            int downloadTryCnt = 3;
//            flagWh:
//            while (downloadTryCnt > 0) {
//                try {
//                    S3Object o = s3.getObject(s3SummaryFileBucketName, gzFilePath);
//                    S3ObjectInputStream s3is = o.getObjectContent();
//                    localFile = new File(s3SummaryFilePath + "/" + gzFilePath);
//                    if (!localFile.exists()) {
//                        String folder = localFile.getParent();
//                        File folderFile = new File(folder);
//                        if (!folderFile.exists()) {
//                            folderFile.mkdirs();
//                        }
//                    }
//                    FileOutputStream fos = new FileOutputStream(localFile);
//                    byte[] read_buf = new byte[1024];
//                    int read_len = 0;
//                    while ((read_len = s3is.read(read_buf)) > 0) {
//                        fos.write(read_buf, 0, read_len);
//                    }
//                    s3is.close();
//                    fos.close();
//                    break flagWh;
//                } catch (Exception e) {
//                    downloadTryCnt--;
//                    log.error("$EC=>download file exception, try again! key: " + gzFilePath + ", msg: " + CollectionUtils.getErrorMsg(e));
//                }
//            }
//
//            if (localFile == null) {
//                log.error("$EC=>download s3 file failed! please check! key: " + gzFilePath);
//                return;
//            }
//
//            String localFileFolder = localFile.getParent();
//            File outputFile = new File(localFileFolder + "/" + StringUtils.replace(localFile.getName(), ".gz", ""));
//            try {
//                GZipUtil.unGzipBigFile(FileUtils.readFileToByteArray(localFile), outputFile);
//            } catch (IOException e) {
//                e.printStackTrace();
//            }
//            processSingleFile(outputFile.getAbsolutePath(), startingPointKeyForKeyListFile, endPointKeyForKeyListFile);
////            System.out.println("");
//            localFile.delete();
//            outputFile.delete();
////            System.out.println(gzFilePath);
//        }
//    }

    /**
     * 主类仅分发文件地址, 由子类下载压缩并上传BB
     */
//    private void processSingleFile(String s3KeyListFilePath, String startingPointKeyForKeyListFile, String endPointKeyForKeyListFile) {
//
//        int processFileCnt = 0;
//
//        FileInputStream inputStream = null;
//        BufferedReader bufferedReader = null;
//        try {
//            inputStream = new FileInputStream(s3KeyListFilePath);
//            bufferedReader = new BufferedReader(new InputStreamReader(inputStream));
//
//            int processFileCntForFolder = 0;
//            List<RecordData> recordDataList = new ArrayList<>(s3ZipFileCnt);
//            String lastFolder = null;
//            String line;
//            while ((line = bufferedReader.readLine()) != null) {
//
////                log.info("$EC=>process currKey: " + line + ", interruptStatus: " + interruptStatus);
//                if (interruptStatus) {
//                    log.error("$EC=>program exception" + ", currKey: " + line);
//                    break;
//                }
//
//                CSVParser csvParser = CSVParser.parse(line, csvFormat);
//                List<CSVRecord> csvRecords = csvParser.getRecords();
//                if (csvRecords.size() != 1) {
//                    continue;
//                }
//
//                CSVRecord record = csvRecords.get(0);
//                String bucketName = record.get(0);
//                String s3FilePath = record.get(1);
//                String s3StorageType = record.get(2);
//
//                if (bucketName == null || s3FilePath == null || s3StorageType == null || s3FilePath.equals("")) {
//                    continue;
//                }
//
//                if (s3StorageType.equals(StorageClass.Glacier.toString())) {
//                    continue;
//                }
//
//                if (startingPointKeyForKeyListFile != null && s3FilePath.compareTo(startingPointKeyForKeyListFile) < 0) {
//                    continue;
//                }
//
//                if (endPointKeyForKeyListFile != null && s3FilePath.compareTo(endPointKeyForKeyListFile) > 0) {
//                    log.info("$EC=>end process s3 key, last key: " + endPointKeyForKeyListFile + ", next key: " + s3FilePath);
//                    break;
//                }
//
//                s3FilePath = StringEscapeUtils.unescapeHtml(StringEscapeUtils.escapeHtml(URLDecoder.decode(s3FilePath, "UTF-8")));
//
//                RecordData recordData = new RecordData();
//                recordData.setBucketName(bucketName);
//                recordData.setS3FilePath(s3FilePath);
//                recordData.setS3StorageType(s3StorageType);
//
//                String currFolder = getS3FileFolder(s3FilePath);
//                if (currFolder == null) {
//                    continue;
//                }
//                if (lastFolder == null) {
//                    lastFolder = currFolder;
//                }
//
//                if (compareFilePath(currFolder, lastFolder)) {
//                    recordDataList.add(recordData);
//                    processFileCntForFolder++;
//                    if (recordDataList.size() >= s3ZipFileCnt) {
//                        createThread(lastFolder, recordDataList);
//                        recordDataList.clear();
//                    }
//                } else {
//                    log.info("$EC=>end process folder: " + lastFolder + "; file cnt for this folder: " + processFileCntForFolder);
//                    processFileCnt += processFileCntForFolder;
//                    processFileCntForFolder = 0;
//                    if (recordDataList.size() > 0) {
//                        createThread(lastFolder, recordDataList);
//                        recordDataList.clear();
//                    }
//                    recordDataList.add(recordData);
//                    processFileCntForFolder++;
//                    lastFolder = currFolder;
//                }
//            }
//
//            if (recordDataList.size() > 0) {
//                if (!interruptStatus) {
//                    createThread(lastFolder, recordDataList);
//                }
//            }
//            processFileCnt += processFileCntForFolder;
//            log.info("$EC=>end process last folder: " + lastFolder + "; file cnt for this folder: " + processFileCntForFolder);
//
//        } catch (IOException e) {
//            e.printStackTrace();
//        } finally {
//            try {
//                inputStream.close();
//                bufferedReader.close();
//            } catch (IOException e) {
//                log.info("关闭流异常");
//                e.printStackTrace();
//            }
//        }
//
//        // threadPool close
//        while (!threadPool.isShutdown()) {
//            ThreadUtil.sleep(15 * 1000);
//            int aliveCount = ((ThreadPoolExecutor) threadPool).getActiveCount();
////            System.out.println("Thread aliveCount : " + aliveCount);
//            if (aliveCount == 0) {
//                threadPool.shutdown();
//            }
//        }
//        log.info("$EC=>end process! file cnt: " + processFileCnt);
//    }

    /**
     * 对比文件路径是否完全一致 (不包括文件名称本身)
     *
     * @param currFileFolder
     * @param lastFileFolder
     * @return
     */
    private boolean compareFilePath(String currFileFolder, String lastFileFolder) {
        if (currFileFolder == null || lastFileFolder == null) {
            return false;
        }
        if (currFileFolder == "" || lastFileFolder == "") {
            return false;
        }
        if (!lastFileFolder.equals(currFileFolder)) {
            return false;
        }
        return true;
    }

    /**
     * 从s3 文件全路径获取文件夹路径
     * 20200802/255-30/poker+regeln => 20200802/255-30/
     * 需要捕获异常路径!
     *
     * @param s3FilePath
     * @return
     */
    private String getS3FileFolder(String s3FilePath) {
        try {
            String[] s3FilePathArr = s3FilePath.split("/");
            String s3FileFolderPath = "";
            for (int i = 0; i < s3FilePathArr.length - 1; i++) {
                s3FileFolderPath += s3FilePathArr[i] + "/";
            }

            System.out.println(" get folder: " + s3FilePath + "->" + s3FileFolderPath);
            return s3FileFolderPath;
        } catch (Exception e) {
            log.info("$EC=>get folder error! key: " + s3FilePath + ", msg: " + CollectionUtils.getErrorMsg(e));
            try {
                FileUtils.write(new File(getFolderErrorFilePath), s3FilePath + "\n", true);
            } catch (IOException ioException) {
                ioException.printStackTrace();
            }
            return null;
        }
    }

    private void createThread(AmazonSQS amazonSQS, String queryUrl) {
        threadPool.execute(new S3ToB2FilePusher_bc.TransferToolThreadV2(amazonSQS, queryUrl));
//        do {
//            int aliveCount = ((ThreadPoolExecutor) threadPool).getActiveCount();
//            if (aliveCount < threadCount) {
//                if (interruptStatus) {
//                    break;
//                }
//
//                break;
//            } else {
//                try {
//                    log.info("$EC=>wait for thread to release=>aliveCount: " + aliveCount);
//                    Thread.sleep(10 * 1000);
//                } catch (InterruptedException e) {
//                    e.printStackTrace();
//                }
//            }
//        } while (true);
    }

    @Deprecated
    private void createThread(String fileFolderPath, List<RecordData> recordDataList) {
//        int aliveCount = ((ThreadPoolExecutor) threadPool).getActiveCount();
//
////        log.info("$EC=>Distribute file path to thread subclass=>" + recordDataList);
//        if (aliveCount < threadCount) {
//            threadPool.execute(new TransferRankingDataFromAmazonS3ToBackBlaze.TransferToolThread(fileFolderPath, recordDataList));
//        }else {
//
//        }
//        log.info("$EC=>Distribute file path to thread subclass=>" +
//                "file path size: " + recordDataList.size() + ", " +
//                "first fileKey: " + recordDataList.get(0).getS3FilePath() + ", " +
//                "last fileKey: " + recordDataList.get(recordDataList.size() - 1).getS3FilePath());
//        do {
//            int aliveCount = ((ThreadPoolExecutor) threadPool).getActiveCount();
////            log.info("$EC=>Distribute file path to thread subclass=>aliveCount: " + aliveCount);
//            if (aliveCount < threadCount) {
//                if (interruptStatus) {
//                    log.warn("$EC=>Distribute file path to thread subclass: this data does not need to be processed=> first fileKey: " + recordDataList.get(0).getS3FilePath() + ", " +
//                            "last fileKey: " + recordDataList.get(recordDataList.size() - 1).getS3FilePath());
//                    break;
//                }
//                threadPool.execute(new S3ToB2FilePusher.TransferToolThread(fileFolderPath, recordDataList));
//                break;
//            } else {
//                try {
//                    log.info("$EC=>wait for thread to release=>aliveCount: " + aliveCount);
//                    Thread.sleep(2 * 1000);
//                } catch (InterruptedException e) {
//                    e.printStackTrace();
//                }
//            }
//        } while (true);
    }



    class TransferToolThreadV2 implements Runnable {
        private AmazonSQS amazonSQS;
        private String queryUrl;

        public TransferToolThreadV2(AmazonSQS amazonSQS, String queryUrl){
            this.amazonSQS = amazonSQS;
            this.queryUrl = queryUrl;
        }

        @Override
        public void run() {
            doGet();
        }

        private void doGet() {
            List<Message> messages = new ArrayList<>();
            boolean firstProcess = true;
            while(true) {
                List<Message> result = SQSUtils.getMessageFromQueue(amazonSQS, queryUrl, message_max_size, visibility_timeout);
                System.out.println("get message size: " + result.size());
                if (result.size() > 0) {
                    messages.addAll(result);
                    if (messages.size() >= message_size_thread) {
                        processMessage(messages);
                        messages.clear();
                    }
                    firstProcess = false;
                }else {
                    if (firstProcess) {
                        //首次读Q即是空
                        log.info("$EC=>queryUrl: " + queryUrl + " no message need to be process! ");
//                        try {
//                            log.info("$EC=> Q message is null. wait 30s!");
//                            Thread.sleep(30 * 1000);
//                        } catch (InterruptedException e) {
//                            e.printStackTrace();
//                        }
                        return;
                    }
                    break;
                }
            }

            if (messages.size() > 0) {
                processMessage(messages);
            }

        }

        private void processMessage(List<Message> messages) {
            List<Message> sortedMessageList = messages.stream().sorted(Comparator.comparing(Message::getBody)).collect(Collectors.toList());
            int s3FilePathCnt = sortedMessageList.size();
            int processFileCnt = 0;
            int processFileCntForFolder = 0;
            List<Message> messageList = new ArrayList<>();
            String lastFolder = null;
            for (Message message : sortedMessageList) {
                String currFolder = getS3FileFolder(message.getBody());
                System.out.println("process message: " + message.getBody());
                if (currFolder == null) {
                    continue;
                }
                if (lastFolder == null) {
                    lastFolder = currFolder;
                }
                if (compareFilePath(currFolder, lastFolder)) {
                    messageList.add(message);
                    processFileCntForFolder++;
                    if (messageList.size() >= s3ZipFileCnt) {
                        processS3FileKey(currFolder, messageList);
                        messageList.clear();
                    }
                } else {
                    log.info("$EC=>end process folder: " + lastFolder + "; file cnt for this folder: " + processFileCntForFolder);
                    processFileCnt += processFileCntForFolder;
                    processFileCntForFolder = 0;
                    if (messageList.size() > 0) {
                        processS3FileKey(lastFolder, messageList);
                        messageList.clear();
                    }
                    messageList.add(message);
                    processFileCntForFolder++;
                    lastFolder = currFolder;
                }
            }
            if (messageList.size() > 0) {
                processS3FileKey(lastFolder, messageList);
            }
            processFileCnt += processFileCntForFolder;
            log.info("$EC=>end process last folder: " + lastFolder + "; file cnt for this folder: " + processFileCntForFolder);
            log.info("$EC=>end process! process file cnt: " + processFileCnt + ", s3FilePathCnt: " + s3FilePathCnt);
        }

        /**
         * @param fileFolderPath eg: 20200322/city-1-1-westbrook,%20me/
         * @param messageList
         */
        private void processS3FileKey(String fileFolderPath, List<Message> messageList) {

            System.out.println("start process folder :" + fileFolderPath + ", message size: " + messageList.size());
            if (messageList.size() == 0) {
                return;
            }

            // TODO: 2022/1/7
            //tempFilePath = "E:/work/work-seoclarity/download from s3/transfer ranking data to bb";
            tempFilePath = "/home/<USER>/source/ewain/dev-clarity-backend-scripts/local_file/s3tobb";
            // 文件在s3的全路径
            String s3FullPathFolder = s3BucketName + "/" + fileFolderPath;
            String bbFullPathFolder = bbBucketName + "/" + fileFolderPath;

            File fileFolder = new File(tempFilePath + "/" + fileFolderPath);
            if (!fileFolder.exists()) {
                fileFolder.mkdirs();
            }

            /** parse file folder */
            Map<String, FileInfo> parsedFileNamePInfoMap = new LinkedHashMap<>();
            // daily-html-virginia/20200322/city-1-1-westfield,%20ny/go+home -> messageId
            Map<String, Message> parsedFileS3FullPathPMessageMap = new HashMap<>();
            List<String> parseFolderErrorKeyList = new ArrayList<>();

            // key_name: 20200802/95-105/go+home
            for (Message message : messageList) {
                String key_name = message.getBody();
                FileInfo fileInfo = null;
                try {
                    fileInfo = getFileInfo(key_name);
                    parsedFileNamePInfoMap.put(key_name, fileInfo);
                    parsedFileS3FullPathPMessageMap.put(s3BucketName + "/" + key_name, message);
                } catch (Exception e) {
                    // 有可能会遇到特别的文件夹名称, 造成解析文件名失败
                    log.info("$EC=>"+Thread.currentThread().getName()+" parse filePath failed! key: " + key_name + ", msg: " + CollectionUtils.getErrorMsg(e));
                    parseFolderErrorKeyList.add(key_name);
                }
            }

            if (parseFolderErrorKeyList.size() > 0) {
                writeErrorLineToFile(parserFolderErrorFilePath, parseFolderErrorKeyList);
            }

            if (parsedFileNamePInfoMap.size() == 0) {
                //文件名全部解析失败, 关闭程序做检查
                log.info("$EC=>"+Thread.currentThread().getName()+" parse filePath all failed! exist!");
                interruptStatus = true;
                return;
            }

            /** 1. download file from s3 */
            Map<String, FileInfo> downLoadFileNamePInfoMap = new LinkedHashMap<>();
            List<File> downLoadLocalFileList = new ArrayList<>();
            downloadFileFromS3(parsedFileNamePInfoMap, downLoadFileNamePInfoMap, downLoadLocalFileList);

            if (downLoadLocalFileList.size() == 0) {
                //没有文件下载成功
                log.info("$EC=>"+Thread.currentThread().getName()+" downLoad file from s3 all failed! exist!");
                return;
            }

            // TODO: 2022/1/12  logout
            System.out.println("sout => s3FullPathFolder: " + s3FullPathFolder);
            System.out.println("sout => bbFullPathFolder: " + bbFullPathFolder);
//            System.out.println("sout => downLoadFileNamePInfoMap: " + downLoadFileNamePInfoMap);
            System.out.println("sout => downLoadLocalFileList: " + downLoadLocalFileList);
//            System.out.println("sout => parsedFileS3FullPathPMessageMap: " + parsedFileS3FullPathPMessageMap);

            /** 2. group and compress files */
            Map<String, List<String>> zipFileNamePLocalFileNameListMap = new HashMap<>();
            LinkedHashMap<String, String> zipFileFullPathPZipFileNameMap = new LinkedHashMap<>();
            List<String> zipFilePathList = new ArrayList<>();
            List<List<File>> downLoadLocalFileLists = CollectionUtils.splitCollectionBySize(downLoadLocalFileList, bbZipFileCnt);

            System.out.println("sout => downLoadLocalFileLists: " + downLoadLocalFileLists);

            for (List<File> loadLocalFileList : downLoadLocalFileLists) {
                List<String> localFileNameList = loadLocalFileList.stream().map(var -> var.getName()).collect(Collectors.toList());
                String zipFileName = UUID.randomUUID().toString() + ".zip";
                String zipFileFullPath = tempFilePath + "/" + fileFolderPath + zipFileName;
                ZipFileUtils.zipFile(zipFileFullPath, loadLocalFileList);
                zipFileNamePLocalFileNameListMap.put(zipFileName, localFileNameList);
                zipFilePathList.add(zipFileFullPath);
                zipFileFullPathPZipFileNameMap.put(zipFileFullPath, zipFileName);
                loadLocalFileList.stream().forEach(var -> var.delete());
            }
//
            System.out.println("sout => zipFileFullPathPZipFileNameMap: " + zipFileFullPathPZipFileNameMap);
            System.out.println("sout => zipFilePathList: " + zipFilePathList);

            /** 3. save file to backblaze */
            //存储成功的文件名(zip)
            Set<String> savedZipFileNameSet = new HashSet<>();

            // TODO: 2022/1/28  
            int status = saveFileToBackBlazeForTest(savedZipFileNameSet, fileFolderPath,
                    zipFileFullPathPZipFileNameMap,
                    zipFileNamePLocalFileNameListMap);

            //delete zip file
            zipFilePathList.forEach(var -> new File(var).delete());

            /** 4. save b2 file info to sqs */
            List<B2FileInfoVo> b2FileInfoVos = new ArrayList<>();
            for (String savedZipFileName : savedZipFileNameSet) {
                B2FileInfoVo b2FileInfoVo = new B2FileInfoVo();
                b2FileInfoVo.setFullPathFolder(bbFullPathFolder);
                b2FileInfoVo.setZipFileName(savedZipFileName);
                List<String> localFileNameList = zipFileNamePLocalFileNameListMap.get(savedZipFileName);
                for (String localFileName : localFileNameList) {
                    B2FileInfoVo.Detail detail = b2FileInfoVo.new Detail();
                    FileInfo fileInfo = downLoadFileNamePInfoMap.get(localFileName);
                    /* s3 file info */
                    detail.setFullPathFolder(s3FullPathFolder);
                    detail.setSourceFileName(localFileName);
                    detail.setProcessStatus(BackBlazeSourceFileS3Entity.DOWNLOAD_SUCCESS);
                    detail.setSourceFileSize(fileInfo.getLength());
                    /* new b2 file info */
                    detail.setRankDate(fileInfo.getInfoData().getDate());
                    detail.setKeywordType(fileInfo.getInfoData().getKeywordType());
                    detail.setEngineId(fileInfo.getInfoData().getEngineId());
                    detail.setLanguageId(fileInfo.getInfoData().getLanguageId());
                    detail.setDevice(fileInfo.getInfoData().getDevice());
                    detail.setCityQueryName(fileInfo.getInfoData().getQueryName());
                    String keywordName = fileInfo.getInfoData().getKeywordName();
                    String rawKeywordName = fileInfo.getInfoData().getRawKeywordName();
                    detail.setKeywordName(keywordName.toLowerCase());
                    detail.setRawKeywordName(rawKeywordName.toLowerCase());
                    detail.setCdbKeywordHash(CityHashUtil.getUrlHashForString(rawKeywordName.toLowerCase()));
                    detail.setCdbKeywordMurmur3hash(MurmurHashUtils.getMurmurHash3_64(rawKeywordName.toLowerCase()));
                    detail.setFileName(keywordName);
                    b2FileInfoVo.getDetails().add(detail);
                }
                b2FileInfoVos.add(b2FileInfoVo);
            }

//            System.out.println("b2FileInfoVos: " + b2FileInfoVos.toString());

            List<List<B2FileInfoVo>> b2FileInfoVoLists = CollectionUtils.splitCollectionBySizeWithStream(b2FileInfoVos, 10);
            b2FileInfoVoLists.forEach(var->{
                String queueUrl = SQSUtils.createQueue(b2FileInfoQPrefix, amazonSQS);
                Map<String, String> message = var.stream().collect(Collectors.toMap(var1 -> Md5Util.Md5(var1.getFullPathFolder() + var1.getZipFileName()), var2 -> JSONObject.toJSONString(var2)));
                SQSUtils.sendBatchMessageToQueue(amazonSQS, queueUrl, message);
                System.out.println("create new q: " + message);
                for (B2FileInfoVo fileInfoVo : var) {
                    String zipFileName = fileInfoVo.getZipFileName();
                    System.out.println("zipFileName: " + zipFileName);
                    List<String> localFileNameList = zipFileNamePLocalFileNameListMap.get(zipFileName);
                    for (String localFileName : localFileNameList) {
                        System.out.println("message pid: " + s3BucketName + "/" + fileFolderPath + localFileName);
                        Message needDeleteMsg = parsedFileS3FullPathPMessageMap.get(s3BucketName + "/" + fileFolderPath + localFileName);
                        SQSUtils.deleteMessageFromQueue(amazonSQS, queryUrl, needDeleteMsg);
                        log.info("$EC=>delete message: " + needDeleteMsg.getMessageId() + "->" + needDeleteMsg.getBody());
                    }
                }
            });

            if (status == uploadToBBError || status == uploadToBBOverLimit) {
                //上传文件发生错误, 中断主类
                interruptStatus = true;
                log.error("$EC=>" + Thread.currentThread().getName()+" current status: " + status + ", interruptStatus: " + true);
                return;
            }
        }

        private void downloadFileFromS3(Map<String, FileInfo> parsedFileNamePInfoMap, Map<String, FileInfo> downLoadFileNamePInfoMap, List<File> downLoadLocalFileList) {
            log.info("$EC=>"+Thread.currentThread().getName()+" start download s3 file...");
            long downloadstart = System.currentTimeMillis();
            List<BackBlazeSourceFileS3Entity> downLoadFailedFileList = new ArrayList<>();
            flagFor:
            for (String key_name : parsedFileNamePInfoMap.keySet()) {
                FileInfo fileInfo = parsedFileNamePInfoMap.get(key_name);
                int downloadTryCnt = 3;
                File localFile;
                while (downloadTryCnt > 0) {
                    try {
                        S3Object o = s3.getObject(s3BucketName, key_name);
                        S3ObjectInputStream s3is = o.getObjectContent();
                        localFile = new File(tempFilePath + "/" + key_name);
                        if (!localFile.exists()) localFile.createNewFile();
                        FileOutputStream fos = new FileOutputStream(localFile);
                        byte[] read_buf = new byte[1024];
                        int read_len = 0;
                        while ((read_len = s3is.read(read_buf)) > 0) {
                            fos.write(read_buf, 0, read_len);
                        }
                        s3is.close();
                        fos.close();
                        fileInfo.setLength(localFile.length());
                        downLoadLocalFileList.add(localFile);
                        downLoadFileNamePInfoMap.put(localFile.getName(), fileInfo);
                        continue flagFor;
                    } catch (Exception e) {
                        downloadTryCnt--;
                        log.error("$EC=>"+Thread.currentThread().getName()+" download s3 file failed! key: " + key_name + ", msg: " + CollectionUtils.getErrorMsg(e));
                    }
                }
                //单个文件下载失败
                //存储表: backblaze_source_file_s3; status = 3
//                BackBlazeSourceFileS3Entity backBlazeSourceFileS3Entity = new BackBlazeSourceFileS3Entity();
//                backBlazeSourceFileS3Entity.setFullPathFolder(s3FullPathFolder);
//                backBlazeSourceFileS3Entity.setSourceFileName(fileInfo.getInfoData().getKeywordName());
//                backBlazeSourceFileS3Entity.setProcessStatus(BackBlazeSourceFileS3Entity.DOWNLOAD_FAILED);
//                downLoadFailedFileList.add(backBlazeSourceFileS3Entity);
            }
            long downloadend = System.currentTimeMillis();
//            System.out.println("s3 download time=>" + s3FullPathFolder + ": " + (downloadend - downloadstart) / 1000 + "s, cnt: " + downLoadLocalFileList.size());

//            if (downLoadFailedFileList.size() > 0) {
//                backBlazeSourceFileS3EntityDao.insertBatchIgnoreDup(downLoadFailedFileList);
//            }
        }


        private int saveFileToBackBlazeForTest(Set<String> savedZipFileNameList, String fileFolderPath,
                                        LinkedHashMap<String, String> zipFileFullPathPZipFileNameMap,
                                        Map<String, List<String>> zipFileNamePLocalFileNameListMap) {
            for (String zipFileFullPath : zipFileFullPathPZipFileNameMap.keySet()) {
                String zipFileName = zipFileFullPathPZipFileNameMap.get(zipFileFullPath);
                savedZipFileNameList.add(zipFileName);
            }
            return uploadToBBSuccess;
        }


        /**
         * @param savedZipFileNameList 保存成功的zip文件集合
         * @return 1: success; 2: error; 3: upload 403 over limit
         * 如果同时存在 2和 3, 那么返回 4
         */
        private int saveFileToBackBlaze(Set<String> savedZipFileNameList, String fileFolderPath,
                                        LinkedHashMap<String, String> zipFileFullPathPZipFileNameMap,
                                        Map<String, List<String>> zipFileNamePLocalFileNameListMap) {
            int result = uploadToBBSuccess;
            flagFor:
            for (String zipFileFullPath : zipFileFullPathPZipFileNameMap.keySet()) {
                String zipFileName = zipFileFullPathPZipFileNameMap.get(zipFileFullPath);
                PutObjectRequest putObjectRequest = null;
                FileInputStream in = null;
                // TODO: 2022/1/14
                int tryCnt = 72;
                flagWh:
                while (tryCnt > 0) {
                    try {
                        ObjectMetadata metadata = new ObjectMetadata();
                        in = new FileInputStream(zipFileFullPath);
                        byte[] bytes = IOUtils.toByteArray(in);
                        //需要指定流长度和文件类型并关闭流,不然就会 out of memory.
                        metadata.setContentLength(bytes.length);
                        metadata.setContentType(ZIP_CONTENT_TYPE);
                        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(bytes);

                        // TODO: 2022/1/18 need update to key->fileFolderPath + zipFileName/s3SummaryFileBucketName->bbBucketName/s3->bb
                        String key = "testS3ToB2Upload5/" + fileFolderPath + zipFileName;
                        putObjectRequest = new PutObjectRequest(bbBucketName, key, byteArrayInputStream, metadata);
                        bb.putObject(putObjectRequest);
                        savedZipFileNameList.add(zipFileName);
                        continue flagFor;
                    } catch (Exception e) {
//                        e.printStackTrace();
                        if (e.getMessage().contains("Error Code: 403 Forbidden")) {
                            //上传文件达到限定值, 需要等待1小时之后重试, 总共72个小时
                            // TODO: 2022/1/14
                            long sleepTime = 1000 * 60 * 60;
//                            long sleepTime = 1000 * 2;
                            log.error("$EC=>"+Thread.currentThread().getName()+" 403 Forbidden! " + (tryCnt == 0 ? "program is about to exit" : "will wait " + (sleepTime / 1000 * tryCnt) + "s"));
                            log.error("$EC=>"+Thread.currentThread().getName()+" s3 error msg: " + e.getMessage());
                            try {
                                Thread.sleep(sleepTime);
                            } catch (InterruptedException interruptedException) {
                                interruptedException.printStackTrace();
                            }
                            String subject = " BackBlaze Error Code: 403 Forbidden ";
                            String msg = " Upload failed! backblaze upload forbidden! Please check backblaze ";
                            String[] emailTo = {"<EMAIL>"};
                            sendEmail(subject, msg, emailTo);
                            tryCnt--;
                        } else {
                            //出现上传错误, 等待10秒后重新上传
                            long sleepTime = 10 * 1000;
                            try {
                                Thread.sleep(sleepTime);
                            } catch (InterruptedException interruptedException) {
                                interruptedException.printStackTrace();
                            }
                            try {
                                bb.putObject(putObjectRequest);
                                continue flagFor;
                            } catch (Exception e1) {
                                log.error("$EC=>"+Thread.currentThread().getName()+" s3 error msg 2: " + e.getMessage());
                                log.error("$EC=>"+Thread.currentThread().getName()+" error first file: " + fileFolderPath + zipFileNamePLocalFileNameListMap.get(zipFileName).get(0));
                                log.error("$EC=>"+Thread.currentThread().getName()+" current thread first file: " + fileFolderPath + zipFileNamePLocalFileNameListMap.get(zipFileName).get(0));
                                return uploadToBBError;
                            }
                        }
                    } finally {
                        try {
                            log.info("$EC=>"+Thread.currentThread().getName()+" 关闭流");
                            in.close();
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }
                }
                log.error("$EC=>"+Thread.currentThread().getName()+" overlimit first file: " + fileFolderPath + zipFileNamePLocalFileNameListMap.get(zipFileName).get(0));
                log.error("$EC=>"+Thread.currentThread().getName()+" current thread first file: " + fileFolderPath + zipFileNamePLocalFileNameListMap.get(zipFileName).get(0));
                return uploadToBBOverLimit;
            }
            return result;
        }

        private void writeErrorLineToFile(String filePath, List<String> lines) {
            synchronized (lock) {
                File file = new File(filePath);
                try {
                    FileUtils.writeLines(file, "UTF-8", lines, true);
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }

    }

//    class TransferToolThread implements Runnable {
//        /**
//         * 149 = "20200802/95-105/"
//         * 150 = "20200802/97-112/"
//         * 151 = "20200802/97-116/"
//         * 152 = "20200802/98-113/"
//         * 153 = "20200802/98-117/"
//         * 154 = "20200802/city-1-1-%20los%20angeles,california%20/"
//         * 155 = "20200802/city-1-1-01028/"
//         * 156 = "20200802/city-1-1-01109/"
//         * 157 = "20200802/city-1-1-01129/"
//         */
//        private String fileFolderPath;
//        private List<RecordData> recordDataList = new ArrayList<>();
//
//        public TransferToolThread(String fileFolderPath, List<RecordData> recordDataList) {
//            super();
//            this.fileFolderPath = fileFolderPath;
//            this.recordDataList.addAll(recordDataList);
//        }
//
//        @Override
//        public void run() {
//            // TODO: 2022/1/18
//            doGet();
//        }
//
//        public void doGet() {
//
//            if (recordDataList.size() == 0) {
//                return;
//            }
//
//            // TODO: 2022/1/7
//            //tempFilePath = "E:/work/work-seoclarity/download from s3/transfer ranking data to bb";
//            tempFilePath = "/home/<USER>/source/ewain/dev-clarity-backend-scripts/local_file/s3tobb";
//            // 文件在s3的全路径
//            String s3FullPathFolder = s3BucketName + "/" + fileFolderPath;
//            String bbFullPathFolder = bbBucketName + "/" + fileFolderPath;
//
//            File fileFolder = new File(tempFilePath + "/" + fileFolderPath);
//            if (!fileFolder.exists()) {
//                fileFolder.mkdirs();
//            }
//
//            /** parse file folder */
//            Map<String, FileInfo> parsedFileNamePInfoMap = new LinkedHashMap<>();
//            List<String> parseFolderErrorKeyList = new ArrayList<>();
//
//            for (RecordData recordData : recordDataList) {
//                String key_name = recordData.getS3FilePath();
//                FileInfo fileInfo = null;
//                try {
//                    fileInfo = getFileInfo(key_name);
//                    parsedFileNamePInfoMap.put(key_name, fileInfo);
//                } catch (Exception e) {
//                    // 有可能会遇到特别的文件夹名称, 造成解析文件名失败
//                    log.info("$EC=>"+Thread.currentThread().getName()+" parse filePath failed! key: " + key_name + ", msg: " + CollectionUtils.getErrorMsg(e));
//                    parseFolderErrorKeyList.add(key_name);
//                }
//            }
//
//            if (parseFolderErrorKeyList.size() > 0) {
//                writeErrorLineToFile(parserFolderErrorFilePath, parseFolderErrorKeyList);
//            }
//
//            if (parsedFileNamePInfoMap.size() == 0) {
//                //文件名全部解析失败, 关闭程序做检查
//                log.info("$EC=>"+Thread.currentThread().getName()+" parse filePath all failed! exist!");
//                interruptStatus = true;
//                return;
//            }
//
//            /** 1. download file from s3 */
//            Map<String, FileInfo> downLoadFileNamePInfoMap = new LinkedHashMap<>();
//            List<File> downLoadLocalFileList = new ArrayList<>();
//            downloadFileFromS3(s3FullPathFolder, parsedFileNamePInfoMap, downLoadFileNamePInfoMap, downLoadLocalFileList);
//
//            if (downLoadLocalFileList.size() == 0) {
//                //没有文件下载成功
//                log.info("$EC=>"+Thread.currentThread().getName()+" downLoad file from s3 all failed! exist!");
//                return;
//            }
//
//            // TODO: 2022/1/12  logout
////            System.out.println("sout => s3FullPathFolder: " + s3FullPathFolder);
////            System.out.println("sout => bbFullPathFolder: " + bbFullPathFolder);
////            System.out.println("sout => downLoadFileNamePInfoMap: " + downLoadFileNamePInfoMap);
////            System.out.println("sout => downLoadLocalFileList: " + downLoadLocalFileList);
//
//            /** 2. group and compress files */
//            Map<String, List<String>> zipFileNamePLocalFileNameListMap = new HashMap<>();
//            LinkedHashMap<String, String> zipFileFullPathPZipFileNameMap = new LinkedHashMap<>();
//            List<String> zipFilePathList = new ArrayList<>();
//            List<List<File>> downLoadLocalFileLists = CollectionUtils.splitCollectionBySize(downLoadLocalFileList, bbZipFileCnt);
//
////            System.out.println("sout => downLoadLocalFileLists: " + downLoadLocalFileLists);
//
//            for (List<File> loadLocalFileList : downLoadLocalFileLists) {
//                List<String> localFileNameList = loadLocalFileList.stream().map(var -> var.getName()).collect(Collectors.toList());
//                String zipFileName = System.currentTimeMillis() + ".zip";
//                String zipFileFullPath = tempFilePath + "/" + fileFolderPath + zipFileName;
//                ZipFileUtils.zipFile(zipFileFullPath, loadLocalFileList);
//                zipFileNamePLocalFileNameListMap.put(zipFileName, localFileNameList);
//                zipFilePathList.add(zipFileFullPath);
//                zipFileFullPathPZipFileNameMap.put(zipFileFullPath, zipFileName);
//                loadLocalFileList.stream().forEach(var -> var.delete());
//            }
////
////            System.out.println("sout => zipFileFullPathPZipFileNameMap: " + zipFileFullPathPZipFileNameMap);
////            System.out.println("sout => zipFilePathList: " + zipFilePathList);
//
//            /** 3. save file to backblaze */
//            //存储成功的文件名(zip)
//            Set<String> savedZipFileNameSet = new HashSet<>();
//
//            int status = saveFileToBackBlaze(savedZipFileNameSet,
//                    zipFileFullPathPZipFileNameMap,
//                    zipFileNamePLocalFileNameListMap);
//
//            //delete zip file
//            zipFilePathList.forEach(var -> new File(var).delete());
//
////            if (zipFileNamePLocalFileNameListMap.size() > 0 &&  == 0) {
////                log.info("$EC=>"+Thread.currentThread().getName()+" save zip file to bb all failed! exist!");
////                //没有压缩文件上传成功
////                return;
////            }
//
//            /** 4. save file info to db */
//            if (savedZipFileNameSet.size() > 0) {
//                BackBlazeMetaFolderEntity dbBackBlazeMetaFolderEntity = saveBackblazeMetaFolder(bbFullPathFolder);
//                Map<String, Long> savedBackBlazeMetaFileNamePIdMap = saveBackblazeMetaFile(savedZipFileNameSet, dbBackBlazeMetaFolderEntity);
//                Map<String, String> localFileNamePZipFileNameMap = getLocalFileNamePZipFileNameMap(zipFileNamePLocalFileNameListMap, savedBackBlazeMetaFileNamePIdMap);
//                Map<String, Long> savedBackBlazeSourceFileNamePId = saveBackblazeSourceFileS3(s3FullPathFolder, downLoadFileNamePInfoMap, zipFileNamePLocalFileNameListMap, savedZipFileNameSet);
//                saveBackblazeKeywordStore(downLoadFileNamePInfoMap, zipFileNamePLocalFileNameListMap, savedZipFileNameSet, savedBackBlazeMetaFileNamePIdMap, localFileNamePZipFileNameMap, savedBackBlazeSourceFileNamePId);
//            }
//
//            if (status == uploadToBBError || status == uploadToBBOverLimit) {
//                //上传文件发生错误, 中断主类
//                interruptStatus = true;
//                log.error("$EC=>"+Thread.currentThread().getName()+" current status: " + status + ", interruptStatus: " + true);
//                return;
//            }
//        }
//
//        private void downloadFileFromS3(String s3FullPathFolder, Map<String, FileInfo> parsedFileNamePInfoMap, Map<String, FileInfo> downLoadFileNamePInfoMap, List<File> downLoadLocalFileList) {
//            log.info("$EC=>"+Thread.currentThread().getName()+" start download s3 file...");
//            long downloadstart = System.currentTimeMillis();
//            List<BackBlazeSourceFileS3Entity> downLoadFailedFileList = new ArrayList<>();
//            flagFor:
//            for (String key_name : parsedFileNamePInfoMap.keySet()) {
//                FileInfo fileInfo = parsedFileNamePInfoMap.get(key_name);
//                int downloadTryCnt = 3;
//                File localFile;
//                while (downloadTryCnt > 0) {
//                    try {
//                        S3Object o = s3.getObject(s3BucketName, key_name);
//                        S3ObjectInputStream s3is = o.getObjectContent();
//                        localFile = new File(tempFilePath + "/" + key_name);
//                        if (!localFile.exists()) localFile.createNewFile();
//                        FileOutputStream fos = new FileOutputStream(localFile);
//                        byte[] read_buf = new byte[1024];
//                        int read_len = 0;
//                        while ((read_len = s3is.read(read_buf)) > 0) {
//                            fos.write(read_buf, 0, read_len);
//                        }
//                        s3is.close();
//                        fos.close();
//                        fileInfo.setLength(localFile.length());
//                        downLoadLocalFileList.add(localFile);
//                        downLoadFileNamePInfoMap.put(localFile.getName(), fileInfo);
//                        continue flagFor;
//                    } catch (Exception e) {
//                        downloadTryCnt--;
//                        log.error("$EC=>"+Thread.currentThread().getName()+" download s3 file failed! key: " + key_name + ", msg: " + CollectionUtils.getErrorMsg(e));
//                    }
//                }
//                //单个文件下载失败
//                //存储表: backblaze_source_file_s3; status = 3
//                BackBlazeSourceFileS3Entity backBlazeSourceFileS3Entity = new BackBlazeSourceFileS3Entity();
//                backBlazeSourceFileS3Entity.setFullPathFolder(s3FullPathFolder);
//                backBlazeSourceFileS3Entity.setSourceFileName(fileInfo.getInfoData().getKeywordName());
//                backBlazeSourceFileS3Entity.setProcessStatus(BackBlazeSourceFileS3Entity.DOWNLOAD_FAILED);
//                downLoadFailedFileList.add(backBlazeSourceFileS3Entity);
//            }
//            long downloadend = System.currentTimeMillis();
////            System.out.println("s3 download time=>" + s3FullPathFolder + ": " + (downloadend - downloadstart) / 1000 + "s, cnt: " + downLoadLocalFileList.size());
//
//            if (downLoadFailedFileList.size() > 0) {
//                backBlazeSourceFileS3EntityDao.insertBatchIgnoreDup(downLoadFailedFileList);
//            }
//        }
//
//        private void writeErrorLineToFile(String filePath, List<String> lines) {
//            synchronized (lock) {
//                File file = new File(filePath);
//                try {
//                    FileUtils.writeLines(file, "UTF-8", lines, true);
//                } catch (IOException e) {
//                    e.printStackTrace();
//                }
//            }
//        }
//
//        /**
//         * @param savedZipFileNameList 保存成功的zip文件集合
//         * @return 1: success; 2: error; 3: upload 403 over limit
//         * 如果同时存在 2和 3, 那么返回 4
//         */
//        private int saveFileToBackBlaze(Set<String> savedZipFileNameList,
//                                        LinkedHashMap<String, String> zipFileFullPathPZipFileNameMap,
//                                        Map<String, List<String>> zipFileNamePLocalFileNameListMap) {
//            int result = uploadToBBSuccess;
//            String firstZipFileName = null;
//            flagFor:
//            for (String zipFileFullPath : zipFileFullPathPZipFileNameMap.keySet()) {
//                String zipFileName = zipFileFullPathPZipFileNameMap.get(zipFileFullPath);
//                if (firstZipFileName == null) {
//                    firstZipFileName = zipFileName;
//                }
//
//                PutObjectRequest putObjectRequest = null;
//                FileInputStream in = null;
//                // TODO: 2022/1/14
//                int tryCnt = 72;
//                flagWh:
//                while (tryCnt > 0) {
//                    try {
//                        ObjectMetadata metadata = new ObjectMetadata();
//                        in = new FileInputStream(zipFileFullPath);
//                        byte[] bytes = IOUtils.toByteArray(in);
//                        //需要指定流长度和文件类型并关闭流,不然就会 out of memory.
//                        metadata.setContentLength(bytes.length);
//                        metadata.setContentType(ZIP_CONTENT_TYPE);
//                        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(bytes);
//
//                        // TODO: 2022/1/18 need update to key->fileFolderPath + zipFileName/s3SummaryFileBucketName->bbBucketName/s3->bb
//                        String key = "testS3ToB2Upload4/" + fileFolderPath + zipFileName;
//                        putObjectRequest = new PutObjectRequest(bbBucketName, key, byteArrayInputStream, metadata);
//                        bb.putObject(putObjectRequest);
//                        savedZipFileNameList.add(zipFileName);
//                        continue flagFor;
//                    } catch (Exception e) {
////                        e.printStackTrace();
//                        if (e.getMessage().contains("Error Code: 403 Forbidden")) {
//                            //上传文件达到限定值, 需要等待1小时之后重试, 总共72个小时
//                            // TODO: 2022/1/14
//                            long sleepTime = 1000 * 60 * 60;
////                            long sleepTime = 1000 * 2;
//                            log.error("$EC=>"+Thread.currentThread().getName()+" 403 Forbidden! " + (tryCnt == 0 ? "program is about to exit" : "will wait " + (sleepTime / 1000 * tryCnt) + "s"));
//                            log.error("$EC=>"+Thread.currentThread().getName()+" s3 error msg: " + e.getMessage());
//                            try {
//                                Thread.sleep(sleepTime);
//                            } catch (InterruptedException interruptedException) {
//                                interruptedException.printStackTrace();
//                            }
//                            String subject = " BackBlaze Error Code: 403 Forbidden ";
//                            String msg = " Upload failed! backblaze upload forbidden! Please check backblaze ";
//                            String[] emailTo = {"<EMAIL>"};
//                            sendEmail(subject, msg, emailTo);
//                            tryCnt--;
//                        } else {
//                            //出现上传错误, 等待10秒后重新上传
//                            long sleepTime = 10 * 1000;
//                            try {
//                                Thread.sleep(sleepTime);
//                            } catch (InterruptedException interruptedException) {
//                                interruptedException.printStackTrace();
//                            }
//                            try {
//                                bb.putObject(putObjectRequest);
//                                continue flagFor;
//                            } catch (Exception e1) {
//                                log.error("$EC=>"+Thread.currentThread().getName()+" s3 error msg 2: " + e.getMessage());
//                                log.error("$EC=>"+Thread.currentThread().getName()+" error first file: " + fileFolderPath + zipFileNamePLocalFileNameListMap.get(zipFileName).get(0));
//                                log.error("$EC=>"+Thread.currentThread().getName()+" current thread first file: " + fileFolderPath + zipFileNamePLocalFileNameListMap.get(zipFileName).get(0));
//                                return uploadToBBError;
//                            }
//                        }
//                    } finally {
//                        try {
//                            log.info("$EC=>"+Thread.currentThread().getName()+" 关闭流");
//                            in.close();
//                        } catch (IOException e) {
//                            e.printStackTrace();
//                        }
//                    }
//                }
//                log.error("$EC=>"+Thread.currentThread().getName()+" overlimit first file: " + fileFolderPath + zipFileNamePLocalFileNameListMap.get(zipFileName).get(0));
//                log.error("$EC=>"+Thread.currentThread().getName()+" current thread first file: " + fileFolderPath + zipFileNamePLocalFileNameListMap.get(zipFileName).get(0));
//                return uploadToBBOverLimit;
//            }
//            return result;
//        }
//
//        @NotNull
//        private Map<String, String> getLocalFileNamePZipFileNameMap(Map<String, List<String>> zipFileNamePLocalFileNameListMap, Map<String, Long> savedBackBlazeMetaFileNamePIdMap) {
//            Map<String, String> localFileNamePZipFileNameMap = new HashMap<>();
//            for (String zipFileName : savedBackBlazeMetaFileNamePIdMap.keySet()) {
//                List<String> localFileNameList = zipFileNamePLocalFileNameListMap.get(zipFileName);
//                for (String localFileName : localFileNameList) {
//                    localFileNamePZipFileNameMap.put(localFileName, zipFileName);
//                }
//            }
//            // TODO: 2022/1/12  logout
////            System.out.println("sout => localFileNamePZipFileNameMap: " + localFileNamePZipFileNameMap);
//            return localFileNamePZipFileNameMap;
//        }
//
//        private BackBlazeMetaFolderEntity saveBackblazeMetaFolder(String bbFullPathFolder) {
//            // //插入表`backblaze_meta_folder`并取回数据
//            // folder表不直接指向数据, backblaze_meta_folder表中的数据存在冗余不是问题
//            BackBlazeMetaFolderEntity dbBackBlazeMetaFolderEntity = backBlazeMetaFolderEntityDao.findByUniqueKey(bbFullPathFolder);
//            if (dbBackBlazeMetaFolderEntity == null) {
//                BackBlazeMetaFolderEntity backBlazeMetaFolderEntity = new BackBlazeMetaFolderEntity();
//                backBlazeMetaFolderEntity.setFullPathFolder(bbFullPathFolder);
//                List<BackBlazeMetaFolderEntity> list = new ArrayList<>();
//                list.add(backBlazeMetaFolderEntity);
//                backBlazeMetaFolderEntityDao.insertBatchIgnoreDup(list);
//                dbBackBlazeMetaFolderEntity = backBlazeMetaFolderEntityDao.findByUniqueKey(bbFullPathFolder);
//            }
//
//            // TODO: 2022/1/12  logout
////            System.out.println("sout => dbBackBlazeMetaFolderEntity: " + dbBackBlazeMetaFolderEntity);
//            return dbBackBlazeMetaFolderEntity;
//        }
//
//        @NotNull
//        private Map<String, Long> saveBackblazeMetaFile(Set<String> savedZipFileNameSet, BackBlazeMetaFolderEntity dbBackBlazeMetaFolderEntity) {
//            List<BackBlazeMetaFileEntity> needInsertBackBlazeMetaFileList = new ArrayList<>();
//            //构建需要插入`backblaze_meta_file`的列表
//            for (String savedZipFileName : savedZipFileNameSet) {
//                BackBlazeMetaFileEntity backBlazeMetaFileEntity = new BackBlazeMetaFileEntity();
//                backBlazeMetaFileEntity.setMetaFolderId(dbBackBlazeMetaFolderEntity.getId());
//                backBlazeMetaFileEntity.setZipFileName(savedZipFileName);
//                needInsertBackBlazeMetaFileList.add(backBlazeMetaFileEntity);
//            }
//
//            // TODO: 2022/1/12  logout
////            System.out.println("sout => needInsertBackBlazeMetaFileList: " + needInsertBackBlazeMetaFileList);
//
//            //插入表`backblaze_meta_file`并取回数据
//            backBlazeMetaFileEntityDao.insertBatchIgnoreDup(needInsertBackBlazeMetaFileList);
//            List<List<String>> savedZipFileNameLists = CollectionUtils.splitCollectionBySize(new ArrayList<>(savedZipFileNameSet), MYSQL_DB_QUERY_WITH_STRING);
//            List<BackBlazeMetaFileEntity> savedBackBlazeMetaFileList = new ArrayList<>();
//            for (List<String> zipFileNameList : savedZipFileNameLists) {
//                List<BackBlazeMetaFileEntity> result = backBlazeMetaFileEntityDao.findListByUniqueKey(dbBackBlazeMetaFolderEntity.getId(), zipFileNameList);
//                if (result != null && result.size() > 0) {
//                    savedBackBlazeMetaFileList.addAll(result);
//                }
//            }
//            Map<String, Long> savedBackBlazeMetaFileNamePIdMap = savedBackBlazeMetaFileList.stream().collect(Collectors.toMap(var1 -> var1.getZipFileName(), var2 -> var2.getId()));
//
//            // TODO: 2022/1/12  logout
////            System.out.println("sout => savedBackBlazeMetaFileList: " + savedBackBlazeMetaFileList);
////            System.out.println("sout => savedBackBlazeMetaFileNamePIdMap: " + savedBackBlazeMetaFileNamePIdMap);
//            return savedBackBlazeMetaFileNamePIdMap;
//        }
//
//        @NotNull
//        private Map<String, Long> saveBackblazeSourceFileS3(String s3FullPathFolder, Map<String, FileInfo> downLoadFileNamePInfoMap, Map<String, List<String>> zipFileNamePLocalFileNameListMap, Set<String> savedZipFileNameSet) {
//            //构建需要插入`backblaze_source_file_s3`的列表 (失败的数据也需要存根, 一般在catch中)
//            List<BackBlazeSourceFileS3Entity> needInsertBackBlazeSourceFileS3List = new ArrayList<>();
//            for (String savedZipFileName : savedZipFileNameSet) {
//                List<String> localFileNameList = zipFileNamePLocalFileNameListMap.get(savedZipFileName);
//                for (String localFileName : localFileNameList) {
//                    FileInfo fileInfo = downLoadFileNamePInfoMap.get(localFileName);
//                    BackBlazeSourceFileS3Entity backBlazeSourceFileS3Entity = new BackBlazeSourceFileS3Entity();
//                    backBlazeSourceFileS3Entity.setFullPathFolder(s3FullPathFolder);
//                    backBlazeSourceFileS3Entity.setSourceFileName(fileInfo.getInfoData().getKeywordName());
//                    backBlazeSourceFileS3Entity.setProcessStatus(BackBlazeSourceFileS3Entity.DOWNLOAD_SUCCESS);
//                    backBlazeSourceFileS3Entity.setSourceFileSize(fileInfo.getLength());
//                    needInsertBackBlazeSourceFileS3List.add(backBlazeSourceFileS3Entity);
//                }
//            }
//
//            // TODO: 2022/1/12  logout
////            System.out.println("sout => needInsertBackBlazeSourceFileS3List: " + needInsertBackBlazeSourceFileS3List);
//
//            //插入表`backblaze_source_file_s3`数据并取回
//            backBlazeSourceFileS3EntityDao.insertBatchIgnoreDup(needInsertBackBlazeSourceFileS3List);
//            List<String> savedS3SourceFileNameList = needInsertBackBlazeSourceFileS3List.stream().map(var -> var.getSourceFileName()).collect(Collectors.toList());
//
//            List<BackBlazeSourceFileS3Entity> savedBackBlazeSourceFileS3List = new ArrayList<>();
//            List<List<String>> savedS3SourceFileNameLists = CollectionUtils.splitCollectionBySize(savedS3SourceFileNameList, MYSQL_DB_QUERY_WITH_STRING);
//            for (List<String> s3SourceFileNameList : savedS3SourceFileNameLists) {
//                List<BackBlazeSourceFileS3Entity> result = backBlazeSourceFileS3EntityDao.findListByUniqueKey(s3FullPathFolder, s3SourceFileNameList);
//                if (result != null && result.size() > 0) {
//                    savedBackBlazeSourceFileS3List.addAll(result);
//                }
//            }
//            Map<String, Long> savedBackBlazeSourceFileNamePId = savedBackBlazeSourceFileS3List.stream().collect(Collectors.toMap(var1 -> var1.getSourceFileName(), var2 -> var2.getId()));
//
//            // TODO: 2022/1/12  logout
////            System.out.println("sout => savedBackBlazeSourceFileS3List: " + savedBackBlazeSourceFileS3List);
////            System.out.println("sout => savedBackBlazeSourceFileNamePId: " + savedBackBlazeSourceFileNamePId);
//            return savedBackBlazeSourceFileNamePId;
//        }
//
//        private void saveBackblazeKeywordStore(Map<String, FileInfo> downLoadFileNamePInfoMap, Map<String, List<String>> zipFileNamePLocalFileNameListMap, Set<String> savedZipFileNameSet, Map<String, Long> savedBackBlazeMetaFileNamePIdMap, Map<String, String> localFileNamePZipFileNameMap, Map<String, Long> savedBackBlazeSourceFileNamePId) {
//            //构建需要插入`backblaze_keyword_store`的列表
//            List<BackBlazeKeywordStoreEntity> needInsertBackBlazeKeywordStoreList = new ArrayList<>();
//            for (String savedZipFileName : savedZipFileNameSet) {
//                List<String> localFileNameList = zipFileNamePLocalFileNameListMap.get(savedZipFileName);
//                for (String localFileName : localFileNameList) {
//                    FileInfo fileInfo = downLoadFileNamePInfoMap.get(localFileName);
//
//                    BackBlazeKeywordStoreEntity backBlazeKeywordStoreEntity = new BackBlazeKeywordStoreEntity();
//                    backBlazeKeywordStoreEntity.setRankDate(fileInfo.getInfoData().getDate());
//                    backBlazeKeywordStoreEntity.setKeywordType(fileInfo.getInfoData().getKeywordType());
//                    backBlazeKeywordStoreEntity.setEngineId(fileInfo.getInfoData().getEngineId());
//                    backBlazeKeywordStoreEntity.setLanguageId(fileInfo.getInfoData().getLanguageId());
//                    backBlazeKeywordStoreEntity.setDevice(fileInfo.getInfoData().getDevice());
//                    backBlazeKeywordStoreEntity.setCityQueryName(fileInfo.getInfoData().getQueryName());
//                    String keywordName = fileInfo.getInfoData().getKeywordName();
//                    String rawKeywordName = fileInfo.getInfoData().getRawKeywordName();
//                    backBlazeKeywordStoreEntity.setKeywordName(keywordName.toLowerCase());
//                    backBlazeKeywordStoreEntity.setRawKeywordName(rawKeywordName.toLowerCase());
//                    backBlazeKeywordStoreEntity.setCdbKeywordHash(CityHashUtil.getUrlHashForString(rawKeywordName.toLowerCase()));
//                    backBlazeKeywordStoreEntity.setCdbKeywordMurmur3hash(MurmurHashUtils.getMurmurHash3_64(rawKeywordName.toLowerCase()));
//                    backBlazeKeywordStoreEntity.setMetaFileId(savedBackBlazeMetaFileNamePIdMap.get(localFileNamePZipFileNameMap.get(keywordName)));
//                    backBlazeKeywordStoreEntity.setFileName(keywordName);
//                    backBlazeKeywordStoreEntity.setSourceFileId(savedBackBlazeSourceFileNamePId.get(keywordName));
//                    needInsertBackBlazeKeywordStoreList.add(backBlazeKeywordStoreEntity);
//                }
//
//            }
//            backBlazeKeywordStoreEntityDao.insertBatchIgnoreDup(needInsertBackBlazeKeywordStoreList);
//
//            // TODO: 2022/1/12  logout
////            System.out.println("sout => needInsertBackBlazeKeywordStoreList: " + needInsertBackBlazeKeywordStoreList);
//        }
//    }

    public void sendEmail(String subject, String message, String[] emailTo) {
        try {
            Map<String, Object> reportMap = new HashMap<String, Object>();
            reportMap.put("userName", "all");
            reportMap.put("dateString", DateFormatUtils.format(new Date(), "MM/dd/yyyy"));
            reportMap.put("startTime", DateFormatUtils.format(new Date(), "MM/dd/yyyy HH:mm:ss"));
            reportMap.put("endTime", DateFormatUtils.format(new Date(), "MM/dd/yyyy HH:mm:ss"));
            reportMap.put("title", " transfer files from S3 to BB ");
            reportMap.put("info", "");
            if (null == message) {
                message = "success!";
            }
            reportMap.put("errormessage", message);
            emailSenderComponent.sendMimeMultiPartMailAndBcc(emailTo, null, subject, "mail_common.txt", "mail_common.html", reportMap);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Data
    class FileInfo {
        //s3文件路径, 不包含桶  eg: 20200802/255-30/online+poker
        private String key;
        private Boolean status;
        //file size
        private long length;
        //s3文件路径数组,不包含桶, 不包含文件名 eg: ["20200802","255-30"]
        private List<String> pathList = new ArrayList<>();
        private InfoData infoData;

        @Data
        class InfoData {
            private int date;
            private String keywordName;
            private String rawKeywordName;
            private int keywordType;
            private int engineId;
            private int languageId;
            private String device;
            private String queryName;
        }
    }

    /**
     * @param key eg:
     *                 "20200802/95-105/1.zip"
     *                 "20200802/mobile-98-113/2.zip"
     *                 "20200802/city-25-9-01028/3.zip"
     *                 "20200802/mobile-city-13-16-01850/4.zip"
     *
     *                 20200322/city-1-1-winston-salem,%20nc/dr+taking+new+patients+near+me
     *
     *                 <p>
     *                 解析特殊文件路径!!!
     *                 "20200802/95-105/1+city.zip",
     *                 "20200802/mobile-98-113/2+mobile+city+mobile.zip",
     *                 "20200802/city-25-9-01028/3+city+mobile.zip",
     *                 "20200802/mobile-city-13-16-01850/4+city+mobile.zip",
     *                 "20200802/fpr-1-1/5+city+mobile.zip",
     *                 "20200802/fpr-mobile-1-1/6+city.zip",
     *                 "20200802/fpr-city-25-9-01028/61+city+mobile.zip",//
     *                 "20200802/fpr-mobile-city-13-16-01850/62+city+city+mobile.zip",//
     *                 "20200802/job-119-1/7+city+mobile.zip",
     *                 "20200802/job-mobile-119-1/8+city+mobile.zip",
     *                 "20200802/job-city-119-1-chicago,%20il/81+city+mobile.zip",//
     *                 "20200802/job-mobile-city-119-1-chicago,%20il/9+city+mobile.zip",
     *                 "20200802/ll-v2-1-1/156995/10+city+mobile.zip",
     *                 "20200322/city-1-1-winston-salem,%20nc/primary+health+near+me+mobile+city+mobile",
     *                 "20200802/ll-14-15/11+city+mobile.zip",
     *                 "20200802/fpr-ved-1-1/00181+city+city+mobile.zip",
     *                 "20200802/fpr-ved-mobile-1-1/00181+city+city+mobile.zip"
     *
     * @return
     * @throws Exception
     */
    private FileInfo getFileInfo(String key) throws Exception {

        int i = StringUtils.indexOf(key, '/');
        Integer date = Integer.parseInt(key.substring(0, i));
        String key_remove_dateStr = key.substring(i + 1);

        String keywordName;
        String engineIdStr;
        String languageIdStr;
        String queryName;

        String[] key_remove_dateStrArr = key_remove_dateStr.split("/");
        keywordName = key_remove_dateStrArr[key_remove_dateStrArr.length-1];
        key_remove_dateStr = key_remove_dateStr.replace("/" + keywordName, "");

        String device = key_remove_dateStr.contains("mobile") ? "m" : "d";
        Integer keywordType = Integer.parseInt(key_remove_dateStr.contains("city") ? "2" : "1");

        String[] engineSpArr = key_remove_dateStrArr[0].split("-");
        boolean notNumFlag = false;
        for (String str : engineSpArr) {
            if (!NumberUtils.isNumber(str)) {
                notNumFlag = true;
            }
        }
        if (engineSpArr.length == 2 && !notNumFlag) {
            engineIdStr = engineSpArr[0];
            languageIdStr = engineSpArr[1];
            queryName = "0";
        } else if (key_remove_dateStr.startsWith("mobile-") && !key_remove_dateStr.contains("city")) {
            // "20200802/mobile-98-113/2.zip"
            String engine = StringUtils.remove(key_remove_dateStr, "mobile-");
            String[] engineArr = engine.split("-");
            engineIdStr = engineArr[0];
            languageIdStr = engineArr[1];
            queryName = "0";
        } else if (key_remove_dateStr.startsWith("city-")) {
            // "20200802/city-25-9-01028/3.zip" => city-25-9-01028/3.zip
            String engine = StringUtils.remove(key_remove_dateStr, "city-");
            int i1 = engine.indexOf('-');
            engineIdStr = engine.substring(0, i1);
            engine = engine.substring(i1 + 1);
            int i2 = engine.indexOf('-');
            languageIdStr = engine.substring(0, i2);
            queryName = engine.substring(i2 + 1);
        } else if (key_remove_dateStr.startsWith("mobile-city-")) {
            // "20200802/mobile-city-13-16-01850/4.zip"
            String engine = StringUtils.remove(key_remove_dateStr, "mobile-city-");
            int i1 = engine.indexOf('-');
            engineIdStr = engine.substring(0, i1);
            engine = engine.substring(i1 + 1);
            int i2 = engine.indexOf('-');
            languageIdStr = engine.substring(0, i2);
            queryName = engine.substring(i2 + 1);
        } else if (key_remove_dateStr.startsWith("fpr-")) {
            if (key_remove_dateStr.startsWith("fpr-mobile-") && !key_remove_dateStr.contains("city")) {
                // "20200802/fpr-mobile-1-1/6.zip"
                String engine = StringUtils.remove(key_remove_dateStr, "fpr-mobile-");
                String[] engineArr = engine.split("-");
                engineIdStr = engineArr[0];
                languageIdStr = engineArr[1];
                queryName = "0";
            }else if (key_remove_dateStr.startsWith("fpr-mobile-city-")) {
                // "20200802/fpr-mobile-city-13-16-01850/62.zip"
                String engine = StringUtils.remove(key_remove_dateStr, "fpr-mobile-city-");
                int i1 = engine.indexOf('-');
                engineIdStr = engine.substring(0, i1);
                engine = engine.substring(i1 + 1);
                int i2 = engine.indexOf('-');
                languageIdStr = engine.substring(0, i2);
                queryName = engine.substring(i2 + 1);
            }else if (key_remove_dateStr.startsWith("fpr-city-")) {
                // "20200802/fpr-city-25-9-01028/61.zip"
                String engine = StringUtils.remove(key_remove_dateStr, "fpr-city-");
                int i1 = engine.indexOf('-');
                engineIdStr = engine.substring(0, i1);
                engine = engine.substring(i1 + 1);
                int i2 = engine.indexOf('-');
                languageIdStr = engine.substring(0, i2);
                queryName = engine.substring(i2 + 1);
            } else if (key_remove_dateStr.startsWith("fpr-ved-mobile-")) {
                // "20200802/fpr-ved-mobile-5-7/66+city.zip"
                String engine = StringUtils.remove(key_remove_dateStr, "fpr-ved-mobile-");
                String[] engineArr = engine.split("-");
                engineIdStr = engineArr[0];
                languageIdStr = engineArr[1];
                queryName = "0";
            } else if (key_remove_dateStr.startsWith("fpr-ved-")) {
                // "20200802/fpr-ved-5-7/65+city.zip"
                String engine = StringUtils.remove(key_remove_dateStr, "fpr-ved-");
                String[] engineArr = engine.split("-");
                engineIdStr = engineArr[0];
                languageIdStr = engineArr[1];
                queryName = "0";
            } else if (key_remove_dateStr.startsWith("fpr-")
                    && !key_remove_dateStr.contains("city")
                    && !key_remove_dateStr.contains("mobile")) {
                // "20200802/fpr-1-1/5.zip"
                String engine = StringUtils.remove(key_remove_dateStr, "fpr-");
                String[] engineArr = engine.split("-");
                engineIdStr = engineArr[0];
                languageIdStr = engineArr[1];
                queryName = "0";
            }else {
                throw new Exception("parse key exception");
            }
        } else if (key_remove_dateStr.startsWith("job-")) {
            if (key_remove_dateStr.startsWith("job-mobile-") && !key_remove_dateStr.contains("city")) {
                // "20200802/job-mobile-1-1/6.zip"
                String engine = StringUtils.remove(key_remove_dateStr, "job-mobile-");
                String[] engineArr = engine.split("-");
                engineIdStr = engineArr[0];
                languageIdStr = engineArr[1];
                queryName = "0";
            }else if (key_remove_dateStr.startsWith("job-mobile-") && key_remove_dateStr.contains("city")) {
                // "20200802/job-mobile-city-13-16-01850/62.zip"
                String engine = StringUtils.remove(key_remove_dateStr, "job-mobile-city-");
                int i1 = engine.indexOf('-');
                engineIdStr = engine.substring(0, i1);
                engine = engine.substring(i1 + 1);
                int i2 = engine.indexOf('-');
                languageIdStr = engine.substring(0, i2);
                queryName = engine.substring(i2 + 1);
            }else if (key_remove_dateStr.startsWith("job-city-")) {
                // "20200802/job-city-25-9-01028/61.zip"
                String engine = StringUtils.remove(key_remove_dateStr, "job-city-");
                int i1 = engine.indexOf('-');
                engineIdStr = engine.substring(0, i1);
                engine = engine.substring(i1 + 1);
                int i2 = engine.indexOf('-');
                languageIdStr = engine.substring(0, i2);
                queryName = engine.substring(i2 + 1);
            }else if (key_remove_dateStr.startsWith("job-")
                    && !key_remove_dateStr.contains("city")
                    && !key_remove_dateStr.contains("mobile")) {
                // "20200802/job-1-1/5.zip"
                String engine = StringUtils.remove(key_remove_dateStr, "job-");
                String[] engineArr = engine.split("-");
                engineIdStr = engineArr[0];
                languageIdStr = engineArr[1];
                queryName = "0";
            }else {
                throw new Exception("parse key exception");
            }
        } else if (key_remove_dateStr.startsWith("ll-v2-")) {
            // "20200802/ll-v2-1-1/156995/10.zip"
            String[] strArr = key_remove_dateStr.split("/");
            queryName = strArr[1];
            String engine = strArr[0];
            engine = StringUtils.remove(engine, "ll-v2-");
            String[] engineArr = engine.split("-");
            engineIdStr = engineArr[0];
            languageIdStr = engineArr[1];
        } else if (key_remove_dateStr.startsWith("ll-") && !key_remove_dateStr.contains("v2")) {
            // "20200802/ll-14-15/11.zip"
            String engine = StringUtils.remove(key_remove_dateStr, "ll-");
            String[] engineArr = engine.split("-");
            engineIdStr = engineArr[0];
            languageIdStr = engineArr[1];
            queryName = "0";
        } else {
            throw new Exception("parse key exception");
        }

        FileInfo fileInfo = new FileInfo();
        String[] fullPath_arr = key.split("/");
        for (int m = 0; m < fullPath_arr.length - 1; m++) {
            fileInfo.getPathList().add(fullPath_arr[m]);
        }

        Integer engineId = Integer.parseInt(engineIdStr);
        Integer languageId = Integer.parseInt(languageIdStr);

        if (date == null ||
                keywordName == null || keywordName == "" ||
                keywordType == null ||
                engineId == null ||
                languageId == null ||
                device == null || device == "" ||
                queryName == null || queryName == "" ||
                fullPath_arr == null || fullPath_arr.length == 0

        ) {
            System.out.println("无法解析文件信息!");
            throw new Exception("无法解析文件信息!");
        }
        String rawKeywordName = StringEscapeUtils.unescapeHtml(StringEscapeUtils.escapeHtml(URLDecoder.decode(keywordName, "UTF-8")));
        fileInfo.setKey(key);
        fileInfo.setStatus(true);
        FileInfo.InfoData infoData = fileInfo.new InfoData();
        infoData.setDate(date);
        infoData.setKeywordName(keywordName);
        infoData.setRawKeywordName(rawKeywordName);
        infoData.setKeywordType(keywordType);
        infoData.setEngineId(engineId);
        infoData.setLanguageId(languageId);
        infoData.setDevice(device);
        infoData.setQueryName(queryName);
        fileInfo.setInfoData(infoData);
        return fileInfo;
    }



    @Data
    static class Manifest {
        private String sourceBucket;
        private List<Key> files = new ArrayList();

        @Data
        class Key {
            private String key;
            private int size;
            private String md5checksum;
        }
    }

    @Data
    static class RecordData {
        private String bucketName;
        private String s3FilePath;
        private String s3StorageType;
    }

    /**
     * for test
     */
    private void processForTest() {

        BackBlazeKeywordStoreEntity backBlazeKeywordStoreEntity1 = new BackBlazeKeywordStoreEntity();
        backBlazeKeywordStoreEntity1.setRankDate(20220107);
        backBlazeKeywordStoreEntity1.setKeywordType(1);
        backBlazeKeywordStoreEntity1.setEngineId(1);
        backBlazeKeywordStoreEntity1.setLanguageId(1);
        backBlazeKeywordStoreEntity1.setDevice("d");
        backBlazeKeywordStoreEntity1.setCityQueryName(300 + "");
        String backBlazeKeywordStoreEntity1KName = "TestKeywordName5_12";
        backBlazeKeywordStoreEntity1.setKeywordName(backBlazeKeywordStoreEntity1KName.toLowerCase());
        backBlazeKeywordStoreEntity1.setRawKeywordName(backBlazeKeywordStoreEntity1KName.toLowerCase());
        backBlazeKeywordStoreEntity1.setCdbKeywordHash(CityHashUtil.getUrlHashForString(backBlazeKeywordStoreEntity1KName.toLowerCase()));
        backBlazeKeywordStoreEntity1.setCdbKeywordMurmur3hash(MurmurHashUtils.getMurmurHash3_64(backBlazeKeywordStoreEntity1KName.toLowerCase()));
        backBlazeKeywordStoreEntity1.setMetaFileId(12345L);
        backBlazeKeywordStoreEntity1.setFileName("TestKeywordName1.zip");
        backBlazeKeywordStoreEntity1.setSourceFileId(23456L);
        List<BackBlazeKeywordStoreEntity> backBlazeKeywordStoreEntityList = new ArrayList<>();
        backBlazeKeywordStoreEntityList.add(backBlazeKeywordStoreEntity1);
        backBlazeKeywordStoreEntityDao.insertBatchIgnoreDup(backBlazeKeywordStoreEntityList);


//        BackBlazeKeywordStoreEntity backBlazeKeywordStoreEntity2 = new BackBlazeKeywordStoreEntity();
//        backBlazeKeywordStoreEntity2.setRankDate(20220107);
//        backBlazeKeywordStoreEntity2.setKeywordType(1);
//        backBlazeKeywordStoreEntity2.setEngineId(1);
//        backBlazeKeywordStoreEntity2.setLanguageId(1);
//        backBlazeKeywordStoreEntity2.setDevice("d");
//        backBlazeKeywordStoreEntity2.setCityId(300000);
//        String backBlazeKeywordStoreEntity2KName = "TestKeywordName2";
//        backBlazeKeywordStoreEntity2.setKeywordName(backBlazeKeywordStoreEntity2KName.toLowerCase());
//        backBlazeKeywordStoreEntity2.setRawKeywordName(backBlazeKeywordStoreEntity2KName.toLowerCase());
//        backBlazeKeywordStoreEntity2.setCdbKeywordHash(CityHashUtil.getUrlHashForString(backBlazeKeywordStoreEntity2KName.toLowerCase()));
//        backBlazeKeywordStoreEntity2.setCdbKeywordMurmur3hash(MurmurHashUtils.getMurmurHash3_64(backBlazeKeywordStoreEntity2KName.toLowerCase()));
//        backBlazeKeywordStoreEntity2.setMetaFileId(12345L);
//        backBlazeKeywordStoreEntity2.setFileName("TestKeywordName2.zip");
//        backBlazeKeywordStoreEntity2.setSourceFileId(23456L);
//        backBlazeKeywordStoreEntityDao.insert(backBlazeKeywordStoreEntity2);
        //*************************************************************************************************************//

        BackBlazeMetaFileEntity backBlazeMetaFileEntity1 = new BackBlazeMetaFileEntity();
        backBlazeMetaFileEntity1.setMetaFolderId(15445);
        backBlazeMetaFileEntity1.setZipFileName("TestKeywordName1.zip");
        backBlazeMetaFileEntityDao.insert(backBlazeMetaFileEntity1);

        BackBlazeMetaFileEntity backBlazeMetaFileEntity2 = new BackBlazeMetaFileEntity();
        backBlazeMetaFileEntity2.setMetaFolderId(13543522);
        backBlazeMetaFileEntity2.setZipFileName("TestKeywordName2.zip");
        List<BackBlazeMetaFileEntity> backBlazeMetaFileEntity2List = new ArrayList<>();
        backBlazeMetaFileEntity2List.add(backBlazeMetaFileEntity2);
        backBlazeMetaFileEntityDao.insertBatchIgnoreDup(backBlazeMetaFileEntity2List);

        //*************************************************************************************************************//

        BackBlazeMetaFolderEntity blazeMetaFolderEntity1 = new BackBlazeMetaFolderEntity();
        blazeMetaFolderEntity1.setFullPathFolder("/verqvreqverv");
        backBlazeMetaFolderEntityDao.insert(blazeMetaFolderEntity1);

        BackBlazeMetaFolderEntity blazeMetaFolderEntity2 = new BackBlazeMetaFolderEntity();
        blazeMetaFolderEntity2.setFullPathFolder("/jwecuewvcrvervrev");
        List<BackBlazeMetaFolderEntity> blazeMetaFolderEntity2List = new ArrayList<>();
        blazeMetaFolderEntity2List.add(blazeMetaFolderEntity2);
        backBlazeMetaFolderEntityDao.insertBatchIgnoreDup(blazeMetaFolderEntity2List);

        //*************************************************************************************************************//

        BackBlazeSourceFileS3Entity backBlazeSourceFileS3Entity1 = new BackBlazeSourceFileS3Entity();
        backBlazeSourceFileS3Entity1.setFullPathFolder("/fwjijvrvrqvreqvrwe");
        backBlazeSourceFileS3Entity1.setSourceFileName("jfwienioe.zip");
        backBlazeSourceFileS3EntityDao.insert(backBlazeSourceFileS3Entity1);

        BackBlazeSourceFileS3Entity backBlazeSourceFileS3Entity2 = new BackBlazeSourceFileS3Entity();
        backBlazeSourceFileS3Entity2.setFullPathFolder("/fwjijvrqvrqvrvwe");
        backBlazeSourceFileS3Entity2.setSourceFileName("jfwienioe.zip");
        List<BackBlazeSourceFileS3Entity> backBlazeSourceFileS3EntityList = new ArrayList<>();
        backBlazeSourceFileS3EntityList.add(backBlazeSourceFileS3Entity2);
        backBlazeSourceFileS3EntityDao.insertBatchIgnoreDup(backBlazeSourceFileS3EntityList);
    }

    private void processForTestS3ListFile() {

        ListObjectsRequest request = new ListObjectsRequest().withBucketName(s3BucketName);
        request.setPrefix("20200816/ll-v2-1-1/1138/");
        ObjectListing objects = s3.listObjects(request);
        List<S3ObjectSummary> objectSummaries = objects.getObjectSummaries();
        for (S3ObjectSummary objectSummary : objectSummaries) {
            System.out.println(objectSummary.getKey());
        }


//        int processFileCnt = 0;
//        /*设置需要哪些数据*/
//        ListObjectsRequest request_date = new ListObjectsRequest().withBucketName(s3BucketName).withDelimiter("/");
//        request_date.setPrefix("20200816/ll-v2-1-1/");
//        /*请求数据*/
//        ObjectListing objects_date = s3.listObjects(request_date);
//        /*循环处理 weekly 文件夹*/
//        /*weekly 文件夹不超过53个 可以使用 getObjectSummaries 直接获取*/
//        List<String> datePathList = objects_date.getCommonPrefixes();
//
//        for (String s : datePathList) {
//            System.out.println(s);
//        }
//        for (String filePath_date : datePathList) {
//            /*统计每每周已处理文件数量*/
//            int processFileCntForDate = 0;
//            // TODO: 2022/1/7
//            if (!"20200816/".equals(filePath_date)) {
//                continue;
//            }
//            ListObjectsRequest request_engine = new ListObjectsRequest().withBucketName(s3BucketName).withDelimiter("/");
//            request_engine.setPrefix(filePath_date);
//            ObjectListing objects_engine = s3.listObjects(request_engine);
//            /*engine 级文件夹超过1000, 需要特殊遍历*/
//            List<String> enginePathList = new ArrayList<>(objects_engine.getCommonPrefixes());
//            while (objects_engine.isTruncated()) {
//                objects_engine = s3.listNextBatchOfObjects(objects_engine);
//                enginePathList.addAll(objects_engine.getCommonPrefixes());
//            }
//            /*循环处理 engine 级文件夹*/
//            for (String filePath_engine : enginePathList) {
//                // TODO: 2022/1/7
//                // 20160320/mobile-34-34/
//                // 20200802/255-30/ 10kw
//                // 20200802/100-32/ 54kw
//                // 20200802/100-9/ 1600kw test in server
//                if (!filePath_engine.equals("20200816/fpr-1-1/")) {
//                    continue;
//                }
//
////                if (!filePath_engine.contains("fpr")) {
////                    continue;
////                }
//                /*统计每个engine文件夹内已处理文件数量*/
//                int processFileCntForEngine = 0;
//                ListObjectsRequest request_file = new ListObjectsRequest().withBucketName(s3BucketName);
//                request_file.setPrefix(filePath_engine);
//                ObjectListing objects_file = s3.listObjects(request_file);
//                /*循环获取文件*/
//                List<S3ObjectSummary> filePathObjectSummaryList = new ArrayList<>(objects_file.getObjectSummaries());
//
//                for (S3ObjectSummary var : filePathObjectSummaryList) {
//                    System.out.println(var.getKey());
//                }
//
////                while (objects_file.isTruncated()) {
////                    objects_file = s3.listNextBatchOfObjects(objects_file);
////                    if (filePathObjectSummaryList.size() >= s3ZipFileCnt) {
//////                        createThread(filePath_engine, filePathObjectSummaryList);
////                        processFileCntForEngine += filePathObjectSummaryList.size();
////                        filePathObjectSummaryList.clear();
////                    }
////                    filePathObjectSummaryList.addAll(objects_file.getObjectSummaries());
////                }
////                if (filePathObjectSummaryList.size() > 0) {
//////                    createThread(filePath_engine, filePathObjectSummaryList);
////                    processFileCntForEngine += filePathObjectSummaryList.size();
////                    filePathObjectSummaryList.clear();
////                }
////                processFileCntForDate += processFileCntForEngine;
////                log.info("$EC=>end process path: " + filePath_engine + "; file cnt: " + processFileCntForEngine);
//            }
//            processFileCnt += processFileCntForDate;
//            log.info("$EC=>end process path: " + filePath_date + "; file cnt: " + processFileCntForDate);
//        }

    }

}
