package seoclarity.backend.tools.transferrankdatafroms3tob2;

import com.amazonaws.services.sqs.AmazonSQS;
import com.amazonaws.services.sqs.model.DeleteQueueRequest;
import com.amazonaws.services.sqs.model.Message;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.math.NumberUtils;
import seoclarity.backend.utils.CollectionUtils;
import seoclarity.backend.utils.Md5Util;
import seoclarity.backend.utils.amazon.SQSUtils;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * ewain
 * seoclarity.backend.transferrankdatafroms3tob2.DeadLatterQValidMessageSender
 *
 * nohup mvn exec:java -Dexec.mainClass="seoclarity.backend.transferrankdatafroms3tob2.DeadLatterQValidMessageSender" -Dexec.cleanupDaemonThreads=false -Dexec.args="" >> log/DeadLatterQValidMessageSender-20220218-01.log 2>&1 &
 */
@CommonsLog
public class DeadLatterQValidMessageSender {

    private static final String deadLatterQPrefix = "S3TOB2_DEAD_LATTER_S3TOB2_S3_FILE_INFO_";
    private static final String reSenderQPrefix = "S3TOB2_RE_SENDER_S3_FILE_INFO_";

    private static final String filePath = "/home/<USER>/source/ewain/dev-clarity-backend-scripts/local_file/s3tobb/errorKey.txt";

    private static AmazonSQS amazonSQS;

    static {
        amazonSQS = SQSUtils.getAmazonSQS();
    }


    public static void main(String[] args) {
        DeadLatterQValidMessageSender sender = new DeadLatterQValidMessageSender();
        sender.process();
    }

    private void process() {
        String startQ = deadLatterQPrefix + 20181125;
        String lastQ = deadLatterQPrefix + 20190519;
        List<String> queueUrls = SQSUtils.getListQueues(amazonSQS, deadLatterQPrefix);
        if (queueUrls.size() > 0) {
            List<String> qList = queueUrls.stream().sorted(String::compareTo).collect(Collectors.toList());
            log.info("all q: " + qList);
            for (String queryUrl : qList) {
                String qName = getQNameByQUrl(queryUrl);
                log.info(" q Name: " + qName + ", last q: " + lastQ);
                if (qName.compareTo(lastQ) > 0) {
                    return;
                }
                if (qName.compareTo(startQ) < 0) {
                    continue;
                }
                log.info("$EC=>start process q: " + queryUrl);
                Integer messageNumberInQueue = SQSUtils.getMessageNumberInQueue(amazonSQS, queryUrl);
                if (messageNumberInQueue == 0) {
                    log.info(" deadlatterq: " + qName + " 为空, 需要删除");
                    amazonSQS.deleteQueue(new DeleteQueueRequest(queryUrl));
                    continue;
                }
                processForUrl(amazonSQS, qName, queryUrl);
            }
        }

    }

    private static String getQNameByQUrl(String queryUrl){
        // https://sqs.us-east-1.amazonaws.com/397485469449/S3TOB2_S3_FILE_INFO_20170507
        String[] split = queryUrl.split("/");
        return split[split.length-1];
    }


    private void processForUrl(AmazonSQS amazonSQS, String qName, String queryUrl) {
        Set<String> folderSet = new HashSet<>();
        Set<String> messageSet = new HashSet<>();
        int tryCnt = 3;
        while (true) {
            List<Message> result = SQSUtils.getMessageFromQueue(amazonSQS, queryUrl, 10, 600);
            if (result != null && result.size() > 0) {
                processMessage(queryUrl, qName, result, folderSet, messageSet);
                tryCnt = 3;
            } else {
                try {
                    log.info("$EC=>" + Thread.currentThread().getName() + "=>Q message is null. wait 2s!");
                    Thread.sleep(2 * 1000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                if (tryCnt-- < 0) {
                    try {
                        Thread.sleep(10 * 1000);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                    if (NumberUtils.createInteger(SQSUtils.getApproximateNumberOfMessages(amazonSQS, queryUrl)) == 0) {
                        log.info(" q 中没有可读消息,需要删除Q ");
                        System.out.println("folderSet sout: " + folderSet);
                        System.out.println("message set size: " + messageSet.size() + ", queryUrl: " + queryUrl);
                        return;
                    } else {
                        log.info(" error q 中有可读消息, 检查 ");
                    }
                }
            }
        }

    }

    private void processMessage(String queryUrl, String qName, List<Message> messages, Set<String> folderSet, Set<String> messageSet) {
        List<String> parseFolderErrorKeyList = new ArrayList<>();
        for (Message message : messages) {
            String key_name = message.getBody();
            if (messageSet.contains(key_name)) {
                continue;
            }
            messageSet.add(key_name);
            S3ToB2FilePusher pusher = new S3ToB2FilePusher();
            S3ToB2FilePusher.FileInfo fileInfo = null;
            try {

                fileInfo = pusher.getFileInfo(key_name);
                if (fileInfo.getInfoData().getKeywordName().equals(".")) {
                    log.info("跳过1 " + key_name + ", 因为其keywordName为 '.' ");
                    sendMessageToFinalDeadLatterQAndDelCurrMessage(queryUrl, qName,  key_name, message);
                    continue;
                }

                if (key_name.endsWith("/") || key_name.endsWith(".")) {
                    log.info("跳过2 " + key_name + ", key_name: " + key_name);
                    sendMessageToFinalDeadLatterQAndDelCurrMessage(queryUrl, qName,  key_name, message);
                    continue;
                }

                if (key_name.contains("http://")) {
                    sendMessageToFinalDeadLatterQAndDelCurrMessage(queryUrl, qName,  key_name, message);
                    log.info("跳过3 " + key_name + ", key_name: " + key_name);
                    continue;
                }

                Map<String, String> messagese = new HashMap<>();
                messagese.put(Md5Util.Md5(key_name), key_name);
                String deadLatterQName = getQNameByQUrl(queryUrl);
                String reSenderQName = getReSenderQName(deadLatterQName);
                String reSenderQUrl = SQSUtils.createQueue(reSenderQName, amazonSQS);
                SQSUtils.sendBatchMessageToQueue(amazonSQS, reSenderQUrl, messagese);
                SQSUtils.deleteMessageFromQueue(amazonSQS, queryUrl, message);
            } catch (Exception e) {
                log.info("$EC=>" + Thread.currentThread().getName() + " parse filePath failed! key: " + key_name + ", msg: " + CollectionUtils.getErrorMsg(e));
                parseFolderErrorKeyList.add(key_name);
                String folder = getFolder(key_name);
                if (folder != null) {
                    folderSet.add(folder);
                }
                sendMessageToFinalDeadLatterQAndDelCurrMessage(queryUrl, qName,  key_name, message);
            }
        }



        if (parseFolderErrorKeyList.size() > 0) {
            writeErrorLineToFile(filePath, parseFolderErrorKeyList);
        }
    }

    private void sendMessageToFinalDeadLatterQAndDelCurrMessage(String queryUrl, String qName, String key_name, Message message) {
        String finalQ = qName + "_FINAL";
        String finalQUrl = SQSUtils.createQueue(finalQ, amazonSQS);
        Map<String, String> messagese = new HashMap<>();
        messagese.put(Md5Util.Md5(key_name), key_name);
        SQSUtils.sendBatchMessageToQueue(amazonSQS, finalQUrl, messagese);
        SQSUtils.deleteMessageFromQueue(amazonSQS, queryUrl, message);
    }

    private void writeErrorLineToFile(String filePath, List<String> lines) {
        File file = new File(filePath);
        try {
            FileUtils.writeLines(file, "UTF-8", lines, true);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private String getReSenderQName(String deadLatterQName) {
        String[] strArr = deadLatterQName.split("_");
        String date = strArr[strArr.length - 1];
        return reSenderQPrefix + date;
    }

    private String getFolder(String key){
        try {
            String[] split = key.split("-");
            StringBuffer sbf = new StringBuffer();
            for (int i = 0; i < split.length-1; i++) {
                sbf.append(split[i] + "-");
            }
            return sbf.toString();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
}
