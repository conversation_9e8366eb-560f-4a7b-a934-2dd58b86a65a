package seoclarity.backend.clarity360;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.util.CollectionUtils;

import com.google.gson.Gson;

import seoclarity.backend.clarity360.vo.BacklinkVO;
import seoclarity.backend.clarity360.vo.BotVO;
import seoclarity.backend.clarity360.vo.Clarity360BaseVO;
import seoclarity.backend.clarity360.vo.CustomDataSourceVO;
import seoclarity.backend.clarity360.vo.GaVO;
import seoclarity.backend.clarity360.vo.GscVO;
import seoclarity.backend.clarity360.vo.InternalLinkVO;
import seoclarity.backend.clarity360.vo.RgVO;
import seoclarity.backend.clarity360.vo.RiVO;
import seoclarity.backend.clarity360.vo.SiteHealthVO;
import seoclarity.backend.clarity360.vo.SiteMapVO;
import seoclarity.backend.dao.actonia.Clarity360ReportEntityDAO;
import seoclarity.backend.dao.actonia.Clarity360ReportStageEntityDAO;
import seoclarity.backend.dao.actonia.CrawlRequestLogDAO;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.actonia.OwnDomainSettingEntityDAO;
import seoclarity.backend.dao.clickhouse.ClDailyRankingEntityDao;
import seoclarity.backend.dao.clickhouse.bot.BotDetailDao;
import seoclarity.backend.dao.clickhouse.bot.SiteMapDetailDao;
import seoclarity.backend.dao.clickhouse.clarity360.lweb05.Clarity360Lweb05DAO;
import seoclarity.backend.dao.clickhouse.clarity360.merge01.Clarity360Merge01DAO;
import seoclarity.backend.dao.clickhouse.clarity360.merge02.Clarity360Merge02DAO;
import seoclarity.backend.dao.clickhouse.clarity360.merge03.Clarity360Merge03DAO;
import seoclarity.backend.dao.clickhouse.clarity360.merge04.Clarity360Merge04DAO;
import seoclarity.backend.dao.clickhouse.clarity360.merge05.Clarity360Merge05DAO;
import seoclarity.backend.dao.clickhouse.ga.Ga001ClarityDBEntityDAO;
import seoclarity.backend.dao.clickhouse.gsc.GscBaseDao;
import seoclarity.backend.dao.clickhouse.monthlyranking.LwebMonthlyRankingDao;
import seoclarity.backend.dao.clickhouse.prodclarity.DisSiteCrawlDoc1For360SummaryOnlyDao;
import seoclarity.backend.entity.actonia.Clarity360ReportEntity;
import seoclarity.backend.entity.actonia.Clarity360ReportStageEntity;
import seoclarity.backend.entity.actonia.CrawlRequestLog;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.actonia.OwnDomainSettingEntity;
import seoclarity.backend.multithread.core.modlefactory.CacheModleFactory;
import seoclarity.backend.multithread.core.thread.threadpool.ThreadPoolManager;
import seoclarity.backend.utils.CommonUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.utils.ZeptoMailSenderComponent;
//mvn exec:java -Dexec.mainClass="seoclarity.backend.clarity360.Clarity360SummaryV4Backup" -Dexec.args=""
//mvn exec:java -Dexec.mainClass="seoclarity.backend.clarity360.Clarity360SummaryV4Backup" -Dexec.args="840 4755"
public class Clarity360SummaryV4Backup {

	public static ThreadPoolManager threadPool = ThreadPoolManager.getInstance();
	
	//mysql
	private Clarity360ReportEntityDAO clarity360ReportEntityDAO;
	private Clarity360ReportStageEntityDAO clarity360ReportStageEntityDAO;
	private OwnDomainEntityDAO ownDomainEntityDAO;
	private OwnDomainSettingEntityDAO ownDomainSettingEntityDAO;
	private CrawlRequestLogDAO crawlRequestLogDAO;
	
	//360 cluster
//	private Clarity360Lweb01DAO clarity360Lweb01DAO;
//	private Clarity360Lweb02DAO clarity360Lweb02DAO;
//	private Clarity360Lweb03DAO clarity360Lweb03DAO;
//	private Clarity360Lweb04DAO clarity360Lweb04DAO;
	private Clarity360Lweb05DAO clarity360Lweb05DAO;
	
	
	private Clarity360Merge03DAO clarity360Merge03DAO;
	private Clarity360Merge04DAO clarity360Merge04DAO;
	private Clarity360Merge05DAO clarity360Merge05DAO;
	
	//datasource
	//----site health
	private DisSiteCrawlDoc1For360SummaryOnlyDao disSiteCrawlDoc1For360SummaryOnlyDao;
	//----site analytics
	private Ga001ClarityDBEntityDAO ga001ClarityDBEntityDAO;
	//----site map
	private SiteMapDetailDao siteMapDetailDao;
	//----bot
	private BotDetailDao botDetailDao;
	//----gsc
	private GscBaseDao gscBaseDao;
	//----ri
	private ClDailyRankingEntityDao clDailyRankingEntityDao;
	//----rg
//	private MonthlyRankingDao monthlyRankingDao;
	private LwebMonthlyRankingDao lwebMonthlyRankingDao;
	//----internal link
//	private InternalLinkServer1Dao internalLinkServer1Dao;
//	//----backlink
//	private BacklinkBaseDao backlinkBaseDao;
	
	private static Map<Integer, String> exceptionMap = new HashMap<>();
	
	private static final Integer DEFAILT_PAGESIZE = 500000;
	private Integer pageSize = DEFAILT_PAGESIZE;
	private static final Integer SITE_HEALTH_SUMMARY_DEFAILT_PAGESIZE = 300000;
	private static final Integer EXPEDIA_SITE_HEALTH_SUMMARY_PAGESIZE = 10000;
	private static final Integer EXPEDIA_DOMAIN_ID = 4765;
	private static final Integer MAX_RETRY_NUMBER = 10;
	
	private static final Integer DEFAILT_LOAD_PAGESIZE = 10000000;
	
	
    public static String localTableName = "local_360_table_v3";
    public static String localCustomTableName = "local_360_custom_urlmurmurhash_v3";
    
    private static String[][] array = new String[6][];
    static {
    	array[0] = new String[] {"3", localTableName};
    	array[1] = new String[] {"4", localTableName};
    	array[2] = new String[] {"5", localTableName};
//    	array[3] = new String[] {"4", localTableName};
//    	array[4] = new String[] {"5", localTableName};
    	array[3] = new String[] {"3", localCustomTableName};
    	array[4] = new String[] {"4", localCustomTableName};
    	array[5] = new String[] {"5", localCustomTableName};
//    	array[8] = new String[] {"4", localCustomTableName};
//    	array[9] = new String[] {"5", localCustomTableName};
    }
    
	
	
//	private static List<Class> classList = new ArrayList<>();
//	static {
//		classList.add(Clarity360Lweb01DAO.class);
//		classList.add(Clarity360Lweb02DAO.class);
//		classList.add(Clarity360Lweb03DAO.class);
//		classList.add(Clarity360Lweb04DAO.class);
//		classList.add(Clarity360Lweb05DAO.class);
//	}

	public Clarity360SummaryV4Backup() {
		ownDomainSettingEntityDAO = SpringBeanFactory.getBean("ownDomainSettingEntityDAO");
		ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
		clarity360ReportEntityDAO = SpringBeanFactory.getBean("clarity360ReportEntityDAO");
		clarity360ReportStageEntityDAO = SpringBeanFactory.getBean("clarity360ReportStageEntityDAO");
		
//		backlinkBaseDao = SpringBeanFactory.getBean("backlinkBaseDao");
//		internalLinkServer1Dao = SpringBeanFactory.getBean("internalLinkServer1Dao");
//		monthlyRankingDao = SpringBeanFactory.getBean("monthlyRankingDao");
		lwebMonthlyRankingDao = SpringBeanFactory.getBean("lwebMonthlyRankingDao");
		disSiteCrawlDoc1For360SummaryOnlyDao = SpringBeanFactory.getBean("disSiteCrawlDoc1For360SummaryOnlyDao");
		ga001ClarityDBEntityDAO = SpringBeanFactory.getBean("ga001ClarityDBEntityDAO");
		siteMapDetailDao = SpringBeanFactory.getBean("siteMapDetailDao");
		botDetailDao = SpringBeanFactory.getBean("botDetailDao");
		gscBaseDao = SpringBeanFactory.getBean("gscBaseDao");
		clDailyRankingEntityDao = SpringBeanFactory.getBean("clDailyRankingEntityDao");
		crawlRequestLogDAO = SpringBeanFactory.getBean("crawlRequestLogDAO");
		
//		clarity360Lweb01DAO = SpringBeanFactory.getBean("clarity360Lweb01DAO");
//		clarity360Lweb02DAO = SpringBeanFactory.getBean("clarity360Lweb02DAO");
//		clarity360Lweb03DAO = SpringBeanFactory.getBean("clarity360Lweb03DAO");
//		clarity360Lweb04DAO = SpringBeanFactory.getBean("clarity360Lweb04DAO");
		clarity360Lweb05DAO = SpringBeanFactory.getBean("clarity360Lweb05DAO");
		
//		clarity360Merge01DAO = SpringBeanFactory.getBean("clarity360Merge01DAO");
//		clarity360Merge02DAO = SpringBeanFactory.getBean("clarity360Merge02DAO");
		clarity360Merge03DAO = SpringBeanFactory.getBean("clarity360Merge03DAO");
		clarity360Merge04DAO = SpringBeanFactory.getBean("clarity360Merge04DAO");
		clarity360Merge05DAO = SpringBeanFactory.getBean("clarity360Merge05DAO");
	}
	
	private static Integer BACK_PROCESS_REPORT_ID = 0;
	private static Integer BACK_PROCESS_DOMAIN_ID = 0;

	public static void main(String[] args) {
		
		Clarity360SummaryV4Backup clarity360Summary = new Clarity360SummaryV4Backup();
		
//		String message = "{\"domainId\":\"4661\",\"sources\":[{\"siteAnalytics\":{\"startDate\":\"2023-07-02\",\"endDate\":\"2023-07-08\",\"dataSourceType\":1,\"engines\":[\"google\"],\"contentTypeFilters\":[],\"leftNaviFilters\":[]}},{\"siteMap\":{\"contentTypeFilters\":[],\"leftNaviFilters\":[]}},{\"internalLink\":{\"crawlRequestId\":\"9946793\",\"contentTypeFilters\":[],\"leftNaviFilters\":[]}},{\"backlink\":{\"domainName\":\"hm.com\",\"startDate\":\"2023-07-02\",\"endDate\":\"2023-07-08\",\"contentTypeFilters\":[],\"leftNaviFilters\":[]}},{\"researchGrid\":{\"index\":\"202306\",\"engineId\":\"1\",\"languageId\":\"1\",\"device\":\"d\",\"domainName\":\"hm.com\",\"ownDomainId\":\"4661\",\"trueRank\":true,\"type\":\"domain\",\"contentTypeFilters\":[],\"leftNaviFilters\":[]}}]}";
////		String message = "{\"domainId\":\"5671\",\"sources\":[{\"siteHealth\":{\"crawlRequestId\":\"9943370\"}},{\"siteAnalytics\":{\"startDate\":\"2023-04-01\",\"endDate\":\"2023-04-30\",\"analyticsGroupFilter\":null,\"analyticsGroupId\":null,\"dataSourceType\":1,\"engines\":[\"google\"]}},{\"searchAnalytics\":{\"relIds\":[\"2025\"],\"startDate\":\"2023-04-01\",\"endDate\":\"2023-04-30\"}},{\"bot\":{\"startDate\":\"2023-04-01\",\"endDate\":\"2023-04-30\"}},{\"siteMap\":{}},{\"ranking\":{\"domainName\":\"newegg.com\",\"rankingType\":\"true\",\"locationId\":0,\"languageId\":1,\"engineId\":1,\"device\":\"d\",\"startDate\":\"2023-04-01\",\"endDate\":\"2023-04-30\"}}]}";
//	 	//		String message = "{\"domainId\":\"9112\",\"sources\":[{\"siteHealth\":{\"crawlRequestId\":\"9944181\"}},{\"siteAnalytics\":{\"startDate\":\"2023-02-07\",\"endDate\":\"2023-03-09\",\"dataSourceType\":1,\"engines\":[\"google\"]}},{\"searchAnalytics\":{\"relIdList\":[\"7141\"],\"startDate\":\"2023-02-07\",\"endDate\":\"2023-03-09\"}},{\"siteMap\":{}},{\"ranking\":{\"domainName\":\"bankrate.com\",\"rankingType\":\"true\",\"locationId\":0,\"languageId\":1,\"engineId\":1,\"device\":\"d\",\"startDate\":\"2023-02-07\",\"endDate\":\"2023-03-09\"}},{\"researchGrid\":{\"index\":202302,\"engineId\":1,\"languageId\":1,\"device\":\"d\",\"domainName\":\"bankrate.com\",\"ownDomainId\":9112,\"trueRank\":true,\"type\":\"domain\"}},{\"internalLink\":{\"crawlRequestId\":\"9944181\"}},{\"backlink\":{\"domainName\":\"bankrate.com\",\"startDate\":\"2022-03-11\",\"endDate\":\"2023-03-11\"}}]}";
//		
//		Clarity360ReportEntity clarity360ReportEntity = new Clarity360ReportEntity();
//		
//		clarity360ReportEntity.setId(192);
//		clarity360ReportEntity.setParamJson(message);
//		clarity360ReportEntity.setCreateDate(new Date());
//		clarity360ReportEntity.setOwnDomainId(4661);
//		
//		try {
//			clarity360Summary.processMsg(clarity360ReportEntity);
//		} catch (Exception e) {
//			// TODO Auto-generated catch block
//			e.printStackTrace();
//		}
		
		if(args != null && args.length >= 2) {
			BACK_PROCESS_REPORT_ID = NumberUtils.toInt(args[0]);
			BACK_PROCESS_DOMAIN_ID = NumberUtils.toInt(args[1]);
			System.out.println("==== backprocess for REPORT: " + BACK_PROCESS_REPORT_ID + ", OID:" + BACK_PROCESS_DOMAIN_ID);
		}
		
		clarity360Summary.process();
	}
	
    private Gson gson = new Gson();
    
    
    private void process() {
    	
    	
    	if (BACK_PROCESS_REPORT_ID > 0 && BACK_PROCESS_DOMAIN_ID > 0) {
    		Clarity360ReportEntity clarity360ReportEntity = clarity360ReportEntityDAO.getReportById(BACK_PROCESS_REPORT_ID, BACK_PROCESS_DOMAIN_ID);
    		
    		if(clarity360ReportEntity == null){
    			System.out.println("Report not found! REPORT:" +  + BACK_PROCESS_REPORT_ID + ", OID:" + BACK_PROCESS_DOMAIN_ID);
    			return;
    		}
    		
    		Integer totalCount = clarity360Merge03DAO.getTotalCountByReportId(BACK_PROCESS_REPORT_ID, BACK_PROCESS_DOMAIN_ID);
    		
    		if(totalCount > 0) {
    			System.out.println("===== Still get data in CDB, can not backprocess now! totalCount:" + totalCount);
    			return;
    		}
    		
    		System.out.println("========== start to update status ===========");
    		
    		clarity360ReportStageEntityDAO.deleteByReportId(BACK_PROCESS_REPORT_ID);
    		clarity360ReportEntityDAO.updateStatusAndStartDate(Clarity360ReportEntity.PROCESS_STATUS_NEED_PROCESS, BACK_PROCESS_REPORT_ID);
    		
    		processTask(clarity360ReportEntity);
		} else {
			
			while(true) {
				Clarity360ReportEntity clarity360ReportEntity = clarity360ReportEntityDAO.getNeedProcessReport();
		    	
		    	if(clarity360ReportEntity == null) {
		    		
		    		System.out.println(" No Task! ");
		    		return;
		    	}
		    	
	    		processTask(clarity360ReportEntity);
			}
			
		}
    }
    
    private void processTask(Clarity360ReportEntity clarity360ReportEntity) {

		System.out.println("### proc report:" + clarity360ReportEntity.getId() + ", oid:" + clarity360ReportEntity.getOwnDomainId());
		
		try {
			
			processMsg(clarity360ReportEntity, clarity360ReportEntity.getId());
			
		} catch (Exception e) {
			e.printStackTrace();
			clarity360ReportEntityDAO.updateStatusAndEndDate(Clarity360ReportEntity.PROCESS_STATUS_ERROR, clarity360ReportEntity.getId());
		}
    }
    
    
    public static List<Integer> GA_GA4_DOMAIN_MAP = new ArrayList<>();
    public static Map<String, String> ALTERNATE_DOMAIN_MAP = new HashMap<String, String>();
    
    private Integer ownDomainId = null;
    private Integer reportId = null;
    private String targetDate = "";
    

    private void processMsg(Clarity360ReportEntity clarity360ReportEntity, Integer crawlRequestId) throws Exception {
    		
    	//===================== GA4 domain ======================
    	List<OwnDomainEntity> mapList = ownDomainEntityDAO.getGa4Domain();
        if (mapList != null) {
            mapList.stream().forEach(ownDomainEntity -> {
                GA_GA4_DOMAIN_MAP.add(ownDomainEntity.getId());
            });
            System.out.println("GA4 domain cnt:" + mapList.size());
        }
        
        //================ cache alternate domain ================
        ALTERNATE_DOMAIN_MAP = ownDomainSettingEntityDAO.getAllAlternateDomainNames();
        
        System.out.println("alternate domain map cnt:" + ALTERNATE_DOMAIN_MAP.size());
        //========================================================
        
    	String message = clarity360ReportEntity.getParamJson();
    	reportId = clarity360ReportEntity.getId();
    	ownDomainId = clarity360ReportEntity.getOwnDomainId();
    	targetDate = FormatUtils.formatDate(clarity360ReportEntity.getCreateDate(), "yyyy-MM-dd");
    	
        System.out.println("===process OID:" + ownDomainId);
        
    	if (ownDomainId == null || ownDomainId == 0) {
    		System.out.println(" !!! skip, Oid: " + ownDomainId);
//    		clarity360ReportEntityDAO.updateStatusAndEndDate(Clarity360ReportEntity.PROCESS_STATUS_ERROR, clarity360ReportEntity.getId());
			return;
		}
    	
    	if (StringUtils.isBlank(targetDate)) {
    		System.out.println(" !!! skip, targetDate is empty: " + targetDate);
//    		clarity360ReportEntityDAO.updateStatusAndEndDate(Clarity360ReportEntity.PROCESS_STATUS_ERROR, clarity360ReportEntity.getId());
    		return;
    	}
    	
    	clarity360ReportEntityDAO.updateStatusAndStartDate(Clarity360ReportEntity.PROCESS_STATUS_PROCESSING, clarity360ReportEntity.getId());
        
    	
    	Integer totalCount = clarity360Merge03DAO.getTotalCountByReportId(reportId, ownDomainId);
		
		if(totalCount > 0) {
			System.out.println("===== Still get merge data in CDB, can not backprocess now! totalCount:" + totalCount + ", reportId:" + reportId);
			return;
		}
		
		Integer totalCountInFinal = clarity360Lweb05DAO.getTotalCountByReportId(reportId, ownDomainId);
		
		if(totalCountInFinal > 0) {
			System.out.println("===== Still get data in CDB, can not backprocess now! totalCountInFinal:" + totalCountInFinal + ", reportId:" + reportId);
			return;
		}
    	//============================================
        
    	
        //TODO run "optimize table clarity360.local_360_table_v2 partition 202212 final"
        
//            for(Class clazz : classList) {
//				 Method optimizeTable = clazz.getMethod("optimizeTable", String.class);
//			     Object o = clazz.newInstance();
//			     optimizeTable.invoke(o, PROCESS_MONTH);
//			}
        
        //optimize in local
        //step 1
    	summary(message, crawlRequestId);
    	
    	//step 2
        optimize();
        
        //step 3
        loadDataIntoFinalTable();
        
        clarity360ReportEntityDAO.updateStatusAndEndDate(Clarity360ReportEntity.PROCESS_STATUS_DONE, clarity360ReportEntity.getId());
        
    }
    
    
    private void loadDataIntoFinalTable() {
    	
    	Integer sleepTime = 0;
    	
    	while (!clarity360Merge03DAO.isMergeFinished(reportId, ownDomainId) || !clarity360Merge03DAO.isMergeCustomFinished(reportId, ownDomainId)) {
    		
    		optimize();
			try {
				System.out.println("merge is not finished, sleep for 60 minutes");
				Thread.sleep(60 * 60 * 1000);
				sleepTime += 5;
				if (sleepTime >= 30) {
					ZeptoMailSenderComponent.sendEmailReportForRv(new Date(), "ERROR/360 Merge", "360 Summary Merge(Report : " + reportId + ") not finished! OID : " + ownDomainId, "");
				}
			} catch (InterruptedException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}
    	
    	Integer totalCount = clarity360Merge03DAO.getTotalCountByReportId(reportId, ownDomainId);
    	
    	System.out.println("totalCount: " + totalCount);
		
    	int partitionNum = 0;
		if (totalCount >= DEFAILT_LOAD_PAGESIZE) {
			partitionNum = (totalCount / DEFAILT_LOAD_PAGESIZE) + 1;
		} else {
			partitionNum = 1;
		} 
		
        try { 
    		for(int pageNum = 0; pageNum < partitionNum; pageNum ++) {
    			try { 
    				System.out.println("Summary :" + (pageNum + 1) + " of " + partitionNum);
    				
    				clarity360Merge03DAO.loadDatIntoFinalTable(ownDomainId, reportId, partitionNum, pageNum, false);
    				try {
    					System.out.println("Sleep 1 sec...");
    					Thread.sleep(1 * 1000);
    				} catch (Exception e) {
						e.printStackTrace();
					}
    			} catch (Exception exception) {
    	            exception.printStackTrace();
    	        }
    			
    		}
        } catch (Exception e) {
        	exceptionMap.put(Clarity360ReportStageEntity.STEP_LOAD_INTO_FINAL, e.getLocalizedMessage());
            e.printStackTrace();
        }
        
        try { 
    		for(int pageNum = 0; pageNum < partitionNum; pageNum ++) {
    			try { 
    				System.out.println("Summary :" + (pageNum + 1) + " of " + partitionNum);
    				
    				clarity360Merge03DAO.loadDatIntoFinalTable(ownDomainId, reportId, partitionNum, pageNum, true);
    				try {
    					System.out.println("Sleep 1 sec...");
    					Thread.sleep(1 * 1000);
    				} catch (Exception e) {
						e.printStackTrace();
					}
    			} catch (Exception exception) {
    	            exception.printStackTrace();
    	        }
    			
    		}
        } catch (Exception e) {
        	exceptionMap.put(Clarity360ReportStageEntity.STEP_LOAD_INTO_FINAL_NORMALIZATION, e.getLocalizedMessage());
            e.printStackTrace();
        }
        
	}

	private void summary(String message, Integer crawlRequestId) {
    	
    	OwnDomainSettingEntity ownDomainSettingEntity = ownDomainSettingEntityDAO.getSettingByDomainId(ownDomainId);
    	OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getById(ownDomainId);
    	
    	if (!isInternalLinkFinished(message)) {
//    		clarity360ReportEntityDAO.updateStatusAndEndDate(Clarity360ReportEntity.PROCESS_STATUS_NEED_PROCESS_INTERNAL_LINK_NOT_FINISHED, crawlRequestId);
    		System.out.println("waiting for Internal link finish report(" + crawlRequestId + ") to waiting list!");
    		return;
		}
        
    	JSONObject jsonObject = new JSONObject(message);
    	
    	JSONArray source = jsonObject.getJSONArray("sources");
        for(Object sourceJson : source) {
        	Map sourceMap = gson.fromJson(String.valueOf(sourceJson), Map.class);
        	String funcName = gson.toJson(sourceMap.keySet().toArray()[0]);
        	
        	funcName =  StringUtils.removeEnd(StringUtils.removeStart(funcName, "\""), "\"");
        	
        	if (StringUtils.equals(funcName, "siteHealth")) {
        		
        		System.out.println(" === processing Site Health ");
        		insertNewStage(reportId, Clarity360ReportStageEntity.STATUS_PROCESSING, Clarity360ReportStageEntity.STEP_SITEHEALTH);
        		
        		try {
        			SiteHealthVO siteHealthVO = gson.fromJson(gson.toJson(sourceMap.get(funcName)), SiteHealthVO.class);
        			
        			Integer totalCount = disSiteCrawlDoc1For360SummaryOnlyDao.getDocTotalCount(ownDomainId, siteHealthVO, 
        					siteHealthVO.getUrlTagId(), siteHealthVO.getContentTypeFilterVO());
        			
        			pageSize = SITE_HEALTH_SUMMARY_DEFAILT_PAGESIZE;
        			if (ownDomainId == EXPEDIA_DOMAIN_ID) {
        				pageSize = EXPEDIA_SITE_HEALTH_SUMMARY_PAGESIZE;
        			}
        			
        			summaryByDataSource(siteHealthVO, totalCount, pageSize, funcName, Clarity360ReportStageEntity.STEP_SITEHEALTH);
        			Thread.sleep(10 * 1000);
        			clarity360ReportStageEntityDAO.updateStatus(reportId, Clarity360ReportStageEntity.STEP_SITEHEALTH, Clarity360ReportStageEntity.STATUS_COMPLETE, "");
				} catch (Exception e) {
					e.printStackTrace();
					clarity360ReportStageEntityDAO.updateStatus(reportId, Clarity360ReportStageEntity.STEP_SITEHEALTH, 
							Clarity360ReportStageEntity.STATUS_ERROR, StringUtils.substring(e.getMessage(), 0, 400));
				}
        		
			} else if (StringUtils.equals(funcName, "searchAnalytics")) {
				System.out.println(" === processing Gsc ");
				insertNewStage(reportId, Clarity360ReportStageEntity.STATUS_PROCESSING, Clarity360ReportStageEntity.STEP_GSC);

				try {
					GscVO gscVO = gson.fromJson(gson.toJson(sourceMap.get(funcName)), GscVO.class);
					
			    	Integer totalCount = 0;
			    	
			    	if(gscVO.getBigQuery() != null && gscVO.getBigQuery()) {
			    		totalCount = gscBaseDao.getBigQueryTotalCount(ownDomainId, gscVO, gscVO.getUrlTagId(), gscVO.getContentTypeFilterVO());
					} else {
						totalCount = gscBaseDao.getTotalCount(ownDomainId, gscVO, gscVO.getUrlTagId(), gscVO.getContentTypeFilterVO());
					}
					
					summaryByDataSource(gscVO, totalCount, pageSize, funcName, Clarity360ReportStageEntity.STEP_GSC);
					clarity360ReportStageEntityDAO.updateStatus(reportId, Clarity360ReportStageEntity.STEP_GSC, Clarity360ReportStageEntity.STATUS_COMPLETE, "");
				} catch (Exception e) {
					exceptionMap.put(Clarity360ReportStageEntity.STEP_GSC, e.getLocalizedMessage());
					e.printStackTrace();
					clarity360ReportStageEntityDAO.updateStatus(reportId, Clarity360ReportStageEntity.STEP_GSC, 
							Clarity360ReportStageEntity.STATUS_ERROR, StringUtils.substring(e.getMessage(), 0, 400));
				}
			} else if (StringUtils.equals(funcName, "siteAnalytics")) {
				System.out.println(" === processing Ga ");
        		insertNewStage(reportId, Clarity360ReportStageEntity.STATUS_PROCESSING, Clarity360ReportStageEntity.STEP_GA);
        		
        		try {
        			GaVO gaVO = gson.fromJson(gson.toJson(sourceMap.get(funcName)), GaVO.class);
        			
        			gaVO.setTrafficType(ownDomainSettingEntity.getTrafficType());
        			gaVO.setOwnDomainProtocol(ownDomainEntity.getProtocol());
        			
        			Integer totalCount = ga001ClarityDBEntityDAO.getTotalCount(gaVO.getTrafficType(), ownDomainId, 
	            			gaVO, targetDate, reportId, GA_GA4_DOMAIN_MAP, gaVO.getUrlTagId(), gaVO.getContentTypeFilterVO(), 
	            			gaVO.getOwnDomainProtocol());
        			
        			summaryByDataSource(gaVO, totalCount, pageSize, funcName, Clarity360ReportStageEntity.STEP_GA);
					clarity360ReportStageEntityDAO.updateStatus(reportId, Clarity360ReportStageEntity.STEP_GA, Clarity360ReportStageEntity.STATUS_COMPLETE, "");
        		} catch (Exception e) {
        			e.printStackTrace();
					clarity360ReportStageEntityDAO.updateStatus(reportId, Clarity360ReportStageEntity.STEP_GA, 
							Clarity360ReportStageEntity.STATUS_ERROR, StringUtils.substring(e.getMessage(), 0, 400));
				}
    		} else if (StringUtils.equals(funcName, "bot")) {
				System.out.println(" === processing Bot ");
        		insertNewStage(reportId, Clarity360ReportStageEntity.STATUS_PROCESSING, Clarity360ReportStageEntity.STEP_BOT);
        		
        		try {
					BotVO botVO = gson.fromJson(gson.toJson(sourceMap.get(funcName)), BotVO.class);
					Integer totalCount = botDetailDao.getTotalCount(ownDomainId, botVO, 
	            			botVO.getUrlTagId(), botVO.getContentTypeFilterVO());
        			
        			summaryByDataSource(botVO, totalCount, pageSize, funcName, Clarity360ReportStageEntity.STEP_BOT);
					
					clarity360ReportStageEntityDAO.updateStatus(reportId, Clarity360ReportStageEntity.STEP_BOT, Clarity360ReportStageEntity.STATUS_COMPLETE, "");
        		} catch (Exception e) {
        			e.printStackTrace();
					clarity360ReportStageEntityDAO.updateStatus(reportId, Clarity360ReportStageEntity.STEP_BOT, 
							Clarity360ReportStageEntity.STATUS_ERROR, StringUtils.substring(e.getMessage(), 0, 400));
				}
    		} else if (StringUtils.equals(funcName, "siteMap")) {
				System.out.println(" === processing Site Map ");
        		insertNewStage(reportId, Clarity360ReportStageEntity.STATUS_PROCESSING, Clarity360ReportStageEntity.STEP_SITEMAP);
        		
				try {
					SiteMapVO siteMapVO = gson.fromJson(gson.toJson(sourceMap.get(funcName)), SiteMapVO.class);
					
					Integer totalCount = siteMapDetailDao.getTotalCount(ownDomainId, siteMapVO, 
							siteMapVO.getUrlTagId(), siteMapVO.getContentTypeFilterVO(), "murmurHash3_64(url)");
        			
        			summaryByDataSource(siteMapVO, totalCount, pageSize, funcName, Clarity360ReportStageEntity.STEP_SITEMAP);
					
					clarity360ReportStageEntityDAO.updateStatus(reportId, Clarity360ReportStageEntity.STEP_SITEMAP, Clarity360ReportStageEntity.STATUS_COMPLETE, "");
				} catch (Exception e) {
					e.printStackTrace();
					clarity360ReportStageEntityDAO.updateStatus(reportId, Clarity360ReportStageEntity.STEP_SITEMAP, 
							Clarity360ReportStageEntity.STATUS_ERROR, StringUtils.substring(e.getMessage(), 0, 400));
				}
    		
    		} else if (StringUtils.equals(funcName, "ranking")) {
				System.out.println(" === processing Rank ");
        		insertNewStage(reportId, Clarity360ReportStageEntity.STATUS_PROCESSING, Clarity360ReportStageEntity.STEP_RI);
        		
        		
        		try {
        			RiVO riVO = gson.fromJson(gson.toJson(sourceMap.get(funcName)), RiVO.class);
        			riVO.setBroadMatch(ownDomainEntity.isBroadMatch());
    				
        			Integer totalCount = clDailyRankingEntityDao.getTotalCount(ownDomainId, riVO, ownDomainEntity, targetDate, 
	            			reportId, ALTERNATE_DOMAIN_MAP, riVO.getUrlTagId(), riVO.getContentTypeFilterVO());
        			
        			summaryByDataSource(riVO, totalCount, pageSize, funcName, Clarity360ReportStageEntity.STEP_RI);
        			
    				clarity360ReportStageEntityDAO.updateStatus(reportId, Clarity360ReportStageEntity.STEP_RI, Clarity360ReportStageEntity.STATUS_COMPLETE, "");
        		} catch (Exception e) {
        			e.printStackTrace();
					clarity360ReportStageEntityDAO.updateStatus(reportId, Clarity360ReportStageEntity.STEP_RI, 
							Clarity360ReportStageEntity.STATUS_ERROR, StringUtils.substring(e.getMessage(), 0, 400));
				}
				
			} else if (StringUtils.equals(funcName, "researchGrid")) {
				System.out.println(" === processing RG ");
        		insertNewStage(reportId, Clarity360ReportStageEntity.STATUS_PROCESSING, Clarity360ReportStageEntity.STEP_RG);
        		
        		try {
        			RgVO rgVO = gson.fromJson(gson.toJson(sourceMap.get(funcName)), RgVO.class);
        			
        			Integer totalCount = lwebMonthlyRankingDao.getTotalCount(ownDomainId, rgVO, ownDomainEntity, targetDate, 
	            			reportId, rgVO.getUrlTagId(), rgVO.getContentTypeFilterVO());
        			
        			summaryByDataSource(rgVO, totalCount, pageSize, funcName, Clarity360ReportStageEntity.STEP_RG);
        			
    				clarity360ReportStageEntityDAO.updateStatus(reportId, Clarity360ReportStageEntity.STEP_RG, Clarity360ReportStageEntity.STATUS_COMPLETE, "");
        		} catch (Exception e) {
        			e.printStackTrace();
					clarity360ReportStageEntityDAO.updateStatus(reportId, Clarity360ReportStageEntity.STEP_RG, 
							Clarity360ReportStageEntity.STATUS_ERROR, StringUtils.substring(e.getMessage(), 0, 400));
				}
				
			} else if (StringUtils.equals(funcName, "internalLink")) {
				System.out.println(" === processing internalLink ");
        		insertNewStage(reportId, Clarity360ReportStageEntity.STATUS_PROCESSING, Clarity360ReportStageEntity.STEP_INTERNAL_LINK);
        		
        		try {
        			InternalLinkVO internalLinkVO = gson.fromJson(gson.toJson(sourceMap.get(funcName)), InternalLinkVO.class);
        			
        			Integer totalCount = clarity360Merge03DAO.getTotalCount(ownDomainId, internalLinkVO, 
		        			targetDate, reportId, internalLinkVO.getUrlTagId(), internalLinkVO.getContentTypeFilterVO());
        			
        			summaryByDataSource(internalLinkVO, totalCount, pageSize, funcName, Clarity360ReportStageEntity.STEP_INTERNAL_LINK);
    				clarity360ReportStageEntityDAO.updateStatus(reportId, Clarity360ReportStageEntity.STEP_INTERNAL_LINK, Clarity360ReportStageEntity.STATUS_COMPLETE, "");
        		} catch (Exception e) {
        			e.printStackTrace();
					clarity360ReportStageEntityDAO.updateStatus(reportId, Clarity360ReportStageEntity.STEP_INTERNAL_LINK, 
							Clarity360ReportStageEntity.STATUS_ERROR, StringUtils.substring(e.getMessage(), 0, 400));
				}
				
			} else if (StringUtils.equals(funcName, "backlink")) {
				System.out.println(" === processing backlink ");
        		insertNewStage(reportId, Clarity360ReportStageEntity.STATUS_PROCESSING, Clarity360ReportStageEntity.STEP_BACKLINK);
        		
        		try {
        			BacklinkVO backlinkVO = gson.fromJson(gson.toJson(sourceMap.get(funcName)), BacklinkVO.class);
        			
        			Integer totalCount = clarity360Merge03DAO.getTotalCount(ownDomainId, backlinkVO, ownDomainEntity,
    						targetDate, reportId, backlinkVO.getUrlTagId(), backlinkVO.getContentTypeFilterVO());
        			
        			summaryByDataSource(backlinkVO, totalCount, pageSize, funcName, Clarity360ReportStageEntity.STEP_BACKLINK);
        			
    				clarity360ReportStageEntityDAO.updateStatus(reportId, Clarity360ReportStageEntity.STEP_BACKLINK, Clarity360ReportStageEntity.STATUS_COMPLETE, "");
        		} catch (Exception e) {
        			e.printStackTrace();
					clarity360ReportStageEntityDAO.updateStatus(reportId, Clarity360ReportStageEntity.STEP_BACKLINK, 
							Clarity360ReportStageEntity.STATUS_ERROR, StringUtils.substring(e.getMessage(), 0, 400));
				}
				
			} else if (StringUtils.equals(funcName, "customDataSource")) {
				System.out.println(" === processing customDataSource ");
        		insertNewStage(reportId, Clarity360ReportStageEntity.STATUS_PROCESSING, Clarity360ReportStageEntity.STEP_CUSTOM_DATA_SOURCE);
        		
        		try {
        			CustomDataSourceVO customDataSourceVO = gson.fromJson(gson.toJson(sourceMap.get(funcName)), CustomDataSourceVO.class);
        			
        			Integer totalCount = clarity360Merge03DAO.getTotalCount(ownDomainId, customDataSourceVO, ownDomainEntity,
    						targetDate, reportId, customDataSourceVO.getUrlTagId(), customDataSourceVO.getContentTypeFilterVO());
        			
        			summaryByDataSource(customDataSourceVO, totalCount, pageSize, funcName, Clarity360ReportStageEntity.STEP_CUSTOM_DATA_SOURCE);
        			
    				clarity360ReportStageEntityDAO.updateStatus(reportId, Clarity360ReportStageEntity.STEP_CUSTOM_DATA_SOURCE, Clarity360ReportStageEntity.STATUS_COMPLETE, "");
        		} catch (Exception e) {
        			e.printStackTrace();
					clarity360ReportStageEntityDAO.updateStatus(reportId, Clarity360ReportStageEntity.STEP_CUSTOM_DATA_SOURCE, 
							Clarity360ReportStageEntity.STATUS_ERROR, StringUtils.substring(e.getMessage(), 0, 400));
				}
				
			} else {
				System.out.println("Un-support func name:" + funcName);
			}
        	
        }
    }
    
    private Boolean isInternalLinkFinished(String message) {
    	
    	boolean isDataSourceHaveInternalLink = false;
    	
    	JSONObject jsonObject = new JSONObject(message);
    	JSONArray source = jsonObject.getJSONArray("sources");
        for(Object sourceJson : source) {
        	Map sourceMap = gson.fromJson(String.valueOf(sourceJson), Map.class);
        	String funcName = gson.toJson(sourceMap.keySet().toArray()[0]);
        	
        	funcName =  StringUtils.removeEnd(StringUtils.removeStart(funcName, "\""), "\"");
        	
        	if (StringUtils.equals(funcName, "internalLink")) {
        		
        		isDataSourceHaveInternalLink = true;
        		try {
        			InternalLinkVO internalLinkVO = gson.fromJson(gson.toJson(sourceMap.get(funcName)), InternalLinkVO.class);
        			
        			CrawlRequestLog crawlRequestLog = crawlRequestLogDAO.getCrawlerByOwnDomainIdAndCrawlId(ownDomainId, internalLinkVO.getCrawlRequestId());
        			if (crawlRequestLog != null && crawlRequestLog.getAdditionalStatusV2() == CrawlRequestLog.INTERNALLINK_DATA_STATUS_COMPLETE) {
						return true;
					} else {
						return false;
					}
        			
        		} catch (Exception e) {
        			e.printStackTrace();
        			System.out.println("Param parse failed.");
				}
        	}
        }
        
        if (!isDataSourceHaveInternalLink) {
        	return true;
		}
        return false;
    }
    
    private void summaryByDataSource(Clarity360BaseVO clarity360BaseVO, Integer totalCount, Integer pageSize, String funcName, Integer step) {
		
		System.out.println("Type: " + step + ", Total Count :" + totalCount + ", pageSize:" + pageSize);
		int partitionNum = 0;
		if (totalCount >= pageSize) {
			partitionNum = (totalCount / pageSize) + 1;
		} else {
			partitionNum = 1;
		}
		
        try { 
        	System.out.println("====== processing " + funcName + " for urlmurmurhash");
    		for(int i = 0; i < partitionNum; i ++) {
    			try { 
    				System.out.println("Summary :" + (i + 1) + " of " + partitionNum);
    				
    				summaryMurmurHashByFunc(clarity360BaseVO, funcName, partitionNum, i);
    				try {
    					System.out.println("Sleep 1 sec...");
    					Thread.sleep(1 * 1000);
    				} catch (Exception e) {
						e.printStackTrace();
					}
    			} catch (Exception exception) {
    	            exception.printStackTrace();
    	        }
    			
    		}
    		
    		if (StringUtils.equals(funcName, "siteHealth")) {
            	SiteHealthVO siteHealthVO = (SiteHealthVO) clarity360BaseVO;
            	disSiteCrawlDoc1For360SummaryOnlyDao.siteHealthClarity360SummaryV2Part2(ownDomainId, siteHealthVO, 
            			targetDate, reportId, siteHealthVO.getUrlTagId(), siteHealthVO.getContentTypeFilterVO(), "url_murmur_hash", false, 2);
            }
        } catch (Exception e) {
        	exceptionMap.put(step, e.getLocalizedMessage());
            e.printStackTrace();
        }
        
        try { 
        	System.out.println("====== processing " + funcName + " for urlhash");
    		for(int i = 0; i < partitionNum; i ++) {
    			try { 
    				System.out.println("Summary :" + (i + 1) + " of " + partitionNum);
    				
    				summaryUrlHashByFunc(clarity360BaseVO, funcName, partitionNum, i);
    				try {
    					System.out.println("Sleep 1 sec...");
    					Thread.sleep(1 * 1000);
    				} catch (Exception e) {
						e.printStackTrace();
					}
    			} catch (Exception exception) {
    	            exception.printStackTrace();
    	        }
    			
    		}
    		
    		if (StringUtils.equals(funcName, "siteHealth")) {
            	SiteHealthVO siteHealthVO = (SiteHealthVO) clarity360BaseVO;
            	disSiteCrawlDoc1For360SummaryOnlyDao.siteHealthClarity360SummaryV2Part2(ownDomainId, siteHealthVO, 
            			targetDate, reportId, siteHealthVO.getUrlTagId(), siteHealthVO.getContentTypeFilterVO(), "URLHash(lower(url))", true, 2);
            }
    		
        } catch (Exception e) {
        	exceptionMap.put(step, e.getLocalizedMessage());
            e.printStackTrace();
        }
        
        
    }
    
    
    private void summaryMurmurHashByFunc(Clarity360BaseVO clarity360BaseVO, String funcName, Integer partitionNum, Integer pageNum) throws Exception {
    	OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getById(ownDomainId);
		
    	int num = 0;
		while (true) {
			
			try {
				if(num > 0) {
					System.out.println("!! Retry " + num + " times");
				}
				
				if (StringUtils.equals(funcName, "siteHealth")) {
		    		System.out.println(" === processing Site Health ");
		    		SiteHealthVO siteHealthVO = (SiteHealthVO) clarity360BaseVO;
		    		
		    		disSiteCrawlDoc1For360SummaryOnlyDao.siteHealthClarity360SummaryV2Part1(ownDomainId, siteHealthVO, 
                			targetDate, reportId, siteHealthVO.getUrlTagId(), siteHealthVO.getContentTypeFilterVO(), "url_murmur_hash", false, partitionNum, pageNum, 2);
				} else if (StringUtils.equals(funcName, "searchAnalytics")) {
					System.out.println(" === processing Gsc ");
					GscVO gscVO = (GscVO) clarity360BaseVO;
					if(gscVO.getBigQuery() != null && gscVO.getBigQuery()) {
						gscBaseDao.gscBigQuery360SummaryByPage(ownDomainId, gscVO, targetDate, reportId, 
								gscVO.getUrlTagId(), gscVO.getContentTypeFilterVO(), false, "url_murmur3_hash", partitionNum, pageNum, 2);
					} else {
						gscBaseDao.gscClarity360SummaryMergedByPage(ownDomainId, gscVO, targetDate, reportId, 
			    				gscVO.getUrlTagId(), gscVO.getContentTypeFilterVO(), partitionNum, pageNum, 2);
					}
					
				} else if (StringUtils.equals(funcName, "siteAnalytics")) {
					System.out.println(" === processing Ga ");
					GaVO gaVO = (GaVO) clarity360BaseVO;
					
					ga001ClarityDBEntityDAO.gaClarity360Summary(gaVO.getTrafficType(), ownDomainId, 
	            			gaVO, targetDate, reportId, GA_GA4_DOMAIN_MAP, gaVO.getUrlTagId(), gaVO.getContentTypeFilterVO(), 
	            			gaVO.getOwnDomainProtocol(), "url_murmur_hash", false, partitionNum, pageNum, 2);
				} else if (StringUtils.equals(funcName, "bot")) {
					System.out.println(" === processing Bot ");
					BotVO botVO = (BotVO)clarity360BaseVO;
		    		
					botDetailDao.botClarity360Summary(ownDomainId, botVO, targetDate, 
	            			reportId, botVO.getUrlTagId(), botVO.getContentTypeFilterVO(), "murmurHash3_64(dis_bot_detail.url)", false, partitionNum, pageNum, 2);
	            	
				} else if (StringUtils.equals(funcName, "siteMap")) {
					System.out.println(" === processing Site Map ");
					SiteMapVO siteMapVO = (SiteMapVO)clarity360BaseVO;
		    		
					siteMapDetailDao.siteMapClarity360SummaryV2(ownDomainId, siteMapVO, 
		        			targetDate, reportId, siteMapVO.getUrlTagId(), siteMapVO.getContentTypeFilterVO(), "murmurHash3_64(url)", false, partitionNum, pageNum, 2);
				} else if (StringUtils.equals(funcName, "ranking")) {
					System.out.println(" === processing Rank ");
					RiVO riVO = (RiVO)clarity360BaseVO;
		    		
					clDailyRankingEntityDao.riClarity360Summary(ownDomainId, riVO, ownDomainEntity, targetDate, 
	            			reportId, ALTERNATE_DOMAIN_MAP, riVO.getUrlTagId(), riVO.getContentTypeFilterVO(), "murmurHash3_64(url)", false, partitionNum, pageNum, 2);
	            	
				} else if (StringUtils.equals(funcName, "researchGrid")) {
					System.out.println(" === processing RG ");
					RgVO rgVO = (RgVO)clarity360BaseVO;
					
					lwebMonthlyRankingDao.processRgSummary(ownDomainId, rgVO, ownDomainEntity, targetDate, 
	            			reportId, rgVO.getUrlTagId(), rgVO.getContentTypeFilterVO(), "murmurHash3_64(url)", false, partitionNum, pageNum, 2);
				} else if (StringUtils.equals(funcName, "internalLink")) {
					System.out.println(" === processing internalLink ");
					InternalLinkVO internalLinkVO = (InternalLinkVO)clarity360BaseVO;
					
					clarity360Merge03DAO.processInternalLinkSummary(ownDomainId, internalLinkVO, 
		        			targetDate, reportId, internalLinkVO.getUrlTagId(), internalLinkVO.getContentTypeFilterVO(), "destinationUrlMurmurHash", false, partitionNum, pageNum);
					
				} else if (StringUtils.equals(funcName, "backlink")) {
					System.out.println(" === processing backlink ");
					BacklinkVO backlinkVO = (BacklinkVO)clarity360BaseVO;
					
					clarity360Merge03DAO.backLink360Summary(ownDomainId, backlinkVO, ownDomainEntity,
							targetDate, reportId, backlinkVO.getUrlTagId(), backlinkVO.getContentTypeFilterVO(), "murmurHash3_64(target_url)", false, partitionNum, pageNum);
					
				} else if (StringUtils.equals(funcName, "customDataSource")) {
					System.out.println(" === processing customDataSource ");
					CustomDataSourceVO customDataSourceVO = (CustomDataSourceVO)clarity360BaseVO;
					
					clarity360Merge03DAO.customDataSource360Summary(ownDomainId, customDataSourceVO, ownDomainEntity,
							targetDate, reportId, customDataSourceVO.getUrlTagId(), customDataSourceVO.getContentTypeFilterVO(), "url_murmur_hash", false, partitionNum, pageNum);
				} else {
					System.out.println("funcName not support! funcName : " + funcName); 
				}
				break;
			} catch (Exception e) {
				e.printStackTrace();
				try {
					System.out.println("Sleep 10 sec...");
					Thread.sleep(10 * 1000);
				} catch (Exception e2) {
					e2.printStackTrace();
				}
				
				if (num >= MAX_RETRY_NUMBER) {
					throw e;
				}
			}
			
			num++;
		}
    	
    }
    
    
    private void summaryUrlHashByFunc(Clarity360BaseVO clarity360BaseVO, String funcName, Integer partitionNum, Integer pageNum) throws Exception {
    	OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getById(ownDomainId);
		
    	int num = 0;
		while (true) {
			
			try {
				if(num > 0) {
					System.out.println("!! Retry " + num + " times");
				}
				
				if (StringUtils.equals(funcName, "siteHealth")) {
		    		System.out.println(" === processing Site Health ");
		    		SiteHealthVO siteHealthVO = (SiteHealthVO) clarity360BaseVO;
		    		disSiteCrawlDoc1For360SummaryOnlyDao.siteHealthClarity360SummaryV2Part1(ownDomainId, siteHealthVO, 
                			targetDate, reportId, siteHealthVO.getUrlTagId(), siteHealthVO.getContentTypeFilterVO(), "URLHash(lower(url))", true, partitionNum, pageNum, 2);
				} else if (StringUtils.equals(funcName, "searchAnalytics")) {
					System.out.println(" === processing Gsc ");
					GscVO gscVO = (GscVO) clarity360BaseVO;
					if(gscVO.getBigQuery() != null && gscVO.getBigQuery()) {
						gscBaseDao.gscBigQuery360SummaryByPage(ownDomainId, gscVO, targetDate, reportId, 
								gscVO.getUrlTagId(), gscVO.getContentTypeFilterVO(), true, "url_hash", partitionNum, pageNum, 2);
					} else {
						gscBaseDao.gscClarity360SummaryMergedForUrlHashByPage(ownDomainId, gscVO, targetDate, reportId, 
			    				gscVO.getUrlTagId(), gscVO.getContentTypeFilterVO(), partitionNum, pageNum, 2);
					}
					
				} else if (StringUtils.equals(funcName, "siteAnalytics")) {
					System.out.println(" === processing Ga ");
					GaVO gaVO = (GaVO) clarity360BaseVO;
					
					ga001ClarityDBEntityDAO.gaClarity360Summary(gaVO.getTrafficType(), ownDomainId, 
	            			gaVO, targetDate, reportId, GA_GA4_DOMAIN_MAP, gaVO.getUrlTagId(), gaVO.getContentTypeFilterVO(), 
	            			gaVO.getOwnDomainProtocol(), "url_hash", true, partitionNum, pageNum, 2);
				} else if (StringUtils.equals(funcName, "bot")) {
					System.out.println(" === processing Bot ");
		    		BotVO botVO = (BotVO)clarity360BaseVO;
		    		
					botDetailDao.botClarity360Summary(ownDomainId, botVO, targetDate, 
	            			reportId, botVO.getUrlTagId(), botVO.getContentTypeFilterVO(), "urlhash", true, partitionNum, pageNum, 2);
				} else if (StringUtils.equals(funcName, "siteMap")) {
					System.out.println(" === processing Site Map ");
					SiteMapVO siteMapVO = (SiteMapVO)clarity360BaseVO;
		    		
		        	siteMapDetailDao.siteMapClarity360SummaryV2(ownDomainId, siteMapVO, 
		        			targetDate, reportId, siteMapVO.getUrlTagId(), siteMapVO.getContentTypeFilterVO(), "URLHash(lower(url))", true, partitionNum, pageNum, 2);	
				} else if (StringUtils.equals(funcName, "ranking")) {
					System.out.println(" === processing Rank ");
					RiVO riVO = (RiVO)clarity360BaseVO;
		    		
					clDailyRankingEntityDao.riClarity360Summary(ownDomainId, riVO, ownDomainEntity, targetDate, 
	            			reportId, ALTERNATE_DOMAIN_MAP, riVO.getUrlTagId(), riVO.getContentTypeFilterVO(), "URLHash(lower(url))", true, partitionNum, pageNum, 2);
				} else if (StringUtils.equals(funcName, "researchGrid")) {
					System.out.println(" === processing RG ");
					RgVO rgVO = (RgVO)clarity360BaseVO;
					
	            	lwebMonthlyRankingDao.processRgSummary(ownDomainId, rgVO, ownDomainEntity, targetDate, 
	            			reportId, rgVO.getUrlTagId(), rgVO.getContentTypeFilterVO(), "urlhash", true, partitionNum, pageNum, 2);
				} else if (StringUtils.equals(funcName, "internalLink")) {
					System.out.println(" === processing internalLink ");
					InternalLinkVO internalLinkVO = (InternalLinkVO)clarity360BaseVO;
					
					clarity360Merge03DAO.processInternalLinkSummary(ownDomainId, internalLinkVO, 
		        			targetDate, reportId, internalLinkVO.getUrlTagId(), internalLinkVO.getContentTypeFilterVO(), "URLHash(lower(destUrl))", true, partitionNum, pageNum);
				} else if (StringUtils.equals(funcName, "backlink")) {
					System.out.println(" === processing backlink ");
					BacklinkVO backlinkVO = (BacklinkVO)clarity360BaseVO;
					
					clarity360Merge03DAO.backLink360Summary(ownDomainId, backlinkVO, ownDomainEntity,
							targetDate, reportId, backlinkVO.getUrlTagId(), backlinkVO.getContentTypeFilterVO(), "URLHash(lower(target_url))", true, partitionNum, pageNum);
				} else if (StringUtils.equals(funcName, "customDataSource")) {
					System.out.println(" === processing customDataSource ");
					CustomDataSourceVO customDataSourceVO = (CustomDataSourceVO)clarity360BaseVO;
					
					clarity360Merge03DAO.customDataSource360Summary(ownDomainId, customDataSourceVO, ownDomainEntity,
							targetDate, reportId, customDataSourceVO.getUrlTagId(), customDataSourceVO.getContentTypeFilterVO(), "url_hash", true, partitionNum, pageNum);
				} else {
					System.out.println("funcName not support! funcName : " + funcName); 
				}
				break;
			} catch (Exception e) {
				e.printStackTrace();
				try {
					System.out.println("Sleep 10 sec...");
					Thread.sleep(10 * 1000);
				} catch (Exception e2) {
					e2.printStackTrace();
				}
				
				if (num >= MAX_RETRY_NUMBER) {
					throw e;
				}
			}
			
			num++;
			
		}
    	
    }
	

    
    
    private Clarity360SummaryOptCommand getMergeCommand(String ip, String[] content) {	
    	//String ip, Clarity360Lweb01DAO clarity360Lweb01DAO, Integer serverId, String localTableName, String targetDate
    	Integer serverId = NumberUtils.toInt(content[0]);
    	String tableName = content[1];
    	Clarity360SummaryOptCommand crawlCommand;
//    	if (serverId == 1) {
//    		crawlCommand = new Clarity360SummaryOptCommand(
//    				ip, clarity360Lweb01DAO, serverId, tableName, targetDate, reportId);
//		} else if (serverId == 2) {
//			crawlCommand = new Clarity360SummaryOptCommand(
//    				ip, clarity360Lweb02DAO, serverId, tableName, targetDate, reportId);
//		} else if (serverId == 3) {
//			crawlCommand = new Clarity360SummaryOptCommand(
//    				ip, clarity360Lweb03DAO, serverId, tableName, targetDate, reportId);
//		} else if (serverId == 4) {
//			crawlCommand = new Clarity360SummaryOptCommand(
//    				ip, clarity360Lweb04DAO, serverId, tableName, targetDate, reportId);
//		} else if (serverId == 5) {
//			crawlCommand = new Clarity360SummaryOptCommand(
//    				ip, clarity360Lweb05DAO, serverId, tableName, targetDate, reportId);
//		} else 
			
		if (serverId == 3) {
			crawlCommand = new Clarity360SummaryOptCommand(
    				ip, clarity360Merge03DAO, serverId, tableName, targetDate, reportId);
		} else if (serverId == 4) {
			crawlCommand = new Clarity360SummaryOptCommand(
    				ip, clarity360Merge04DAO, serverId, tableName, targetDate, reportId);
		} else if (serverId == 5) {
			crawlCommand = new Clarity360SummaryOptCommand(
    				ip, clarity360Merge05DAO, serverId, tableName, targetDate, reportId);
		} else {
			System.out.println("invalid serverId, serverId:" + serverId);
			return null;
		}
    	
		crawlCommand.setStatus(true);
		return crawlCommand;
	}
    
	
	private void optimize() {
		
        
        
        try {
			threadPool.init();
			CommonUtils.initThreads(10);
			
			String[][] strArray = array.clone();
			
			do {

				String ipAddress = CacheModleFactory.getInstance().getAliveIpAddress();
				if (ipAddress == null) {
					Thread.sleep(1 * 1000);
					continue;
				}
				
				if (strArray == null || strArray.length == 0) {
					break;
				}
				
				String[][] commandData = (String[][]) ArrayUtils.subarray(strArray, 0, 1);
				strArray = (String[][])  ArrayUtils.remove(strArray, 0);
				
				//String ip, Integer processDate, 
				//String keyword, Response project, Integer engineId
				Clarity360SummaryOptCommand crawCommand = getMergeCommand(ipAddress, commandData[0]);
				try {
					threadPool.execute(crawCommand); 
				} catch (Exception e) {
					e.printStackTrace();
				}
				

			} while (true);
			
			do {
				try {
					Thread.sleep(5000);
				} catch (InterruptedException e) {
					e.printStackTrace();
				}
			} while (threadPool.getThreadPool().getActiveCount() > 0);
			
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			threadPool.destroy();
		}
        
	}
	
    private void insertNewStage(Integer reportId, Integer status, Integer dateSourceType) {
    	try {
    		Clarity360ReportStageEntity clarity360ReportStageEntity = new Clarity360ReportStageEntity();
        	
        	clarity360ReportStageEntity.setReportId(reportId);
        	clarity360ReportStageEntity.setStatus(status);
        	clarity360ReportStageEntity.setDataSource(dateSourceType);
        	clarity360ReportStageEntity.setProcessStartTime(new Date());
        	
        	clarity360ReportStageEntityDAO.insertAutoRunDetail(clarity360ReportStageEntity);
		} catch (Exception e) {
			e.printStackTrace();
		}
    	
    }
	
}
