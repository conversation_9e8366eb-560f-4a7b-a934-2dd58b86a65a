package seoclarity.backend.clarity360;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.util.CollectionUtils;

import com.google.gson.Gson;

import seoclarity.backend.clarity360.exception.TaskException;
import seoclarity.backend.clarity360.vo.BacklinkVO;
import seoclarity.backend.clarity360.vo.BotVO;
import seoclarity.backend.clarity360.vo.Clarity360BaseVO;
import seoclarity.backend.clarity360.vo.CustomDataSourceVO;
import seoclarity.backend.clarity360.vo.GaVO;
import seoclarity.backend.clarity360.vo.GscVO;
import seoclarity.backend.clarity360.vo.InternalLinkVO;
import seoclarity.backend.clarity360.vo.RgVO;
import seoclarity.backend.clarity360.vo.RiVO;
import seoclarity.backend.clarity360.vo.SiteHealthVO;
import seoclarity.backend.clarity360.vo.SiteMapVO;
import seoclarity.backend.dao.actonia.Clarity360ReportEntityDAO;
import seoclarity.backend.dao.actonia.Clarity360ReportStageEntityDAO;
import seoclarity.backend.dao.actonia.CrawlRequestLogDAO;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.actonia.OwnDomainSettingEntityDAO;
import seoclarity.backend.dao.actonia.gabigquery.GaBqBulkExportInfoDAO;
import seoclarity.backend.dao.clickhouse.ClDailyRankingEntityDao;
import seoclarity.backend.dao.clickhouse.bot.BotDetailDao;
import seoclarity.backend.dao.clickhouse.bot.SiteMapDetailDao;
import seoclarity.backend.dao.clickhouse.clarity360.lweb05.Clarity360Lweb05DAO;
import seoclarity.backend.dao.clickhouse.clarity360.merge01.Clarity360Merge01DAO;
import seoclarity.backend.dao.clickhouse.ga.Ga001ClarityDBEntityDAO;
import seoclarity.backend.dao.clickhouse.gsc.GscBaseDao;
import seoclarity.backend.dao.clickhouse.monthlyranking.LwebMonthlyRankingDao;
import seoclarity.backend.dao.clickhouse.prodclarity.DisSiteCrawlDoc1For360SummaryOnlyDao;
import seoclarity.backend.dao.clickhouse.prodclarity.DisSiteCrawlDocDao;
import seoclarity.backend.dao.mdbkeywordsuggest.InternalLinkPageRankInstanceEntityDAO;
import seoclarity.backend.entity.actonia.Clarity360ReportEntity;
import seoclarity.backend.entity.actonia.Clarity360ReportStageEntity;
import seoclarity.backend.entity.actonia.CrawlRequestLog;
import seoclarity.backend.entity.actonia.InternalLinkPageRankInstanceEntity;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.actonia.OwnDomainSettingEntity;
import seoclarity.backend.entity.actonia.gabigquery.GaBqBulkexportInfoEntity;
import seoclarity.backend.multithread.core.thread.threadpool.ThreadPoolManager;
import seoclarity.backend.summary.Clarity360Entity;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.utils.ZeptoMailSenderComponent;

//mvn exec:java -Dexec.mainClass="seoclarity.backend.clarity360.Clarity360SummaryV6" -Dexec.args=""
//mvn exec:java -Dexec.mainClass="seoclarity.backend.clarity360.Clarity360SummaryV6" -Dexec.args="3036 4739 1"
public class Clarity360SummaryV6 {

	public static ThreadPoolManager threadPool = ThreadPoolManager.getInstance();
	
	//mysql
	private Clarity360ReportEntityDAO clarity360ReportEntityDAO;
	private Clarity360ReportStageEntityDAO clarity360ReportStageEntityDAO;
	private OwnDomainEntityDAO ownDomainEntityDAO;
	private OwnDomainSettingEntityDAO ownDomainSettingEntityDAO;
	private CrawlRequestLogDAO crawlRequestLogDAO;
	
	//360 cluster
//	private Clarity360Lweb01DAO clarity360Lweb01DAO;
//	private Clarity360Lweb02DAO clarity360Lweb02DAO;
//	private Clarity360Lweb03DAO clarity360Lweb03DAO;
//	private Clarity360Lweb04DAO clarity360Lweb04DAO;
	private Clarity360Lweb05DAO clarity360Lweb05DAO;
	
	private Clarity360Merge01DAO clarity360Merge01DAO;
	
	private InternalLinkPageRankInstanceEntityDAO internalLinkPageRankInstanceEntityDAO;
	
	//datasource
	//----site health
	private DisSiteCrawlDoc1For360SummaryOnlyDao disSiteCrawlDoc1For360SummaryOnlyDao;
	//----site analytics
	private Ga001ClarityDBEntityDAO ga001ClarityDBEntityDAO;
	//----site map
	private SiteMapDetailDao siteMapDetailDao;
	//----bot
	private BotDetailDao botDetailDao;
	//----gsc
	private GscBaseDao gscBaseDao;
	//----ri
	private ClDailyRankingEntityDao clDailyRankingEntityDao;
	//----rg
//	private MonthlyRankingDao monthlyRankingDao;
	private LwebMonthlyRankingDao lwebMonthlyRankingDao;
	//----internal link
//	private InternalLinkServer1Dao internalLinkServer1Dao;
//	//----backlink
//	private BacklinkBaseDao backlinkBaseDao;
	private DisSiteCrawlDocDao disSiteCrawlDocDao;
	public ZeptoMailSenderComponent zeptoMailSenderComponent;
	public GaBqBulkExportInfoDAO gaBqBulkExportInfoDAO;
	private static Map<Integer, String> exceptionMap = new HashMap<>();
	
	private static final Integer DEFAILT_PAGESIZE = 500000;
	private Integer pageSize = DEFAILT_PAGESIZE;
	private static final Integer SITE_HEALTH_SUMMARY_DEFAILT_PAGESIZE = 300000;
	private static final Integer EXPEDIA_SITE_HEALTH_SUMMARY_PAGESIZE = 10000;
	private static final Integer EXPEDIA_DOMAIN_ID = 4765;
	private static final Integer MAX_RETRY_NUMBER = 10;
	
	private static final Integer DEFAILT_LOAD_PAGESIZE = 100000;
	
	
    public static String localTableName = "local_360_table_v3";
    public static String localCustomTableName = "local_360_custom_urlmurmurhash_v3";
    
    
	
	
//	private static List<Class> classList = new ArrayList<>();
//	static {
//		classList.add(Clarity360Lweb01DAO.class);
//		classList.add(Clarity360Lweb02DAO.class);
//		classList.add(Clarity360Lweb03DAO.class);
//		classList.add(Clarity360Lweb04DAO.class);
//		classList.add(Clarity360Lweb05DAO.class);
//	}

	public Clarity360SummaryV6() {
		ownDomainSettingEntityDAO = SpringBeanFactory.getBean("ownDomainSettingEntityDAO");
		ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
		clarity360ReportEntityDAO = SpringBeanFactory.getBean("clarity360ReportEntityDAO");
		clarity360ReportStageEntityDAO = SpringBeanFactory.getBean("clarity360ReportStageEntityDAO");
		
//		backlinkBaseDao = SpringBeanFactory.getBean("backlinkBaseDao");
//		internalLinkServer1Dao = SpringBeanFactory.getBean("internalLinkServer1Dao");
//		monthlyRankingDao = SpringBeanFactory.getBean("monthlyRankingDao");
		lwebMonthlyRankingDao = SpringBeanFactory.getBean("lwebMonthlyRankingDao");
		disSiteCrawlDoc1For360SummaryOnlyDao = SpringBeanFactory.getBean("disSiteCrawlDoc1For360SummaryOnlyDao");
		ga001ClarityDBEntityDAO = SpringBeanFactory.getBean("ga001ClarityDBEntityDAO");
		siteMapDetailDao = SpringBeanFactory.getBean("siteMapDetailDao");
		botDetailDao = SpringBeanFactory.getBean("botDetailDao");
		gscBaseDao = SpringBeanFactory.getBean("gscBaseDao");
		clDailyRankingEntityDao = SpringBeanFactory.getBean("clDailyRankingEntityDao");
		crawlRequestLogDAO = SpringBeanFactory.getBean("crawlRequestLogDAO");
		disSiteCrawlDocDao = SpringBeanFactory.getBean("disSiteCrawlDocDao");
		
//		clarity360Lweb01DAO = SpringBeanFactory.getBean("clarity360Lweb01DAO");
//		clarity360Lweb02DAO = SpringBeanFactory.getBean("clarity360Lweb02DAO");
//		clarity360Lweb03DAO = SpringBeanFactory.getBean("clarity360Lweb03DAO");
//		clarity360Lweb04DAO = SpringBeanFactory.getBean("clarity360Lweb04DAO");
		clarity360Lweb05DAO = SpringBeanFactory.getBean("clarity360Lweb05DAO");
		gaBqBulkExportInfoDAO = SpringBeanFactory.getBean("gaBqBulkExportInfoDAO");
		
		clarity360Merge01DAO = SpringBeanFactory.getBean("clarity360Merge01DAO");
		zeptoMailSenderComponent = SpringBeanFactory.getBean("zeptoMailSenderComponent");
		internalLinkPageRankInstanceEntityDAO = SpringBeanFactory.getBean("internalLinkPageRankInstanceEntityDAO");
		
	}
	
	public static Integer STAGE_1 = 1;
	public static Integer STAGE_2 = 2;
	
	public static Map<String, Integer> FUNCTION_STAGE_MAP = new HashMap<String, Integer>();
	static {
		
		FUNCTION_STAGE_MAP.put("backlink", STAGE_1);
		FUNCTION_STAGE_MAP.put("bot", STAGE_1);
		FUNCTION_STAGE_MAP.put("customDataSource", STAGE_1);
		FUNCTION_STAGE_MAP.put("ranking", STAGE_1);
		FUNCTION_STAGE_MAP.put("researchGrid", STAGE_1);
		FUNCTION_STAGE_MAP.put("searchAnalytics", STAGE_1);
		FUNCTION_STAGE_MAP.put("siteAnalytics", STAGE_1);
		FUNCTION_STAGE_MAP.put("siteMap", STAGE_1);
		FUNCTION_STAGE_MAP.put("siteHealth", STAGE_1);
		
		FUNCTION_STAGE_MAP.put("internalLink", STAGE_2);
	}
	
	private static Integer BACK_PROCESS_REPORT_ID = 0;
	private static Integer BACK_PROCESS_DOMAIN_ID = 0;
	private static Integer VERSION = 1;

	public static void main(String[] args) {
		
		Clarity360SummaryV6 clarity360Summary = new Clarity360SummaryV6();
		if(args != null && args.length >= 3) {
			BACK_PROCESS_REPORT_ID = NumberUtils.toInt(args[0]);
			BACK_PROCESS_DOMAIN_ID = NumberUtils.toInt(args[1]);
			VERSION = NumberUtils.toInt(args[2]);
			System.out.println("==== backprocess for REPORT: " + BACK_PROCESS_REPORT_ID + ", OID:" + BACK_PROCESS_DOMAIN_ID + ", VERSION:" + VERSION);
		}
		
		clarity360Summary.process();
	}
	
    private Gson gson = new Gson();
    
    
    private void process() {
    	
    	if (BACK_PROCESS_REPORT_ID > 0 && BACK_PROCESS_DOMAIN_ID > 0) {
    		Clarity360ReportEntity clarity360ReportEntity = clarity360ReportEntityDAO.getReportById(BACK_PROCESS_REPORT_ID, BACK_PROCESS_DOMAIN_ID);
    		Date startDate = new Date();
    		if(clarity360ReportEntity == null){
    			System.out.println("Report not found! REPORT:" +  + BACK_PROCESS_REPORT_ID + ", OID:" + BACK_PROCESS_DOMAIN_ID);
    			return;
    		}
    		
    		Integer totalCount = clarity360Merge01DAO.getTotalCountByReportId(BACK_PROCESS_REPORT_ID, BACK_PROCESS_DOMAIN_ID);
    		
    		if(totalCount > 0) {
    			System.out.println("===== Still get data in CDB, can not backprocess now! totalCount:" + totalCount);
    			return;
    		}
    		
    		System.out.println("========== start to update status ===========");
    		
    		clarity360ReportStageEntityDAO.deleteByReportId(BACK_PROCESS_REPORT_ID);
    		clarity360ReportEntityDAO.updateStatusAndStartDate(Clarity360ReportEntity.PROCESS_STATUS_NEED_PROCESS, BACK_PROCESS_REPORT_ID);
    		
    		processTask(clarity360ReportEntity);
    		
    		OwnDomainSettingEntity ownDomainSettingEntity = ownDomainSettingEntityDAO.getSettingByDomainId(clarity360ReportEntity.getOwnDomainId());
    		if(ownDomainSettingEntity == null) {
    			System.out.println("==== domain can not found! OID:" + clarity360ReportEntity.getOwnDomainId());
    			return;
    		}
    		
    		if(ownDomainSettingEntity.getC360AppendPostprocessing() && clarity360ReportEntity.getStageEnabled() != null && clarity360ReportEntity.getStageEnabled()) {
    			Date endDate = new Date();
        		
    			//int id, int ownDomainId, Date startDate, Date endDate, Integer stage, Integer stageStatus
    			clarity360ReportEntityDAO.updateStage2Detail(BACK_PROCESS_REPORT_ID, BACK_PROCESS_DOMAIN_ID, startDate, endDate, 
    					Clarity360ReportEntity.CURRENT_STAGE_2, Clarity360ReportEntity.PROCESS_STATUS_DONE);
    		}
    		
		} else {
			List<Clarity360ReportEntity> resultList = clarity360ReportEntityDAO.getNeedProcessReportList();
	    	if(CollectionUtils.isEmpty(resultList)) {
	    		
	    		System.out.println(" No Task! ");
	    		return;
	    	}
	    	
	    	for(Clarity360ReportEntity clarity360ReportEntity : resultList) {
	    		
	    		clarity360ReportEntity = clarity360ReportEntityDAO.getReportById(clarity360ReportEntity.getId(), clarity360ReportEntity.getOwnDomainId());
	    		
	    		OwnDomainSettingEntity ownDomainSettingEntity = ownDomainSettingEntityDAO.getSettingByDomainId(clarity360ReportEntity.getOwnDomainId());
	    		if(ownDomainSettingEntity == null) {
	    			System.out.println("==== domain can not found! OID:" + clarity360ReportEntity.getOwnDomainId());
	    			continue;
	    		}
	    		
	    		//https://www.wrike.com/open.htm?id=1465595832
	    		// check if domain enabled and report enabled
	    		if(ownDomainSettingEntity != null && ownDomainSettingEntity.getC360AppendPostprocessing() != null && ownDomainSettingEntity.getC360AppendPostprocessing()) {
	    			
	    			// process 1. processStatus = 0 and (stage1Status is null or 0)
	    			//         2. processStatus = 2 and stage1Status = 2 and (stage2Status is null or 0)
	    			if((clarity360ReportEntity.getProcessStatus() == Clarity360ReportEntity.PROCESS_STATUS_NEED_PROCESS && 
	    					(clarity360ReportEntity.getStage1Status() == null ||  
	    					clarity360ReportEntity.getStage1Status() == Clarity360ReportEntity.PROCESS_STATUS_NEED_PROCESS))
	    					
	    					|| (clarity360ReportEntity.getProcessStatus() == Clarity360ReportEntity.PROCESS_STATUS_DONE && 
	    							clarity360ReportEntity.getStage1Status() == Clarity360ReportEntity.PROCESS_STATUS_DONE &&
	    							(clarity360ReportEntity.getStage2Status() == null ||  
	    	    					clarity360ReportEntity.getStage2Status() == Clarity360ReportEntity.PROCESS_STATUS_NEED_PROCESS))){
		    			
	    				System.out.println("===== processing on postprocessing task ! ID:" + clarity360ReportEntity.getId());
		    			
		    			// if domain open this flag, then the summary will be split to 2 part
		    			// 1. all data source except internal link
		    			// 2. internal link and page rank(part of data in internal link)
		    			
		    			Date startDate = new Date();
		    			Integer currentStage = clarity360ReportEntity.getCurrentAvailableStage();
		    			if(clarity360ReportEntity.getCurrentAvailableStage() == null || clarity360ReportEntity.getCurrentAvailableStage() == 0) {
		    				currentStage = Clarity360ReportEntity.CURRENT_STAGE_1;
		    			} else if(currentStage == Clarity360ReportEntity.CURRENT_STAGE_1){
		    				currentStage = Clarity360ReportEntity.CURRENT_STAGE_2;
		    			} else {
		    				System.out.println("currentStage incorrect! skip!!!  currentStage:" + currentStage);
		    				return;
		    			}
		    			
		    			try {
		    				processPostprocessingTask(clarity360ReportEntity, currentStage);
		    			} catch (TaskException e) {
		    				continue;
		    			}
		    			
		    			Date endDate = new Date();
		    			
		    			System.out.println(" update stage status, currentStage:" + currentStage);
		    			if(currentStage == Clarity360ReportEntity.CURRENT_STAGE_1) {
		    				clarity360ReportEntityDAO.updateStage1Detail(reportId, ownDomainId, startDate, endDate, currentStage, Clarity360ReportEntity.PROCESS_STATUS_DONE);
		    			} else if(currentStage == Clarity360ReportEntity.CURRENT_STAGE_2) {
		    				clarity360ReportEntityDAO.updateStage2Detail(reportId, ownDomainId, startDate, endDate, currentStage, Clarity360ReportEntity.PROCESS_STATUS_DONE);
		    			}
		    		} else {
		    			System.out.println("===== task processing, skip! reportId : " + reportId + ", processStatus:" 
		    					+ clarity360ReportEntity.getProcessStatus() + ", stage1Status ： " 
		    					+ clarity360ReportEntity.getStage1Status() + ", stage2Status ： " 
		    					+ clarity360ReportEntity.getStage2Status());
		    			continue;
		    		}
	    			
	    			
	    		} else {
	    			
	    			if(clarity360ReportEntity.getProcessStatus() != Clarity360ReportEntity.PROCESS_STATUS_NEED_PROCESS){
		    			System.out.println("===== task processing, skip!");
		    			continue;
		    		}
	    			
		    		Integer totalCount = clarity360Merge01DAO.getTotalCountByReportId(clarity360ReportEntity.getId(), clarity360ReportEntity.getOwnDomainId());
		    		
		    		if(totalCount > 0) {
		    			System.out.println("===== Already get data in CDB, skip now! totalCount:" + totalCount);
		    			continue;
		    		}
		    		
	    			processTask(clarity360ReportEntity);
	    		}
	    		
	    	}
		}
    }
    
    public static List<Integer> GA_GA4_DOMAIN_MAP = new ArrayList<>();
    public static List<Integer> GA_GA4_BQ_DOMAIN_MAP = new ArrayList<>();
    public static Map<String, String> ALTERNATE_DOMAIN_MAP = new HashMap<String, String>();
    
    private Integer ownDomainId = null;
    private Integer reportId = null;
    private String targetDate = "";
    
    public Map<String, Integer> sourceDataCountMap = new HashMap<String, Integer>();
    

    private void processTask(Clarity360ReportEntity clarity360ReportEntity) {
    	
    	System.out.println("### proc report:" + clarity360ReportEntity.getId() + ", oid:" + clarity360ReportEntity.getOwnDomainId());
		try {
			
			//===================== GA4 domain ======================
	    	List<OwnDomainEntity> mapList = ownDomainEntityDAO.getGa4Domain();
	        if (mapList != null) {
	            mapList.stream().forEach(ownDomainEntity -> {
	                GA_GA4_DOMAIN_MAP.add(ownDomainEntity.getId());
	            });
	            System.out.println("GA4 domain cnt:" + mapList.size());
	        }
	        
	        List<GaBqBulkexportInfoEntity> ga4BqMapList = gaBqBulkExportInfoDAO.getBulkExportInfoList();
	        if (ga4BqMapList != null) {
	        	ga4BqMapList.stream().forEach(GaBqBulkexportInfoEntity -> {
	        		GA_GA4_BQ_DOMAIN_MAP.add(GaBqBulkexportInfoEntity.getDomainId());
	            });
	            System.out.println("GA4 BQ domain cnt:" + GA_GA4_BQ_DOMAIN_MAP.size());
	        }
	        
	        
	        //================ cache alternate domain ================
	        ALTERNATE_DOMAIN_MAP = ownDomainSettingEntityDAO.getAllAlternateDomainNames();
	        
	        System.out.println("alternate domain map cnt:" + ALTERNATE_DOMAIN_MAP.size());
	        //========================================================
	        
	    	String message = clarity360ReportEntity.getParamJson();
	    	reportId = clarity360ReportEntity.getId();
	    	ownDomainId = clarity360ReportEntity.getOwnDomainId();
	    	targetDate = FormatUtils.formatDate(clarity360ReportEntity.getCreateDate(), "yyyy-MM-dd");
	    	
	        System.out.println("===process OID:" + ownDomainId);
	        
	    	if (ownDomainId == null || ownDomainId == 0) {
	    		System.out.println(" !!! skip, Oid: " + ownDomainId);
//	    		clarity360ReportEntityDAO.updateStatusAndEndDate(Clarity360ReportEntity.PROCESS_STATUS_ERROR, clarity360ReportEntity.getId());
				return;
			}
	    	
	    	if (StringUtils.isBlank(targetDate)) {
	    		System.out.println(" !!! skip, targetDate is empty: " + targetDate);
//	    		clarity360ReportEntityDAO.updateStatusAndEndDate(Clarity360ReportEntity.PROCESS_STATUS_ERROR, clarity360ReportEntity.getId());
	    		return;
	    	}
	    	
	    	clarity360ReportEntityDAO.updateStatusAndStartDate(Clarity360ReportEntity.PROCESS_STATUS_PROCESSING, clarity360ReportEntity.getId());
	        
	    	
	    	Integer totalCount = clarity360Merge01DAO.getTotalCountByReportId(reportId, ownDomainId);
			if(totalCount > 0) {
				System.out.println("===== Still get merge data in CDB, can not backprocess now! totalCount:" + totalCount + ", reportId:" + reportId);
				return;
			}
			
			Integer totalCountInFinal = clarity360Lweb05DAO.getTotalCountByReportId(reportId, ownDomainId);
			if(totalCountInFinal > 0) {
				System.out.println("===== Still get data in CDB, can not backprocess now! totalCountInFinal:" + totalCountInFinal + ", reportId:" + reportId);
				return;
			}
	    	//============================================
			
			if(clarity360ReportEntity.getGenerateBy() == Clarity360ReportEntity.GENERATE_BY_360_LIMITED_TASK && StringUtils.isBlank(message)) {
				String messageTemplate = "{\"domainId\":\"%d\",\"sources\":[{\"siteHealth\":{\"crawlRequestId\":\"%d\",\"contentTypeFilters\":[],\"leftNaviFilters\":[]}},{\"siteMap\":{\"sitemapHashList\":[],\"urlList\":[],\"contentTypeFilters\":[],\"leftNaviFilters\":[]}},{\"internalLink\":{\"crawlRequestId\":\"%d\",\"contentTypeFilters\":[],\"leftNaviFilters\":[]}}]}";
				
				message = String.format(messageTemplate, ownDomainId, clarity360ReportEntity.getSiteAuditCrawlId(), clarity360ReportEntity.getSiteAuditCrawlId());
				
			}
	        
			
			  //step 1
			try {
				summary(message, clarity360ReportEntity, null);
			} catch (TaskException e) {
				clarity360ReportEntityDAO.updateStatusAndEndDate(Clarity360ReportEntity.PROCESS_STATUS_NEED_PROCESS, clarity360ReportEntity.getId());
				return ;
			}
	    	
	    	//step 2
//	        optimize();
	        
	        //step 3
	        loadDataIntoFinalTable(message, clarity360ReportEntity, VERSION);
	        
	        
	    	List<Clarity360Entity> sourceResult = clarity360Lweb05DAO.getDatasourceResultCountByReportId(reportId, ownDomainId);
	    	
	    	Map<String, Integer> targetSourcCntMap = sourceResult.stream()
	    	            .collect(Collectors.toMap(Clarity360Entity::getSource, Clarity360Entity::getCnt));
	    	System.out.println("sourceDataCountMap:" + gson.toJson(sourceDataCountMap));
	    	System.out.println("targetSourcCntMap:" + gson.toJson(sourceDataCountMap));
	    	clarity360ReportStageEntityDAO.batchUpdate(reportId, sourceDataCountMap, targetSourcCntMap);
	    	
//	    	for(String source : sourceDataCountMap.keySet()) {
//	    		try {
//	    			//updateTargetCount(Integer reportId, Integer dateSource, Integer status, Integer count)
//	    			clarity360ReportStageEntityDAO.updateTargetCount(reportId, getSourceIdByName(source), sourceDataCountMap.get(source));
//	    		} catch (Exception e) {
//	    			e.printStackTrace();
//	    		}
//	    	}
	            
	        clarity360ReportEntityDAO.updateStatusAndEndDate(Clarity360ReportEntity.PROCESS_STATUS_DONE, clarity360ReportEntity.getId());
			
		} catch (Exception e) {
			e.printStackTrace();
			clarity360ReportEntityDAO.updateStatusAndEndDate(Clarity360ReportEntity.PROCESS_STATUS_ERROR, clarity360ReportEntity.getId());
		}
		
    }
    
    
    private void processPostprocessingTask(Clarity360ReportEntity clarity360ReportEntity, Integer currentStage) {
    	
    	System.out.println("### proc report:" + clarity360ReportEntity.getId() + ", oid:" + clarity360ReportEntity.getOwnDomainId());
		try {
			
			//===================== GA4 domain ======================
	    	List<OwnDomainEntity> mapList = ownDomainEntityDAO.getGa4Domain();
	        if (mapList != null) {
	            mapList.stream().forEach(ownDomainEntity -> {
	                GA_GA4_DOMAIN_MAP.add(ownDomainEntity.getId());
	            });
	            System.out.println("GA4 domain cnt:" + mapList.size());
	        }
	        
	        //================ cache alternate domain ================
	        ALTERNATE_DOMAIN_MAP = ownDomainSettingEntityDAO.getAllAlternateDomainNames();
	        
	        System.out.println("alternate domain map cnt:" + ALTERNATE_DOMAIN_MAP.size());
	        //========================================================
	        
	    	String message = clarity360ReportEntity.getParamJson();
	    	reportId = clarity360ReportEntity.getId();
	    	ownDomainId = clarity360ReportEntity.getOwnDomainId();
	    	targetDate = FormatUtils.formatDate(clarity360ReportEntity.getCreateDate(), "yyyy-MM-dd");
	    	
	        System.out.println("===process OID:" + ownDomainId);
	        
	    	if (ownDomainId == null || ownDomainId == 0) {
	    		System.out.println(" !!! skip, Oid: " + ownDomainId);
//	    		clarity360ReportEntityDAO.updateStatusAndEndDate(Clarity360ReportEntity.PROCESS_STATUS_ERROR, clarity360ReportEntity.getId());
				return;
			}
	    	
	    	if (StringUtils.isBlank(targetDate)) {
	    		System.out.println(" !!! skip, targetDate is empty: " + targetDate);
//	    		clarity360ReportEntityDAO.updateStatusAndEndDate(Clarity360ReportEntity.PROCESS_STATUS_ERROR, clarity360ReportEntity.getId());
	    		return;
	    	}
	    	
	    	if(currentStage == Clarity360ReportEntity.CURRENT_STAGE_1){
	    		clarity360ReportEntityDAO.updateStatusAndStartDate(Clarity360ReportEntity.PROCESS_STATUS_PROCESSING, clarity360ReportEntity.getId());
			}
	        
	    	
	    	clarity360ReportEntityDAO.updateStageStatus(reportId, ownDomainId, currentStage, Clarity360ReportEntity.PROCESS_STATUS_PROCESSING);
	        
			Integer totalCountInFinal = clarity360Lweb05DAO.getTotalCountByReportIdAndVer(reportId, ownDomainId, currentStage);
			if(totalCountInFinal > 0) {
				System.out.println("===== Still get data in CDB, can not backprocess now! totalCountInFinal:" + totalCountInFinal + ", reportId:" + reportId);
				return;
			}
	    	//============================================
			
			//step 1
			try {
				summary(message, clarity360ReportEntity, currentStage);
			} catch (TaskException e) {
				clarity360ReportEntityDAO.updateStage2Detail(reportId, ownDomainId, null, null, Clarity360ReportEntity.CURRENT_STAGE_1, Clarity360ReportEntity.PROCESS_STATUS_NEED_PROCESS);
				throw e;
			}
	    	
	        //step 3
	        loadDataIntoFinalTable(message, clarity360ReportEntity, currentStage);
	        
	        
	    	List<Clarity360Entity> sourceResult = clarity360Lweb05DAO.getDatasourceResultCountByReportId(reportId, ownDomainId);
	    	for(Clarity360Entity clarity360Entity : sourceResult) {
	    		try {
	    			//updateTargetCount(Integer reportId, Integer dateSource, Integer status, Integer count)
	    			clarity360ReportStageEntityDAO.updateTargetCount(reportId, getSourceIdByName(clarity360Entity.getSource()), clarity360Entity.getCnt());
	    		} catch (Exception e) {
	    			e.printStackTrace();
	    		}
	    	}
	            
	        clarity360ReportEntityDAO.updateStatusAndEndDate(Clarity360ReportEntity.PROCESS_STATUS_DONE, clarity360ReportEntity.getId());
			
		} catch (TaskException e) {
			throw e;
		} catch (Exception e) {
			e.printStackTrace();
			clarity360ReportEntityDAO.updateStatusAndEndDate(Clarity360ReportEntity.PROCESS_STATUS_ERROR, clarity360ReportEntity.getId());
		}
		
    }
    
    public void processC360ErrorPage(Integer ownDomainId, Integer crawlRequestLogId) throws TaskException {
    	
    	CrawlRequestLog crawlRequestLog = crawlRequestLogDAO.getById(crawlRequestLogId);
    	
    	Integer count = disSiteCrawlDoc1For360SummaryOnlyDao.getErrorPageCount(ownDomainId, crawlRequestLogId);
    	System.out.println(" getErrorPageCount count :" + count);
    	if (count != null && count != 0) {
    		System.out.println("== Error Pages Data existed!");
		} else {
			try {
    			crawlRequestLogDAO.updateErrorPageSummaryStatus(crawlRequestLogId, CrawlRequestLog.ERROR_PAGE_SUMMARY_STATUS_PROCESSING);
    			System.out.println("==processC360ErrorPage ownDomainId:" + ownDomainId + ", crawlRequestLogId:" + crawlRequestLogId);
    			disSiteCrawlDoc1For360SummaryOnlyDao.insertErrorPage(ownDomainId, crawlRequestLogId);
    		} catch (Exception e) {
    			// TODO: handle exception
    			e.printStackTrace();
    			crawlRequestLogDAO.updateUploadStatus(crawlRequestLogId, CrawlRequestLog.ERROR_PAGE_SUMMARY_STATUS_FAILED);
    			String subject = "Insert clarity 360 ErrorPage failed";
    			sendEmail(ownDomainId, crawlRequestLogId, null, e.getMessage(), subject);
    			throw new TaskException(subject); 
    		}
    		
    		crawlRequestLogDAO.updateErrorPageSummaryStatus(crawlRequestLogId, CrawlRequestLog.ERROR_PAGE_SUMMARY_STATUS_SUCCESS);
		}
		
    }
    
    private void sendEmail(Integer ownDomainId, Integer crawlRequestLogId, BigInteger crawlRequestProjectId, String message, String subject) {
        String title = subject + ", please check s20: /home/<USER>/source/checkSiteHealthUploadStatus/clarity-backend-scripts/";
        try {
            Map<String, Object> reportMap = new HashMap<>();
            reportMap.put("userName", "Ewain");
            reportMap.put("dateString", org.apache.commons.lang.time.DateFormatUtils.format(new Date(), "MM/dd/yyyy"));
            reportMap.put("title", title);

            if (org.apache.commons.lang.StringUtils.isEmpty(message)) {
                reportMap.put("errormessage", "");
            } else {
                reportMap.put("errormessage", message + "=== OID: " + ownDomainId + ", crawlRequestLogId: " + crawlRequestLogId);
            }
             String emailTo = "<EMAIL>";
			zeptoMailSenderComponent.sendMimeMultiPartZeptoMailAndBccByFunctionType(emailTo, null, subject, "mail_common.txt", "mail_common.html", reportMap, null,
					ZeptoMailSenderComponent.FUNCTION_TYPE_BACKEND_INTERNAL, null, null);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    private void loadDataIntoFinalTable(String message, Clarity360ReportEntity clarity360ReportEntity, Integer currentStage) {
    	
    	JSONObject jsonObject = new JSONObject(message);
    	String primaryDatasourceStr = "";
    	try {
    		primaryDatasourceStr = getSourceByName(jsonObject.getString("primaryDatasource"));

    		System.out.println("primaryDatasource:" + primaryDatasourceStr);
		} catch (Exception e) {
			// TODO: handle exception
		}
    	
    	
    	Integer totalCount = clarity360Merge01DAO.getTotalCountByReportId(reportId, ownDomainId);
    	
    	System.out.println("totalCount: " + totalCount);
		
    	int partitionNum = 0;
		if (totalCount >= DEFAILT_LOAD_PAGESIZE) {
			partitionNum = (totalCount / DEFAILT_LOAD_PAGESIZE) + 1;
		} else {
			partitionNum = 1;
		} 
		
        try { 
    		for(int pageNum = 0; pageNum < partitionNum; pageNum ++) {
    			try { 
    				System.out.println("Summary :" + (pageNum + 1) + " of " + partitionNum);
    				
    				clarity360Merge01DAO.loadDatIntoFinalTableV2(ownDomainId, reportId, partitionNum, pageNum, false, primaryDatasourceStr, currentStage);
    				try {
    					System.out.println("Sleep 1 sec...");
    					Thread.sleep(1 * 1000);
    				} catch (Exception e) {
						e.printStackTrace();
					}
    			} catch (Exception exception) {
    	            exception.printStackTrace();
    	        }
    			
    		}
        } catch (Exception e) {
        	exceptionMap.put(Clarity360ReportStageEntity.STEP_LOAD_INTO_FINAL, e.getLocalizedMessage());
            e.printStackTrace();
        }
        
        
        if (StringUtils.equals(clarity360ReportEntity.getClarity360SummaryHashType(), Clarity360ReportEntity.TYPE_BOTH) 
        		|| StringUtils.equals(clarity360ReportEntity.getClarity360SummaryHashType(), Clarity360ReportEntity.TYPE_NORM)) {
        	 try { 
         		for(int pageNum = 0; pageNum < partitionNum; pageNum ++) {
         			try { 
         				System.out.println("Summary :" + (pageNum + 1) + " of " + partitionNum);
         				
         				clarity360Merge01DAO.loadDatIntoFinalTableV2(ownDomainId, reportId, partitionNum, pageNum, true, primaryDatasourceStr, currentStage);
         				try {
         					System.out.println("Sleep 1 sec...");
         					Thread.sleep(1 * 1000);
         				} catch (Exception e) {
     						e.printStackTrace();
     					}
         			} catch (Exception exception) {
         	            exception.printStackTrace();
         	        }
         			
         		}
             } catch (Exception e) {
             	exceptionMap.put(Clarity360ReportStageEntity.STEP_LOAD_INTO_FINAL_NORMALIZATION, e.getLocalizedMessage());
                 e.printStackTrace();
             }
        }
       
        
	}

	private void summary(String message, Clarity360ReportEntity clarity360ReportEntity, Integer currentStage) throws TaskException {
    	
    	OwnDomainSettingEntity ownDomainSettingEntity = ownDomainSettingEntityDAO.getSettingByDomainId(ownDomainId);
    	OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getById(ownDomainId);
    	
    	if ((currentStage == null || currentStage == STAGE_2) && !isInternalLinkFinished(message, clarity360ReportEntity)) {
//    		clarity360ReportEntityDAO.updateStatusAndEndDate(Clarity360ReportEntity.PROCESS_STATUS_NEED_PROCESS_INTERNAL_LINK_NOT_FINISHED, crawlRequestId);
    		System.out.println("waiting for Internal link finish report(" + reportId + ") to waiting list!");
			throw new TaskException("Internal Link not ready!");
		}
    	
    	if (currentStage != null) {
    		System.out.println("==== processing currentStage:" + currentStage);
    	}
    	
    	JSONObject jsonObject = new JSONObject(message);
    	JSONArray source = jsonObject.getJSONArray("sources");
    	
        for(Object sourceJson : source) {
        	Map sourceMap = gson.fromJson(String.valueOf(sourceJson), Map.class);
        	String funcName = gson.toJson(sourceMap.keySet().toArray()[0]);
        	
        	funcName =  StringUtils.removeEnd(StringUtils.removeStart(funcName, "\""), "\"");
        	
        	
        	if (currentStage != null) {
        		Integer stage = FUNCTION_STAGE_MAP.get(funcName);
            	if (stage == null) {
    				System.out.println(" funcName not found in stage map! " + funcName);
    				continue;
    			}
            	
            	if (stage != currentStage) {
            		System.out.println("=== skip funcName:" + funcName + ", stage:" + stage);
    				continue;
    			}
			}
        	Integer totalCount = 0;
        	if (StringUtils.equals(funcName, "siteHealth")) {
        		
        		System.out.println(" === processing Site Health ");
        		insertNewStage(reportId, Clarity360ReportStageEntity.STATUS_PROCESSING, Clarity360ReportStageEntity.STEP_SITEHEALTH);
        		
        		try {
        			System.out.println("===== working on Error Page ");
        			processC360ErrorPage(clarity360ReportEntity.getOwnDomainId(), clarity360ReportEntity.getSiteAuditCrawlId());
        			System.out.println("===== end Error Page ");
        	    	
        			SiteHealthVO siteHealthVO = gson.fromJson(gson.toJson(sourceMap.get(funcName)), SiteHealthVO.class);
        			
        			totalCount = disSiteCrawlDoc1For360SummaryOnlyDao.getDocTotalCount(ownDomainId, siteHealthVO, 
        					siteHealthVO.getUrlTagId(), siteHealthVO.getContentTypeFilterVO());
        			
        			
        			pageSize = SITE_HEALTH_SUMMARY_DEFAILT_PAGESIZE;
        			if (ownDomainId == EXPEDIA_DOMAIN_ID) {
        				pageSize = EXPEDIA_SITE_HEALTH_SUMMARY_PAGESIZE;
        			}
        			
        			summaryByDataSource(siteHealthVO, totalCount, pageSize, funcName, Clarity360ReportStageEntity.STEP_SITEHEALTH, clarity360ReportEntity);
        			Thread.sleep(10 * 1000);
        			clarity360ReportStageEntityDAO.updateStatus(reportId, Clarity360ReportStageEntity.STEP_SITEHEALTH, Clarity360ReportStageEntity.STATUS_COMPLETE, "");
				} catch (Exception e) {
					e.printStackTrace();
					clarity360ReportStageEntityDAO.updateStatus(reportId, Clarity360ReportStageEntity.STEP_SITEHEALTH, 
							Clarity360ReportStageEntity.STATUS_ERROR, StringUtils.substring(e.getMessage(), 0, 400));
				}
        		
			} else if (StringUtils.equals(funcName, "searchAnalytics")) {
				System.out.println(" === processing Gsc ");
				insertNewStage(reportId, Clarity360ReportStageEntity.STATUS_PROCESSING, Clarity360ReportStageEntity.STEP_GSC);

				try {
					GscVO gscVO = gson.fromJson(gson.toJson(sourceMap.get(funcName)), GscVO.class);
					
			    	
			    	if(gscVO.getBigQuery() != null && gscVO.getBigQuery()) {
			    		totalCount = gscBaseDao.getBigQueryTotalCount(ownDomainId, gscVO, gscVO.getUrlTagId(), gscVO.getContentTypeFilterVO());
					} else {
						totalCount = gscBaseDao.getTotalCount(ownDomainId, gscVO, gscVO.getUrlTagId(), gscVO.getContentTypeFilterVO());
					}
					
					summaryByDataSource(gscVO, totalCount, pageSize, funcName, Clarity360ReportStageEntity.STEP_GSC, clarity360ReportEntity);
					clarity360ReportStageEntityDAO.updateStatus(reportId, Clarity360ReportStageEntity.STEP_GSC, Clarity360ReportStageEntity.STATUS_COMPLETE, "");
				} catch (Exception e) {
					exceptionMap.put(Clarity360ReportStageEntity.STEP_GSC, e.getLocalizedMessage());
					e.printStackTrace();
					clarity360ReportStageEntityDAO.updateStatus(reportId, Clarity360ReportStageEntity.STEP_GSC, 
							Clarity360ReportStageEntity.STATUS_ERROR, StringUtils.substring(e.getMessage(), 0, 400));
				}
			} else if (StringUtils.equals(funcName, "siteAnalytics")) {
				System.out.println(" === processing Ga ");
        		insertNewStage(reportId, Clarity360ReportStageEntity.STATUS_PROCESSING, Clarity360ReportStageEntity.STEP_GA);
        		
        		try {
        			GaVO gaVO = gson.fromJson(gson.toJson(sourceMap.get(funcName)), GaVO.class);
        			
        			gaVO.setTrafficType(ownDomainSettingEntity.getTrafficType());
        			gaVO.setOwnDomainProtocol(ownDomainEntity.getProtocol());
        			
        			totalCount = ga001ClarityDBEntityDAO.getTotalCount(gaVO.getTrafficType(), ownDomainId, 
	            			gaVO, targetDate, reportId, GA_GA4_DOMAIN_MAP, GA_GA4_BQ_DOMAIN_MAP, gaVO.getUrlTagId(), gaVO.getContentTypeFilterVO(), 
	            			gaVO.getOwnDomainProtocol());
        			
        			summaryByDataSource(gaVO, totalCount, pageSize, funcName, Clarity360ReportStageEntity.STEP_GA, clarity360ReportEntity);
					clarity360ReportStageEntityDAO.updateStatus(reportId, Clarity360ReportStageEntity.STEP_GA, Clarity360ReportStageEntity.STATUS_COMPLETE, "");
        		} catch (Exception e) {
        			e.printStackTrace();
					clarity360ReportStageEntityDAO.updateStatus(reportId, Clarity360ReportStageEntity.STEP_GA, 
							Clarity360ReportStageEntity.STATUS_ERROR, StringUtils.substring(e.getMessage(), 0, 400));
				}
    		} else if (StringUtils.equals(funcName, "bot")) {
				System.out.println(" === processing Bot ");
        		insertNewStage(reportId, Clarity360ReportStageEntity.STATUS_PROCESSING, Clarity360ReportStageEntity.STEP_BOT);
        		
        		try {
					BotVO botVO = gson.fromJson(gson.toJson(sourceMap.get(funcName)), BotVO.class);
					totalCount = botDetailDao.getTotalCount(ownDomainId, botVO, 
	            			botVO.getUrlTagId(), botVO.getContentTypeFilterVO());
        			
        			summaryByDataSource(botVO, totalCount, pageSize, funcName, Clarity360ReportStageEntity.STEP_BOT, clarity360ReportEntity);
					
					clarity360ReportStageEntityDAO.updateStatus(reportId, Clarity360ReportStageEntity.STEP_BOT, Clarity360ReportStageEntity.STATUS_COMPLETE, "");
        		} catch (Exception e) {
        			e.printStackTrace();
					clarity360ReportStageEntityDAO.updateStatus(reportId, Clarity360ReportStageEntity.STEP_BOT, 
							Clarity360ReportStageEntity.STATUS_ERROR, StringUtils.substring(e.getMessage(), 0, 400));
				}
    		} else if (StringUtils.equals(funcName, "siteMap")) {
				System.out.println(" === processing Site Map ");
        		insertNewStage(reportId, Clarity360ReportStageEntity.STATUS_PROCESSING, Clarity360ReportStageEntity.STEP_SITEMAP);
        		
				try {
					SiteMapVO siteMapVO = gson.fromJson(gson.toJson(sourceMap.get(funcName)), SiteMapVO.class);
					
					totalCount = siteMapDetailDao.getTotalCount(ownDomainId, siteMapVO, 
							siteMapVO.getUrlTagId(), siteMapVO.getContentTypeFilterVO(), "murmurHash3_64(url)");
        			
        			summaryByDataSource(siteMapVO, totalCount, pageSize, funcName, Clarity360ReportStageEntity.STEP_SITEMAP, clarity360ReportEntity);
					
					clarity360ReportStageEntityDAO.updateStatus(reportId, Clarity360ReportStageEntity.STEP_SITEMAP, Clarity360ReportStageEntity.STATUS_COMPLETE, "");
				} catch (Exception e) {
					e.printStackTrace();
					clarity360ReportStageEntityDAO.updateStatus(reportId, Clarity360ReportStageEntity.STEP_SITEMAP, 
							Clarity360ReportStageEntity.STATUS_ERROR, StringUtils.substring(e.getMessage(), 0, 400));
				}
    		
    		} else if (StringUtils.equals(funcName, "ranking")) {
				System.out.println(" === processing Rank ");
        		insertNewStage(reportId, Clarity360ReportStageEntity.STATUS_PROCESSING, Clarity360ReportStageEntity.STEP_RI);
        		
        		
        		try {
        			RiVO riVO = gson.fromJson(gson.toJson(sourceMap.get(funcName)), RiVO.class);
        			riVO.setBroadMatch(ownDomainEntity.isBroadMatch());
    				
        			totalCount = clDailyRankingEntityDao.getTotalCount(ownDomainId, riVO, ownDomainEntity, targetDate, 
	            			reportId, ALTERNATE_DOMAIN_MAP, riVO.getUrlTagId(), riVO.getContentTypeFilterVO());
        			
        			summaryByDataSource(riVO, totalCount, pageSize, funcName, Clarity360ReportStageEntity.STEP_RI, clarity360ReportEntity);
        			
    				clarity360ReportStageEntityDAO.updateStatus(reportId, Clarity360ReportStageEntity.STEP_RI, Clarity360ReportStageEntity.STATUS_COMPLETE, "");
        		} catch (Exception e) {
        			e.printStackTrace();
					clarity360ReportStageEntityDAO.updateStatus(reportId, Clarity360ReportStageEntity.STEP_RI, 
							Clarity360ReportStageEntity.STATUS_ERROR, StringUtils.substring(e.getMessage(), 0, 400));
				}
				
			} else if (StringUtils.equals(funcName, "researchGrid")) {
				System.out.println(" === processing RG ");
        		insertNewStage(reportId, Clarity360ReportStageEntity.STATUS_PROCESSING, Clarity360ReportStageEntity.STEP_RG);
        		
        		try {
        			RgVO rgVO = gson.fromJson(gson.toJson(sourceMap.get(funcName)), RgVO.class);
        			
        			totalCount = lwebMonthlyRankingDao.getTotalCount(ownDomainId, rgVO, ownDomainEntity, targetDate, 
	            			reportId, rgVO.getUrlTagId(), rgVO.getContentTypeFilterVO());
        			
        			summaryByDataSource(rgVO, totalCount, pageSize, funcName, Clarity360ReportStageEntity.STEP_RG, clarity360ReportEntity);
        			
    				clarity360ReportStageEntityDAO.updateStatus(reportId, Clarity360ReportStageEntity.STEP_RG, Clarity360ReportStageEntity.STATUS_COMPLETE, "");
        		} catch (Exception e) {
        			e.printStackTrace();
					clarity360ReportStageEntityDAO.updateStatus(reportId, Clarity360ReportStageEntity.STEP_RG, 
							Clarity360ReportStageEntity.STATUS_ERROR, StringUtils.substring(e.getMessage(), 0, 400));
				}
				
			} else if (StringUtils.equals(funcName, "internalLink")) {
				System.out.println(" === processing internalLink ");
        		insertNewStage(reportId, Clarity360ReportStageEntity.STATUS_PROCESSING, Clarity360ReportStageEntity.STEP_INTERNAL_LINK);
        		
        		try {
        			InternalLinkVO internalLinkVO = gson.fromJson(gson.toJson(sourceMap.get(funcName)), InternalLinkVO.class);
        			
        			totalCount = clarity360Merge01DAO.getTotalCount(ownDomainId, internalLinkVO, 
		        			targetDate, reportId, internalLinkVO.getUrlTagId(), internalLinkVO.getContentTypeFilterVO());
        			
        			summaryByDataSource(internalLinkVO, totalCount, pageSize, funcName, Clarity360ReportStageEntity.STEP_INTERNAL_LINK, clarity360ReportEntity);
    				clarity360ReportStageEntityDAO.updateStatus(reportId, Clarity360ReportStageEntity.STEP_INTERNAL_LINK, Clarity360ReportStageEntity.STATUS_COMPLETE, "");
        		} catch (Exception e) {
        			e.printStackTrace();
					clarity360ReportStageEntityDAO.updateStatus(reportId, Clarity360ReportStageEntity.STEP_INTERNAL_LINK, 
							Clarity360ReportStageEntity.STATUS_ERROR, StringUtils.substring(e.getMessage(), 0, 400));
				}
				
			} else if (StringUtils.equals(funcName, "backlink")) {
				System.out.println(" === processing backlink ");
        		insertNewStage(reportId, Clarity360ReportStageEntity.STATUS_PROCESSING, Clarity360ReportStageEntity.STEP_BACKLINK);
        		
        		try {
        			BacklinkVO backlinkVO = gson.fromJson(gson.toJson(sourceMap.get(funcName)), BacklinkVO.class);
        			
        			totalCount = clarity360Merge01DAO.getTotalCount(ownDomainId, backlinkVO, ownDomainEntity,
    						targetDate, reportId, backlinkVO.getUrlTagId(), backlinkVO.getContentTypeFilterVO());
        			
        			summaryByDataSource(backlinkVO, totalCount, pageSize, funcName, Clarity360ReportStageEntity.STEP_BACKLINK, clarity360ReportEntity);
        			
    				clarity360ReportStageEntityDAO.updateStatus(reportId, Clarity360ReportStageEntity.STEP_BACKLINK, Clarity360ReportStageEntity.STATUS_COMPLETE, "");
        		} catch (Exception e) {
        			e.printStackTrace();
					clarity360ReportStageEntityDAO.updateStatus(reportId, Clarity360ReportStageEntity.STEP_BACKLINK, 
							Clarity360ReportStageEntity.STATUS_ERROR, StringUtils.substring(e.getMessage(), 0, 400));
				}
				
			} else if (StringUtils.equals(funcName, "customDataSource")) {
				System.out.println(" === processing customDataSource ");
        		insertNewStage(reportId, Clarity360ReportStageEntity.STATUS_PROCESSING, Clarity360ReportStageEntity.STEP_CUSTOM_DATA_SOURCE);
        		
        		try {
        			CustomDataSourceVO customDataSourceVO = gson.fromJson(gson.toJson(sourceMap.get(funcName)), CustomDataSourceVO.class);
        			
        			totalCount = clarity360Merge01DAO.getTotalCount(ownDomainId, customDataSourceVO, ownDomainEntity,
    						targetDate, reportId, customDataSourceVO.getUrlTagId(), customDataSourceVO.getContentTypeFilterVO());
        			
        			summaryByDataSource(customDataSourceVO, totalCount, pageSize, funcName, Clarity360ReportStageEntity.STEP_CUSTOM_DATA_SOURCE, clarity360ReportEntity);
        			
    				clarity360ReportStageEntityDAO.updateStatus(reportId, Clarity360ReportStageEntity.STEP_CUSTOM_DATA_SOURCE, Clarity360ReportStageEntity.STATUS_COMPLETE, "");
        		} catch (Exception e) {
        			e.printStackTrace();
					clarity360ReportStageEntityDAO.updateStatus(reportId, Clarity360ReportStageEntity.STEP_CUSTOM_DATA_SOURCE, 
							Clarity360ReportStageEntity.STATUS_ERROR, StringUtils.substring(e.getMessage(), 0, 400));
				}
				
			} else {
				System.out.println("Un-support func name:" + funcName);
			}
        	
        	sourceDataCountMap.put(getSourceByName(funcName), totalCount);
        	
        }
    }
    
    private Boolean isInternalLinkFinished(String message, Clarity360ReportEntity clarity360ReportEntity) {
    	
    	boolean isDataSourceHaveInternalLink = false;
    	
    	JSONObject jsonObject = new JSONObject(message);
    	JSONArray source = jsonObject.getJSONArray("sources");
        for(Object sourceJson : source) {
        	Map sourceMap = gson.fromJson(String.valueOf(sourceJson), Map.class);
        	String funcName = gson.toJson(sourceMap.keySet().toArray()[0]);
        	
        	funcName =  StringUtils.removeEnd(StringUtils.removeStart(funcName, "\""), "\"");
        	
        	if (StringUtils.equals(funcName, "internalLink")) {
        		
        		isDataSourceHaveInternalLink = true;
        		try {
        			InternalLinkVO internalLinkVO = gson.fromJson(gson.toJson(sourceMap.get(funcName)), InternalLinkVO.class);
        			
        			CrawlRequestLog crawlRequestLog = crawlRequestLogDAO.getCrawlerByOwnDomainIdAndCrawlId(ownDomainId, internalLinkVO.getCrawlRequestId());
        			
        			System.out.println("Internal link status: " + crawlRequestLog.getAdditionalStatusV2());
        			// internal link should not be generated
        			if (crawlRequestLog == null ||crawlRequestLog.getGeneratePageLinks() != 1) {
        				System.out.println("generatePageLinks is false, there wont be data in internal link!");
						return true;
					}
        			
        			if (crawlRequestLog != null && crawlRequestLog.getAdditionalStatusV2() != null &&crawlRequestLog.getAdditionalStatusV2() == CrawlRequestLog.INTERNALLINK_DATA_STATUS_COMPLETE) {
						System.out.println("crawlRequestLog.getAdditionalStatusV2：" + crawlRequestLog.getAdditionalStatusV2());
						
						if (internalLinkVO.getEnablePageRank()) {
							//check page rank status
							System.out.println("page rank enabled, verifying PR status!");
							
							InternalLinkPageRankInstanceEntity internalLinkPageRankInstanceEntity = internalLinkPageRankInstanceEntityDAO.getByCrawlRequestId(ownDomainId, internalLinkVO.getCrawlRequestId());
							
							if (internalLinkPageRankInstanceEntity == null || internalLinkPageRankInstanceEntity.getStatus() == 2) {
								System.out.println("=== PR finished!");
								return true;
							} else {
								System.out.println("=== PR not finished! crawlId : " + internalLinkVO.getCrawlRequestId());
								return false;
							}
							
						}
        				return true;
					} else {
						System.out.println("Internal link summary not finished!");
						return false;
					}
        			
        		} catch (Exception e) {
        			e.printStackTrace();
        			System.out.println("Param parse failed.");
				}
        	}
        }
        
        if (!isDataSourceHaveInternalLink) {
        	return true;
		}
        return false;
    }
    
    private void summaryByDataSource(Clarity360BaseVO clarity360BaseVO, Integer totalCount, Integer pageSize, String funcName, Integer step, Clarity360ReportEntity clarity360ReportEntity) {
		
		System.out.println("Type: " + step + ", Total Count :" + totalCount + ", pageSize:" + pageSize);
		int partitionNum = 0;
		if (totalCount >= pageSize) {
			partitionNum = (totalCount / pageSize) + 1;
		} else {
			partitionNum = 1;
		}
		
        try { 
        	System.out.println("====== processing " + funcName + " for urlmurmurhash");
    		for(int i = 0; i < partitionNum; i ++) {
    			try { 
    				System.out.println("Summary :" + (i + 1) + " of " + partitionNum);
    				
    				summaryMurmurHashByFunc(clarity360BaseVO, funcName, partitionNum, i);
    				try {
    					System.out.println("Sleep 1 sec...");
    					Thread.sleep(1 * 1000);
    				} catch (Exception e) {
						e.printStackTrace();
					}
    			} catch (Exception exception) {
    	            exception.printStackTrace();
    	        }
    			
    		}
    		
    		if (StringUtils.equals(funcName, "siteHealth")) {
            	SiteHealthVO siteHealthVO = (SiteHealthVO) clarity360BaseVO;
            	disSiteCrawlDoc1For360SummaryOnlyDao.siteHealthClarity360SummaryV2Part2(ownDomainId, siteHealthVO, 
            			targetDate, reportId, siteHealthVO.getUrlTagId(), siteHealthVO.getContentTypeFilterVO(), "url_murmur_hash", false, 1);
            	
            }
        } catch (Exception e) {
        	exceptionMap.put(step, e.getLocalizedMessage());
            e.printStackTrace();
        }
        
        if (StringUtils.equals(clarity360ReportEntity.getClarity360SummaryHashType(), Clarity360ReportEntity.TYPE_BOTH) 
        		|| StringUtils.equals(clarity360ReportEntity.getClarity360SummaryHashType(), Clarity360ReportEntity.TYPE_NORM)) {
			
        	try { 
            	System.out.println("====== processing " + funcName + " for urlhash");
        		for(int i = 0; i < partitionNum; i ++) {
        			try { 
        				System.out.println("Summary :" + (i + 1) + " of " + partitionNum);
        				
        				summaryUrlHashByFunc(clarity360BaseVO, funcName, partitionNum, i);
        				try {
        					System.out.println("Sleep 1 sec...");
        					Thread.sleep(1 * 1000);
        				} catch (Exception e) {
    						e.printStackTrace();
    					}
        			} catch (Exception exception) {
        	            exception.printStackTrace();
        	        }
        			
        		}
        		
        		if (StringUtils.equals(funcName, "siteHealth")) {
                	SiteHealthVO siteHealthVO = (SiteHealthVO) clarity360BaseVO;
                	disSiteCrawlDoc1For360SummaryOnlyDao.siteHealthClarity360SummaryV2Part2(ownDomainId, siteHealthVO, 
                			targetDate, reportId, siteHealthVO.getUrlTagId(), siteHealthVO.getContentTypeFilterVO(), "URLHash(lower(url))", true, 1);
                }
        		
            } catch (Exception e) {
            	exceptionMap.put(step, e.getLocalizedMessage());
                e.printStackTrace();
            }
		}
    }
    
    
    private String getSourceByName(String dateSource) {
    	
    	if (StringUtils.equals(dateSource, "siteHealth")) {
			return "sitehealth";
		} else if (StringUtils.equals(dateSource, "bot")) {
			return "bot";
		} else if (StringUtils.equals(dateSource, "ranking")) {
			return "ri";
		} else if (StringUtils.equals(dateSource, "siteAnalytics")) {
			return "traffic";
		} else if (StringUtils.equals(dateSource, "searchAnalytics")) {
			return "gsc";
		} else if (StringUtils.equals(dateSource, "backlink")) {
			return "backlink";
		} else if (StringUtils.equals(dateSource, "researchGrid")) {
			return "rg";
		} else if (StringUtils.equals(dateSource, "siteMap")) {
			return "sitemap";
		} else if (StringUtils.equals(dateSource, "internalLink")) {
			return "internallink";
		} else if (StringUtils.equals(dateSource, "customDataSource")) {
			return "customDataSource";
		}
    	return "sitehealth";
    }
    
    /**
     * 	public static final Integer STEP_SITEHEALTH = 1;
		public static final Integer STEP_GSC = 2;
		public static final Integer STEP_GA = 3;
		public static final Integer STEP_BOT = 4;
		public static final Integer STEP_SITEMAP = 5;
		public static final Integer STEP_RI = 6;
		public static final Integer STEP_RG = 7;
		public static final Integer STEP_INTERNAL_LINK = 8;
		public static final Integer STEP_BACKLINK = 9;
		public static final Integer STEP_CUSTOM_DATA_SOURCE = 10;
     * @param dateSource
     * @return
     */
    
    public static Integer getSourceIdByName(String dateSource) {
    	
    	if (StringUtils.equals(dateSource, "sitehealth")) {
			return Clarity360ReportStageEntity.STEP_SITEHEALTH;
		} else if (StringUtils.equals(dateSource, "bot")) {
			return Clarity360ReportStageEntity.STEP_BOT;
		} else if (StringUtils.equals(dateSource, "ri")) {
			return Clarity360ReportStageEntity.STEP_RI;
		} else if (StringUtils.equals(dateSource, "traffic")) {
			return Clarity360ReportStageEntity.STEP_GA;
		} else if (StringUtils.equals(dateSource, "gsc")) {
			return Clarity360ReportStageEntity.STEP_GSC;
		} else if (StringUtils.equals(dateSource, "backlink")) {
			return Clarity360ReportStageEntity.STEP_BACKLINK;
		} else if (StringUtils.equals(dateSource, "rg")) {
			return Clarity360ReportStageEntity.STEP_RG;
		} else if (StringUtils.equals(dateSource, "sitemap")) {
			return Clarity360ReportStageEntity.STEP_SITEMAP;
		} else if (StringUtils.equals(dateSource, "internallink")) {
			return Clarity360ReportStageEntity.STEP_INTERNAL_LINK;
		} else if (StringUtils.equals(dateSource, "customDataSource")) {
			return Clarity360ReportStageEntity.STEP_CUSTOM_DATA_SOURCE;
		}
    	return null;
    }
    
    
    private void summaryMurmurHashByFunc(Clarity360BaseVO clarity360BaseVO, String funcName, Integer partitionNum, Integer pageNum) throws Exception {
    	OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getById(ownDomainId);
		
    	int num = 0;
		while (true) {
			
			try {
				if(num > 0) {
					System.out.println("!! Retry " + num + " times");
				}
				
				if (StringUtils.equals(funcName, "siteHealth")) {
		    		System.out.println(" === processing Site Health ");
		    		SiteHealthVO siteHealthVO = (SiteHealthVO) clarity360BaseVO;
		    		
		    		List<Integer> statusCodeList = disSiteCrawlDoc1For360SummaryOnlyDao.getStatusListForErrorPage(ownDomainId, siteHealthVO);
		    		disSiteCrawlDoc1For360SummaryOnlyDao.siteHealthClarity360SummaryV2Part1(ownDomainId, siteHealthVO, 
                			targetDate, reportId, siteHealthVO.getUrlTagId(), siteHealthVO.getContentTypeFilterVO(), "url_murmur_hash", false, partitionNum, pageNum, 1);
		    		//Integer ownDomainId, SiteHealthVO siteHealthVO, Integer reportId, Boolean isForUrlHash, Integer clusterNum
		    		System.out.println("CNT: " + disSiteCrawlDoc1For360SummaryOnlyDao.getTotalCount(ownDomainId, siteHealthVO, reportId, false, 1));
		    		try {
		    			System.out.println("====== processing status attr arrays");
			    		disSiteCrawlDoc1For360SummaryOnlyDao.siteHealthClarity360SummaryV2Part3(ownDomainId, siteHealthVO, 
		            			targetDate, reportId, siteHealthVO.getUrlTagId(), siteHealthVO.getContentTypeFilterVO(), "url_murmur_hash", false, partitionNum, pageNum, 1, statusCodeList);
			    		System.out.println("CNT: " + disSiteCrawlDoc1For360SummaryOnlyDao.getTotalCount(ownDomainId, siteHealthVO, reportId, false, 1));
		    		} catch (Exception e) {
						e.printStackTrace();
					}
		    		
				} else if (StringUtils.equals(funcName, "searchAnalytics")) {
					System.out.println(" === processing Gsc ");
					GscVO gscVO = (GscVO) clarity360BaseVO;
					if(gscVO.getBigQuery() != null && gscVO.getBigQuery()) {
						gscBaseDao.gscBigQuery360SummaryByPage(ownDomainId, gscVO, targetDate, reportId, 
								gscVO.getUrlTagId(), gscVO.getContentTypeFilterVO(), false, "url_murmur3_hash", partitionNum, pageNum, 1);
					} else {
						gscBaseDao.gscClarity360SummaryMergedByPage(ownDomainId, gscVO, targetDate, reportId, 
			    				gscVO.getUrlTagId(), gscVO.getContentTypeFilterVO(), partitionNum, pageNum, 1);
					}
					
				} else if (StringUtils.equals(funcName, "siteAnalytics")) {
					System.out.println(" === processing Ga ");
					GaVO gaVO = (GaVO) clarity360BaseVO;
					
					ga001ClarityDBEntityDAO.gaClarity360Summary(gaVO.getTrafficType(), ownDomainId, 
	            			gaVO, targetDate, reportId, GA_GA4_DOMAIN_MAP, GA_GA4_BQ_DOMAIN_MAP, gaVO.getUrlTagId(), gaVO.getContentTypeFilterVO(), 
	            			gaVO.getOwnDomainProtocol(), "url_murmur_hash", false, partitionNum, pageNum, 1);
				} else if (StringUtils.equals(funcName, "bot")) {
					System.out.println(" === processing Bot ");
					BotVO botVO = (BotVO)clarity360BaseVO;
		    		
					botDetailDao.botClarity360Summary(ownDomainId, botVO, targetDate, 
	            			reportId, botVO.getUrlTagId(), botVO.getContentTypeFilterVO(), "murmurHash3_64(dis_bot_detail.url)", false, partitionNum, pageNum, 1);
	            	
				} else if (StringUtils.equals(funcName, "siteMap")) {
					System.out.println(" === processing Site Map ");
					SiteMapVO siteMapVO = (SiteMapVO)clarity360BaseVO;
		    		
					siteMapDetailDao.siteMapClarity360SummaryV2(ownDomainId, siteMapVO, 
		        			targetDate, reportId, siteMapVO.getUrlTagId(), siteMapVO.getContentTypeFilterVO(), "murmurHash3_64(url)", false, partitionNum, pageNum, 1);
				} else if (StringUtils.equals(funcName, "ranking")) {
					System.out.println(" === processing Rank ");
					RiVO riVO = (RiVO)clarity360BaseVO;
		    		
					clDailyRankingEntityDao.riClarity360Summary(ownDomainId, riVO, ownDomainEntity, targetDate, 
	            			reportId, ALTERNATE_DOMAIN_MAP, riVO.getUrlTagId(), riVO.getContentTypeFilterVO(), "murmurHash3_64(url)", false, partitionNum, pageNum, 1);
	            	
				} else if (StringUtils.equals(funcName, "researchGrid")) {
					System.out.println(" === processing RG ");
					RgVO rgVO = (RgVO)clarity360BaseVO;
					
					lwebMonthlyRankingDao.processRgSummary(ownDomainId, rgVO, ownDomainEntity, targetDate, 
	            			reportId, rgVO.getUrlTagId(), rgVO.getContentTypeFilterVO(), "murmurHash3_64(url)", false, partitionNum, pageNum, 1);
				} else if (StringUtils.equals(funcName, "internalLink")) {
					System.out.println(" === processing internalLink ");
					InternalLinkVO internalLinkVO = (InternalLinkVO)clarity360BaseVO;
					
					Integer totalUniqUrlCount = clarity360Merge01DAO.getTotalUniqeUrlCount(ownDomainId, internalLinkVO, 
		        			targetDate, reportId, internalLinkVO.getUrlTagId(), internalLinkVO.getContentTypeFilterVO(), "destinationUrlMurmurHash");
					
					clarity360Merge01DAO.processInternalLinkSummary(ownDomainId, internalLinkVO, 
		        			targetDate, reportId, internalLinkVO.getUrlTagId(), internalLinkVO.getContentTypeFilterVO(), "destinationUrlMurmurHash", false, partitionNum, pageNum, totalUniqUrlCount);
					
					// process page rank
					if(internalLinkVO.getEnablePageRank()) {
						
						clarity360Merge01DAO.processPageRank(ownDomainId, internalLinkVO, 
								targetDate, reportId, internalLinkVO.getUrlTagId(), internalLinkVO.getContentTypeFilterVO(), "url_murmur_hash", false, partitionNum, pageNum);
					}
					
					
				} else if (StringUtils.equals(funcName, "backlink")) {
					System.out.println(" === processing backlink ");
					BacklinkVO backlinkVO = (BacklinkVO)clarity360BaseVO;
					
					clarity360Merge01DAO.backLink360Summary(ownDomainId, backlinkVO, ownDomainEntity,
							targetDate, reportId, backlinkVO.getUrlTagId(), backlinkVO.getContentTypeFilterVO(), "murmurHash3_64(target_url)", false, partitionNum, pageNum);
					
				} else if (StringUtils.equals(funcName, "customDataSource")) {
					System.out.println(" === processing customDataSource ");
					CustomDataSourceVO customDataSourceVO = (CustomDataSourceVO)clarity360BaseVO;
					
					clarity360Merge01DAO.customDataSource360Summary(ownDomainId, customDataSourceVO, ownDomainEntity,
							targetDate, reportId, customDataSourceVO.getUrlTagId(), customDataSourceVO.getContentTypeFilterVO(), "url_murmur_hash", false, partitionNum, pageNum);
				} else {
					System.out.println("funcName not support! funcName : " + funcName); 
				}
				break;
			} catch (Exception e) {
				e.printStackTrace();
				try {
					System.out.println("Sleep 10 sec...");
					Thread.sleep(10 * 1000);
				} catch (Exception e2) {
					e2.printStackTrace();
				}
				
				if (num >= MAX_RETRY_NUMBER) {
					throw e;
				}
			}
			
			num++;
		}
    	
    }
    
    
    private void summaryUrlHashByFunc(Clarity360BaseVO clarity360BaseVO, String funcName, Integer partitionNum, Integer pageNum) throws Exception {
    	OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getById(ownDomainId);
		
    	int num = 0;
		while (true) {
			
			try {
				if(num > 0) {
					System.out.println("!! Retry " + num + " times");
				}
				
				if (StringUtils.equals(funcName, "siteHealth")) {
		    		System.out.println(" === processing Site Health ");

		    		SiteHealthVO siteHealthVO = (SiteHealthVO) clarity360BaseVO;
		    		List<Integer> statusCodeList = disSiteCrawlDoc1For360SummaryOnlyDao.getStatusListForErrorPage(ownDomainId, siteHealthVO);
		    		
		    		disSiteCrawlDoc1For360SummaryOnlyDao.siteHealthClarity360SummaryV2Part1(ownDomainId, siteHealthVO, 
                			targetDate, reportId, siteHealthVO.getUrlTagId(), siteHealthVO.getContentTypeFilterVO(), "URLHash(lower(url))", true, partitionNum, pageNum, 1);
		    		
		    		try {
		    			System.out.println("====== processing status attr arrays");
			    		disSiteCrawlDoc1For360SummaryOnlyDao.siteHealthClarity360SummaryV2Part3(ownDomainId, siteHealthVO, 
		            			targetDate, reportId, siteHealthVO.getUrlTagId(), siteHealthVO.getContentTypeFilterVO(), "URLHash(lower(url))", true, partitionNum, pageNum, 1, statusCodeList);
			    		
		    		} catch (Exception e) {
						e.printStackTrace();
					}
		    		
				} else if (StringUtils.equals(funcName, "searchAnalytics")) {
					System.out.println(" === processing Gsc ");
					GscVO gscVO = (GscVO) clarity360BaseVO;
					if(gscVO.getBigQuery() != null && gscVO.getBigQuery()) {
						gscBaseDao.gscBigQuery360SummaryByPage(ownDomainId, gscVO, targetDate, reportId, 
								gscVO.getUrlTagId(), gscVO.getContentTypeFilterVO(), true, "url_hash", partitionNum, pageNum, 1);
					} else {
						gscBaseDao.gscClarity360SummaryMergedForUrlHashByPage(ownDomainId, gscVO, targetDate, reportId, 
			    				gscVO.getUrlTagId(), gscVO.getContentTypeFilterVO(), partitionNum, pageNum, 1);
					}
					
				} else if (StringUtils.equals(funcName, "siteAnalytics")) {
					System.out.println(" === processing Ga ");
					GaVO gaVO = (GaVO) clarity360BaseVO;
					
					ga001ClarityDBEntityDAO.gaClarity360Summary(gaVO.getTrafficType(), ownDomainId, 
	            			gaVO, targetDate, reportId, GA_GA4_DOMAIN_MAP, GA_GA4_BQ_DOMAIN_MAP, gaVO.getUrlTagId(), gaVO.getContentTypeFilterVO(), 
	            			gaVO.getOwnDomainProtocol(), "url_hash", true, partitionNum, pageNum, 1);
				} else if (StringUtils.equals(funcName, "bot")) {
					System.out.println(" === processing Bot ");
		    		BotVO botVO = (BotVO)clarity360BaseVO;
		    		
					botDetailDao.botClarity360Summary(ownDomainId, botVO, targetDate, 
	            			reportId, botVO.getUrlTagId(), botVO.getContentTypeFilterVO(), "urlhash", true, partitionNum, pageNum, 1);
				} else if (StringUtils.equals(funcName, "siteMap")) {
					System.out.println(" === processing Site Map ");
					SiteMapVO siteMapVO = (SiteMapVO)clarity360BaseVO;
		    		
		        	siteMapDetailDao.siteMapClarity360SummaryV2(ownDomainId, siteMapVO, 
		        			targetDate, reportId, siteMapVO.getUrlTagId(), siteMapVO.getContentTypeFilterVO(), "URLHash(lower(url))", true, partitionNum, pageNum, 1);	
				} else if (StringUtils.equals(funcName, "ranking")) {
					System.out.println(" === processing Rank ");
					RiVO riVO = (RiVO)clarity360BaseVO;
		    		
					clDailyRankingEntityDao.riClarity360Summary(ownDomainId, riVO, ownDomainEntity, targetDate, 
	            			reportId, ALTERNATE_DOMAIN_MAP, riVO.getUrlTagId(), riVO.getContentTypeFilterVO(), "URLHash(lower(url))", true, partitionNum, pageNum, 1);
				} else if (StringUtils.equals(funcName, "researchGrid")) {
					System.out.println(" === processing RG ");
					RgVO rgVO = (RgVO)clarity360BaseVO;
					
	            	lwebMonthlyRankingDao.processRgSummary(ownDomainId, rgVO, ownDomainEntity, targetDate, 
	            			reportId, rgVO.getUrlTagId(), rgVO.getContentTypeFilterVO(), "urlhash", true, partitionNum, pageNum, 1);
				} else if (StringUtils.equals(funcName, "internalLink")) {
					System.out.println(" === processing internalLink ");
					InternalLinkVO internalLinkVO = (InternalLinkVO)clarity360BaseVO;
					
					Integer totalUniqUrlCount = clarity360Merge01DAO.getTotalUniqeUrlCount(ownDomainId, internalLinkVO, 
		        			targetDate, reportId, internalLinkVO.getUrlTagId(), internalLinkVO.getContentTypeFilterVO(), "URLHash(lower(destUrl))");
					
					clarity360Merge01DAO.processInternalLinkSummary(ownDomainId, internalLinkVO, 
		        			targetDate, reportId, internalLinkVO.getUrlTagId(), internalLinkVO.getContentTypeFilterVO(), "URLHash(lower(destUrl))", true, partitionNum, pageNum, totalUniqUrlCount);
					
					// process page rank
					if(internalLinkVO.getEnablePageRank()) {
						
						clarity360Merge01DAO.processPageRank(ownDomainId, internalLinkVO, 
								targetDate, reportId, internalLinkVO.getUrlTagId(), internalLinkVO.getContentTypeFilterVO(), "URLHash(lower(url))", true, partitionNum, pageNum);
					}
					
				} else if (StringUtils.equals(funcName, "backlink")) {
					System.out.println(" === processing backlink ");
					BacklinkVO backlinkVO = (BacklinkVO)clarity360BaseVO;
					
					clarity360Merge01DAO.backLink360Summary(ownDomainId, backlinkVO, ownDomainEntity,
							targetDate, reportId, backlinkVO.getUrlTagId(), backlinkVO.getContentTypeFilterVO(), "URLHash(lower(target_url))", true, partitionNum, pageNum);
				} else if (StringUtils.equals(funcName, "customDataSource")) {
					System.out.println(" === processing customDataSource ");
					CustomDataSourceVO customDataSourceVO = (CustomDataSourceVO)clarity360BaseVO;
					
					clarity360Merge01DAO.customDataSource360Summary(ownDomainId, customDataSourceVO, ownDomainEntity,
							targetDate, reportId, customDataSourceVO.getUrlTagId(), customDataSourceVO.getContentTypeFilterVO(), "url_hash", true, partitionNum, pageNum);
				} else {
					System.out.println("funcName not support! funcName : " + funcName); 
				}
				break;
			} catch (Exception e) {
				e.printStackTrace();
				try {
					System.out.println("Sleep 10 sec...");
					Thread.sleep(10 * 1000);
				} catch (Exception e2) {
					e2.printStackTrace();
				}
				
				if (num >= MAX_RETRY_NUMBER) {
					throw e;
				}
			}
			
			num++;
			
		}
    	
    }

	
    private void insertNewStage(Integer reportId, Integer status, Integer dateSourceType) {
    	try {
    		Clarity360ReportStageEntity clarity360ReportStageEntity = new Clarity360ReportStageEntity();
        	
        	clarity360ReportStageEntity.setReportId(reportId);
        	clarity360ReportStageEntity.setStatus(status);
        	clarity360ReportStageEntity.setDataSource(dateSourceType);
        	clarity360ReportStageEntity.setProcessStartTime(new Date());
        	
        	clarity360ReportStageEntityDAO.insertAutoRunDetail(clarity360ReportStageEntity);
		} catch (Exception e) {
			e.printStackTrace();
		}
    	
    }
	
}
