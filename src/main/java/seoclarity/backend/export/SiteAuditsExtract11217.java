package seoclarity.backend.export;


import cn.hutool.core.util.ZipUtil;
import com.amazonaws.regions.Regions;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.lang.StringUtils;
import seoclarity.backend.dao.actonia.CrawlRequestLogDAO;
import seoclarity.backend.entity.ClarityDBConstants;
import seoclarity.backend.entity.actonia.CrawlRequestLog;
import seoclarity.backend.utils.AmazonS3UploadTool;
import seoclarity.backend.utils.ClarityDBUtils;
import seoclarity.backend.utils.GZipUtil;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.utils.amazon.AwsCredentialsEnvKeyConstructor;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * https://www.wrike.com/open.htm?id=1049163096
 */
@CommonsLog
public class SiteAuditsExtract11217 {

    /** if additional_status_v2 = 2 , run extract
     * select * from crawl_request_log
     * where project_id =  12993697908167219 and crawl_request_date <= 20230508
     * order by id desc limit 5
     */

    private static final String S3_ROLE_ARN ="arn:aws:iam::739939173819:role/seoclarity_role";
    private static final String S3_EXTERNAL_ID ="seoclarityDev-externalId-assumeRole";
    private static final String S3_BUCKET_NAME = "upwork-usw2-prod-seo-clarity-crawl";
    private static final int S3_SESSION_DURATION_SECONDS = 3600;
    private static final int S3_RETRY_COUNT = 10;
    private CrawlRequestLogDAO crawlRequestLogDAO;

    public SiteAuditsExtract11217(){
        crawlRequestLogDAO = SpringBeanFactory.getBean("crawlRequestLogDAO");
    }

    private void process(int domainId, int processDate,  long projectId){

        CrawlRequestLog crawlRequestLog = crawlRequestLogDAO.getLogIdByProjectId(domainId, projectId, processDate);
        if(crawlRequestLog == null){
            log.info("===logId not exist,domainId:" + domainId + ",projectId:" + projectId + ",processDate:" + processDate);
            return;
        }

//        if(crawlRequestLog.getAdditionalStatusV2() != null && crawlRequestLog.getAdditionalStatusV2() != CrawlRequestLog.INTERNALLINK_DATA_STATUS_COMPLETE){
//            log.info("====data not ready.");
//            return;
//        }

        int logId = crawlRequestLog.getId();
        log.info("====start logId:" + logId);
        StringBuffer sql = new StringBuffer();
        sql.append("	SELECT  ");
        sql.append("	    sourceUrl, sourceRespCode, destUrl, destRespCode, destUrlTitle, anchorText  ");
        sql.append("     FROM dis_internal_link_sampled_view_final");
        sql.append("  WHERE crawlRequestLogId = " + logId + " AND domainId = " + domainId);
        sql.append("  AND crawlRequestLogMod = " + (logId % 1000));

        File extractFile = new File("/home/<USER>/11217/11217_SiteAuditsExtract_" + processDate + ".csv");
        String filePath = extractFile.getAbsolutePath();
        List<String> header = new ArrayList<String>();
        header.add("Source Url");
        header.add("Source Url Response Code");
        header.add("Destination Url");
        header.add("Destination Response Code");
        header.add("Page title");
        header.add("Anchor Text");

        try {
            ClarityDBUtils.postToFile(ClarityDBConstants.INTERNAL_LINK_SERVER4_HOST, sql + " format CSV", filePath, header.toArray(new String[header.size()]));
            System.out.println("=out fileName:" + filePath);

            File zipFile = new File(filePath+".gz");
            if (zipFile!= null && zipFile.exists() && zipFile.isFile()) {
                System.out.println("file already exist, deleted!!!");
                zipFile.delete();
            }

//            GZipUtil.zip(filePath, zipFile.getAbsolutePath());
            ZipUtil.zip(filePath, zipFile.getAbsolutePath());
            System.out.println("======OutGzipFile:" + zipFile.getAbsolutePath());

            extractFile.delete();
            sentToS3(zipFile.getName(), zipFile.getAbsolutePath());
        } catch (Exception e) {
            System.out.println(sql);
            e.printStackTrace();
        }

    }

    private void sentToS3(String s3Key, String filePath){
        String s3AccessKey = AwsCredentialsEnvKeyConstructor.getInstance().getPlainTextS3AccessKey();
        String s3SecretKey = AwsCredentialsEnvKeyConstructor.getInstance().getS3DecryptedSecretKey();
        boolean savedFilesToS3 = AmazonS3UploadTool.sendFileToS3WithRoleAndSSEByTransferManager(
                s3AccessKey, s3SecretKey, S3_EXTERNAL_ID, S3_ROLE_ARN, S3_BUCKET_NAME, s3Key,
                filePath, Regions.US_WEST_2, S3_SESSION_DURATION_SECONDS, S3_RETRY_COUNT);
        if (!savedFilesToS3) {
            log.error("===send to s3 failed.");
        } else {
            log.info("=====send to s3 success!" + s3Key);
        }
    }

    public static void main(String[] args){
        SiteAuditsExtract11217 siteAuditsExtract11217 = new SiteAuditsExtract11217();
//        siteAuditsExtract11217.process(11217 ,20230413, 12993697908167219l);
        siteAuditsExtract11217.sentToS3("11217_SiteAuditsExtract_20230413.csv.gz", "/home/<USER>/11217/11217_SiteAuditsExtract_20230413.csv.gz");
//        siteAuditsExtract11217.sentToS3("test.txt", "D:\\workspace\\extract\\s3\\test.txt");
    }


}
