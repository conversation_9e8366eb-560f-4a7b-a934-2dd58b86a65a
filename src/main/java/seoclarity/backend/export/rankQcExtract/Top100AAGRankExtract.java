package seoclarity.backend.export.rankQcExtract;

import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import seoclarity.backend.dao.actonia.GroupTagEntityDAO;
import seoclarity.backend.dao.actonia.KeywordEntityDAO;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.actonia.OwnDomainSettingEntityDAO;
import seoclarity.backend.dao.clickhouse.ClDailyRankingEntityDao;
import seoclarity.backend.entity.ExtractQueryVO;
import seoclarity.backend.entity.actonia.*;
import seoclarity.backend.entity.actonia.extract.ExtractScriptConfigEntity;
import seoclarity.backend.entity.actonia.extract.ExtractScriptDetailEntity;
import seoclarity.backend.entity.actonia.extract.ExtractScriptInstanceEntity;
import seoclarity.backend.entity.clickhouse.CLRankingDetailEntity;
import seoclarity.backend.service.*;
import seoclarity.backend.service.RankTypeManager;
import seoclarity.backend.utils.*;

import java.io.File;
import java.io.IOException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@CommonsLog
public class Top100AAGRankExtract {

    private static final String SPLIT = "\t";
    private static final String TAG_SPLIT = "!_!";
    private static final String KEY_SPLIT = "#_#";
    private static String LOC = "/home/<USER>/";
    private static int configDomainId = 6630;
    private static final int QUERY_TRY_COUNT = 10;

    private static Date processDate;
    private static String[] domainIdList;
    private static int domainId;
    private static boolean extractTag = false;
    private static String device = "desktop";
    private static boolean ignoreQC = false;

    private int rankDate;
    private int engineId;
    private int languageId;
    private int rankType;
    private static int topX;
    private static boolean isGeo = false;
    private static int qcFrequence = 7;
    private long startTime;
    private int outputDataCount = 0;
    private ExtractScriptDetailEntity extractScriptDetail;

    private ScKeywordRankManager scKeywordRankManager;
    private OwnDomainEntityDAO ownDomainEntityDAO;
    private OwnDomainSettingEntityDAO ownDomainSettingEntityDAO;
    private ClDailyRankingEntityDao clDailyRankingEntityDao;
    private ServerAuthenticationInfoService serverAuthenticationInfoService;
    private EmailSenderComponent emailSenderComponent;
    private KeywordEntityDAO keywordEntityDAO;
    private GroupTagEntityDAO groupTagEntityDAO;
    private ExtractService extractService;
    private GeoService geoService;

    private final static List<Integer> DOMAIN_LIST = Arrays.asList(
            6630
            , 6821, 6822, 6823, 6824, 6825, 6826, 6827, 6828, 6829, 6830, 6831, 6832, 6833, 6834, 6835, 6837, 6838, 6839, 6840,
            6841, 6842, 6843, 6845, 6846, 6847, 6848, 6849, 6850, 6851, 6852, 6853, 6854, 6856, 6857, 6858, 6859, 6860, 6861, 6862,
            6863, 6864, 6865, 6866, 6867, 6868, 6869, 6871, 6873, 6874, 6875, 6876, 6878, 6879, 6880, 6881, 6882, 6883, 6884, 6885,
            6886, 6888, 6890, 6891, 6893, 6894, 6895, 6896, 6897, 6898, 6899, 6900, 6901, 6902, 6904, 6905, 6906, 6981, 6985, 6986,
            7118, 7262, 7406, 7472, 7473, 7499, 7512, 7513, 7546, 7571, 7608, 8815, 9396, 9491, 9492, 9586, 9587, 9588, 9653, 9674,
            9684, 9685
    );

    //send to special remote server
    public static final Map<Integer, Integer> SPECIAL_DOMAIN_SERVER_MAP = new HashMap();
    static {
        SPECIAL_DOMAIN_SERVER_MAP.put(6630, ServerAuthenticationInfoEntity.SERVER_TYPE_NEW_FTP_METHOD);
    }

    public Top100AAGRankExtract() {
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        ownDomainSettingEntityDAO = SpringBeanFactory.getBean("ownDomainSettingEntityDAO");
        scKeywordRankManager = SpringBeanFactory.getBean("scKeywordRankManager");
        clDailyRankingEntityDao = SpringBeanFactory.getBean("clDailyRankingEntityDao");
        serverAuthenticationInfoService = SpringBeanFactory.getBean("serverAuthenticationInfoService");
        emailSenderComponent = SpringBeanFactory.getBean("emailSenderComponent");
        keywordEntityDAO = SpringBeanFactory.getBean("keywordEntityDAO");
        groupTagEntityDAO = SpringBeanFactory.getBean("groupTagEntityDAO");
        extractService = SpringBeanFactory.getBean("extractService");
        geoService = SpringBeanFactory.getBean("geoService");
    }


    private void processMonthly() {

        Date nowDate = new Date();

        log.info("**************** start time :" + FormatUtils.formatDate(nowDate, "yyyy-MM-dd HH:mm:ss") + " **********");

        Date processingDate = FormatUtils.getLastMonthLastDay(nowDate, 1);
        log.info("====processingDate:" + processingDate);

        List<ExtractScriptInstanceEntity> instanceList = extractService.getNeedRunExtract(
                ExtractScriptConfigEntity.CATEGORY_RANK, getClass().getName(),
                ExtractScriptConfigEntity.SPECIAL_CATEGORY_DOMAIN_LEVEL + "", ExtractScriptConfigEntity.FREQUENCY_MONTHLY, processingDate);

        if (CollectionUtils.isEmpty(instanceList)) {
            log.info("===no instant exit!");
            return;
        }

        for (ExtractScriptInstanceEntity instance : instanceList) {

            List<ExtractScriptDetailEntity> detailList = instance.getScriptDetailList();
            for (ExtractScriptDetailEntity scriptDetailEntity : detailList) {
                //todo qc check
//                boolean isPassQC = allDomainPassQc(instance, scriptDetailEntity);
                boolean isPassQC = true;

                rankType = instance.getRankType();
                topX = instance.getTopXRank();
                extractScriptDetail = scriptDetailEntity;
                rankDate = FormatUtils.formatDateToYyyyMmDd(processDate);
                processDate = FormatUtils.toDate(String.valueOf(scriptDetailEntity.getTargetDate()), FormatUtils.DATE_FORMAT_YYYYMMDD);

                if (isPassQC) {

                    String rankingDate = FormatUtils.formatDate(processDate, FormatUtils.DATE_PATTERN_2);

                    String fileName = getFileName(rankingDate);
                    File localFolder = new File(LOC + configDomainId);
                    if (!localFolder.exists() || !localFolder.isDirectory()) {
                        localFolder.mkdirs();
                    }

                    String localFilePath = LOC + configDomainId + File.separator;
                    String remoteFilePath = "/";

                    String localFileName = localFilePath + fileName;
                    File localFile = new File(localFileName);
                    if (localFile.exists()) {
                        localFile.delete();
                    }

                    try {
                        addHeadersForExactFile(localFile);
                        extractScriptDetail.setOutputFile(localFileName);

                    } catch (Exception e) {
                        e.printStackTrace();
                        String fatalError = "002 error";
                        extractService.updateDetailAndSendEmail(extractScriptDetail, ExtractScriptDetailEntity.STATUS_FAILURE,
                                fatalError, startTime, configDomainId, device, false, processDate);
                        return;
                    }



                    for (Integer ownDomainId : DOMAIN_LIST) {
                        domainId = ownDomainId;
                        log.info("********************** process domain " + domainId + ",processDate: " + processDate + " *******************");
                        OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getById(domainId);
                        if(ownDomainEntity == null){
                            log.info("======skip inactive domain:" + domainId);
                            continue;
                        }
                        engineId = scKeywordRankManager.getSearchEngineId(ownDomainEntity);
                        languageId = scKeywordRankManager.getSearchLanguageId(ownDomainEntity);
                        processExtract(localFile,ownDomainEntity);

                        try {
                            Thread.sleep(1000);
                        }catch (Exception e){
                        }

                    }

                    extractScriptDetail.setOutputDataCount(outputDataCount);
                    extractScriptDetail.setOutputFileSizeKB(seoclarity.backend.utils.FileUtils.GetFileKBSize(localFile));

                    int serverType = ServerAuthenticationInfoEntity.SERVER_TYPE_FTP;
                    if (SPECIAL_DOMAIN_SERVER_MAP.get(configDomainId) != null) {
                        serverType = SPECIAL_DOMAIN_SERVER_MAP.get(configDomainId);
                    }

                    String zipFile = null;
                    try {
                        if(serverType == ServerAuthenticationInfoEntity.SERVER_TYPE_AMAZON_S3){
                            serverAuthenticationInfoService.copyFileToRemoteServerNew(serverType, configDomainId, localFileName, null, extractScriptDetail);
                        }else {
                            serverAuthenticationInfoService.copyFileToRemoteServerNew(serverType, configDomainId, localFileName, remoteFilePath, extractScriptDetail);
                        }

                    } catch (Exception e) {
                        e.printStackTrace();
                        String fatalError = "send to remote server error!!";
                        extractService.updateDetailAndSendEmail(extractScriptDetail, ExtractScriptDetailEntity.STATUS_FAILURE,
                                fatalError, startTime, configDomainId, device, false, processDate);
                    }

                    extractScriptDetail.setStatus(ExtractScriptDetailEntity.STATUS_SUCCESS);
                    long elapsedSeconds = (System.currentTimeMillis() - startTime) / 1000;
                    extractScriptDetail.setEndedTime(new Date());
                    extractScriptDetail.setElapsedSeconds((int) elapsedSeconds);
                    log.info("===update for success id:" + extractScriptDetail.getId());
                    extractService.updateForSuccess(extractScriptDetail);



                } else {
                    log.error("=====================not pass qc: waiting for run next time.");
                    break;
                }
            }

        }


    }

    private boolean allDomainPassQc(ExtractScriptInstanceEntity instance, ExtractScriptDetailEntity scriptDetailEntity) {

        for (Integer ownDomainId : DOMAIN_LIST) {

            domainId = ownDomainId;
            processDate = FormatUtils.toDate(String.valueOf(scriptDetailEntity.getTargetDate()), FormatUtils.DATE_FORMAT_YYYYMMDD);
            log.info("********************** process domain " + domainId + ",processDate: " + processDate + " *******************");

            int tagId = instance.getTagId();
            if (tagId > 0) {
                extractTag = true;
            }

            rankType = instance.getRankType();
            topX = instance.getTopXRank();
            extractScriptDetail = scriptDetailEntity;

            try {
                OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getById(domainId);
                if (ownDomainEntity == null) {
                    log.error(" domain not exist : " + domainId);
                    return false;
                }

                engineId = scKeywordRankManager.getSearchEngineId(ownDomainEntity);
                languageId = scKeywordRankManager.getSearchLanguageId(ownDomainEntity);
                rankDate = FormatUtils.formatDateToYyyyMmDd(processDate);


                extractScriptDetail.setStatus(ExtractScriptDetailEntity.STATUS_PROCESSING);
                extractScriptDetail.setStartedTime(new Date());

                startTime = System.currentTimeMillis();

                extractService.updateForStart(extractScriptDetail);

                log.info("======process device:" + device);
                if (!ignoreQC) {
                    if (rankType == ExtractScriptInstanceEntity.RANK_TYPE_ALL) {

                        boolean isPassed = extractService.isRankQcPass(rankDate, domainId, engineId, languageId, device, ExtractScriptInstanceEntity.RANK_TYPE_NATIONAL, qcFrequence);
                        if (!isPassed) {
                            log.error("===not pass rank qc!!" + ExtractScriptInstanceEntity.RANK_TYPE_NATIONAL);
                            extractService.updateDetailAndSendEmail(extractScriptDetail, ExtractScriptDetailEntity.SKIP_TO_RUN,
                                    null, startTime, domainId, device, false, processDate);
                            return false;
                        }
                        isPassed = extractService.isRankQcPass(rankDate, domainId, engineId, languageId, device, ExtractScriptInstanceEntity.RANK_TYPE_GEO, qcFrequence);
                        if (!isPassed) {
                            log.error("===not pass rank qc!!" + ExtractScriptInstanceEntity.RANK_TYPE_GEO);
                            extractService.updateDetailAndSendEmail(extractScriptDetail, ExtractScriptDetailEntity.SKIP_TO_RUN,
                                    null, startTime, domainId, device, false, processDate);
                            return false;
                        }

                    }

                }

            } catch (Exception e) {
                e.printStackTrace();
                String fatalError = "001 exception";
                extractService.updateDetailAndSendEmail(extractScriptDetail, ExtractScriptDetailEntity.STATUS_FAILURE,
                        fatalError, startTime, domainId, device, false, processDate);
                return false;
            }

        }

        return true;
    }

    private void processExtract(File localFile, OwnDomainEntity ownDomainEntity) {

        try {

            processFile(localFile, ownDomainEntity);

        } catch (Exception e) {
            e.printStackTrace();
            String fatalError = "002 error";
            extractService.updateDetailAndSendEmail(extractScriptDetail, ExtractScriptDetailEntity.STATUS_FAILURE,
                    fatalError, startTime, configDomainId, device, false, processDate);
            return;
        }

    }

    private String getFileName(String rankingDate) {

        String fileName = "AAG_Google_" + rankingDate + "_RankReport_Desktop.txt";

        return fileName;
    }

    private void processFile(File localFile, OwnDomainEntity ownDomainEntity) throws Exception {

        int retryCount = 1;
        List<String> dataList = new ArrayList<>();
        while (true) {
            try {
                dataList = getDataFromDB(ownDomainEntity);
                break;
            } catch (Exception e) {

                if (retryCount >= QUERY_TRY_COUNT) {
                    System.out.println("====error extract oid : " + domainId);
                    System.out.println("====RETRY OVER " + QUERY_TRY_COUNT + "TIMES error :" + domainId);
                    String fatalError = "002 error";
                    extractService.updateDetailAndSendEmail(extractScriptDetail, ExtractScriptDetailEntity.STATUS_FAILURE,
                            fatalError, startTime, configDomainId, device, false, processDate);
                    return;
                }

                e.printStackTrace();
                System.out.println("====extract error oid:" + domainId + ", sleep " + (1000 * 60 * retryCount));
                try {
                    Thread.sleep(1000 * 60 * retryCount);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
                retryCount++;
            }
        }

        FileUtils.writeLines(localFile, dataList, true);
        outputDataCount += dataList.size();
    }


    private List<String> getDataFromDB(OwnDomainEntity ownDomainEntity) throws Exception {

        List<String> extractLines = new ArrayList<String>();

        int ownDomainId = ownDomainEntity.getId();
        int engineId = scKeywordRankManager.getSearchEngineId(ownDomainEntity);
        int languageId = scKeywordRankManager.getSearchLanguageId(ownDomainEntity);
        String domainName = ownDomainEntity.getDomain();
        String rankingDate = FormatUtils.formatDate(processDate, FormatUtils.DATE_PATTERN_2);

        List<CLRankingDetailEntity> dataList = new ArrayList<>();
        int locationId = 0;

        ExtractQueryVO extractQueryVO = new ExtractQueryVO();
        extractQueryVO.setDomainId(ownDomainId);
        extractQueryVO.setEngineId(engineId);
        extractQueryVO.setLanguageId(languageId);
        extractQueryVO.setDevice(device);
        extractQueryVO.setRankDate(rankingDate);
        extractQueryVO.setRank(topX);
        extractQueryVO.setLocationId(locationId);
        extractQueryVO.setExtractAllLocation(true);

        //https://www.wrike.com/open.htm?id=614656875
        List<Integer> locationIdList = geoService.getLocationIdList(domainId);
        System.out.println("locationIdList=>"+locationIdList);
        Integer [] idArr = locationIdList.toArray(new Integer[locationIdList.size()]);

        dataList = clDailyRankingEntityDao.exportTopXKeywordsWithMultFilter(extractQueryVO, idArr);


        log.info("====dataList size:" + dataList.size());

        for (CLRankingDetailEntity detail : dataList) {
            String locationName =  detail.getLocationId() == 0 ? "United States" : geoService.getCityName(detail.getLocationId());
            if (StringUtils.isBlank(locationName)) {
                locationName = "-";
            }
            extractLines.add(appendData(detail, ownDomainEntity, locationName));
        }

        return extractLines;
    }

    private void addHeadersForExactFile(File outFile) throws IOException {

        List<String> lines = new ArrayList<String>();
        StringBuffer header = new StringBuffer();
        header.append("Date").append(SPLIT);
        header.append("Domain").append(SPLIT);
        header.append("Location").append(SPLIT);
        header.append("Keyword").append(SPLIT);
        header.append("Ranking URL").append(SPLIT);
        header.append("Rank").append(SPLIT);
        header.append("URL Type").append(SPLIT);
        header.append("Search Volume");

        if (StringUtils.isNotBlank(header.toString())) {
            lines.add(header.toString());
            FileUtils.writeLines(outFile, lines, true);
        }

    }


    public static String appendData(CLRankingDetailEntity clRankingDetailEntity, OwnDomainEntity ownDomainEntity, String locationName) {
        StringBuffer line = new StringBuffer();

        line.append(clRankingDetailEntity.getRankingDate()).append(SPLIT);
        line.append(ownDomainEntity.getDomain()).append(SPLIT);
        line.append(locationName).append(SPLIT);
        line.append(clRankingDetailEntity.getKeywordName()).append(SPLIT);
        line.append(clRankingDetailEntity.getUrl() == null ? "" : clRankingDetailEntity.getUrl()).append(SPLIT);
        if (clRankingDetailEntity.getTrueRank().equals(-1) || clRankingDetailEntity.getTrueRank().equals(0)) {
            line.append("101").append(SPLIT);
        } else {
            line.append(clRankingDetailEntity.getTrueRank()).append(SPLIT);
        }
        line.append(RankTypeManager.getUrlTypeName(clRankingDetailEntity.getType())).append(SPLIT);
        line.append(clRankingDetailEntity.getAvgSearchVolume());

        return line.toString();
    }


    public static void main(String[] args) {


        Top100AAGRankExtract top100AAGRankExtract = new Top100AAGRankExtract();
        top100AAGRankExtract.processMonthly();

    }

}
