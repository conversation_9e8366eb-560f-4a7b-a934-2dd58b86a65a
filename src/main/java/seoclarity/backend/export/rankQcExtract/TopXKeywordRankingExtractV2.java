package seoclarity.backend.export.rankQcExtract;

import com.google.gson.Gson;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import seoclarity.backend.dao.actonia.GroupTagEntityDAO;
import seoclarity.backend.dao.actonia.KeywordEntityDAO;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.actonia.OwnDomainSettingEntityDAO;
import seoclarity.backend.dao.clickhouse.ClDailyRankingEntityDao;
import seoclarity.backend.entity.ExtractQueryVO;
import seoclarity.backend.entity.actonia.*;
import seoclarity.backend.entity.actonia.extract.ExtractScriptConfigEntity;
import seoclarity.backend.entity.actonia.extract.ExtractScriptDetailEntity;
import seoclarity.backend.entity.actonia.extract.ExtractScriptInstanceEntity;
import seoclarity.backend.entity.clickhouse.CLRankingDetailEntity;
import seoclarity.backend.service.*;
import seoclarity.backend.utils.CommonUtils;
import seoclarity.backend.utils.EmailSenderComponent;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.utils.loggly.LogglyUtils;
import seoclarity.backend.utils.loggly.LogglyVO;

import java.io.File;
import java.io.IOException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.*;

/**
 * https://www.wrike.com/open.htm?id=534333697
 */
@CommonsLog
public class TopXKeywordRankingExtractV2 {

    private static final String SPLIT = "\t";
    private static final String TAG_SPLIT = "!_!";
    private static final String KEY_SPLIT = "#_#";
    private static String LOC = "/home/<USER>/";
    private static final int QUERY_TRY_COUNT = 10;

    private static Date processDate;
    private static String[] domainIdList;
    private static int domainId;
    private static boolean extractTag = false;
    private static String device;
    private static boolean ignoreQC = false;
    private static LogglyVO logglyVO = new LogglyVO();
    private static int totalCnt = 0;


    private int rankDate;
    private int engineId;
    private int languageId;
    private int rankType;
    private static int topX;
    private static boolean isGeo = false;
    private static int frequence;
    private long startTime;
    private ExtractScriptDetailEntity extractScriptDetail;

    private ScKeywordRankManager scKeywordRankManager;
    private OwnDomainEntityDAO ownDomainEntityDAO;
    private OwnDomainSettingEntityDAO ownDomainSettingEntityDAO;
    private ClDailyRankingEntityDao clDailyRankingEntityDao;
    private ServerAuthenticationInfoService serverAuthenticationInfoService;
    private EmailSenderComponent emailSenderComponent;
    private KeywordEntityDAO keywordEntityDAO;
    private GroupTagEntityDAO groupTagEntityDAO;
    private ExtractService extractService;
    private GeoService geoService;

    public TopXKeywordRankingExtractV2() {
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        ownDomainSettingEntityDAO = SpringBeanFactory.getBean("ownDomainSettingEntityDAO");
        scKeywordRankManager = SpringBeanFactory.getBean("scKeywordRankManager");
        clDailyRankingEntityDao = SpringBeanFactory.getBean("clDailyRankingEntityDao");
        serverAuthenticationInfoService = SpringBeanFactory.getBean("serverAuthenticationInfoService");
        emailSenderComponent = SpringBeanFactory.getBean("emailSenderComponent");
        keywordEntityDAO = SpringBeanFactory.getBean("keywordEntityDAO");
        groupTagEntityDAO = SpringBeanFactory.getBean("groupTagEntityDAO");
        extractService = SpringBeanFactory.getBean("extractService");
        geoService = SpringBeanFactory.getBean("geoService");
    }


    //send to special remote server
    public static final Map<Integer, Integer> SPECIAL_DOMAIN_SERVER_MAP = new HashMap();

    static {
        SPECIAL_DOMAIN_SERVER_MAP.put(9623, ServerAuthenticationInfoEntity.SERVER_TYPE_AMAZON_S3);
        SPECIAL_DOMAIN_SERVER_MAP.put(9463, ServerAuthenticationInfoEntity.SERVER_TYPE_SFTP);
        SPECIAL_DOMAIN_SERVER_MAP.put(9511, ServerAuthenticationInfoEntity.SERVER_TYPE_AMAZON_S3);
        SPECIAL_DOMAIN_SERVER_MAP.put(4609, ServerAuthenticationInfoEntity.SERVER_TYPE_AMAZON_S3);//https://www.wrike.com/open.htm?id=1102975596
        SPECIAL_DOMAIN_SERVER_MAP.put(10010, ServerAuthenticationInfoEntity.SERVER_TYPE_AMAZON_S3);
        SPECIAL_DOMAIN_SERVER_MAP.put(765, ServerAuthenticationInfoEntity.SERVER_TYPE_AMAZON_S3);
        SPECIAL_DOMAIN_SERVER_MAP.put(2047, ServerAuthenticationInfoEntity.SERVER_TYPE_AMAZON_S3);
        SPECIAL_DOMAIN_SERVER_MAP.put(9422, ServerAuthenticationInfoEntity.SERVER_TYPE_AMAZON_S3);
    }

    //need extract secondary domains
    public static final List<Integer> EXTRACT_SECONDARY_DOMAIN_LIST = Arrays.asList(9511, 12044);

    public static final List<Integer> FOUND_SEARCH_MARKETING_DOMAIN_LIST = Arrays.asList(
            9463, 9465, 9515, 9927, 9982, 10142, 10143, 10144, 10145, 10146, 10147, 10148, 10149,
            10150, 10151, 10152, 10153, 10154, 10155, 10156, 10157, 10158, 10159, 10160, 10166, 10633);

    //https://www.wrike.com/open.htm?id=1152694464
    private static final List<Integer> GROUNDWORKS_DOMAIN_LIST_TEST = Arrays.asList(11699,11700);

    private static final List<Integer> GROUNDWORKS_DOMAIN_LIST = Arrays.asList(11699, 11700, 11701, 11702, 11703, 11704, 11705, 11706, 11707, 11708, 11709, 11710
            , 11711, 11712, 11713, 11714, 11715, 11716, 11717, 11718);

    private static Map<String, String> engineNameMap = new HashMap<>();

    private void processForDomain(int ownDomainId) {

        log.info("********************** process domain " + ownDomainId + ",processDate: " + processDate + " *******************");

        try {
            log.info("sleep 2s");
            Thread.sleep(2 * 1000);
        } catch (Exception e) {
            e.printStackTrace();
        }

        OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getById(ownDomainId);
        if (ownDomainEntity == null) {
            log.error(" domain not exist : " + ownDomainId);
            return;
        }

        OwnDomainSettingEntity ownDomainSettingEntity = ownDomainSettingEntityDAO.getSettingByDomainId(ownDomainId);

//        int engineId = scKeywordRankManager.getSearchEngineId(ownDomainEntity);
//        int languageId = scKeywordRankManager.getSearchLanguageId(ownDomainEntity);
//        int rankDate = FormatUtils.formatDateToYyyyMmDd(processDate);
//        boolean isPassed = extractService.isRankQcPass(rankDate,ownDomainId,engineId, languageId,device, );
//        if(!isPassed){
//            log.error("===not pass rank qc!!");
//            return;
//        }
        extractScriptDetail = new ExtractScriptDetailEntity();
        processExtract(ownDomainEntity);
    }

    private void processExtract(OwnDomainEntity ownDomainEntity) {

        int ownDomainId = ownDomainEntity.getId();
        int processingDate = FormatUtils.formatDateToYyyyMmDd(processDate);

        String fileName = getFileName(ownDomainEntity, processingDate);

        File localFolder = new File(LOC + ownDomainId);
        if (!localFolder.exists() || !localFolder.isDirectory()) {
            localFolder.mkdirs();
        }

        String localFilePath = LOC + ownDomainId + File.separator;
        String remoteFilePath = localFilePath;
        if (ownDomainId == 6997) {
            remoteFilePath = LOC + ownDomainId + File.separator + "StandardRankExtract" + File.separator;
        } else if (ownDomainId == 10619) {
            remoteFilePath = LOC + ownDomainId + File.separator + "cvs-ranking-files" + File.separator;
        } else if (FOUND_SEARCH_MARKETING_DOMAIN_LIST.contains(ownDomainId)) {
            remoteFilePath = "/seoclarity" + File.separator;
        } else if (ownDomainId == 10735) {
            if (device.equalsIgnoreCase("mobile")) {
                remoteFilePath = LOC + ownDomainId + File.separator + "Top_rank_data_mobile" + File.separator;
            } else {
                remoteFilePath = LOC + ownDomainId + File.separator + "Top_rank_data_desktop" + File.separator;
            }
        } else if (ownDomainId == 5367) {//https://www.wrike.com/open.htm?id=862559926
            remoteFilePath = LOC + ownDomainId + File.separator + "search_volume_daily" + File.separator;
        } else if (ownDomainId == 12044) {
            remoteFilePath = LOC + ownDomainId + File.separator + "PD23-daily-ranking-files" + File.separator;
        }


        String localFileName = localFilePath + fileName;
        File localFile = new File(localFileName);
        if (localFile.exists()) {
            localFile.delete();
        }

        try {
            extractScriptDetail.setOutputFile(localFileName);
            processFile(localFile, ownDomainEntity, localFileName, remoteFilePath);
        } catch (Exception e) {
            e.printStackTrace();
            String fatalError = "002 error";
            extractService.updateDetailAndSendEmail(extractScriptDetail, ExtractScriptDetailEntity.STATUS_FAILURE,
                    fatalError, startTime, ownDomainId, device, false, processDate);
            return;
        }

    }

    private String getFileName(OwnDomainEntity ownDomainEntity, int processingDate) {

        String fileName = "";
        if (domainId == 7006) {
            fileName = ownDomainEntity.getDomain() + "_" + ownDomainEntity.getId() + "_" + ownDomainEntity.getSearchEngine() + "_" + processingDate + "_TOP_" + topX + "_RankReport_" + device + ".txt";
        } else if (domainId == 185) {
            String Date = FormatUtils.formatDate(processDate, "MMdd");
            if (device.equalsIgnoreCase("mobile")) {
                return "overstockMobileRankReport" + Date + ".csv";
            } else {
                return "overstockRankReport" + Date + ".csv";
            }
        } else if (domainId == 6997) {
            fileName = ownDomainEntity.getDomain() + "_" + ownDomainEntity.getId() + "_" + ownDomainEntity.getSearchEngine() + "_" + processingDate + "_RankReport_" + device + ".txt";
        } else if (FOUND_SEARCH_MARKETING_DOMAIN_LIST.contains(domainId)) {
            fileName = ownDomainEntity.getDomain() + "_" + ownDomainEntity.getSearchEngine() + "_" + processingDate + "_TOP_" + topX + "_RankReport_" + device + ".txt";
        } else if (domainId == 10735) {//https://www.wrike.com/open.htm?id=794254127
            fileName = ownDomainEntity.getId() + "_" + processingDate + "_TOP_" + topX + "_RankReport_" + device + ".csv";
        } else if (domainId == 5367) {
            fileName = "search_volume_daily_" + ownDomainEntity.getSearchEngine() + "_" + processingDate + ".txt";
        } else if (GROUNDWORKS_DOMAIN_LIST.contains(ownDomainEntity.getId())) {
            fileName =  ownDomainEntity.getDomain() + "_" + ownDomainEntity.getSearchEngine() + "_" + processingDate + "_RankReport_" + device + ".txt";
        }
        else {
            fileName = ownDomainEntity.getId() + "_" + processingDate + "_TOP_" + topX + "_RankReport_" + device + ".txt";
        }
        System.out.println(" domain : " +ownDomainEntity.getId() + " filename : " + fileName );
        return fileName;
    }

    private void processFile(File localFile, OwnDomainEntity ownDomainEntity, String localFilePath, String remoteFilePath) throws Exception {

        int ownDomainId = ownDomainEntity.getId();
        addHeadersForExactFile(localFile, ownDomainEntity);

        List<String> dataList = new ArrayList<>();
        int retryCount = 1;
        while (true) {
            try {
                dataList = getDataFromDB(ownDomainEntity);
                break;
            } catch (Exception e) {

                if (retryCount >= QUERY_TRY_COUNT) {
                    System.out.println("====error extract oid : " + ownDomainId);
                    System.out.println("====RETRY OVER " + QUERY_TRY_COUNT + "TIMES error :" + ownDomainId);

                    String fatalError = "002 error";
                    extractService.updateDetailAndSendEmail(extractScriptDetail, ExtractScriptDetailEntity.STATUS_FAILURE,
                            fatalError, startTime, ownDomainId, device, false, processDate);
                    return;
                }

                e.printStackTrace();
                System.out.println("====extract error oid:" + ownDomainId + ", sleep " + (1000 * 60 * retryCount));
                try {
                    Thread.sleep(1000 * 60 * retryCount);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
                retryCount++;
            }
        }

        FileUtils.writeLines(localFile, dataList, true);
        totalCnt = totalCnt + dataList.size();
        extractScriptDetail.setOutputDataCount(dataList.size());
        extractScriptDetail.setOutputFileSizeKB(seoclarity.backend.utils.FileUtils.GetFileKBSize(localFile));

        int serverType = ServerAuthenticationInfoEntity.SERVER_TYPE_FTP;
        if (SPECIAL_DOMAIN_SERVER_MAP.get(ownDomainId) != null) {
            serverType = SPECIAL_DOMAIN_SERVER_MAP.get(ownDomainId);
        } else if (FOUND_SEARCH_MARKETING_DOMAIN_LIST.contains(ownDomainId)) {
            serverType = SPECIAL_DOMAIN_SERVER_MAP.get(9463);
            ownDomainId = 9463;
        }

//        String zipFile = null;
//        try {
//
//            //https://www.wrike.com/open.htm?id=630279208
//            if (ownDomainId == 185) {
//                zipFile = GZipUtil.zipWithFileName(localFilePath);
//                if(serverType == ServerAuthenticationInfoEntity.SERVER_TYPE_AMAZON_S3){
//                    serverAuthenticationInfoService.copyFileToRemoteServerNew(serverType, ownDomainId, zipFile, null, extractScriptDetail);
//                }else {
//                    serverAuthenticationInfoService.copyFileToRemoteServerNew(serverType, ownDomainId, zipFile, remoteFilePath, extractScriptDetail);
//                }
//
//            } else {//unzipped file
//                if(serverType == ServerAuthenticationInfoEntity.SERVER_TYPE_AMAZON_S3){
//                    if(ownDomainId == 765 || ownDomainId == 2047 || ownDomainId == 9422){//https://www.wrike.com/open.htm?id=1111890232
//                        String s3KeyPath = "seo/ranking/" + localFile.getName();
//                        boolean isSendS3Success = serverAuthenticationInfoService.putFileForS3WithSSE(ServerAuthenticationInfoEntity.SERVER_TYPE_AMAZON_S3, ownDomainId, localFilePath, s3KeyPath);
//                        if(!isSendS3Success){
//                            log.error("====send to s3 failed!!");
//                        }
//                    }else {
//                        serverAuthenticationInfoService.copyFileToRemoteServerNew(serverType, ownDomainId, localFilePath, null, extractScriptDetail);
//                    }
//                }else {
//                    serverAuthenticationInfoService.copyFileToRemoteServerNew(serverType, ownDomainId, localFilePath, remoteFilePath, extractScriptDetail);
//                }
//            }
//
//            extractScriptDetail.setStatus(ExtractScriptDetailEntity.STATUS_SUCCESS);
//            long elapsedSeconds = (System.currentTimeMillis() - startTime) / 1000;
//            extractScriptDetail.setEndedTime(new Date());
//            extractScriptDetail.setElapsedSeconds((int) elapsedSeconds);
//            log.info("===update for success id:" + extractScriptDetail.getId());
//            extractService.updateForSuccess(extractScriptDetail);
//
//        } catch (Exception e) {
//            e.printStackTrace();
//            String fatalError = "send to remote server error!!";
//            extractService.updateDetailAndSendEmail(extractScriptDetail, ExtractScriptDetailEntity.STATUS_FAILURE,
//                    fatalError, startTime, domainId, device, false, processDate);
//        }

    }

    private List<String> getDataFromDB(OwnDomainEntity ownDomainEntity) throws Exception {

        List<String> extractLines = new ArrayList<String>();

        int ownDomainId = ownDomainEntity.getId();
        int engineId = scKeywordRankManager.getSearchEngineId(ownDomainEntity);
        int languageId = scKeywordRankManager.getSearchLanguageId(ownDomainEntity);
        String domainName = ownDomainEntity.getDomain();
        boolean isBroadMatch = ownDomainEntity.isBroadMatch();
        String rankingDate = FormatUtils.formatDate(processDate, FormatUtils.DATE_PATTERN_2);

        String domainReverse = StringUtils.reverseDelimited(domainName, '.');
        String rootDomainReverse = CommonUtils.getReversedRootDomain(domainName);


        List<CLRankingDetailEntity> dataList = new ArrayList<>();
        boolean isMobile = device.equalsIgnoreCase("mobile") ? true : false;
        String extractKey = engineId + "_" + languageId + "_" + isMobile;
        int locationId = 0;

        String engineName = ownDomainEntity.getSearchEngine();
        engineNameMap.put(extractKey, engineName);

        ExtractQueryVO extractQueryVO = new ExtractQueryVO();
        extractQueryVO.setDomainId(domainId);
        extractQueryVO.setEngineId(engineId);
        extractQueryVO.setLanguageId(languageId);
        extractQueryVO.setDevice(device);
        extractQueryVO.setRankDate(rankingDate);
        extractQueryVO.setRank(topX);
        extractQueryVO.setLocationId(locationId);

        //https://www.wrike.com/open.htm?id=938041639
        if (ownDomainId == 7006 || ownDomainId == 8509 || ownDomainId == 5367) {
            extractQueryVO.setIsBroadMatch(isBroadMatch);
            extractQueryVO.setRootDomainReverse(rootDomainReverse);
            extractQueryVO.setDomainReverse(domainReverse);
            dataList = clDailyRankingEntityDao.exportTopXAllKeywordsWithMultFilter(extractQueryVO);

        } else if (ownDomainId == 185 || FOUND_SEARCH_MARKETING_DOMAIN_LIST.contains(ownDomainId)) {
            extractQueryVO.setIsBroadMatch(isBroadMatch);
            extractQueryVO.setRootDomainReverse(rootDomainReverse);
            extractQueryVO.setDomainReverse(domainReverse);
            extractQueryVO.setIsHighest(true);
            extractQueryVO.setExtractOwnDomain(true);
            dataList = clDailyRankingEntityDao.exportTopXKeywordsWithMultFilter(extractQueryVO, null);
        } else if (ownDomainId == 10619) {//https://www.wrike.com/open.htm?id=774196093
            extractQueryVO.setExtractAllLocation(true);
            dataList = clDailyRankingEntityDao.exportTopXKeywordsWithMultFilter(extractQueryVO, null);
        } else if (ownDomainId == 10010) {
            extractQueryVO.setExtractGeo(true);
            dataList = clDailyRankingEntityDao.exportTopXKeywordsWithMultFilter(extractQueryVO, null);
        } else if (ownDomainId == 765 || ownDomainId == 2047 || ownDomainId == 9422) {
            dataList = clDailyRankingEntityDao.exportKeywordsFor765(true, ownDomainId, engineId, languageId,
                    rankingDate, isBroadMatch, isMobile, domainReverse, rootDomainReverse);
        } else if (GROUNDWORKS_DOMAIN_LIST.contains(domainId)) {
            extractQueryVO.setExtractAllLocation(true);
            dataList = clDailyRankingEntityDao.exportTopXKeywordsWithMultFilter(extractQueryVO, null);
        } else {
            dataList = clDailyRankingEntityDao.exportTopXKeywords(
                    ownDomainId, engineId, languageId, locationId, rankingDate, isMobile, topX);
        }

        log.info("===primary dataList size: " + dataList.size());

        if (EXTRACT_SECONDARY_DOMAIN_LIST.contains(ownDomainId)) {
            List<DomainSearchEngineRelEntity> domainSearchEngineRelEntityList = scKeywordRankManager.getDomainSearchEngineRels(ownDomainEntity);
            if (CollectionUtils.isNotEmpty(domainSearchEngineRelEntityList)) {
                for (DomainSearchEngineRelEntity domainSearchEngineRelEntity : domainSearchEngineRelEntityList) {
                    engineId = domainSearchEngineRelEntity.getRankcheckSearchEngineId();
                    languageId = domainSearchEngineRelEntity.getRankcheckSearchLanguageid();
                    boolean secondaryIsMobile = domainSearchEngineRelEntity.getDevice().equalsIgnoreCase("d") ? false : true;
                    if (secondaryIsMobile != isMobile) {
                        log.info("===secondary not same device isMobile: " + isMobile + ",secondaryIsMobile:" + secondaryIsMobile);
                        continue;
                    }
                    String secondaryExtractKey = engineId + "_" + languageId + "_" + secondaryIsMobile;
                    if (secondaryExtractKey.equalsIgnoreCase(extractKey)) {
                        log.info("===already extract engine skip: " + secondaryExtractKey + ",extractKey:" + extractKey);
                        continue;
                    }
                    String secondaryEngineName = domainSearchEngineRelEntity.getSearchEngine();
                    engineNameMap.put(secondaryExtractKey, secondaryEngineName);

                    log.info("===process secondary engine :" + engineId + ",languageId: " + languageId + ",isMobile:" + secondaryIsMobile + ",domain:" + ownDomainId);
                    List<CLRankingDetailEntity> secondaryDataList = new ArrayList<>();
                    if (ownDomainId == 8682) {
                        int rank = 100;
                        secondaryDataList = clDailyRankingEntityDao.exportTopXKeywords(
                                ownDomainId, engineId, languageId, locationId, rankingDate, secondaryIsMobile, rank);
                    } else {
                        secondaryDataList = clDailyRankingEntityDao.exportTopXKeywords(
                                ownDomainId, engineId, languageId, locationId, rankingDate, secondaryIsMobile, topX);
                    }
                    log.info("===secondaryDataList size:" + secondaryDataList.size());
                    dataList.addAll(secondaryDataList);
                }
            }
        }

        List<String> keywordNameList = new ArrayList<>();
        Map<String, String[]> tagMap = new HashMap<>();
        Map<String, CLRankingDetailEntity> keywordMap = new HashMap<>();

        log.info("===dataList size: " + dataList.size());

        if (extractTag) {
            //get tag
            for (CLRankingDetailEntity detail : dataList) {
                try {

                    keywordNameList.add(URLEncoder.encode(detail.getKeywordName(), "utf-8").toLowerCase());
                    if (keywordNameList.size() >= 100) {
                        //tag
                        List<GroupTagEntity> tagList = groupTagEntityDAO.getTagNamesByKeywordNameList(domainId, keywordNameList);
                        if (CollectionUtils.isNotEmpty(tagList)) {
                            for (GroupTagEntity groupTagEntity : tagList) {
                                String[] tagNameArray = groupTagEntity.getTagName().split(TAG_SPLIT);
                                String decodeKw = URLDecoder.decode(groupTagEntity.getKeywordName(), "utf-8").toLowerCase();
                                tagMap.put(decodeKw, tagNameArray);
                            }
                        }
                        keywordNameList.clear();
                    }
                    String key = detail.getKeywordName() + TAG_SPLIT + detail.getRank();
                    keywordMap.put(key, detail);

                } catch (Exception e) {
                    e.printStackTrace();
                    throw e;
                }
            }

            if (CollectionUtils.isNotEmpty(keywordNameList)) {
                List<GroupTagEntity> tagList = groupTagEntityDAO.getTagNamesByKeywordNameList(domainId, keywordNameList);
                if (CollectionUtils.isNotEmpty(tagList)) {
                    for (GroupTagEntity groupTagEntity : tagList) {
                        String[] tagNameArray = groupTagEntity.getTagName().split(TAG_SPLIT);
                        String decodeKw = URLDecoder.decode(groupTagEntity.getKeywordName(), "utf-8").toLowerCase();
                        tagMap.put(decodeKw, tagNameArray);
                    }
                }
            }

            for (String key : keywordMap.keySet()) {

                String kw = key.split(TAG_SPLIT)[0];
                CLRankingDetailEntity detail = keywordMap.get(key);
                String locationName = detail.getLocationId() == 0 ? "United States" : geoService.getCityName(detail.getLocationId());
                if (StringUtils.isBlank(locationName)) {
                    locationName = "-";
                }
                detail.setLocationName(locationName);

                String[] tags = tagMap.get(kw);
                if (tags != null && tags.length > 0) {
                    for (String tag : tags) {
                        extractLines.add(appendData(detail, ownDomainId, tag));
                    }
                } else {
                    extractLines.add(appendData(detail, ownDomainId, null));
                }
            }
        } else {
            if (ownDomainId == 4609 || ownDomainId == 10010 || ownDomainId == 765 || ownDomainId == 9422) {//extract plp url
                List<CLRankingDetailEntity> plpList = clDailyRankingEntityDao.getPLPKeywordUrl(extractQueryVO);
                Map<String, List<String>> plpMap = new HashMap<>();
                for (CLRankingDetailEntity clRankingDetailEntity : plpList) {
                    String key = clRankingDetailEntity.getKeywordRankcheckId() + TAG_SPLIT + clRankingDetailEntity.getLocationId();
                    if (plpMap.get(key) == null) {
                        List<String> plpUrlList = new ArrayList<>();
                        plpUrlList.add(clRankingDetailEntity.getPlpUrl());
                        plpMap.put(key, plpUrlList);
                    } else {
                        List<String> plpUrlList = plpMap.get(key);
                        plpUrlList.add(clRankingDetailEntity.getPlpUrl());
                        plpMap.put(key, plpUrlList);
                    }
                }
                log.info("====PLPMap:" + plpMap.size());
                for (CLRankingDetailEntity detail : dataList) {
                    String locationName = detail.getLocationId() == 0 ? "United States" : geoService.getCityName(detail.getLocationId());
                    if (StringUtils.isBlank(locationName)) {
                        locationName = "-";
                    }
                    detail.setLocationName(locationName);
                    String detailKey = detail.getKeywordRankcheckId() + TAG_SPLIT + detail.getLocationId();
                    List<String> plpUrlList = plpMap.get(detailKey);
                    if (CollectionUtils.isNotEmpty(plpUrlList)) {
                        String plpUrl = StringUtils.join(plpUrlList, TAG_SPLIT);
                        detail.setPlpUrl(plpUrl);
                    } else {
                        detail.setPlpUrl("-");
                    }
                    extractLines.add(appendData(detail, ownDomainId, null));
                }

            } else {
                for (CLRankingDetailEntity detail : dataList) {
                    String locationName = detail.getLocationId() == 0 ? "United States" : geoService.getCityName(detail.getLocationId());
                    if (StringUtils.isBlank(locationName)) {
                        locationName = "-";
                    }
                    detail.setLocationName(locationName);
                    if (GROUNDWORKS_DOMAIN_LIST.contains(ownDomainEntity.getId())) {
                        detail.setDomain(domainName);
                    }
                    extractLines.add(appendData(detail, ownDomainId, null));
                }
            }
        }

        return extractLines;
    }


    private void addHeadersForExactFile(File outFile, OwnDomainEntity ownDomainEntity) throws IOException {

        List<String> lines = new ArrayList<String>();
        StringBuffer header = new StringBuffer();
        int domainId = ownDomainEntity.getId();
        if (domainId == 7006 || domainId == 6997) {
            header.append("Date").append(SPLIT);
            header.append("Keyword").append(SPLIT);
            header.append("Ranking URL").append(SPLIT);
            header.append("True Rank").append(SPLIT);
            header.append("Web Rank").append(SPLIT);
            header.append("URL Type").append(SPLIT);
            header.append("Search Volume");
        } else if (domainId == 185) {//skip header

        } else if (domainId == 9511) {
            header.append("Date").append(SPLIT);
            header.append("Keyword").append(SPLIT);
            header.append("Search Engine").append(SPLIT);
            header.append("Ranking URL").append(SPLIT);
            header.append("True Rank").append(SPLIT);
            header.append("Web Rank").append(SPLIT);
            header.append("URL Type").append(SPLIT);
            header.append("Search Volume");
        } else if (domainId == 10619) {
            header.append("Date").append(SPLIT);
            header.append("Location").append(SPLIT);
            header.append("Keyword").append(SPLIT);
            header.append("Ranking_URL").append(SPLIT);
            header.append("True_Rank").append(SPLIT);
            header.append("Web_Rank").append(SPLIT);
            header.append("URL_Type").append(SPLIT);
            header.append("Search_Volume");
        } else if (domainId == 4609 || domainId == 765 || domainId == 9422) {
            header.append("Date").append(SPLIT);
            header.append("Keyword").append(SPLIT);
            header.append("Ranking URL").append(SPLIT);
            header.append("True Rank").append(SPLIT);
            header.append("Web Rank").append(SPLIT);
            header.append("URL Type").append(SPLIT);
            header.append("Search Volume").append(SPLIT);
            header.append("PLP Url");
        } else if (domainId == 10010) {
            header.append("Date").append(SPLIT);
            header.append("Location").append(SPLIT);
            header.append("Keyword").append(SPLIT);
            header.append("Ranking_URL").append(SPLIT);
            header.append("True_Rank").append(SPLIT);
            header.append("Web_Rank").append(SPLIT);
            header.append("URL_Type").append(SPLIT);
            header.append("Search_Volume").append(SPLIT);
            header.append("PLP Url");
        } else if (domainId == 12044) {
            header.append("Date").append(SPLIT);
            header.append("Keyword").append(SPLIT);
            header.append("Search Engine").append(SPLIT);
            header.append("Ranking URL").append(SPLIT);
            header.append("True Rank").append(SPLIT);
            header.append("Web Rank").append(SPLIT);
            header.append("URL Type").append(SPLIT);
            header.append("Search Volume").append(SPLIT);
            header.append("Title").append(SPLIT);
            header.append("Meta Description");
        } else if (GROUNDWORKS_DOMAIN_LIST.contains(domainId)) {
            header.append("Date").append(SPLIT);
            header.append("Domain").append(SPLIT);
            header.append("Geo Location").append(SPLIT);
            header.append("Keyword").append(SPLIT);
            header.append("Ranking URL").append(SPLIT);
            header.append("True Rank").append(SPLIT);
            header.append("Search Volume");
        } else {
            header.append("Date").append(SPLIT);
            header.append("Keyword").append(SPLIT);
            header.append("Ranking URL").append(SPLIT);
            header.append("True Rank").append(SPLIT);
            header.append("Web Rank").append(SPLIT);
            header.append("URL Type").append(SPLIT);
            header.append("Search Volume");
        }

        if (StringUtils.isNotBlank(header.toString())) {
            lines.add(header.toString());
            FileUtils.writeLines(outFile, lines, true);
        }

    }


    public static String appendData(CLRankingDetailEntity clRankingDetailEntity, int domainId, String tagName) {
        StringBuffer line = new StringBuffer();

        if (domainId == 185) {
            line.append(FormatUtils.formatDate(FormatUtils.toDate(clRankingDetailEntity.getRankingDate(), FormatUtils.DATE_PATTERN_2), "MM/dd/yyyy")).append(SPLIT);
            line.append(clRankingDetailEntity.getKeywordName()).append(SPLIT);
            line.append("Google").append(SPLIT);
            line.append(clRankingDetailEntity.getAvgSearchVolume()).append(SPLIT);
            line.append(clRankingDetailEntity.getTrueRank() == null ? "" : clRankingDetailEntity.getTrueRank()).append(SPLIT);
            line.append(StringUtils.isBlank(clRankingDetailEntity.getUrl()) ? "" : ExtractService.formatGoogleUrl(clRankingDetailEntity.getUrl()));

        } else if (domainId == 7006) {
            line.append(clRankingDetailEntity.getRankingDate()).append(SPLIT);
            line.append(clRankingDetailEntity.getKeywordName()).append(SPLIT);
            if (StringUtils.isBlank(clRankingDetailEntity.getUrl())) {
                line.append("-").append(SPLIT);
                line.append("999").append(SPLIT);
                line.append("999").append(SPLIT);
                line.append("-").append(SPLIT);
                line.append("999");
            } else {
                line.append(ExtractService.formatGoogleUrl(clRankingDetailEntity.getUrl())).append(SPLIT);
                if (clRankingDetailEntity.getTrueRank().equals(0)) {
                    line.append("101").append(SPLIT);
                } else {
                    line.append(clRankingDetailEntity.getTrueRank()).append(SPLIT);
                }
                if (clRankingDetailEntity.getWebRank().equals(0)) {
                    line.append("101").append(SPLIT);
                } else {
                    line.append(clRankingDetailEntity.getWebRank()).append(SPLIT);
                }
                line.append(RankTypeManager.getUrlTypeName(clRankingDetailEntity.getType())).append(SPLIT);
                line.append(clRankingDetailEntity.getAvgSearchVolume());
            }

        } else if (domainId == 6997) {

            line.append(clRankingDetailEntity.getRankingDate()).append(SPLIT);
            line.append(clRankingDetailEntity.getKeywordName()).append(SPLIT);
            line.append(StringUtils.isBlank(clRankingDetailEntity.getUrl()) ? "-" : ExtractService.formatGoogleUrl(clRankingDetailEntity.getUrl())).append(SPLIT);
            if (clRankingDetailEntity.getTrueRank().equals(-1) || clRankingDetailEntity.getTrueRank().equals(0)) {
                line.append("101").append(SPLIT);
            } else {
                line.append(clRankingDetailEntity.getTrueRank()).append(SPLIT);
            }
            if (clRankingDetailEntity.getWebRank().equals(-1) || clRankingDetailEntity.getWebRank().equals(0)) {
                line.append("101").append(SPLIT);
            } else {
                line.append(clRankingDetailEntity.getWebRank()).append(SPLIT);
            }
            line.append(RankTypeManager.getUrlTypeName(clRankingDetailEntity.getType())).append(SPLIT);
            line.append(clRankingDetailEntity.getAvgSearchVolume());

        } else if (domainId == 9511) {
            boolean isMobile = device.equalsIgnoreCase("mobile") ? true : false;
            String key = clRankingDetailEntity.getEngine() + "_" + clRankingDetailEntity.getLanguage() + "_" + isMobile;
            line.append(clRankingDetailEntity.getRankingDate()).append(SPLIT);
            line.append(clRankingDetailEntity.getKeywordName()).append(SPLIT);
            line.append(engineNameMap.get(key)).append(SPLIT);
            if (engineNameMap.get(key) == null) {
                log.error("==emptyengineNameMap:" + key + "," + new Gson().toJson(engineNameMap));
            }
            line.append(StringUtils.isBlank(clRankingDetailEntity.getUrl()) ? "-" : ExtractService.formatGoogleUrl(clRankingDetailEntity.getUrl())).append(SPLIT);
            if (clRankingDetailEntity.getTrueRank().equals(-1) || clRankingDetailEntity.getTrueRank().equals(0)) {
                line.append("101").append(SPLIT);
            } else {
                line.append(clRankingDetailEntity.getTrueRank()).append(SPLIT);
            }
            if (clRankingDetailEntity.getWebRank().equals(-1) || clRankingDetailEntity.getWebRank().equals(0)) {
                line.append("101").append(SPLIT);
            } else {
                line.append(clRankingDetailEntity.getWebRank()).append(SPLIT);
            }
            line.append(RankTypeManager.getUrlTypeName(clRankingDetailEntity.getType())).append(SPLIT);
            line.append(clRankingDetailEntity.getAvgSearchVolume());

        } else if (domainId == 10619) {
            line.append(clRankingDetailEntity.getRankingDate()).append(SPLIT);
            line.append(clRankingDetailEntity.getLocationName()).append(SPLIT);
            line.append(clRankingDetailEntity.getKeywordName()).append(SPLIT);
            line.append(StringUtils.isBlank(clRankingDetailEntity.getUrl()) ? "-" : ExtractService.formatGoogleUrl(clRankingDetailEntity.getUrl())).append(SPLIT);
            if (clRankingDetailEntity.getTrueRank().equals(-1) || clRankingDetailEntity.getTrueRank().equals(0)) {
                line.append("101").append(SPLIT);
            } else {
                line.append(clRankingDetailEntity.getTrueRank()).append(SPLIT);
            }
            if (clRankingDetailEntity.getWebRank().equals(-1) || clRankingDetailEntity.getWebRank().equals(0)) {
                line.append("101").append(SPLIT);
            } else {
                line.append(clRankingDetailEntity.getWebRank()).append(SPLIT);
            }
            line.append(RankTypeManager.getUrlTypeName(clRankingDetailEntity.getType())).append(SPLIT);

            if (extractTag) {
                line.append(clRankingDetailEntity.getAvgSearchVolume()).append(SPLIT);
                line.append(StringUtils.isBlank(tagName) ? "-" : tagName);
            } else {
                line.append(clRankingDetailEntity.getAvgSearchVolume());
            }

        } else if (domainId == 4609 || domainId == 765 || domainId == 9422) {
            line.append(clRankingDetailEntity.getRankingDate()).append(SPLIT);
            line.append(clRankingDetailEntity.getKeywordName()).append(SPLIT);
            line.append(StringUtils.isBlank(clRankingDetailEntity.getUrl()) ? "-" : ExtractService.formatGoogleUrl(clRankingDetailEntity.getUrl())).append(SPLIT);
            if (clRankingDetailEntity.getTrueRank().equals(-1) || clRankingDetailEntity.getTrueRank().equals(0)) {
                line.append("101").append(SPLIT);
            } else {
                line.append(clRankingDetailEntity.getTrueRank()).append(SPLIT);
            }
            if (clRankingDetailEntity.getWebRank().equals(-1) || clRankingDetailEntity.getWebRank().equals(0)) {
                line.append("101").append(SPLIT);
            } else {
                line.append(clRankingDetailEntity.getWebRank()).append(SPLIT);
            }
            line.append(RankTypeManager.getUrlTypeName(clRankingDetailEntity.getType())).append(SPLIT);
            line.append(clRankingDetailEntity.getAvgSearchVolume()).append(SPLIT);
            line.append(clRankingDetailEntity.getPlpUrl());
        } else if (domainId == 10010) {
            line.append(clRankingDetailEntity.getRankingDate()).append(SPLIT);
            line.append(clRankingDetailEntity.getLocationName()).append(SPLIT);
            line.append(clRankingDetailEntity.getKeywordName()).append(SPLIT);
            line.append(StringUtils.isBlank(clRankingDetailEntity.getUrl()) ? "-" : ExtractService.formatGoogleUrl(clRankingDetailEntity.getUrl())).append(SPLIT);
            if (clRankingDetailEntity.getTrueRank().equals(-1) || clRankingDetailEntity.getTrueRank().equals(0)) {
                line.append("101").append(SPLIT);
            } else {
                line.append(clRankingDetailEntity.getTrueRank()).append(SPLIT);
            }
            if (clRankingDetailEntity.getWebRank().equals(-1) || clRankingDetailEntity.getWebRank().equals(0)) {
                line.append("101").append(SPLIT);
            } else {
                line.append(clRankingDetailEntity.getWebRank()).append(SPLIT);
            }
            line.append(RankTypeManager.getUrlTypeName(clRankingDetailEntity.getType())).append(SPLIT);
            line.append(clRankingDetailEntity.getAvgSearchVolume()).append(SPLIT);
            line.append(clRankingDetailEntity.getPlpUrl());
        } else if (domainId == 12044) {
            boolean isMobile = device.equalsIgnoreCase("mobile") ? true : false;
            String key = clRankingDetailEntity.getEngine() + "_" + clRankingDetailEntity.getLanguage() + "_" + isMobile;
            line.append(clRankingDetailEntity.getRankingDate()).append(SPLIT);
            line.append(clRankingDetailEntity.getKeywordName()).append(SPLIT);
            line.append(engineNameMap.get(key)).append(SPLIT);
            if (engineNameMap.get(key) == null) {
                log.error("==emptyengineNameMap:" + key + "," + new Gson().toJson(engineNameMap));
            }
            line.append(StringUtils.isBlank(clRankingDetailEntity.getUrl()) ? "-" : ExtractService.formatGoogleUrl(clRankingDetailEntity.getUrl())).append(SPLIT);
            if (clRankingDetailEntity.getTrueRank().equals(-1) || clRankingDetailEntity.getTrueRank().equals(0)) {
                line.append("101").append(SPLIT);
            } else {
                line.append(clRankingDetailEntity.getTrueRank()).append(SPLIT);
            }
            if (clRankingDetailEntity.getWebRank().equals(-1) || clRankingDetailEntity.getWebRank().equals(0)) {
                line.append("101").append(SPLIT);
            } else {
                line.append(clRankingDetailEntity.getWebRank()).append(SPLIT);
            }
            line.append(RankTypeManager.getUrlTypeName(clRankingDetailEntity.getType())).append(SPLIT);
            line.append(clRankingDetailEntity.getAvgSearchVolume()).append(SPLIT);
            line.append(StringUtils.isBlank(clRankingDetailEntity.getLabel()) ? "-" : clRankingDetailEntity.getLabel()).append(SPLIT);
            line.append(StringUtils.isBlank(clRankingDetailEntity.getMeta()) ? "-" : clRankingDetailEntity.getMeta());
        } else if (GROUNDWORKS_DOMAIN_LIST.contains(domainId)) {
            //Date
            //Domain
            //Geo Location
            //Keyword
            //Ranking URL
            //Rank
            //Search Volume
            line.append(clRankingDetailEntity.getRankingDate()).append(SPLIT);
            line.append(clRankingDetailEntity.getDomain() + "-" + domainId).append(SPLIT);
            line.append(clRankingDetailEntity.getLocationName()).append(SPLIT);
            line.append(clRankingDetailEntity.getKeywordName()).append(SPLIT);
            line.append(StringUtils.isBlank(clRankingDetailEntity.getUrl()) ? "-" : ExtractService.formatGoogleUrl(clRankingDetailEntity.getUrl())).append(SPLIT);
            if (clRankingDetailEntity.getTrueRank().equals(-1) || clRankingDetailEntity.getTrueRank().equals(0)) {
                line.append("101").append(SPLIT);
            } else {
                line.append(clRankingDetailEntity.getTrueRank()).append(SPLIT);
            }
            line.append(clRankingDetailEntity.getAvgSearchVolume());

        } else {
            line.append(clRankingDetailEntity.getRankingDate()).append(SPLIT);
            line.append(clRankingDetailEntity.getKeywordName()).append(SPLIT);
            line.append(StringUtils.isBlank(clRankingDetailEntity.getUrl()) ? "-" : ExtractService.formatGoogleUrl(clRankingDetailEntity.getUrl())).append(SPLIT);
            if (clRankingDetailEntity.getTrueRank().equals(-1) || clRankingDetailEntity.getTrueRank().equals(0)) {
                line.append("101").append(SPLIT);
            } else {
                line.append(clRankingDetailEntity.getTrueRank()).append(SPLIT);
            }
            if (clRankingDetailEntity.getWebRank().equals(-1) || clRankingDetailEntity.getWebRank().equals(0)) {
                line.append("101").append(SPLIT);
            } else {
                line.append(clRankingDetailEntity.getWebRank()).append(SPLIT);
            }
            line.append(RankTypeManager.getUrlTypeName(clRankingDetailEntity.getType())).append(SPLIT);

            if (extractTag) {
                line.append(clRankingDetailEntity.getAvgSearchVolume()).append(SPLIT);
                line.append(StringUtils.isBlank(tagName) ? "-" : tagName);
            } else {
                line.append(clRankingDetailEntity.getAvgSearchVolume());
            }

        }

        return line.toString();
    }


    private void processByFrequency(int frequency) {

        Date nowDate = new Date();
//        nowDate = FormatUtils.toDate("2021-03-15", "yyyy-MM-dd");
        boolean isSunday = FormatUtils.isSunday(DateUtils.addDays(nowDate, -1));
        if (frequency == ExtractScriptConfigEntity.FREQUENCY_WEEKLY && !isSunday) {
            log.info("=====process weekly not sunday,exit!" + nowDate);
            return;
        }

        log.info("**************** start time :" + FormatUtils.formatDate(nowDate, "yyyy-MM-dd HH:mm:ss") + " **********");

        List<ExtractScriptInstanceEntity> instanceList = extractService.getNeedRunExtract(
                ExtractScriptConfigEntity.CATEGORY_RANK, getClass().getName(),
                ExtractScriptConfigEntity.SPECIAL_CATEGORY_DOMAIN_LEVEL + "", frequency, nowDate);

        if (CollectionUtils.isEmpty(instanceList)) {
            log.info("===no instant exit!");
            return;
        }

        for (ExtractScriptInstanceEntity instance : instanceList) {

            String pDate = FormatUtils.formatDate(processDate, FormatUtils.DATE_FORMAT_YYYYMMDD);
            String sTime = FormatUtils.formatDate(new Date(), "yyyyMMdd HH:mm:ss");

            logglyVO.setoId(String.valueOf(instance.getOwnDomainId()));
            logglyVO.setName("TopXKeywordRankingExtract");
            logglyVO.setDevice(instance.getDevice());

            logglyVO.setpDate(pDate);
            List<String> groupList = new ArrayList<>();
            groupList.add(LogglyVO.GROUP_RANKING_EXTRACT);
            logglyVO.setGroups(groupList);


            List<ExtractScriptDetailEntity> detailList = instance.getScriptDetailList();
            for (ExtractScriptDetailEntity scriptDetailEntity : detailList) {
                processForInstance(instance, scriptDetailEntity);
            }

            logglyVO.setStatus(LogglyVO.STATUS_OK);
            logglyVO.setsTime(sTime);
            logglyVO.seteTime(FormatUtils.formatDate(new Date(), "yyyyMMdd HH:mm:ss"));
            logglyVO.setRows(String.valueOf(totalCnt));
            String body = new Gson().toJson(logglyVO);
            LogglyUtils.sendLoggly(body, LogglyUtils.EXTRACT_TAG_NAME);

        }


    }

    private void processForInstance(ExtractScriptInstanceEntity instance, ExtractScriptDetailEntity scriptDetailEntity) {

        domainId = instance.getOwnDomainId();
        processDate = FormatUtils.toDate(String.valueOf(scriptDetailEntity.getTargetDate()), FormatUtils.DATE_FORMAT_YYYYMMDD);
        log.info("********************** process domain " + domainId + ",processDate: " + processDate + " *******************");

        int tagId = instance.getTagId();
        if (tagId > 0) {
            extractTag = true;
        }

        rankType = instance.getRankType();
        topX = instance.getTopXRank();
        extractScriptDetail = scriptDetailEntity;
        frequence = instance.getFrequency();
//        boolean isExpectedProcessHour = instance.getIsExpectedProcessHour();
//        extractScriptDetail.setIsExpectedProcessHour(isExpectedProcessHour);

        try {
            log.info("sleep 2s");
            Thread.sleep(2 * 1000);

            OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getById(domainId);
            if (ownDomainEntity == null) {
                log.error(" domain not exist : " + domainId);
                return;
            }
            int frequency = 1;//daily
            if (ownDomainEntity.getKeywordRankFrequency() != null && ownDomainEntity.getKeywordRankFrequency() == OwnDomainEntity.KEYWORD_RANK_FREQUENCY_WEEKLY) {
                processDate = FormatUtils.getLastSundayForWeeklyDomainExtract(processDate);
                System.out.println("====weekly domain processDate:" + processDate);
                frequency = 7;//weekly
            }


            engineId = scKeywordRankManager.getSearchEngineId(ownDomainEntity);
            languageId = scKeywordRankManager.getSearchLanguageId(ownDomainEntity);
            rankDate = FormatUtils.formatDateToYyyyMmDd(processDate);


            extractScriptDetail.setStatus(ExtractScriptDetailEntity.STATUS_PROCESSING);
            extractScriptDetail.setStartedTime(new Date());

            startTime = System.currentTimeMillis();

            extractService.updateForStart(extractScriptDetail);

            String instanceDevice = instance.getDevice();
            if (instanceDevice.equals("0")) {//todo write desktop and mobile into ine file
//                device = "desktop";
//                processForDevice(ownDomainEntity);
//                device = "mobile";
//                processForDevice(ownDomainEntity);

            } else if (instanceDevice.equals("d")) {
                device = "desktop";
                processForDevice(ownDomainEntity);

            } else if (instanceDevice.equals("m")) {
                device = "mobile";
                processForDevice(ownDomainEntity);
            }

        } catch (Exception e) {
            e.printStackTrace();
            String fatalError = "001 exception";
            extractService.updateDetailAndSendEmail(extractScriptDetail, ExtractScriptDetailEntity.STATUS_FAILURE,
                    fatalError, startTime, domainId, device, false, processDate);


            logglyVO.setStatus(LogglyVO.STATUS_NG);
            String body = new Gson().toJson(logglyVO);
            LogglyUtils.sendLoggly(body, LogglyUtils.EXTRACT_TAG_NAME);

        }

    }

    private void processForDevice(OwnDomainEntity ownDomainEntity) {

        int domainId = ownDomainEntity.getId();
        log.info("======process device:" + device);
        if (frequence == RankQcStateEntity.FREQUENCE_DAILY && !ignoreQC) {
            boolean isPassed = extractService.isRankQcPass(rankDate, domainId, engineId, languageId, device, rankType, frequence);
//            isPassed = true;// TODO: 2020/7/21 test
            if (!isPassed) {
                log.error("===not pass rank qc!!" + topX);
                extractService.updateDetailAndSendEmail(extractScriptDetail, ExtractScriptDetailEntity.SKIP_TO_RUN,
                        null, startTime, domainId, device, false, processDate);
                return;
            }
        }

        if (EXTRACT_SECONDARY_DOMAIN_LIST.contains(domainId)) {
            List<DomainSearchEngineRelEntity> domainSearchEngineRelEntityList = scKeywordRankManager.getDomainSearchEngineRels(ownDomainEntity);
            if (CollectionUtils.isNotEmpty(domainSearchEngineRelEntityList)) {
                for (DomainSearchEngineRelEntity domainSearchEngineRelEntity : domainSearchEngineRelEntityList) {
                    int searchEngineId = domainSearchEngineRelEntity.getRankcheckSearchEngineId();
                    int searchLanguageId = domainSearchEngineRelEntity.getRankcheckSearchLanguageid();
                    String secondaryDevice = domainSearchEngineRelEntity.getDevice().equalsIgnoreCase("d") ? "desktop" : "mobile";
                    if (secondaryDevice.equalsIgnoreCase(device)) {
                        log.info("===secondary not same device: " + device + ",secondaryIsMobile:" + secondaryDevice);
                        continue;
                    }
                    boolean isPassed = extractService.isRankQcPass(rankDate, domainId, searchEngineId, searchLanguageId, device, rankType, frequence);
                    if (!isPassed) {
                        log.error("===secondary not pass rank qc,engineId:" + searchEngineId + ",languageId:" + searchLanguageId);
                        extractService.updateDetailAndSendEmail(extractScriptDetail, ExtractScriptDetailEntity.SKIP_TO_RUN,
                                null, startTime, domainId, device, false, processDate);
                        return;
                    }
                }
            }
        }

        processExtract(ownDomainEntity);

    }

    public static void main(String[] args) {

//        if (args != null && args.length > 0) {
//            ignoreQC = Boolean.parseBoolean(args[0]);
//        }

//        TopXKeywordRankingExtract exportTopXRankData = new TopXKeywordRankingExtract();
//        exportTopXRankData.processByFrequency(ExtractScriptConfigEntity.FREQUENCY_DAILY);
//        exportTopXRankData.processByFrequency(ExtractScriptConfigEntity.FREQUENCY_WEEKLY);


        if (args != null && args.length > 0) {

            if (args[0].contains(",")) {
                domainIdList = args[0].split(",");
            } else {
                domainId = Integer.parseInt(args[0]);
            }

        }

        topX = Integer.parseInt(args[1]);
        device = args[2];

        TopXKeywordRankingExtractV2 exportTopXRankData = new TopXKeywordRankingExtractV2();

        if (domainIdList != null && domainIdList.length > 0) {

            for (String processingDomainId : domainIdList) {

                if (args.length >= 4 && StringUtils.containsIgnoreCase(args[3], ",")) {
                    Date sDate = FormatUtils.toDate(args[3].split(",")[0], FormatUtils.DATE_PATTERN_2);
                    Date eDate = FormatUtils.toDate(args[3].split(",")[1], FormatUtils.DATE_PATTERN_2);
                    while (sDate.compareTo(eDate) <= 0) {
                        processDate = sDate;
                        exportTopXRankData.processForDomain(Integer.parseInt(processingDomainId));
                        sDate = DateUtils.addDays(sDate, 1);
                    }

                } else if (args.length >= 4 && !StringUtils.containsIgnoreCase(args[3], ",")) {
                    processDate = FormatUtils.toDate(args[3], FormatUtils.DATE_PATTERN_2);
                    exportTopXRankData.processForDomain(Integer.parseInt(processingDomainId));
                } else {
                    processDate = DateUtils.addDays(new Date(), -1);
                    exportTopXRankData.processForDomain(Integer.parseInt(processingDomainId));
                }
            }

        } else {

            if (args.length >= 4 && StringUtils.containsIgnoreCase(args[3], ",")) {
                Date sDate = FormatUtils.toDate(args[3].split(",")[0], FormatUtils.DATE_PATTERN_2);
                Date eDate = FormatUtils.toDate(args[3].split(",")[1], FormatUtils.DATE_PATTERN_2);
                while (sDate.compareTo(eDate) <= 0) {
                    processDate = sDate;
                    exportTopXRankData.processForDomain(domainId);
                    sDate = DateUtils.addDays(sDate, 1);
                }

            } else if (args.length >= 4 && !StringUtils.containsIgnoreCase(args[3], ",")) {
                processDate = FormatUtils.toDate(args[3], FormatUtils.DATE_PATTERN_2);
                exportTopXRankData.processForDomain(domainId);
            } else {
                processDate = DateUtils.addDays(new Date(), -1);
                exportTopXRankData.processForDomain(domainId);
            }

        }

    }

}
