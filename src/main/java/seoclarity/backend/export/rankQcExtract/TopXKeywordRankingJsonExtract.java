package seoclarity.backend.export.rankQcExtract;

import com.google.gson.Gson;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.beans.BeanUtils;
import seoclarity.backend.dao.actonia.*;
import seoclarity.backend.dao.clickhouse.ClDailyRankingEntityDao;
import seoclarity.backend.dao.clickhouse.monthlyranking.MonthlyRankingDao;
import seoclarity.backend.entity.EngineCountryLanguageMappingEntity;
import seoclarity.backend.entity.ExtractQueryVO;
import seoclarity.backend.entity.RankIndexParamVO;
import seoclarity.backend.entity.actonia.*;
import seoclarity.backend.entity.actonia.extract.ExtractScriptConfigEntity;
import seoclarity.backend.entity.actonia.extract.ExtractScriptDetailEntity;
import seoclarity.backend.entity.actonia.extract.ExtractScriptInstanceEntity;
import seoclarity.backend.entity.clickhouse.CLRankingDetailEntity;
import seoclarity.backend.service.*;
import seoclarity.backend.utils.*;
import seoclarity.backend.utils.loggly.LogglyUtils;
import seoclarity.backend.utils.loggly.LogglyVO;

import java.io.File;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * https://www.wrike.com/open.htm?id=1502213335
 */
@CommonsLog
public class TopXKeywordRankingJsonExtract {

    private static final String SPLIT = "\t";
    private static final String TAG_SPLIT = "!_!";
    private static final String KEY_SPLIT = "#_#";
    private static String LOC = "/home/<USER>/";
    private static final int QUERY_TRY_COUNT = 10;
    private static final int KEYWORD_SPLIT_COUNT = 300;

    private static Date processDate;
    private static String[] domainIdList;
    private static int domainId;
    private static boolean extractTag = false;
    private static String device;
    private static boolean ignoreQC = false;
    private static LogglyVO logglyVO = new LogglyVO();
    private static int totalCnt = 0;


    private int rankDate;
    private int engineId;
    private int languageId;
    private int rankType;
    private static int topX;
    private static boolean isGeo = false;
    private static int frequence;
    private long startTime;
    private ExtractScriptDetailEntity extractScriptDetail;

    private ScKeywordRankManager scKeywordRankManager;
    private OwnDomainEntityDAO ownDomainEntityDAO;
    private OwnDomainSettingEntityDAO ownDomainSettingEntityDAO;
    private ClDailyRankingEntityDao clDailyRankingEntityDao;
    private ServerAuthenticationInfoService serverAuthenticationInfoService;
    private KeywordEntityDAO keywordEntityDAO;
    private GroupTagEntityDAO groupTagEntityDAO;
    private ExtractService extractService;
    private GeoService geoService;
    private MonthlyRankingDao monthlyRankingDao;
    private RankIndexParamEntityDAO rankIndexParamEntityDAO;
    private EngineCountryLanguageMappingEntityDAO engineCountryLanguageMappingEntityDAO;
    private TagParentChildRelDAO tagParentChildRelDAO;

    public TopXKeywordRankingJsonExtract() {
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        ownDomainSettingEntityDAO = SpringBeanFactory.getBean("ownDomainSettingEntityDAO");
        scKeywordRankManager = SpringBeanFactory.getBean("scKeywordRankManager");
        clDailyRankingEntityDao = SpringBeanFactory.getBean("clDailyRankingEntityDao");
        serverAuthenticationInfoService = SpringBeanFactory.getBean("serverAuthenticationInfoService");
        keywordEntityDAO = SpringBeanFactory.getBean("keywordEntityDAO");
        groupTagEntityDAO = SpringBeanFactory.getBean("groupTagEntityDAO");
        extractService = SpringBeanFactory.getBean("extractService");
        geoService = SpringBeanFactory.getBean("geoService");
        monthlyRankingDao = SpringBeanFactory.getBean("monthlyRankingDao");
        rankIndexParamEntityDAO = SpringBeanFactory.getBean("rankIndexParamEntityDAO");
        engineCountryLanguageMappingEntityDAO = SpringBeanFactory.getBean("engineCountryLanguageMappingEntityDAO");
        tagParentChildRelDAO = SpringBeanFactory.getBean("tagParentChildRelDAO");
    }


    //send to special remote server
    public static final Map<Integer, Integer> SPECIAL_DOMAIN_SERVER_MAP = new HashMap();

    public static final Map<Integer, String> INDEED_DOMAIN_NAME_MAP = new HashMap();//(domainId, domainName)

    static {
        SPECIAL_DOMAIN_SERVER_MAP.put(13235, ServerAuthenticationInfoEntity.SERVER_TYPE_AMAZON_S3);
        SPECIAL_DOMAIN_SERVER_MAP.put(9463, ServerAuthenticationInfoEntity.SERVER_TYPE_SFTP);
        SPECIAL_DOMAIN_SERVER_MAP.put(4739, ServerAuthenticationInfoEntity.SERVER_TYPE_FTP);
        SPECIAL_DOMAIN_SERVER_MAP.put(11983, ServerAuthenticationInfoEntity.SERVER_TYPE_SFTP_WITH_KEY_FILE);
        SPECIAL_DOMAIN_SERVER_MAP.put(13297, ServerAuthenticationInfoEntity.SERVER_TYPE_AMAZON_S3);
        SPECIAL_DOMAIN_SERVER_MAP.put(11818, ServerAuthenticationInfoEntity.SERVER_TYPE_SFTP_GCS);
        SPECIAL_DOMAIN_SERVER_MAP.put(11525, ServerAuthenticationInfoEntity.SERVER_TYPE_COMMON_PARAM_S3);
    }

    //need extract secondary domains
    public static final List<Integer> EXTRACT_SECONDARY_DOMAIN_LIST = Arrays.asList();

    private static Map<String, String> engineNameMap = new HashMap<>();

    private void processForDomain(int ownDomainId) {

        log.info("********************** process domain " + ownDomainId + ",processDate: " + processDate + " *******************");

        try {
            log.info("sleep 2s");
            Thread.sleep(2 * 1000);
        } catch (Exception e) {
            e.printStackTrace();
        }

        OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getById(ownDomainId);
        if (ownDomainEntity == null) {
            log.error(" domain not exist : " + ownDomainId);
            return;
        }

        OwnDomainSettingEntity ownDomainSettingEntity = ownDomainSettingEntityDAO.getSettingByDomainId(ownDomainId);

//        int engineId = scKeywordRankManager.getSearchEngineId(ownDomainEntity);
//        int languageId = scKeywordRankManager.getSearchLanguageId(ownDomainEntity);
//        int rankDate = FormatUtils.formatDateToYyyyMmDd(processDate);
//        boolean isPassed = extractService.isRankQcPass(rankDate,ownDomainId,engineId, languageId,device, );
//        if(!isPassed){
//            log.error("===not pass rank qc!!");
//            return;
//        }
        extractScriptDetail = new ExtractScriptDetailEntity();
        processExtract(ownDomainEntity, ownDomainSettingEntity);
    }

    private void processExtract(OwnDomainEntity ownDomainEntity, OwnDomainSettingEntity ownDomainSettingEntity) {

        int ownDomainId = ownDomainEntity.getId();
        int processingDate = FormatUtils.formatDateToYyyyMmDd(processDate);

        String fileName = getFileName(ownDomainEntity, processingDate);

        File localFolder = new File(LOC + ownDomainId);
        if (!localFolder.exists() || !localFolder.isDirectory()) {
            localFolder.mkdirs();
        }

        String localFilePath = LOC + ownDomainId + File.separator;

        String remoteFilePath = localFilePath;
        if (ownDomainId == 6997) {
            remoteFilePath = LOC + ownDomainId + File.separator + "StandardRankExtract" + File.separator;
        }if (ownDomainId == 476) {
            remoteFilePath = LOC + ownDomainId + File.separator + "top_rank_report" + File.separator;
        } else if(ownDomainId == 11818){
            remoteFilePath = "228890";//gcs common_param id
        }

        String localFileName = localFilePath + fileName;
        File localFile = new File(localFileName);
        if (localFile.exists()) {
            localFile.delete();
        }

        try {
            extractScriptDetail.setOutputFile(localFileName);
            processFile(localFile, ownDomainEntity, ownDomainSettingEntity, localFileName, remoteFilePath);
        } catch (Exception e) {
            e.printStackTrace();
            String fatalError = "002 error";
            extractService.updateDetailAndSendEmail(extractScriptDetail, ExtractScriptDetailEntity.STATUS_FAILURE,
                    fatalError, startTime, ownDomainId, device, false, processDate);
            return;
        }

    }

    private String getFileName(OwnDomainEntity ownDomainEntity, int processingDate) {

        String fileName = "";
        if (domainId == 7006) {
            fileName = ownDomainEntity.getDomain() + "_" + ownDomainEntity.getId() + "_" + ownDomainEntity.getSearchEngine() + "_" + processingDate + "_TOP_" + topX + "_RankReport_" + device + ".txt";
        }else if (domainId == 13557) {
            fileName = "ticketmaster_googlemobile_" + processingDate + "_geolocationextract.json";
        }else if (domainId == 476) {
            fileName = ownDomainEntity.getId() + "_" + processingDate + "_TOP_" + topX + "_RankReport_" + device + ".json";
        }
        else {
            fileName = ownDomainEntity.getId() + "_" + processingDate + "_TOP_" + topX + "_RankReport_" + device + ".txt";
        }

        return fileName;
    }

    private void processFile(File localFile, OwnDomainEntity ownDomainEntity, OwnDomainSettingEntity ownDomainSettingEntity, String localFilePath, String remoteFilePath) throws Exception {

        int ownDomainId = ownDomainEntity.getId();
//        addHeadersForExactFile(localFile, ownDomainEntity);

        List<String> dataList = new ArrayList<>();
        int retryCount = 1;
        while (true) {
            try {
                Thread.sleep(1000);
                dataList = getDataFromDB(ownDomainEntity, ownDomainSettingEntity);
                break;
            } catch (Exception e) {

                if (retryCount >= QUERY_TRY_COUNT) {
                    System.out.println("====error extract oid : " + ownDomainId);
                    System.out.println("====RETRY OVER " + QUERY_TRY_COUNT + "TIMES error :" + ownDomainId);

                    String fatalError = "002 error";
                    extractService.updateDetailAndSendEmail(extractScriptDetail, ExtractScriptDetailEntity.STATUS_FAILURE,
                            fatalError, startTime, ownDomainId, device, false, processDate);
                    return;
                }

                e.printStackTrace();
                System.out.println("====extract error oid:" + ownDomainId + ", sleep " + (1000 * 60 * retryCount));
                try {
                    Thread.sleep(1000 * 60 * retryCount);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
                retryCount++;
            }
        }

        if(CollectionUtils.isEmpty(dataList)){
            log.info("============dataListEmpty");
            return;
        }

        FileUtils.writeLines(localFile, dataList, true);
        totalCnt = totalCnt + dataList.size();
        extractScriptDetail.setOutputDataCount(dataList.size());
        extractScriptDetail.setOutputFileSizeKB(seoclarity.backend.utils.FileUtils.GetFileKBSize(localFile));

        int serverType = ServerAuthenticationInfoEntity.SERVER_TYPE_FTP;
        if (SPECIAL_DOMAIN_SERVER_MAP.get(ownDomainId) != null) {
            serverType = SPECIAL_DOMAIN_SERVER_MAP.get(ownDomainId);
        }
        try {
            if(serverType == ServerAuthenticationInfoEntity.SERVER_TYPE_AMAZON_S3 || serverType == ServerAuthenticationInfoEntity.SERVER_TYPE_COMMON_PARAM_S3){//for s3 path no folder
                remoteFilePath = null;
            }
            if(ownDomainId == 13297){//https://www.wrike.com/open.htm?id=1590468861
                remoteFilePath = "extracts/";
            }

            serverAuthenticationInfoService.copyFileToRemoteServerNew(serverType, ownDomainId, localFilePath, remoteFilePath, extractScriptDetail);

            extractScriptDetail.setStatus(ExtractScriptDetailEntity.STATUS_SUCCESS);
            long elapsedSeconds = (System.currentTimeMillis() - startTime) / 1000;
            extractScriptDetail.setEndedTime(new Date());
            extractScriptDetail.setElapsedSeconds((int) elapsedSeconds);
            log.info("===update for success id:" + extractScriptDetail.getId());
            extractService.updateForSuccess(extractScriptDetail);

        } catch (Exception e) {
            e.printStackTrace();
            String fatalError = "send to remote server error!!";
            extractService.updateDetailAndSendEmail(extractScriptDetail, ExtractScriptDetailEntity.STATUS_FAILURE,
                    fatalError, startTime, domainId, device, false, processDate);
        }

    }

    private List<String> getDataFromDB(OwnDomainEntity ownDomainEntity, OwnDomainSettingEntity ownDomainSettingEntity) throws Exception {

        List<String> extractLines = new ArrayList<String>();

        Integer enableCustomSearchVolume = ownDomainSettingEntity.getEnableCustomSearchVolume();

        int ownDomainId = ownDomainEntity.getId();
        int engineId = scKeywordRankManager.getSearchEngineId(ownDomainEntity);
        int languageId = scKeywordRankManager.getSearchLanguageId(ownDomainEntity);
        String domainName = ownDomainEntity.getDomain();
        boolean isBroadMatch = ownDomainEntity.isBroadMatch();
        String rankingDate = FormatUtils.formatDate(processDate, FormatUtils.DATE_PATTERN_2);

        String domainReverse = StringUtils.reverseDelimited(domainName, '.');
        String rootDomainReverse = CommonUtils.getReversedRootDomain(domainName);

        LocalDate localDate = LocalDate.parse(rankingDate);
        int monthDayCount = localDate.lengthOfMonth();
        List<RankIndexParamEntity> paramList = rankIndexParamEntityDAO.getRankIndexParams(domainId, 0);
        RankIndexParamVO rankIndexParamVO = new RankIndexParamVO(paramList);
        List<String> ctrList = rankIndexParamVO.getParamList();

        String sumCtr = rankIndexParamVO.getTotalIndex(ctrList);
        log.error("=====totalCtrIndex:" + sumCtr);
        if (sumCtr == null) {
            log.error("=====ctr index null");
        }

        List<CLRankingDetailEntity> infoDataList = new ArrayList<>();
        boolean isMobile = device.equalsIgnoreCase("mobile") ? true : false;
        String extractKey = engineId + "_" + languageId + "_" + isMobile;
        int locationId = 0;

        ExtractQueryVO extractQueryVO = new ExtractQueryVO();
        extractQueryVO.setDomainId(ownDomainId);
        extractQueryVO.setEngineId(engineId);
        extractQueryVO.setLanguageId(languageId);
        extractQueryVO.setDevice(device);
        extractQueryVO.setRankDate(rankingDate);
        extractQueryVO.setRank(topX);
        extractQueryVO.setLocationId(locationId);
        extractQueryVO.setEnableCustomSearchVolume(enableCustomSearchVolume);
        extractQueryVO.setEnabledDiffSes(ownDomainSettingEntity.differentVsKeywordSetEnabled());

        //estd queryVO
        ExtractQueryVO estdQueryVO = new ExtractQueryVO();
        BeanUtils.copyProperties(extractQueryVO, estdQueryVO);
        estdQueryVO.setIsBroadMatch(isBroadMatch);
        estdQueryVO.setRootDomainReverse(rootDomainReverse);
        estdQueryVO.setDomainReverse(domainReverse);
        estdQueryVO.setCtrList(ctrList);
        estdQueryVO.setSumCtr(sumCtr);
        estdQueryVO.setMonthDayCount(monthDayCount);

        List<GroupTagEntity> tagList = groupTagEntityDAO.getTagEntityByType(domainId, GroupTagEntity.TAG_TYPE_KEYWORD);
        Map<Integer, String> tagMap = constructTagMap(tagList);
        System.out.println("======extractAllKW OID:" + domainId + " tagCnt:" + tagList.size() + "=>" + tagMap.size());


        if (ownDomainId == 13557) {
            extractQueryVO.setTagNames("Venue Analysis Type:Comp");
            extractQueryVO.setExtractGeo(true);
        }
//        if (ownDomainId == 13235) {
//            extractQueryVO.setIsBroadMatch(isBroadMatch);
//            extractQueryVO.setRootDomainReverse(rootDomainReverse);
//            extractQueryVO.setDomainReverse(domainReverse);
//            extractQueryVO.setIsHighest(true);
//            dataList = clDailyRankingEntityDao.exportTopXKeywordJsonWithMultFilter(extractQueryVO);
//
//        }
//        else {
        infoDataList = clDailyRankingEntityDao.exportTopXKeywordJsonWithMultFilter(extractQueryVO);
//        }

        log.info("===primary dataList size: " + infoDataList.size());

        if(CollectionUtils.isNotEmpty(infoDataList)){

            Map<String, CLRankingDetailEntity> splitEstdRankKwMap = new HashMap<>();
            Map<String, List<CLRankingDetailEntity>> splitDetailRankKwMap = new HashMap<>();
            Map<String, List<CLRankingDetailEntity>> splitSubRankKwMap = new HashMap<>();


            List<Long> keywordRankCheckIdList = infoDataList.stream()
                    .map(CLRankingDetailEntity::getKeywordRankcheckId)
                    .distinct()
                    .collect(Collectors.toList());
            List<List<Long>> splitList = CollectionSplitUtils.splitCollectionBySize(keywordRankCheckIdList, KEYWORD_SPLIT_COUNT);
            for(List<Long> kwRankCheckIdSet: splitList){

                //estd sov som
                List<CLRankingDetailEntity> estdRankList = clDailyRankingEntityDao.getEstdInfoWithMultFilter(estdQueryVO, kwRankCheckIdSet);
                splitEstdRankKwMap.putAll(estdRankList.stream()
                        .collect(Collectors.toMap(
                                entity -> entity.getKeywordRankcheckId() + "!_!" + entity.getLocationId(),
                                entity -> entity,
                                (entity1, entity2) -> entity1
                        ))
                );

                //get detailRankMap
                List<CLRankingDetailEntity> detailRankList = clDailyRankingEntityDao.exportDetailJsonWithMultFilter(extractQueryVO, kwRankCheckIdSet);
                splitDetailRankKwMap.putAll(detailRankList.stream()
                        .collect(Collectors.groupingBy(
                                entity -> entity.getKeywordRankcheckId() + "!_!" + entity.getLocationId()
                        ))
                );

                //get subRankMap
                List<CLRankingDetailEntity> subRankList = clDailyRankingEntityDao.exportSunRankJsonWithMultFilter(extractQueryVO, kwRankCheckIdSet);
                splitSubRankKwMap.putAll(subRankList.stream()
                        .collect(Collectors.groupingBy(
                                entity -> entity.getKeywordRankcheckId() + "!_!" + entity.getLocationId()
                        ))
                );
            }
            log.info("==========splitDetailRankKwMap:" + splitDetailRankKwMap.size() + " ,splitSubRankKwMapSIZE:" + splitSubRankKwMap.size());


            EngineCountryLanguageMappingEntity engineCountryLanguageMapping = engineCountryLanguageMappingEntityDAO.getByEngineIdLanguageId(engineId, languageId);
            String countryName = engineCountryLanguageMapping.getCountryQueryName();
            String engineName = engineCountryLanguageMapping.getEngineDisplayName();
            String languageName = engineCountryLanguageMapping.getLanguageQueryName();


            for (CLRankingDetailEntity info : infoDataList) {
                info.setSearchEngine(engineName);
                info.setLanguageName(languageName);
                info.setCountry(countryName);
                info.setDevice(device);
                log.info("============tags:" + info.getTagList());
                String locationName =  info.getLocationId() == 0 ? "National" : geoService.getCityName(info.getLocationId());
                if (StringUtils.isBlank(locationName)) {
                    locationName = "-";
                }
                info.setLocationName(locationName);

                long kwRankCheckId = info.getKeywordRankcheckId();
//            int kwTrueRank = detail.getTrueRank();
                int kwLocationId = info.getLocationId();
                String kwMapKey = kwRankCheckId + TAG_SPLIT + kwLocationId;

                //estd
                CLRankingDetailEntity estdRank = splitEstdRankKwMap.get(kwMapKey);
                if(estdRank == null){
                    log.info("==== null estd:" + kwMapKey);
                }else {
                    info.setEstdTraffic(estdRank.getEstdTraffic());
                    info.setShareOfVoice(estdRank.getShareOfVoice());
                    info.setShareOfMarket(estdRank.getShareOfMarket());
                }

                List<CLRankingDetailEntity> detailRankList = splitDetailRankKwMap.get(kwMapKey);
                List<CLRankingDetailEntity> subRankList = splitSubRankKwMap.get(kwMapKey);

                extractLines.add(appendData(info, detailRankList, subRankList, tagMap));
            }

        }

        if (EXTRACT_SECONDARY_DOMAIN_LIST.contains(ownDomainId)) {
            List<DomainSearchEngineRelEntity> domainSearchEngineRelEntityList = scKeywordRankManager.getDomainSearchEngineRels(ownDomainEntity);
            if (CollectionUtils.isNotEmpty(domainSearchEngineRelEntityList)) {
                for (DomainSearchEngineRelEntity domainSearchEngineRelEntity : domainSearchEngineRelEntityList) {
                    engineId = domainSearchEngineRelEntity.getRankcheckSearchEngineId();
                    languageId = domainSearchEngineRelEntity.getRankcheckSearchLanguageid();
                    boolean secondaryIsMobile = domainSearchEngineRelEntity.getDevice().equalsIgnoreCase("d") ? false : true;
                    if (secondaryIsMobile != isMobile) {
                        log.info("===secondary not same device isMobile: " + isMobile + ",secondaryIsMobile:" + secondaryIsMobile);
                        continue;
                    }
                    String secondaryExtractKey = engineId + "_" + languageId + "_" + secondaryIsMobile;
                    if (secondaryExtractKey.equalsIgnoreCase(extractKey)) {
                        log.info("===already extract engine skip: " + secondaryExtractKey + ",extractKey:" + extractKey);
                        continue;
                    }
                    EngineCountryLanguageMappingEntity secEngineCountryLanguageMapping = engineCountryLanguageMappingEntityDAO.getByEngineIdLanguageId(engineId, languageId);
                    String secCountryName = secEngineCountryLanguageMapping.getCountryQueryName();
                    String secEngineName = secEngineCountryLanguageMapping.getEngineDisplayName();
                    String secLanguageName = secEngineCountryLanguageMapping.getLanguageQueryName();

                    log.info("===process secondary engine :" + engineId + ",languageId: " + languageId + ",isMobile:" + secondaryIsMobile + ",domain:" + ownDomainId);

                    List<CLRankingDetailEntity> secondaryDataList = new ArrayList<>();
                    secondaryDataList = clDailyRankingEntityDao.exportTopXKeywordJsonWithMultFilter(extractQueryVO);
                    log.info("===secondaryDataList size:" + secondaryDataList.size());
//                    dataList.addAll(secondaryDataList);

                    if(CollectionUtils.isNotEmpty(secondaryDataList)){
                        //get subRankMap
                        Map<String, List<CLRankingDetailEntity>> secSplitEstdRankKwMap = new HashMap<>();
                        Map<String, List<CLRankingDetailEntity>> secSplitDetailRankKwMap = new HashMap<>();
                        Map<String, List<CLRankingDetailEntity>> secSplitSubRankKwMap = new HashMap<>();
                        List<Long> secKeywordRankCheckIdList = secondaryDataList.stream()
                                .map(CLRankingDetailEntity::getKeywordRankcheckId)
                                .distinct()
                                .collect(Collectors.toList());
                        List<List<Long>> secSplitList = CollectionSplitUtils.splitCollectionBySize(secKeywordRankCheckIdList, KEYWORD_SPLIT_COUNT);
                        for(List<Long> secKwRankCheckIdSet: secSplitList){

                            //estd sov som
                            List<CLRankingDetailEntity> secEstdRankList = clDailyRankingEntityDao.getEstdInfoWithMultFilter(extractQueryVO, secKwRankCheckIdSet);
                            secSplitEstdRankKwMap.putAll(secEstdRankList.stream()
                                    .collect(Collectors.groupingBy(
                                            entity -> entity.getKeywordRankcheckId() + "!_!" + entity.getLocationId()
                                    ))
                            );

                            List<CLRankingDetailEntity> secDetailRankList = clDailyRankingEntityDao.exportDetailJsonWithMultFilter(extractQueryVO, secKwRankCheckIdSet);
                            secSplitDetailRankKwMap.putAll(secDetailRankList.stream()
                                    .collect(Collectors.groupingBy(
                                            entity -> entity.getKeywordRankcheckId() + "!_!" + entity.getLocationId()
                                    ))
                            );

                            List<CLRankingDetailEntity> secSubRankList = clDailyRankingEntityDao.exportSunRankJsonWithMultFilter(extractQueryVO, secKwRankCheckIdSet);
                            secSplitSubRankKwMap.putAll(secSubRankList.stream()
                                    .collect(Collectors.groupingBy(
                                            entity -> entity.getKeywordRankcheckId() + "!_!" + entity.getLocationId() + "!_!" + entity.getTrueRank()
                                    ))
                            );
                        }

                        for (CLRankingDetailEntity detail : secondaryDataList) {
                            detail.setSearchEngine(secCountryName);
                            detail.setLanguageName(secEngineName);
                            detail.setCountry(secLanguageName);
                            detail.setDevice(device);

                            String locationName =  detail.getLocationId() == 0 ? "National" : geoService.getCityName(detail.getLocationId());
                            if (StringUtils.isBlank(locationName)) {
                                locationName = "-";
                            }
                            detail.setLocationName(locationName);

                            long kwRankCheckId = detail.getKeywordRankcheckId();
//                            int kwTrueRank = detail.getTrueRank();
                            int kwLocationId = detail.getLocationId();
                            String kwMapKey = kwRankCheckId + TAG_SPLIT + kwLocationId;

                            List<CLRankingDetailEntity> secDetailRankList = secSplitDetailRankKwMap.get(kwMapKey);
                            List<CLRankingDetailEntity> secSubRankList = secSplitSubRankKwMap.get(kwMapKey);

                            extractLines.add(appendData(detail, secDetailRankList, secSubRankList, tagMap));
                        }
                    }

                }
            }
        }
        return extractLines;
    }

    public String appendData(CLRankingDetailEntity info, List<CLRankingDetailEntity> detailRankList, List<CLRankingDetailEntity> subRankList, Map<Integer, String> tagMap) {
        StringBuffer line = new StringBuffer();
        line.append(extractService.setExtractJsonResult(info, detailRankList, subRankList, tagMap)).append(SPLIT);
        return line.toString();
    }


    private void processByFrequency(int frequency) {
        Date nowDate = new Date();
//        nowDate = FormatUtils.toDate("2024-03-11", "yyyy-MM-dd");
        boolean isSunday = FormatUtils.isSunday(DateUtils.addDays(nowDate, -1));
        if (frequency == ExtractScriptConfigEntity.FREQUENCY_WEEKLY && !isSunday) {
            log.info("=====process weekly not sunday,exit!" + nowDate);
            return;
        }

        log.info("**************** start time :" + FormatUtils.formatDate(nowDate, "yyyy-MM-dd HH:mm:ss") + " **********");

        List<ExtractScriptInstanceEntity> instanceList = extractService.getNeedRunExtract(
                ExtractScriptConfigEntity.CATEGORY_RANK, getClass().getName(),
                ExtractScriptConfigEntity.SPECIAL_CATEGORY_DOMAIN_LEVEL + "", frequency, nowDate);

        if (CollectionUtils.isEmpty(instanceList)) {
            log.info("===no instant exit!");
            return;
        }

        for (ExtractScriptInstanceEntity instance : instanceList) {
            String pDate = FormatUtils.formatDate(processDate, FormatUtils.DATE_FORMAT_YYYYMMDD);
            String sTime = FormatUtils.formatDate(new Date(), "yyyyMMdd HH:mm:ss");
            log.info("====processDate:" + processDate + ",pDate:" + pDate + ",sTime:" + sTime);

            logglyVO.setoId(String.valueOf(instance.getOwnDomainId()));
            logglyVO.setName("TopXKeywordRankingJsonExtract");
            logglyVO.setDevice(instance.getDevice());

            logglyVO.setpDate(pDate);
            List<String> groupList = new ArrayList<>();
            groupList.add(LogglyVO.GROUP_RANKING_EXTRACT);
            logglyVO.setGroups(groupList);

            List<ExtractScriptDetailEntity> detailList = instance.getScriptDetailList();
            for (ExtractScriptDetailEntity scriptDetailEntity : detailList) {
                processForInstance(instance, scriptDetailEntity);
            }

            logglyVO.setStatus(LogglyVO.STATUS_OK);
            logglyVO.setsTime(sTime);
            logglyVO.seteTime(FormatUtils.formatDate(new Date(), "yyyyMMdd HH:mm:ss"));
            logglyVO.setRows(String.valueOf(totalCnt));
            String body = new Gson().toJson(logglyVO);
            LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);
        }
    }

    private void processForInstance(ExtractScriptInstanceEntity instance, ExtractScriptDetailEntity scriptDetailEntity) {

        domainId = instance.getOwnDomainId();
        processDate = FormatUtils.toDate(String.valueOf(scriptDetailEntity.getTargetDate()), FormatUtils.DATE_FORMAT_YYYYMMDD);
        log.info("********************** process domain " + domainId + ",processDate: " + processDate + " *******************");

        int tagId = instance.getTagId();
        if (tagId > 0) {
            extractTag = true;
        }else {
            extractTag = false;
        }

        rankType = instance.getRankType();
        topX = instance.getTopXRank();
        extractScriptDetail = scriptDetailEntity;
        frequence = instance.getFrequency();
//        boolean isExpectedProcessHour = instance.getIsExpectedProcessHour();
//        extractScriptDetail.setIsExpectedProcessHour(isExpectedProcessHour);

        try {
            log.info("sleep 2s");
            Thread.sleep(2 * 1000);

            OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getById(domainId);
            if (ownDomainEntity == null) {
                log.error(" domain not exist : " + domainId);
                return;
            }
            if (ownDomainEntity.getKeywordRankFrequency() != null && ownDomainEntity.getKeywordRankFrequency() == OwnDomainEntity.KEYWORD_RANK_FREQUENCY_WEEKLY
                    || frequence == ExtractScriptInstanceEntity.FREQUENCY_WEEKLY) {
                processDate = FormatUtils.getLastSundayForWeeklyDomainExtract(processDate);
                System.out.println("====weekly domain processDate:" + processDate);
            }

            OwnDomainSettingEntity ownDomainSettingEntity = ownDomainSettingEntityDAO.getSettingByDomainId(domainId);

            engineId = scKeywordRankManager.getSearchEngineId(ownDomainEntity);
            languageId = scKeywordRankManager.getSearchLanguageId(ownDomainEntity);
            rankDate = FormatUtils.formatDateToYyyyMmDd(processDate);


            extractScriptDetail.setStatus(ExtractScriptDetailEntity.STATUS_PROCESSING);
            extractScriptDetail.setStartedTime(new Date());

            startTime = System.currentTimeMillis();

            extractService.updateForStart(extractScriptDetail);

            String instanceDevice = instance.getDevice();
            if (instanceDevice.equals("0")) {//todo write desktop and mobile into ine file
//                device = "desktop";
//                processForDevice(ownDomainEntity);
//                device = "mobile";
//                processForDevice(ownDomainEntity);

            } else if (instanceDevice.equals("d")) {
//                if(rankDate == 20230802){
//                    log.info("=====skip 20230802 desktop.");//todo rank data error, waiting for fix
//                    return;
//                }
                device = "desktop";
                processForDevice(ownDomainEntity, ownDomainSettingEntity);

            } else if (instanceDevice.equals("m")) {
                device = "mobile";
                processForDevice(ownDomainEntity, ownDomainSettingEntity);
            }

        } catch (Exception e) {
            e.printStackTrace();
            String fatalError = "001 exception";
            extractService.updateDetailAndSendEmail(extractScriptDetail, ExtractScriptDetailEntity.STATUS_FAILURE,
                    fatalError, startTime, domainId, device, false, processDate);


            logglyVO.setStatus(LogglyVO.STATUS_NG);
            String body = new Gson().toJson(logglyVO);
            LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);

        }

    }

    private void processForDevice(OwnDomainEntity ownDomainEntity, OwnDomainSettingEntity ownDomainSettingEntity) {

        int domainId = ownDomainEntity.getId();
        log.info("======process device:" + device);
        if (frequence == RankQcStateEntity.FREQUENCE_DAILY && !ignoreQC) {
            boolean isPassed = extractService.isRankQcPass(rankDate, domainId, engineId, languageId, device, rankType, frequence);
//            isPassed = true;// TODO: 2020/7/21 test
            if (!isPassed) {
                log.error("===not pass rank qc!!" + topX);
                extractService.updateDetailAndSendEmail(extractScriptDetail, ExtractScriptDetailEntity.SKIP_TO_RUN,
                        null, startTime, domainId, device, false, processDate);
                return;
            }
        }

        if (EXTRACT_SECONDARY_DOMAIN_LIST.contains(domainId)) {
            List<DomainSearchEngineRelEntity> domainSearchEngineRelEntityList = scKeywordRankManager.getDomainSearchEngineRels(ownDomainEntity);
            if (CollectionUtils.isNotEmpty(domainSearchEngineRelEntityList)) {
                for (DomainSearchEngineRelEntity domainSearchEngineRelEntity : domainSearchEngineRelEntityList) {
                    int searchEngineId = domainSearchEngineRelEntity.getRankcheckSearchEngineId();
                    int searchLanguageId = domainSearchEngineRelEntity.getRankcheckSearchLanguageid();
                    String secondaryDevice = domainSearchEngineRelEntity.getDevice().equalsIgnoreCase("d") ? "desktop" : "mobile";
                    if (secondaryDevice.equalsIgnoreCase(device)) {
                        log.info("===secondary not same device: " + device + ",secondaryIsMobile:" + secondaryDevice);
                        continue;
                    }
                    boolean isPassed = extractService.isRankQcPass(rankDate, domainId, searchEngineId, searchLanguageId, device, rankType, frequence);
                    if (!isPassed) {
                        log.error("===secondary not pass rank qc,engineId:" + searchEngineId + ",languageId:" + searchLanguageId);
                        extractService.updateDetailAndSendEmail(extractScriptDetail, ExtractScriptDetailEntity.SKIP_TO_RUN,
                                null, startTime, domainId, device, false, processDate);
                        return;
                    }
                }
            }
        }

        processExtract(ownDomainEntity, ownDomainSettingEntity);
    }

    private Map<Integer, String> constructTagMap(List<GroupTagEntity> tagList) {
        Map<Integer, String> tagMap = new HashMap<Integer, String>();
        if (tagList != null && tagList.size() > 0) {
            for (GroupTagEntity tagEntity : tagList) {
                tagMap.put(tagEntity.getId(), tagEntity.getTagName());
            }
        }
        return tagMap;
    }

    public static void main(String[] args) {

        TopXKeywordRankingJsonExtract topXKeywordRankingJsonExtract = new TopXKeywordRankingJsonExtract();
        topXKeywordRankingJsonExtract.processByFrequency(ExtractScriptConfigEntity.FREQUENCY_DAILY);
        topXKeywordRankingJsonExtract.processByFrequency(ExtractScriptConfigEntity.FREQUENCY_WEEKLY);

//        if (args != null && args.length > 0) {
//
//            if (args[0].contains(",")) {
//                domainIdList = args[0].split(",");
//            } else {
//                domainId = Integer.parseInt(args[0]);
//            }
//
//        }
//
//        topX = Integer.parseInt(args[1]);
//        device = args[2];
//
//        TopXKeywordRankingJsonExtract topXKeywordRankingJsonExtract = new TopXKeywordRankingJsonExtract();
//
//        if (domainIdList != null && domainIdList.length > 0) {
//
//            for (String processingDomainId : domainIdList) {
//                domainId = Integer.parseInt(processingDomainId);
//                if (args.length >= 4 && StringUtils.containsIgnoreCase(args[3], ",")) {
//                    Date sDate = FormatUtils.toDate(args[3].split(",")[0], FormatUtils.DATE_PATTERN_2);
//                    Date eDate = FormatUtils.toDate(args[3].split(",")[1], FormatUtils.DATE_PATTERN_2);
//                    while (sDate.compareTo(eDate) <= 0) {
//                        processDate = sDate;
//                        topXKeywordRankingJsonExtract.processForDomain(Integer.parseInt(processingDomainId));
//                        sDate = DateUtils.addDays(sDate, 1);
//                    }
//
//                } else if(args.length >= 4 && !StringUtils.containsIgnoreCase(args[3], ",")){
//                    processDate = FormatUtils.toDate(args[3], FormatUtils.DATE_PATTERN_2);
//                    topXKeywordRankingJsonExtract.processForDomain(Integer.parseInt(processingDomainId));
//                } else {
//                    processDate = DateUtils.addDays(new Date(), -1);
//                    topXKeywordRankingJsonExtract.processForDomain(Integer.parseInt(processingDomainId));
//                }
//            }
//
//        } else {
//
//            if (args.length >= 4 && StringUtils.containsIgnoreCase(args[3], ",")) {
//                Date sDate = FormatUtils.toDate(args[3].split(",")[0], FormatUtils.DATE_PATTERN_2);
//                Date eDate = FormatUtils.toDate(args[3].split(",")[1], FormatUtils.DATE_PATTERN_2);
//                while (sDate.compareTo(eDate) <= 0) {
//                    processDate = sDate;
//                    topXKeywordRankingJsonExtract.processForDomain(domainId);
//                    sDate = DateUtils.addDays(sDate, 1);
//                }
//
//            } else if(args.length >= 4 && !StringUtils.containsIgnoreCase(args[3], ",")){
//                processDate = FormatUtils.toDate(args[3], FormatUtils.DATE_PATTERN_2);
//                topXKeywordRankingJsonExtract.processForDomain(domainId);
//            } else {
//                processDate = DateUtils.addDays(new Date(), -1);
//                topXKeywordRankingJsonExtract.processForDomain(domainId);
//            }
//
//        }

    }

}
