package seoclarity.backend.export;

import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3Client;
import com.amazonaws.services.s3.model.CannedAccessControlList;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.amazonaws.services.s3.model.StorageClass;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;

import com.google.gson.Gson;

import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.text.csv.CsvWriteConfig;
import cn.hutool.core.text.csv.CsvWriter;
import org.apache.commons.lang.time.DateFormatUtils;
import seoclarity.backend.dao.actonia.CommonParamDAO;
import seoclarity.backend.dao.actonia.EngineCountryLanguageMappingEntityDAO;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.actonia.UserDAO;
import seoclarity.backend.dao.actonia.adhoc.AdhocRankSvProjectRelDAO;
import seoclarity.backend.dao.actonia.adhoc.AutoAdhocRankProjectEntityDAO;
import seoclarity.backend.dao.aurora.HourlyRankJdbcDAO;
import seoclarity.backend.dao.aurora.HourlyRankTaskDAO;
import seoclarity.backend.dao.clickhouse.adhoc.AdhocInfoDao;
import seoclarity.backend.entity.*;
import seoclarity.backend.entity.actonia.CommonParamEntity;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.actonia.UserEntity;
import seoclarity.backend.entity.actonia.adhoc.AutoAdhocRankProjectEntity;
import seoclarity.backend.entity.bean.FTPServerInfoBean;
import seoclarity.backend.service.*;
import seoclarity.backend.service.RankTypeManager;
import seoclarity.backend.upload.ExtractRedVenturesKeywords;
import seoclarity.backend.utils.*;
import seoclarity.backend.utils.amazon.AwsCredentialsEnvKeyConstructor;
import seoclarity.backend.utils.loggly.LogglyUtils;
import seoclarity.backend.utils.loggly.LogglyVO;


@CommonsLog
public class AutoAdhocExtract {

    private static final String SPLIT = "\t";
    public static final String TABLE_INFO_TABLE = "adhoc_ranking_info";
    public static final String TABLE_DETAIL_TYPE = "adhoc_ranking_detail";
    public static final String TABLE_SUBRANK_TYPE = "adhoc_ranking_subrank";
    private static final String DATABASE_NAME = "adhoc_ranking";
    private static final String HOURLY_DATABASE_NAME = "hourly_adhoc_ranking";

    private static final String LOCAL_FOLDER = "/home/<USER>/";
    private static final String ADHOC_FOLDER = "/adhocExtract";
    private static final String HOURLY_ADHOC_FOLDER = "/hourlyAdhocExtract";

    private static final String CACHE_LIST_URL = "https://event.seoclarity.workers.dev/list/";
    private static final String DELETE_CACHE_URL_PREFIX = "https://event.seoclarity.workers.dev/delete/";
    private static final String CACHE_RANKING_UPLOAD_COMPLETE_KEY = "event_ranking_upload_complete_adhoc";

    private static final int PAGESIZE = 100000;

    private static Date processDate;
    private static boolean isHourly = false;
    private static final int QUERY_TRY_COUNT = 10;

    private static LogglyVO logglyVO = new LogglyVO();
    private static int totalCnt = 0;

    private AutoAdhocRankProjectEntityDAO autoAdhocRankProjectEntityDAO;
    private HourlyRankJdbcDAO hourlyRankJdbcDAO;
    private HourlyRankTaskDAO hourlyRankTaskDAO;
    //    private SeoClarityCityEntityDAO seoClarityCityEntityDAO;
    private OwnDomainEntityDAO ownDomainEntityDAO;
    private EngineCountryLanguageMappingEntityDAO mappingEntityDAO;
    private AdhocInfoDao adhocInfoDao;
    private GeoService geoService;
    private EmailSenderComponent emailSenderComponent;
    private CommonParamDAO commonParamDAO;
    private AgencyInfoManager agencyInfoManager;
    private UserDAO userDAO;
    private ZeptoMailSenderComponent zeptoMailSenderComponent;
    private CommonDataService commonDataService;
    private AdhocRankSvProjectRelDAO adhocRankSvProjectRelDAO;

    public AutoAdhocExtract() {
        autoAdhocRankProjectEntityDAO = SpringBeanFactory.getBean("autoAdhocRankProjectEntityDAO");
        hourlyRankJdbcDAO = SpringBeanFactory.getBean("hourlyRankJdbcDAO");
//        seoClarityCityEntityDAO = SpringBeanFactory.getBean("seoClarityCityEntityDAO");
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        mappingEntityDAO = SpringBeanFactory.getBean("engineCountryLanguageMappingEntityDAO");
        adhocInfoDao = SpringBeanFactory.getBean("adhocInfoDao");
        hourlyRankTaskDAO = SpringBeanFactory.getBean("hourlyRankTaskDAO");
        geoService = SpringBeanFactory.getBean("geoService");
        emailSenderComponent = SpringBeanFactory.getBean("emailSenderComponent");
        commonParamDAO = SpringBeanFactory.getBean("commonParamDAO");
        agencyInfoManager = SpringBeanFactory.getBean("agencyInfoManager");
        userDAO = SpringBeanFactory.getBean("userDAO");
        zeptoMailSenderComponent = SpringBeanFactory.getBean("zeptoMailSenderComponent");
        commonDataService = SpringBeanFactory.getBean("commonDataService");
        adhocRankSvProjectRelDAO = SpringBeanFactory.getBean("adhocRankSvProjectRelDAO");
    }

    private static String S3_BUCKET_KEY_PREFIX = "hourly/";

    private void process() {
        List<Integer> frequencyTypeList = new ArrayList<>();
        if (isHourly) {
            List<AdhocRankTask> hourlyTaskList = hourlyRankTaskDAO.getCompletedTask();
            if (!CollectionUtils.isEmpty(hourlyTaskList)) {

                List<Integer> rvIdList = commonDataService.getRVTargetDomainIdList();

                for (AdhocRankTask adhocRankTask : hourlyTaskList) {
                    AutoAdhocRankProjectEntity autoAdhocRankProjectEntity = autoAdhocRankProjectEntityDAO.getProjectById(adhocRankTask.getProjectId());
                    if (autoAdhocRankProjectEntity == null) {
                        log.info("===project not exist:" + adhocRankTask.getProjectId() + ",time2:" + System.currentTimeMillis());
                        continue;
                    }
                    int projectOwnDomainId = autoAdhocRankProjectEntity.getOwnDomainId();
//                    OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getDomainListBasedCompanyNameAndDomainId(
//                            "Red Ventures LLC", autoAdhocRankProjectEntity.getOwnDomainId());

                    try {
                        hourlyRankJdbcDAO.updateExtractStatus(adhocRankTask.getId(), AdhocRankTask.EXTRACT_RANK_STSTUS_PROCESSING);

                        if (rvIdList.contains(projectOwnDomainId)) {
                            System.out.println("==== processing Red Venture domain, OID:" + autoAdhocRankProjectEntity.getOwnDomainId());
                            String outputFile = processRedVentureDomain(adhocRankTask, autoAdhocRankProjectEntity);

                            String startDayYYYYMMDD = adhocRankTask.getStartDay() + "";
                            int startHour = adhocRankTask.getStartHour();
//                            ExtractRedVenturesKeywords.saveFileToS3ForUntagByTransferManager(startDayYYYYMMDD + startHour, outputFile, S3_BUCKET_KEY_PREFIX);
                            sendToRemoteServer(autoAdhocRankProjectEntity, new File(outputFile), true, startDayYYYYMMDD, startHour, outputFile);

                            //PAA extract
                            File outFile = processHourlyTask(adhocRankTask, true);
                            sendToRemoteServer(autoAdhocRankProjectEntity, outFile, true, startDayYYYYMMDD, startHour, outFile.getAbsolutePath());

                        } else {
                            File outFile = processHourlyTask(adhocRankTask, false);
                            sendToRemoteServer(autoAdhocRankProjectEntity, outFile, false, null, null, null);
                            outFile = processHourlyTask(adhocRankTask, true);
                            sendToRemoteServer(autoAdhocRankProjectEntity, outFile, false, null, null, null);
                        }

                        hourlyRankJdbcDAO.updateExtractStatus(adhocRankTask.getId(), AdhocRankTask.EXTRACT_RANK_STSTUS_COMPLETED_WITHOUT_ERROR);
                    } catch (Exception e) {
                        System.out.println("====error hourly task: " + adhocRankTask.getId());
                        hourlyRankJdbcDAO.updateExtractStatus(adhocRankTask.getId(), AdhocRankTask.EXTRACT_RANK_STSTUS_ERROR);
                        e.printStackTrace();
                        continue;
                    }

                }
            } else {
                System.out.println("====not task need to extract.");
            }

        } else {

            frequencyTypeList.add(AutoAdhocRankProjectEntity.FREQUENCY_TYPE_DAILY);
            frequencyTypeList.add(AutoAdhocRankProjectEntity.FREQUENCY_TYPE_WEEKLY);
            frequencyTypeList.add(AutoAdhocRankProjectEntity.FREQUENCY_TYPE_MONTHLY);

            List<AutoAdhocRankProjectEntity> projectEntityList = autoAdhocRankProjectEntityDAO.getNeedExtractRankTaskByDate(frequencyTypeList);
            if (!CollectionUtils.isEmpty(projectEntityList)) {

                //get from cache
                List<String> cacheList = new ArrayList<>();
                try {
                    cacheList = getCacheList(CACHE_RANKING_UPLOAD_COMPLETE_KEY);
                } catch (Exception e) {
                    e.printStackTrace();
                    System.out.println("=====get cache list error");
                }

                if (org.springframework.util.CollectionUtils.isEmpty(projectEntityList)) {
                    if (!CollectionUtils.isEmpty(cacheList)) {
                        for (String key : cacheList) {
                            String deleteUrl = DELETE_CACHE_URL_PREFIX + key;
                            deleteCache(deleteUrl);
                        }
                    }
                    System.out.println("===no task need to run,exit!!!");
                    return;
                }

                deleteNotExistProjectCache(projectEntityList, cacheList);

                for (AutoAdhocRankProjectEntity autoAdhocRankProjectEntity : projectEntityList) {
                    int projectId = autoAdhocRankProjectEntity.getId();
                    //https://www.wrike.com/open.htm?id=1353390634
                    List<Integer> parentProjectId = adhocRankSvProjectRelDAO.getParentProjectId(projectId);
                    if (CollectionUtils.isNotEmpty(parentProjectId) && autoAdhocRankProjectEntity.getRetrieveType() == AutoAdhocRankProjectEntity.RETRIEVE_TYPE_RANKCHECK_ONLY) {
                        log.info("=====skip child RankingProjectId:" + projectId);
                        autoAdhocRankProjectEntityDAO.updateExtractStatus(projectId, AutoAdhocRankProjectEntity.EXPORT_RANK_STSTUS_COMPLETED_WITHOUT_ERROR);
                        continue;
                    }
                    autoAdhocRankProjectEntityDAO.updateExtractStatus(projectId, AutoAdhocRankProjectEntity.EXPORT_RANK_STSTUS_PROCESSING);
                    int retryCount = 1;
                    while (true) {
                        try {
                            System.out.println("********************** for projectId:" + projectId + " *******************");
                            File outFile = processForProject(autoAdhocRankProjectEntity, null, null, false);
                            sendToRemoteServer(autoAdhocRankProjectEntity, outFile, false, null, null, null);
                            File paaOutFile = processForProject(autoAdhocRankProjectEntity, null, null, true);
                            sendToRemoteServer(autoAdhocRankProjectEntity, paaOutFile, false, null, null, null);
                            autoAdhocRankProjectEntityDAO.updateExtractStatus(projectId, AutoAdhocRankProjectEntity.EXPORT_RANK_STSTUS_COMPLETED_WITHOUT_ERROR);

                            //https://www.wrike.com/open.htm?id=856995362
                            UserEntity user = userDAO.getUser(autoAdhocRankProjectEntity.getCreateUserId());
                            sendEmail(user.getName(), user.getEmail(), outFile.getParent() + File.separator, outFile.getName(),
                                    autoAdhocRankProjectEntity.getOwnDomainId(), autoAdhocRankProjectEntity.getProjectName());
                            break;
                        } catch (Exception e) {

                            if (retryCount >= QUERY_TRY_COUNT) {
                                System.out.println("====error project : " + projectId);
                                autoAdhocRankProjectEntityDAO.updateExtractStatus(projectId, AutoAdhocRankProjectEntity.EXPORT_RANK_STSTUS_ERROR);
                                System.out.println("====RETRY OVER " + QUERY_TRY_COUNT + "TIMES error :" + projectId);
                                String message = "Failed Adhoc Extract projectId:" + projectId;
                                sendMailReport(projectId, "Failed", message);
                                break;
                            }

                            e.printStackTrace();

                            logglyVO.setStatus(LogglyVO.STATUS_NG);
                            String body = new Gson().toJson(logglyVO);
                            LogglyUtils.sendLoggly(body, LogglyUtils.EXTRACT_TAG_NAME);

                            System.out.println("====projectId error :" + projectId + ", sleep " + (1000 * 60 * retryCount));
                            try {
                                Thread.sleep(1000 * 60 * retryCount);
                            } catch (Exception ex) {
                                ex.printStackTrace();
                            }
                            retryCount++;
                        }
                    }

                }
            } else {
                System.out.println("====not task need to extract.");
            }
        }

    }

    private File processHourlyTask(AdhocRankTask adhocRankTask, boolean isPAA) {

        File outFile = null;
        int projectId = adhocRankTask.getProjectId();
        String startDay = FormatUtils.formatDate(FormatUtils.toDate(adhocRankTask.getStartDay().toString(), FormatUtils.DATE_FORMAT_YYYYMMDD), FormatUtils.DATE_PATTERN_2);
        int startHour = adhocRankTask.getStartHour();
        AutoAdhocRankProjectEntity autoAdhocRankProjectEntity = autoAdhocRankProjectEntityDAO.getProjectById(projectId);
        if (autoAdhocRankProjectEntity == null) {
            System.out.println("====project not exist:" + projectId + ",time:" + System.currentTimeMillis());
            return null;
        }

        int retryCount = 1;
        while (true) {
            try {
                System.out.println("********************** for hourly projectId:" + projectId + " *******************");
                outFile = processForProject(autoAdhocRankProjectEntity, startDay, startHour, isPAA);
                break;
            } catch (Exception e) {

                if (retryCount >= QUERY_TRY_COUNT) {
                    System.out.println("====error hourly project : " + projectId);
                    System.out.println("====RETRY OVER " + QUERY_TRY_COUNT + "TIMES error :" + projectId);
                    String message = "Failed Hourly Adhoc Extract projectId:" + projectId;
                    sendMailReport(projectId, "Failed", message);
                    break;
                }

                e.printStackTrace();
                System.out.println("====hourly projectId error :" + projectId + ", sleep " + (1000 * 60 * retryCount));
                try {
                    Thread.sleep(1000 * 60 * retryCount);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
                retryCount++;
            }
        }
        return outFile;
    }

    private File processForProject(AutoAdhocRankProjectEntity autoAdhocRankProject, String startDay, Integer startHour, boolean isPAA) throws Exception {

        String country = autoAdhocRankProject.getCountry();
        int domainId = autoAdhocRankProject.getOwnDomainId();
        int projectId = autoAdhocRankProject.getId();
        int engineId = autoAdhocRankProject.getSearchEngineId();
        int languageId = autoAdhocRankProject.getLanguageId();
        int device = autoAdhocRankProject.getDevice();
        int keywordType = autoAdhocRankProject.getKeywordType();
        Integer cityId = autoAdhocRankProject.getCityId();
        boolean isMobile = device == 1 ? false : true;
        boolean isCity = false;
        int retrieveType = autoAdhocRankProject.getRetrieveType();

        String pDate = FormatUtils.formatDate(new Date(), FormatUtils.DATE_FORMAT_YYYYMMDD);
        String stTime = FormatUtils.formatDate(new Date(), "yyyyMMdd HH:mm:ss");

        logglyVO.setoId(String.valueOf(domainId));
        logglyVO.setName("AutoAdhocExtract");
        logglyVO.setDevice(isMobile ? "m" : "d");

        logglyVO.setpDate(pDate);
        List<String> groupList = new ArrayList<>();
        groupList.add(LogglyVO.GROUP_CUSTOMIZED_EXTRACT);
        logglyVO.setGroups(groupList);

        if (keywordType == AutoAdhocRankProjectEntity.KEYWORD_TYPE_GEO
                || autoAdhocRankProject.getGeoPattern() == AutoAdhocRankProjectEntity.GEO_PATTERN_BOTH_GEO_ID_AND_KEYWORD_IN_FILE
                || cityId != null && cityId > 0) {
            isCity = true;
        }

        String extractFilePath = "";
        String fileName = "";
        if (isHourly) {
            extractFilePath = LOCAL_FOLDER + domainId + HOURLY_ADHOC_FOLDER;
            fileName = autoAdhocRankProject.getProjectName() + "_" + domainId + "_" + startDay + "_" + startHour + "_hourlyAdhoc.txt";
            if (isPAA) {
                fileName = "PAA_" + fileName;
            }
        } else {
            extractFilePath = LOCAL_FOLDER + domainId + ADHOC_FOLDER;
            fileName = autoAdhocRankProject.getProjectName() + "_" + domainId + "_" + FormatUtils.formatDate(processDate, "yyyy-MM-dd") + "_adhoc.txt";
            if (isPAA) {
                fileName = "PAA_" + fileName;
            }
        }

        File fileFolder = new File(extractFilePath);
        if (!fileFolder.exists()) {
            fileFolder.mkdirs();
        }

        File outFile = new File(extractFilePath + File.separator + fileName);
        if (outFile.exists()) {
            outFile.delete();
        }

        if (isPAA) {
            addHeadersForExactFilePAA(outFile);
        } else {
            addHeadersForExactFile(outFile, retrieveType);
        }

        System.out.println("====extract file path : " + outFile.getAbsolutePath());

        String infoTableName = getInfoTable(engineId, languageId, isMobile);
        String detailTableName = getDetailTable(engineId, languageId, isMobile);
        String subRankTableName = getSubrankTable(engineId, languageId, isMobile);

        List<CLRankingDetailEntity> clRankingDetailEntityList = new ArrayList<>();
        if (isPAA) {
            List<String> extractLines = new ArrayList<String>();
            clRankingDetailEntityList = adhocInfoDao.extractPAAForAdhoc(infoTableName, isMobile, projectId, domainId, engineId, languageId, cityId,
                    isHourly, startDay, startHour);
            log.info("===extract size:" + clRankingDetailEntityList.size());
            if (CollectionUtils.isNotEmpty(clRankingDetailEntityList)) {
                for (CLRankingDetailEntity detail : clRankingDetailEntityList) {

                    String[] questionArr = detail.getQuestions().split("!_!");
                    String[] additionalQuestionArr = detail.getAdditionalQuestions().split("@_@");
                    if (questionArr.length != additionalQuestionArr.length) {
                        log.info("===skip not match kw:" + detail.getKeywordName());
                        continue;
                    }
                    for (int i = 0; i < questionArr.length; i++) {
                        String que = questionArr[i];
                        try {
                            String[] questionDetailArr = additionalQuestionArr[i].split("!_!");
                            String link = questionDetailArr[0];
                            extractLines.add(appendDataPAA(detail.getKeywordName(), que, link));
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }

                }
                totalCnt = extractLines.size();
                FileUtils.writeLines(outFile, extractLines, true);
            }

        } else {
            int startId = 0;
            while (true) {
                log.info("===startId:" + startId);
                clRankingDetailEntityList =
                        adhocInfoDao.queryForExtract(detailTableName, subRankTableName, projectId, isHourly, isCity, country,
                                startDay, startHour, startId, PAGESIZE, retrieveType, infoTableName);
                log.info("clRankingDetailEntityList:" + clRankingDetailEntityList.size());
                if (CollectionUtils.isEmpty(clRankingDetailEntityList)) {
                    break;
                }

                List<String> extractLines = new ArrayList<String>();
                for (CLRankingDetailEntity detail : clRankingDetailEntityList) {
                    extractLines.add(appendData(detail, retrieveType));
                }
                totalCnt = extractLines.size();
                FileUtils.writeLines(outFile, extractLines, true);
                extractLines = new ArrayList<>();
                startId += PAGESIZE;

                Thread.sleep(1000 * 2);
            }

        }
        logglyVO.setStatus(LogglyVO.STATUS_OK);
        logglyVO.setsTime(stTime);
        logglyVO.seteTime(FormatUtils.formatDate(new Date(), "yyyyMMdd HH:mm:ss"));
        logglyVO.setRows(String.valueOf(totalCnt));
        String body = new Gson().toJson(logglyVO);
        LogglyUtils.sendLoggly(body, LogglyUtils.EXTRACT_TAG_NAME);

        return outFile;
    }

    private void sendToRemoteServer(AutoAdhocRankProjectEntity autoAdhocRankProject, File outFile, boolean isRvDomain, String startDayYYYYMMDD, Integer startHour, String fullFilePath) throws Exception {

        int projectId = autoAdhocRankProject.getId();
        if (outFile == null) {
            throw new RuntimeException("outFile is null,projectId:" + projectId);
        }

        int domainId = autoAdhocRankProject.getOwnDomainId();
        Long additionalExtractConfigId = autoAdhocRankProject.getAdditionalExtractConfigId();

        if (additionalExtractConfigId != null) {
            System.out.println("-----send by additionalExtractConfigId :" + additionalExtractConfigId);
            CommonParamEntity commonParamEntity = commonParamDAO.getById(domainId, additionalExtractConfigId);
            boolean isS3InfoOk = true;
            if (commonParamEntity == null) {
                System.out.println("-----not find commonParam projectId:" + projectId + ",additionalExtractConfigId:" + additionalExtractConfigId);
            }
            String paramJson = commonParamEntity.getParamJson();
            if (StringUtils.isBlank(paramJson)) {
                System.out.println("-----paramJson error :" + paramJson);
                isS3InfoOk = false;
            }
            Gson gson = new Gson();
            try {
                Map<String, String> s3Info = gson.fromJson(paramJson, Map.class);
                String accessKey = "";
                String secretKey = "";
                String s3Type = s3Info.get("s3Type");
                accessKey = s3Info.get("accessKey");
                if(StringUtils.isNotBlank(s3Type) && s3Type.equalsIgnoreCase("share")){
                    secretKey = AwsCredentialsEnvKeyConstructor.getInstance().getSQSDecryptedSecretKey();
                    log.info("===share key:" + accessKey);
                }else if(StringUtils.isNotBlank(s3Type) && s3Type.equalsIgnoreCase("credential")){
                    secretKey = ScStringEncryptor.decrypt(s3Info.get("secretKey"));
                    log.info("===credential key:" + accessKey);
                }else {//todo for new logic future

                }

                String bucketName = s3Info.get("bucket");
                String path = s3Info.get("path");
                if (path.equalsIgnoreCase("/")) {
                    path = "";
                }

                if (isS3InfoOk) {
                    AWSCredentials credentials = new BasicAWSCredentials(accessKey, secretKey);
                    AmazonS3 s3client = new AmazonS3Client(credentials);
                    String keyPath = path + outFile.getName();

                    s3client.putObject(new PutObjectRequest(bucketName, keyPath, outFile).withStorageClass(StorageClass.ReducedRedundancy)
                            .withCannedAcl(CannedAccessControlList.BucketOwnerFullControl));
                    System.out.println("S3 eekey: " + keyPath + " , file: " + outFile.getName());
                } else {
                    throw new RuntimeException("s3 info error,projectId:" + projectId);
                }

            } catch (Exception e) {
                throw e;
            }

        } else if (additionalExtractConfigId == null && isRvDomain) {

            ExtractRedVenturesKeywords.saveFileToS3ForUntagByTransferManager(startDayYYYYMMDD + startHour, fullFilePath, S3_BUCKET_KEY_PREFIX);
            FTPUtils.copyBySSH(FTPUtils.FTP_SERVER, FTPUtils.FTP_SERVER_USER, FTPUtils.FTP_SERVER_PW, outFile.getAbsolutePath(), LOCAL_FOLDER + domainId, 0, 3);

        } else {
            System.out.println("-----send to ftp");
            FTPUtils.copyBySSH(FTPUtils.FTP_SERVER, FTPUtils.FTP_SERVER_USER, FTPUtils.FTP_SERVER_PW, outFile.getAbsolutePath(), LOCAL_FOLDER + domainId, 0, 3);
        }

    }

    private void addHeadersForExactFile(File outFile, int retrieveType) throws IOException {
        List<String> lines = new ArrayList<String>();
        StringBuffer header = new StringBuffer();
        header.append("Date").append(SPLIT);
        header.append("Keyword").append(SPLIT);
        header.append("Location").append(SPLIT);
        header.append("Url").append(SPLIT);
        if(retrieveType == AutoAdhocRankProjectEntity.RETRIEVE_RANKCHECK_AND_GOOGLE_SV){
            header.append("AvgSv").append(SPLIT);
            header.append("Cpc").append(SPLIT);
            header.append("True Rank").append(SPLIT);
            header.append("Web Rank").append(SPLIT);
            header.append("URL Type").append(SPLIT);
            header.append("SubRank Url").append(SPLIT);
            header.append("SubRank").append(SPLIT);
            header.append("title").append(SPLIT);
            header.append("meta");
        }else {
            header.append("True Rank").append(SPLIT);
            header.append("Web Rank").append(SPLIT);
            header.append("URL Type").append(SPLIT);
            header.append("SubRank Url").append(SPLIT);
            header.append("SubRank").append(SPLIT);
            header.append("title").append(SPLIT);
            header.append("meta");
        }

        lines.add(header.toString());
        FileUtils.writeLines(outFile, lines, true);
    }

    public static String appendData(CLRankingDetailEntity clRankingDetailEntity, int retrieveType) {
        StringBuffer line = new StringBuffer();
        line.append(clRankingDetailEntity.getRankingDate()).append(SPLIT);
        line.append(clRankingDetailEntity.getKeywordName()).append(SPLIT);
        line.append(clRankingDetailEntity.getCityName()).append(SPLIT);
        line.append(clRankingDetailEntity.getUrl() == null ? "-" : ExtractService.formatGoogleUrl(clRankingDetailEntity.getUrl())).append(SPLIT);
        if(retrieveType == AutoAdhocRankProjectEntity.RETRIEVE_RANKCHECK_AND_GOOGLE_SV){
            line.append(clRankingDetailEntity.getAvgSearchVolume() == null ? "-" : clRankingDetailEntity.getAvgSearchVolume()).append(SPLIT);
            line.append(clRankingDetailEntity.getCpc() == null ? "-" : clRankingDetailEntity.getCpc()).append(SPLIT);
            if (clRankingDetailEntity.getTrueRank().equals(-1) || clRankingDetailEntity.getTrueRank().equals(0)) {
                line.append("101").append(SPLIT);
            } else {
                line.append(clRankingDetailEntity.getTrueRank()).append(SPLIT);
            }
            line.append(clRankingDetailEntity.getWebRank()).append(SPLIT);
            line.append(RankTypeManager.getUrlTypeName(clRankingDetailEntity.getType())).append(SPLIT);
            line.append(StringUtils.isBlank(clRankingDetailEntity.getSubRankUrl()) ? "-" : clRankingDetailEntity.getSubRankUrl()).append(SPLIT);
            line.append(clRankingDetailEntity.getSubRank() == null ? "-" : clRankingDetailEntity.getSubRank()).append(SPLIT);
            String label = CommonUtils.formatTitleMeta(clRankingDetailEntity.getLabel());
            line.append(StringUtils.isBlank(label) ? "-" : label).append(SPLIT);
            String meta = CommonUtils.formatTitleMeta(clRankingDetailEntity.getMeta());
            line.append(StringUtils.isBlank(meta) ? "-" : meta);
        }else {
            if (clRankingDetailEntity.getTrueRank().equals(-1) || clRankingDetailEntity.getTrueRank().equals(0)) {
                line.append("101").append(SPLIT);
            } else {
                line.append(clRankingDetailEntity.getTrueRank()).append(SPLIT);
            }
            line.append(clRankingDetailEntity.getWebRank()).append(SPLIT);
            line.append(RankTypeManager.getUrlTypeName(clRankingDetailEntity.getType())).append(SPLIT);
            line.append(StringUtils.isBlank(clRankingDetailEntity.getSubRankUrl()) ? "-" : clRankingDetailEntity.getSubRankUrl()).append(SPLIT);
            line.append(clRankingDetailEntity.getSubRank() == null ? "-" : clRankingDetailEntity.getSubRank()).append(SPLIT);
            String label = CommonUtils.formatTitleMeta(clRankingDetailEntity.getLabel());
            line.append(StringUtils.isBlank(label) ? "-" : label).append(SPLIT);
            String meta = CommonUtils.formatTitleMeta(clRankingDetailEntity.getMeta());
            line.append(StringUtils.isBlank(meta) ? "-" : meta);
        }
        return line.toString();
    }

    private void addHeadersForExactFilePAA(File outFile) throws IOException {
        List<String> lines = new ArrayList<String>();
        StringBuffer header = new StringBuffer();

        header.append("Keyword").append(SPLIT);
        header.append("Question").append(SPLIT);
        header.append("Question Link");
        lines.add(header.toString());
        FileUtils.writeLines(outFile, lines, true);
    }

    public static String appendDataPAA(String keywordName, String question, String link) {
        StringBuffer line = new StringBuffer();
        line.append(keywordName).append(SPLIT);
        line.append(question).append(SPLIT);
        line.append(link);
        return line.toString();
    }

    public String getDetailTable(int engine, int language, boolean isMobile) {

        String prefix = isMobile ? "m_" : "d_";
        String tableName = prefix + TABLE_DETAIL_TYPE;

        if (engine == 1 && language == 1) {
            tableName = tableName + "_us";
        } else {
            tableName = tableName + "_intl";
        }
        if (isHourly) {
            return HOURLY_DATABASE_NAME + "." + tableName;
        } else {
            return DATABASE_NAME + "." + tableName;
        }

    }

    public String getInfoTable(int engine, int language, boolean isMobile) {

        String prefix = isMobile ? "m_" : "d_";
        String tableName = prefix + TABLE_INFO_TABLE;

        if (engine == 1 && language == 1) {
            tableName = tableName + "_us";
        } else {
            tableName = tableName + "_intl";
        }
        if (isHourly) {
            return HOURLY_DATABASE_NAME + "." + tableName;
        } else {
            return DATABASE_NAME + "." + tableName;
        }

    }

    public String getSubrankTable(int engine, int language, boolean isMobile) {

        String prefix = isMobile ? "m_" : "d_";
        String tableName = prefix + TABLE_SUBRANK_TYPE;

        if (engine == 1 && language == 1) {
            tableName = tableName + "_us";
        } else {
            tableName = tableName + "_intl";
        }
        if (isHourly) {
            return HOURLY_DATABASE_NAME + "." + tableName;
        } else {
            return DATABASE_NAME + "." + tableName;
        }

    }

    public static void main(String[] args) {
        AutoAdhocExtract autoAdhocExtract = new AutoAdhocExtract();
        processDate = new Date();
        if (args != null && args.length > 0) {
            isHourly = Boolean.parseBoolean(args[0]);

            if (args.length >= 2) {

            }
        }
        System.out.println("=====isHourly:" + isHourly);
        autoAdhocExtract.process();

    }

    private static String[] rv_headers = new String[]{
            "search_metadata - id", "search_metadata - status", "search_metadata - date", "search_parameters - engine", "search_parameters - google_domain", "search_parameters - gl", "search_parameters - hl", "search_parameters - device", "search_parameters - q", "search_parameters - location_requested", "search_information - total_results", "search_information - app_flag", "search_information - local_map_flag", "search_information - ads_flag", "search_information - answer_box_flag", "search_information - cpc", "search_information - search_volume", "search_information - commercial_flag", "search_information - flights_flag", "search_information - google_research_flag", "search_information - top_ppc_count", "search_information - social_in_knowledge", "search_information - pla_flag", "search_information - knowledge_graph_flag", "answer_box_url", "google_recommend", "refine_by - title", "refine_by - details", "google_job - link", "google_job - details", "local_map - link", "local_map - title", "local_map - places - rank", "local_map - places - title", "inline_videos - rank", "inline_videos - title", "inline_videos - link", "inline_images - rank", "inline_images - title", "inline_images - link", "top_stories - rank", "top_stories - title", "top_stories - link", "people_also_ask - title", "people_also_ask - link", "organic_results - true rank", "organic_results - web rank", "organic_results - type", "organic_results - title", "organic_results - snippet", "organic_results - link", "organic_results - rating_number", "organic_results - mobile_friendly"
    };

    private static Integer totalExtractCnt = 0;
    private static Integer totalErrorCnt = 0;
    private static final String DEVICE_EXPORT_STR_DESKTOP = "desktop";
    private static final String DEVICE_EXPORT_STR_MOBILE = "mobile";
    private static final String BAD_CHAR = "�";

    private String processRedVentureDomain(AdhocRankTask adhocRankTask, AutoAdhocRankProjectEntity autoAdhocRankProjectEntity) {
        int projectId = adhocRankTask.getProjectId();
        String startDay = FormatUtils.formatDate(FormatUtils.toDate(adhocRankTask.getStartDay().toString(),
                FormatUtils.DATE_FORMAT_YYYYMMDD), FormatUtils.DATE_PATTERN_2);
        int startHour = adhocRankTask.getStartHour();
        if (autoAdhocRankProjectEntity == null) {
            System.out.println("====project not exist:" + projectId+ ",time3:" + System.currentTimeMillis());
            return null;
        }

        String extractFilePath = LOCAL_FOLDER + autoAdhocRankProjectEntity.getOwnDomainId() + HOURLY_ADHOC_FOLDER;
        String fileName = autoAdhocRankProjectEntity.getProjectName() + "_" + autoAdhocRankProjectEntity.getOwnDomainId() + "_" + startDay + "_" + startHour + "_hourlyAdhoc.txt";

        File outFile = new File(extractFilePath + "/" + fileName);

        if (outFile != null && outFile.exists()) {
            outFile.delete();
        }

        CsvWriteConfig csvWriteConfig = CsvWriteConfig.defaultConfig();
        csvWriteConfig.setFieldSeparator('\t');
        CsvWriter csvWriter = CsvUtil.getWriter(outFile, Charset.forName("utf-8"), true, csvWriteConfig);
        csvWriter.write(rv_headers);

        OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getById(autoAdhocRankProjectEntity.getOwnDomainId());
        processForEngineLanguage(ownDomainEntity, autoAdhocRankProjectEntity, csvWriter, startDay, startHour);

        if (csvWriter != null) {
            try {
                csvWriter.close();
            } catch (Exception exp) {
                exp.printStackTrace();
            }
        }

        return outFile.getAbsolutePath();
    }


    private void processForEngineLanguage(OwnDomainEntity ownDomainEntity,
                                          AutoAdhocRankProjectEntity autoAdhocRankProject, CsvWriter csvWriter, String startDay, Integer startHour) {

        int projectId = autoAdhocRankProject.getId();
        int engineId = autoAdhocRankProject.getEnabled();
        int languageId = autoAdhocRankProject.getLanguageId();
        int device = autoAdhocRankProject.getDevice();
        boolean isMobile = device == 1 ? false : true;

        int num = 0;
        String currKeywordKeywordHash = "";
        Integer currCityId = 0;
        Integer currentRank = 0;
        KeywordRankVO keywordRankVO = new KeywordRankVO();
        KeywordRankEntityVO keywordRankEntityVO = new KeywordRankEntityVO();
        List<KeywordRankEntityVO> keywordRankEntityVOs;
        KeywordSubRankEntityVO keywordSubRankEntityVO;

        String country = ownDomainEntity.getSearchEngineCountry();
        String countryName = "United States";
        List<EngineCountryLanguageMappingEntity> mappingEntities = mappingEntityDAO.getByEngineLanguageIdForCountry(engineId, languageId, country);
        if (!mappingEntities.isEmpty()) {
            countryName = mappingEntities.get(0).getCountryDisplayName();
            System.out.println("Find country name : " + countryName);
        } else {
            System.out.println("Can't find country name use default : " + countryName);
        }
        String location = "";

        String detailTableName = getDetailTable(engineId, languageId, isMobile);
        String infoTableName = getInfoTable(engineId, languageId, isMobile);
        String subRankTableName = getSubrankTable(engineId, languageId, isMobile);

        List<CLRankingDetailEntity> rankList = adhocInfoDao.queryForExtract(engineId, languageId, isMobile,
                projectId, startDay, startHour, detailTableName, infoTableName, subRankTableName, isHourly);
        if (rankList != null && rankList.size() > 0) {
            try {
                for (CLRankingDetailEntity detailEntity : rankList) {
                    if (StringUtils.isBlank(currKeywordKeywordHash)
                            || !StringUtils.equalsIgnoreCase(currKeywordKeywordHash, detailEntity.getKeywordHash())
                            || currCityId.intValue() != detailEntity.getLocationId()) {
                        if (StringUtils.isNotBlank(currKeywordKeywordHash)) {

                            num++;

                            location = StringUtils.isNotBlank(keywordRankVO.getCityName()) ? keywordRankVO.getCityName() : countryName;
                            processSignalKeyword(keywordRankVO, csvWriter, ownDomainEntity, isMobile, location, startDay, startHour);
                        }

                        keywordRankVO = new KeywordRankVO();

                        currKeywordKeywordHash = detailEntity.getKeywordHash();
                        currCityId = detailEntity.getLocationId();

                        keywordRankVO.setId(0);
                        keywordRankVO.setKeywordHash(detailEntity.getKeywordHash());
                        keywordRankVO.setCpc(detailEntity.getCpc() + "");

                        //from ALps: one more thing. if sv is 0 , you have to check additional flg to ensure its indeed 0.
                        //if not_realSearchVolume =1 means we dont have SV for this keywords. we should put -99 instead of 0
                        if (NumberUtils.toInt(detailEntity.getNotRealSearchVolume()) == 1 && detailEntity.getAvgSearchVolume() == 0L) {
                            keywordRankVO.setSearchVol("-99");
                        } else {
                            keywordRankVO.setSearchVol(detailEntity.getAvgSearchVolume() + "");
                        }
                        if (detailEntity.getLocationId() > 0) {
                            String cityName = geoService.getCityName(detailEntity.getLocationId());
//                            			String cityName = detailEntity.getCityName();
//                            if (cityName == null) {
//                                cityName = "Unknown city";
//                            }
                            // getCityName： Is it feasible?
//                            String cityName = detailEntity.getCityName();
                            keywordRankVO.setCityName(cityName);
                        }
                        keywordRankVO.setKeyword(detailEntity.getKeywordName());
                        keywordRankVO.setAppFlg(NumberUtils.toInt(detailEntity.getAppFlg()) == 1 ? "Y" : "N");
                        keywordRankVO.setLlFlg(NumberUtils.toInt(detailEntity.getLlFlg()) == 1 ? "Y" : "N");
                        keywordRankVO.setPpcFlg(NumberUtils.toInt(detailEntity.getPpcFlg()) == 1 ? "Y" : "N");
                        keywordRankVO.setCommercialFlg(NumberUtils.toInt(detailEntity.getCommercialFlg()) == 1 ? "Y" : "N");
                        keywordRankVO.setFlightSearchFlg(NumberUtils.toInt(detailEntity.getFlightSearchFlg()) == 1 ? "Y" : "N");
                        keywordRankVO.setResearchFlg(NumberUtils.toInt(detailEntity.getResearchFlg()) == 1 ? "Y" : "N");
                        keywordRankVO.setTopPpcCnt(detailEntity.getTopPpcCnt() + "");
                        keywordRankVO.setSocialInKg(NumberUtils.toInt(detailEntity.getSocial_in_kg()) == 1 ? "Y" : "N");
                        keywordRankVO.setPlaFlg(NumberUtils.toInt(detailEntity.getPla_flg()) == 1 ? "Y" : "N");
                        keywordRankVO.setKnogTag(NumberUtils.toInt(detailEntity.getKnog_flg()) == 1 ? "Y" : "N");

                        String answerboxUrl = "";
                        if (StringUtils.isNotBlank(detailEntity.getAnswerboxUrl())) {
                            answerboxUrl = detailEntity.getAnswerboxUrl();

                            if (StringUtils.isNotBlank(detailEntity.getAdditionalAb())) {
                                answerboxUrl = detailEntity.getAnswerboxUrl() + "!_!" + detailEntity.getAdditionalAb();
                            }
                        }

                        keywordRankVO.setAnswerBox(answerboxUrl);
                        keywordRankVO.setGoogleRecommend(detailEntity.getGoogleRecommend());
                        keywordRankVO.setRefineBy(detailEntity.getRefineBy());
                        keywordRankVO.setJobLink(detailEntity.getJobLink());
                        keywordRankVO.setCityId(detailEntity.getLocationId());

                        keywordRankVO.setAdditionalQuestions(detailEntity.getAdditionalQuestions());
                        keywordRankVO.setQuestions(detailEntity.getQuestions());
                    }

                    //means this record is not a subrank
                    if (currentRank == 0 || currentRank != detailEntity.getTrueRank()) {
                        currentRank = detailEntity.getTrueRank();

                        keywordRankEntityVO = new KeywordRankEntityVO();
                        keywordRankEntityVO.setLabel(detailEntity.getLabel());
                        keywordRankEntityVO.setMetaDesc(detailEntity.getMeta());

                        String landingPage = detailEntity.getUrl();
                        keywordRankEntityVO.setLandingPage(landingPage);
                        keywordRankEntityVO.setRank(detailEntity.getTrueRank());
                        keywordRankEntityVO.setRating(NumberUtils.toInt(detailEntity.getRating()) == 1 ? "Y" : "N");
                        keywordRankEntityVO.setRatingNumber(detailEntity.getRatingNumber());
                        keywordRankEntityVO.setType(detailEntity.getType());
                        if (keywordRankVO.getKeywordRankEntityVOs() == null) {
                            keywordRankEntityVOs = new ArrayList<>();
                            keywordRankVO.setKeywordRankEntityVOs(keywordRankEntityVOs);
                        } else {
                            keywordRankEntityVOs = keywordRankVO.getKeywordRankEntityVOs();
                        }

                        keywordRankEntityVOs.add(keywordRankEntityVO);

                    }

                    if (detailEntity.getType() == KeywordRankEntityVO.TYPE_LOCALLISTING) {

//                        			System.out.println(" ======= subRank for locallisting");
                        List<String> localListingList;
                        if (keywordRankVO.getLocalListing() == null) {
                            localListingList = new ArrayList<>();

                        } else {
                            localListingList = keywordRankVO.getLocalListing();
                        }

                        localListingList.add(detailEntity.getSubDomainRev() + "!_!" + detailEntity.getSubRankUrl());
                        keywordRankVO.setLocalListing(localListingList);
                        continue;
                    }


                    //======================  subrank ========================

                    if ((detailEntity.getType() == KeywordRankEntityVO.TYPE_NEWS
                            || detailEntity.getType() == KeywordRankEntityVO.TYPE_IMGAGE
                            || detailEntity.getType() == KeywordRankEntityVO.TYPE_VIDEO)) {

                        List<KeywordSubRankEntityVO> subrankList;

                        if (keywordRankEntityVO.getSubRankVOs() == null) {
                            subrankList = new ArrayList<>();

                        } else {
                            subrankList = keywordRankEntityVO.getSubRankVOs();
                        }

                        String subRankLandingPage = detailEntity.getSubRankUrl();
                        keywordSubRankEntityVO = new KeywordSubRankEntityVO();

                        keywordSubRankEntityVO.setRank(detailEntity.getTrueRank());
                        keywordSubRankEntityVO.setSubRank(detailEntity.getSubRank());
                        keywordSubRankEntityVO.setLabel(detailEntity.getSubRankLabel());
                        keywordSubRankEntityVO.setLandingPage(subRankLandingPage);

                        subrankList.add(keywordSubRankEntityVO);

                        keywordRankEntityVO.setSubRankVOs(subrankList);

                    }

                    //======================  subrank ========================

                }

                System.out.println("processing on :" + num);


                if (keywordRankVO != null) {
                    //process for the last keyword
                    processSignalKeyword(keywordRankVO, csvWriter, ownDomainEntity, isMobile, location, startDay, startHour);
                    num++;
                }

                if (num > 0) {


                    System.out.println("Total processed : " + num + ", OID:" + ownDomainEntity.getId()
                            + ", engine:" + engineId + ", languageId:" + languageId + ", device:" + isMobile);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else {
            System.out.println(" nothing need to process!!!");
            return;
        }

    }


    private void processSignalKeyword(KeywordRankVO keywordRankVO, CsvWriter csvWriter,
                                      OwnDomainEntity ownDomainEntity, boolean isMobile, String location, String startDay, Integer startHour) {


        String[] defaultArr = new String[rv_headers.length];
        String engineStr = ownDomainEntity.getSearchEngine();
        String country = ownDomainEntity.getSearchEngineCountry();
        String languageStr = ownDomainEntity.getLanguage();
        String deviceStr = DEVICE_EXPORT_STR_DESKTOP;
        if (isMobile) {
            deviceStr = DEVICE_EXPORT_STR_MOBILE;
        }

        try {


            for (int i = 0; i < rv_headers.length; i++) {
                defaultArr[i] = "";
            }
//            System.out.println(" MatchKW:" + keywordRankVO.getKeyword());
//        SeoClarityKeywordEntity clarityKeywordEntity = seoClarityKeywordEntityDAO.getByKeyword(keywordRankVO.getKeyword());
//        if(null == clarityKeywordEntity) {
//            System.out.println("ERROR : can't find rankcheck id : "+keywordRankVO.getKeyword());
//            continue;
//        }
            Integer id = keywordRankVO.getId();
//        SeoClarityKeywordAdwordsEntity adwordsEntity = seoClarityKeywordAdwordsEntityDAO.getExistedEntity(id, 99, languageId, 0);
            String sv = StringUtils.isNotBlank(keywordRankVO.getSearchVol()) ? keywordRankVO.getSearchVol() : "";
            sv = sv.replaceAll(",", "");
            String cpc = StringUtils.isNotBlank(keywordRankVO.getCpc()) ? keywordRankVO.getCpc() : "";
            cpc = cpc.replaceAll(",", "");


            // search_metadata - id
            // search_metadata - status
            defaultArr[0] = keywordRankVO.getKeywordHash();
            defaultArr[1] = "Success";
            defaultArr[2] = StringUtils.replace(startDay, "-", "") + (startHour >= 10 ? startHour : "0" + startHour);

//            String qKeyword = FormatUtils.decodeKeyword(keywordRankVO.getKeyword());
            String qKeyword = keywordRankVO.getKeyword();
            // search_parameters - engine
            // search_parameters - google_domain
            // search_parameters - gl
            // search_parameters - hl
            // search_parameters - device
            // search_parameters - q
            // search_parameters - location_requested
            defaultArr[2 + 1] = "google";
            defaultArr[3 + 1] = engineStr;
            defaultArr[4 + 1] = country;
            defaultArr[5 + 1] = languageStr;
            defaultArr[6 + 1] = deviceStr;
            defaultArr[7 + 1] = qKeyword;
            defaultArr[8 + 1] = location;

            String totalResult = keywordRankVO.getGoogleResultCnt();
            totalResult = StringUtils.isBlank(totalResult) ? "" : totalResult.replaceAll(",", "").replaceAll("\\.", "");
            // search_information - total_results
            // search_information - app_flag
            // search_information - local_map_flag
            // search_information - shop_flag
            // search_information - ads_flag
            // search_information - answer_box_flag
            // search_information - cpc
            // search_information - search_volume
            // search_information - commercial_flag
            // search_information - flights_flag
            // search_information - google_research_flag
            // search_information - top_ppc_count
            // search_information - social_in_knowledge
            // search_information - pla_flag
            // search_information - lpu
            defaultArr[9 + 1] = getStringValue(totalResult);
            defaultArr[10 + 1] = StringUtils.isNotBlank(keywordRankVO.getAppFlg()) ? keywordRankVO.getAppFlg() : "N";
            defaultArr[11 + 1] = getStringValue(keywordRankVO.getLlFlg());
            defaultArr[12 + 1] = getStringValue(keywordRankVO.getPpcFlg());
            defaultArr[13 + 1] = StringUtils.isNotBlank(keywordRankVO.getAnswerBox()) ? "Y" : "N";
            defaultArr[14 + 1] = getStringValue(cpc);
            defaultArr[15 + 1] = getStringValue(sv);
            defaultArr[16 + 1] = getStringValue(keywordRankVO.getCommercialFlg());
            defaultArr[17 + 1] = getStringValue(keywordRankVO.getFlightSearchFlg());
            defaultArr[18 + 1] = getStringValue(keywordRankVO.getResearchFlg());
            defaultArr[19 + 1] = getStringValue(StringUtils.isBlank(keywordRankVO.getTopPpcCnt()) ? "0" : keywordRankVO.getTopPpcCnt());
            defaultArr[20 + 1] = StringUtils.isNotBlank(keywordRankVO.getSocialInKg()) ? keywordRankVO.getSocialInKg() : "N";
            defaultArr[21 + 1] = StringUtils.isNotBlank(keywordRankVO.getPlaFlg()) ? keywordRankVO.getPlaFlg() : "N";
            defaultArr[22 + 1] = getStringValue(keywordRankVO.getKnogTag());

            if (StringUtils.isNotBlank(keywordRankVO.getAnswerBox()) && !StringUtils.equalsIgnoreCase(keywordRankVO.getAnswerBox(), "null")) {
                defaultArr[23 + 1] = keywordRankVO.getAnswerBox();
                // answer_box_url
            }

            if (StringUtils.isNotBlank(keywordRankVO.getGoogleRecommend()) && !StringUtils.equalsIgnoreCase(keywordRankVO.getGoogleRecommend(), "-")) {
                defaultArr[24 + 1] = keywordRankVO.getGoogleRecommend();
                // google_recommend
            }

            if (StringUtils.isNotBlank(keywordRankVO.getRefineBy()) && !StringUtils.equalsIgnoreCase(keywordRankVO.getRefineBy(), "-")) {
                String[] arr = StringUtils.split(keywordRankVO.getRefineBy(), "!_!");
                // refine_by - title
                // refine_by - details
                if (arr != null && arr.length >= 2) {
                    String[] sarray = arr[1].split(" ");
                    if (sarray != null && sarray.length >= 2) {
                        for (String s : sarray) {
                            defaultArr[25 + 1] = getStringValue(arr[0]);
                            defaultArr[26 + 1] = getStringValue(s);
                            csvWriter.write(defaultArr);
                        }
                    }
                    defaultArr[25 + 1] = "";
                    defaultArr[26 + 1] = "";
                }

            }

            if (StringUtils.isNotBlank(keywordRankVO.getJobLink()) && !StringUtils.equalsIgnoreCase(keywordRankVO.getJobLink(), "-")) {
                String[] arr = StringUtils.split(keywordRankVO.getJobLink(), "@_@");
                if (arr != null && arr.length >= 2) {
                    // google_job - link
                    // google_job - details
                    String[] sarray = arr[1].split(" ");
                    if (sarray != null && sarray.length >= 2) {
                        for (String s : sarray) {
                            defaultArr[27 + 1] = getStringValue(arr[0]);
                            defaultArr[28 + 1] = getStringValue(s);
                            csvWriter.write(defaultArr);
                        }
                    }
                    defaultArr[27 + 1] = "";
                    defaultArr[28 + 1] = "";
                }
            }

            List<KeywordRankEntityVO> tmpList = getTargetType(keywordRankVO, KeywordRankEntityVO.TYPE_LOCALLISTING);
            if (CollectionUtils.isNotEmpty(tmpList)) {
                defaultArr[29 + 1] = tmpList.get(0).getLandingPage();
                defaultArr[30 + 1] = tmpList.get(0).getLabel();

                if (CollectionUtils.isNotEmpty(keywordRankVO.getLocalListing())) {
                    for (int i = 0; i < keywordRankVO.getLocalListing().size(); i++) {
                        String[] s = keywordRankVO.getLocalListing().get(i).split("!_!");
                        if (s.length == 0 || StringUtils.isBlank(s[0])) {
                            continue;
                        }
                        defaultArr[31 + 1] = String.valueOf(i + 1);
                        defaultArr[32 + 1] = getStringValue(s[0]);
                        csvWriter.write(defaultArr);
                    }
                    defaultArr[31 + 1] = "";
                    defaultArr[32 + 1] = "";
                }
                // local_map - link
                // local_map - title
                // local_map - places - rank
                // local_map - places - title
                defaultArr[29 + 1] = "";
                defaultArr[30 + 1] = "";
            }


            tmpList = getTargetType(keywordRankVO, KeywordRankEntityVO.TYPE_VIDEO);
            if (CollectionUtils.isNotEmpty(tmpList)) {
                for (KeywordRankEntityVO keywordRankEntityVO : tmpList) {
                    if (CollectionUtils.isEmpty(keywordRankEntityVO.getSubRankVOs())) {
                        continue;
                    }
                    for (KeywordSubRankEntityVO keywordSubRankEntityVO : keywordRankEntityVO.getSubRankVOs()) {
                        defaultArr[33 + 1] = String.valueOf(keywordSubRankEntityVO.getSubRank());
                        defaultArr[34 + 1] = getStringValue(keywordSubRankEntityVO.getLabel());
                        defaultArr[35 + 1] = getStringValue(keywordSubRankEntityVO.getLandingPage());
                        csvWriter.write(defaultArr);
                    }
                }
                defaultArr[33 + 1] = "";
                defaultArr[34 + 1] = "";
                defaultArr[35 + 1] = "";
                // inline_videos - rank
                // inline_videos - title
                // inline_videos - link
            }

            tmpList = getTargetType(keywordRankVO, KeywordRankEntityVO.TYPE_IMGAGE);
            if (CollectionUtils.isNotEmpty(tmpList)) {
                for (KeywordRankEntityVO keywordRankEntityVO : tmpList) {
                    if (CollectionUtils.isEmpty(keywordRankEntityVO.getSubRankVOs())) {
                        continue;
                    }
                    for (KeywordSubRankEntityVO keywordSubRankEntityVO : keywordRankEntityVO.getSubRankVOs()) {
                        defaultArr[36 + 1] = String.valueOf(keywordSubRankEntityVO.getSubRank());
                        defaultArr[37 + 1] = getStringValue(keywordSubRankEntityVO.getLabel());
                        defaultArr[38 + 1] = getStringValue(keywordSubRankEntityVO.getLandingPage());
                        csvWriter.write(defaultArr);
                    }
                }
                // inline_images - rank
                // inline_images - title
                // inline_images - link
                defaultArr[36 + 1] = "";
                defaultArr[37 + 1] = "";
                defaultArr[38 + 1] = "";
            }

            tmpList = getTargetType(keywordRankVO, KeywordRankEntityVO.TYPE_NEWS);
            if (CollectionUtils.isNotEmpty(tmpList)) {
                for (KeywordRankEntityVO keywordRankEntityVO : tmpList) {
                    if (CollectionUtils.isEmpty(keywordRankEntityVO.getSubRankVOs())) {
                        continue;
                    }
                    for (KeywordSubRankEntityVO keywordSubRankEntityVO : keywordRankEntityVO.getSubRankVOs()) {
                        defaultArr[39 + 1] = String.valueOf(keywordSubRankEntityVO.getSubRank());
                        defaultArr[40 + 1] = getStringValue(keywordSubRankEntityVO.getLabel());
                        defaultArr[41 + 1] = getStringValue(keywordSubRankEntityVO.getLandingPage());
                        csvWriter.write(defaultArr);
                    }
                }
                // top_stories - rank
                // top_stories - title
                // top_stories - link
                defaultArr[39 + 1] = "";
                defaultArr[40 + 1] = "";
                defaultArr[41 + 1] = "";
            }

            if (StringUtils.isNotBlank(keywordRankVO.getAdditionalQuestions()) && !StringUtils.equalsIgnoreCase(keywordRankVO.getAdditionalQuestions(), "-")) {
                String[] qArr = keywordRankVO.getAdditionalQuestions().split("@_@");
                if (qArr.length > 0) {
                    for (String s : qArr) {
                        String[] tArr = s.split("!_!");

                        if (tArr != null && tArr.length >= 2) {
                            defaultArr[42 + 1] = getStringValue(tArr[1]);
                            defaultArr[43 + 1] = getStringValue(tArr[0]);
                            csvWriter.write(defaultArr);
                        }

                    }
                }
            } else if (StringUtils.isNotBlank(keywordRankVO.getQuestions()) && !StringUtils.equalsIgnoreCase(keywordRankVO.getQuestions(), "-")) {
                String[] qArr = keywordRankVO.getQuestions().split("!_!");
                if (qArr.length > 0) {
                    for (String s : qArr) {
                        defaultArr[42 + 1] = getStringValue(s);
                        defaultArr[43 + 1] = "";
                        csvWriter.write(defaultArr);
                    }
                }
            }
            // related_questions - title
            // related_questions - link
            defaultArr[42 + 1] = "";
            defaultArr[43 + 1] = "";

            int webRank = 1;

            if (keywordRankVO != null && keywordRankVO.getKeywordRankEntityVOs() != null) {
                for (KeywordRankEntityVO keywordRankEntityVO : keywordRankVO.getKeywordRankEntityVOs()) {
                    String type = "Web Url";
                    String url = keywordRankEntityVO.getLandingPage();
                    if (url.contains(".google.com")) {
                        switch (keywordRankEntityVO.getType()) {
                            case KeywordRankEntityVO.TYPE_IMGAGE:
                                url = "images.google.com";
                                type = "Google Images";
                                break;
                            case KeywordRankEntityVO.TYPE_VIDEO:
                                url = "videos.google.com";
                                type = "Google Videos";
                                break;
                            case KeywordRankEntityVO.TYPE_NEWS:
                                url = "news.google.com";
                                type = "Google News";
                                break;
                            case KeywordRankEntityVO.TYPE_LOCALLISTING:
                                url = "maps.google.com";
                                type = "Google Map";
                                break;
                            case KeywordRankEntityVO.TYPE_FLIGHTS:
                                url = "flights.google.com";
                                type = "Google Flights";
                                break;
                            default:
                                url = FormatUtils.getDomainByUrl(url);
                                break;
                        }
                    }

                    defaultArr[44 + 1] = String.valueOf(keywordRankEntityVO.getRank());
                    defaultArr[44 + 2] = String.valueOf(keywordRankEntityVO.getType() != KeywordRankEntityVO.TYPE_WEB_RESOURCE ? 0 : webRank);
                    defaultArr[44 + 3] = type;
                    defaultArr[45 + 3] = getStringValue(keywordRankEntityVO.getLabel());
                    defaultArr[46 + 3] = getStringValue(keywordRankEntityVO.getMetaDesc());
                    defaultArr[47 + 3] = getStringValue(url);

                    if (StringUtils.equalsIgnoreCase(keywordRankEntityVO.getRating(), "Y")) {
                        defaultArr[48 + 3] = getStringValue(keywordRankEntityVO.getRatingNumber());
                    }
                    if (StringUtils.isNotBlank(keywordRankEntityVO.getAmpFlg())) {
                        defaultArr[49 + 3] = getStringValue(keywordRankEntityVO.getAmpFlg());
                    }
                    csvWriter.write(defaultArr);
                    defaultArr[48 + 3] = "";
                    defaultArr[49 + 3] = "";
                    if (keywordRankEntityVO.getType() == KeywordRankEntityVO.TYPE_WEB_RESOURCE) {
                        webRank++;
                    }
                }
                // organic_results - rank
                // organic_results - web rank
                // organic_results - type
                // organic_results - title
                // organic_results - snippet
                // organic_results - link
                // organic_results - rating_number
                // organic_results - mobile_friendly
            } else {
                System.out.println("Json:" + new Gson().toJson(keywordRankVO));
            }


//        String serpJson = JSONUtil.toJsonStr(object);
            csvWriter.flush();
        } catch (Exception e) {
            e.printStackTrace();

            totalErrorCnt++;
        }
    }

    private List<KeywordRankEntityVO> getTargetType(KeywordRankVO keywordRankVO, int type) {
        List<KeywordRankEntityVO> list = new ArrayList<>();

        if (keywordRankVO == null || keywordRankVO.getKeywordRankEntityVOs() == null) {
            return list;
        }

        for (KeywordRankEntityVO keywordRankEntityVO : keywordRankVO.getKeywordRankEntityVOs()) {
            if (keywordRankEntityVO.getType() == type) {
                list.add(keywordRankEntityVO);
            }
        }
        return list;
    }

    private String getStringValue(Object str) {
        return (str == null || StringUtils.isBlank(String.valueOf(str))) ? "" : str.toString().replaceAll(BAD_CHAR, "");
    }

    private void sendMailReport(int projectId, String status, String message) {
        Map<String, Object> reportMap = new HashMap<String, Object>();
        reportMap.put("userName", "Jason");
        reportMap.put("successMessage", message);
        String emailTo = "<EMAIL>";
        String subject = "Export Adhoc projectId " + projectId + "  " + status;
        String[] ccTo = new String[]{"<EMAIL>"};
//        String[] ccTo = null;

//        emailSenderComponent.sendMimeMultiPartMailAndBcc(emailTo, ccTo, subject, "mail_resource_operate_report.txt", "mail_resource_operate_report.html", reportMap);
        zeptoMailSenderComponent.sendMimeMultiPartZeptoMailAndBccByFunctionType(emailTo, ccTo, subject, "mail_resource_operate_report.txt", "mail_resource_operate_report.html", reportMap,
                null, ZeptoMailSenderComponent.FUNCTION_TYPE_BACKEND_INTERNAL, null, null);
    }

    private void sendEmail(String userName, String emailAddress, String fileLoc, String filename, int ownDomainId, String projectName) throws Exception {

        String emailTo = emailAddress;
//        emailTo = "<EMAIL>";
        System.out.println("=========Send to : " + emailTo + " start!");

        String subject = "Ad Hoc Data Retrieval Download Ready - " + projectName;
        String info = "The raw data for ad hoc retrieval project - " + projectName + " is now ready for download.";

        Map<String, Object> reportMap = new HashMap<String, Object>();
        reportMap.put("userName", userName);
        reportMap.put("info", info);

        AgencyInfoEntity agencyInfo = agencyInfoManager.getByDomainId(ownDomainId);

        int sizeMb = 0;
        File file = new File(fileLoc + filename);
        String downloadFileName = "";
        if (file.exists()) {
            long fileSize = file.length();
            sizeMb = (int) (fileSize / 1024 / 1024);
            System.out.println(" OID:" + ownDomainId + " filename: " + (fileLoc + filename) +
                    " size:" + fileSize + " MB:" + sizeMb);

            GZipUtil.zipFile(file.getAbsolutePath(), GZipUtil.ZIPFile_POSTFIX);
            downloadFileName = file.getAbsolutePath() + GZipUtil.ZIPFile_POSTFIX;
        }

        FTPServerInfoBean ftpServerInfo = FTPUtils.getFTPServerInfo();
        String host = ftpServerInfo.getPrivateHost();
        String ftpUsername = ftpServerInfo.getServerUserName();
        String ftpPassword = ftpServerInfo.getServerPassword();

//        FTPUtils.saveFileToFTPForAdhoc(host, ftpUsername, ftpPassword, ownDomainId, downloadFileName, true);
        //https://www.wrike.com/open.htm?id=1066001811 move to seagate
        String linkUrl = "";
        try {
            boolean isSaved = SeagateUtils.saveFileToDefaultSeagate(ownDomainId, downloadFileName);
            if(!isSaved){
                log.error("===send to Seagate Failed!projectName:" + projectName);
            }
            linkUrl = SeagateUtils.getDefaultSeagatePresignedUrl(ownDomainId, filename + GZipUtil.ZIPFile_POSTFIX);
            log.info("==isSaved:" + isSaved + ",linkUrl:" + linkUrl);
        }catch (Exception e){
            e.printStackTrace();
            log.error("===send to Seagate Failed!projectName:" + projectName);
        }

        String linkText = null;
//        try {
//            String name = URLEncoder.encode(filename + GZipUtil.ZIPFile_POSTFIX, "utf-8");
//            linkText = "https://downloads.seoclarity.net/adhocRankingExtract/" + StringUtils.replace(name, "+", "%20");
            linkText = linkUrl;
//        } catch (UnsupportedEncodingException e) {
//            log.error(e.getMessage(), e);
//            linkText = "https://downloads.seoclarity.net/adhocRankingExtract/" + filename + GZipUtil.ZIPFile_POSTFIX;
//            linkText = linkUrl;
//        }

        System.out.println("  fileLink:" + linkText);
        reportMap.put("fileLink", linkText);
//        String[] bccTo = new String[]{};
        String[] bccTo = new String[]{Constants.DEV_TEAM_EMAIL};
//        String[] bccTo = new String[]{"<EMAIL>", "<EMAIL>"};//test
//        emailSenderComponent.sendMimeMultiPartMailAndBccWithNickname(agencyInfo.getEmailNickname(), new String[]{emailTo}, bccTo,
//                subject, "mail_exportdata_backend_noattach.txt", "mail_exportdata_backend_noattach.html", reportMap, agencyInfo);
        zeptoMailSenderComponent.sendMimeMultiPartZeptoMailAndBccByFunctionType(emailTo, bccTo,subject, "mail_exportdata_backend_noattach.txt", "mail_exportdata_backend_noattach.html", reportMap, agencyInfo,
                ZeptoMailSenderComponent.FUNCTION_TYPE_DOWNLOAD_ALL, null, null);
        System.out.println("=========Send to : " + emailAddress + " success!");
    }

    private List<String> getCacheList(String key) throws Exception {
        if (StringUtils.isBlank(key)) {
            log.error("==keyEmpty");
            return null;
        }
        List<String> resultList = new ArrayList<>();
        String url = CACHE_LIST_URL + key;
        log.info("===worker get:" + url);
        String adhocCacheList = WorkerUtils.doGet(url, WorkerUtils.KEY_SECURITY_HEADER, WorkerUtils.VALUE_SECURITY_HEADER);
        String keys = new Gson().fromJson(adhocCacheList, Map.class).get("keys").toString();
        List<String> nameList = new Gson().fromJson(keys, List.class);
        JSONArray jsonArray = JSONObject.parseArray(JSON.toJSONString(nameList));
        List<Map> mapList = jsonArray.toJavaList(Map.class);
        System.out.println("===mapList :" + JSON.toJSONString(mapList));
        for (Map map : mapList) {
            System.out.println("===name :" + map.get("name"));
            resultList.add(map.get("name").toString());
        }
        return resultList;
    }

    private void deleteCache(String url) {
        try {
            System.out.println("==delete cache:" + url);
            WorkerUtils.doDelete(url, WorkerUtils.KEY_SECURITY_HEADER, WorkerUtils.VALUE_SECURITY_HEADER);
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("====doDelete error:" + url);
        }
    }

    private void deleteNotExistProjectCache(List<AutoAdhocRankProjectEntity> taskList, List<String> cacheList) {
        List<String> existProject = new ArrayList<>();
        for (AutoAdhocRankProjectEntity adhocRankProject : taskList) {
            int projectId = adhocRankProject.getId();
            int domainId = adhocRankProject.getOwnDomainId();
            String key = DELETE_CACHE_URL_PREFIX + CACHE_RANKING_UPLOAD_COMPLETE_KEY + "_" + domainId + "_" + projectId;
            existProject.add(key);
        }
        System.out.println("====taskList size:" + taskList.size() + ",cacheList:" + cacheList);
        cacheList.removeAll(existProject);
        System.out.println("====after remove taskList size:" + taskList.size() + ",cacheList:" + cacheList);
        if (!org.springframework.util.CollectionUtils.isEmpty(cacheList)) {
            for (String key : cacheList) {
                String deleteUrl = DELETE_CACHE_URL_PREFIX + key;
                System.out.println("==delete cache:" + deleteUrl);
                deleteCache(deleteUrl);
            }
        }
    }


}
