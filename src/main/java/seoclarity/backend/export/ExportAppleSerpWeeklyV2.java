package seoclarity.backend.export;

import ch.ethz.ssh2.Connection;
import ch.ethz.ssh2.SCPClient;
import com.google.gson.Gson;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import seoclarity.backend.dao.actonia.GroupTagEntityDAO;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.actonia.OwnDomainSettingEntityDAO;
import seoclarity.backend.dao.clickhouse.ClDailyRankingEntityDao;
import seoclarity.backend.dao.clickhouse.urlclaritydb.CrawlUrlDao;
import seoclarity.backend.dao.rankcheck.SeoClaritySearchEngineEntityDAO;
import seoclarity.backend.entity.actonia.*;
import seoclarity.backend.entity.bean.FTPServerInfoBean;
import seoclarity.backend.entity.clickhouse.CLRankingDetailEntity;
import seoclarity.backend.entity.clickhouse.crawlurl.TargetUrlHtmlDaily;
import seoclarity.backend.service.CommonDataService;
import seoclarity.backend.service.ScKeywordRankManager;
import seoclarity.backend.utils.*;
import seoclarity.backend.utils.loggly.LogglyUtils;
import seoclarity.backend.utils.loggly.LogglyVO;

import java.io.*;
import java.net.InetAddress;
import java.net.URLDecoder;
import java.util.*;

/**
 * https://www.wrike.com/open.htm?id=295468527
 */
public class ExportAppleSerpWeeklyV2 {

    private static final String SPLIT = "\t";
    private static final int SSH_TRY_COUNT = 20;
    private static final int EXTRACT_FOLDER_DOMAIN = 5739;
    private static final int QUERY_TRY_COUNT = 10;

    public static final String NO_BREAK_SPACE = "\u00a0";
    public static final String ONE_SPACE = " ";

    private static final String KEY_SEPARATOR = "!_!";
    private static final String TAG_NAME = "Meta Data Report";
    private static Date processDate = new Date();

    public static final Map<Integer, String> COUNTRY_NAME_MAP = new HashMap();

    static {
        COUNTRY_NAME_MAP.put(5739, "US-en");
        COUNTRY_NAME_MAP.put(5740, "AE-en");
        COUNTRY_NAME_MAP.put(5741, "AT-de");
        COUNTRY_NAME_MAP.put(5742, "AU-en");
        COUNTRY_NAME_MAP.put(5743, "BE-nl");
        COUNTRY_NAME_MAP.put(5744, "BR-pt");
        COUNTRY_NAME_MAP.put(5745, "CA-en");
        COUNTRY_NAME_MAP.put(5746, "CA-fr");
        COUNTRY_NAME_MAP.put(5747, "CH-de");
        COUNTRY_NAME_MAP.put(5748, "CN-zh");
        COUNTRY_NAME_MAP.put(5749, "CZ-cs");
        COUNTRY_NAME_MAP.put(5750, "DE-de");
        COUNTRY_NAME_MAP.put(5751, "DK-da");
        COUNTRY_NAME_MAP.put(5752, "ES-es");
        COUNTRY_NAME_MAP.put(5753, "FI-fi");
        COUNTRY_NAME_MAP.put(5754, "FR-fr");
        COUNTRY_NAME_MAP.put(5755, "HK-zh");
        COUNTRY_NAME_MAP.put(5756, "HU-hu");
        COUNTRY_NAME_MAP.put(5757, "IE-en");
        COUNTRY_NAME_MAP.put(5759, "IT-it");
        COUNTRY_NAME_MAP.put(5760, "JP-jp");
        COUNTRY_NAME_MAP.put(5761, "KR-kr");
        COUNTRY_NAME_MAP.put(5762, "MX-es");
        COUNTRY_NAME_MAP.put(5763, "MY-my");
        COUNTRY_NAME_MAP.put(5764, "NL-nl");
        COUNTRY_NAME_MAP.put(5765, "NO-no");
        COUNTRY_NAME_MAP.put(5766, "NZ-en");
        COUNTRY_NAME_MAP.put(5767, "PH-ph");
        COUNTRY_NAME_MAP.put(5768, "PL-pl");
        COUNTRY_NAME_MAP.put(5769, "PT-pt");
        COUNTRY_NAME_MAP.put(5770, "RU-ru");
        COUNTRY_NAME_MAP.put(5771, "SE-sv");
        COUNTRY_NAME_MAP.put(5772, "SG-en");
        COUNTRY_NAME_MAP.put(5773, "TH-th");
        COUNTRY_NAME_MAP.put(5774, "TR-tr");
        COUNTRY_NAME_MAP.put(5775, "TW-zh");
        COUNTRY_NAME_MAP.put(5776, "UK-en");
        COUNTRY_NAME_MAP.put(5784, "BE-fr");
        COUNTRY_NAME_MAP.put(5785, "CH-fr");
        COUNTRY_NAME_MAP.put(5786, "HK-en");
        COUNTRY_NAME_MAP.put(5787, "TH-en");
        COUNTRY_NAME_MAP.put(5843, "LU-fr");
        COUNTRY_NAME_MAP.put(5758, "IN-en");
    }

    public static final Map<Integer, String> COUNTRY_MAP = new HashMap();

    static {
        COUNTRY_MAP.put(5739, "");
        COUNTRY_MAP.put(5740, "ae");
        COUNTRY_MAP.put(5741, "at");
        COUNTRY_MAP.put(5742, "au");
        COUNTRY_MAP.put(5743, "benl");
        COUNTRY_MAP.put(5744, "br");
        COUNTRY_MAP.put(5745, "ca");
        COUNTRY_MAP.put(5746, "ca");
        COUNTRY_MAP.put(5747, "chde");
        COUNTRY_MAP.put(5748, "cn");
        COUNTRY_MAP.put(5749, "cz");
        COUNTRY_MAP.put(5750, "de");
        COUNTRY_MAP.put(5751, "dk");
        COUNTRY_MAP.put(5752, "es");
        COUNTRY_MAP.put(5753, "fi");
        COUNTRY_MAP.put(5754, "fr");
        COUNTRY_MAP.put(5755, "hk");
        COUNTRY_MAP.put(5756, "hu");
        COUNTRY_MAP.put(5757, "ie");
        COUNTRY_MAP.put(5759, "it");
        COUNTRY_MAP.put(5760, "jp");
        COUNTRY_MAP.put(5761, "kr");
        COUNTRY_MAP.put(5762, "mx");
        COUNTRY_MAP.put(5763, "my");
        COUNTRY_MAP.put(5764, "nl");
        COUNTRY_MAP.put(5765, "no");
        COUNTRY_MAP.put(5766, "nz");
        COUNTRY_MAP.put(5767, "ph");
        COUNTRY_MAP.put(5768, "pl");
        COUNTRY_MAP.put(5769, "pt");
        COUNTRY_MAP.put(5770, "ru");
        COUNTRY_MAP.put(5771, "se");
        COUNTRY_MAP.put(5772, "sg");
        COUNTRY_MAP.put(5773, "th");
        COUNTRY_MAP.put(5774, "tr");
        COUNTRY_MAP.put(5775, "tw");
        COUNTRY_MAP.put(5776, "uk");
        COUNTRY_MAP.put(5784, "befr");
        COUNTRY_MAP.put(5785, "chfr");
        COUNTRY_MAP.put(5786, "hk");
        COUNTRY_MAP.put(5787, "th");
        COUNTRY_MAP.put(5843, "lu");
        COUNTRY_MAP.put(5758, "in");
    }

    private static String LOC = "/home/<USER>/";
    //    private static String LOC = "/home/<USER>/source/jason/apple/";
    private static String FTP_SPECIAL_FOLDER = "MetaDataReport";

    private static String[] domainList;
    //    private ScriptRunInstanceEntity monitorEntity;
    private int outputDataCount = 0;

    private GroupTagEntityDAO groupTagEntityDAO;
    private ScKeywordRankManager scKeywordRankManager;
    private OwnDomainEntityDAO ownDomainEntityDAO;
    private OwnDomainSettingEntityDAO ownDomainSettingEntityDAO;
    private ClDailyRankingEntityDao clDailyRankingEntityDao;
    private SeoClaritySearchEngineEntityDAO seoClaritySearchEngineEntityDAO;
    private EmailSenderComponent emailSenderComponent;
    private CommonDataService commonDataService;
    private CrawlUrlDao crawlUrlDao;

    public ExportAppleSerpWeeklyV2() {
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        ownDomainSettingEntityDAO = SpringBeanFactory.getBean("ownDomainSettingEntityDAO");
        groupTagEntityDAO = SpringBeanFactory.getBean("groupTagEntityDAO");
        scKeywordRankManager = SpringBeanFactory.getBean("scKeywordRankManager");
        clDailyRankingEntityDao = SpringBeanFactory.getBean("clDailyRankingEntityDao");
        seoClaritySearchEngineEntityDAO = SpringBeanFactory.getBean("seoClaritySearchEngineEntityDAO");
        emailSenderComponent = SpringBeanFactory.getBean("emailSenderComponent");
        commonDataService = SpringBeanFactory.getBean("commonDataService");
        crawlUrlDao = SpringBeanFactory.getBean("crawlUrlDao");
    }


    public void processMulti() throws Exception {

//        Date now = new Date();
//        Calendar c = Calendar.getInstance();
//        c.setTime(now);
//        int weekday = c.get(Calendar.DAY_OF_WEEK);
//            if (weekday != 2) {
//                System.out.println("===not Monday , exit !!");
//                return;
//            }
        long startTime = System.currentTimeMillis();

        String rankDate = FormatUtils.formatDate(DateUtils.addDays(processDate, -1), FormatUtils.DATE_PATTERN_2);
        String dateStr = FormatUtils.formatDate(DateUtils.addDays(processDate, -1), "MMddyyyy");
        String loc = checkFolderExist(EXTRACT_FOLDER_DOMAIN);
        String message = "Export Weekly Apple SERP " + dateStr;

//        //get monitor iny.FREQfo entity
////        ScriptDeployInfoEntity monitorInfoEntity = commonDataService.getScriptDeployInfo("clarity-backend-scripts", this.getClass().getName());
////
////        monitorEntity = commonDataService.saveScriptRunInfo(monitorInfoEntity.getId(),
////                FormatUtils.formatDateToYyyyMmDd(processDate), ScriptRunInstanceEntity.STATUS_STARTED,
////                EXTRACT_FOLDER_DOMAIN, null, InetAddress.getLocalHost().getHostAddress(), getServerPath(),
////                ScriptRunInstanceEntity.DEVICE_DESKTOP, ScriptRunInstanceEntity.TAG_NO,
////                ScriptRunInstanceEntitUENCY_WEEKLY);


        String fileName = loc + File.separator + "MetaDataReport_www.Apple.com_" + dateStr + ".csv";
//        String latestFileName = loc + File.separator + "Apple_SERP-Snippet-Extract.csv";

        try {

            File outFile = new File(fileName);
            if (outFile.exists()) {
                outFile.delete();
            }
            addHeadersForExactFile(outFile);
            //https://www.wrike.com/workspace.htm?acc=45177#/inbox/task/500505448
            FTPServerInfoBean ftpServerInfo = FTPUtils.getFTPServerInfo();
            String host = ftpServerInfo.getPrivateHost();
            String ftpUsername = ftpServerInfo.getServerUserName();
            String ftpPassword = ftpServerInfo.getServerPassword();
            for (String domain : domainList) {
                int retryCount = 0;
                int domainId = NumberUtils.toInt(domain);

                String pDate = FormatUtils.formatDate(processDate, FormatUtils.DATE_FORMAT_YYYYMMDD);
                String sTime = FormatUtils.formatDate(new Date(), "yyyyMMdd HH:mm:ss");
                LogglyVO logglyVO = new LogglyVO();
                logglyVO.setoId(String.valueOf(domainId));
                logglyVO.setName("ExportAppleSerpWeeklyV2");
                logglyVO.setDevice("m");

                logglyVO.setpDate(pDate);
                List<String> groupList = new ArrayList<>();
                groupList.add(LogglyVO.GROUP_CUSTOMIZED_EXTRACT);
                logglyVO.setGroups(groupList);

                while (true){
                    try {
                        System.out.println("********************** for domain " + domainId + " *******************");

                        processDomain(domainId, rankDate, outFile);

                        logglyVO.setStatus(LogglyVO.STATUS_OK);
                        logglyVO.setsTime(sTime);
                        logglyVO.seteTime(FormatUtils.formatDate(new Date(), "yyyyMMdd HH:mm:ss"));
                        logglyVO.setRows(String.valueOf(outputDataCount));
                        String body = new Gson().toJson(logglyVO);
                        LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);

                        break;
                    } catch (Exception e) {
                        if(retryCount >= QUERY_TRY_COUNT){
                            System.out.println("====RETRY OVER " + QUERY_TRY_COUNT + "TIMES error :" + domainId);
//                            monitorEntity.setFatalError(e.getMessage());
//                            commonDataService.updateScriptRunInfo(monitorEntity);
//                            sendMailReport("Failed", message);
                            break;
                        }
                        e.printStackTrace();
                        System.out.println("====domain error :" + domainId + ", sleep 20s ");
                        Thread.sleep(1000 *20);
                        retryCount ++;

                        logglyVO.setStatus(LogglyVO.STATUS_NG);
                        String body = new Gson().toJson(logglyVO);
                        LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);

//                    continue;
                    }
                }

            }
            copyBySSH(host, ftpUsername, ftpPassword,
                    outFile.getAbsolutePath(),
                    LOC + EXTRACT_FOLDER_DOMAIN + File.separator + FTP_SPECIAL_FOLDER, 0);

//            monitorEntity.setDestinationServerIp(FTP_SERVER);
//            monitorEntity.setOutputFile(fileName);
//            monitorEntity.setOutputFileSizeKB(FormatUtils.getFileSizeKB(outFile.length()));
//            monitorEntity.setOutputDataCount(outputDataCount);

//            sendMailReport("Success", message);

        } catch (Exception e) {
//            monitorEntity.setFatalError(e.getMessage());
            e.printStackTrace();
//            sendMailReport("Failed", message);
        }

//        long endTime = System.currentTimeMillis();
//        int elapsedSeconds = (int) (endTime - startTime);
//        if (StringUtils.isNotBlank(monitorEntity.getFatalError())) {
//            monitorEntity.setStatus(ScriptRunInstanceEntity.STATUS_FAILURE);
//        } else {
//            monitorEntity.setStatus(ScriptRunInstanceEntity.STATUS_SUCCESS);
//        }
//
//        monitorEntity.setElapsedSeconds(elapsedSeconds);
//
//        commonDataService.updateScriptRunInfo(monitorEntity);
    }

    private void processDomain(int ownDomainId, String rankDate, File outFile) throws Exception {

        List<String> extractLines = new ArrayList<String>();

        OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getById(ownDomainId);
        if (ownDomainEntity == null) {
            System.out.println("=== domain not exist , exit !!");
            return;
        }
        int frequency = 1;//daily
        if (ownDomainEntity.getKeywordRankFrequency() != null && ownDomainEntity.getKeywordRankFrequency() == OwnDomainEntity.KEYWORD_RANK_FREQUENCY_WEEKLY) {
            processDate = FormatUtils.getLastSundayForWeeklyDomainExtract(processDate);
            System.out.println("====weekly domain processDate:" + processDate);
            frequency = 7;//weekly
        }


        int engineId = scKeywordRankManager.getSearchEngineId(ownDomainEntity);
        int languageId = scKeywordRankManager.getSearchLanguageId(ownDomainEntity);
        GroupTagEntity groupTag = groupTagEntityDAO.getGroupTagEntity(ownDomainId, TAG_NAME, GroupTagEntity.TAG_TYPE_KEYWORD);
        String countryName = COUNTRY_NAME_MAP.get(ownDomainId);
        String country = COUNTRY_MAP.get(ownDomainId);
        boolean isBroadMatch = ownDomainEntity.isBroadMatch();
        String searchEngine = ownDomainEntity.getSearchEngine();
        boolean isMobile = true;
        String domainName = ownDomainEntity.getDomain();
        String domainReverse = org.apache.commons.lang.StringUtils.reverseDelimited(domainName, '.');
        String rootDomainReverse = CommonUtils.getReversedRootDomain(domainName);

        if (ownDomainId == 5770) {

            System.out.println("=====process for 5770 engine:" + engineId + "," + searchEngine);
            extractLines = getWriteLines(ownDomainId, engineId, languageId, rankDate, groupTag.getId(), countryName,
                    isBroadMatch, searchEngine, false, domainReverse, rootDomainReverse);
            outputDataCount += extractLines.size();
            FileUtils.writeLines(outFile, "UTF-8", extractLines, true);

            engineId = 39;
            searchEngine = seoClaritySearchEngineEntityDAO.getEngineNameById(engineId);
            System.out.println("=====process for 5770 engine:" + engineId + "," + searchEngine);
            extractLines = getWriteLines(ownDomainId, engineId, languageId, rankDate, groupTag.getId(), countryName,
                    isBroadMatch, searchEngine, false, domainReverse, rootDomainReverse);
            outputDataCount += extractLines.size();
            FileUtils.writeLines(outFile, "UTF-8", extractLines, true);
        } else if(ownDomainId == 5760){

            System.out.println("=====process for 5760 engine:" + engineId + "," + searchEngine);
            searchEngine = "yahoo";
            extractLines = getWriteLines(ownDomainId, engineId, languageId, rankDate, groupTag.getId(), countryName,
                    isBroadMatch, searchEngine, false, domainReverse, rootDomainReverse);
            System.out.println("=====extractLines size1:" + extractLines.size());
            outputDataCount += extractLines.size();
            FileUtils.writeLines(outFile, "UTF-8", extractLines, true);

            engineId = 18;
//            searchEngine = seoClaritySearchEngineEntityDAO.getEngineNameById(engineId);
            searchEngine = "google.co.jp";
            System.out.println("=====process for 5760 engine:" + engineId + "," + searchEngine);
            extractLines = getWriteLines(ownDomainId, engineId, languageId, rankDate, groupTag.getId(), countryName,
                    isBroadMatch, searchEngine, isMobile, domainReverse, rootDomainReverse);
            System.out.println("=====extractLines size2:" + extractLines.size());
            outputDataCount += extractLines.size();
            FileUtils.writeLines(outFile, "UTF-8", extractLines, true);

        } else if (ownDomainId == 5761) {

            System.out.println("=====process for 5761 engine:" + engineId + "," + searchEngine);
            searchEngine = "naver.com";
            extractLines = getWriteLines(ownDomainId, engineId, languageId, rankDate, groupTag.getId(), countryName,
                    isBroadMatch, searchEngine, false, domainReverse, rootDomainReverse);
            outputDataCount += extractLines.size();
            FileUtils.writeLines(outFile, "UTF-8", extractLines, true);

            engineId = 23;
            searchEngine = "google.co.kr";
//            searchEngine = seoClaritySearchEngineEntityDAO.getEngineNameById(engineId);
            System.out.println("=====process for 5761 engine 160 " + "," + searchEngine);
            extractLines = getWriteLines(ownDomainId, engineId, languageId, rankDate, groupTag.getId(), countryName,
                    isBroadMatch, searchEngine, isMobile, domainReverse, rootDomainReverse);
            outputDataCount += extractLines.size();

            FileUtils.writeLines(outFile, "UTF-8", extractLines, true);

        }
        else {

            extractLines = getWriteLines(ownDomainId, engineId, languageId, rankDate, groupTag.getId(), countryName,
                    isBroadMatch, searchEngine, isMobile, domainReverse, rootDomainReverse);
            outputDataCount += extractLines.size();
            FileUtils.writeLines(outFile, "UTF-8", extractLines, true);
        }

    }

    private List<String> getWriteLines(int ownDomainId, int engineId, int languageId, String rankingDate, int tagId,
                                       String country, boolean isBroadMatch, String searchEngine, boolean isMobile,
                                       String domainReverse, String rootDomainReverse) throws Exception{

        Map<String, CLRankingDetailEntity> extractMap = new HashMap<>();

        List<String> extractLines = new ArrayList<String>();
        List<TargetUrlHtmlDaily> targetUrlList = new ArrayList<>();
        List<String> rankingUrlList = new ArrayList<String>();
        List<String> urlHashUrlList = new ArrayList<String>();

        OwnDomainSettingEntity ownDomainSettingEntity = ownDomainSettingEntityDAO.getSettingByDomainId(ownDomainId);
        Integer targetUrlHtmlDailyDate = ownDomainSettingEntity.getTargetUrlHtmlDailyDate();
        String dailyDataCreationDate = FormatUtils.formatDate(FormatUtils.toDate(targetUrlHtmlDailyDate.toString(), FormatUtils.DATE_FORMAT_YYYYMMDD), FormatUtils.DATE_PATTERN_2);

        List<CLRankingDetailEntity> highestList =
                clDailyRankingEntityDao.exportAppleSerpWeekly(true, ownDomainId, engineId, languageId, rankingDate,
                        tagId, country, isBroadMatch, null, isMobile, domainReverse, rootDomainReverse);

//        String searchEngine = seoClaritySearchEngineEntityDAO.getEngineNameById(engineId);

        System.out.println("=====detail size : " + highestList.size());

        for (CLRankingDetailEntity detail : highestList) {

            String higestUrlHash = detail.getUrlhash();
            if(detail.getRank().equals(-1) || detail.getRank().equals(0)){//skip no ranking kw https://www.wrike.com/open.htm?id=1149658449
                continue;
            }

            String higestKeyword = detail.getKeywordName();
            detail.setSearchEngine(searchEngine);

            rankingUrlList.add(detail.getUrl());
            urlHashUrlList.add(higestUrlHash);
            if(rankingUrlList.size() >= 100){
                targetUrlList.addAll(crawlUrlDao.getDataByUrl(ownDomainId, rankingUrlList, urlHashUrlList));
                rankingUrlList.clear();
                urlHashUrlList.clear();
            }

            try {
//                extractLines.add(writeLine(detail));
                String higestKey = higestKeyword + KEY_SEPARATOR + higestUrlHash;
                System.out.println("=====higestKey : " + higestKey);
                extractMap.put(higestKey, detail);
            } catch (Exception e) {
//                monitorEntity.setFatalError(e.getMessage());
                e.printStackTrace();
                continue;
            }

            String keywodName = detail.getKeywordName();
            String url = detail.getUrl();
//            if (!url.contains("/shop/")) {

//            }
        }

        //get /shop/ or /buy/ urls
        List<CLRankingDetailEntity> normalList =
                clDailyRankingEntityDao.exportAppleSerpWeeklyForKeyword(ownDomainId, engineId, languageId, rankingDate,
                        tagId, country, isBroadMatch, null, isMobile, domainReverse, rootDomainReverse);

        System.out.println("=====normalList size : " + normalList.size());
        for (CLRankingDetailEntity rd : normalList) {

            String keyword = rd.getKeywordName();
            String shopUrlHash = rd.getUrlhash();
            if(rd.getRank().equals(-1) || rd.getRank().equals(0)){//skip no ranking kw https://www.wrike.com/open.htm?id=1149658449
                continue;
            }

            rd.setSearchEngine(searchEngine);

            rankingUrlList.add(rd.getUrl());
            urlHashUrlList.add(shopUrlHash);
            if(rankingUrlList.size() >= 100){
                targetUrlList.addAll(crawlUrlDao.getDataByUrl(ownDomainId, rankingUrlList, urlHashUrlList));
                rankingUrlList.clear();
                urlHashUrlList.clear();
            }

//                    extractLines.add(writeLine(rd));
            String key = keyword + KEY_SEPARATOR + shopUrlHash;
            System.out.println("=====key : " + key);
            extractMap.put(key, rd);
        }

        if(CollectionUtils.isNotEmpty(rankingUrlList)){
            System.out.println("=====rankingUrlList size : " + rankingUrlList.size());
            System.out.println("=====urlHashUrlList size : " + urlHashUrlList.size());
            targetUrlList.addAll(crawlUrlDao.getDataByUrl(ownDomainId, rankingUrlList, urlHashUrlList));
        }

        System.out.println("=====targetUrlList size : " + targetUrlList.size());
        System.out.println("=====extractMap size : " + extractMap.size());

        for(String key : extractMap.keySet()){

            String[] keyArray = key.split(KEY_SEPARATOR);
            String urlHashKey = keyArray[1];

            String codeTitle = "-";
            String codeMeta = "-";
            String responseCode = "-";

            boolean isMatch = false;

            for(TargetUrlHtmlDaily targetUrlHtmlDaily : targetUrlList){

                String urlHash = targetUrlHtmlDaily.getLowerCaseUrlHash();
                if(urlHashKey.equals(urlHash)){
                    codeTitle = StringUtils.trim(targetUrlHtmlDaily.getTitle().replaceAll(NO_BREAK_SPACE, ONE_SPACE));
                    codeMeta = StringUtils.trim(targetUrlHtmlDaily.getDescription().replaceAll(NO_BREAK_SPACE, ONE_SPACE));
                    responseCode = targetUrlHtmlDaily.getResponseCode();
                    isMatch = true;
                    break;
                }
            }

            if(!isMatch){
                TargetUrlHtmlDaily hashMatch = crawlUrlDao.getDataByUrlHash(ownDomainId, extractMap.get(key).getUrl(), false);
                if(hashMatch != null){
                    codeTitle = StringUtils.trim(hashMatch.getTitle().replaceAll(NO_BREAK_SPACE, ONE_SPACE));
                    codeMeta = StringUtils.trim(hashMatch.getDescription().replaceAll(NO_BREAK_SPACE, ONE_SPACE));
                    responseCode = hashMatch.getResponseCode();
                }else {
                    hashMatch = crawlUrlDao.getDataByUrlHash(ownDomainId, extractMap.get(key).getUrl(), true);
                    if(hashMatch != null){
                        codeTitle = StringUtils.trim(hashMatch.getTitle().replaceAll(NO_BREAK_SPACE, ONE_SPACE));
                        codeMeta = StringUtils.trim(hashMatch.getDescription().replaceAll(NO_BREAK_SPACE, ONE_SPACE));
                        responseCode = hashMatch.getResponseCode();
                    }
                }
            }

            extractLines.add(writeLine(extractMap.get(key), codeTitle, codeMeta, responseCode));
        }



        return extractLines;
    }

    public static String writeLine(CLRankingDetailEntity clRankingDetailEntity,String codeTitle, String codeMeta, String responseCode) throws Exception{

        StringBuffer line = new StringBuffer();
        line.append(clRankingDetailEntity.getDate()).append(SPLIT);
        line.append(clRankingDetailEntity.getDomain()).append(SPLIT);
        line.append(clRankingDetailEntity.getCountry()).append(SPLIT);
        line.append(clRankingDetailEntity.getSearchEngine()).append(SPLIT);
        line.append(clRankingDetailEntity.getKeywordName()).append(SPLIT);
        if (clRankingDetailEntity.getAvgSearchVolume().equals(-1l)) {
            line.append("-").append(SPLIT);
        } else {
            line.append(clRankingDetailEntity.getAvgSearchVolume()).append(SPLIT);
        }
        String url = URLDecoder.decode(clRankingDetailEntity.getUrl(), "utf-8");
        line.append(CommonUtils.formatTitleMeta(url)).append(SPLIT);
        if (clRankingDetailEntity.getRank().equals(-1) || clRankingDetailEntity.getRank().equals(0)) {
            line.append("101").append(SPLIT);
        } else {
            line.append(clRankingDetailEntity.getRank()).append(SPLIT);
        }

        line.append(responseCode).append(SPLIT);

        String title = clRankingDetailEntity.getLabel();
        if (title.contains("http://") || title.contains("https://")) {
            title = title.replaceAll(clRankingDetailEntity.getUrl(), "");
        }
        line.append(CommonUtils.formatTitleMeta(title)).append(SPLIT);

        line.append(CommonUtils.formatTitleMeta(codeTitle)).append(SPLIT);
        if(title.equalsIgnoreCase(codeTitle)){
            line.append("Yes").append(SPLIT);
        }else {
            line.append("No").append(SPLIT);
        }

        line.append(CommonUtils.formatTitleMeta(clRankingDetailEntity.getMeta())).append(SPLIT);
        line.append(clRankingDetailEntity.getMeta().length()).append(SPLIT);
        //https://www.wrike.com/open.htm?id=398592039
        line.append(CommonUtils.formatTitleMeta(codeMeta)).append(SPLIT);
        line.append(codeMeta.length()).append(SPLIT);
        //https://www.wrike.com/open.htm?id=398592039
        if(clRankingDetailEntity.getMeta().contains("...")){
            line.append("Yes").append(SPLIT);
        }else {
            line.append("No").append(SPLIT);
        }

        if(clRankingDetailEntity.getMeta().equalsIgnoreCase(codeMeta)){
            line.append("Yes");
        }else {
            line.append("No");
        }

        return line.toString();
    }


    public static void addHeadersForExactFile(File outFile) throws IOException {
        List<String> lines = new ArrayList<String>();
        StringBuffer header = new StringBuffer();
        header.append("Date").append(SPLIT);
        header.append("Domain").append(SPLIT);
        header.append("Country").append(SPLIT);
        header.append("Search Engine").append(SPLIT);
        header.append("Keyword").append(SPLIT);
        header.append("Average Search Volume").append(SPLIT);
        header.append("URL").append(SPLIT);
        header.append("Rank").append(SPLIT);

        header.append("Response Code").append(SPLIT);
        header.append("Live Search Engine Title").append(SPLIT);
        header.append("Code Page Title").append(SPLIT);
        header.append("Live Title Matches Code Title?").append(SPLIT);


        header.append("Live Search Engine Meta Description").append(SPLIT);
        header.append("Live Desc LEN").append(SPLIT);
        header.append("Code Meta Description").append(SPLIT);
        header.append("Code Desc LEN").append(SPLIT);
        header.append("Live Meta Desc Truncated?").append(SPLIT);
        header.append("Live Meta Desc Matches Code Meta Desc?");

        lines.add(header.toString());

        FileUtils.writeLines(outFile, lines, true);
    }


    public static String checkFolderExist(int domainId) {

        File backUpDir = new File(LOC + domainId);

        if (!backUpDir.exists() || !backUpDir.isDirectory()) {
            backUpDir.mkdir();
        }

        return backUpDir.getPath();
    }

    private static void copyBySSH(String host, String userName, String pw, String from, String saveTo, int type)
            throws Exception {
        if (type == 0) {
            System.out.println("copy from local to remote , file name:" + from);
        } else {
            System.out.println("copy from remote to local , file name:" + from);
        }

        boolean copySucceed = false;
        for (int i = 0; i < SSH_TRY_COUNT; i++) {

            try {
                Connection connection = new Connection(host);
                connection.connect();
                if (connection.authenticateWithPassword(userName, pw)) {
                    System.out.println("login successfully.");
                    SCPClient scpClient = connection.createSCPClient();
                    if (type == 0) {
                        scpClient.put(from, saveTo);
                    } else {
                        scpClient.get(from, saveTo);
                    }
                    connection.close();
                    copySucceed = true;
                    break;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }


            System.out.println("Failed to login to target host...");

            try {
                Thread.sleep(300000);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        if (copySucceed == true) {
            System.out.println("Copy " + from + " to " + host + " successfully.");
        } else {
//            monitorEntity.setExtraError("Couldnot login " + host + " for " + SSH_TRY_COUNT
//                    + " tries. Please copy file yourself.");
            throw new RuntimeException("Couldnot login " + host + " for " + SSH_TRY_COUNT
                    + " tries. Please copy file yourself.");
        }
    }

    public static void main(String[] args) {


        if (StringUtils.isNotBlank(args[0]) && StringUtils.containsIgnoreCase(args[0], ",")) {
            System.out.println("========== domain list:" + args[0] + " ===============");
            domainList = StringUtils.split(args[0], ",");
        }

        if (args.length >= 2 && StringUtils.isNotBlank(args[1])) {
            processDate = FormatUtils.toDate(args[1], FormatUtils.DATE_PATTERN_2);
        }

        try {
            ExportAppleSerpWeeklyV2 exportAppleSerpWeekly = new ExportAppleSerpWeeklyV2();
            exportAppleSerpWeekly.processMulti();
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    private String getServerPath() throws IOException {
        File directory = new File("");
        String courseFile = directory.getCanonicalPath();
        return courseFile;

    }

}
