package seoclarity.backend.export.onetime;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import lombok.extern.apachecommons.CommonsLog;
import okhttp3.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;

import java.io.*;
import java.util.*;
import java.util.concurrent.TimeUnit;

@CommonsLog
public class GetCategoryFromOpenAI {

    private static final String SPLIT = "\t";
    private static final String TITLE_SPLIT = "','";
    private static final String LOCAL_PATH = "/home/<USER>/openAI/";
//    private static final String LOCAL_PATH = "D:\\workspace\\extract\\api\\";
    private static final String REQUEST_URL = "https://api.openai.com/v1/chat/completions";
//    private static final String API_KEY = "Bearer ***************************************************";
    private static final String API_KEY = "Bearer ***************************************************";

    private static final int REQUEST_KW_CNT = 40;
    private Gson gson = new GsonBuilder().disableHtmlEscaping().create();

    public GetCategoryFromOpenAI() {

    }


    public static void main(String[] args) throws Exception{
//        GetCategoryFromOpenAI getCategoryFromOpenAI = new GetCategoryFromOpenAI();
//        String testContent = "{\"id\":\"chatcmpl-76cSGwb2s9WUOl2UHpmU3kwYqGPtF\",\"object\":\"chat.completion\",\"created\":1681812144,\"model\":\"gpt-3.5-turbo-0301\",\"usage\":{\"prompt_tokens\":1256,\"completion_tokens\":2841,\"total_tokens\":4097},\"choices\":[{\"message\":{\"role\":\"assistant\",\"content\":\"{\"farmhouse fresh\": \"home decor\",\"long layered hair front\": \"hairstyles\",\"air pod pro 2\": \"audio accessories\",\"cvs extra care\": \"pharmacy rewards program\",\"sinatraa\": \"esports\",\"tracker id amazon\": \"order tracking\",\"custom puzzles\": \"puzzles\",\"listen the music\": \"music streaming\",\"skinwalker dog\": \"urban legends\",\"coyote calls\": \"hunting\",\"what period\": \"na\",\"walking dead season 12\": \"tv shows\",\"2k games\": \"video games\",\"tents for glamping\": \"camping gear\",\"cruiseplan\": \"cruise planning\",\"cbs sports fantasy\": \"fantasy sports\",\"clinton kane\": \"music artists\",\"resume templates harvard\": \"resume templates\",\"weta\": \"special effects\",\"cookies carts\": \"cannabis products\",\"eid ul fitr 2022\": \"Islamic holidays\",\"purcell\": \"classical music composers\",\"signing company\": \"sign language services\",\"lilly krug\": \"social media influencers\",\"new york giants football\": \"football teams\",\"java 17\": \"programming languages\",\"giardia symptom\": \"disease symptoms\",\"journey 2 the mysterious island\": \"movies\",\"squishville\": \"toys\",\"tattoo ink red\": \"tattoos\",\"ideas bulletin boards\": \"office supplies\",\"otterbox iphone 13\": \"phone cases\",\"eyes jaundice\": \"disease symptoms\",\"evergreen apartments\": \"apartments for rent\",\"strangle\": \"crime\",\"fetus after 12 weeks\": \"pregnancy\",\"eshop nintendo card\": \"gaming gift cards\",\"rivian news\": \"electric vehicles\",\"how to tell if a wound is healing or infected\": \"first aid\",\"best buy tab\": \"tablets\",\"shaft 2019\": \"movies\",\"food in acidity\": \"diet for acidity\",\"net worth of larry page\": \"business executives\",\"ff7 remake part 2\": \"video games\",\"pimples nose\": \"skin care\",\"bedroom ideas teens\": \"bedroom decor\",\"prepagent\": \"real estate exam prep\",\"healthy ice creams\": \"healthy desserts\",\"itching on left hand\": \"disease symptoms\",\"the 355 movie\": \"movies\",\"cheap meals that are healthy\": \"budget healthy meals\",\"courts bed\": \"beds\",\"new balance near me\": \"sporting goods stores\",\"definition for hospice\": \"hospice care\",\"name the european countries\": \"geography\",\"chic by royalton punta cana\": \"hotels and resorts\",\"symptoms of pregnancy in 2 weeks\": \"pregnancy symptoms\",\"hospital rental beds\": \"medical equipment rental\",\"bread in a can\": \"canned bread\",\"how much do i need to sleep\": \"sleep requirements\",\"how to remove the blackheads\": \"skin care\",\"smoked pork tenderloin\": \"recipes\",\"kevin jewelry\": \"jewelry stores\",\"vacation 2015\": \"movies\",\"patent leather jordan 1\": \"sneakers\",\"evenkeel\": \"mental health\",\"married at first sight season 12\": \"reality tv\",\"bananaphone\": \"song\",\"alex toussaint\": \"fitness trainers\",\"wayfare phone number\": \"customer service\",\"sushi nearby\": \"sushi restaurants\",\"green hoodie mens\": \"men's clothing\",\"wind defenders\": \"motorcycle accessories\",\"jordan 1 patent leather\": \"sneakers\",\"double jointedness\": \"medical conditions\",\"women high top vans\": \"women's shoes\",\"chicken breast internal temp\": \"cooking temperatures\",\"beautiful crazy\": \"song\",\"the thai language\": \"languages\",\"costco travel login\": \"travel booking\",\"sony a1\": \"cameras\",\"cardi b songs\": \"music artists\",\"the kitchen on the food network\": \"cooking shows\",\"tax in new york city\": \"taxes\",\"ciox health\": \"healthcare technology\",\"snake poop\": \"animal waste\",\"unfettering\": \"na\",\"crescent moon tattoo\": \"tattoos\",\"160 kg to lbs\": \"unit conversion\",\"fixed sign\": \"astrology\",\"movies of alicia silverstone\": \"movies\",\"biostatisticians\": \"healthcare professionals\",\"rash from tick bites\": \"disease symptoms\",\"low maintenance front yard landscaping\": \"landscaping\",\"patriot supply food\": \"emergency food supply\",\"spanish class near me\": \"language classes\",\"runaway trains\": \"train accidents\",\"what minimum wage in texas\": \"minimum wage laws\",\"all time nba point leaders\": \"basketball statistics\",\"the goddess of fire\": \"mythology\",\"age of consent in ca\": \"legal age\",\"seizure definitions\": \"medical conditions\",\"kelly housewife\": \"reality tv\",\"dollar a mexico\": \"currency exchange\",\"big worm\": \"movie characters\",\"bts military service\": \"military service\",\"do you get pneumonia\": \"disease\",\"64 cm\": \"unit conversion\",\"tsla quote\": \"stock quotes\",\"harden vol 5\": \"basketball shoes\",\"trent baalke\": \"football executives\",\"zahara store\": \"clothing stores\",\"internet dishes\": \"satellite internet\",\"the donner pass\": \"historical events\",\"robert galbraith\": \"authors\",\"chakras are\": \"spirituality\",\"alvin louise martin\": \"na\",\"daniel wellington\": \"watch brands\",\"jointing compound\": \"construction materials\",\"what does sus mean\": \"internet slang\",\"how to ripen bananas\": \"fruit ripening\",\"scoops ahoy\": \"tv shows\",\"counter cafe\": \"restaurants\",\"rodri\": \"soccer players\",\"drawer organization kitchen\": \"kitchen organization\",\"tukol\": \"cold medicine\",\"aji de gallina\": \"peruvian cuisine\",\"dungeons and daddies\": \"podcasts\",\"daniel seavey\": \"music artists\",\"chicken schnitzel\": \"recipes\",\"nashville outfit\": \"fashion\",\"francis ii of france\": \"historical figures\",\"review a restaurant\": \"restaurant reviews\",\"non perishables food\": \"emergency food supply\",\"night light pediatrics\": \"pediatric clinics\",\"dan saunders\": \"na\",\"car rental to florida\": \"car rentals\",\"iconic definition\": \"vocabulary\",\"givenchy the gentleman\": \"fragrances\",\"draw a skull easy\": \"drawing tutorials\",\"health e arizona plus\": \"health insurance\",\"what is st patrick day\": \"holidays\",\"set of plate\": \"dinnerware sets\",\"prepass\": \"truck toll payment\",\"compress mp4\": \"video compression\",\"wallpaper in gold\": \"wallpaper\",\"the escape game\": \"escape rooms\",\"ga blue cross blue shield\": \"health insurance\",\"lunch boxes adult\": \"lunch boxes\",\"fat bao\": \"restaurants\",\"bass drum set\": \"drum sets\",\"natural gas for grills\": \"grilling fuel\",\"bisecthosting\": \"web hosting\",\"hiring mcdonalds near me\": \"job search\",\"black jacket woman\": \"women's clothing\",\"waterbeds n stuff\": \"furniture stores\",\"koch foods\": \"food manufacturers\",\"avenue clothing\": \"women's clothing stores\",\"cathy goodman\": \"na\",\"hub group\": \"logistics companies\",\"grey hoodie mens\": \"men's clothing\",\"frank ruiz\": \"na\",\"cashmere valley bank\": \"banks\",\"gaslamp quarter san diego ca\": \"neighborhoods\",\"channel 3000 news\": \"news channels\",\"julie peters\": \"na\",\"brushes for straightening hair\": \"hair styling tools\",\"intriguing synonym\": \"vocabulary\",\"el paso zoo\": \"zoos\",\"dreamworld coke flavor\": \"soda flavors\",\"frozen ground\": \"movies\",\"mayweather net worth\": \"boxers\",\"dress suit for woman\": \"women's suits\",\"colombian movies\": \"movies\",\"how much do doctors make\": \"salary information\",\"salma hayek young\": \"actresses\",\"update in spanish\": \"news\",\"drew barrymore net worth\": \"actresses\",\"texas drivers license replacement\": \"driver's license\",\"bobby mackey\": \"music venues\",\"types of horses\": \"horse breeds\",\"navy federal credit union customer service\": \"bank customer service\",\"tommy on martin\": \"tv characters\",\"yamaha side by side\": \"atvs\",\"jack sparrow run\": \"running events\",\"mcmansion\": \"architecture\",\"croc all terrain\": \"shoes\",\"macys cologne for man\": \"cologne\",\"province definition\": \"vocabulary\",\"stone glacier\": \"outdoor gear\",\"cheapest car insurance new driver\": \"car insurance\",\"xherdan shaqiri\": \"soccer players\",\"1phone 8 plus cases\": \"phone cases\",\"assurancewireless com\": \"cell phone service\",\"daysofourlives recap\": \"tv show recaps\",\"abc smart cookies\": \"tv shows\",\"ho house\": \"restaurants\",\"priscilla presley 60s\": \"actresses\",\"new mexico fire update\": \"wildfires\",\"video game characters\": \"fictional characters\",\"redbone meaning\": \"song meanings\",\"boost store mobile\": \"cell phone stores\",\"1pm pst to est\": \"time conversion\",\"pianists\": \"music professions\",\"xbox 1 controller\": \"gaming controllers\",\"what is a notary\": \"notary public\",\"coin exchange near me\": \"currency exchange\",\"cooper electric\": \"electrical supply stores\",\"paedon brown\": \"na\",\"michoacana ice cream\": \"ice cream shops\",\"noa kirel\": \"music artists\",\"the lovely bones cast\": \"movies\",\"mothergoose\": \"nursery rhymes\",\"what condoms\": \"contraceptives\",\"elf for christmas\": \"christmas decorations\",\"grutas tolantongo\": \"tourist attractions\",\"quick way of making money\": \"making money\",\"red ballooning\": \"na\",\"mid century modern house\": \"architecture\",\"air jordans 1 blue\": \"sneakers\",\"blockbuster bend oregon\": \"video rental stores\",\"polka dot man\": \"movie characters\",\"dual enrolled\": \"education\",\"ppp loan frauds\": \"fraud\",\"jennys nails\": \"nail salons\",\"free phone with t mobile\": \"cell phone deals\",\"adhd adult woman\": \"mental health\",\"swings coffee\": \"coffee shops\",\"life aids\": \"na\",\"mcgregor square\": \"tourist attractions\",\"la cuevita\": \"bars\",\"bullies in movies\": \"movie characters\",\"andros taverna\": \"restaurants\",\"modbox\": \"shipping containers\",\"steve martin movies and tv shows\": \"movies and tv shows\",\"ynw bslime\": \"music artists\",\"online notary\": \"notary public\",\"vanguard 401 k\": \"retirement plans\",\"snoball\": \"shaved ice\",\"rich paul net worth\": \"sports agents\",\"trucking services\": \"freight transportation\",\"pierogi near me\": \"restaurants\",\"rob rackstraw\": \"voice actors\",\"dreery\": \"na\",\"audioextractor\": \"audio tools\",\"india vs\": \"cricket matches\",\"anime shoe\": \"shoes\",\"goldplated\": \"jew\"},\"finish_reason\":\"length\",\"index\":0}]}";

//        File outFile = new File("D:\\workspace\\extract\\api\\openAI_0419.csv");
//        if(outFile.exists()){
//            outFile.delete();
//        }
//        File file = new File("D:\\workspace\\extract\\api\\errorJsonResultsKw2.txt");
//        List<String> lineList = FileUtils.readLines(file);
//        int i = 0;
//        for(String line :  lineList){
//            i ++;
//            if(i != 7){
//                continue;
//            }
//            try {
//                getCategoryFromOpenAI.formatResponse(line, outFile);
//            }catch (Exception e){
//                log.info("===errorLine:" + i);
//            }
//
//        }
//        String testContent = "" +
//                "{\"id\":\"chatcmpl-76vXNl5HzsuFrnhPtdqgp4SEH9xKD\",\"object\":\"chat.completion\",\"created\":1681885497,\"model\":\"gpt-3.5-turbo-0301\",\"usage\":{\"prompt_tokens\":1442,\"completion_tokens\":2655,\"total_tokens\":4097},\"choices\":[{\"message\":{\"role\":\"assistant\",\"content\":\"{\\n    \\\"mother in law's tongue\\\": \\\"gardening\\\",\\n    \\\"zupan's market\\\": \\\"grocery\\\",\\n    \\\"mary's pizza\\\": \\\"italian cuisine\\\",\\n    \\\"mama's fish house maui\\\": \\\"seafood\\\",\\n    \\\"buc ee's texas\\\": \\\"gas station\\\",\\n    \\\"applebee's specials\\\": \\\"casual dining\\\",\\n    \\\"keke palmer's boyfriend\\\": \\\"na\\\",\\n    \\\"peter's principles\\\": \\\"self-help\\\",\\n    \\\"münchausen\\\": \\\"na\\\",\\n    \\\"expedia.com flights\\\": \\\"travel\\\",\\n    \\\"sebastian roché\\\": \\\"na\\\",\\n    \\\"bruce's beach\\\": \\\"beach\\\",\\n    \\\"vitamin. b3\\\": \\\"health supplements\\\",\\n    \\\"plug-in wall sconces\\\": \\\"home decor\\\",\\n    \\\"sarasota/bradenton international airport\\\": \\\"airport\\\",\\n    \\\"zyrtec vs. claritin\\\": \\\"allergy medication\\\",\\n    \\\"dry-erase marker\\\": \\\"office supplies\\\",\\n    \\\"nbc.com\\\": \\\"television network\\\",\\n    \\\"señor tequila\\\": \\\"mexican cuisine\\\",\\n    \\\"uconn women's bb\\\": \\\"college basketball\\\",\\n    \\\"zara.com\\\": \\\"fashion\\\",\\n    \\\"mueblerías cerca de mí\\\": \\\"furniture stores\\\",\\n    \\\"1/4inch to mm\\\": \\\"measurement conversion\\\",\\n    \\\"grand wailea - a waldorf astoria resort\\\": \\\"luxury resort\\\",\\n    \\\"outfit 50's\\\": \\\"retro fashion\\\",\\n    \\\"bachelor's in nursing degree\\\": \\\"nursing education\\\",\\n    \\\"at&t dtv\\\": \\\"television provider\\\",\\n    \\\"pee wee's playhouse\\\": \\\"children's television show\\\",\\n    \\\"r/nsfw\\\": \\\"adult content\\\",\\n    \\\"kids' tent\\\": \\\"outdoor gear for kids\\\",\\n    \\\"mew pokémon go\\\": \\\"mobile game\\\",\\n    \\\"how to clean dog's ears\\\": \\\"pet care\\\",\\n    \\\"maker's mark distillery\\\": \\\"whiskey distillery\\\",\\n    \\\"tennessee women's basketball\\\": \\\"college basketball\\\",\\n    \\\"baker's crust\\\": \\\"bakery and cafe\\\",\\n    \\\"assassin's creed game\\\": \\\"video game\\\",\\n    \\\"a dog's way home\\\": \\\"movie\\\",\\n    \\\"mayberry's\\\": \\\"na\\\",\\n    \\\"lee's marketplace\\\": \\\"grocery store\\\",\\n    \\\"king henry's\\\": \\\"renaissance fair\\\",\\n    \\\"pokémon trading card games\\\": \\\"card games\\\",\\n    \\\"magic's game\\\": \\\"basketball game\\\",\\n    \\\"al's formal wear\\\": \\\"formal wear rental\\\",\\n    \\\"donaldson's\\\": \\\"department store\\\",\\n    \\\"sam's tires\\\": \\\"tire shop\\\",\\n    \\\"gamecocks women's basketball\\\": \\\"college basketball\\\",\\n    \\\"men's heated vest\\\": \\\"clothing\\\",\\n    \\\"sheba's\\\": \\\"ethiopian cuisine\\\",\\n    \\\"celia's\\\": \\\"mexican cuisine\\\",\\n    \\\"my.xfinity.com\\\": \\\"television and internet provider\\\",\\n    \\\"morton's toe\\\": \\\"foot condition\\\",\\n    \\\"the world's brightest flashlight\\\": \\\"flashlight\\\",\\n    \\\"tonight's heat game\\\": \\\"basketball game\\\",\\n    \\\"harry's hofbrau\\\": \\\"restaurant and bar\\\",\\n    \\\"mo's seafood\\\": \\\"seafood restaurant\\\",\\n    \\\"ramsay's kitchen\\\": \\\"television show\\\",\\n    \\\"how much is a membership to sam's club\\\": \\\"warehouse club membership\\\",\\n    \\\"hugo's restaurant\\\": \\\"fine dining\\\",\\n    \\\"smoothie blenderı\\\": \\\"kitchen appliance\\\",\\n    \\\"lake.placid lodges\\\": \\\"lodging\\\",\\n    \\\"o - cirque du soleil\\\": \\\"circus show\\\",\\n    \\\"nascar results for today's race\\\": \\\"auto racing\\\",\\n    \\\"usb a 3.0\\\": \\\"computer hardware\\\",\\n    \\\"huntingtonbank.com\\\": \\\"banking\\\",\\n    \\\"mercedes-benz s class\\\": \\\"luxury car\\\",\\n    \\\"women's clothes near me\\\": \\\"clothing stores\\\",\\n    \\\"chicken sandwich mcdonald's\\\": \\\"fast food\\\",\\n    \\\"taylor's do it center\\\": \\\"home improvement store\\\",\\n    \\\"women's fashion in 1920s\\\": \\\"vintage fashion\\\",\\n    \\\"enfamil a.r. formula\\\": \\\"baby formula\\\",\\n    \\\"santa's workshop\\\": \\\"holiday attraction\\\",\\n    \\\"madison's avenue\\\": \\\"na\\\",\\n    \\\"the women's\\\": \\\"clothing store\\\",\\n    \\\"2020 honda cr-v\\\": \\\"automobile\\\",\\n    \\\"watchman's\\\": \\\"book\\\",\\n    \\\"men's tennis rankings\\\": \\\"tennis rankings\\\",\\n    \\\"login kahoot.it\\\": \\\"educational website\\\",\\n    \\\"chamblee's\\\": \\\"hardware store\\\",\\n    \\\"where's my refund ca\\\": \\\"tax refund status\\\",\\n    \\\"ducky's car wash\\\": \\\"car wash\\\",\\n    \\\"children's museum of houston\\\": \\\"museum\\\",\\n    \\\"mazda rx-7\\\": \\\"sports car\\\",\\n    \\\"virgil's real bbq\\\": \\\"barbecue restaurant\\\",\\n    \\\"clip-on sunglasses\\\": \\\"sunglasses\\\",\\n    \\\"smugglers' notch resort\\\": \\\"ski resort\\\",\\n    \\\"hotels near to at&t stadium\\\": \\\"hotels\\\",\\n    \\\"atrevete a soñar\\\": \\\"television show\\\",\\n    \\\"welly's\\\": \\\"rain boots\\\",\\n    \\\"bankofamerica/activate\\\": \\\"banking\\\",\\n    \\\"dr. pepper museum\\\": \\\"museum\\\",\\n    \\\"ocean's twelve\\\": \\\"movie\\\",\\n    \\\"i-797a\\\": \\\"immigration form\\\",\\n    \\\"short-term memory loss\\\": \\\"medical condition\\\",\\n    \\\"ordinary's peeling solution\\\": \\\"skincare product\\\",\\n    \\\"oregon's women's basketball\\\": \\\"college basketball\\\",\\n    \\\"creditonebank.com\\\": \\\"credit card\\\",\\n    \\\"alibaba.com\\\": \\\"online marketplace\\\",\\n    \\\"high-protein vegetables\\\": \\\"nutrition\\\",\\n    \\\"café au lait spot\\\": \\\"skin condition\\\",\\n    \\\"how.much water is too much\\\": \\\"health question\\\",\\n    \\\"vh1.com/activate\\\": \\\"television network\\\",\\n    \\\"hdmi 2.1 cable\\\": \\\"computer hardware\\\",\\n    \\\"artery vs. vein\\\": \\\"anatomy\\\",\\n    \\\"high-fiber diet\\\": \\\"nutrition\\\",\\n    \\\"synovial joints.\\\": \\\"anatomy\\\",\\n    \\\"women's clothing near me\\\": \\\"clothing stores\\\",\\n    \\\"irs.gov status of refund\\\": \\\"tax refund status\\\",\\n    \\\"louise's\\\": \\\"restaurant\\\",\\n    \\\"97.5 the fanatic\\\": \\\"sports radio station\\\",\\n    \\\"siggi's yogurt\\\": \\\"yogurt brand\\\",\\n    \\\"mccray's tavern\\\": \\\"restaurant and bar\\\",\\n    \\\"macy's department store\\\": \\\"department store\\\",\\n    \\\"peyton's\\\": \\\"grocery store\\\",\\n    \\\"crazy rock'n sushi\\\": \\\"sushi restaurant\\\",\\n    \\\"federico's mexican food\\\": \\\"mexican cuisine\\\",\\n    \\\"what's the average lifespan of a cat\\\": \\\"pet question\\\",\\n    \\\"alarm.com\\\": \\\"home security\\\",\\n    \\\"fabric.com\\\": \\\"fabric store\\\",\\n    \\\"men's winter hats\\\": \\\"clothing\\\",\\n    \\\"all-inclusive resorts mexico adults-only\\\": \\\"resorts\\\",\\n    \\\"coupon code for ebay.in\\\": \\\"online marketplace coupon\\\",\\n    \\\"shula's steakhouse\\\": \\\"steakhouse\\\",\\n    \\\"what's the interest rate on student loans\\\": \\\"student loan interest rate\\\",\\n    \\\"scarpa's\\\": \\\"shoe store\\\",\\n    \\\"charlie's angels 2\\\": \\\"movie\\\",\\n    \\\"sophie's cuban cuisine\\\": \\\"cuban cuisine\\\",\\n    \\\"men's wool coat\\\": \\\"clothing\\\",\\n    \\\"re-run\\\": \\\"television show\\\",\\n    \\\"jeep. car\\\": \\\"automobile\\\",\\n    \\\"is tylenol an anti-inflammatory\\\": \\\"medication question\\\",\\n    \\\"www.amazon.com/mytv enter code\\\": \\\"television streaming\\\",\\n    \\\"rodeo dental & orthodontics\\\": \\\"dental and orthodontic practice\\\",\\n    \\\"1.5 as a fraction\\\": \\\"mathematics\\\",\\n    \\\"kelly's heroes\\\": \\\"movie\\\",\\n    \\\"bulova women's watch\\\": \\\"watch brand\\\",\\n    \\\"walter mitty's\\\": \\\"movie\\\",\\n    \\\"men's accessories\\\": \\\"clothing accessories\\\",\\n    \\\"last. minute cruises\\\": \\\"cruises\\\",\\n    \\\"jetblue. reservations\\\": \\\"airline reservations\\\",\\n    \\\"ohio state basketball women's\\\": \\\"college basketball\\\",\\n    \\\"tin tức the giới\\\": \\\"news\\\",\\n    \\\"wall.murals\\\": \\\"home decor\\\",\\n    \\\"bob saget's daughter\\\": \\\"na\\\",\\n    \\\"don't let m e down\\\": \\\"song\\\",\\n    \\\"mike's place\\\": \\\"restaurant and bar\\\",\\n    \\\"victor frankenstein's\\\": \\\"book\\\",\\n    \\\"not your father's root beer\\\": \\\"alcoholic beverage\\\",\\n    \\\"how many seasons of grey's anatomy\\\": \\\"television show question\\\",\\n    \\\"devil's advocate meaning\\\": \\\"phrase meaning\\\",\\n    \\\"a knight's tale cast\\\": \\\"movie cast\\\",\\n    \\\"women's basketball maryland\\\": \\\"college basketball\\\",\\n    \\\"athlete's foot cream\\\": \\\"foot cream\\\",\\n    \\\"bout time pub & grub\\\": \\\"restaurant and bar\\\",\\n    \\\"victoria's secret angel\\\": \\\"lingerie brand\\\",\\n    \\\"coupé car\\\": \\\"automobile\\\",\\n    \\\"mercedes-benz g-wagon\\\": \\\"luxury suv\\\",\\n    \\\"gran-canaria\\\": \\\"travel destination\\\",\\n    \\\"sore throat no.other symptoms\\\": \\\"medical condition\\\",\\n    \\\"oversized women's t shirt\\\": \\\"clothing\\\",\\n    \\\"men's ray ban\\\": \\\"sunglasses brand\\\",\\n    \\\"2.1 hdmi\\\": \\\"computer hardware\\\",\\n    \\\"men's black jacket\\\": \\\"clothing\\\",\\n    \\\"mike's discount foods\\\": \\\"grocery store\\\",\\n    \\\"fastsupport.com\\\": \\\"remote tech support\\\",\\n    \\\"piñacolada\\\": \\\"cocktail\\\",\\n    \\\"hodgkin's lymphoma symptoms\\\": \\\"medical condition\\\",\\n    \\\"in-home senior care\\\": \\\"senior care services\\\",\\n    \\\"at-home senior care\\\": \\\"senior care services\\\",\\n    \\\"chandler az. hotels\\\": \\\"hotels\\\",\\n    \\\"men's pendant necklaces\\\": \\\"jewelry\\\",\\n    \\\"men's air jordan 1\\\": \\\"shoe brand\\\",\\n    \\\"mildew vs. mold\\\": \\\"household mold\\\",\\n    \\\"non small. cell lung cancer\\\": \\\"medical condition\\\",\\n    \\\"hilton st. louis at the ballpark\\\": \\\"hotel\\\",\\n    \\\"yale's acceptance rate\\\": \\\"college acceptance rate\\\",\\n    \\\"ika's\\\": \\\"restaurant\\\",\\n    \\\"drake's dealership\\\": \\\"restaurant and bar\\\",\\n    \\\"joey b's\\\": \\\"restaurant and bar\\\",\\n    \\\"today's world\\\": \\\"news program\\\",\\n    \\\"doan's pills\\\": \\\"pain relief medication\\\",\\n    \\\"pinball pete's\\\": \\\"arcade\\\",\\n    \\\"lillian's\\\": \\\"clothing store\\\",\\n    \\\"dining-room-ideas\\\": \\\"home decor ideas\\\",\\n    \\\"ikea.twin bed\\\": \\\"furniture\\\",\\n    \\\"teléfono\\\": \\\"na\\\",\\n    \\\"it's my life\\\": \\\"song\\\",\\n    \\\"pro-bot\\\": \\\"robotics\\\",\\n    \\\"washington d.c. georgetown\\\": \\\"neighborhood\\\",\\n    \\\"u-haul truck rentals near me\\\": \\\"truck rental\\\",\\n    \\\"millie's cafe\\\": \\\"restaurant\\\",\\n    \\\"white women's blazers\\\": \\\"clothing\\\",\\n    \\\"perani's\\\": \\\"sporting goods store\\\",\\n    \\\"malley's chocolate\\\": \\\"chocolate brand\\\",\\n    \\\"hell's kitchen menu\\\": \\\"restaurant menu\\\",\\n    \\\"today's golfer\\\": \\\"golf magazine\\\",\\n    \\\"hanlon's razor\\\": \\\"philosophy\\\",\\n    \\\"oh driver's license renewal\\\": \\\"driver's license renewal\\\",\\n    \\\"men's kimono\\\": \\\"clothing\\\",\\n    \\\"upside-down christmas tree\\\": \\\"christmas tree\\\",\\n    \\\"emperor's new clothes\\\": \\\"song\\\",\\n    \\\"sam's club return policy\\\": \\\"return policy\\\",\\n    \\\"vincent's clam bar\\\": \\\"seafood restaurant\\\",\\n    \\\"shakespeare's sonnets\\\": \\\"poetry\\\",\\n    \\\"symptoms for bell's palsy\\\": \\\"medical condition\\\",\\n    \\\"park.city mountain resort\\\": \\\"ski resort\\\",\\n    \\\"nc tar heels men's basketball\\\": \\\"college basketball\\\",\\n    \\\"st. peter's church\\\": \\\"church\\\",\\n    \\\"demián bichir\\\": \\\"actor\\\",\\n    \\\"ritz-carlton philadelphia\\\": \\\"hotel\\\",\\n    \\\"ez-up\\\": \\\"canopy tent\\\",\\n    \\\"strep throat self-care\\\": \\\"medical self-care\\\",\\n    \\\"mysedgwick/walmart\\\": \\\"employee benefits\\\",\\n    \\\"can cats eat chocolate?\\\": \\\"pet question\\\",\\n    \\\"dishanywhere.com activate\\\": \\\"television streaming\\\",\\n    \\\"mustang-car\\\": \\\"autom\\\"}\"},\"finish_reason\":\"length\",\"index\":0}]}" +
//                "" +
//                "";
//        if(testContent.contains("'")){
//            testContent = testContent.replaceAll("'","");
//        }


//        Gson gson1 = new Gson();
//        testContent = gson1.toJson(testContent);


//        checkKw();

//        sortKw("D:\\workspace\\extract\\api\\postKw.txt");

        if(args[0].equalsIgnoreCase("1")){//sort file
            sortKw(args[1]);
        }else if(args[0].equalsIgnoreCase("2")){//post + extract file
            GetCategoryFromOpenAI getCategoryFromOpenAI = new GetCategoryFromOpenAI();
            if(new File(args[1]).isDirectory()){
                File[] files = new File(args[1]).listFiles();
                for (File f : files) {
                    getCategoryFromOpenAI.readFile(f.getAbsolutePath());
                }
            }else {
                getCategoryFromOpenAI.readFile(args[1]);
            }
        }

//        getCategoryFromOpenAI.readFile("D:\\workspace\\extract\\api\\testkw.txt");
//        getCategoryFromOpenAI.readFile("D:\\workspace\\extract\\api\\kwd-title-10k-v2.txt");

    }

    private static void checkKw() throws Exception{
        String rqKw = "po rn hub,2youtube,yu ou,youtube,ylh,ybe,youtip,yuoto,ube youtube,the only youtube,you tubbbbbbe,youtube ube,otbe,youtour,utbe tube,youtubepp,y our,book the face,facebook,wrathe,restaurants,fa book,restaurants nearby,vether,nearest restaurants,weather,weatheri,weather info,eathe,weatherinfo,area weather,amazon,wlamrat,wordling,wordle,walmart,walmart4,et target,porn,target,home depot,g9ogle,google in,google,det home,ixxn,homedepot,gugou,gmai9l,0gmail,ailgm,gmail3,gmail2,gmail4,gmails,gamik,gmail,starbus bus,hotels,gmail1,starbucks,gmail5,starbus,costco,goo translate,mcdonalds,to google translate,transliterator google,go ogle translate,google translate on,google translate,from google translate,google translat3e,translation of google,translator in google,translate of google,fooding near me,foodstuffs near me,yagoo,fox news,walgreens,mail mail yahoo,fox,yaho9 mail,7yahoo mail,6yahoo mail,mail yh,food near me,yahoo mail inbox,fox news today,home dwot,yahoo,yahof,yahoo mail,from fox news,fox newscast,chick fil,yah mal,y7ahoo,walgree a,foxtv news,mail in yahoo,the restaurants near me,transmate,nearest restaurant from me,google mpa s,goooooooooooooooooooooogle maps,google on the map,translated into,restaurants near me,bestbuys,resturant near me,google maps3,nearby restaurants menu,translatio,trasler,translite,translatory,translr,t translate,goo maps,best buy,translatero,google maps,etranslation,food,foodx,goodmaps,translate,google m a p s,weather in tomorrow,america back,weather tomorrow,c vs,nbav,nfl,chorionic villi sampling,nfls,espn,tradu,chronische vermoeidheidssyndroom,3ebay,instagram,cnn,cvs,wittee,ebay0,bank of america,twity,taco bell,twitter,nvba,bank of america na,twitt3er,ebay7,nbam,nba,tomorrows weather,ebay,ebay3,twt,ebay4,tvit,twet,bank to america,hoame,spain english,corfee,news,sams club,csams club,himr,offee,nexs,bewd,scores from nfl,gome,a in spanish to english,scores on nfl,spanning in english,west fargo,home,joe trades,spain in english,wells fargo,spanishdict to english,spanish to english,joe trade,nfl a scores,english for spanish,english into spanish,neee,station gasoline,coffee,georgia station,lowes,news ws,nfl scores,amazon prime,cof fee,dollar tree,gas station,nfi scores,the prime amazon,spanish into english,breakfast,subway,fe dex,craigslist,macys,burger king,break the fast,subwaying,us tracker,google docs s,google docs,breafkast,chipotle,zillow,fedec,ideep,good willing,calcumate,kings burger,breakfasting,good wills,goodwill,calculation,tracking post us,postal tracking usps,kohls,crail list,indeed s,gooooooooooooooooooooogle docs,break fasting,track us,cacator,usps tracking,indeed,fedex,gregg list,google docs is,gases,king burger,us tracking,google doc,calculator,gas,band of america,mars hall";
        String[] arr= rqKw.split(",");
        log.info("===arr size:" + arr.length);

        File file = new File("D:\\workspace\\extract\\api\\openAI_0418.csv");
        List<String> list = FileUtils.readLines(file, "utf-8");
        List<String> list1 = new ArrayList<>();
        for(String line: list){
            String kw1 = line.split(SPLIT)[0];
            list1.add(kw1);
        }
        for(String kw: arr){
            if(!list1.contains(kw)){
                log.info("===noResult:" + kw);
            }
        }

    }

    private static void sortKw(String filePath) {
        File inFile = new File(filePath);
        File outKwFile = new File(LOCAL_PATH + "gKw.txt");
        File outSymbolKwFile = new File(LOCAL_PATH + "sKw.txt");
        List<String> kwList = new ArrayList<>();
        List<String> symbolKwList = new ArrayList<>();
        try {
            InputStreamReader inputReader = new InputStreamReader(new FileInputStream(inFile));
            BufferedReader bf = new BufferedReader(inputReader);
            String line;
            int linNo = 0;
            while ((line = bf.readLine()) != null) {
                linNo++;
                if(StringUtils.isBlank(line)){
                    continue;
                }
                if (isLetterDigit(line)) {
                    kwList.add(line);
                } else {
                    log.info("SK:" + line);
                    symbolKwList.add(line);
                }

                if (kwList.size() >= 10000) {
                    FileUtils.writeLines(outKwFile, kwList, true);
                    kwList = new ArrayList<>();
                }

                if (symbolKwList.size() >= 10000) {
                    FileUtils.writeLines(outSymbolKwFile, kwList, true);
                    symbolKwList = new ArrayList<>();
                }

            }
            if (CollectionUtils.isNotEmpty(kwList)) {
                FileUtils.writeLines(outKwFile, kwList, true);
            }
            if (CollectionUtils.isNotEmpty(symbolKwList)) {
                FileUtils.writeLines(outSymbolKwFile, symbolKwList, true);
            }

            bf.close();
            inputReader.close();

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void readFile(String filePath) {
        log.info("====readFile:" + filePath);
        int errorTime = 0;
        File inFile = new File(filePath);
        File errorFile = new File(LOCAL_PATH + "errorKW" + inFile.getName() + ".txt");
        File errorJsonFile = new File(LOCAL_PATH + "errorJsonResult" + inFile.getName() + ".txt");
        File outFile = new File(LOCAL_PATH + "result" + inFile.getName() + ".txt");
        File noTitleKw = new File(LOCAL_PATH + "noTitleKW" + inFile.getName() + ".txt");

        List<String> kwList = new ArrayList<>();
        List<String> errorKwList = new ArrayList<>();
        List<String> errorJsonList = new ArrayList<>();
        try {
            InputStreamReader inputReader = new InputStreamReader(new FileInputStream(inFile));
            BufferedReader bf = new BufferedReader(inputReader);

            String line;
            int linNo = 0;
            while ((line = bf.readLine()) != null) {
                linNo++;
                if(StringUtils.isBlank(line)){
                    continue;
                }
//                line = line.replaceAll("'',","");
//                line = line.replaceAll(",''","");
//                line = line.replaceAll("\\\\'","");
//                line =  line.replaceAll("'","");
//                line =  line.replaceAll("\"","");
//                log.info("======line:" + line);

                kwList.add(line);

                if (kwList.size() >= REQUEST_KW_CNT) {
                    log.info("===linNo1:" + linNo);
                    RequestContent requestContent = setBodyMap(kwList, noTitleKw);
                    String response = "";
                    try {
                        response = startPost(gson.toJson(requestContent));
                    } catch (Exception e) {
                        errorTime++;
                        log.error("errorTime1:" + errorTime);
                        errorKwList.addAll(kwList);
                        FileUtils.writeLines(errorFile, errorKwList, true);
                        errorKwList = new ArrayList<>();
                        kwList = new ArrayList<>();
//                        if(errorTime >= 50){
//                            System.exit(0);
//                        }else {
                            continue;
//                        }
                    }

                    try {
                        formatResponse(response, outFile);
                    }catch (Exception ex){
                        log.error("forError,SPLIT to 20");
                        List<List<String>> splitLists = seoclarity.backend.utils.CollectionUtils.splitCollectionBySize(kwList, REQUEST_KW_CNT/2);
                        for(List<String> splitList: splitLists){
                            log.info("===splitKw1:" + splitList.size());
                            requestContent = setBodyMap(splitList, noTitleKw);
                            try {
                                response = startPost(gson.toJson(requestContent));
                            } catch (Exception e) {
                                errorTime++;
                                log.error("errorTime1:" + errorTime);
                                errorKwList.addAll(kwList);
                                FileUtils.writeLines(errorFile, errorKwList, true);
                                errorKwList = new ArrayList<>();
//                                kwList = new ArrayList<>();
                                continue;
//                                if(errorTime >= 3){
//                                    System.exit(0);
//                                }else {
//                                    continue;
//                                }
                            }

                            try {
                                formatResponse(response, outFile);
                            }catch (Exception exc){
                                log.error("forError2,skip");
                                exc.printStackTrace();
                                errorJsonList.addAll(splitList);
                                FileUtils.writeLines(errorJsonFile, errorJsonList, true);
                                errorJsonList = new ArrayList<>();
                            }
                        }
                    }
                    kwList = new ArrayList<>();
                }
            }

            if (CollectionUtils.isNotEmpty(kwList)) {
                log.info("===linNo2:" + linNo);
                RequestContent requestContent = setBodyMap(kwList, noTitleKw);
                String response = "";
                try {
                    response = startPost(gson.toJson(requestContent));
                } catch (Exception e) {
                    errorTime++;
                    log.error("errorTime2:" + errorTime);
                    errorKwList.addAll(kwList);
                    FileUtils.writeLines(errorFile, errorKwList, true);
                    if(errorTime >= 20){
                        log.error("******error too many times,exit.");
                        System.exit(0);
                    }
                }

                try {
                    formatResponse(response, outFile);
                }catch (Exception ex){
                    log.error("forError2");
                    errorJsonList.addAll(kwList);
                    FileUtils.writeLines(errorJsonFile, errorJsonList, true);
                }
            }

            bf.close();
            inputReader.close();

        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    private RequestContent setBodyMap(List<String> kwList, File noTitleKw) {

        RequestContent requestContent = new RequestContent();
        requestContent.setModel("gpt-3.5-turbo");
        requestContent.setTemperature(0);
        requestContent.setTop_p(1);
        requestContent.setFrequency_penalty(0);
        requestContent.setPresence_penalty(0);

        List<Message> messages = new ArrayList<>();
        Message message = new Message();
        message.setRole("system");
        message.setContent("You are an expert at product and service categorization who has detailed knowledge of the Google Ads Product and Service taxonomy. As input, you will be provided a list of phrases and a sample sentence.\n For example \n Phrase 1: [Sample Sentence for Phrase 1]\n Phrase 2: [Sample Sentence for Phrase 2] \n For each phrase, return the top 2 most appropriate hierarchies that it relates to. \n Follow the instructions below carefully:\n 1) ONLY if you are unsure of the hierarchy for a phrase, use the sentence provided to derive the context of the phrase and for selecting the appropriate hierarchies. \n 2) If still unsure, check what you know about phrase\n 3) If still unsure, output \"N/A\" \n 4) Limit each hierarchy to 4 levels only\n 5) Output the category hierarchy separated by > \n 6) If a phrase could be categorized into multiple hierarchies, output all the hierarchies. \n 7) Do not explain anything. \n 8) Output in JSON format as follows {phrase 1: [\"hierarchy 1\", \"hierarchy 2\"],phrase 2: [\"hierarchy 1\", \"hierarchy 2\"]}");
        messages.add(message);

        String content = "";
        int index = 0;
        for(String line : kwList){
            List<String> noTitleKwList = new ArrayList<>();
            index ++;

            line = line.replaceAll("'',","");
            String[] arr = line.split(SPLIT);
            String kw = arr[0];
            String titleAll = arr[1];
            String[] titleArr = titleAll.split(TITLE_SPLIT);
            String formatTitle = "";
            int i = 0;
            for(String title: titleArr){

                if(i>= 2){
//                    log.info("===formatTitle>=2:" + formatTitle);
                    break;
                }
                if(StringUtils.isBlank(title)){
                    continue;
                }
//                if(title.equals("['")){
//                    continue;
//                }
                if(title.contains("wikipedia") || title.contains("Wikipedia")){
                    log.info("===skipWikipedia:" + title);
                    continue;
                }

                title = title.replaceAll(",''","")
                        .replaceAll("\\\\'","").replaceAll("'","").replaceAll("\"","");

                formatTitle += title + ",";
                i++;
            }

            if(i == 0){
                index --;
                log.info("=====no title kw:" + line);
                noTitleKwList.add(kw);
                try {
                    FileUtils.writeLines(noTitleKw, noTitleKwList, true);
                }catch (Exception e){
                    e.printStackTrace();
                }
                continue;
            }

            if(!formatTitle.substring(0,1).equals("[")){
                formatTitle = "[" + formatTitle;
            }
            formatTitle = formatTitle.substring(0, formatTitle.length() -2);
            formatTitle = formatTitle + "]";

//            kw = kw.replaceAll("'","");
//            title = title.replaceAll("'","");
            content += index + ")" + kw + ":" + formatTitle + " ";
        }
        log.info("*****content:" + content);

//        String kwContent = StringUtils.join(kwList, ",");
        Message kwMessage = new Message();
        kwMessage.setRole("user");
        kwMessage.setContent(content);
        messages.add(kwMessage);
        log.info("====messages:" + gson.toJson(messages));

        requestContent.setMessages(messages);
        return requestContent;
    }

    private static String startPost(String bodyStr) throws Exception {

        log.info("************* bodyStr:" + bodyStr);
        long startTime = System.currentTimeMillis();
        String resultString = "";
        Response response = null;
        try {
            OkHttpClient client = new OkHttpClient().newBuilder().callTimeout(5, TimeUnit.MINUTES)
                    .connectTimeout(10, TimeUnit.MINUTES)
                    .readTimeout(10, TimeUnit.MINUTES)
                    .writeTimeout(10, TimeUnit.MINUTES)
                    .build();
            MediaType mediaType = MediaType.parse("application/json");
            RequestBody body = RequestBody.create(mediaType, bodyStr);
            Request request = new Request.Builder()
                    .url(REQUEST_URL)
                    .method("POST", body)
                    .addHeader("Authorization", API_KEY)
                    .addHeader("Content-Type", "application/json")
                    .build();

            response = client.newCall(request).execute();
            if (!response.isSuccessful()) {
                throw new IOException("Unexcepted code:" + response);
            }
            resultString = response.body().string();
            log.info("====TIME:" + ((System.currentTimeMillis() - startTime)/1000) + "s");
            System.out.println("====response:" + response);
            return resultString;

        } catch (Exception e) {
            System.out.println("=====Failed post:" + bodyStr);
            e.printStackTrace();
            throw e;
        } finally {
            if (response != null) {
                log.info("====close");
                response.body().close();
                response.close();
            }
        }
//        return null;
    }

    private void formatResponse(String content, File outFile) throws Exception {
        Gson gson1 = new GsonBuilder().disableHtmlEscaping().create();
        try {
            ResponseContent responseContent = gson1.fromJson(content, ResponseContent.class);
            List<Choices> choicesList = responseContent.getChoices();
            if (CollectionUtils.isNotEmpty(choicesList)) {
                for (Choices choices : choicesList) {
                    if (choices.getMessage() == null) {
                        log.error("===MSGNULL" + gson1.toJson(responseContent));
                        continue;
                    }
                    String messageContent = choices.getMessage().getContent();
                    if (StringUtils.isBlank(messageContent)) {
                        log.error("===messageContentNULL" + gson1.toJson(responseContent));
                        continue;
                    }
                    log.info("======" + gson1.toJson(messageContent));
                    Map<String, Object> map = JSONObject.parseObject(messageContent, new TypeReference<Map<String, Object>>() {
                    });
                    log.info("222222222222  " + gson1.toJson(map));
                    List<String> dataList = new ArrayList<>();
                    for (String key : map.keySet()) {
                        String keyword = key;
                        String category = map.get(key).toString();
                        dataList.add(appendData(keyword, category));
                    }
                    FileUtils.writeLines(outFile, dataList, true);
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
            log.error("====formatResponseError:" + content);
            throw e;
        }
    }

    public static String appendData(String keyword, String category) {
        StringBuffer line = new StringBuffer();
        line.append(keyword).append(SPLIT);
        line.append(category);
        return line.toString();
    }


    public static boolean isLetterDigit(String str) {
        str = str.replaceAll(" ", "");
        String regex = "^[a-z0-9A-Z]+$";
        return str.matches(regex);
    }

}


/**
 * {
 * "id": "chatcmpl-76VFk8uBNnSJ7hXpDoB5kHzA3ZCyJ",
 * "object": "chat.completion",
 * "created": 1681784460,
 * "model": "gpt-3.5-turbo-0301",
 * "usage": {
 * "prompt_tokens": 1156,
 * "completion_tokens": 2198,
 * "total_tokens": 3354
 * },
 * "choices": [
 * {
 * "message": {
 * "role": "assistant",
 * "content": "{\n\"i one\": \"na\",\n\"baklava near me\": \"food\",\n\"make an appointment at social security office\": \"government\",\n\"new elvis movie\": \"movies\",\n\"execute order\": \"na\",\n\"uruguay vs\": \"sports\",\n\"the game on eminem\": \"music\",\n\"aliens on tape\": \"na\",\n\"tv series once upon a time\": \"tv shows\",\n\"jonquel jones\": \"sports\",\n\"ark animals\": \"animals\",\n\"keto dessert recipes\": \"food\",\n\"men's sweaters\": \"fashion\",\n\"coach.com\": \"fashion\",\n\"apprenticeships\": \"jobs\",\n\"gintama\": \"anime\",\n\"ocean's eleven\": \"movies\",\n\"o'jays\": \"music\",\n\"versacarry\": \"na\",\n\"spies like us\": \"movies\",\n\"stallone\": \"movies\",\n\"mary did you know\": \"music\",\n\"borough\": \"na\",\n\"ouran host club\": \"anime\",\n\"titans football\": \"sports\",\n\"mens fur coat\": \"fashion\",\n\"angel of death\": \"na\",\n\"bj's locations\": \"shopping\",\n\"jordans.com\": \"shopping\",\n\"noel neill\": \"na\",\n\"this too shall pass\": \"na\",\n\"paid in full\": \"movies\",\n\"my mixtapez\": \"music\",\n\"hogan's heroes\": \"tv shows\",\n\"saint patrick's cathedral\": \"landmarks\",\n\"tractorsupply.com\": \"shopping\",\n\"fingerhut.com\": \"shopping\",\n\"myciti.com\": \"na\",\n\"naver.com\": \"na\",\n\"www.live.com\": \"na\",\n\"nest.com\": \"home automation\",\n\"my.pitt.edu\": \"education\",\n\"www.capitalonedirect.com\": \"banking\",\n\"realystate.com\": \"real estate\",\n\"membership to bj's\": \"shopping\",\n\"robert's rules\": \"na\",\n\"frankenstein's monster\": \"na\",\n\"women's jumpsuits petite\": \"fashion\",\n\"nermal\": \"na\",\n\"google office\": \"na\",\n\"cass elliot\": \"music\",\n\"nelson piquet\": \"sports\",\n\"desjardins\": \"banking\",\n\"passengers\": \"movies\",\n\"valet parking\": \"na\",\n\"securities and exchange commission\": \"government\",\n\"populous\": \"na\",\n\"cavani\": \"sports\",\n\"chitarra\": \"na\",\n\"point lookout\": \"na\",\n\"ordinance\": \"law\",\n\"wastewater treatment plant\": \"na\",\n\"paolo guerrero\": \"sports\",\n\"drew carey show\": \"tv shows\",\n\"matthew garber\": \"na\",\n\"fire dragon\": \"na\",\n\"mae whitman\": \"na\",\n\"amanita\": \"na\",\n\"the frighteners\": \"movies\",\n\"hammam\": \"na\",\n\"roguelike\": \"video games\",\n\"sableye\": \"video games\",\n\"gromit\": \"na\",\n\"ian holm\": \"na\",\n\"american staffordshire terrier\": \"animals\",\n\"migos\": \"music\",\n\"mx player\": \"na\",\n\"amado carrillo\": \"na\",\n\"easy origami\": \"crafts\",\n\"conceptual\": \"na\",\n\"demiurge\": \"na\",\n\"jurassic world\": \"movies\",\n\"turner and hooch\": \"movies\",\n\"traci wolfe\": \"na\",\n\"outer\": \"na\",\n\"capricorn birthstone\": \"astrology\",\n\"thatched roof\": \"architecture\",\n\"ketubah\": \"religion\",\n\"lavender fields\": \"travel\",\n\"earnestly\": \"na\",\n\"youtube desktop\": \"technology\",\n\"smartphone stylus\": \"technology\",\n\"haka dance\": \"na\",\n\"newport weather\": \"weather\",\n\"jaden smith age\": \"na\",\n\"brewdog\": \"na\",\n\"the privilege\": \"na\",\n\"cinema 6\": \"movies\",\n\"kim jong nam\": \"na\",\n\"lone tree\": \"na\",\n\"how to do a screenshot on a mac\": \"technology\",\n\"madara uchiha\": \"anime\",\n\"terrazza\": \"na\",\n\"temperance movement\": \"na\",\n\"brazil national team\": \"sports\",\n\"world cup live\": \"sports\",\n\"flylady\": \"na\",\n\"killdozer\": \"na\",\n\"squid ink\": \"food\",\n\"vox media\": \"media\",\n\"capital of california\": \"geography\",\n\"women's mule shoes\": \"fashion\",\n\"people's pharmacy\": \"health\",\n\"st luke's\": \"health\",\n\"rosatispizza\": \"food\",\n\"untried\": \"na\",\n\"dualism\": \"philosophy\",\n\"dr bronner's castile soap\": \"health\",\n\"latavius murray\": \"sports\",\n\"ameer abdullah\": \"sports\",\n\"channel 7 news miami\": \"news\",\n\"children's museum of atlanta\": \"museums\",\n\"the lincoln lawyer on netflix\": \"movies\",\n\"misty ray\": \"na\",\n\"framegame\": \"na\",\n\"topgolf ontario\": \"sports\",\n\"chevy traverse 2022\": \"cars\",\n\"isaiah wong\": \"sports\",\n\"snowport\": \"na\",\n\"supermercado morelos\": \"shopping\",\n\"iayze\": \"na\",\n\"stock barrel\": \"na\",\n\"steam deck docked\": \"video games\",\n\"vikings valhalla cast\": \"tv shows\",\n\"goodwill donation trailer\": \"charity\",\n\"when is passover 2022\": \"holidays\",\n\"installed adobe reader\": \"technology\",\n\"twin valleys\": \"na\",\n\"social cantina\": \"food\",\n\"patisserie fouet\": \"food\",\n\"1 5 percent\": \"na\",\n\"kibbe body type test\": \"health\",\n\"talkiatry\": \"health\",\n\"cobra kai season 4 cast\": \"tv shows\",\n\"the owl house hunter\": \"tv shows\",\n\"ponchera cerca de mi\": \"na\",\n\"steven wallis\": \"na\",\n\"casino resort hotels\": \"travel\",\n\"the greatest beer run ever\": \"movies\",\n\"2023 dodge charger\": \"cars\",\n\"valorant tracker gg\": \"video games\",\n\"emily in paris outfit\": \"fashion\",\n\"vota casa de los famosos\": \"tv shows\",\n\"browns diamonds\": \"jewelry\",\n\"de soto sale\": \"na\",\n\"california minimum wage 2022\": \"politics\",\n\"chi cha san chen\": \"food\",\n\"lingerie shein\": \"fashion\",\n\"metroexpresscarwash\": \"na\",\n\"precancerous mouth cancer\": \"health\",\n\"restaurants barbeque near me\": \"food\",\n\"tyler barriss\": \"na\",\n\"born in east la\": \"movies\",\n\"jobs with unions\": \"jobs\",\n\"account brokers\": \"finance\",\n\"comal park\": \"na\",\n\"dedham ma\": \"na\",\n\"deerfield il\": \"na\",\n\"twin murphy bed\": \"furniture\",\n\"cnh dealer portal\": \"business\",\n\"lens protectors\": \"technology\",\n\"mortys\": \"na\",\n\"ann sather restaurant\": \"food\",\n\"first wok\": \"food\",\n\"tokyo sushi\": \"food\",\n\"gilbert restaurants\": \"food\",\n\"efficacy\": \"na\",\n\"titular\": \"na\",\n\"illegitimate\": \"na\",\n\"decimate\": \"na\",\n\"gnarl meaning\": \"na\",\n\"the fiends\": \"na\",\n\"cooperating definition\": \"na\",\n\"associate\": \"na\",\n\"bottomlessness\": \"na\",\n\"disparagingly definition\": \"na\",\n\"debacle means\": \"na\",\n\"what time is the lunar eclipse\": \"astronomy\",\n\"streaming tnt\": \"tv shows\",\n\"google camera\": \"technology\",\n\"how to fax from iphone\": \"technology\",\n\"abrasive\": \"na\",\n\"kanye west shoes\": \"fashion\",\n\"junco bird\": \"animals\",\n\"sayonara\": \"na\",\n\"seance meaning\": \"na\",\n\"what is a geographer\": \"geography\",\n\"kneecaps\": \"anatomy\",\n\"mttr\": \"technology\",\n\"myhyundai\": \"cars\",\n\"cute pumpkin carvings\": \"crafts\",\n\"tenofovir/emtricitabine\": \"health\",\n\"doordash dr\": \"food\",\n\"salvaged\": \"na\",\n\"headwater\": \"na\",\n\"business start\": \"business\",\n\"ford f150 sales\": \"cars\",\n\"refused\": \"na\",\n\"british comedy\": \"tv shows\",\n\"fluff\": \"na\",\n\"snares\": \"music\",\n\"drum sticks\": \"music\",\n\"2 cellos\": \"music\",\n\"dozen\": \"mathematics\",\n\"remixes\": \"music\",\n\"bit coin exchange\": \"finance\",\n\"philippine map\": \"geography\",\n\"alcohol hepatitis\": \"health\",\n\"beagle pups for sale\": \"animals\",\n\"old nation\": \"na\",\n\"perspective definition\": \"na\",\n\"how to win the lotto\": \"na\",\n\"cool hair cuts\": \"fashion\",\n\"fetus heartbeat\": \"health\",\n\"infection disease\": \"health\",\n\"pro form\": \"na\",\n\"wart removals\": \"health\",\n\"cowboy boots for woman\": \"fashion\",\n\"chase business log in\": \"banking\",\n\"pre nuptial agreements\": \"law\",\n\"coat for woman\": \"fashion\",\n\"suits woman\": \"fashion\",\n\"woman skirt\": \"fashion\",\n\"woman clothing sale\": \"fashion\",\n\"eye brow brush\": \"beauty\",\n\"iceland airways\": \"travel\",\n\"hardie backer\": \"na\",\n\"safety key chains\": \"na\",\n\"grand daughter\": \"family\",\n\"disintegration\": \"na\",\n\"bull whips\": \"na\",\n\"heartworm preventative dogs\": \"animals\",\n\"big poppas\": \"na\",\n\"sub lease\": \"real estate\",\n\"brecken ridge\": \"travel\",\n\"three wheeler bikes\": \"vehicles\",\n\"grilled pan\": \"cooking\",\n\"skate skirt\": \"fashion\",\n\"hair style for short hair\": \"beauty\",\n\"hybrid toyotas\": \"cars\"\n}"
 * },
 * "finish_reason": "stop",
 * "index": 0
 * }
 * ]
 * }
 */
class ResponseContent {

    private String id;
    private String object;
    private Integer created;
    private String model;
    private Usage usage;
    private List<Choices> choices;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getObject() {
        return object;
    }

    public void setObject(String object) {
        this.object = object;
    }

    public Integer getCreated() {
        return created;
    }

    public void setCreated(Integer created) {
        this.created = created;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public Usage getUsage() {
        return usage;
    }

    public void setUsage(Usage usage) {
        this.usage = usage;
    }

    public List<Choices> getChoices() {
        return choices;
    }

    public void setChoices(List<Choices> choices) {
        this.choices = choices;
    }
}

class Usage {
    private Integer prompt_tokens;
    private Integer completion_tokens;
    private Integer total_tokens;

    public Integer getPrompt_tokens() {
        return prompt_tokens;
    }

    public void setPrompt_tokens(Integer prompt_tokens) {
        this.prompt_tokens = prompt_tokens;
    }

    public Integer getCompletion_tokens() {
        return completion_tokens;
    }

    public void setCompletion_tokens(Integer completion_tokens) {
        this.completion_tokens = completion_tokens;
    }

    public Integer getTotal_tokens() {
        return total_tokens;
    }

    public void setTotal_tokens(Integer total_tokens) {
        this.total_tokens = total_tokens;
    }
}

class Choices {
    private Message message;
    private String finish_reason;
    private Integer index;

    public Message getMessage() {
        return message;
    }

    public void setMessage(Message message) {
        this.message = message;
    }

    public String getFinish_reason() {
        return finish_reason;
    }

    public void setFinish_reason(String finish_reason) {
        this.finish_reason = finish_reason;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }
}

class Message {
    private String role;
    private String content;

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}

class RequestContent {
    private String model;
    private List<Message> messages;
    private Integer temperature;
    private Integer top_p;
    private Integer frequency_penalty;
    private Integer presence_penalty;

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public List<Message> getMessages() {
        return messages;
    }

    public void setMessages(List<Message> messages) {
        this.messages = messages;
    }

    public Integer getTemperature() {
        return temperature;
    }

    public void setTemperature(Integer temperature) {
        this.temperature = temperature;
    }

    public Integer getTop_p() {
        return top_p;
    }

    public void setTop_p(Integer top_p) {
        this.top_p = top_p;
    }

    public Integer getFrequency_penalty() {
        return frequency_penalty;
    }

    public void setFrequency_penalty(Integer frequency_penalty) {
        this.frequency_penalty = frequency_penalty;
    }

    public Integer getPresence_penalty() {
        return presence_penalty;
    }

    public void setPresence_penalty(Integer presence_penalty) {
        this.presence_penalty = presence_penalty;
    }
}