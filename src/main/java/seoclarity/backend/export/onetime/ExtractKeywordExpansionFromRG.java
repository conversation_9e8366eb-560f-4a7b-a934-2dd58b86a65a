package seoclarity.backend.export.onetime;

import com.alibaba.fastjson.JSON;
import com.google.gson.Gson;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.clickhouse.ClDailyRankingEntityDao;
import seoclarity.backend.dao.clickhouse.gscclicksteam.GscClickSteamDAO;
import seoclarity.backend.dao.clickhouse.monthlyranking.CentralKeywordTokenizerDAO;
import seoclarity.backend.dao.clickhouse.monthlyranking.MonthlyRankingDao;
import seoclarity.backend.entity.ClarityDBConstants;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.clickhouse.gscclicksteam.GscClickSteamEntity;
import seoclarity.backend.entity.clickhouse.gscclicksteam.KeywordStreamEntity;
import seoclarity.backend.service.ScKeywordRankManager;
import seoclarity.backend.utils.ClarityDBUtils;
import seoclarity.backend.utils.CommonUtils;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.File;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@CommonsLog
public class ExtractKeywordExpansionFromRG {

    private static final String SPLIT = "\t";
    private GscClickSteamDAO gscClickSteamDAO;
    private CentralKeywordTokenizerDAO centralKeywordTokenizerDAO;
    private OwnDomainEntityDAO ownDomainEntityDAO;
    private ScKeywordRankManager scKeywordRankManager;
    private ClDailyRankingEntityDao clDailyRankingEntityDao;

    public ExtractKeywordExpansionFromRG() {
        gscClickSteamDAO = SpringBeanFactory.getBean("gscClickSteamDAO");
        centralKeywordTokenizerDAO = SpringBeanFactory.getBean("centralKeywordTokenizerDAO");
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        scKeywordRankManager = SpringBeanFactory.getBean("scKeywordRankManager");
        clDailyRankingEntityDao = SpringBeanFactory.getBean("clDailyRankingEntityDao");
    }

    public static final Set<String> URL_SET = new HashSet<>();

    static {
        URL_SET.add("https://www.genentech-access.com/");
    }

    private void process() throws Exception {
        String device = "desktop";

        File outFile = new File("/home/<USER>/KeywordExpansionGenentech_0908.csv");
        if (outFile.exists()) {
            outFile.delete();
        }

        File domainReverseFile = new File("/home/<USER>/KeywordExpansionGenentechDR.csv");
        Set<String> allDomainList = new HashSet<>();
        long stTime = System.currentTimeMillis();
        if (!domainReverseFile.exists()) {
            for (String url : URL_SET) {
                Thread.sleep(2000);
                log.info("====startUrl:" + url);
                String domainName = gscClickSteamDAO.getDomainByUrl(url);
                String domainReverse = StringUtils.reverseDelimited(domainName, '.');
                String rootDomainReverse = CommonUtils.getReversedRootDomain(domainName);
                allDomainList.add(domainReverse);
                List<String> top3CompetitorDomainList = centralKeywordTokenizerDAO.getTop3CompetitorDomain(1, 1, domainReverse, rootDomainReverse, device);
                if (CollectionUtils.isNotEmpty(top3CompetitorDomainList)) {
                    allDomainList.addAll(top3CompetitorDomainList);
                }
            }
            FileUtils.writeLines(domainReverseFile, allDomainList, true);
        } else {
            List<String> list = FileUtils.readLines(domainReverseFile, "utf-8");
            allDomainList.addAll(list);
        }
        log.info("===allDomainListSize:" + allDomainList.size());
        log.info("====耗时1：" + (System.currentTimeMillis() - stTime) / 1000 + "s");

        String countryCd = "usa";
        for (int i = 0; i <= 499; i++) {//分成500份
            log.info("===startHashNo:" + i);
            try {
                Thread.sleep(2000);
                stTime = System.currentTimeMillis();
                List<GscClickSteamEntity> keywordList = gscClickSteamDAO.getKeywordsByDomainReverse(countryCd, i, allDomainList, device);
                log.info("===keywordStreamList size:" + keywordList.size() + ",耗时1：" + (System.currentTimeMillis() - stTime) / 1000 + "s");

                if (CollectionUtils.isNotEmpty(keywordList)) {
                    List<String> lines = new ArrayList<String>();
                    for (GscClickSteamEntity gscClickSteamEntity : keywordList) {
                        StringBuffer stringBuffer = new StringBuffer();
                        stringBuffer.append(gscClickSteamEntity.getKeywordName()).append(SPLIT);
                        stringBuffer.append(gscClickSteamEntity.getImpressions());
                        lines.add(stringBuffer.toString());
                    }
                    FileUtils.writeLines(outFile, lines, true);
                }

            } catch (Exception e) {
                e.printStackTrace();
                continue;
            }
        }

    }

    private void getTop3CompetitorExpandKeywordFile(String domainName, int engineId, int languageId, String countryCd, String device) {

        File outFile = new File("/home/<USER>/KeywordExpansion/KeywordExpansion" + domainName + "_" + engineId + "_" + languageId + "_" + device + "_" + LocalDate.now() + ".csv");
        if (outFile.exists()) {
            outFile.delete();
        }
        long stTime = System.currentTimeMillis();
        Set<String> allDomainSet = new HashSet<>();
        String domainReverse = StringUtils.reverseDelimited(domainName, '.');
        String rootDomainReverse = CommonUtils.getReversedRootDomain(domainName);
        allDomainSet.add(domainReverse);
        List<String> top3CompetitorDomainList = centralKeywordTokenizerDAO.getTop3CompetitorDomain(engineId, languageId, domainReverse, rootDomainReverse, device);
        if (CollectionUtils.isNotEmpty(top3CompetitorDomainList)) {
            allDomainSet.addAll(top3CompetitorDomainList);
        }
        log.info("===top3CompetitorDomainList:" + JSON.toJSONString(top3CompetitorDomainList));
        if (CollectionUtils.isEmpty(top3CompetitorDomainList)) {
            return;
        }

        List<String> allDomainList = new ArrayList<>();
        allDomainList.addAll(allDomainSet);
        List<List<String>> splitDomainList = seoclarity.backend.utils.CollectionUtils.splitCollectionBySize(allDomainList, 10);
        for (List<String> domainList : splitDomainList) {
            Set<String> domainSet = new HashSet<>();
            domainSet.addAll(domainList);
            log.info("=====STdomainSet:" + JSON.toJSONString(domainSet));
            for (int i = 0; i <= 499; i++) {//分成500份
                log.info("===startHashNo:" + i);
                try {
                    Thread.sleep(2000);
                    stTime = System.currentTimeMillis();
                    List<GscClickSteamEntity> keywordList = gscClickSteamDAO.getKeywordsByDomainReverse(countryCd, i, domainSet, device);
                    log.info("===keywordStreamList size:" + keywordList.size() + ",耗时1：" + (System.currentTimeMillis() - stTime) / 1000 + "s");

                    if (CollectionUtils.isNotEmpty(keywordList)) {
                        List<String> lines = new ArrayList<String>();
                        for (GscClickSteamEntity gscClickSteamEntity : keywordList) {
                            StringBuffer stringBuffer = new StringBuffer();
                            stringBuffer.append(engineId).append(SPLIT);//engineId
                            stringBuffer.append(languageId).append(SPLIT);//languageId
                            stringBuffer.append(gscClickSteamEntity.getKeywordName()).append(SPLIT);
                            stringBuffer.append(gscClickSteamEntity.getImpressions());
                            lines.add(stringBuffer.toString());
                        }
                        FileUtils.writeLines(outFile, lines, true);
                    }

                } catch (Exception e) {
                    e.printStackTrace();
                    continue;
                }
            }
        }

    }

    private static void sortImpression(File keywordFile) {

        try {
            List<String> lines = FileUtils.readLines(keywordFile, "utf-8");
            List<GscClickSteamEntity> gscClickSteamEntityList = new ArrayList<>();
            for (String line : lines) {
                String keyword = line.split(SPLIT)[0];
                Float impression = Float.parseFloat(line.split(SPLIT)[1]);
                GscClickSteamEntity gscClickSteamEntity = new GscClickSteamEntity();
                gscClickSteamEntity.setImpressions(impression);
                gscClickSteamEntity.setKeywordName(keyword);
                gscClickSteamEntityList.add(gscClickSteamEntity);
            }

            File outFile = new File("D:\\workspace\\extract\\onetime\\KeywordExpansionGenentechOrder_0908.csv");
            List<GscClickSteamEntity> gscClickSteamEntitySortList =
                    gscClickSteamEntityList.stream().sorted(Comparator.comparing(GscClickSteamEntity::getImpressions).reversed()).collect(Collectors.toList());

            List<String> extractLines = new ArrayList<String>();
            for (GscClickSteamEntity gscClickSteamEntity : gscClickSteamEntitySortList) {
                StringBuffer stringBuffer = new StringBuffer();
                stringBuffer.append(gscClickSteamEntity.getKeywordName()).append(SPLIT);
                stringBuffer.append(gscClickSteamEntity.getImpressions());
                extractLines.add(stringBuffer.toString());
            }
            FileUtils.writeLines(outFile, extractLines, true);

        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    private void getCompetitor(int engineId, int languageId, String device) throws Exception {
        for (String url : URL_SET) {
            Thread.sleep(2000);
            log.info("====startUrl:" + url);
            String domainName = centralKeywordTokenizerDAO.getDomainByUrl(url);
            String domainReverse = StringUtils.reverseDelimited(domainName, '.');
            String rootDomainReverse = CommonUtils.getReversedRootDomain(domainName);
            List<String> top3CompetitorDomainList = centralKeywordTokenizerDAO.getTop3CompetitorDomain(engineId, languageId, domainReverse, rootDomainReverse, device);
            if (CollectionUtils.isNotEmpty(top3CompetitorDomainList) && top3CompetitorDomainList.contains("com.nature.www")) {
                log.info("=111111111111domainName:" + domainName);
            }
            if (CollectionUtils.isNotEmpty(top3CompetitorDomainList) && top3CompetitorDomainList.contains("com.fiercepharma.www")) {
                log.info("=222222222domainName:" + domainName);
            }
        }
    }

    private void getAllUSDomain() throws Exception {
        File allDomainFile = new File("/home/<USER>/allDomainFile0206.txt");
        Set<String> uniqueDomainSet = new HashSet<>();
        if(allDomainFile.exists()){
            List<String> lineList = FileUtils.readLines(allDomainFile, "utf-8");
            uniqueDomainSet.addAll(lineList);
        }else {
            String rankingDate = "2024-02-04";
            List<OwnDomainEntity> allDomainList = ownDomainEntityDAO.getByIdAsc();
            log.info("===allDomainList:" + allDomainList.size());

            for (OwnDomainEntity ownDomainEntity : allDomainList) {
                int ownDomainId = ownDomainEntity.getId();
                int engineId = scKeywordRankManager.getSearchEngineId(ownDomainEntity);
                int languageId = scKeywordRankManager.getSearchLanguageId(ownDomainEntity);
                if ((engineId == 1 && languageId == 1) || (engineId == 217 && languageId == 1) || (engineId == 223 && languageId == 1)) {
                    String domainName = ownDomainEntity.getDomain();
                    boolean isBroadMatch = ownDomainEntity.isBroadMatch();
                    String domainReverse = StringUtils.reverseDelimited(domainName, '.');
                    String rootDomainReverse = CommonUtils.getReversedRootDomain(domainName);

                    List<String> competitorList = clDailyRankingEntityDao.getTop3Competitors(rankingDate, engineId, languageId, ownDomainId, rootDomainReverse);
                    uniqueDomainSet.addAll(competitorList);
                    uniqueDomainSet.add(rootDomainReverse);

                } else {
                    log.info("===NOTUSDomain,skip:" + ownDomainId);
                    continue;
                }
            }
            FileUtils.writeLines(allDomainFile, uniqueDomainSet, true);
        }
        log.info("===uniqueDomainSetSize:" + uniqueDomainSet.size());

        int trueRank = 30;
//        String[] columnName = new String[]{"RootDomainReverse", "keyword"};
        String fileName = "RGUSExpansion_0206.csv";
        int index = 0;
        for (String rootDomainReverse : uniqueDomainSet) {
            if (rootDomainReverse.contains(".google") || rootDomainReverse.contains(".yahoo") || rootDomainReverse.contains(".bing")
                    || rootDomainReverse.contains(".baidu") || rootDomainReverse.contains(".yandex") || rootDomainReverse.contains(".naver")) {
                log.info("====skipDomain:" + rootDomainReverse);
                continue;
            }
            index ++;
//            if(index > 2){
//                break;
//            }
//            Integer kwCnt = gscClickSteamDAO.getKeywordsByRootDomainReverseCount(rootDomainReverse, trueRank);
//            log.info("=====kwCnt:" + kwCnt);
//            if (kwCnt.intValue() > 1000000) {
//                log.info("***********LargeKwCnt:" + kwCnt + ",rootDomainReverse:" + rootDomainReverse);
//            }
            fileName = rootDomainReverse + ".csv";
            log.info("===================extractDomain:" + rootDomainReverse);
            String sql = " select distinct root_domain_reverse,keyword_name from monthly_ranking.d_ranking_detail_us ";
            sql += " where root_domain_reverse='" + rootDomainReverse + "' and own_domain_id=0 and engine_id=1 and language_id=1 ";
            sql += " and location_id=0 and true_rank<=" + trueRank;
            System.out.println("SQL : " + sql);
            ClarityDBUtils.executeQueryTabSeparatedRawForFile(ClarityDBConstants.LWEB_CDB_01, sql,
                    "/home/<USER>/RGUSExpansion_0206/" + fileName, true);
        }


    }

    public static void main(String[] args) throws Exception {
        ExtractKeywordExpansionFromRG extractKeywordExpansionFromRG = new ExtractKeywordExpansionFromRG();
//        extractKeywordExpansionFromRG.process();
//        extractKeywordExpansionFromRG.getCompetitor(1,1);
//        sortImpression(new File("D:\\workspace\\extract\\onetime\\KeywordExpansionGenentech_0908.csv"));

//        extractKeywordExpansionFromRG.getTop3CompetitorExpandKeywordFile("www.boels.com",17,18, "nld", "desktop");//https://www.wrike.com/open.htm?id=1226292154

//        extractKeywordExpansionFromRG.getTop3CompetitorExpandKeywordFile("www.ajg.com",1,1, "usa", "desktop");
//        extractKeywordExpansionFromRG.getTop3CompetitorExpandKeywordFile("www.gallaghersmallbusiness.com",1,1, "usa", "desktop");
//        extractKeywordExpansionFromRG.getTop3CompetitorExpandKeywordFile("www.artexrisk.com",1,1, "usa", "desktop");
//        extractKeywordExpansionFromRG.getTop3CompetitorExpandKeywordFile("www.rpsins.com",1,1, "usa", "mobile");
//        extractKeywordExpansionFromRG.getTop3CompetitorExpandKeywordFile("www.gallagherbassett.com",1,1, "usa", "mobile");
//        extractKeywordExpansionFromRG.getTop3CompetitorExpandKeywordFile("www.ajg.com.au",2,5, "nld", "mobile");
//        extractKeywordExpansionFromRG.getTop3CompetitorExpandKeywordFile("www.ajg.com",2,5, "usa", "desktop");
//        extractKeywordExpansionFromRG.getTop3CompetitorExpandKeywordFile("www.ajg.com",24,25, "usa", "desktop");
//        extractKeywordExpansionFromRG.getTop3CompetitorExpandKeywordFile("www.ajg.com",6,8, "usa", "desktop");

//        extractKeywordExpansionFromRG.getTop3CompetitorExpandKeywordFile(args[0],Integer.parseInt(args[1]),Integer.parseInt(args[2]), args[3], args[4]);

        extractKeywordExpansionFromRG.getAllUSDomain();
    }

}
