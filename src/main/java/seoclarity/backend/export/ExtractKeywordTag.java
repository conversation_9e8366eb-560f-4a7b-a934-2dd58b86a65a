package seoclarity.backend.export;

import com.google.gson.Gson;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import seoclarity.backend.dao.actonia.GroupTagEntityDAO;
import seoclarity.backend.dao.actonia.KeywordEntityDAO;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.clickhouse.ClDailyRankingEntityDao;
import seoclarity.backend.entity.actonia.GroupTagEntity;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.clickhouse.CLRankingDetailEntity;
import seoclarity.backend.service.*;
import seoclarity.backend.utils.EmailSenderComponent;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.utils.loggly.LogglyUtils;
import seoclarity.backend.utils.loggly.LogglyVO;

import java.io.File;
import java.io.IOException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.*;

@CommonsLog
public class ExtractKeywordTag {

    private static final String SPLIT = "\t";
    private static final String TAG_SPLIT = "!_!";
    private static String LOC = "/home/<USER>/";
    private static final int QUERY_TRY_COUNT = 10;
    private  static LogglyVO logglyVO = new LogglyVO();
    private static int totalCnt = 0;

    private KeywordEntityDAO keywordEntityDAO;
    private OwnDomainEntityDAO ownDomainEntityDAO;
    private GroupTagEntityDAO groupTagEntityDAO;
    private ScKeywordRankManager scKeywordRankManager;
    private ClDailyRankingEntityDao clDailyRankingEntityDao;
    private ServerAuthenticationInfoService serverAuthenticationInfoService;
    private EmailSenderComponent emailSenderComponent;
    private GeoService geoService;

    public ExtractKeywordTag() {
        keywordEntityDAO = SpringBeanFactory.getBean("keywordEntityDAO");
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        groupTagEntityDAO = SpringBeanFactory.getBean("groupTagEntityDAO");
        scKeywordRankManager = SpringBeanFactory.getBean("scKeywordRankManager");
        clDailyRankingEntityDao = SpringBeanFactory.getBean("clDailyRankingEntityDao");
        serverAuthenticationInfoService = SpringBeanFactory.getBean("serverAuthenticationInfoService");
        emailSenderComponent = SpringBeanFactory.getBean("emailSenderComponent");
        geoService = SpringBeanFactory.getBean("geoService");
    }

    private boolean onlyGeo = false;

    private void process(int domainId, Boolean isMobile, Integer locationId, String rankingDate) throws Exception {

        String device = "desktop";
        if (isMobile != null && isMobile) {
            device = "mobile";
        }

        Map<String, Object> paramMap = new HashMap<>();
        OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getById(domainId);
        if (ownDomainEntity == null) {
            System.out.println("===Skip suspend domain, OID:" + domainId);
            return;
        }
        int frequency = 1;//daily
        if (ownDomainEntity.getKeywordRankFrequency() != null && ownDomainEntity.getKeywordRankFrequency() == OwnDomainEntity.KEYWORD_RANK_FREQUENCY_WEEKLY) {
            rankingDate = FormatUtils.getLastSundayForWeeklyOfString(rankingDate);
            System.out.println("====weekly domain processDate:" + rankingDate);
            frequency = 7;//weekly
        }

        int engineId = scKeywordRankManager.getSearchEngineId(ownDomainEntity);
        int languageId = scKeywordRankManager.getSearchLanguageId(ownDomainEntity);
        if (locationId == null) {
            locationId = 0;
        } else if (locationId == 8888) {//all location
            onlyGeo = false;
            locationId = null;
        } else if (locationId == 9999) {
            onlyGeo = true;
            locationId = null;
            paramMap.put("onlyGeo", onlyGeo);
        }

        if (isMobile == null) {
            isMobile = false;
        }

        int rank = 100;
//        String rankingDate = "2020-02-18";


        String stTime = FormatUtils.formatDate(new Date(), "yyyyMMdd HH:mm:ss");
        logglyVO.setoId(String.valueOf(domainId));
        logglyVO.setName("ExtractKeywordTag");
        logglyVO.setDevice(device.startsWith("m")?"m":"d");
        logglyVO.setpDate(rankingDate);
        List<String> groupList = new ArrayList<>();
        groupList.add(LogglyVO.GROUP_KEYWORD_AND_TAG_EXTRACT);
        logglyVO.setGroups(groupList);


        String fileName = domainId + "_RankExtract_" + rankingDate + "_" + device + ".csv";
        if (onlyGeo) {
            fileName = domainId + "_GEORankExtract_" + rankingDate + "_" + device + ".csv";
        }

        String localFilePath = LOC + domainId + File.separator;
        String remoteFilePath = localFilePath;
        if (domainId == 6686) {
            remoteFilePath = localFilePath + "Top100Rankings" + File.separator;
        } else if(domainId == 9672){
            remoteFilePath = LOC + 6686 + File.separator + "Top100Rankings" + File.separator;
        }

        String localFileName = localFilePath + fileName;
        File localFile = new File(localFileName);
        if (localFile.exists()) {
            localFile.delete();
        }
        addHeadersForExactFile(localFile);

        //https://www.wrike.com/open.htm?id=614656875
        List<Integer> locationIdList = geoService.getLocationIdList(domainId);
        System.out.println("locationIdList=>"+locationIdList);
        Integer [] idArr = locationIdList.toArray(new Integer[locationIdList.size()]);


        int retryCount = 1;
        List<CLRankingDetailEntity> dataList = new ArrayList<>();
        while (true) {
            try {
                dataList = clDailyRankingEntityDao.exportTopXKeywords(domainId, engineId, languageId, locationId, rankingDate, isMobile, rank, paramMap, idArr);
                break;
            } catch (Exception e) {
                if (retryCount >= QUERY_TRY_COUNT) {
                    System.out.println("====error extract oid : " + domainId);
                    System.out.println("====RETRY OVER " + QUERY_TRY_COUNT + "TIMES error :" + domainId);
                    logglyVO.setStatus(LogglyVO.STATUS_NG);
                    String body = new Gson().toJson(logglyVO);
                    LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);
//                    String subject = getEmailSubject(domainId, false, fileName, rankingDate);
//                    String message = subject;
//                    sendMailReport(subject, message);
                    return;
                }

                e.printStackTrace();
                System.out.println("====extract error oid:" + domainId + ", sleep " + (1000 * 60 * retryCount));
                try {
                    Thread.sleep(1000 * 60 * retryCount);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
                retryCount++;
            }
        }

        log.info("===dataList size: " + dataList.size());

        Set<String> keywordNameSet = new HashSet<>();
        Map<String, String> tagMap = new HashMap<>();
        Map<String, CLRankingDetailEntity> keywordMap = new HashMap<>();
        //get tag
        for (CLRankingDetailEntity detail : dataList) {
            try {
                keywordNameSet.add(URLEncoder.encode(detail.getKeywordName(), "utf-8").toLowerCase());
                if (keywordNameSet.size() >= 100) {
                    List<String> keywordNameList = new ArrayList<>();
                    keywordNameList.addAll(keywordNameSet);
                    //tag
                    log.info("===keywordNameList size: " + keywordNameList.size());
                    List<GroupTagEntity> tagList = groupTagEntityDAO.getTagNamesByKeywordNameList(domainId, keywordNameList);
                    log.info("===tagList size: " + tagList.size());
                    if (CollectionUtils.isNotEmpty(tagList)) {
                        for (GroupTagEntity groupTagEntity : tagList) {
                            String tagNameArray = groupTagEntity.getTagName();
                            String decodeKw = URLDecoder.decode(groupTagEntity.getKeywordName(), "utf-8").toLowerCase();
                            tagMap.put(decodeKw, tagNameArray);
                        }
                    }
                    keywordNameSet.clear();
                }

            } catch (Exception e) {
                e.printStackTrace();
                continue;
            }
        }

        if (CollectionUtils.isNotEmpty(keywordNameSet)) {
            List<String> keywordNameList = new ArrayList<>();
            keywordNameList.addAll(keywordNameSet);
            log.info("===keywordNameList size: " + keywordNameList.size());
            List<GroupTagEntity> tagList = groupTagEntityDAO.getTagNamesByKeywordNameList(domainId, keywordNameList);
            log.info("===tagList size: " + tagList.size());
            if (CollectionUtils.isNotEmpty(tagList)) {
                for (GroupTagEntity groupTagEntity : tagList) {
                    String tagNameArray = groupTagEntity.getTagName();
                    String decodeKw = URLDecoder.decode(groupTagEntity.getKeywordName(), "utf-8").toLowerCase();
                    tagMap.put(decodeKw, tagNameArray);
                }
            }
        }

        List<String> extractLines = new ArrayList<String>();

        for (CLRankingDetailEntity detail : dataList) {
            detail.setDomain(ownDomainEntity.getDomain());
            String kw = detail.getKeywordName();
//            String decodeKw = URLDecoder.decode(kw,"utf-8");

            String tags = tagMap.get(kw);
            if (StringUtils.isNotBlank(tags)) {
                extractLines.add(appendDataFor6686(detail, tags));
            } else {
                extractLines.add(appendDataFor6686(detail, null));
            }
        }
        totalCnt = dataList.size();
        FileUtils.writeLines(localFile, extractLines, true);

        int serverType = 1;
        boolean success = serverAuthenticationInfoService.copyFileToRemoteServer(serverType, domainId, localFileName, remoteFilePath, null);
        if (!success) {
//            String subject = getEmailSubject(domainId, success, fileName, rankingDate);
//            String message = subject;
//            sendMailReport(subject, message);
        }

        logglyVO.setStatus(LogglyVO.STATUS_OK);
        logglyVO.setsTime(stTime);
        logglyVO.seteTime(FormatUtils.formatDate(new Date(), "yyyyMMdd HH:mm:ss"));
        logglyVO.setRows(String.valueOf(totalCnt));
        String body = new Gson().toJson(logglyVO);
        LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);
    }

    private static String appendData(String keywordName, String tagName) {
        StringBuffer line = new StringBuffer();

        line.append(keywordName).append(SPLIT);
        line.append(StringUtils.isBlank(tagName) ? "-" : tagName);
        return line.toString();
    }

    public String appendDataFor6686(CLRankingDetailEntity clRankingDetailEntity, String tagName) {
        StringBuffer line = new StringBuffer();

        if (onlyGeo) {
            //https://www.wrike.com/open.htm?id=614656875
            line.append(geoService.getCityName(clRankingDetailEntity.getLocationId())).append(SPLIT);
        }

        line.append(clRankingDetailEntity.getKeywordName()).append(SPLIT);
        line.append(clRankingDetailEntity.getAvgSearchVolume()).append(SPLIT);
        if (clRankingDetailEntity.getTrueRank().equals(-1) || clRankingDetailEntity.getTrueRank().equals(0)) {
            line.append("101").append(SPLIT);
        } else {
            line.append(clRankingDetailEntity.getTrueRank()).append(SPLIT);
        }
        if (clRankingDetailEntity.getWebRank().equals(-1) || clRankingDetailEntity.getWebRank().equals(0)) {
            line.append("101").append(SPLIT);
        } else {
            line.append(clRankingDetailEntity.getWebRank()).append(SPLIT);
        }
        line.append(RankTypeManager.getUrlTypeName(clRankingDetailEntity.getType())).append(SPLIT);
        String domainReverse = clRankingDetailEntity.getDomainReverse();
        String[] domainNameArray = domainReverse.split("\\.");
        String domainName = "";
        for (int i = 1; i <= domainNameArray.length; i++) {
            domainName += domainNameArray[domainNameArray.length - i] + ".";
        }
        domainName = domainName.substring(0, domainName.length() - 1);
        line.append(domainName).append(SPLIT);
        line.append(clRankingDetailEntity.getUrl() == null ? "-" : clRankingDetailEntity.getUrl()).append(SPLIT);
        line.append(StringUtils.isBlank(tagName) ? "-" : tagName);
        return line.toString();
    }

    private void addHeadersForExactFile(File outFile) throws IOException {

        List<String> lines = new ArrayList<String>();
        StringBuffer header = new StringBuffer();
        if (onlyGeo) {
            header.append("Location").append(SPLIT);
        }
        header.append("Keyword").append(SPLIT);
        header.append("Search Volume").append(SPLIT);
        header.append("True Rank").append(SPLIT);
        header.append("Web Rank").append(SPLIT);
        header.append("Url Type").append(SPLIT);
        header.append("Domain").append(SPLIT);
        header.append("Ranking URL").append(SPLIT);
        header.append("Keyword Tag");

        lines.add(header.toString());
        FileUtils.writeLines(outFile, lines, true);
    }

    private String getEmailSubject(int domainId, boolean success, String fileName, String rankingDate) {

        String subject = "";
        String status = success ? "Success" : "Failed";
        switch (domainId) {
            case 6686:
                subject = status + " Export 6686 " + fileName + "  " + rankingDate;
            case 9672:
                subject = status + " Export 9672 " + fileName + "  " + rankingDate;
            default:
                break;
        }

        return subject;
    }

    public static void main(String[] args) {
        ExtractKeywordTag extractKeywordTag = new ExtractKeywordTag();
        try {
            int domainId = Integer.parseInt(args[0]);
            String rankingDate = "";
            Boolean isMobile = StringUtils.isBlank(args[1]) ? null : Boolean.parseBoolean(args[1]);
            Integer locationId = StringUtils.isBlank(args[2]) ? null : Integer.parseInt((args[2]));

            if (args.length >= 4 && StringUtils.containsIgnoreCase(args[3], ",")) {
                Date sDate = FormatUtils.toDate(args[3].split(",")[0], FormatUtils.DATE_PATTERN_2);
                Date eDate = FormatUtils.toDate(args[3].split(",")[1], FormatUtils.DATE_PATTERN_2);
                while (sDate.compareTo(eDate) <= 0) {
                    rankingDate = FormatUtils.formatDate(sDate, FormatUtils.DATE_PATTERN_2);
                    extractKeywordTag.process(domainId, isMobile, locationId, rankingDate);

                    sDate = DateUtils.addDays(sDate, 1);
                }

            } else {
                rankingDate = FormatUtils.formatDate(DateUtils.addDays(new Date(), -1), FormatUtils.DATE_PATTERN_2);
                extractKeywordTag.process(domainId, isMobile, locationId, rankingDate);
            }

        } catch (Exception e) {
            e.printStackTrace();

            logglyVO.setStatus(LogglyVO.STATUS_NG);
            String body = new Gson().toJson(logglyVO);
            LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);
        }
    }

}
