package seoclarity.backend.export;

import com.google.gson.Gson;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import seoclarity.backend.dao.actonia.KeywordEntityDAO;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.entity.actonia.GroupTagRelationEntity;
import seoclarity.backend.entity.actonia.KeywordEntity;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.service.ExtractService;
import seoclarity.backend.service.ServerAuthenticationInfoService;
import seoclarity.backend.utils.EmailSenderComponent;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.utils.loggly.LogglyUtils;
import seoclarity.backend.utils.loggly.LogglyVO;

import java.io.File;
import java.io.IOException;
import java.net.URLDecoder;
import java.util.*;

@CommonsLog
public class ExtractKeywordTagStandard {

    private static final String SPLIT = "\t";
    private static final String TAG_SPLIT = "!_!";
    private static String LOC = "/home/<USER>/";

    private static boolean extractAllKeyword = false;
    private  static LogglyVO logglyVO = new LogglyVO();
    private static int totalCnt = 0;

    private KeywordEntityDAO keywordEntityDAO;
    private OwnDomainEntityDAO ownDomainEntityDAO;
    private ServerAuthenticationInfoService serverAuthenticationInfoService;
    private EmailSenderComponent emailSenderComponent;
    private ExtractService extractService;

    public ExtractKeywordTagStandard() {
        keywordEntityDAO = SpringBeanFactory.getBean("keywordEntityDAO");
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        serverAuthenticationInfoService = SpringBeanFactory.getBean("serverAuthenticationInfoService");
        emailSenderComponent = SpringBeanFactory.getBean("emailSenderComponent");
        extractService = SpringBeanFactory.getBean("extractService");
    }


    private void process(int domainId, String processingDate) {

        OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getById(domainId);
        if(ownDomainEntity == null){
            log.info("====ownDomainEntity not exist : " + domainId);
            try {
                extractService.sendMailReport("ERROR:Export for inactive OID:" + domainId, "Please disable export for inactive OID:" + domainId + "(" + getClass().getName() + ")");
            } catch (Exception exp) {
                exp.printStackTrace();
            }
            return;
        }
        String pDate = FormatUtils.formatDate(new Date(), FormatUtils.DATE_FORMAT_YYYYMMDD);
        String stTime = FormatUtils.formatDate(new Date(), "yyyyMMdd HH:mm:ss");

        logglyVO.setoId(String.valueOf(domainId));
        logglyVO.setName("ExtractKeywordTagStandard");
        logglyVO.setDevice(ownDomainEntity.isMobileDomain()?"m":"d");

        logglyVO.setpDate(pDate);
        List<String> groupList = new ArrayList<>();
        groupList.add(LogglyVO.GROUP_KEYWORD_AND_TAG_EXTRACT);
        logglyVO.setGroups(groupList);

        String fileName = getFileName(ownDomainEntity, processingDate);

        String localFilePath = LOC + domainId + File.separator;
        String remoteFilePath = localFilePath;
        if (domainId == 7006) {
            remoteFilePath = localFilePath + "www.truecar.com_keyword_tags" + File.separator;
        }

        String localFileName = localFilePath + fileName;
        File localFile = new File(localFileName);
        if (localFile.exists()) {
            localFile.delete();
        }

        try {
            addHeadersForExactFile(localFile);
            List<String> extractLines = new ArrayList<String>();
            if(extractAllKeyword){
                log.info("====extractAllKeyword : " + domainId);
                List<KeywordEntity> keywordList = keywordEntityDAO.getKeywordAndTagListByDomainId(domainId, GroupTagRelationEntity.RESOURCE_TYPE_KEYWORD);
                System.out.println("***** keywordList size : " + keywordList.size());

                if(CollectionUtils.isNotEmpty(keywordList)){
                    for(KeywordEntity keywordEntity : keywordList){
                        String decodeKeywordName = URLDecoder.decode(keywordEntity.getKeywordName(), "utf-8");
                        String tagNames = keywordEntity.getTagName();
                        if(StringUtils.isNotBlank(tagNames)){
                            String[] tagNameArr = tagNames.split("\\|");
                            for(String tag : tagNameArr){
                                extractLines.add(appendData(decodeKeywordName, tag, processingDate));
                            }
                        }else {
                            extractLines.add(appendData(decodeKeywordName, null, processingDate));
                        }
                    }
                }

            }else {
                log.info("====extractKeywordOnlyTag : " + domainId);
                List<KeywordEntity> keywordEntityList = keywordEntityDAO.geKeywordTagList(domainId);
                if(CollectionUtils.isNotEmpty(keywordEntityList)){
                    for(KeywordEntity keywordEntity : keywordEntityList){
                        String decodeKeywordName = URLDecoder.decode(keywordEntity.getKeywordName(), "utf-8");
                        extractLines.add(appendData(decodeKeywordName, keywordEntity.getTagName(), processingDate));
                    }
                }
            }
            totalCnt = extractLines.size();
            FileUtils.writeLines(localFile, extractLines, true);
            int serverType = 1;
            try {
                serverAuthenticationInfoService.copyFileToRemoteServer(serverType, domainId, localFileName, remoteFilePath, null);
            }catch (Exception e){
                log.error("====trans file error :" + domainId + ",fileName:" +fileName + ",processingDate:" + processingDate);
                logglyVO.setStatus(LogglyVO.STATUS_NG);
                String body = new Gson().toJson(logglyVO);
                LogglyUtils.sendLoggly(body, LogglyUtils.EXTRACT_TAG_NAME);
//                String subject = getEmailSubject(domainId, false, fileName, processingDate);
//                String message = subject;
//                sendMailReport(subject, message);
                e.printStackTrace();
            }

            logglyVO.setStatus(LogglyVO.STATUS_OK);
            logglyVO.setsTime(stTime);
            logglyVO.seteTime(FormatUtils.formatDate(new Date(), "yyyyMMdd HH:mm:ss"));
            logglyVO.setRows(String.valueOf(totalCnt));
            String body = new Gson().toJson(logglyVO);
            LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);

        }catch (Exception e){
            e.printStackTrace();
            logglyVO.setStatus(LogglyVO.STATUS_NG);
            String body = new Gson().toJson(logglyVO);
            LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);
//            String subject = getEmailSubject(domainId, false, fileName, processingDate);
//            String message = subject;
//            sendMailReport(subject, message);
        }

    }

    private String getFileName(OwnDomainEntity ownDomainEntity, String processingDate) {

        String fileName = "";
        int domainId = ownDomainEntity.getId();
        if (domainId == 7006) {
            fileName = ownDomainEntity.getDomain() + "_" + processingDate + "_Keyword-Tags.txt";
        } else {
            fileName = domainId + "_KeywordTagExtract_" + processingDate  + ".csv";
        }

        return fileName;
    }

    private void addHeadersForExactFile(File outFile) throws IOException {

        List<String> lines = new ArrayList<String>();
        StringBuffer header = new StringBuffer();
        header.append("Keyword").append(SPLIT);
        header.append("Group Tag").append(SPLIT);
        header.append("Date").append(SPLIT);

        lines.add(header.toString());
        FileUtils.writeLines(outFile, lines, true);
    }

    private static String appendData(String keywordName, String tagName, String processingDate) {
        StringBuffer line = new StringBuffer();

        line.append(keywordName).append(SPLIT);
        line.append(StringUtils.isBlank(tagName) ? "-" : tagName).append(SPLIT);
        line.append(processingDate);
        return line.toString();
    }


    private String getEmailSubject(int domainId, boolean success, String fileName, String rankingDate) {

        String subject = "";
        String status = success ? "Success" : "Failed";
        switch (domainId) {
            case 7006 :
                subject = status + " Export 7006  " + fileName + "  " + rankingDate;
            default:
                break;
        }

        return subject;
    }

    public static void main(String[] args) {
        ExtractKeywordTagStandard extractKeywordTagStandard = new ExtractKeywordTagStandard();
        try {
            int domainId = Integer.parseInt(args[0]);
            String rankingDate = "";

            if(args.length >= 2){
                extractAllKeyword = Boolean.parseBoolean(args[1]);
            }

            if (args.length >= 3 && StringUtils.containsIgnoreCase(args[1], ",")) {
                Date sDate = FormatUtils.toDate(args[2].split(",")[0], FormatUtils.DATE_PATTERN_2);
                Date eDate = FormatUtils.toDate(args[2].split(",")[1], FormatUtils.DATE_PATTERN_2);
                while (sDate.compareTo(eDate) <= 0) {
                    rankingDate = FormatUtils.formatDate(sDate, FormatUtils.DATE_PATTERN_2);
                    extractKeywordTagStandard.process(domainId, rankingDate);

                    sDate = DateUtils.addDays(sDate, 1);
                }

            } else {
                rankingDate = FormatUtils.formatDate(DateUtils.addDays(new Date(), -1), FormatUtils.DATE_PATTERN_2);
                extractKeywordTagStandard.process(domainId, rankingDate);
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
