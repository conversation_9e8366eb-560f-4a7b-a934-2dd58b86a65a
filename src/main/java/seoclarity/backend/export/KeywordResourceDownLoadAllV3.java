package seoclarity.backend.export;

import com.google.gson.Gson;

import com.google.gson.JsonObject;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.csv.QuoteMode;
import org.apache.commons.lang.StringUtils;
import org.w3c.dom.Node;
import org.springframework.util.StopWatch;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import seoclarity.backend.dao.actonia.ExportInfoDAO;
import seoclarity.backend.dao.clickhouse.RIDailyRankingService;
import seoclarity.backend.entity.KeywordRankEntityVO;
import seoclarity.backend.entity.actonia.TExportInfoEntity;
import seoclarity.backend.entity.clickhouse.ri.RankIntelligenceQueryForClarityDBVO;
import seoclarity.backend.utils.*;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.OutputKeys;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import java.io.*;
import java.util.*;

/**
 * 
 * <AUTHOR>
 * @date 2021-01-22
 * @path seoclarity.backend.export.KeywordResourceDownLoadAllV3
 *
 */
public class KeywordResourceDownLoadAllV3 {
	private static final int MAX_WORKONG_DOMAIN_CHECK_DATE_RANGE = 2;
	private static final int MAX_RE_TRY_COUNT = 3;
	private static final String EXPORT_PATH = "/tmp/async_download/";
	private static final String FTP_EXPORT_PATH = "/home/<USER>/%OID%/async_download/";
	private static final String EXPORT_FILE_REFIX = "async_%INFOID%_%OID%_%DATERANGE%_";
	private static final int MAX_PROCESS_SIZE = 1;
	private static final int DUPLICATED_TASK_CHECK_DATE_RANGE = 30;
	private static final String FILE_TYPE_CSV = ".csv";
	private static final String FILE_TYPE_XML = ".xml";
	private static final boolean isDebug = true;
	private static final String CREATE_SQL_API_PATH = "/seoClarity/rankIntelligenceAPI/createRankDetailSqlForKeywordResource";
	private static final String API_ADDRESS_DEV = "http://*************:8183";
	private static final String API_ADDRESS_APP = "http://api.seoclarity.net";
	private static final String INTERNAL_TYK_HEADER = "seoclarity_tky_internal_authorization";
	private static String CREATE_SQL_API_PATH_VAL = API_ADDRESS_APP + CREATE_SQL_API_PATH;
	static CSVFormat csvFullFormat = CSVFormat.DEFAULT.withHeader(
			"keyword name",
			"date",
			"highestTrueRank",
			"highestWebRank",
			"highestRankUrl",
			"highestLocalRank",
			"highestNewsRank",
			"highestImageRank",
			"highestVideoRank",
			"highestStarRank",
			"monthlySearchVolume",
			"competitorName",
			"landingPage",
			"rank"
			)
			.withDelimiter(',').withQuoteMode(QuoteMode.NON_NUMERIC);

	private RIDailyRankingService riDailyRankingService;
	private ExportInfoDAO exportInfoDAO;
	private Gson gson = new Gson();

	public KeywordResourceDownLoadAllV3() {
		exportInfoDAO = SpringBeanFactory.getBean("exportInfoDAO");
		riDailyRankingService = new RIDailyRankingService(true);
	}

	public static void main(String[] args) throws Exception {
		KeywordResourceDownLoadAllV3 keywordResourceDownLoadAll = new KeywordResourceDownLoadAllV3();
		if (isDebug) {
			CREATE_SQL_API_PATH_VAL = API_ADDRESS_DEV + CREATE_SQL_API_PATH;
			keywordResourceDownLoadAll.test((args == null || args.length == 0) ? 0 : Integer.valueOf(args[0]));
		}
		keywordResourceDownLoadAll.processTasks();
	}
	
	private void test(int infoId) {
		if (infoId > 0) {
			exportInfoDAO.reUpdateErrorStatus(infoId, 0);
		}
	}
	
	private void processTasks() {
		// get all working domains
		List<String> workingDomains = filterWorkingTask(TExportInfoEntity.EXPORT_PROCESSING);
		System.out.println("=workingDomains:" + workingDomains);
		// filter working domains
		// get new created or error tasks
		List<TExportInfoEntity> taskList = exportInfoDAO.filterDomainAndStatus(workingDomains, MAX_PROCESS_SIZE);
		if (taskList.size() > 0) {
			for (TExportInfoEntity entity : taskList) {
				try {
					entity = exportInfoDAO.getById(entity.getId());
					if (entity.getStatus() == TExportInfoEntity.EXPORT_CREATE) {
						exportInfoDAO.updateStatus(entity.getId(), TExportInfoEntity.EXPORT_PROCESSING);
					} else if (entity.getStatus() == TExportInfoEntity.EXPORT_ERROR && entity.getRetryCount() < MAX_RE_TRY_COUNT){
						exportInfoDAO.updateStatusForError(entity.getId(), TExportInfoEntity.EXPORT_PROCESSING);
					} else {
						System.out.println("===Skip current task, id:" + entity.getId() + ", status:" + entity.getStatus() + ", oid:" + entity.getOwnDomainId() 
							+ ", re-try:" + entity.getRetryCount() + ", createDate:" + entity.getCreateDate());
						continue;
					}
					System.out.println("===id : " + entity.getId() + ", status:" + entity.getStatus() + " -> " + TExportInfoEntity.EXPORT_PROCESSING + ", createDate:" + entity.getCreateDate());
					if (!isDuplicatedTask(entity)) {
						exportAndChangeTask(entity);
					}
				} catch (Exception e) {
					e.printStackTrace();
					errorTaskHandler(entity, null);
				}
			}
		} else {
			System.out.println(" there are no task need to be export !");
		}
	}

	private boolean isDuplicatedTask(TExportInfoEntity entity) {
		// check duplicated task
		List<TExportInfoEntity> repetitionList = exportInfoDAO.checkRepetationTask(entity.getOwnDomainId(), entity.getMd5(), getMinDateForCheckingDuplicatedTask(entity), TExportInfoEntity.EXPORT_DONE);
		if (repetitionList != null && repetitionList.size() > 0) {
			TExportInfoEntity finsihedTask = repetitionList.get(0);
			System.out.println("===Find duplicated task, currTask:" + entity.getId() + ", status:" + entity.getStatus()
					+ ", OID:" + entity.getOwnDomainId() + ", prevTask:" + finsihedTask.getId() + ", status:" + finsihedTask.getStatus() + ", createDate:" + finsihedTask.getCreateDate() 
					+ ", file:" + finsihedTask.getFileName());
			exportInfoDAO.setTaskComplete(entity.getId(), TExportInfoEntity.EXPORT_DONE, finsihedTask.getFileName());
			System.out.println("===Finish curr task, task:" + entity.getId() + ", OID:" + entity.getOwnDomainId() + ", status:" + entity.getStatus() + "->" + TExportInfoEntity.EXPORT_DONE 
					+ ", create:" + entity.getCreateDate());
			return true;
		}
		return false;
	}

	private List<String> filterWorkingTask(int status) {
		Calendar cal = Calendar.getInstance();
		cal.add(Calendar.DAY_OF_MONTH, -MAX_WORKONG_DOMAIN_CHECK_DATE_RANGE);
		Date startDate = cal.getTime();
		Date endDate = new Date();
		List<TExportInfoEntity> tExportInfoEntityList = exportInfoDAO.filterWorkingTask(status, startDate, endDate);
		Set<String> domainSet = new HashSet<>();
		List<String> domainList = new ArrayList<>();
		if (tExportInfoEntityList.size() > 0) {
			for (TExportInfoEntity tExportInfoEntity : tExportInfoEntityList) {
				domainSet.add(tExportInfoEntity.getOwnDomainId().toString());
			}
		}
		domainList.addAll(domainSet);
		return domainList;
	}


	private void exportAndChangeTask(TExportInfoEntity tExportInfoEntity) {
		if (checkParam(tExportInfoEntity) == 1) {
			processExport(tExportInfoEntity);
		} else {
			errorTaskHandler(tExportInfoEntity, ", param check failed:" + tExportInfoEntity.getExportParam());
		}
	}

	private int checkParam(TExportInfoEntity tExportInfoEntity) {
		System.out.println(" =====check params");
		if (tExportInfoEntity.getExportParam() == null) {
			return 0;
		} else {
			String param = tExportInfoEntity.getExportParam();
			RankIntelligenceQueryForClarityDBVO paramVO = null;
			paramVO = gson.fromJson(param, RankIntelligenceQueryForClarityDBVO.class);
			if (null == paramVO.getIndexList() || paramVO.getIndexList().length <= 0) {
				System.out.println("  no :Index List");
				return 0;
			}
			if (null == paramVO.getDateRangeTableMapList() || paramVO.getDateRangeTableMapList().size() <= 0) {
				System.out.println(" no :DateRangeTableMapList ");
				return 0;
			} else {
				for (Map<String, String> map : paramVO.getDateRangeTableMapList()) {
					if (StringUtils.isBlank(map.get("info"))) {
						System.out.println("  no : info ");
						return 0;
					}
					if (StringUtils.isBlank(map.get("detailTable"))) {
						System.out.println(" no : detailTable ");
						return 0;
					}
					if (StringUtils.isBlank(map.get("queryStart"))) {
						System.out.println(" no : queryStart ");
						return 0;
					}
					if (StringUtils.isBlank(map.get("queryEnd"))) {
						System.out.println(" no : queryEnd ");
						return 0;
					}
				}
			}
		}
		return 1;
	}
	
	private void processExport(TExportInfoEntity tExportInfoEntity) {
		System.out.println("==========================get export_param, task:" + tExportInfoEntity.getId() + ", OID:" + tExportInfoEntity.getOwnDomainId() + "========================");
		String json = tExportInfoEntity.getExportParam();
		System.out.println("===json params :" + json);
		
		RankIntelligenceQueryForClarityDBVO paramVO = null;
		StopWatch watch = new StopWatch();
		try {
			paramVO = gson.fromJson(json, RankIntelligenceQueryForClarityDBVO.class);
			List<Map<String, String>> dateRangeQueryParams = paramVO.getDateRangeTableMapList();
			
			// get query domains
			boolean isFolderDomain = ExportTaskUtils.isFolderDomain(paramVO.getOwnDomainName());
			String domainName = isFolderDomain ? StringUtils.substringBefore(paramVO.getOwnDomainName(), "/") : paramVO.getOwnDomainName();
			String rootDomainReverse = StringUtils.reverseDelimited(ClarityDBUtils.getRootDomain(domainName), '.');
			List<String> rootDomains = new ArrayList<String>();
			// for competitors
			if (paramVO.getCompetitorDomainList() != null && paramVO.getCompetitorDomainList().size() > 0) {
				for (String[] competitors : paramVO.getCompetitorDomainList()) {
					rootDomains.add(competitors[0]);
				}
			}
			// for own domain name without folder
			rootDomains.add(rootDomainReverse);
			// for images/videos/news/local
			rootDomains.add("com.google");
			
			// get query sql List
			// when querying keywords, just one month a sql, if query all keywords, need one day a sql.
			boolean isSingleDayProcess = paramVO.getQueryKeywords() == null || paramVO.getQueryKeywords().length == 0;
			List<File> exportedFile = new ArrayList<File>();
			
			for (Map<String, String> queryDateRangeMap : dateRangeQueryParams) {
				
				Integer fromIndex = Integer.valueOf(StringUtils.replace(queryDateRangeMap.get("queryStart"), "-", ""));
				Integer toIndex = Integer.valueOf(StringUtils.replace(queryDateRangeMap.get("queryEnd"), "-", ""));
				String infoTable = "seo_daily_ranking." + queryDateRangeMap.get("info");
				String detailTable = "seo_daily_ranking." + queryDateRangeMap.get("detailTable");
				List<String> dateList = new ArrayList<String>(Arrays.asList(StringUtils.split(queryDateRangeMap.get("queryDateList"), ",")));
				
				List<String> sqlList = new ArrayList<String>();
				if (!isSingleDayProcess) {
					paramVO.setIndexList(new String[] {String.valueOf(fromIndex), String.valueOf(toIndex)});
					paramVO.setWeeklyDates(dateList);
					String sql = createRankingDetailsSqlV2(paramVO, queryDateRangeMap, rootDomains.toArray(new String[rootDomains.size()]), infoTable, detailTable);
					sqlList.add(sql);
				} else {
					for (String date : dateList) {
						String index = StringUtils.replace(date, "-", "");
						paramVO.setIndexList(new String[] {String.valueOf(index), String.valueOf(index)});
						paramVO.setWeeklyDates(new ArrayList<String>(Arrays.asList(new String[] {date})));
						String sql = createRankingDetailsSqlV2(paramVO, queryDateRangeMap, rootDomains.toArray(new String[rootDomains.size()]), infoTable, detailTable);
						sqlList.add(sql);
					}
				}
				
				// create file for per month
				File file = createFile(paramVO, tExportInfoEntity, new int[] {fromIndex, toIndex});
				Exportor expotor = tExportInfoEntity.getFileSuffix().contains("xml") ? new ExportorXML(file, paramVO.getCompetitorDomainList()) : new ExportorCSV(file, paramVO.getCompetitorDomainList());
				exportedFile.add(file);
				
				if (sqlList.size() > 0) {
					// // add file header
					expotor.addHeader();
					
					// query ranking data
					for (int i = 0; i < sqlList.size(); i++) {
						String sql = sqlList.get(i);
						String date = dateList.get(i);
						watch.start("Porcessdate:" + (isSingleDayProcess ? date : paramVO.getWeeklyDates()));
						List<Map<String, Object>> resultList = riDailyRankingService.queryForAll(sql);
						watch.stop();
						System.out.println();
						System.out.println("=========Query finished. isSingleDayProcess:" + isSingleDayProcess + ", " + watch.getLastTaskName() + ", cost:" + watch.getLastTaskTimeMillis() / 1000 + ", resultList:" + resultList.size());
						
						// parse export
						List<Map<String, Object>> parsedList = null;
						watch.start("Parse ranking results, resultList:" + resultList.size());
						if (!isSingleDayProcess) {
							parsedList = parseValueV2(resultList, paramVO.getCompetitorDomainList(), paramVO.getWeeklyDates());
						} else {
							parsedList = parseValueForSingleDay(resultList, paramVO.getCompetitorDomainList(), Integer.valueOf(StringUtils.replace(date, "-", "")));
						}
						watch.stop();
						System.out.println("===Parse finished." + watch.getLastTaskName() + ", cost:" + watch.getLastTaskTimeMillis() / 1000);
						
						// append to file
						expotor.appendData(parsedList);
					}
					// end file
					expotor.endFile();
				}
				updateExportStatus(exportedFile, paramVO, tExportInfoEntity);
			}
			
			endExport(tExportInfoEntity);
			System.out.println("=========export finished. id:" + tExportInfoEntity.getId() + ", OID:" + tExportInfoEntity.getOwnDomainId() + " total cost:" + watch.getTotalTimeSeconds());
		} catch (Exception e) {
			e.printStackTrace();
			errorTaskHandler(tExportInfoEntity, null);
			return;
		}
	}
	
	private File createFile(RankIntelligenceQueryForClarityDBVO paramVO, TExportInfoEntity tExportInfoEntity, int[] indexList) {
		String fileType = FILE_TYPE_XML;
		if (!tExportInfoEntity.getFileSuffix().contains(FILE_TYPE_XML)) {
			fileType = FILE_TYPE_CSV;
		}
		File filePath = new File(EXPORT_PATH);
		if (!filePath.exists()) {
			filePath.mkdir();
		}
		File file = null;
		try {
			String outFileName = StringUtils.replace(EXPORT_FILE_REFIX, "%INFOID%", tExportInfoEntity.getId().toString());
			outFileName = StringUtils.replace(outFileName, "%OID%", tExportInfoEntity.getOwnDomainId().toString());
			outFileName = StringUtils.replace(outFileName, "%DATERANGE%", (indexList[0] + "-" + indexList[1]));
			file = File.createTempFile(outFileName, fileType, new File(EXPORT_PATH));
			System.out.println("===Create file:" + file.getAbsolutePath());
		} catch (IOException e) {
			e.printStackTrace();
		}
		return file;
	}
	
	private void endExport(TExportInfoEntity tExportInfoEntity){
		exportInfoDAO.updateStatus(tExportInfoEntity.getId(), TExportInfoEntity.EXPORT_DONE);
	}
	
	private void updateExportStatus(List<File> files, RankIntelligenceQueryForClarityDBVO paramVo, TExportInfoEntity tExportInfoEntity) throws Exception{
		List<String> fileList = new ArrayList<String>();
		String ftpFilePath = StringUtils.replace(FTP_EXPORT_PATH, "%OID%", String.valueOf(tExportInfoEntity.getOwnDomainId()));
		for (File file : files) {
			file = new File(file.getAbsolutePath());
			if (file.exists()) {
				// remote name, remove the last temp file number
				String remoteName = StringUtils.substringBeforeLast(file.getName(), "_") + "." + tExportInfoEntity.getFileSuffix();
				FTPUtils.saveFileToFTP(paramVo.getDomainId(), file.getAbsolutePath(), ftpFilePath, remoteName);
				try {
					file.delete();
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
			// only save the dateRange
			String dateRange = StringUtils.split(file.getName(), "_")[3];
			fileList.add(dateRange);
		}
		try {
			exportInfoDAO.updateFtpFileName(tExportInfoEntity.getId(), StringUtils.join(fileList, ","));
		} catch (Exception e) {
			System.out.println("===ERROR! Update task file failed. info:" + tExportInfoEntity.getId() + ", oid:" + tExportInfoEntity.getOwnDomainId());
			e.printStackTrace();
		}
	}
	
	private List<Map<String, Object>> parseValueV2(List<Map<String, Object>> resultList, List<String[]> competitorDomainList, List<String> dateList) {
		List<Map<String, Object>> newResultList = new ArrayList<>();
		Set<String> kidList = new HashSet<String>();
		for (Map<String, Object> map : resultList) {
			String kid = map.get("keyword_rankcheck_id").toString();
			kidList.add(kid);
		}
		for (String date : dateList) {
			for (String kid : kidList) {
				Map<String, Object> resultMap = new HashMap<String, Object>();
				int dateIdx = Integer.valueOf(StringUtils.replace(date, "-", ""));
				parseRow(competitorDomainList, resultList, dateIdx, kid, resultMap);
				if (resultMap.size() != 0) {
					newResultList.add(resultMap);
				}
			}
		}
		return newResultList;
	}
	
	private List<Map<String, Object>> parseValueForSingleDay(List<Map<String, Object>> resultList, List<String[]> competitorDomainList, int dateIdx) {
		List<Map<String, Object>> newResultList = new ArrayList<>();
		Map<Integer, List<Map<String, Object>>> keywordResultMap = new HashMap<Integer, List<Map<String,Object>>>();
		for (Map<String, Object> map : resultList) {
			int kid = Integer.valueOf(map.get("keyword_rankcheck_id").toString());
			if (keywordResultMap.containsKey(kid)) {
				keywordResultMap.get(kid).add(map);
			} else {
				List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
				list.add(map);
				keywordResultMap.put(kid, list);
			}
		}
		for (int kid : keywordResultMap.keySet()) {
			Map<String, Object> resultMap = new HashMap<String, Object>();
			parseRow(competitorDomainList, keywordResultMap.get(kid), dateIdx, String.valueOf(kid), resultMap);
			if (resultMap.size() != 0) {
				newResultList.add(resultMap);
			}
		}
		return newResultList;
	}

	private void parseRow(List<String[]> competitorList, List<Map<String, Object>> resultList, int dateIdx, String kid, Map<String, Object> resultMap) {
		int highestTrueRank = 101;
		int highestWebRank = 101;
		String highestRankUrl = "";
		int highestLocalRank = 101;
		int highestNewsRank = 101;
		int highestImageRank = 101;
		int highestVideoRank = 101;
		int highestStarRank = 101;
		
		String name = "";
		String monthlySearchVolume = "0";
		Map<String, String[]> competitorRanMap = new LinkedHashMap<String, String[]>();

		for (int i = 0; i < resultList.size(); i++) {
			Map<String, Object> map = resultList.get(i);
			String ranking_date = map.get("ranking_date").toString();
			int rowDateIdx = Integer.valueOf(StringUtils.replace(ranking_date, "-", ""));
			if (rowDateIdx != dateIdx) {
				continue;
			}
			String kidStr = map.get("keyword_rankcheck_id").toString();
			if (!StringUtils.equals(kidStr, kid)) {
				continue;
			}
			
			int true_rank = Integer.valueOf(map.get("true_rank").toString());
			int web_rank = Integer.valueOf(map.get("web_rank").toString());
			int type = Integer.valueOf(map.get("type").toString());
			String domain_reverse = map.get("domain_reverse").toString();
			name = map.get("name").toString();
			monthlySearchVolume = map.get("monthlySearchVolume").toString();
			
			String url = map.get("url").toString();
			int ownDomainFlg = Integer.valueOf(map.get("ownDomainFlg").toString());

			if (type == KeywordRankEntityVO.TYPE_ADDRESS && highestLocalRank > true_rank) {
				highestLocalRank = true_rank;
			}
			if (type == KeywordRankEntityVO.TYPE_NEWS && highestNewsRank > true_rank) {
				highestNewsRank = true_rank;
			}
			if (type == KeywordRankEntityVO.TYPE_IMGAGE && highestImageRank > true_rank) {
				highestImageRank = true_rank;
			}
			if (type == KeywordRankEntityVO.TYPE_VIDEO && highestVideoRank > true_rank) {
				highestVideoRank = true_rank;
			}
//			if (rating.equals("1") && highestStarRank > true_rank) {
//				highestStarRank = true_rank;
//			}

			// for own domain highest rank
			if (ownDomainFlg == 1) {
				if (highestTrueRank > true_rank) {
					highestTrueRank = true_rank;
				}
				if (highestWebRank > web_rank) {
					highestWebRank = web_rank;
				}
				if (StringUtils.isBlank(highestRankUrl)) {
					highestRankUrl = url;
				}
			}

			// for competitor highest rank
			for (String[] competitorDomain : competitorList) {
				if (competitorDomain[1] != null && !competitorRanMap.containsKey(competitorDomain[1]) && domain_reverse.equalsIgnoreCase(competitorDomain[1])) {
					competitorRanMap.put(competitorDomain[1], new String[]{
							url, String.valueOf(true_rank)
					});
				} else if (!competitorRanMap.containsKey(competitorDomain[0]) && StringUtils.startsWithIgnoreCase(domain_reverse, competitorDomain[0])) {
					competitorRanMap.put(competitorDomain[0], new String[]{
							url, String.valueOf(true_rank)
					});
				}
			}
			resultList.remove(i);
			i--;
		}

		resultMap.put("name", name);
        resultMap.put("monthlySearchVolume", monthlySearchVolume);
        resultMap.put("date", dateIdx);

		resultMap.put("highestTrueRank", highestTrueRank);
		resultMap.put("highestWebRank", highestWebRank);
		resultMap.put("highestRankUrl", highestRankUrl);
		resultMap.put("highestLocalRank", highestLocalRank);
		resultMap.put("highestNewsRank", highestNewsRank);
		resultMap.put("highestImageRank", highestImageRank);
		resultMap.put("highestVideoRank", highestVideoRank);
		resultMap.put("highestStarRank", highestStarRank);
		resultMap.put("competitorRanMap", competitorRanMap);
	}

	private String createRankingDetailsSqlV2(RankIntelligenceQueryForClarityDBVO paramVo, Map<String, String> dateRangeMap, String[] rootDomains, String infoTable, String detailTable) {
		String jsonParam = gson.toJson(paramVo);
		String response = post(jsonParam);
		try {
			if (StringUtils.isNotBlank(response)) {
				Map<String, String> data = gson.fromJson(response, Map.class);
				String sql = data.get("data");
				if (StringUtils.isNotBlank(sql)) {
					if (isDebug) {
						System.out.println("-----------------------------------------");
						System.out.println("===createRankingDetailsSqlV2:" + sql.toString());
					}
					return sql;
				}
			}
		} catch (Exception e) {
			System.out.println("===get sql failed, jsonParam:" + jsonParam);
			System.out.println("===failed response:" + response);
		}

		return createRankingDetailsSql(paramVo, dateRangeMap, rootDomains, infoTable, detailTable);
	}

	private String post(String jsonParam) {
		int MAX_RETRY_COUNT = 10;
		Map<String, String> expandHeader = new HashMap<>();
		expandHeader.put("org.restlet.http.headers", INTERNAL_TYK_HEADER);
		int tryCnt = 0;
		do {
			tryCnt++;
			try {
				String response = HttpRequestUtils.queryWebServiceFunctionPostWithJsonParams(CREATE_SQL_API_PATH_VAL, jsonParam, expandHeader);
				return response;
			} catch (Exception e) {
				System.out.println("=api query faild, will re-try, tryCnt:" + tryCnt + ", error:" + e.getMessage());
				try {
					Thread.sleep(3000);
				} catch (Exception e1) {
				}
			}
		} while (tryCnt <= MAX_RETRY_COUNT);
		return null;
	}

	private String createRankingDetailsSql(RankIntelligenceQueryForClarityDBVO paramVo, Map<String, String> dateRangeMap, String[] rootDomains, String infoTable, String detailTable) {
		boolean isGeo = ClarityDBUtils.isGeoQuery(paramVo.getLocationIdList());
		boolean isFolderDomain = ExportTaskUtils.isFolderDomain(paramVo.getOwnDomainName());
		String domainName = isFolderDomain ? StringUtils.substringBefore(paramVo.getOwnDomainName(), "/") : paramVo.getOwnDomainName();
		String rootDomainReverse = StringUtils.reverseDelimited(ClarityDBUtils.getRootDomain(domainName), '.');
		String domainReverse = StringUtils.reverseDelimited(domainName, '.');
		String reversDomainCol = rootDomainReverse.equals(domainReverse) ? "root_domain_reverse" : "domain_reverse";
		
		StringBuffer filterSql = new StringBuffer();
		if (paramVo.getQueryKeywords() != null && paramVo.getQueryKeywords().length > 0) {
			filterSql.append(" AND keyword_name = '" + ClarityDBUtils.formatQueryStringForClarityDBQuery(paramVo.getQueryKeywords()[0]) + "' ");
		}
		if (paramVo.getTagIdList() != null && paramVo.getTagIdList().length > 0) {
			filterSql.append(" AND (keyword_rankcheck_id, dictGetUInt32('geo_master_mapping', 'geoMasterId', tuple(location_id))) GLOBAL IN ( ");
			filterSql.append(" SELECT ");
			filterSql.append("     keyword_rankcheck_id, ");
			filterSql.append("     location_id ");
			filterSql.append(" FROM seo_daily_ranking.cdb_tracked_keyword ");
			filterSql.append(" WHERE (own_domain_id = " + paramVo.getDomainId() + ") AND (grouptag_id IN (" + StringUtils.join(paramVo.getTagIdList(), ",") + ")) AND (grouptag_id > 0) ");
			if (!isGeo) {
				filterSql.append(" AND (keyword_type IN (1)) ");
			} else {
				filterSql.append(" AND (keyword_type IN (2)) ");
			}
			filterSql.append(" GROUP BY ");
			filterSql.append("     grouptag_id, ");
			filterSql.append("     own_domain_id, ");
			filterSql.append("     location_id, ");
			filterSql.append("     grouptag_status, ");
			filterSql.append("     keyword_rankcheck_id ");
			filterSql.append(" HAVING sum(sign) > 0 ");
			filterSql.append(" ) AS t1 ");
		}
		
		StringBuffer sql = new StringBuffer();
		sql.append(" SELECT ");
		sql.append("     ranking_date, ");
		sql.append("     keyword_rankcheck_id,");
		sql.append("     name,");
		sql.append("     monthlySearchVolume, ");
		sql.append("     true_rank, ");
		sql.append("     web_rank, ");
		sql.append("     type, ");
		sql.append("     domain_reverse, ");
		sql.append("     url, ");
		sql.append("     ownDomainFlg ");
		sql.append(" FROM ( ");
		sql.append(" SELECT ");
		sql.append("     ranking_date, ");
		sql.append("     keyword_rankcheck_id,");
		sql.append("     keyword_name as name,");
		sql.append("     " + ExportTaskUtils.getStaticSearchVolumeSqlForKP(paramVo.isCustomSearchVol(),"avg_search_volume") + " as monthlySearchVolume ");
		sql.append(" FROM " + infoTable + " AS T1 ");
		sql.append(" WHERE (1 = 1) ");
		sql.append(ExportTaskUtils.getCommonQueryParams1(paramVo, null, null));
		sql.append(ExportTaskUtils.getRankingDateSql(paramVo, paramVo.getFromIndex(), paramVo.getToIndex(), paramVo.getFromIndex() != paramVo.getToIndex()));
		if (StringUtils.isNotBlank(filterSql.toString())) {
			sql.append(filterSql.toString());
		}
		sql.append(" ) AS t2 ALL LEFT JOIN ( ");
		sql.append(" SELECT ");
		sql.append("     true_rank, ");
		sql.append("     web_rank, ");
		sql.append("     type, ");
		sql.append("     domain_reverse, ");
		sql.append("     url, ");
		sql.append("     keyword_rankcheck_id,");
		String hrdSql = "";
		if (isFolderDomain) {
			hrdSql = ExportTaskUtils.getFolderSqlForDomain(paramVo.getOwnDomainName());
		} else {
			hrdSql = rootDomainReverse.equals(domainReverse) ? " AND hrrd = 1 " : " AND hrd = 1 ";
		}
		String domainSqlStr = reversDomainCol + " = '" + domainReverse + "' ";
		sql.append("     multiIf(" + domainSqlStr + " " + hrdSql + ", 1, 0) AS ownDomainFlg,  ");
		sql.append("     ranking_date ");
		sql.append(" FROM " + detailTable + " AS T2 ");
		sql.append(" WHERE (1 = 1) ");
		sql.append(ExportTaskUtils.getCommonQueryParams1(paramVo, null, null));
		sql.append(ExportTaskUtils.getRankingDateSql(paramVo, paramVo.getFromIndex(), paramVo.getToIndex(), paramVo.getFromIndex() != paramVo.getToIndex()));
		if (StringUtils.isNotBlank(filterSql.toString())) {
			sql.append(filterSql.toString());
		}
		if (rootDomains.length == 1) {
			sql.append(" and root_domain_reverse = '" + rootDomains[0] + "'");
		} else {
			sql.append(" and root_domain_reverse in ('" + StringUtils.join(rootDomains, "','") + "') ");
		}
		sql.append(" ) AS t3 USING (keyword_rankcheck_id, ranking_date) ");
		if (isDebug) {
			System.out.println("-----------------------------------------");
			System.out.println(sql.toString());
		}
			
		 return sql.toString();
	}
	
	private static Date getMinDateForCheckingDuplicatedTask(TExportInfoEntity entity) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(entity.getCreateDate());
		cal.add(Calendar.DAY_OF_MONTH, -1 * DUPLICATED_TASK_CHECK_DATE_RANGE);
		return cal.getTime();
	}
	
	private void errorTaskHandler(TExportInfoEntity entity, String message) {
		exportInfoDAO.updateStatus(entity.getId(), TExportInfoEntity.EXPORT_ERROR);
		System.out.println("=Process export task failed! taskId:" + entity.getId() + ", OID:" + entity.getOwnDomainId() 
			+ ", status:" + entity.getStatus() + "->" + TExportInfoEntity.EXPORT_ERROR + message == null ? "" : message);
	}
	
}

abstract class Exportor {
	protected File file;
	protected List<String[]> competitors;
	public Exportor(File file, List<String[]> competitors) {
		this.file = file;
		this.competitors = competitors;
	}
	abstract void addHeader();
	abstract void appendData(List<Map<String, Object>> rows);
	abstract void addFooter();
	abstract void closeFile();
	void endFile() {
		addFooter();
		closeFile();
	}
}

class ExportorXML extends Exportor {
	private FileOutputStream fos;

	public ExportorXML(File file, List<String[]> competitors) {
		super(file, competitors);
		try {
			fos = new FileOutputStream(file, true);
		} catch (FileNotFoundException e) {
			e.printStackTrace();
		}
	}

	@Override
	void addHeader() {
		try {
			TransformerFactory transFactory = TransformerFactory.newInstance();
			Transformer transformer = transFactory.newTransformer();
			transformer.setOutputProperty("indent", "yes");
			transformer.setOutputProperty(OutputKeys.ENCODING, "utf-8");
			
			DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
			DocumentBuilder builder = factory.newDocumentBuilder();
			Document d = builder.newDocument();
			d.setXmlStandalone(true);
			
			Element element = d.createElement("keywords");
			Element elementChildOne = d.createElement("test");
			elementChildOne.setAttribute("attr", "test");
			element.appendChild(elementChildOne);
			d.appendChild(element);
			String str = toStringFromDoc(d.getDocumentElement());
			String subStr = str.substring(0, str.indexOf("<keywords>") + 10);
			fos.write(subStr.getBytes());
			
			fos.flush();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	/*
	 * dom to xml
	 */
	public String toStringFromDoc(Node document) {
		String result = null;

		if (document != null) {
			StringWriter strWtr = new StringWriter();
			StreamResult strResult = new StreamResult(strWtr);
			TransformerFactory tfac = TransformerFactory.newInstance();
			try {
				javax.xml.transform.Transformer t = tfac.newTransformer();
				t.setOutputProperty(OutputKeys.ENCODING, "UTF-8");
				t.setOutputProperty(OutputKeys.INDENT, "yes");
				t.setOutputProperty(OutputKeys.METHOD, "xml"); // xml, html,
				// text
				t.setOutputProperty(
						"{http://xml.apache.org/xslt}indent-amount", "4");
				t.transform(new DOMSource(document),
						strResult);
			} catch (Exception e) {
				System.err.println("XML.toString(Document): " + e);
			}
			result = strResult.getWriter().toString();
			try {
				strWtr.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}

		return result;
	}

	@Override
	void appendData(List<Map<String, Object>> rows) {
		try {
			DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
			DocumentBuilder builder = factory.newDocumentBuilder();
			Document d = builder.newDocument();
			d.setXmlStandalone(true);
			for (Map<String, Object> map : rows) {
				Element eltItem = d.createElement("keyword");

				Element eltName = d.createElement("name");
				eltName.appendChild(d.createTextNode(RankCheckUtils.decoderString(map.get("name").toString())));
				eltItem.appendChild(eltName);

				Element eltDate = d.createElement("date");
				eltDate.appendChild(d.createTextNode(map.get("date").toString()));
				eltItem.appendChild(eltDate);

				Element highestTrueRank = d.createElement("highestTrueRank");
				highestTrueRank.appendChild(d.createTextNode(map.get("highestTrueRank").toString()));
				eltItem.appendChild(highestTrueRank);

				Element highestWebRank = d.createElement("highestWebRank");
				highestWebRank.appendChild(d.createTextNode(map.get("highestWebRank").toString()));
				eltItem.appendChild(highestWebRank);

				Element highestRankUrl = d.createElement("highestRankUrl");
				if (StringUtils.isBlank(map.get("highestRankUrl").toString())) {
					highestRankUrl.appendChild(d.createTextNode("-"));
				} else {
					highestRankUrl.appendChild(d.createTextNode(map.get("highestRankUrl").toString()));
				}
				eltItem.appendChild(highestRankUrl);

				Element highestLocalRank = d.createElement("highestLocalRank");
				highestLocalRank.appendChild(d.createTextNode(map.get("highestLocalRank").toString()));
				eltItem.appendChild(highestLocalRank);

				Element highestNewsRank = d.createElement("highestNewsRank");
				highestNewsRank.appendChild(d.createTextNode(map.get("highestNewsRank").toString()));
				eltItem.appendChild(highestNewsRank);

				Element highestImageRank = d.createElement("highestImageRank");
				highestImageRank.appendChild(d.createTextNode(map.get("highestImageRank").toString()));
				eltItem.appendChild(highestImageRank);

				Element highestVideoRank = d.createElement("highestVideoRank");
				highestVideoRank.appendChild(d.createTextNode(map.get("highestVideoRank").toString()));
				eltItem.appendChild(highestVideoRank);

				Element highestStarRank = d.createElement("highestStarRank");
				highestStarRank.appendChild(d.createTextNode(map.get("highestStarRank").toString()));
				eltItem.appendChild(highestStarRank);

				Element monthlySearchVolume = d.createElement("monthlySearchVolume");
				monthlySearchVolume.appendChild(d.createTextNode(map.get("monthlySearchVolume").toString()));
				eltItem.appendChild(monthlySearchVolume);

				if (competitors != null && competitors.size() > 0) {
					Element competitorsEle = d.createElement("competitors");

					Map<String, String[]> competitorList = (Map) map.get("competitorRanMap");
					for (String[] competitorDomain : competitors) {
						String competitorName = competitorDomain[1] == null ? competitorDomain[0] : competitorDomain[1];
						String competitorDomainName = StringUtils.reverseDelimited(competitorName, '.');
						String landingPage = "";
						String rank = "101";
						if (competitorList.containsKey(competitorName)) {
							String[] val = competitorList.get(competitorName);
							landingPage = val[0];
							rank = val[1];
						}

						Element competitor = d.createElement("competitor");
						Element eltCompetitorName = d.createElement("competitorName");
						eltCompetitorName.appendChild(d.createTextNode(competitorDomainName));
						competitor.appendChild(eltCompetitorName);

						Element eltlandingPage = d.createElement("landingPage");
						if (StringUtils.isBlank(landingPage)) {
							landingPage = "http://" + competitorDomainName + "/";
						}
						eltlandingPage.appendChild(d.createTextNode(landingPage));
						competitor.appendChild(eltlandingPage);

						Element eltrank = d.createElement("rank");
						eltrank.appendChild(d.createTextNode(rank));
						competitor.appendChild(eltrank);

						competitorsEle.appendChild(competitor);
					}
					eltItem.appendChild(competitorsEle);
				}
				
				// export row
				String str = toStringFromDoc(eltItem);
				str =  "<keyword>" + StringUtils.substringAfter(str, "<keyword>");
				fos.write(str.getBytes());
				fos.flush();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		
	}

	@Override
	void addFooter() {
		String end = "</keywords>";
		try {
			fos.write(end.getBytes());
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	@Override
	void closeFile() {
		try {
			fos.close();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
	
}

class ExportorCSV extends Exportor {
	private CSVPrinter csvPrinter = null;
	public ExportorCSV(File file, List<String[]> competitors) {
		super(file, competitors);
		try {
			csvPrinter = new CSVPrinter(new BufferedWriter(new FileWriter(file)), KeywordResourceDownLoadAllV3.csvFullFormat);
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	@Override
	void addHeader() {
	}

	@Override
	void appendData(List<Map<String, Object>> rows) {
		for (Map<String, Object> objectMap : rows) {
			Map<String, String[]> competitorList = (Map) objectMap.get("competitorRanMap");
			if (competitors != null && competitors.size() > 0) {
				for (String[] competitorDomain : competitors) {
					String competitorName = competitorDomain[1] == null ? competitorDomain[0] : competitorDomain[1];
					String competitorDomainName = StringUtils.reverseDelimited(competitorName, '.');
					String landingPage = "";
					String rank = "101";
					if (competitorList.containsKey(competitorName)) {
						String[] val = competitorList.get(competitorName);
						landingPage = val[0];
						rank = val[1];
					}
					if (StringUtils.isBlank(landingPage)) {
						landingPage = "http://" + competitorDomainName + "/";
					}
					try {
						csvPrinter.printRecord(
								objectMap.get("name"),
								objectMap.get("date"),
								objectMap.get("highestTrueRank"),
								objectMap.get("highestWebRank"),
								objectMap.get("highestRankUrl"),
								objectMap.get("highestLocalRank"),
								objectMap.get("highestNewsRank"),
								objectMap.get("highestImageRank"),
								objectMap.get("highestVideoRank"),
								objectMap.get("highestStarRank"),
								objectMap.get("monthlySearchVolume"),
								competitorDomainName,
								landingPage,
								rank
						);
					} catch (IOException e) {
						e.printStackTrace();
					}
				}
			} else {
				try {
					csvPrinter.printRecord(
							objectMap.get("name"),
							objectMap.get("date"),
							objectMap.get("highestTrueRank"),
							objectMap.get("highestWebRank"),
							objectMap.get("highestRankUrl"),
							objectMap.get("highestLocalRank"),
							objectMap.get("highestNewsRank"),
							objectMap.get("highestImageRank"),
							objectMap.get("highestVideoRank"),
							objectMap.get("highestStarRank"),
							objectMap.get("monthlySearchVolume")
					);
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
		try {
			csvPrinter.flush();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	@Override
	void addFooter() {
	}

	@Override
	void closeFile() {
		try {
			csvPrinter.close();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
}
