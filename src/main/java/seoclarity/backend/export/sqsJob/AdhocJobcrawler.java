package seoclarity.backend.export.sqsJob;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.sqs.AmazonSQS;
import com.amazonaws.services.sqs.model.Message;
import com.google.gson.Gson;
import org.apache.commons.lang.StringUtils;
import seoclarity.backend.dao.actonia.UserDAO;
import seoclarity.backend.dao.actonia.adhoc.AutoAdhocRankProjectEntityDAO;
import seoclarity.backend.dao.clickhouse.gscclicksteam.GscClickSteamDAO;
import seoclarity.backend.dao.rankcheck.SeoClarityKeywordAdwordsEntityDAO;
import seoclarity.backend.entity.AgencyInfoEntity;
import seoclarity.backend.entity.actonia.UserEntity;
import seoclarity.backend.entity.actonia.adhoc.AdHocJobCrawlerEntity;
import seoclarity.backend.entity.actonia.adhoc.AutoAdhocRankProjectEntity;
import seoclarity.backend.entity.rankcheck.SeoClarityKeywordAdwordsEntity;
import seoclarity.backend.service.AgencyInfoManager;
import seoclarity.backend.utils.*;
import seoclarity.backend.utils.amazon.AwsCredentialsEnvKeyConstructor;
import seoclarity.backend.utils.amazon.SQSUtils;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.math.BigInteger;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 2023-12-12 最新版本
 * Adhoc Job crawler
 * https://www.wrike.com/open.htm?id=1082205455
 * get sqs job ->load file->send to ftp
 * Hao
 * <p>
 * s15 : src/main/java/seoclarity/backend/export/sqsJob/AdhocJobcrawler.java
 * /home/<USER>/source/adhocExtract/clarity-backend-scripts/AdhocJobcrawler.sh>>
 * /home/<USER>/source/adhocExtract/clarity-backend-scripts/log/adhocJobCrawler_`date'+\%Y\%m\%d'`.log 2>&1
 */
public class AdhocJobcrawler {

    private static final String accessKey = "********************";
    private static final String secretKey = "v7Qts+qswF9qDojbKZQ1BofRbWcQLJu8CLM+eXrf";
    private static String queueName = "SQS_OUT_JOB_ADHOC_";
    private static String orgQueueName = "ORIGINAL_JOB_ADHOC_";
    private static String FTP_LOC = "/home/<USER>/";
    private static String FOLDER = "/adHocDownload/";
    private static String LOCAL_PATH = "files/adhoc/";
    private static String GFJ_US_Market_Ad_Hoc_Weekly = "GFJ US Market Ad Hoc Weekly";
    private static int WEEKLY_DOMAIN_8711 = 8711;
    private static String LINE_BREAK = "<BR>";
    private static String countryCd = "";
    private static String query_big_or_normal_table = ""; // project 查询哪个表
    private static String dis_keyword_summary_annual_big_v2 = "dis_keyword_summary_annual_big_v2";
    private static String dis_keyword_summary_annual_normal_v2 = "dis_keyword_summary_annual_normal_v2";
    //https://www.wrike.com/open.htm?id=1193831742
    private static Integer big_v2_hash_mod = 2000; // big 表分成2000层
    private static Integer normal_v2_hash_mod = 500; // mormal 表分成500 层
    private static Integer max_query_size = 200; // 最多200 关键字查询一次

    //    private static String COUNTRY = "US";
    private static Map<Integer, List<String>> keywordHashModMap = new HashMap<>(); //表根据keywordHash%2000分成不同片区存放keywords ， big:2000片 ， normal :500片

    private static boolean test = false;
    static final String S3_ACCESS_KEY = AwsCredentialsEnvKeyConstructor.getInstance().getPlainTextS3AccessKey();
    static final String S3_SECRET_KEY = AwsCredentialsEnvKeyConstructor.getInstance().getS3DecryptedSecretKey();
    private static final String S3_FOLDER = "gfj/";
    private static final String S3_BUCKET_NAME = "msci-seoclarity-extracts";
    static final BasicAWSCredentials awsCreds = new BasicAWSCredentials(S3_ACCESS_KEY, S3_SECRET_KEY);
    static final AmazonS3 s3 = AmazonS3ClientBuilder.standard()
            .withCredentials(new AWSStaticCredentialsProvider(awsCreds)).withRegion("us-east-2").build();

    /* 重试获取q次数 */
    private final int TRY_CNT = 3;
    // 获取q 失败次数
    private final int MISS_CNT = 3;
    // 每次拿消息的数量
    private final int message_max_size = 10;
    // sqs visibilityTimeOut
    // visibility_timeout = message_size_thread * 处理每条数据消耗的时间 * 1.2(系数)
    private static int visibility_timeout = 3600;
    private final int max_visibility_timeout = 43200;
    private static AmazonSQS amazonSQS;

    //statistical data fot Leo
    private Integer keywordCnt = 0;
    private Integer jobEntityVOsCnt = 0;
    private Integer jobTitleCnt = 0;
    private Integer jobCompanyCnt = 0;
    private Integer jobLocationCnt = 0;
    private Integer jobSourceCnt = 0;
    private Integer jobPostTimeCnt = 0;
    private Integer jobTypeCnt = 0;
    private Integer jobPrimaryJobLinkCnt = 0;
    private Integer applyOnListCnt = 0;
    private Integer jsCompanyCnt = 0;
    private Integer companyIdCnt = 0;
    private Integer companyCityCnt = 0;
    private Integer companyStateCnt = 0;
    private Integer minSalaryCnt = 0;
    private Integer maxSalaryCnt = 0;
    private Integer salaryUnitCnt = 0;
    private Integer currencyCodeCnt = 0;
    private Integer ratingArrayCnt = 0;
    private Integer ratingArray_linkCnt = 0;
    private Integer ratingArray_sourceCnt = 0;
    private Integer ratingArray_starRatingCnt = 0;
    private Integer ratingArray_reviewCountCnt = 0;
    private Integer salaryArrayCnt = 0;
    private Integer salaryArray_urlCnt = 0;
    private Integer salaryArray_titleCnt = 0;
    private Integer salaryArray_metaCnt = 0;
    private Integer salaryArray_salaryCnt = 0;
    private Integer salaryArray_salaryUnitCnt = 0;
    private Integer salaryArray_sourceCnt = 0;

    static {
        amazonSQS = SQSUtils.getAmazonSQS();
    }


    private AutoAdhocRankProjectEntityDAO autoAdhocRankProjectEntityDAO;
    private AgencyInfoManager agencyInfoManager;
    private ZeptoMailSenderComponent zeptoMailSenderComponent;
    private UserDAO userDAO;
    private SeoClarityKeywordAdwordsEntityDAO seoClarityKeywordAdwordsEntityDAO;
    private GscClickSteamDAO gscClickSteamDAO;

    public AdhocJobcrawler() {
        autoAdhocRankProjectEntityDAO = SpringBeanFactory.getBean("autoAdhocRankProjectEntityDAO");
        agencyInfoManager = SpringBeanFactory.getBean("agencyInfoManager");
        zeptoMailSenderComponent = SpringBeanFactory.getBean("zeptoMailSenderComponent");
        userDAO = SpringBeanFactory.getBean("userDAO");
        seoClarityKeywordAdwordsEntityDAO = SpringBeanFactory.getBean("seoClarityKeywordAdwordsEntityDAO");
        gscClickSteamDAO = SpringBeanFactory.getBean("gscClickSteamDAO");
    }

    public static void main(String[] args) {
        if (null != args && args.length > 0) {
            if (args[0].equals("test")) {
                System.out.println("===###test=============================================");
                test = true;
                visibility_timeout = 360;
                max_query_size = 2;
                WEEKLY_DOMAIN_8711 = 4;
                GFJ_US_Market_Ad_Hoc_Weekly = "haoTest";
            }
        }
        AdhocJobcrawler in = new AdhocJobcrawler();
        in.process();
    }

    private void process() {

        long start = System.currentTimeMillis();
        //  (1): auto_adhoc_rank_project:  retrieveType=10(job) (2)rankStatus=2(完成)  (3)rankUploadStatus!=2(未完成)
        List<AutoAdhocRankProjectEntity> projectList = autoAdhocRankProjectEntityDAO.getProject(AutoAdhocRankProjectEntity.RETRIEVESV_STATUS_ADHOC_JOB_CRAWLER);
        System.out.println(" =====###getProjectFromSQSByProjectId : " + projectList.size());

        if (projectList.size() > 0) {
            try {
                getDataFromSQS(projectList);
            } catch (Exception e) {

                String message = "Failed Adhoc Weekly 8711  ";
                sendMailReport(0, "Failed", message);
                e.printStackTrace();
            }
        } else {
            System.out.println("=====###noAdhocJobCrawlerTask======================");
        }
        long end = System.currentTimeMillis();
        System.out.println("process time: " + (end - start) / 1000);
    }

    private void sendMailReport(int projectId, String status, String message) {
        Map<String, Object> reportMap = new HashMap<String, Object>();
        reportMap.put("userName", "Jason");
        reportMap.put("successMessage", message);
        String emailTo = "<EMAIL>";
        String subject = "Export Adhoc projectId " + projectId + "  " + status;
        String[] ccTo = new String[]{"<EMAIL>"};
        ccTo = null;

//        emailSenderComponent.sendMimeMultiPartMailAndBcc(emailTo, ccTo, subject, "mail_resource_operate_report.txt", "mail_resource_operate_report.html", reportMap);
        zeptoMailSenderComponent.sendMimeMultiPartZeptoMailAndBccByFunctionType(emailTo, ccTo, subject, "mail_resource_operate_report.txt", "mail_resource_operate_report.html", reportMap,
                null, ZeptoMailSenderComponent.FUNCTION_TYPE_BACKEND_INTERNAL, null, null);
    }

    private void getDataFromSQS(List<AutoAdhocRankProjectEntity> projectList) throws Exception {

        for (AutoAdhocRankProjectEntity autoEntity : projectList) {
            cleancount();
            // 一个project 创建一个本地文件
            Integer domainid = autoEntity.getOwnDomainId();
            Integer engineId = autoEntity.getSearchEngineId();
            Integer languageId = autoEntity.getLanguageId();
            Integer cityId = autoEntity.getCityId();

            File remoteFolder = new File(LOCAL_PATH + domainid + FOLDER);
            if (!remoteFolder.exists()) {
                remoteFolder.mkdirs();
            }
            // 8711 adhoc 特殊处理 更改文件名上传s3
            String fname = queueName + autoEntity.getId().toString() + ".txt";
            String orgName = orgQueueName + autoEntity.getId().toString() + ".txt";
            String path = LOCAL_PATH + domainid + FOLDER + fname;
            String orgpath = LOCAL_PATH + domainid + FOLDER + orgName;

            if (autoEntity.getOwnDomainId() == WEEKLY_DOMAIN_8711 && StringUtils.startsWithIgnoreCase(autoEntity.getProjectName(), GFJ_US_Market_Ad_Hoc_Weekly)) {
                fname = "JOB_ADHOC_" + autoEntity.getId().toString() + "_" + autoEntity.getCreateDate() + ".txt";
                path = LOCAL_PATH + domainid + FOLDER + fname;

                orgName = "ORIGINAL_JOB_ADHOC_" + autoEntity.getId().toString() + "_" + autoEntity.getCreateDate() + ".txt";
                orgpath = LOCAL_PATH + domainid + FOLDER + orgName;
            }
            File fi = new File(path);
            FileWriter fileWriter = new FileWriter(fi, true);
            System.out.println(autoEntity.getId() + " domain : " + domainid + " path " + path);

            System.out.println("===###保存原始文件到 ： " + orgpath);
            File originalSQSFiles = new File(orgpath);
            FileWriter originalSQSFilesWriter = new FileWriter(originalSQSFiles, true);

            List<AdHocJobCrawlerEntity> adHocJobCrawlerEntityList = new ArrayList<>();
            List<String> keywordSearhList = new ArrayList<>();

            flagWh:
            while (true) {
                String queueUrl = SQSUtils.createQueue(queueName + autoEntity.getId().toString(), amazonSQS);
//                String queueUrl = SQSUtils.createQueue("SQS_OUT_JOB_ADHOC_ZSHTEST_01", amazonSQS);
                if (StringUtils.isNotBlank(queueUrl)) {
                    System.out.println("===###start process q: " + queueUrl);

                    int tryCnt = TRY_CNT;
                    for (int i = 0; i < tryCnt; i++) {
                        Integer messageCnt = null;
                        try {
                            messageCnt = SQSUtils.getMessageNumberInQueue(amazonSQS, queueUrl);
                        } catch (Exception e) {
                            System.out.println("===###=>can not get message cnt: " + CollectionUtils.getErrorMsg(e) + ", will sleep 30s");
                            try {
                                Thread.sleep(30 * 1000);
                            } catch (InterruptedException ex) {
                                ex.printStackTrace();
                            }
                        }

                        if (messageCnt != null && messageCnt > 0) {
                            System.out.println("===###messageCnt : " + messageCnt);
                            List<Message> messageFromQueue = SQSUtils.getMessageFromQueue(amazonSQS, queueUrl, message_max_size, visibility_timeout);
                            if (messageFromQueue.size() == 0) {

                            }
                            // 取出数据 写入文件
                            for (Message message : messageFromQueue) {
                                String k = message.getBody();

                                try {
                                    //保存原始sqs message
                                    writeSQSOriginalToFile(originalSQSFilesWriter, k);
                                    originalSQSFilesWriter.write("\r\n");
                                } catch (Exception e) {
                                    System.out.println("===###原始sqs message  保存失败 =====");
                                }

                                AdHocJobCrawlerEntity entity;
                                entity = getFileInfo(k, domainid);
                                adHocJobCrawlerEntityList.add(entity);
                                String kw = entity.getKeyword();
                                keywordSearhList.add(kw);
//                                System.out.println(" ====###kw : " + kw);

                            }

                            if (keywordSearhList.size() >= messageCnt) {
                                System.out.println(" ===###消息拿完了=================");
                                break;
                            }
                            System.out.println("===###keywordSearhList : " + keywordSearhList.size());
                            continue flagWh;
                        } else {
                            try {
                                System.out.println("===###=>approximateNumberOfMessages is 0, will wait 30s and try again.");
                                Thread.sleep(30 * 1000);
                            } catch (InterruptedException e) {
                                e.printStackTrace();
                            }
                        }
                    }

                    System.out.println("===###keywordSearhList : " + keywordSearhList.size() + " ,adHocJobCrawlerEntityList : " + adHocJobCrawlerEntityList.size());

                    if (domainid == WEEKLY_DOMAIN_8711) {
                        //keyword 去重
                        Set<String> removeDuplicatesKeywords = new HashSet<>();
                        removeDuplicatesKeywords.addAll(keywordSearhList);
                        keywordSearhList = removeDuplicatesKeywords.stream().collect(Collectors.toList());
                        // 根据keyword hash 进行分片
                        //https://www.wrike.com/open.htm?id=1193831742
                        queryByBigOrNormalTable(engineId, languageId, keywordSearhList);

                        //所有的 kw sv 数据
                        Map<String, Integer> allKwSearchVolumMap = new HashMap<>();
                        // 所有 kw truedemand 数据
                        Map<String, Integer> allKwTrueDemandMap = new HashMap<>();


                        for (Integer mod : keywordHashModMap.keySet()) {
                            List<String> keywordListOfMod = keywordHashModMap.get(mod);
                            if (keywordListOfMod.size() <= 0) {
                                continue;
                            }
                            if (keywordListOfMod.size() > max_query_size) {
                                // mod 中 keyword 过多， 分成200 个一组查询
                                List<List<String>> keywordsSpit200 = CollectionUtils.splitCollectionBySize(keywordListOfMod, max_query_size);
                                for (List<String> list : keywordsSpit200) {
                                    getKwSearchVolumeMap(99, languageId, cityId, list, allKwSearchVolumMap);
                                    getTrueDemandMap(mod, list, allKwTrueDemandMap);
                                }
                            } else {
                                getKwSearchVolumeMap(99, languageId, cityId, keywordListOfMod, allKwSearchVolumMap);
                                getTrueDemandMap(mod, keywordListOfMod, allKwTrueDemandMap);

                            }

                        }

                        for (AdHocJobCrawlerEntity en : adHocJobCrawlerEntityList) {
                            writeJsonToFile_8711(fileWriter, en, allKwSearchVolumMap, allKwTrueDemandMap);
                            fileWriter.write("\r\n");
                        }
                    } else {
                        for (AdHocJobCrawlerEntity en : adHocJobCrawlerEntityList) {
                            writeJsonToFile(fileWriter, en);
                            fileWriter.write("\r\n");
                        }
                    }

                    fileWriter.flush();
                    originalSQSFilesWriter.flush();
                    System.out.println("===###queryUrl: " + queueUrl + ", Q message is null. will del this Q!");

                    //" ===###文件可能为空文件，检查一下 ======================="
                    File file = new File(path);
                    if (null == file || 0 == file.length() || !file.exists()) {
                        System.out.println("===###file文件为空！");
                    }

                    File orgfile = new File(orgpath);
                    if (null == orgfile || 0 == orgfile.length() || !orgfile.exists()) {
                        System.out.println("===###orgfile文件为空！");
                    }

                    System.out.println("====###文件上传ftp ================================");
                    saveFilesToFtp(path, domainid);

                    System.out.println("====###orgpath文件上传ftp ================================");
                    saveFilesToFtp(orgpath, domainid);

                    System.out.println("====###weeklyDomain文件上传s3 ================================");
                    if (autoEntity.getOwnDomainId() == WEEKLY_DOMAIN_8711 && StringUtils.startsWithIgnoreCase(autoEntity.getProjectName(), GFJ_US_Market_Ad_Hoc_Weekly)) {
                        if (!test) {
                            sendToS3(file);
                        }
                    }

                    System.out.println("====###更新 auto_adhoc_rank_project 状态为完成 ================================");
                    if (!test) {
                        autoAdhocRankProjectEntityDAO.updateRankUploadStatusAndExtractStatus(autoEntity.getId(), AutoAdhocRankProjectEntity.RANK_STATUS_COMPLETEED_WITHOUT_ERROR, AutoAdhocRankProjectEntity.RANK_STATUS_COMPLETEED_WITHOUT_ERROR, keywordSearhList.size());
                    }
                    try {
                        System.out.println("====###删除sqs ================================");
                        System.out.println(queueUrl);
                        if (!test) {
                            amazonSQS.deleteQueue(queueUrl);
                        }
                    } catch (Exception e) {
                        // 当前q可能已经被被其他服务器删除
                        System.out.println("$EC=>del Q failed. msg: " + CollectionUtils.getErrorMsg(e));
                        try {
                            Thread.sleep(10 * 1000);
                        } catch (InterruptedException interruptedException) {
                            interruptedException.printStackTrace();
                        }
                    }
                    System.out.println("===###sendEmail ====================================");
                    UserEntity user = userDAO.getUser(autoEntity.getCreateUserId());

                    sendEmail(user.getName(), user.getEmail(), path, fname,
                            autoEntity.getOwnDomainId(), autoEntity.getProjectName());

//                    if (autoEntity.getOwnDomainId() == WEEKLY_DOMAIN_8711) {
                    sendEmailToLeo(null, user.getEmail(), path, fname,
                            autoEntity.getOwnDomainId(), autoEntity.getProjectName());
//                    }
                    System.out.println("====###删除本地文件 ================================");
                    deleteFiles(path);
                }
                break;
            }

        }
    }

    private void queryByBigOrNormalTable(Integer engineId, Integer languageId, List<String> keywordSearhList) {
        List<String> countryCds = gscClickSteamDAO.getCountryCode(engineId, languageId);

        if (countryCds.size() <= 0) {

        } else {
            countryCd = countryCds.get(0);
            System.out.println("===###countryCd : " + countryCd);
            List<String> countryCds_big = gscClickSteamDAO.getCountryCode_v2(countryCd, dis_keyword_summary_annual_big_v2);
            if (countryCds_big.size() > 0) {
                System.out.println("===###使用 dis_keyword_summary_annual_big_v2 表 2000 片");
                // 使用 big 表 分成2000 片
                query_big_or_normal_table = dis_keyword_summary_annual_big_v2;
                modKeywordList(keywordSearhList, big_v2_hash_mod);
            } else {
                List<String> countryCds_normal = gscClickSteamDAO.getCountryCode_v2(countryCd, dis_keyword_summary_annual_normal_v2);
                if (countryCds_normal.size() > 0) {
                    System.out.println("===###使用 dis_keyword_summary_annual_normal_v2 表 500 片 ");
                    // 使用 normal 表 分成500 片
                    query_big_or_normal_table = dis_keyword_summary_annual_normal_v2;
                    modKeywordList(keywordSearhList, normal_v2_hash_mod);
                }
            }
        }
    }

    private void modKeywordList(List<String> keywordSearhList, Integer modSize) {

        for (String keywordName : keywordSearhList) {
            BigInteger keywordHash = seoclarity.backend.utils.cityhash.CityHashUtil.getUrlHashForBigIntegerLowercase(FormatUtils.decodeKeyword(keywordName));
            BigInteger mod_bigint = keywordHash.mod(BigInteger.valueOf(modSize));
            Integer mod = mod_bigint.intValue();
            System.out.println("===###keywordhash : " + keywordHash +"keywordName " + keywordName + ", decodeKeyword  : " +FormatUtils.decodeKeyword(keywordName) + ",  mod :" + mod);
            if (null == keywordHashModMap.get(mod) || keywordHashModMap.get(mod).size() <= 0) {
                List<String> keywordList = new ArrayList<>();
                keywordList.add(keywordName);
                keywordHashModMap.put(mod, keywordList);
            } else {
                List<String> oldKeywordList = keywordHashModMap.get(mod);
                oldKeywordList.add(keywordName);
                keywordHashModMap.put(mod, oldKeywordList);
            }
        }
    }

    private void cleancount() {
        keywordCnt = 0;
        jobEntityVOsCnt = 0;
        jobTitleCnt = 0;
        jobCompanyCnt = 0;
        jobLocationCnt = 0;
        jobSourceCnt = 0;
        jobPostTimeCnt = 0;
        jobTypeCnt = 0;
        jobPrimaryJobLinkCnt = 0;
        applyOnListCnt = 0;
        jsCompanyCnt = 0;
        companyIdCnt = 0;
        companyCityCnt = 0;
        companyStateCnt = 0;
        minSalaryCnt = 0;
        maxSalaryCnt = 0;
        salaryUnitCnt = 0;
        currencyCodeCnt = 0;
        ratingArrayCnt = 0;
        ratingArray_linkCnt = 0;
        ratingArray_sourceCnt = 0;
        ratingArray_starRatingCnt = 0;
        ratingArray_reviewCountCnt = 0;
        salaryArrayCnt = 0;
        salaryArray_urlCnt = 0;
        salaryArray_titleCnt = 0;
        salaryArray_metaCnt = 0;
        salaryArray_salaryCnt = 0;
        salaryArray_salaryUnitCnt = 0;
        salaryArray_sourceCnt = 0;
    }

    private Map<String, Integer> getKwSearchVolumeMap(Integer engineId, Integer languageId, Integer cityId, List<String> keywordSearhList, Map<String, Integer> kwSearckVolum) {
        List<SeoClarityKeywordAdwordsEntity> seoClarityKeywordAdwordsEntities = seoClarityKeywordAdwordsEntityDAO.getExistedAdwordListByKeywordName(keywordSearhList, engineId, languageId, cityId);
        seoClarityKeywordAdwordsEntities.forEach(obj -> {
            kwSearckVolum.put(obj.getKeywordName(), obj.getAvgMonthlySearchVolume());
        });
        return kwSearckVolum;
    }

    private Map<String, Integer> getTrueDemandMap(Integer mod, List<String> keywordSearhList, Map<String, Integer> result) throws Exception {

        List<Map<String, Object>> trueDemandList = gscClickSteamDAO.getTrueDemandByKeywordAndCountryCd(mod, countryCd, keywordSearhList, query_big_or_normal_table);
        if (trueDemandList.size() > 0) {
            for (Map<String, Object> trueDemandMap : trueDemandList) {
                String keyword = trueDemandMap.get("keywordName").toString();
                keyword = URLEncoder.encode(keyword, "utf-8").toLowerCase();
                String trueDemand = trueDemandMap.get("trueDemand").toString();
                if (trueDemand.contains(",")) {
                    trueDemand = trueDemand.replace(",", "");
                } else if (trueDemand.contains(".")) {
                    trueDemand = trueDemand.substring(0, trueDemand.indexOf("."));
                }
                result.put(keyword, Integer.parseInt(trueDemand));
            }
        }
        return result;
    }

    private void saveFilesToFtp(String localFileName, Integer domainid) {
        String targetPath = FTP_LOC + domainid + FOLDER;
        System.out.println("===###savefileToFTP =====" + targetPath + " file : " + localFileName);
        FTPUtils.saveFileToFTP(domainid, localFileName, targetPath);
    }

    private void deleteFiles(String fileName) {
        File localFile = new File(fileName);
        FileUtils.deleteFile(localFile);
    }

    private AdHocJobCrawlerEntity getFileInfo(String k, Integer domainId) {

        AdHocJobCrawlerEntity en = new AdHocJobCrawlerEntity();
        JSONObject jasonk = JSONObject.parseObject(k);
        //keyword level
        if (null != jasonk.get("keyword")) {
            String keyword = jasonk.get("keyword").toString();
            en.setKeyword(keyword);
            keywordCnt++;
        }
        if (null != jasonk.get("queryDate")) {
            String queryDate = jasonk.get("queryDate").toString();
            en.setQueryDate(queryDate);
        }
//        if (null != jasonk.get("searchVolume")) {
//            String searchVolume = jasonk.get("searchVolume").toString();
//            en.setSearchVolume(searchVolume);
//        }
//        if (null != jasonk.get("trueDemand")) {
//            String cityId = jasonk.get("cityId").toString();
//            en.setCityId(cityId);
//        }
        if (null != jasonk.get("cityName")) {
            String cityName = jasonk.get("cityName").toString();
            en.setCityName(cityName);
        }

        // url level
        if (null == jasonk.get("jobEntityVOs")) {
            System.out.println("===###keyword has null jobEntityVOs :  " + jasonk.get("keyword").toString());
        } else {

            JSONArray jobEntityVOsArr = jasonk.getJSONArray("jobEntityVOs");
            List<AdHocJobCrawlerEntity.JobEntityVO> voList = new ArrayList<>();
            jobEntityVOsCnt = jobEntityVOsCnt + jobEntityVOsArr.size();
            for (Object obj : jobEntityVOsArr) {
                AdHocJobCrawlerEntity.JobEntityVO jobEntityVO = en.new JobEntityVO();
                JSONObject jobJson = (JSONObject) JSONObject.toJSON(obj);
                if (null != jobJson.get("jobTitle")) {
                    String jobTitle = jobJson.get("jobTitle").toString();
                    jobEntityVO.setJobTitle(jobTitle);
                    jobTitleCnt++;
                }
                if (null != jobJson.get("jobCompany")) {
                    String jobCompany = jobJson.get("jobCompany").toString();
                    jobEntityVO.setJobCompany(jobCompany);
                    jobCompanyCnt++;
                }
                if (null != jobJson.get("jobLocation")) {
                    String jobLocation = jobJson.get("jobLocation").toString();
                    jobEntityVO.setJobLocation(jobLocation);
                    jobLocationCnt++;
                }
                if (null != jobJson.get("jobSource")) {
                    String jobSource = jobJson.get("jobSource").toString();
                    jobEntityVO.setJobSource(jobSource);
                    jobSourceCnt++;
                }
                if (null != jobJson.get("jobPostTime")) {
                    String jobPostTime = jobJson.get("jobPostTime").toString();
                    jobEntityVO.setJobPostTime(jobPostTime);
                    jobPostTimeCnt++;
                }
                if (null != jobJson.get("jobType")) {
                    String jobType = jobJson.get("jobType").toString();
                    jobEntityVO.setJobType(jobType);
                    jobTypeCnt++;
                }
                if (null != jobJson.get("jobPrimaryJobLink")) {
                    String jobPrimaryJobLink = jobJson.get("jobPrimaryJobLink").toString();
                    jobEntityVO.setJobPrimaryJobLink(jobPrimaryJobLink);
                    jobPrimaryJobLinkCnt++;
                }
                if (null != jobJson.get("jsCompany")) {
                    String jsCompany = jobJson.get("jsCompany").toString();
                    jobEntityVO.setJsCompany(jsCompany);
                    jsCompanyCnt++;
                }
                if (null != jobJson.get("companyId")) {
                    String companyId = null == jobJson.get("companyId") ? "" : jobJson.get("companyId").toString();
                    jobEntityVO.setCompanyId(companyId);
                    companyIdCnt++;
                }
                if (null != jobJson.get("companyCity")) {
                    String companyCity = null == jobJson.get("companyCity") ? "" : jobJson.get("companyCity").toString();
                    jobEntityVO.setCompanyCity(companyCity);
                    companyCityCnt++;
                }
                if (null != jobJson.get("companyState")) {
                    String companyState = null == jobJson.get("companyState") ? "" : jobJson.get("companyState").toString();
                    jobEntityVO.setCompanyState(companyState);
                    companyStateCnt++;
                }
                if (null != jobJson.get("minSalary")) {
                    String minSalary = null == jobJson.get("minSalary") ? "" : jobJson.get("minSalary").toString();
                    jobEntityVO.setMinSalary(minSalary);
                    minSalaryCnt++;
                }
                if (null != jobJson.get("maxSalary")) {
                    String maxSalary = null == jobJson.get("maxSalary") ? "" : jobJson.get("maxSalary").toString();
                    jobEntityVO.setMaxSalary(maxSalary);
                    maxSalaryCnt++;
                }
                if (null != jobJson.get("salaryUnit")) {
                    String salaryUnit = null == jobJson.get("salaryUnit") ? "" : jobJson.get("salaryUnit").toString();
                    jobEntityVO.setSalaryUnit(salaryUnit);
                    salaryUnitCnt++;
                }
                if (null != jobJson.get("currencyCode")) {
                    String currencyCode = null == jobJson.get("currencyCode") ? "" : jobJson.get("currencyCode").toString();
                    jobEntityVO.setCurrencyCode(currencyCode);
                    currencyCodeCnt++;
                }
                if (null != jobJson.get("applyOnList")) {
                    Object applyOnObj = jobJson.get("applyOnList");
                    Map<String, String> applyOnList = new HashMap<>();
                    Map<String, String> applyOnMap = JSONObject.parseObject(JSONObject.toJSONString(applyOnObj), Map.class);
                    applyOnListCnt = applyOnListCnt + applyOnMap.size();
                    for (String s : applyOnMap.keySet()) {
                        applyOnList.put(s, applyOnMap.get(s));
                    }
                    jobEntityVO.setApplyOnList(applyOnList);
                }

                JSONArray rantingArr = new JSONArray();
                if (null != jobJson.get("ratingArray") && jobJson.getJSONArray("ratingArray").size() > 0) {
                    rantingArr = jobJson.getJSONArray("ratingArray");
                    List<AdHocJobCrawlerEntity.JobEntityVO.RatingEntity> rantingList = new ArrayList<>();
                    ratingArrayCnt = ratingArrayCnt + rantingArr.size();
                    for (Object rantingObj : rantingArr) {
                        JSONObject rantingJson = (JSONObject) JSONObject.toJSON(rantingObj);
                        AdHocJobCrawlerEntity.JobEntityVO.RatingEntity rantingEntity = jobEntityVO.new RatingEntity();
                        if (null != rantingJson.get("link")) {
                            String link = rantingJson.get("link").toString();
                            rantingEntity.setLink(link);
                            ratingArray_linkCnt++;
                        }
                        if (null != rantingJson.get("source")) {
                            String source = rantingJson.get("source").toString();
                            rantingEntity.setSource(source);
                            ratingArray_sourceCnt++;
                        }
                        if (domainId == WEEKLY_DOMAIN_8711) { //https://www.wrike.com/open.htm?id=1177965676
                            if (null != rantingJson.get("rank")) {
                                String rank = rantingJson.get("rank").toString();
                                rantingEntity.setRank(rank);
                            }
                            if (null != rantingJson.get("starRating")) {
                                String starRating = rantingJson.get("starRating").toString();
                                rantingEntity.setStarRating(starRating);
                                ratingArray_starRatingCnt++;
                            }
                            if (null != rantingJson.get("reviewCount")) {
                                String reviewCount = rantingJson.get("reviewCount").toString();
                                rantingEntity.setReviewCount(reviewCount);
                                ratingArray_reviewCountCnt++;
                            }
                        }
                        if (null != rantingEntity && (StringUtils.isNotBlank(rantingEntity.getLink()) || StringUtils.isNotBlank(rantingEntity.getSource()))) {
                            rantingList.add(rantingEntity);
                        }
                    }
                    jobEntityVO.setRatingArray(rantingList);
                }

                //   salaryArray
                JSONArray salaryArr = new JSONArray();
                if (null != jobJson.get("salaryArray") && jobJson.getJSONArray("salaryArray").size() > 0) {
                    salaryArr = jobJson.getJSONArray("salaryArray");
                    List<AdHocJobCrawlerEntity.JobEntityVO.SalaryEntity> salaryList = new ArrayList<>();
                    salaryArrayCnt = salaryArrayCnt + salaryArr.size();
                    for (Object salaryObj : salaryArr) {
                        JSONObject salaryJson = (JSONObject) JSONObject.toJSON(salaryObj);
                        AdHocJobCrawlerEntity.JobEntityVO.SalaryEntity salaryEntity = jobEntityVO.new SalaryEntity();
                        if (null != salaryJson.get("url")) {
                            String url = salaryJson.get("url").toString();
                            salaryEntity.setUrl(url);
                            salaryArray_urlCnt++;
                        }
                        if (null != salaryJson.get("title")) {
                            String title = salaryJson.get("title").toString();
                            salaryEntity.setTitle(title);
                            salaryArray_titleCnt++;
                        }
                        if (null != salaryJson.get("meta")) {
                            String meta = salaryJson.get("meta").toString();
                            salaryEntity.setMeta(meta);
                            salaryArray_metaCnt++;
                        }
                        if (null != salaryJson.get("salary")) {
                            String salary = salaryJson.get("salary").toString();
                            salaryEntity.setSalary(salary);
                            salaryArray_salaryCnt++;
                        }
                        if (null != salaryJson.get("salary_unit")) {
                            String salary_unit = salaryJson.get("salary_unit").toString();
                            salaryEntity.setSalaryUnit(salary_unit);
                            salaryArray_salaryUnitCnt++;
                        }
                        if (null != salaryJson.get("source")) {
                            String source = salaryJson.get("source").toString();
                            salaryEntity.setSource(source);
                            salaryArray_sourceCnt++;
                        }
                        if (domainId == WEEKLY_DOMAIN_8711) {
                            if (null != salaryJson.get("rank")) {
                                String rank = salaryJson.get("rank").toString();
                                if (rank.contains(".")) {
                                    rank = rank.substring(0, rank.indexOf("."));
                                }
                                salaryEntity.setRank(rank);
                            }
                        }
                        if (null != salaryEntity && (StringUtils.isNotBlank(salaryEntity.getUrl()) || StringUtils.isNotBlank(salaryEntity.getTitle()))) {
                            salaryList.add(salaryEntity);
                        }
                    }
                    jobEntityVO.setSalaryArray(salaryList);
                }
                voList.add(jobEntityVO);
            }
            en.setJobEntityVOs(voList);
        }
        return en;
    }

    public void writeJsonToFile(FileWriter fileWriter, AdHocJobCrawlerEntity entity) {
        try {
            String keyword = entity.getKeyword();
            Gson gson = new Gson();
            String jsonString = gson.toJson(entity);
            fileWriter.write(jsonString);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public void writeJsonToFile_8711(FileWriter fileWriter, AdHocJobCrawlerEntity entity, Map<String, Integer> kwSearckVolumMap, Map<String, Integer> kwTrueDemand) {
        try {
            String keyword = entity.getKeyword();
            Integer searchVolume = kwSearckVolumMap.get(keyword);
            Integer trueDemand = kwTrueDemand.get(keyword);
            entity.setSearchVolume(searchVolume);
//            System.out.println("trueDemand : " + trueDemand);
            entity.setTrueDemand(trueDemand);
            Gson gson = new Gson();
            String jsonString = gson.toJson(entity);
            fileWriter.write(jsonString);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public void writeSQSOriginalToFile(FileWriter fileWriter, String k) {
        try {
            fileWriter.write(k);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

//    private void sendEmail(String userName, String emailAddress, String fileLoc, String filename, int ownDomainId, String projectName) throws Exception {
//
//        String emailTo = emailAddress;
//        emailTo = "<EMAIL>";
//        System.out.println("=========Send to : " + emailTo + " start!");
//
//        String subject = "Ad Hoc Data Retrieval Download Ready - " + projectName;
//        String info = "The raw data for ad hoc retrieval project - " + projectName + " is now ready for download.";
//
//        Map<String, Object> reportMap = new HashMap<String, Object>();
//        reportMap.put("userName", userName);
//        reportMap.put("info", info);
//
//        AgencyInfoEntity agencyInfo = agencyInfoManager.getByDomainId(ownDomainId);
//
//        int sizeMb = 0;
//        File file = new File(fileLoc + filename);
//        String downloadFileName = "";
//        if (file.exists()) {
//            long fileSize = file.length();
//            sizeMb = (int) (fileSize / 1024 / 1024);
//            System.out.println(" OID:" + ownDomainId + " filename: " + (fileLoc + filename) +
//                    " size:" + fileSize + " MB:" + sizeMb);
//
//            GZipUtil.zipFile(file.getAbsolutePath(), GZipUtil.ZIPFile_POSTFIX);
//            downloadFileName = file.getAbsolutePath() + GZipUtil.ZIPFile_POSTFIX;
//        }
//
//        FTPServerInfoBean ftpServerInfo = FTPUtils.getFTPServerInfo();
//        String host = ftpServerInfo.getPrivateHost();
//        String ftpUsername = ftpServerInfo.getServerUserName();
//        String ftpPassword = ftpServerInfo.getServerPassword();
//
////        FTPUtils.saveFileToFTPForAdhoc(host, ftpUsername, ftpPassword, ownDomainId, downloadFileName, true);
//        //https://www.wrike.com/open.htm?id=1066001811 move to seagate
//        String linkUrl = "";
//        try {
//            boolean isSaved = SeagateUtils.saveFileToDefaultSeagate(ownDomainId, downloadFileName);
//            if(!isSaved){
//                System.out.println("===send to Seagate Failed!projectName:" + projectName);
//            }
//            linkUrl = SeagateUtils.getDefaultSeagatePresignedUrl(ownDomainId, filename + GZipUtil.ZIPFile_POSTFIX);
//            System.out.println("==isSaved:" + isSaved + ",linkUrl:" + linkUrl);
//        }catch (Exception e){
//            e.printStackTrace();
//            System.out.println("===send to Seagate Failed!projectName:" + projectName);
//        }
//
//        String linkText = null;
//        try {
//            String name = URLEncoder.encode(filename + GZipUtil.ZIPFile_POSTFIX, "utf-8");
////            linkText = "https://downloads.seoclarity.net/adhocRankingExtract/" + StringUtils.replace(name, "+", "%20");
//        linkText = linkUrl;
//        } catch (UnsupportedEncodingException e) {
//            System.out.println(e.getMessage());
////            linkText = "https://downloads.seoclarity.net/adhocRankingExtract/" + filename + GZipUtil.ZIPFile_POSTFIX;
//            linkText = linkUrl;
//        }
//
//        System.out.println("  fileLink:" + linkText);
//        reportMap.put("fileLink", linkText);
////        String[] bccTo = new String[]{};
//        String[] bccTo = new String[]{"<EMAIL>", "<EMAIL>", "<EMAIL>", Constants.DEV_TEAM_EMAIL};
//        bccTo = new String[]{"<EMAIL>"};
//        zeptoMailSenderComponent.sendMimeMultiPartZeptoMailAndBccByFunctionType(emailTo, bccTo, subject, "mail_exportdata_backend_noattach.txt", "mail_exportdata_backend_noattach.html", reportMap, agencyInfo,
//                ZeptoMailSenderComponent.FUNCTION_TYPE_DOWNLOAD_ALL, null, null);
//        System.out.println("=========Send to : " + emailAddress + " success!");
//    }

    private void sendEmail(String userName, String emailAddress, String path, String fname, int ownDomainId, String projectName) throws Exception {


        String emailTo = emailAddress;
        emailTo = "<EMAIL>";
        System.out.println("=========Send to : " + emailTo + " start!");

        String subject = "Ad Hoc Data Retrieval Download Ready - " + projectName;
        String info = "The raw data for ad hoc retrieval project - " + projectName + " is now ready for download.";

        Map<String, Object> reportMap = new HashMap<String, Object>();
        reportMap.put("userName", userName);
        reportMap.put("info", info);

        AgencyInfoEntity agencyInfo = agencyInfoManager.getByDomainId(ownDomainId);
        int sizeMb = 0;
        File file = new File(path);
        String downloadFileName = "";
        if (file.exists()) {
            long fileSize = file.length();
            sizeMb = (int) (fileSize / 1024 / 1024);
            System.out.println(" OID:" + ownDomainId + " filename: " + (path) +
                    " size:" + fileSize + " MB:" + sizeMb);

            GZipUtil.zipFile(file.getAbsolutePath(), GZipUtil.ZIPFile_POSTFIX);
            downloadFileName = file.getAbsolutePath() + GZipUtil.ZIPFile_POSTFIX;
        } else {
            System.out.println("===###can not find file");
        }

        String linkUrl = "";
        try {
            boolean isSaved = SeagateUtils.saveFileToDefaultSeagate(ownDomainId, downloadFileName);
            if (!isSaved) {
                System.out.println("===send to Seagate Failed!projectName:" + projectName);
            }
            linkUrl = SeagateUtils.getDefaultSeagatePresignedUrl(ownDomainId, fname + GZipUtil.ZIPFile_POSTFIX);
            System.out.println("==isSaved:" + isSaved + ",linkUrl:" + linkUrl);
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("===send to Seagate Failed!projectName:" + projectName);
        }


        //https://www.wrike.com/open.htm?id=960781633
        String linkText = null;
//        try {
//            String name = URLEncoder.encode(filename, "utf-8");
        linkText = linkUrl;
//        } catch (UnsupportedEncodingException e) {
//            System.out.println(e.getMessage());
//            linkText = linkUrl;
//        }

        System.out.println("  fileLink:" + linkText);
        reportMap.put("fileLink", linkText);
        String[] bccTo = new String[]{"<EMAIL>", Constants.DEV_TEAM_EMAIL};
        bccTo = new String[]{"<EMAIL>", "<EMAIL>"};
        zeptoMailSenderComponent.sendMimeMultiPartZeptoMailAndBccByFunctionType(emailTo, bccTo, subject, "mail_exportdata_backend_noattach.txt", "mail_exportdata_backend_noattach.html", reportMap, agencyInfo,
                ZeptoMailSenderComponent.FUNCTION_TYPE_DOWNLOAD_ALL, null, null);

        System.out.println("=========Send to : " + emailAddress + " success!");
    }

    private void sendToS3(File f) {
        String s3FilePath = S3_FOLDER + f.getName();
        System.out.println("*** " + s3FilePath);
        try {
            s3.putObject(S3_BUCKET_NAME, s3FilePath, f);
        } catch (Exception e) {
            e.printStackTrace();
        }


//        S3Object s3Object = s3.getObject(S3_BUCKET_NAME, "gfj/sqs.txt");
//        System.out.println(s3Object);
        System.out.println(" ###===sendToS3 success");
    }


    private void sendEmailToLeo(String userName, String emailAddress, String path, String fname, int ownDomainId, String projectName) throws Exception {

        String subject = "Ad Hoc Data Retrieval Download Ready - " + projectName;
        String info = "===###statistical data for OID " + ownDomainId;
        info += "===### keyword count : " + keywordCnt + LINE_BREAK;
        info += "===### jobEntityVOs count : " + jobEntityVOsCnt + LINE_BREAK;
        info += "===### jobTitle count : " + jobTitleCnt + LINE_BREAK;
        info += "===### jobCompany count : " + jobCompanyCnt + LINE_BREAK;
        info += "===### jobLocation count : " + jobLocationCnt + LINE_BREAK;
        info += "===### jobSource count : " + jobSourceCnt + LINE_BREAK;
        info += "===### jobPostTime count : " + jobPostTimeCnt + LINE_BREAK;
        info += "===### jobPrimaryJobLink count : " + jobPrimaryJobLinkCnt + LINE_BREAK;
        info += "===### applyOnList count : " + applyOnListCnt + LINE_BREAK;
        info += "===### jsCompany count : " + jsCompanyCnt + LINE_BREAK;
        info += "===### companyId count : " + companyIdCnt + LINE_BREAK;
        info += "===### companyCity count : " + companyCityCnt + LINE_BREAK;
        info += "===### companyState count : " + companyStateCnt + LINE_BREAK;
        info += "===### minSalary count : " + minSalaryCnt + LINE_BREAK;
        info += "===### maxSalary count : " + maxSalaryCnt + LINE_BREAK;
        info += "===### salaryUnit count : " + salaryUnitCnt + LINE_BREAK;
        info += "===### currencyCode count : " + currencyCodeCnt + LINE_BREAK;
        info += "===### ratingArray count : " + ratingArrayCnt + LINE_BREAK;
        info += "===### ratingArray_link count : " + ratingArray_linkCnt + LINE_BREAK;
        info += "===### ratingArray_source count : " + ratingArray_sourceCnt + LINE_BREAK;
        info += "===### ratingArray_starRating count : " + ratingArray_starRatingCnt + LINE_BREAK;
        info += "===### ratingArray_reviewCount count : " + ratingArray_reviewCountCnt + LINE_BREAK;
        info += "===### salaryArray count : " + salaryArrayCnt + LINE_BREAK;
        info += "===### salaryArray_url count : " + salaryArray_urlCnt + LINE_BREAK;
        info += "===### salaryArray_title count : " + salaryArray_titleCnt + LINE_BREAK;
        info += "===### salaryArray_meta count : " + salaryArray_metaCnt + LINE_BREAK;
        info += "===### salaryArray_salary count : " + salaryArray_salaryCnt + LINE_BREAK;
        info += "===### salaryArray_salaryUnit count : " + salaryArray_salaryUnitCnt + LINE_BREAK;
        info += "===### salaryArray_source count : " + salaryArray_sourceCnt + LINE_BREAK;
        System.out.println(info);
        System.out.println(path);
        System.out.println(fname);

        Map<String, Object> reportMap = new HashMap<String, Object>();
        reportMap.put("userName", "");
        reportMap.put("info", info);

        AgencyInfoEntity agencyInfo = agencyInfoManager.getByDomainId(ownDomainId);
        int sizeMb = 0;
        File file = new File(path);
        String downloadFileName = "";
        try {
            if (file.exists()) {
                long fileSize = file.length();
                sizeMb = (int) (fileSize / 1024 / 1024);
                System.out.println(" OID:" + ownDomainId + " filename: " + (path) +
                        " size:" + fileSize + " MB:" + sizeMb);

                GZipUtil.zipFile(file.getAbsolutePath(), GZipUtil.ZIPFile_POSTFIX);
                downloadFileName = file.getAbsolutePath() + GZipUtil.ZIPFile_POSTFIX;
            }
        } catch (Exception e) {
            System.out.println(" zip file error downloadFileName : " + downloadFileName);
        }

        String linkUrl = "";
        try {
            boolean isSaved = SeagateUtils.saveFileToDefaultSeagate(ownDomainId, downloadFileName);
            if (!isSaved) {
                System.out.println("===send to Seagate Failed!projectName:" + projectName);
            }
            linkUrl = SeagateUtils.getDefaultSeagatePresignedUrl(ownDomainId, fname + GZipUtil.ZIPFile_POSTFIX);
            System.out.println("==isSaved:" + isSaved + ",linkUrl:" + linkUrl);
        } catch (Exception e) {
//            e.printStackTrace();
            System.out.println("===send to Seagate Failed!projectName:" + projectName);
        }


        //https://www.wrike.com/open.htm?id=960781633
        String linkText = null;
//        try {
//            String name = URLEncoder.encode(filename, "utf-8");
        linkText = linkUrl;
//        } catch (UnsupportedEncodingException e) {
//            System.out.println(e.getMessage());
//            linkText = linkUrl;
//        }

        System.out.println("  fileLink:" + linkText);
        reportMap.put("fileLink", linkText);
        String emailTo = "<EMAIL>";
        String[] bccTo = new String[]{"<EMAIL>", "<EMAIL>"};
        if (test) {
//            bccTo = new String[]{};
        }
        zeptoMailSenderComponent.sendMimeMultiPartZeptoMailAndBccByFunctionType(emailTo, bccTo, subject, "mail_exportdata_backend_noattach.txt", "mail_exportdata_backend_noattach.html", reportMap, agencyInfo,
                ZeptoMailSenderComponent.FUNCTION_TYPE_DOWNLOAD_ALL, null, null);

        System.out.println("=========Send to : " + emailAddress + " success!");
    }


}
