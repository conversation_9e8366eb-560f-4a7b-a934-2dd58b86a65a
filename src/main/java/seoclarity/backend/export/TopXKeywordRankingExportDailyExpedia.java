package seoclarity.backend.export;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.net.InetAddress;
import java.text.SimpleDateFormat;
import java.util.*;

import com.google.gson.Gson;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;

import com.google.common.net.InternetDomainName;

import ch.ethz.ssh2.Connection;
import ch.ethz.ssh2.SCPClient;
import scala.Array;
import seoclarity.backend.dao.clickhouse.ClDailyRankingEntityDao;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.actonia.ScriptDeployInfoEntity;
import seoclarity.backend.entity.actonia.ScriptRunInstanceEntity;
import seoclarity.backend.entity.actonia.extract.ExtractScriptConfigEntity;
import seoclarity.backend.entity.actonia.extract.ExtractScriptDetailEntity;
import seoclarity.backend.entity.actonia.extract.ExtractScriptInstanceEntity;
import seoclarity.backend.entity.actonia.extract.RankQcStateEntity;
import seoclarity.backend.entity.clickhouse.CLRankingDetailEntity;
import seoclarity.backend.service.BaseService;
import seoclarity.backend.service.CommonDataService;
import seoclarity.backend.service.ExtractService;
import seoclarity.backend.service.RankTypeManager;
import seoclarity.backend.service.ScKeywordRankManager;
import seoclarity.backend.utils.EmailSenderComponent;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.GZipUtil;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.utils.ZeptoMailSenderComponent;
import seoclarity.backend.utils.loggly.LogglyUtils;
import seoclarity.backend.utils.loggly.LogglyVO;

/**
 * <AUTHOR>
 * @create 2020-01-30 14:54
 * mvn exec:java -Dexec.mainClass="seoclarity.backend.export.TopXKeywordRankingExportDailyExpedia" -Dexec.args="40 true 2020-10-26 2020-10-26"
 **/
public class TopXKeywordRankingExportDailyExpedia extends BaseService {
	
	public final static int GROUP_CONCAT_MAX_LENGTH = 512;
    
	public static final int TYPE_WEB_RESOURCE = 1;

	public static final int TYPE_IMGAGE = 2;

	public static final int TYPE_ADDRESS = 3;

	public static final int TYPE_VIDEO = 4;

	public static final int TYPE_NEWS = 5;

	public static final int TYPE_SHOPPING = 6;
	
    public static final int TYPE_LOCALLISTING = 7;
    
	public static final int TYPE_TWITTER = 8;

	public static final int TYPE_ANSWERBOX = 9;

	public static final int TYPE_JOB_URL = 10;

	private static final String FTP_SERVER = "expediaftp.seoclarity.net";
	private static final String FTP_SERVER_USER = "expedia";
	private static final String FTP_SERVER_PW = "AmesEu";
	private static final String FTP_SERVER_PATH = "/Ranking/Unique/";

	private static final int SSH_TRY_COUNT = 20;

	private static final String DELIMITED = "	";

	private static final String NULL_VALUE = "\\N";
	public static final String NO_BREAK_SPACE = "\u00a0";
	public static final String ONE_SPACE = " ";

//	private static boolean MOBILE_RANKING = false;

	private static final String LOC_BASE = "/home/<USER>/expedia/";

	private final static List<String> COMPANY_LIST = Arrays.asList("Expedia","Hotels.com", "Hotwire", "HomeAway", "CarRentals");

	private static SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");

	private long startTime;
	private static int totalCnt;
	private static LogglyVO logglyVO = new LogglyVO();

	private ScriptRunInstanceEntity monitorEntity;

	private CommonDataService commonDataService;
	private EmailSenderComponent emailSenderComponent;
	private ClDailyRankingEntityDao clDailyRankingEntityDao;
	private ZeptoMailSenderComponent zeptoMailSenderComponent;

	public TopXKeywordRankingExportDailyExpedia() {
		commonDataService = SpringBeanFactory.getBean("commonDataService");
		emailSenderComponent = SpringBeanFactory.getBean("emailSenderComponent");
		clDailyRankingEntityDao = SpringBeanFactory.getBean("clDailyRankingEntityDao");
		zeptoMailSenderComponent = SpringBeanFactory.getBean("zeptoMailSenderComponent");
	}

	private int totalLines;

	@Override
	public void process(OwnDomainEntity ownDomainEntity) throws Exception {
	}

	public void reprocess(Date processDate, boolean isMobile, Integer topX) throws Exception {

		ExtractScriptConfigEntity extractScriptConfigEntity = extractScriptConfigDAO.getUniqueExtractScriptConfig(ExtractScriptConfigEntity.CATEGORY_RANK,
				"clarity-backend-scripts", getClass().getName(), ExtractScriptConfigEntity.SPECIAL_CATEGORY_COUNTRY_LEVEL + "", ExtractScriptConfigEntity.FREQUENCY_DAILY);

		List<ExtractScriptInstanceEntity> instanceList = extractScriptInstanceDAO.getInstanceByConfig(extractScriptConfigEntity.getId());

		for(ExtractScriptInstanceEntity extractScriptInstanceEntity : instanceList) {
			System.out.println("===ProcessingExtractScriptIns:" + extractScriptInstanceEntity.getId() + " config:" + extractScriptConfigEntity.getId());

			if (StringUtils.equalsIgnoreCase(extractScriptInstanceEntity.getDevice(), ExtractScriptInstanceEntity.DEVICE_DESKTOP) == isMobile) {

				ExtractScriptDetailEntity detail = extractScriptDetailDAO.getUniqueExtractScriptDetail(extractScriptConfigEntity.getId(),
						extractScriptInstanceEntity.getId(), FormatUtils.formatDateToYyyyMmDd(processDate));

				if (detail == null) {
					ExtractScriptDetailEntity extractScriptDetail = new ExtractScriptDetailEntity();
					extractScriptDetail.setExtractScriptId(extractScriptConfigEntity.getId());
					extractScriptDetail.setExtractInstanceId(extractScriptInstanceEntity.getId());
					extractScriptDetail.setTargetDate(FormatUtils.formatDateToYyyyMmDd(processDate));
					extractScriptDetail.setStatus(ExtractScriptDetailEntity.STATUS_PROCESSING);
					extractScriptDetail.setStartedTime(new Date());
					extractScriptDetail.setServerHostname(InetAddress.getLocalHost().getHostAddress());
					extractScriptDetail.setServerPath(ExtractService.getServerPath());
					extractScriptDetail.setIsExpectedProcessHour(true);

		            int detailId = extractScriptDetailDAO.insert(extractScriptDetail);
		            extractScriptDetail.setId(detailId);
				}

				processByDay(detail, processDate, isMobile, topX);
			}
		}
	}

	public void processDaily() throws Exception {
        List<ExtractScriptInstanceEntity> instanceList = extractService.getNeedRunExtract(
                ExtractScriptConfigEntity.CATEGORY_RANK, getClass().getName(),
                ExtractScriptConfigEntity.SPECIAL_CATEGORY_COUNTRY_LEVEL + "", ExtractScriptConfigEntity.FREQUENCY_DAILY, new Date());

        if (CollectionUtils.isEmpty(instanceList)) {

        	System.out.println("=== instance is not setup correctly !!!");
            return;
        }

        for(ExtractScriptInstanceEntity extract : instanceList) {
        	boolean isMobile = false;
        	if (StringUtils.equalsIgnoreCase(extract.getDevice(), ExtractScriptInstanceEntity.DEVICE_DESKTOP)) {
        		isMobile = false;
			} else if (StringUtils.equalsIgnoreCase(extract.getDevice(), ExtractScriptInstanceEntity.DEVICE_MOBILE)) {
				isMobile = true;
			}

        	if (CollectionUtils.isEmpty(extract.getScriptDetailList())) {
				continue;
			}

        	for(ExtractScriptDetailEntity detail : extract.getScriptDetailList()) {

        		processByDay(detail, FormatUtils.toDate(detail.getTargetDate() + "", "yyyyMMdd"), isMobile, extract.getTopXRank());
        	}

        }

	}

	private static Integer UPPER_LIMIT_NEED_TO_SPLIT = 600000;
	private static Integer pageSize = 1000000;

	private void processByDay(ExtractScriptDetailEntity extractScriptDetail, Date sTime, boolean isMobile, Integer topX) {
		try {

         	long startTime = System.currentTimeMillis();

//     		String companyName = "Expedia";

     		List<Map> countryLanguageEngineList = ownDomainEntityDAO.getDistinctCountryAndLanguageByCompanyNameList(COMPANY_LIST, isMobile);

     		if (countryLanguageEngineList == null || countryLanguageEngineList.isEmpty()) {
     			System.out.println("no Country and Language need to process!!!");
				try {
					extractService.sendMailReport("ERROR:Export for inactive Company:" + COMPANY_LIST, "Please disable export script:" + getClass().getName());
				} catch (Exception exp) {
					exp.printStackTrace();
				}
     			return;
     		}

     		//get monitor info entity
     		ScriptDeployInfoEntity monitorInfoEntity = commonDataService.getScriptDeployInfo("clarity-backend-scripts", this.getClass().getName());

     		// QC start
     		for (Map countryLanguageEngine : countryLanguageEngineList) {

     			String country = countryLanguageEngine.get("search_engine_country") == null ? null : countryLanguageEngine.get("search_engine_country").toString();
     			String language = countryLanguageEngine.get("language") == null ? null : countryLanguageEngine.get("language").toString();
     			String engine = countryLanguageEngine.get("search_engine") == null ? null : countryLanguageEngine.get("search_engine").toString();

     			OwnDomainEntity ownDomainEntity = new OwnDomainEntity();
 				ownDomainEntity.setSearchEngineCountry(country);
 				ownDomainEntity.setSearchEngine(engine);
 				ownDomainEntity.setLanguage(language);

     			List<Integer> domainIdList = ownDomainEntityDAO.getDistinctDomainIdByCompanyNameList(COMPANY_LIST, isMobile, country, language, engine);
     			boolean isPassed = rankQcStateDAO.isAllDomainPassedQcByDomainIdList(
     					domainIdList, RankQcStateEntity.RANK_TYPE_NATIONAL, RankQcStateEntity.FREQ_DAILY,
     					(isMobile ? RankQcStateEntity.DEVICE_MOBILE : RankQcStateEntity.DEVICE_DESKTOP), sTime,
     					ScKeywordRankManager.getSearchEngineId(ownDomainEntity),
 						ScKeywordRankManager.getSearchLanguageId(ownDomainEntity));

     			// set as passed
     			if (false) {
//     			if (!isPassed) {
     				System.out.println(" QC is not passed for engine:" + engine + ", language:" + language + ", country:" + country);

     				String fatalError = " QC not passed!! ";
     	            extractService.updateDetailAndSendEmail(extractScriptDetail, ExtractScriptDetailEntity.STATUS_FAILURE,
     		                    fatalError, startTime, 4765, (isMobile ? "m" : "d"), false, sTime);
 					return;
 				}
 			}
     		// QC end

     		for (Map countryLanguageEngine : countryLanguageEngineList) {
     			totalLines = 0;

     			String country = countryLanguageEngine.get("search_engine_country") == null ? null : countryLanguageEngine.get("search_engine_country").toString();
     			String language = countryLanguageEngine.get("language") == null ? null : countryLanguageEngine.get("language").toString();
     			String engine = countryLanguageEngine.get("search_engine") == null ? null : countryLanguageEngine.get("search_engine").toString();

     			if (keyMapRelationList.contains(country + "-" + StringUtils.capitalize(engine) + "-" + language)) {
     				continue;
     			}

//     			OwnDomainEntity ownDomainEntity = new OwnDomainEntity();
// 				ownDomainEntity.setSearchEngineCountry(country);
// 				ownDomainEntity.setSearchEngine(engine);
// 				ownDomainEntity.setLanguage(language);

     			List<Integer> domainIdList = ownDomainEntityDAO.getDistinctDomainIdByCompanyNameList(COMPANY_LIST, isMobile, country, language, engine);

     			String fileName = formatFileName(country, engine, language, sTime, isMobile);
     			String loc = checkFolderExist(LOC_BASE);
     			String fullPathFileName = loc + File.separator + fileName;
     			System.out.println("======FullpathOutputFile:" + fullPathFileName);

     			StringBuffer header = new StringBuffer();

     			header.append("Date").append(DELIMITED);
     			header.append("Keyword").append(DELIMITED);
     			header.append("Search Engine").append(DELIMITED);
//     			header.append("Avg Search Volume").append(DELIMITED);
     			header.append("True Rank").append(DELIMITED);
     			header.append("Web Rank").append(DELIMITED);
     			header.append("Url Type").append(DELIMITED);
				header.append("Landing Page").append(DELIMITED);
				header.append("Title").append(DELIMITED);
				header.append("Description");

     			// https://www.wrike.com/open.htm?id=*********
     			File outputFile = new File(fullPathFileName);
     			try {
     				String backupSuffixStr = "bk" + DateFormatUtils.format(new Date(), "yyyyMMddHHmmss");
     				if (outputFile.exists()) {
     					String backupFileName = fullPathFileName + backupSuffixStr;
     					outputFile.renameTo(new File(backupFileName));
     					System.out.println(" ===RenameFile:" + fullPathFileName + "-->" + backupFileName);
     				}
     				File gzipFile = new File(fullPathFileName + GZipUtil.GZFile_POSTFIX);
     				if (gzipFile.exists()) {
     					String backupGzipFileName = fullPathFileName + GZipUtil.GZFile_POSTFIX + backupSuffixStr;
     					gzipFile.renameTo(new File(backupGzipFileName));
     					System.out.println(" ===RenameFile:" + fullPathFileName + GZipUtil.GZFile_POSTFIX + "-->" + backupGzipFileName);
     				}
     			} catch (Exception exp) {
     				exp.printStackTrace();
     			}

     			FileUtils.writeStringToFile(outputFile, header.toString() + "\n", true);

//     			if (!StringUtils.equalsIgnoreCase(country, "us")) {
//     				System.out.println("country : " + country + " or language : " + language + " or engine :" + engine + " , will skip!!!");
//     				continue;
//     			}

     			if (StringUtils.isEmpty(country) || StringUtils.isEmpty(language)) {
     				System.out.println(" ===Skip error country:" + country + " language:" + language + " engine :" + engine);
     				continue;
     			}

                 List<Integer> excludeDomainIdList = new ArrayList<>();
                 if (CollectionUtils.isEmpty(domainIdList)) {
     				System.out.println(" !!!domain Id list is empty!!!");
                 	continue;
     			}
     			//System.out.println("DomainIdList : " + domainIdList);

     			monitorEntity = commonDataService.saveScriptRunInfo(monitorInfoEntity.getId(),
     					FormatUtils.formatDateToYyyyMmDd(sTime), ScriptRunInstanceEntity.STATUS_STARTED,
     					0, country, InetAddress.getLocalHost().getHostAddress(), getServerPath(),
     					isMobile ? ScriptRunInstanceEntity.DEVICE_MOBILE : ScriptRunInstanceEntity.DEVICE_DESKTOP,
     					ScriptRunInstanceEntity.TAG_NO, ScriptRunInstanceEntity.FREQUENCY_DAILY);

     			System.out.println("==ProcesingCountry:" + country + " SE:" + engine + "_" + language + " day:" + format.format(sTime) + " OIDs:" + domainIdList);

     			for(Integer ownDomainId : domainIdList) {
     				OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getById(ownDomainId);
					if (ownDomainEntity == null) {
						System.out.println("===Error InactiveOID:" + ownDomainId);
			            try {
			            	sendErrorMail("ERROR:Export for inactive OID:" + ownDomainId, "Please disable export for inactive OID:" + ownDomainId + "(" + getClass().getName() + ")");
			            } catch (Exception exp) {
			            	exp.printStackTrace();
			            }
						return;
					}

					int frequency = 1;//daily
					if (ownDomainEntity.getKeywordRankFrequency() != null && ownDomainEntity.getKeywordRankFrequency() == OwnDomainEntity.KEYWORD_RANK_FREQUENCY_WEEKLY) {
						sTime = FormatUtils.getLastSundayForWeeklyDomainExtract(sTime);
						System.out.println("====weekly domain processDate:" + sTime);
						frequency = 7;//weekly
					}

					String pDate = FormatUtils.formatDate(sTime, "yyyyMMdd HH:mm:ss");
					String stTime = FormatUtils.formatDate(new Date(), "yyyyMMdd HH:mm:ss");
					logglyVO.setoId(String.valueOf(ownDomainId));
					logglyVO.setName("TopXKeywordRankingExportDailyExpedia");
					logglyVO.setDevice(isMobile ? "m" : "d");

					logglyVO.setpDate(pDate);
					List<String> groupList = new ArrayList<>();
					groupList.add(LogglyVO.GROUP_RANKING_EXTRACT);
					logglyVO.setGroups(groupList);

     				Integer totalKeywordCnt = clDailyRankingEntityDao.getKeywordCount(
 							ownDomainId,
 							ScKeywordRankManager.getSearchEngineId(ownDomainEntity),
 							ScKeywordRankManager.getSearchLanguageId(ownDomainEntity),
 							FormatUtils.formatDate(sTime, "yyyy-MM-dd"), isMobile);

					 if(totalKeywordCnt == null || totalKeywordCnt == 0){
						 System.out.println("====noRankingOid:" + ownDomainId + ",isMobile:" + isMobile);
						 continue;
					 }

 					try {
						if (totalKeywordCnt >= UPPER_LIMIT_NEED_TO_SPLIT) {
							for (int prefix = 1; prefix <= 9; prefix++) {
								processForDomain(ownDomainEntity, ownDomainId,
										sTime, isMobile, topX, excludeDomainIdList,
										engine, fullPathFileName, prefix + "");
							}
						} else {
							processForDomain(ownDomainEntity, ownDomainId,
									sTime, isMobile, topX, excludeDomainIdList,
									engine, fullPathFileName, "");
						}

						logglyVO.setStatus(LogglyVO.STATUS_OK);
						logglyVO.setsTime(stTime);
						logglyVO.seteTime(FormatUtils.formatDate(new Date(), "yyyyMMdd HH:mm:ss"));
						logglyVO.setRows(String.valueOf(totalCnt));
						String body = new Gson().toJson(logglyVO);
						LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);

					}catch (Exception e){
 						e.printStackTrace();
						logglyVO.setStatus(LogglyVO.STATUS_NG);
						String body = new Gson().toJson(logglyVO);
						LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);
					}


 					excludeDomainIdList.add(ownDomainId);
     			}

     			outputCsvFile(fullPathFileName);

     			long endTime = System.currentTimeMillis();
     			int elapsedSeconds = (int) (endTime - startTime);


     			if (StringUtils.isNotBlank(monitorEntity.getFatalError())) {
     				monitorEntity.setStatus(ScriptRunInstanceEntity.STATUS_FAILURE);
     			} else {
     				monitorEntity.setStatus(ScriptRunInstanceEntity.STATUS_SUCCESS);
     			}

     			monitorEntity.setElapsedSeconds(elapsedSeconds);

     			commonDataService.updateScriptRunInfo(monitorEntity);
     		}

 		} catch (Exception e) {
 			e.printStackTrace();
 			String fatalError = e.getMessage();
             extractService.updateDetailAndSendEmail(extractScriptDetail, ExtractScriptDetailEntity.STATUS_FAILURE,
 	                    fatalError, startTime, 4765, (isMobile ? "m" : "d"), false, sTime);
 		}

		extractScriptDetail.setStatus(ExtractScriptDetailEntity.STATUS_SUCCESS);
        long elapsedSeconds = (System.currentTimeMillis() - startTime) / 1000;
        extractScriptDetail.setEndedTime(new Date());
        extractScriptDetail.setElapsedSeconds((int) elapsedSeconds);
        System.out.println("===update for success id:" + extractScriptDetail.getId());
        extractService.updateForSuccess(extractScriptDetail);
	}

	private static Integer QUERY_TRY_COUNT = 5;

	public void processForDomain(OwnDomainEntity ownDomainEntity, Integer ownDomainId,
			Date sTime, boolean isMobile, Integer topX, List<Integer> excludeDomainIdList,
			String engine, String fullPathFileName, String prefix) throws Exception {
		boolean finished = false;
		int pageNum = 0;
		List<CLRankingDetailEntity> resultList = new ArrayList<>();
		while (!finished) {

			int retryCount = 0;
	        while (true) {
	            try {
	                resultList = clDailyRankingEntityDao.exportByDomain(ownDomainId,
	    					ScKeywordRankManager.getSearchEngineId(ownDomainEntity),
	    					ScKeywordRankManager.getSearchLanguageId(ownDomainEntity),
	    					FormatUtils.formatDate(sTime, "yyyy-MM-dd"), isMobile,
	    					excludeDomainIdList, pageNum, pageSize, topX, prefix);
	                break;
	            } catch (Exception e) {

	                if (retryCount >= QUERY_TRY_COUNT) {
						logglyVO.setStatus(LogglyVO.STATUS_NG);
						String body = new Gson().toJson(logglyVO);
						LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);
						sendErrorMail("ERROR:Export ExpediaTop40", "Over max retry times:" + "(" + getClass().getName() + ")");
	                    break;
	                }
	                e.printStackTrace();
	                try {
	                    Thread.sleep(1000 * 60 * retryCount);
	                } catch (Exception ex) {
	                    ex.printStackTrace();
	                }
	                retryCount++;
	            }
	        }


			if (resultList != null) {
				totalLines += resultList.size();
			}
			totalCnt = resultList.size();
			pageNum ++ ;
			System.out.println("Page num : " + pageNum + ", pageSize : " + pageSize);

    		for(CLRankingDetailEntity rankVO : resultList) {

    			String line = convertToCsvLine(rankVO, engine, sTime, topX);
    			if (StringUtils.isBlank(line)) {
					continue;
				}

    			try {
    				FileWriter writer = new FileWriter(new File(fullPathFileName), true);
    				try {
    					writer.write(line + "\n");

    				} catch (IOException e) {
    					e.printStackTrace();
    				}

    				writer.close();
    			} catch (Exception e) {
    				e.printStackTrace();
    			}

        	}
    		if (resultList.size() < pageSize) {
        		finished = true;
    			break;
    		}
		}

	}

	public String getRankType(String url, int urlType) {

		if (StringUtils.isBlank(url)) {
			return "Web";
		}

        // maps.google.it/
        if (urlType == TYPE_ADDRESS || StringUtils.containsIgnoreCase(url, "maps.google.com")
                || StringUtils.containsIgnoreCase(url, "maps.google.")
                || StringUtils.containsIgnoreCase(url, "www.google.com/maps/")) {
            return "Local";
        } else if (urlType == TYPE_IMGAGE || StringUtils.containsIgnoreCase(url, "images.google.com")
                || StringUtils.containsIgnoreCase(url, "images.google.")) {
            return "Image";
        } else if (urlType == TYPE_NEWS || StringUtils.containsIgnoreCase(url, "news.google.com")
                || StringUtils.containsIgnoreCase(url, "news.google.")) {
            return "News";
        } else if (urlType == TYPE_SHOPPING
                || StringUtils.containsIgnoreCase(url, "www.google.com/shopping")
                || (StringUtils.containsIgnoreCase(url, "www.google.") && StringUtils.containsIgnoreCase(
                		url, "/shopping"))) {
            return "Shopping";
        } else if (urlType == TYPE_VIDEO) {
            return "Video";
        } else if (urlType == TYPE_LOCALLISTING
				|| (StringUtils.containsIgnoreCase(url, "www.google.") && StringUtils.containsIgnoreCase(
						url, "tbm=lcl"))) {
			return "LocalListing";
		} else if (StringUtils.containsIgnoreCase(url, "www.google.com")
                || StringUtils.containsIgnoreCase(url, "www.google.")
                || urlType == TYPE_TWITTER
//                || urlType == TYPE_ANSWERBOX
                || urlType == TYPE_JOB_URL
                ) {
            return "Universal";
        }

        return "Web";

	}

//	private int getRankUrl(Date specialDate, List<String> allKeywords,
//			String searchCountry, String searchLanguage, String searchEngine, String fullPathFileName, String domainIdListStr) throws Exception {
//		System.out.println(specialDate + " " + allKeywords.size());
//		System.out.println("domainIdListStr:" + domainIdListStr);
//
////		lines.add(header.toString());
//
//		//Use to get engine, language id
//		OwnDomainEntity ownDomainEntity = new OwnDomainEntity();
//		ownDomainEntity.setSearchEngineCountry(searchCountry);
//		ownDomainEntity.setSearchEngine(searchEngine);
//		ownDomainEntity.setLanguage(searchLanguage);
//
//		int count = 0;
//		for (String keyword : allKeywords) {
//
//			List<String> lines = new ArrayList<String>();
//
//			if(StringUtils.isEmpty(keyword)) {
//				System.out.println("Empty keyword String found!!!");
//				continue;
//			}
//			
//			List<CLRankingDetailEntity> resultList = clColdDailyRankingEntityDao.getKeywordDetailByDomainIdList(
//					domainIdListStr, ScKeywordRankManager.getSearchEngineId(ownDomainEntity), 
//					ScKeywordRankManager.getSearchLanguageId(ownDomainEntity), 0, 
//					FormatUtils.formatDate(specialDate, "yyyy-MM-dd"), MOBILE_RANKING, 
//					ClarityDBUtils.formatQueryStringForClarityDBQuery(FormatUtils.decodeKeyword(keyword)));
//
//
//
//			List<String> extracts = new ArrayList<String>();
//			if (CollectionUtils.isNotEmpty(resultList) && resultList.size()>0) {
//
//				int countRank = 0;
//				for (CLRankingDetailEntity rankVO : resultList) {
//
////					if (!StringUtils.equalsIgnoreCase(RankTypeManager.getRankType(rankVO.getLandingPage(), NumberUtils.toInt(rankVO.getType())), "web")) {
//					if (!StringUtils.equalsIgnoreCase(getRankType(rankVO.getUrl(), rankVO.getType()), "web")) {
//		                countRank++;
//		                rankVO.setWebRank(-1);
//		                continue;
//		            }
//
//					int tRank = rankVO.getRank();
//	                rankVO.setWebRank(tRank - countRank);
//				}
//				
//				for (CLRankingDetailEntity rankVO : resultList) {
//					if (rankVO != null) {
//						extracts.addAll(convertToCsvLine(rankVO, searchEngine, specialDate));
//					}
//				}
//
//				if (extracts.size() == 0) {
//					lines.add(blankLine(specialDate, ClarityDBUtils.formatQueryStringForClarityDBQuery(FormatUtils.decodeKeyword(keyword)), searchEngine));
//				} else {
//					lines.addAll(extracts);
//				}
//			} else {
//				lines.add(blankLine(specialDate, ClarityDBUtils.formatQueryStringForClarityDBQuery(FormatUtils.decodeKeyword(keyword)), searchEngine));
//				System.out.println("No mongo data keyword:" + keyword);
//			}
//
//			FileUtils.writeLines(new File(fullPathFileName), lines, true);
//			totalLines += lines.size();
//			count++;
//		}
//
//
//
//		return count;
//
//	}


	private String blankLine(Date specialDate, String keywordName, String searchEngine) {
		StringBuffer line = new StringBuffer();
		line.append(FormatUtils.formatDate(specialDate, "MM/dd/yyyy")).append(DELIMITED);
		line.append(keywordName).append(DELIMITED);
		line.append(searchEngine);
//		try {
//			line.append(StringEscapeUtils.unescapeXml(URLDecoder.decode(keywordNameRelService.getKeywordName(keywordEntity), "UTF-8"))).append(DELIMITED);
//		} catch (UnsupportedEncodingException e) {
//			// TODO Auto-generated catch block
//			e.printStackTrace();
//		}

//		line.append( ( keywordEntity.getAvgMonthlySearchVolume() == null || keywordEntity.getAvgMonthlySearchVolume() < 0) ? "0" : keywordEntity.getAvgMonthlySearchVolume()).append(
//				DELIMITED);
		line.append(NULL_VALUE).append(DELIMITED);
		line.append(NULL_VALUE).append(DELIMITED);
		line.append(NULL_VALUE);
		return line.toString();
	}

	private static List<String> keyMapRelationList = new ArrayList<>();
	static {
		keyMapRelationList.add("CN-Google.com.hk-zh-tw");
		keyMapRelationList.add("CN-Google.com.hk-zh");
		keyMapRelationList.add("MY-Google.com.my-ms");
		keyMapRelationList.add("NL-Google.nl-en");
		keyMapRelationList.add("CA-Google.ca-fr");
	}

	private String formatFileName(String searchCountry, String searchEngine, String language, Date specialDate, boolean isMobile) {
		String Date = FormatUtils.formatDate(specialDate, "yyyyMMdd");
		String extension = "txt";

		if (keyMapRelationList.contains(searchCountry + "-" + StringUtils.capitalize(searchEngine) + "-" + language)) {
			return searchCountry + "-" + StringUtils.capitalize(searchEngine) + "." + StringUtils.replace(language, "-", ".") + "-" + (isMobile ? "Mobile" : "Desktop") + "-" + Date + "." + extension;
		} else {
			return searchCountry + "-" + StringUtils.capitalize(searchEngine) + "-" + (isMobile ? "Mobile" : "Desktop") + "-" + Date + "." + extension;
		}
	}

	private String convertToCsvLine(CLRankingDetailEntity rankVO, String searchEngine, Date specialDate, Integer topX) throws Exception {

		StringBuffer line = new StringBuffer();
		try {
			if (rankVO.getTrueRank() > topX) {
				return null;
			}
			String url = rankVO.getUrl();
			String fullDomain = FormatUtils.getDomainByUrl(rankVO.getUrl(), false);
			String rootDomain = getRootDomain(fullDomain);
			if (StringUtils.equalsIgnoreCase(rootDomain, "google.com") && StringUtils.indexOf(url, "?") != -1) {

//				System.out.println(" substring url from : " + url + " to " + StringUtils.substring(url, 0, StringUtils.indexOf(url, "?")));
				url = StringUtils.substring(url, 0, StringUtils.indexOf(url, "?"));
			}

			String title = rankVO.getLabel();
			String meta = rankVO.getMeta();
			title = org.apache.commons.lang3.StringUtils.trim(title.replaceAll(NO_BREAK_SPACE, ONE_SPACE));
			meta = org.apache.commons.lang3.StringUtils.trim(meta.replaceAll(NO_BREAK_SPACE, ONE_SPACE));

			line.append(FormatUtils.formatDate(specialDate, "MM/dd/yyyy")).append(DELIMITED);
			line.append(rankVO.getKeywordName()).append(DELIMITED);
			line.append(searchEngine).append(DELIMITED);
			line.append(rankVO.getTrueRank() == null ? NULL_VALUE : rankVO.getTrueRank()).append(DELIMITED);
			line.append(rankVO.getWebRank() == 0 ? NULL_VALUE : rankVO.getWebRank()).append(DELIMITED);
			line.append(RankTypeManager.getRankType(url, rankVO.getType())).append(DELIMITED);
			line.append(url).append(DELIMITED);
			line.append(title).append(DELIMITED);
			line.append(meta);

		} catch (Exception e) {
			e.printStackTrace();
		}
		return line.toString();
	}

	public static String getRootDomain(String fullDomain) {
		try {
			return InternetDomainName.from(fullDomain).topPrivateDomain().toString();
		} catch (Exception e) {
		}
		return fullDomain;
	}

	private void outputCsvFile(String fullPathFileName)
			throws Exception {

		GZipUtil.zip(fullPathFileName, fullPathFileName + GZipUtil.GZFile_POSTFIX);
		System.out.println("TopXKeywordRankingExportDailyExpedia will copy " + fullPathFileName + " to FTP server.");

		try {

			copyBySSH(FTP_SERVER, FTP_SERVER_USER, FTP_SERVER_PW,
					fullPathFileName + GZipUtil.GZFile_POSTFIX, FTP_SERVER_PATH, 0);

			System.out.println("TopXKeywordRankingExportDailyExpedia " + fullPathFileName + " was copied to FTP server successfully.");
		} catch(Exception e) {
			monitorEntity.setFatalError(fullPathFileName + " copy to FTP server Failed");
			e.printStackTrace();
		}

		File file = new File(fullPathFileName);
		monitorEntity.setDestinationServerIp(FTP_SERVER);
		monitorEntity.setOutputFile(fullPathFileName);
		monitorEntity.setOutputFileSizeKB(FormatUtils.getFileSizeKB(file.length()));
		monitorEntity.setOutputDataCount(totalLines);

	}

	private String getServerPath() throws IOException {
		 File directory = new File("");
	     String courseFile = directory.getCanonicalPath();
	     return courseFile;

	}

	private void copyBySSH(String host, String userName, String pw, String from, String saveTo, int type)
			throws Exception {
		if (type == 0) {
			System.out.println("copy from local to remote , file name:" + from);
		} else {
			System.out.println("copy from remote to local , file name:" + from);
		}

		boolean copySucceed = false;
		for (int i = 0; i < SSH_TRY_COUNT; i++) {

			try {
				Connection connection = new Connection(host);
				connection.connect();
				if (connection.authenticateWithPassword(userName, pw)) {
					System.out.println("login successfully.");
					SCPClient scpClient = connection.createSCPClient();
					if (type == 0) {
						scpClient.put(from, saveTo);
					} else {
						scpClient.get(from, saveTo);
					}
					connection.close();
					copySucceed = true;
					break;
				}
			} catch(Exception e) {
				e.printStackTrace();
			}

			System.out.println("Failed to login to target host...");

			try {
				Thread.sleep(300000);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}

		if (copySucceed == true) {
			System.out.println("Copy " + from + " to " + host + " successfully.");
		} else {
			monitorEntity.setExtraError("Couldnot login " + host + " for " + SSH_TRY_COUNT
					+ " tries. Please copy file yourself.");
			throw new RuntimeException("Couldnot login " + host + " for " + SSH_TRY_COUNT
					+ " tries. Please copy file yourself.");
		}
	}

	public String checkFolderExist(String path) {
		File backUpDir = new File(path);
		if (!backUpDir.exists() || !backUpDir.isDirectory()) {
			backUpDir.mkdir();
		}

		return backUpDir.getPath();
	}

	public static void main(String[] args) throws Exception{
		TopXKeywordRankingExportDailyExpedia topXKeywordRankingExport = new TopXKeywordRankingExportDailyExpedia();

		if (args == null || args.length <2) {
			topXKeywordRankingExport.processDaily();
		} else {
			Integer topX = NumberUtils.toInt(args[0]);
			boolean processMobile = BooleanUtils.toBoolean(args[1]);

			//TODO for reprocess, please change sTime
			Date sDate = null;
			Date eDate = null;
			if(args.length >= 4) {
				sDate = format.parse(args[2]);
				eDate = format.parse(args[3]);
			} else {
				Date sTime = FormatUtils.getYesterday(true);
				sDate = sTime;
				eDate = sTime;
			}

			System.out.println("==========Parameters TOPX:" + topX + " mobile ranking:" + processMobile
					+ " sDate:" + FormatUtils.formatDateToYyyyMmDd(sDate) + " eDate:" + FormatUtils.formatDateToYyyyMmDd(eDate));

			while (sDate.compareTo(eDate) <= 0) {

				System.out.println("-----------------------------------For date " + FormatUtils.formatDate(sDate));

				try {
					topXKeywordRankingExport.reprocess(sDate, processMobile, topX);
				} catch (Exception e) {
					e.printStackTrace();
				}

				sDate = DateUtils.addDays(sDate, 1);
			}
		}
	}

    private void sendErrorMail(String subject, String errMessage) {
        List<String> infos = new ArrayList<String>();
        infos.add(errMessage);
        sendEmailForError(new Date(), subject, infos);
    }

    private void sendEmailForError(Date startTime, String subject, List<String> infos) {
        try {
            Map<String, Object> reportMap = new HashMap<String, Object>();
            reportMap.put("userName", "Mitul");
            reportMap.put("dateString", DateFormatUtils.format(new Date(), "MM/dd/yyyy"));
            reportMap.put("startTime", DateFormatUtils.format(startTime, "MM/dd/yyyy HH:mm:ss"));
            reportMap.put("endTime", DateFormatUtils.format(new Date(), "MM/dd/yyyy HH:mm:ss"));
            reportMap.put("info", infos);
            reportMap.put("title", subject);
            reportMap.put("errormessage", "");

            String emailTo = "<EMAIL>";
            String[] ccTo = new String[] { "<EMAIL>" };
            zeptoMailSenderComponent.sendMimeMultiPartZeptoMailAndBccByFunctionType(emailTo, ccTo, subject, "mail_common.txt", "mail_common.html",
                    reportMap, null, ZeptoMailSenderComponent.FUNCTION_TYPE_BACKEND_INTERNAL, null, null);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}