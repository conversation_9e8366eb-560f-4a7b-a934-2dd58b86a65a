package seoclarity.backend.export;

import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import seoclarity.backend.dao.actonia.*;
import seoclarity.backend.dao.clickhouse.gsc.GscBaseDao;
import seoclarity.backend.entity.actonia.*;
import seoclarity.backend.entity.clickhouse.gsc.GscEntity;
import seoclarity.backend.service.ExtractService;
import seoclarity.backend.service.ScKeywordRankManager;
import seoclarity.backend.service.ServerAuthenticationInfoService;
import seoclarity.backend.utils.*;

import java.io.File;
import java.io.IOException;
import java.util.*;

import static seoclarity.backend.utils.GscCountryMap.GSC_COUNTRY_MAP;

@CommonsLog
public class GscStandardExtract {

    private static final String SPLIT = "\t";
    private static final String TAG_SPLIT = "!_!";
    private static final String KEY_SPLIT = "#_#";
    private static String LOC = "/home/<USER>/";
    private static final int QUERY_TRY_COUNT = 10;

    private static Date processDate;
    private static String[] domainIdList;
    private static int domainId;

    //send to special remote server
    public static final Map<Integer, Integer> SPECIAL_DOMAIN_SERVER_MAP = new HashMap();
    static {
        SPECIAL_DOMAIN_SERVER_MAP.put(765, ServerAuthenticationInfoEntity.SERVER_TYPE_AMAZON_S3);
        SPECIAL_DOMAIN_SERVER_MAP.put(9422, ServerAuthenticationInfoEntity.SERVER_TYPE_AMAZON_S3);
    }

    private ScKeywordRankManager scKeywordRankManager;
    private OwnDomainEntityDAO ownDomainEntityDAO;
    private ServerAuthenticationInfoService serverAuthenticationInfoService;
    private EmailSenderComponent emailSenderComponent;
    private GscBaseDao gscBaseDao;
    private GwmDomainRelDAO gwmDomainRelDAO;
    private ExtractService extractService;

    public GscStandardExtract() {
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        scKeywordRankManager = SpringBeanFactory.getBean("scKeywordRankManager");
        serverAuthenticationInfoService = SpringBeanFactory.getBean("serverAuthenticationInfoService");
        emailSenderComponent = SpringBeanFactory.getBean("emailSenderComponent");
        gscBaseDao = SpringBeanFactory.getBean("gscBaseDao");
        gwmDomainRelDAO = SpringBeanFactory.getBean("gwmDomainRelDAO");
        extractService = SpringBeanFactory.getBean("extractService");
    }

    private void processExtract(int ownDomainId) {

        OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getById(domainId);
        if (ownDomainEntity == null) {
            log.error(" domain not exist : " + domainId);
            try {
                extractService.sendMailReport("ERROR:Export for inactive OID:" + ownDomainId, "Please disable export for inactive OID:" + ownDomainId + "(" + getClass().getName() + ")");
            } catch (Exception exp) {
                exp.printStackTrace();
            }
            return;
        }

        int processingDate = FormatUtils.formatDateToYyyyMmDd(processDate);

        String fileName = getFileName(ownDomainEntity, processingDate);

        File localFolder = new File(LOC + ownDomainId);
        if (!localFolder.exists() || !localFolder.isDirectory()) {
            localFolder.mkdirs();
        }

        String localFilePath = LOC + ownDomainId + File.separator;
        String remoteFilePath = localFilePath;
//        if (ownDomainId == 6997) {
//            remoteFilePath = LOC + ownDomainId + File.separator + "StandardRankExtract" + File.separator;
//        }

        String localFileName = localFilePath + fileName;
        File localFile = new File(localFileName);
        if (localFile.exists()) {
            localFile.delete();
        }

        try {
            processFile(localFile, ownDomainEntity, localFileName, remoteFilePath);

        } catch (Exception e) {
            e.printStackTrace();
            String fatalError = "002 error";
            log.error("====error :" + fatalError);
        }

    }

    private String getFileName(OwnDomainEntity ownDomainEntity, int processingDate) {

        String fileName = "";
        fileName = ownDomainEntity.getDomain() + "_GSCExtract_" + processingDate + ".csv";
        return fileName;
    }

    private void processFile(File localFile, OwnDomainEntity ownDomainEntity, String localFilePath, String remoteFilePath) throws Exception {

        int ownDomainId = ownDomainEntity.getId();
        addHeadersForExactFile(localFile, ownDomainEntity);

        List<String> dataList = new ArrayList<>();
        int retryCount = 1;
        while (true) {
            try {
                dataList = getDataFromDB(ownDomainEntity);
                break;
            } catch (Exception e) {

                if (retryCount >= QUERY_TRY_COUNT) {
                    System.out.println("====error extract oid : " + ownDomainId);
                    System.out.println("====RETRY OVER " + QUERY_TRY_COUNT + "TIMES error :" + ownDomainId);

                    String fatalError = "002 error";
                    System.out.println("==== : " + fatalError);
                    return;
                }

                e.printStackTrace();
                System.out.println("====extract error oid:" + ownDomainId + ", sleep " + (1000 * 60 * retryCount));
                try {
                    Thread.sleep(1000 * 60 * retryCount);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
                retryCount++;
            }
        }

        FileUtils.writeLines(localFile, dataList, true);

        int serverType = ServerAuthenticationInfoEntity.SERVER_TYPE_FTP;
        if (SPECIAL_DOMAIN_SERVER_MAP.get(ownDomainId) != null) {
            serverType = SPECIAL_DOMAIN_SERVER_MAP.get(ownDomainId);
        }

        String zipFile = null;
        try {

            if(serverType == ServerAuthenticationInfoEntity.SERVER_TYPE_AMAZON_S3){
                if(ownDomainId == 765 || ownDomainId == 9422){//https://www.wrike.com/open.htm?id=1111890232
                    String s3KeyPath = "seo/gsc/" + localFile.getName();
                    boolean isSendS3Success = serverAuthenticationInfoService.putFileForS3WithSSE(ServerAuthenticationInfoEntity.SERVER_TYPE_AMAZON_S3, ownDomainId, localFilePath, s3KeyPath);
                    if(!isSendS3Success){
                        log.error("====send to s3 failed!!");
                    }
                }else {
                    serverAuthenticationInfoService.copyFileToRemoteServerNew(serverType, ownDomainId, localFilePath, null, null);
                }
            }else {
                serverAuthenticationInfoService.copyFileToRemoteServerNew(serverType, ownDomainId, localFilePath, remoteFilePath, null);
            }

        } catch (Exception e) {
            e.printStackTrace();
            String fatalError = "send to remote server error!!";
            log.error("===fatalError:" + fatalError);
        }

    }

    private void addHeadersForExactFile(File outFile, OwnDomainEntity ownDomainEntity) throws IOException {

        int domainId = ownDomainEntity.getId();

        List<String> lines = new ArrayList<String>();
        StringBuffer header = new StringBuffer();
        if(domainId == 765 || domainId == 9422){
            header.append("Date").append(SPLIT);
            header.append("Keyword").append(SPLIT);
            header.append("URL").append(SPLIT);
            header.append("Country").append(SPLIT);
            header.append("Device").append(SPLIT);
            header.append("Impressions").append(SPLIT);
            header.append("Clicks").append(SPLIT);
            header.append("CTR").append(SPLIT);
            header.append("Avg Position");
        }else {//default
            header.append("Date").append(SPLIT);
            header.append("Keyword").append(SPLIT);
            header.append("URL").append(SPLIT);
            header.append("Country").append(SPLIT);
            header.append("Device").append(SPLIT);
            header.append("Profile").append(SPLIT);
            header.append("Impressions").append(SPLIT);
            header.append("Clicks").append(SPLIT);
            header.append("CTR").append(SPLIT);
            header.append("Avg Position");
        }

        lines.add(header.toString());
        FileUtils.writeLines(outFile, lines, true);
    }

    private List<String> getDataFromDB(OwnDomainEntity ownDomainEntity) throws Exception {

        int ownDomainId = ownDomainEntity.getId();
        String domainName = ownDomainEntity.getDomain();
        String logDate = FormatUtils.formatDate(processDate, FormatUtils.DATE_PATTERN_2);

        Integer profileId = null;

        List<GscEntity> gscEntityList;
        if(ownDomainId == 765 || ownDomainId == 9422){
            gscEntityList = gscBaseDao.geStandardExtractDataGroupByKeyHash(GscEntity.TYPE_ALL, domainId, logDate, profileId);
        }else {
            gscEntityList = gscBaseDao.geStandardExtractData(GscEntity.TYPE_ALL, domainId, logDate, profileId);
        }

        log.info("=== gscEntityList size: " + gscEntityList.size());

        List<String> extractLines = new ArrayList<String>();
        if (CollectionUtils.isNotEmpty(gscEntityList)) {
            for(GscEntity gscEntity : gscEntityList){
                if(ownDomainId == 765 || ownDomainId == 9422){
                    extractLines.add(appendData(ownDomainId, gscEntity, logDate, null));
                }else {
                    String profileName = gwmDomainRelDAO.getProfileById(gscEntity.getRelId());
                    extractLines.add(appendData(ownDomainId, gscEntity, logDate, profileName));
                }

            }
        }
        return extractLines;
    }

    public static String appendData(int ownDomainId, GscEntity gscEntity, String dateRange, String profileName) {
        StringBuffer line = new StringBuffer();
        if(ownDomainId == 765 || ownDomainId == 9422){
            line.append(dateRange).append(SPLIT);
            line.append(gscEntity.getKeywordName()).append(SPLIT);
            String url = gscEntity.getUrl();
            if(url.contains("\r")){
                url = url.replaceAll("\r", "");
            }
            if(url.contains("\n")){
                url = url.replaceAll("\n", "");
            }
            if(url.contains(SPLIT)){
                url = url.replaceAll(SPLIT, "    ");
            }
            line.append(url).append(SPLIT);
            line.append(GSC_COUNTRY_MAP.get(gscEntity.getCountryCd())).append(SPLIT);
            line.append(gscEntity.getDevice()).append(SPLIT);
            line.append(gscEntity.getImpressions()).append(SPLIT);
            line.append(gscEntity.getClicks()).append(SPLIT);
            line.append(gscEntity.getCtr()).append(SPLIT);
            line.append(gscEntity.getPosition());
        }else {
            line.append(dateRange).append(SPLIT);
            line.append(gscEntity.getKeywordName()).append(SPLIT);
            String url = gscEntity.getUrl();
            if(url.contains("\r")){
                url = url.replaceAll("\r", "");
            }
            if(url.contains("\n")){
                url = url.replaceAll("\n", "");
            }
            if(url.contains(SPLIT)){
                url = url.replaceAll(SPLIT, "    ");
            }
            line.append(url).append(SPLIT);
            line.append(GSC_COUNTRY_MAP.get(gscEntity.getCountryCd())).append(SPLIT);
            line.append(gscEntity.getDevice()).append(SPLIT);
            line.append(profileName).append(SPLIT);
            line.append(gscEntity.getImpressions()).append(SPLIT);
            line.append(gscEntity.getClicks()).append(SPLIT);
            line.append(gscEntity.getCtr()).append(SPLIT);
            line.append(gscEntity.getPosition());
        }

        return line.toString();
    }


    public static void main(String[] args){
        GscStandardExtract standardExtract = new GscStandardExtract();

        if (args != null && args.length > 0) {

            if (args[0].contains(",")) {
                domainIdList = args[0].split(",");
            } else {
                domainId = Integer.parseInt(args[0]);
            }

        }

        if (domainIdList != null && domainIdList.length > 0) {

            for (String processingDomainId : domainIdList) {
                domainId = Integer.parseInt(processingDomainId);
                if (args.length >= 2 && StringUtils.containsIgnoreCase(args[1], ",")) {
                    Date sDate = FormatUtils.toDate(args[1].split(",")[0], FormatUtils.DATE_PATTERN_2);
                    Date eDate = FormatUtils.toDate(args[1].split(",")[1], FormatUtils.DATE_PATTERN_2);
                    while (sDate.compareTo(eDate) <= 0) {
                        processDate = sDate;
                        standardExtract.processExtract(Integer.parseInt(processingDomainId));
                        sDate = DateUtils.addDays(sDate, 1);
                    }

                } else if(args.length >= 2 && !StringUtils.containsIgnoreCase(args[1], ",")){
                    processDate = FormatUtils.toDate(args[1], FormatUtils.DATE_PATTERN_2);
                    standardExtract.processExtract(Integer.parseInt(processingDomainId));
                } else {
                    processDate = DateUtils.addDays(new Date(), -4);
                    standardExtract.processExtract(Integer.parseInt(processingDomainId));
                }
            }

        } else {

            if (args.length >= 2 && StringUtils.containsIgnoreCase(args[1], ",")) {
                Date sDate = FormatUtils.toDate(args[1].split(",")[0], FormatUtils.DATE_PATTERN_2);
                Date eDate = FormatUtils.toDate(args[1].split(",")[1], FormatUtils.DATE_PATTERN_2);
                while (sDate.compareTo(eDate) <= 0) {
                    processDate = sDate;
                    standardExtract.processExtract(domainId);
                    sDate = DateUtils.addDays(sDate, 1);
                }

            } else if(args.length >= 2 && !StringUtils.containsIgnoreCase(args[3], ",")){
                processDate = FormatUtils.toDate(args[1], FormatUtils.DATE_PATTERN_2);
                standardExtract.processExtract(domainId);
            } else {
                processDate = DateUtils.addDays(new Date(), -4);
                standardExtract.processExtract(domainId);
            }

        }
    }

}
