package seoclarity.backend.export;

import com.google.gson.Gson;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import seoclarity.backend.dao.actonia.GroupTagEntityDAO;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.actonia.OwnDomainSettingEntityDAO;
import seoclarity.backend.dao.clickhouse.ClDailyRankingEntityDao;
import seoclarity.backend.entity.ExtractQueryVO;
import seoclarity.backend.entity.actonia.GroupTagEntity;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.actonia.ServerAuthenticationInfoEntity;
import seoclarity.backend.entity.clickhouse.CLRankingDetailEntity;
import seoclarity.backend.service.ExtractService;
import seoclarity.backend.service.ScKeywordRankManager;
import seoclarity.backend.service.ServerAuthenticationInfoService;
import seoclarity.backend.utils.CommonUtils;
import seoclarity.backend.utils.EmailSenderComponent;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.utils.loggly.LogglyUtils;
import seoclarity.backend.utils.loggly.LogglyVO;

import java.io.File;
import java.io.IOException;
import java.util.*;

/**
 * https://www.wrike.com/open.htm?id=975794016
 */
@CommonsLog
public class ExtractPLPRank {

    private static final String SPLIT = "\t";
    private static final String TAG_SPLIT = "!_!";
    private static String LOC = "/home/<USER>/";
    private static final int QUERY_TRY_COUNT = 10;
    private static Date processDate = new Date();
    private static String device;
    private static String[] domainIdList;
    private static int domainId;
    private  static LogglyVO logglyVO = new LogglyVO();
    private static int totalCnt = 0;

    private ScKeywordRankManager scKeywordRankManager;
    private OwnDomainEntityDAO ownDomainEntityDAO;
    private ServerAuthenticationInfoService serverAuthenticationInfoService;
    private EmailSenderComponent emailSenderComponent;
    private ClDailyRankingEntityDao clDailyRankingEntityDao;
    private GroupTagEntityDAO groupTagEntityDAO;
    private ExtractService extractService;

    public ExtractPLPRank() {
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        scKeywordRankManager = SpringBeanFactory.getBean("scKeywordRankManager");
        serverAuthenticationInfoService = SpringBeanFactory.getBean("serverAuthenticationInfoService");
        emailSenderComponent = SpringBeanFactory.getBean("emailSenderComponent");
        clDailyRankingEntityDao = SpringBeanFactory.getBean("clDailyRankingEntityDao");
        groupTagEntityDAO = SpringBeanFactory.getBean("groupTagEntityDAO");
        extractService = SpringBeanFactory.getBean("extractService");
    }


    private void starProcess(String pDate, int domainId) {

        try {

            OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getById(domainId);
            if (ownDomainEntity == null) {
                log.error(" domain not exist : " + domainId);
                try {
                    extractService.sendMailReport("ERROR:Export for inactive OID:" + domainId, "Please disable export for inactive OID:" + domainId + "(" + getClass().getName() + ")");
                } catch (Exception exp) {
                    exp.printStackTrace();
                }
                return;
            }

            int frequency = 1;//daily
            if (ownDomainEntity.getKeywordRankFrequency() != null && ownDomainEntity.getKeywordRankFrequency() == OwnDomainEntity.KEYWORD_RANK_FREQUENCY_WEEKLY) {
                pDate = FormatUtils.getLastSundayForWeeklyOfString(pDate);
                System.out.println("====weekly domain processDate:" + pDate);
                frequency = 7;//weekly
            }
            String stTime = FormatUtils.formatDate(new Date(), "yyyyMMdd HH:mm:ss");

            logglyVO.setoId(String.valueOf(domainId));
            logglyVO.setName("ExtractPLPRank");
            logglyVO.setDevice(device.startsWith("m")?"m":"d");
            logglyVO.setpDate(pDate);
            List<String> groupList = new ArrayList<>();
            groupList.add(LogglyVO.GROUP_RANKING_EXTRACT);
            logglyVO.setGroups(groupList);

            String fileName = getFileName(ownDomainEntity, pDate);
            File localFolder = new File(LOC + domainId);
            if (!localFolder.exists() || !localFolder.isDirectory()) {
                localFolder.mkdirs();
            }

            String localFilePath = LOC + domainId + File.separator;
            String remoteFilePath = localFilePath;

            String localFileName = localFilePath + fileName;
            File localFile = new File(localFileName);
            if (localFile.exists()) {
                localFile.delete();
            }
            addHeadersForExactFile(localFile);
            processForDomain(ownDomainEntity, localFile, pDate);

            int serverType = ServerAuthenticationInfoEntity.SERVER_TYPE_FTP;
            copyFileToRemoteServer(serverType, domainId, localFileName, remoteFilePath);

            logglyVO.setStatus(LogglyVO.STATUS_OK);
            logglyVO.setsTime(stTime);
            logglyVO.seteTime(FormatUtils.formatDate(new Date(), "yyyyMMdd HH:mm:ss"));
            logglyVO.setRows(String.valueOf(totalCnt));
            String body = new Gson().toJson(logglyVO);
            LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);

        } catch (Exception e) {
            e.printStackTrace();
//            String subject = getEmailSubject(false);
//            String message = subject;
//            sendMailReport(subject, message);

            logglyVO.setStatus(LogglyVO.STATUS_NG);
            String body = new Gson().toJson(logglyVO);
            LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);
        }

    }

    private void processForDomain(OwnDomainEntity ownDomainEntity, File localFile, String processDate) {

        log.info("********************** process domain " + ownDomainEntity.getId() + ",processDate: " + processDate + ",device:" + device + " *******************");

        try {
            log.info("sleep 2s");
            Thread.sleep(2 * 1000);
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (device.equalsIgnoreCase("desktop")) {
            processFile(localFile, ownDomainEntity, false, processDate);
        } else {
            processFile(localFile, ownDomainEntity, true, processDate);
        }

    }


    private void processFile(File localFile, OwnDomainEntity ownDomainEntity, boolean isMobile, String processDate) {

        int domainId = ownDomainEntity.getId();
        int retryCount = 1;

        while (true) {
            try {
                List<String> dataList = new ArrayList<>();
                dataList = getDataFromDB(ownDomainEntity, isMobile, processDate);
                totalCnt = dataList.size();
                FileUtils.writeLines(localFile, dataList, true);
                break;
            } catch (Exception e) {

                if (retryCount >= QUERY_TRY_COUNT) {
                    System.out.println("====error extract oid : " + domainId);
                    System.out.println("====RETRY OVER " + QUERY_TRY_COUNT + "TIMES error :" + domainId);
                    logglyVO.setStatus(LogglyVO.STATUS_NG);
                    String body = new Gson().toJson(logglyVO);
                    LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);
//                    String subject = getEmailSubject(false);
//                    String message = subject;
//                    sendMailReport(subject, message);
                    return;
                }
                e.printStackTrace();
                System.out.println("====extract error oid:" + domainId + ", sleep " + (1000 * 60 * retryCount));
                try {
                    Thread.sleep(1000 * 60 * retryCount);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
                retryCount++;
            }
        }
    }

    private String getFileName(OwnDomainEntity ownDomainEntity, String processingDate) {

        String fileName = ownDomainEntity.getDomain() + "_" + processingDate + "_PLP+KWtag.txt";
        return fileName;
    }

    private List<String> getDataFromDB(OwnDomainEntity ownDomainEntity, boolean isMobile, String processDate) {

        List<String> extractLines = new ArrayList<String>();
        Set<String> processedEngineSet = new HashSet<>();

        int ownDomainId = ownDomainEntity.getId();
        int engineId = scKeywordRankManager.getSearchEngineId(ownDomainEntity);
        int languageId = scKeywordRankManager.getSearchLanguageId(ownDomainEntity);

        String domainName = ownDomainEntity.getDomain();
        String rootDomainReverse = CommonUtils.getReversedRootDomain(domainName);
        List<CLRankingDetailEntity> dataList = new ArrayList<>();

        int locationId = 0;
        String setKey = engineId + "_" + languageId + "_" + locationId + "_" + device;
        processedEngineSet.add(setKey);

        ExtractQueryVO queryEntity = new ExtractQueryVO();
        queryEntity.setDomainId(ownDomainId);
        queryEntity.setEngineId(engineId);
        queryEntity.setLanguageId(languageId);
        queryEntity.setLocationId(locationId);
        queryEntity.setDevice(device);
        queryEntity.setRankDate(processDate);
        queryEntity.setRootDomainReverse(rootDomainReverse);

        dataList = clDailyRankingEntityDao.exportPLPRank(queryEntity);
        log.info("===dataList size: " + dataList.size());
        for (CLRankingDetailEntity detail : dataList) {
            detail.setRankingDate(processDate);
            extractLines.add(appendData(detail));
        }

        return extractLines;
    }

    private void addHeadersForExactFile(File outFile) throws IOException {

        List<String> lines = new ArrayList<String>();
        StringBuffer header = new StringBuffer();
        header.append("Date").append(SPLIT);
        header.append("Keyword").append(SPLIT);
        header.append("PLP").append(SPLIT);
        header.append("PLP True Rank").append(SPLIT);
        header.append("PLP Web Rank").append(SPLIT);
        header.append("Keyword tags");

        lines.add(header.toString());
        FileUtils.writeLines(outFile, lines, true);
    }

    public static String appendData(CLRankingDetailEntity clRankingDetailEntity) {
        StringBuffer line = new StringBuffer();

        line.append(clRankingDetailEntity.getRankingDate()).append(SPLIT);
        line.append(clRankingDetailEntity.getKeywordName()).append(SPLIT);
        line.append(clRankingDetailEntity.getPlpUrl() == null ? "-" : ExtractService.formatGoogleUrl(clRankingDetailEntity.getPlpUrl())).append(SPLIT);
        if (clRankingDetailEntity.getTrueRank().equals(-1) || clRankingDetailEntity.getTrueRank().equals(0)) {
            line.append("101").append(SPLIT);
        } else {
            line.append(clRankingDetailEntity.getTrueRank()).append(SPLIT);
        }
        if (clRankingDetailEntity.getWebRank().equals(-1) || clRankingDetailEntity.getWebRank().equals(0)) {
            line.append("101").append(SPLIT);
        } else {
            line.append(clRankingDetailEntity.getWebRank()).append(SPLIT);
        }
        String tags = "";
//        log.info("====getTagNameList:" + clRankingDetailEntity.getTagNameList());
        if (StringUtils.isBlank(clRankingDetailEntity.getTagNameList())) {
            tags = "-";
        } else {
            List<String> tagList  = new Gson().fromJson(clRankingDetailEntity.getTagNameList(), List.class);
            for (String tag : tagList) {
                tags += tag + TAG_SPLIT;

            }
            tags = tags.substring(0, tags.length()-TAG_SPLIT.length());
        }
        line.append(tags);
        return line.toString();
    }

    private void copyFileToRemoteServer(int serverType, int domainId, String localFilePath, String remoteFilePath) throws Exception {

        int getServerInfoDomainId = domainId;

        boolean success = serverAuthenticationInfoService.copyFileToRemoteServer(serverType, getServerInfoDomainId, localFilePath, remoteFilePath, null);
    }

    private String getEmailSubject(boolean success) {

        String subject = "";
        String status = success ? "Success" : "Failed";
        subject = status + " Export PLP Rank " + FormatUtils.formatDateToYyyyMmDd(processDate);
        return subject;
    }

    public static void main(String[] args) {

        if (args != null && args.length > 0) {

            if (args[0].contains(",")) {
                domainIdList = args[0].split(",");
            } else {
                domainId = Integer.parseInt(args[0]);
            }

        }

        device = args[1];

        ExtractPLPRank extractPLPRank = new ExtractPLPRank();

        if (domainIdList != null && domainIdList.length > 0) {

            for (String processingDomainId : domainIdList) {

                if (args.length >= 3 && StringUtils.containsIgnoreCase(args[2], ",")) {
                    Date sDate = FormatUtils.toDate(args[2].split(",")[0], FormatUtils.DATE_PATTERN_2);
                    Date eDate = FormatUtils.toDate(args[2].split(",")[1], FormatUtils.DATE_PATTERN_2);
                    while (sDate.compareTo(eDate) <= 0) {
                        processDate = sDate;
                        extractPLPRank.starProcess(FormatUtils.formatDate(processDate, FormatUtils.DATE_PATTERN_2), Integer.parseInt(processingDomainId));
                        sDate = DateUtils.addDays(sDate, 1);
                    }

                } else {
                    processDate = DateUtils.addDays(new Date(), -1);
                    extractPLPRank.starProcess(FormatUtils.formatDate(processDate, FormatUtils.DATE_PATTERN_2), Integer.parseInt(processingDomainId));
                }
            }

        } else {

            if (args.length >= 3 && StringUtils.containsIgnoreCase(args[2], ",")) {
                Date sDate = FormatUtils.toDate(args[2].split(",")[0], FormatUtils.DATE_PATTERN_2);
                Date eDate = FormatUtils.toDate(args[2].split(",")[1], FormatUtils.DATE_PATTERN_2);
                while (sDate.compareTo(eDate) <= 0) {
                    processDate = sDate;
                    extractPLPRank.starProcess(FormatUtils.formatDate(processDate, FormatUtils.DATE_PATTERN_2), domainId);
                    sDate = DateUtils.addDays(sDate, 1);
                }

            } else {
                processDate = DateUtils.addDays(new Date(), -1);
                extractPLPRank.starProcess(FormatUtils.formatDate(processDate, FormatUtils.DATE_PATTERN_2), domainId);
            }

        }
    }

}
