package seoclarity.backend.onetime;

import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import seoclarity.backend.dao.actonia.CommonParamDAO;
import seoclarity.backend.dao.actonia.EngineCountryLanguageMappingEntityDAO;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.actonia.adhoc.AutoAdhocRankProjectEntityDAO;
import seoclarity.backend.dao.aurora.HourlyRankJdbcDAO;
import seoclarity.backend.dao.aurora.HourlyRankTaskDAO;
import seoclarity.backend.dao.clickhouse.adhoc.AdhocInfoDao;
import seoclarity.backend.entity.CLRankingDetailEntity;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.actonia.adhoc.AutoAdhocRankProjectEntity;
import seoclarity.backend.service.ExtractService;
import seoclarity.backend.service.GeoService;
import seoclarity.backend.service.RankTypeManager;
import seoclarity.backend.utils.EmailSenderComponent;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@CommonsLog
public class AdhocExtract {

    private static final String SPLIT = "\t";
    public static final String TABLE_INFO_TABLE = "adhoc_ranking_info";
    public static final String TABLE_DETAIL_TYPE = "adhoc_ranking_detail";
    public static final String TABLE_SUBRANK_TYPE = "adhoc_ranking_subrank";
    private static final String DATABASE_NAME = "adhoc_ranking";
    private static final String HOURLY_DATABASE_NAME = "hourly_adhoc_ranking";

    private static final String LOCAL_FOLDER = "/home/<USER>/";
    private static final String ADHOC_FOLDER = "/adhocExtract";
    private static final String HOURLY_ADHOC_FOLDER = "/hourlyAdhocExtract";

    private static final int PAGESIZE = 100000;

    private static Date processDate;
    private static boolean isHourly = false;
    private static final int QUERY_TRY_COUNT = 10;

    private AutoAdhocRankProjectEntityDAO autoAdhocRankProjectEntityDAO;
    private HourlyRankJdbcDAO hourlyRankJdbcDAO;
    private HourlyRankTaskDAO hourlyRankTaskDAO;
    //    private SeoClarityCityEntityDAO seoClarityCityEntityDAO;
    private OwnDomainEntityDAO ownDomainEntityDAO;
    private EngineCountryLanguageMappingEntityDAO mappingEntityDAO;
    private AdhocInfoDao adhocInfoDao;
    private GeoService geoService;
    private EmailSenderComponent emailSenderComponent;
    private CommonParamDAO commonParamDAO;


    public AdhocExtract(){
        autoAdhocRankProjectEntityDAO = SpringBeanFactory.getBean("autoAdhocRankProjectEntityDAO");
        hourlyRankJdbcDAO = SpringBeanFactory.getBean("hourlyRankJdbcDAO");
//        seoClarityCityEntityDAO = SpringBeanFactory.getBean("seoClarityCityEntityDAO");
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        mappingEntityDAO = SpringBeanFactory.getBean("engineCountryLanguageMappingEntityDAO");
        adhocInfoDao = SpringBeanFactory.getBean("adhocInfoDao");
        hourlyRankTaskDAO = SpringBeanFactory.getBean("hourlyRankTaskDAO");
        geoService = SpringBeanFactory.getBean("geoService");
        emailSenderComponent = SpringBeanFactory.getBean("emailSenderComponent");
        commonParamDAO = SpringBeanFactory.getBean("commonParamDAO");
    }

    private void process(int projectId) {

        AutoAdhocRankProjectEntity autoAdhocRankProjectEntity = autoAdhocRankProjectEntityDAO.getProjectById(projectId);
        if(autoAdhocRankProjectEntity == null){
            System.out.println("====project not exist:" + projectId);
            return;
        }
        int retryCount = 1;
        while (true) {
            try {
                System.out.println("********************** for projectId:" + projectId + " *******************");
                processForProject(autoAdhocRankProjectEntity, null, null);
                break;
            } catch (Exception e) {

                if (retryCount >= QUERY_TRY_COUNT) {
                    System.out.println("====error project : " + projectId);
                    System.out.println("====RETRY OVER " + QUERY_TRY_COUNT + "TIMES error :" + projectId);
                    break;
                }

                e.printStackTrace();
                System.out.println("====projectId error :" + projectId + ", sleep " + (1000 * 60 * retryCount));
                try {
                    Thread.sleep(1000 * 60 * retryCount);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
                retryCount++;
            }
        }

    }

    private void processForProject(AutoAdhocRankProjectEntity autoAdhocRankProject, String startDay, Integer startHour) throws Exception {

        String country = autoAdhocRankProject.getCountry();
        int domainId = autoAdhocRankProject.getOwnDomainId();
        int projectId = autoAdhocRankProject.getId();
        int engineId = autoAdhocRankProject.getSearchEngineId();
        int languageId = autoAdhocRankProject.getLanguageId();
        int device = autoAdhocRankProject.getDevice();
        int keywordType = autoAdhocRankProject.getKeywordType();
        Integer cityId = autoAdhocRankProject.getCityId();
        boolean isMobile = device == 1 ? false : true;
        boolean isCity = false;
        if (keywordType == AutoAdhocRankProjectEntity.KEYWORD_TYPE_GEO
                || autoAdhocRankProject.getGeoPattern() == AutoAdhocRankProjectEntity.GEO_PATTERN_BOTH_GEO_ID_AND_KEYWORD_IN_FILE
                || cityId != null && cityId > 0) {
            isCity = true;
        }

        String extractFilePath = "";
        String fileName = "";
        if (isHourly) {
            extractFilePath = LOCAL_FOLDER + domainId + HOURLY_ADHOC_FOLDER;
            fileName = autoAdhocRankProject.getProjectName() + "_" + domainId + "_" + startDay + "_" + startHour + "_hourlyAdhoc.csv";
        } else {
            extractFilePath = LOCAL_FOLDER + domainId + ADHOC_FOLDER;
            fileName = autoAdhocRankProject.getProjectName() + "_" + domainId + "_" + FormatUtils.formatDate(processDate, "yyyy-MM-dd") + "_adhoc.csv";
        }

        File fileFolder = new File(extractFilePath);
        if (!fileFolder.exists()) {
            fileFolder.mkdirs();
        }

        File outFile = new File("/home/<USER>" + File.separator + fileName);
        if (outFile.exists()) {
            outFile.delete();
        }
        addHeadersForExactFile(outFile);
        System.out.println("====extract file path : " + outFile.getAbsolutePath());

        String detailTableName = getDetailTable(engineId, languageId, isMobile);
        String subRankTableName = getSubrankTable(engineId, languageId, isMobile);

        List<String> extractLines = new ArrayList<String>();
        List<CLRankingDetailEntity> clRankingDetailEntityList = new ArrayList<>();
        clRankingDetailEntityList = adhocInfoDao.extractForAdhoc(isMobile, projectId, domainId, engineId, languageId, cityId, isCity, country);
        log.info("===extract size:" + clRankingDetailEntityList.size());
        if(CollectionUtils.isNotEmpty(clRankingDetailEntityList)){
            for (CLRankingDetailEntity detail : clRankingDetailEntityList) {
                extractLines.add(appendData(detail));
            }
            FileUtils.writeLines(outFile, extractLines, true);
        }


//        int startId = 0;
//        while (true) {
//            log.info("===startId:" + startId);
//
//
////            clRankingDetailEntityList =
////                    adhocInfoDao.queryForExtract(detailTableName, subRankTableName, projectId, isHourly, isCity, country,
////                            startDay, startHour, startId, PAGESIZE);
//
//            log.info("clRankingDetailEntityList:" + clRankingDetailEntityList.size());
//            if (CollectionUtils.isEmpty(clRankingDetailEntityList)) {
//                break;
//            }
//
//            List<String> extractLines = new ArrayList<String>();
//            for (CLRankingDetailEntity detail : clRankingDetailEntityList) {
//                extractLines.add(appendData(detail));
//            }
//            FileUtils.writeLines(outFile, extractLines, true);
//            extractLines = new ArrayList<>();
//
//            Thread.sleep(1000 * 2);
//        }


    }


    private void addHeadersForExactFile(File outFile) throws IOException {
        List<String> lines = new ArrayList<String>();
        StringBuffer header = new StringBuffer();

        header.append("Date").append(SPLIT);
        header.append("Keyword").append(SPLIT);
        header.append("Location").append(SPLIT);
        header.append("Url").append(SPLIT);
        header.append("Url Title").append(SPLIT);
        header.append("Url Parameters").append(SPLIT);
        header.append("True Rank").append(SPLIT);
        header.append("Web Rank").append(SPLIT);
        header.append("URL Type");
//        header.append("SubRank Url").append(SPLIT);
//        header.append("SubRank");

        lines.add(header.toString());
        FileUtils.writeLines(outFile, lines, true);
    }

    public static String appendData(CLRankingDetailEntity clRankingDetailEntity) {
        StringBuffer line = new StringBuffer();
        line.append(clRankingDetailEntity.getRankingDate()).append(SPLIT);
        line.append(clRankingDetailEntity.getKeywordName()).append(SPLIT);
        line.append(clRankingDetailEntity.getCityName()).append(SPLIT);
        String url = StringUtils.isBlank(clRankingDetailEntity.getUrl()) ? "-" : ExtractService.formatGoogleUrl(clRankingDetailEntity.getUrl());
        line.append(url).append(SPLIT);
        line.append(StringUtils.isBlank(clRankingDetailEntity.getLabel()) ? "-" : clRankingDetailEntity.getLabel()).append(SPLIT);
        String urlParam = StringUtils.isBlank(clRankingDetailEntity.getUrlParameters()) ? "-" : clRankingDetailEntity.getUrlParameters();
        if(url.contains("google.com")){
            urlParam = "-";
        }
        line.append(urlParam).append(SPLIT);
        if (clRankingDetailEntity.getTrueRank().equals(-1) || clRankingDetailEntity.getTrueRank().equals(0)) {
            line.append("101").append(SPLIT);
        } else {
            line.append(clRankingDetailEntity.getTrueRank()).append(SPLIT);
        }
        line.append(clRankingDetailEntity.getWebRank().intValue() == 0 ? "-" : clRankingDetailEntity.getWebRank()).append(SPLIT);
        line.append(RankTypeManager.getUrlTypeName(clRankingDetailEntity.getType()));
//        line.append(StringUtils.isBlank(clRankingDetailEntity.getSubRankUrl()) ? "-" : clRankingDetailEntity.getSubRankUrl()).append(SPLIT);
//        line.append(clRankingDetailEntity.getSubRank() == null ? "-" : clRankingDetailEntity.getSubRank());

        return line.toString();
    }

    public String getDetailTable(int engine, int language, boolean isMobile) {

        String prefix = isMobile ? "m_" : "d_";
        String tableName = prefix + TABLE_DETAIL_TYPE;

        if (engine == 1 && language == 1) {
            tableName = tableName + "_us";
        } else {
            tableName = tableName + "_intl";
        }
        if (isHourly) {
            return HOURLY_DATABASE_NAME + "." + tableName;
        } else {
            return DATABASE_NAME + "." + tableName;
        }

    }

    public String getInfoTable(int engine, int language, boolean isMobile) {

        String prefix = isMobile ? "m_" : "d_";
        String tableName = prefix + TABLE_INFO_TABLE;

        if (engine == 1 && language == 1) {
            tableName = tableName + "_us";
        } else {
            tableName = tableName + "_intl";
        }
        if (isHourly) {
            return HOURLY_DATABASE_NAME + "." + tableName;
        } else {
            return DATABASE_NAME + "." + tableName;
        }

    }

    public String getSubrankTable(int engine, int language, boolean isMobile) {

        String prefix = isMobile ? "m_" : "d_";
        String tableName = prefix + TABLE_SUBRANK_TYPE;

        if (engine == 1 && language == 1) {
            tableName = tableName + "_us";
        } else {
            tableName = tableName + "_intl";
        }
        if (isHourly) {
            return HOURLY_DATABASE_NAME + "." + tableName;
        } else {
            return DATABASE_NAME + "." + tableName;
        }

    }

    public static void main(String[] args) {
        AdhocExtract adhocExtract = new AdhocExtract();
        processDate = new Date();
        Integer projectId = null;
        if (args != null && args.length > 0) {
            projectId = Integer.parseInt(args[0]);
            if(projectId == null){
                return;
            }
        }
        System.out.println("=====projectId:" + projectId);
        adhocExtract.process(projectId);
    }




}
