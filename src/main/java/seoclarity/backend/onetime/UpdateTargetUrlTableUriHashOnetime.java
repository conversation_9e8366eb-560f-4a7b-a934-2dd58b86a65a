package seoclarity.backend.onetime;

import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.lang.StringUtils;
import seoclarity.backend.dao.actonia.TargetUrlEntityDAO;
import seoclarity.backend.entity.actonia.TargetUrlEntity;
import seoclarity.backend.utils.CollectionUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.utils.cityhash.CityHashUtil;
import seoclarity.backend.utils.murmurhash.MurmurHashUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * @Ewain
 * @2023-06-02
 */
@CommonsLog
public class UpdateTargetUrlTableUriHashOnetime {

    private TargetUrlEntityDAO targetUrlEntityDAO;

    public UpdateTargetUrlTableUriHashOnetime() {
        targetUrlEntityDAO = SpringBeanFactory.getBean("targetUrlEntityDAO");
    }

    public static void main(String[] args) {
        new UpdateTargetUrlTableUriHashOnetime().process();
    }

    private void process() {
        log.info("Start..............");
        int pageNumber = 0;
        int pageSize = 30000;
        long timeStartMi = System.currentTimeMillis();
        long timeLastMi = System.currentTimeMillis();
        while (true) {
            List<TargetUrlEntity> targetUrlEntityList = targetUrlEntityDAO.targetUrlEntitiesWithPaging(pageNumber, pageSize);
            if (targetUrlEntityList.size() == 0) {
                break;
            }
            List<String> sqlList = new ArrayList<>();
            for (TargetUrlEntity targetUrlEntity : targetUrlEntityList) {
                long id = targetUrlEntity.getId();
                String url = targetUrlEntity.getUrl();
                String uriHash = CityHashUtil.getUnsignedUrlHash(StringUtils.lowerCase(FormatUtils.getUriFromUrl(url)));
                String uriMurmur3Hash = MurmurHashUtils.getMurmurHash3_64(FormatUtils.getUriFromUrl(url));
                String sql = "update t_target_url set uriMurmur3Hash = " + uriMurmur3Hash + ",uri_hash = " + uriHash
                        + " where id = " + id + " and own_domain_id = " + targetUrlEntity.getOwnDomainId();
                sqlList.add(sql);
            }
            List<List<String>> sqlLists = CollectionUtils.splitCollectionBySizeWithStream(sqlList, 5000);
            sqlLists.forEach(var->targetUrlEntityDAO.batchUpdateSql(var));
            log.info("completed size: " + (pageNumber*pageSize + pageSize));
            pageNumber++;
            long curr = System.currentTimeMillis();
            log.info("time cost this time: "+(curr-timeLastMi)/1000 + "s, all time: " + (curr-timeStartMi)/1000 + "s");
            timeLastMi = curr;
        }
        log.info("end..............");
    }
}

// nohup /bin/mvn -f pom.xml exec:java -Dexec.mainClass="seoclarity.backend.onetime.UpdateTargetUrlTableUriHashOnetime" -Dexec.args="" -Dexec.cleanupDaemonThreads=false >> log/update_targetUrl_table.log 2>&1 &