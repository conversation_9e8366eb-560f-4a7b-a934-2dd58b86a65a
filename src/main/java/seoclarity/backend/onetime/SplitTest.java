package seoclarity.backend.onetime;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.ParseException;
import org.apache.http.StatusLine;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.util.EntityUtils;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.util.StopWatch;
import seoclarity.backend.dao.actonia.contentdownload.CommonParamDao;
import seoclarity.backend.entity.actonia.CommonParamEntity;
import seoclarity.backend.entity.clickhouse.ri.BaseClarityDBVO.STRING_ACTION;
import seoclarity.backend.export.vo.TextTypeFilterVO;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


@Slf4j
public class SplitTest {

    private static final String DB_URL = "http://99.198.97.70:8123";
    private static final String CH_DB_NAME = "actonia_site_analytics"; // TODO

    private static final String USER = "default";
    private static final String PSW = "clarity99!";
    private static final String DB_QUERY_URL;

    private static final String endpoint = "https://split-test.seoclarity.net/politeCrawlService/causal_impact/calculate_correlation_coefficient";
    private static final String accessToken = "c09yxv13-opr3-d745-9734-8pu48420nj67";
    private static final CloseableHttpClient httpClient;
    private static final ConcurrentMap<String, UrlMetrics> urlMetricsMap = new ConcurrentHashMap<>();
    private static final LocalDate today = LocalDate.now();
    private static final int MIN_URLS_SIZE = 100;
    private static final int MIN_DAYS = 50;
    private static final int DEFAULT_LOOP_COUNT = 100;
    private static final int DEFAULT_OFF_DAYS = 60;
    private static final String distinctURLSql = "select distinct url from (%s)";
    private static final String GSC_INNER_SELECT_SQL = "select log_date, clicks as entrances, murmurHash3_64(url) as url_murmur_hash, multiIf((dictGetHierarchy('file_dic_parent_child_page_rel', assumeNotNull(murmurHash3_64(concat(toString(own_domain_id), url))))[-1]) = 0, url, dictGetString('file_dic_parent_customhash_to_url', 'parent_url', tuple(dictGetHierarchy('file_dic_parent_child_page_rel', assumeNotNull(murmurHash3_64(concat(toString(own_domain_id), url))))[-1]))) as url";
    private static final String queryGSCInnerSql = GSC_INNER_SELECT_SQL + " FROM actonia_gsc.gsc_daily_insert WHERE own_domain_id = %s AND log_date >= '%s' AND log_date <= '%s' AND (versioning = dictGetUInt16('claritydbVersionGSC', 'versioning', (1, toUInt32(own_domain_id), toDate(log_date), toUInt64(0)))) AND (type = 3) AND (length(attrs.key) = 0)";
    private static final Pattern GSC_SQL_PATTERN = Pattern.compile("^.*(FROM.*) GROUP BY.*$");
    private static final Pattern GA_SQL_PATTERN = Pattern.compile("SELECT.*(FROM.*WHERE.*)\\) t0.*WHERE 1 = 1(.*) GROUP BY");
    private static final String GA_URL_COLUMN_NAME = "concat('https://', hostName, uriX)";
    private static final String GA_INNER_SELECT_SQL = "select log_date, trim(" + GA_URL_COLUMN_NAME + ") as url, murmurHash3_64(url) as url_murmur_hash, dictGetHierarchy('file_dic_parent_child_page_rel', murmurHash3_64(concat(toString(domain_id), 'https://', host_name, uri)))[-1] AS parentCustomUrlMurmur3Hash, if(dictGetString('file_dic_parent_customhash_to_url', 'parent_url', tuple(parentCustomUrlMurmur3Hash)) = '0', host_name, domain(dictGetString('file_dic_parent_customhash_to_url', 'parent_url', tuple(parentCustomUrlMurmur3Hash)))) AS hostName, if(dictGetString('file_dic_parent_customhash_to_url', 'parent_url', tuple(parentCustomUrlMurmur3Hash)) = '0', uri, pathFull(dictGetString('file_dic_parent_customhash_to_url', 'parent_url', tuple(parentCustomUrlMurmur3Hash)))) AS uriX, entrances ";
    private static final String queryGAInnerSql = GA_INNER_SELECT_SQL + " FROM %s WHERE domain_id = %d AND log_date >= '%s' AND log_date <= '%s' AND (medium = 'organic') AND (versoin = dictGetUInt16('claritydbVersion', 'versioning', (2, toUInt32(domain_id), log_date, toUInt64(0))))";
    private static final String QUERY_SQL = "SELECT url,groupArray(concat(toString(log_date),'!_!',toString(sumEntrances))) FROM (" +
            " select url, count(distinct log_date) days, sum(sumEntrances) totalEntrances from (select url, log_date, sum(entrances) sumEntrances from ( %s ) tInner group by url, log_date having sumEntrances>0) t2 group by url HAVING days >= %d) ta" +
            " INNER JOIN ( select url, log_date, sum(entrances) sumEntrances from ( %s ) tInner group by url, log_date having sumEntrances>0 order by url, log_date) tb using (url) group by url ORDER BY rand() ASC";
    private static final List<Character> SQL_SPECIAL_CHAR = Collections.singletonList('\'');
    private static final List<String> SQL_REGEX_ESCAPED_CHAR = new ArrayList<>();
    public static final Integer DATA_SOURCE_TYPE_DAILY_GA = 1;
    public static final Integer DATA_SOURCE_TYPE_OMNITURE = 2;
    public static final Integer DATA_SOURCE_TYPE_FILE_UPLOAD = 3;
    public static final Integer DATA_SOURCE_TYPE_FILE_GA_ALL = 4;
    public static final Integer DATA_SOURCE_TYPE_OMNITURE_GA_ALL = 5;

    static {
        SQL_REGEX_ESCAPED_CHAR.add("\\|");
        SQL_REGEX_ESCAPED_CHAR.add("\\(");
        SQL_REGEX_ESCAPED_CHAR.add("\\)");
        SQL_REGEX_ESCAPED_CHAR.add("\\?");
        SQL_REGEX_ESCAPED_CHAR.add("\\.");
        SQL_REGEX_ESCAPED_CHAR.add("\\*");
        SQL_REGEX_ESCAPED_CHAR.add("\\!");
        SQL_REGEX_ESCAPED_CHAR.add("\\^");
        SQL_REGEX_ESCAPED_CHAR.add("\\$");
        SQL_REGEX_ESCAPED_CHAR.add("\\+");
        SQL_REGEX_ESCAPED_CHAR.add("\\[");
        SQL_REGEX_ESCAPED_CHAR.add("\\]");

        // use http connection pool
        final PoolingHttpClientConnectionManager connManager = new PoolingHttpClientConnectionManager();
        httpClient = HttpClientBuilder.create()
                .setConnectionManager(connManager).build();
        try {
            DB_QUERY_URL = DB_URL + "/?database=" + CH_DB_NAME + "&enable_http_compression=1&user=" + URLEncoder.encode(USER, "UTF-8") + "&password=" +
                    URLEncoder.encode(PSW, "UTF-8") + "&query=";
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
    }

    private final LocalDate prePeriodStartDate;
    private final LocalDate prePeriodEndDate;
    private final LocalDate postPeriodStartDate;
    private final MetricsParams metricsParams;
    private final LocalDate postPeriodEndDate;
    private List<String> distinctUrlList;

    public SplitTest(MetricsParams metricsParams) {
        this.metricsParams = metricsParams;
        log.info("metricsParams: {}", metricsParams);
        final LocalDate endDate = metricsParams.getEndDate();
        prePeriodStartDate = endDate.minusDays(DEFAULT_OFF_DAYS);
        prePeriodEndDate = endDate;
        postPeriodStartDate = endDate.plusDays(1);
        postPeriodEndDate = endDate.plusDays(DEFAULT_OFF_DAYS + 1);
        log.info("endDate: {}, prePeriodStartDate: {}, prePeriodEndDate: {}, postPeriodStartDate: {}, postPeriodEndDate: {}", endDate, prePeriodStartDate, prePeriodEndDate, postPeriodStartDate, postPeriodEndDate);
    }

    public static void main(String[] args) throws IOException {
        MetricsParams metricsParams;
        if (args.length == 0) {
            final JSONObject json = JSONUtil.readJSONObject(new File("metricsParams.json"), StandardCharsets.UTF_8);
            metricsParams = JSONUtil.toBean(json, MetricsParams.class);
            log.info("read metrics params from file: metricsParams.json");
        } else {
            final String arg = args[0];
            log.info("metrics params: {}", arg);
            metricsParams = JSONUtil.toBean(arg, MetricsParams.class);
        }
        if (metricsParams.getLoopCount() == null || metricsParams.getLoopCount() == 0) {
            log.warn("loop count in param is 0, will use default loop count: {}", DEFAULT_LOOP_COUNT);
            metricsParams.setLoopCount(DEFAULT_LOOP_COUNT);
        }
        if (metricsParams.getDomainId() == null || metricsParams.getDomainId() == 0) {
            throw new IllegalArgumentException("Invalid domain id: " + metricsParams.getDomainId());
        }
        if (metricsParams.getMetricsType() == null || metricsParams.getMetricsType() == 0) {
            throw new IllegalArgumentException("Invalid metrics type: " + metricsParams.getMetricsType());
        }
        if (metricsParams.getEndDate() == null) {
            LocalDate endDate = metricsParams.getMetricsType() == 1 ? today.minusDays(DEFAULT_OFF_DAYS + 1) : today.minusDays(DEFAULT_OFF_DAYS + 1 + 4);
            metricsParams.setEndDate(endDate);
        }
    }

    private static UrlMetrics createUrlMetrics(String url, String data) {
        Map<LocalDate, Double> metrics = new HashMap<>();
        for (String s : data.split(",")) {
            final Matcher matcher = Pattern.compile("(\\d{4}-\\d{2}-\\d{2})!_!(\\d+\\.?\\d*)").matcher(s);
            if (!matcher.find()) {
                throw new IllegalArgumentException("Invalid data: " + s);
            }
            LocalDate date = LocalDate.parse(matcher.group(1));
            double metric = Double.parseDouble(matcher.group(2));
            metrics.put(date, metric);
        }
        UrlMetrics urlMetrics = new UrlMetrics();
        urlMetrics.setUrl(url);
        urlMetrics.setMetrics(metrics);
        return urlMetrics;
    }

    private static String filterUrlByContentTypes(int domainId, int metricsType, UrlFilterParam urlFilter) {
        CommonParamDao commonParamDao = SpringBeanFactory.getBean("commonParamDao");
        final List<CommonParamEntity> urlTextTypes = commonParamDao.getParamJsonByDomainId(domainId, "UrlTextType");
        final Optional<String> commonParamOpt = urlTextTypes.stream()
                .filter(commonParamEntity -> urlFilter.searchInput.equals(commonParamEntity.getTitle()))
                .findFirst()
                .map(CommonParamEntity::getParamJson);
        final String paramJson = commonParamOpt.orElseThrow(() -> new IllegalArgumentException("not found content type: " + urlFilter.searchInput));
        final TextTypeFilterVO textTypeFilterVO = JSONUtil.toBean(paramJson, TextTypeFilterVO.class);
        String filterUrlSql = null;
        if (metricsType == 1) {
            return textTypeFilterToSql("url", textTypeFilterVO);
        } else if (metricsType == 2) {
            return textTypeFilterToSql(GA_URL_COLUMN_NAME, textTypeFilterVO);
        }
        return filterUrlSql;
    }

    private static String textTypeFilterToSql(String columnName, TextTypeFilterVO textTypeFilterVO) {
        final StringBuilder sb = new StringBuilder().append(textTypeFilterVO.getCond()).append(" (");
        if (textTypeFilterVO.getLeaf()) {
            sb.append(filterUrlByPages(columnName, textTypeFilterVO.getAction(), textTypeFilterVO.getValue()));
        } else {
            final String collect = Arrays.stream(textTypeFilterVO.getItems()).map(textTypeFilterVO1 -> textTypeFilterToSql(columnName, textTypeFilterVO1))
                    .collect(Collectors.joining(" "));
            sb.append(collect);
        }
        final String string = sb.append(")").toString();
        return string.replaceAll("\\((or|and) ", "(");
    }

    private static String filterUrlByPageTagId(int domainId, int metricsType, UrlFilterParam urlFilter) {
        String filterUrlSql = null;
        if (metricsType == 1) {
            filterUrlSql = " AND (dictGetUInt64('file_dic_managed_url_with_tag', 'target_url_id', (toUInt64(" + domainId + "), toUInt64(" + urlFilter.getSearchInput() + "), assumeNotNull(murmurHash3_64(url)))) > 0)";
        } else if (metricsType == 2) {
            filterUrlSql = " AND (dictGetUInt64('file_dic_rg_managed_uri', 'target_url_id', (toUInt64(" + domainId + "), toUInt64(" + urlFilter.getSearchInput() + "), URLHash(lowerUTF8(uriX)))) > 0)";
        }
        return filterUrlSql;
    }

    private static String filterUrlByPages(String urlColumnName, String actionType, String searchInput) {
        searchInput = checkSqlSpecialCharacter(searchInput);

        String originColumnName = urlColumnName;
        String originSearchInput = searchInput;

        urlColumnName = "lower(" + urlColumnName + ")";
        searchInput = StringUtils.lowerCase(searchInput);

        StringBuilder filterSql = new StringBuilder();
        if (STRING_ACTION.eq.toString().equals(actionType)) {
            filterSql.append(urlColumnName).append(" = '").append(searchInput).append("'");
        } else if (STRING_ACTION.neq.toString().equals(actionType)) {
            filterSql.append(urlColumnName).append(" <> '").append(searchInput).append("'");
        } else if (STRING_ACTION.ct.toString().equals(actionType)) {
            if (!StringUtils.containsAny(searchInput, StringUtils.join(SQL_SPECIAL_CHAR, ""))) {
                searchInput = FormatUtils.getClarityDBLikeFilter(searchInput);
            }
            filterSql.append(urlColumnName).append(" like '%").append(searchInput).append("%'");
        } else if (STRING_ACTION.nct.toString().equals(actionType)) {
            if (!StringUtils.containsAny(searchInput, StringUtils.join(SQL_SPECIAL_CHAR, ""))) {
                searchInput = FormatUtils.getClarityDBLikeFilter(searchInput);
            }
            filterSql.append(urlColumnName).append(" not like '%").append(searchInput).append("%'");
        } else if (STRING_ACTION.sw.toString().equals(actionType)) {
            if (!StringUtils.containsAny(searchInput, StringUtils.join(SQL_SPECIAL_CHAR, ""))) {
                searchInput = FormatUtils.getClarityDBLikeFilter(searchInput);
            }
            filterSql.append(urlColumnName).append(" like '").append(searchInput).append("%'");
        } else if (STRING_ACTION.ew.toString().equals(actionType)) {
            if (!StringUtils.containsAny(searchInput, StringUtils.join(SQL_SPECIAL_CHAR, ""))) {
                searchInput = FormatUtils.getClarityDBLikeFilter(searchInput);
            }
            filterSql.append(urlColumnName).append(" like '%").append(searchInput).append("'");
        } else if (STRING_ACTION.pt.toString().equalsIgnoreCase(actionType)) {
            filterSql.append(" match(").append(originColumnName).append(", '").append(getReplacedEscapedSpecialCharacterForRegx(originSearchInput)).append("')");
        } else if (STRING_ACTION.npt.toString().equalsIgnoreCase(actionType)) {
            filterSql.append(" not match(").append(originColumnName).append(", '").append(getReplacedEscapedSpecialCharacterForRegx(originSearchInput)).append("')");
        } else if (StringUtils.isBlank(actionType)) {
            if (!StringUtils.containsAny(searchInput, StringUtils.join(SQL_SPECIAL_CHAR, ""))) {
                searchInput = FormatUtils.getClarityDBLikeFilter(searchInput);
            }
            filterSql.append(" and ").append(urlColumnName).append(" like '%").append(searchInput).append("%'");
        }
        return filterSql.toString();
    }

    public static String getReplacedEscapedSpecialCharacterForRegx(String val) {
        String input = val;
        for (String chr : SQL_REGEX_ESCAPED_CHAR) {
            if (StringUtils.contains(val, chr) && !StringUtils.contains(val, "\\" + chr)) {
                val = StringUtils.replace(val, chr, "\\" + chr);
                System.out.println("checkSqlSpecialEscapedCharacter, input val:" + input + " -> " + val);
            }
        }
        return val;
    }

    public static String checkSqlSpecialCharacter(String decodedStr) {
        boolean isNeedCheck = false;
        for (char c : SQL_SPECIAL_CHAR) {
            if (StringUtils.contains(decodedStr, c)) {
                isNeedCheck = true;
                break;
            }
        }
        if (!isNeedCheck) {
            return decodedStr;
        }
        StringBuilder res = new StringBuilder();
        for (int i = 0; i < decodedStr.length(); i++) {
            char x = decodedStr.charAt(i);
            if (SQL_SPECIAL_CHAR.contains(x)) {
                if (i > 0) {
                    char y = decodedStr.charAt(i - 1);
                    res.append((StringUtils.equals("\\", String.valueOf(y))) ? x : ("\\" + x));
                } else {
                    res.append("\\").append(x);
                }
            } else {
                res.append(x);
            }
        }
        System.out.println("checkSqlSpecialCharacter, decodedStr:" + decodedStr + " -> " + res);
        return res.toString();
    }

    private static String httpQuerySql(String queryUrl) throws IOException {
        final HttpGet httpGet = new HttpGet(queryUrl);
        httpGet.setHeader("Content-Type", "text/plain; charset=UTF-8");
        return httpClient.execute(httpGet, response -> {
            final HttpEntity responseEntity = response.getEntity();
            final String result = EntityUtils.toString(responseEntity);
            EntityUtils.consumeQuietly(responseEntity);
            return result;
        });
    }

    /**
     * t_own_domain_setting.traffic_type vs. ga-cdb tables:
     * 1: dis_ga + dis_analytics_ga4
     * 2: dis_adobe      	 --domain list need take om_analytics_group into accounted
     * 3: dis_upload
     * 314: dis_upload + dis_ga + dis_analytics_ga4
     * 215: dis_adobe + dis_ga + dis_analytics_ga4  --domain list need take om_analytics_group into accounted
     */
    // https://www.wrike.com/open.htm?id=**********
    public static String getTable(Integer trafficType, Integer googleAnalyticsVersion) {
        // https://www.wrike.com/open.htm?id=*********
        // for special data type like 214, it means old sourceType = 2, now sourceType = 1, we should merge 2 and 1 data, use the last value 4 as the sourceType
        int dateSourceType = trafficType == null ? DATA_SOURCE_TYPE_FILE_GA_ALL : trafficType < 10 ? trafficType : trafficType % 10;
        Set<String> tables = new HashSet<>(1);
        if (dateSourceType == DATA_SOURCE_TYPE_OMNITURE) {
            tables.add("dis_adobe");
        } else if (dateSourceType == DATA_SOURCE_TYPE_FILE_UPLOAD) {
            tables.add("dis_upload");
        } else if (dateSourceType == DATA_SOURCE_TYPE_FILE_GA_ALL) {
            tables.add("dis_ga");
            tables.add("dis_analytics_ga4");
//			tables.add("dis_adobe");
            tables.add("dis_upload");
        } else if (dateSourceType == DATA_SOURCE_TYPE_OMNITURE_GA_ALL) {
            tables.add("dis_ga");
            tables.add("dis_analytics_ga4");
            tables.add("dis_adobe");
        } else {
            tables.add("dis_ga");
        }
        // if googleAnalyticsVersion is 4, then we need to add dis_analytics_ga4 table
        if (googleAnalyticsVersion != null && googleAnalyticsVersion == 4) {
            tables.add("dis_analytics_ga4");
        }
        String dataBase = "actonia_site_analytics";
        if (tables.size() == 1) {
            return dataBase + "." + tables.iterator().next();
        } else {
            return "merge(" + dataBase + ",'^" + StringUtils.join(tables, "$|^") + "$')";
        }
    }

    public List<String> getDistinctUrlList(String innerSql) {
        final String distinctUrlQueryUrl;
        try {
            distinctUrlQueryUrl = DB_QUERY_URL + URLEncoder.encode(String.format(distinctURLSql, innerSql), "UTF-8");
            log.info("distinctUrlQueryUrl: {}", distinctUrlQueryUrl);
            final String result = httpQuerySql(distinctUrlQueryUrl);
            log.info("distinct urls: {}", result);
            this.distinctUrlList = Arrays.stream(result.split("\n")).map(String::trim).collect(Collectors.toList());
            return distinctUrlList;
        } catch (Exception e) {
            log.error("get distinct url error: {}", e.getMessage());
            return Collections.emptyList();
        }
    }

    public CorrelationReport start(String innerSql) throws IOException {
        final StopWatch stopWatch = new StopWatch(this.metricsParams.domainId.toString());
        final String sql;
        try {
            stopWatch.start("getRetrieveMetricsSql");
            sql = getRetrieveMetricsSql(innerSql);
            stopWatch.stop();
            log.info("getRetrieveMetricsSql: {}", sql);
        } catch (Exception e) {
            log.error("getRetrieveMetricsSql error: {}", e.getMessage());
            return null;
        }
        CorrelationReport highestCorrelationReport = new CorrelationReport();
        stopWatch.start("Loop");
        for (int i = 1; i <= this.metricsParams.getLoopCount(); i++) {
            log.info("Loop {} starts", i);

            // Step 1: Retrieve metrics from data source based on client selection
            final List<String> metrics = retrieveMetrics(sql);
            List<UrlMetrics> urlMetricsList = metrics
                    .stream()
                    .map(s -> {
                        // split url and data with regex group
                        final Matcher matcher = Pattern.compile("^(.*)\\s+\\[(.*)]$").matcher(s);
                        if (!matcher.find()) {
                            throw new RuntimeException("Failed to parse line: " + s);
                        }
                        final String url = matcher.group(1);
                        UrlMetrics urlMetrics = urlMetricsMap.get(url);
                        if (urlMetrics != null) {
                            return urlMetrics;
                        }
                        final String data = matcher.group(2);
                        urlMetrics = createUrlMetrics(url, data);
                        urlMetricsMap.putIfAbsent(url, urlMetrics);
                        return urlMetrics;
                    })
                    .collect(Collectors.toList());
            final CorrelationReport correlationReport = new CorrelationReport();
            correlationReport.setTime(LocalDateTime.now());
            correlationReport.setIndex(i);
            if (urlMetricsList.size() < 2) {
                log.warn("filteredUrlMetricsList size is less than 2, no need to split test. urlMetricsList size: {}", urlMetricsList.size());
                return correlationReport;
            }
            // split urlMetricsList into get first two records to build testGroup and controlGroup
            int midIndex = urlMetricsList.size() / 2;
            List<UrlMetrics> testGroup = new ArrayList<>(urlMetricsList.subList(0, midIndex));
            List<UrlMetrics> controlGroup = new ArrayList<>(urlMetricsList.subList(midIndex, urlMetricsList.size()));
            correlationReport.setTestGroup(testGroup);
            correlationReport.setControlGroup(controlGroup);
            log.info("testGroup size: {}", testGroup.size());
            log.info("controlGroup size: {}", controlGroup.size());
            // Step 4: Query CausalImpact API and record correlation coefficients
            // Loop 100 times with different URLs in each group
            // Record the "control_name_corr_coef_map" for each iteration
            final CorrelationCoefficientRequest correlationCoefficientRequest = buildRequest(correlationReport.testTimeSeries, Collections.singletonList(correlationReport.controlTimeSeries), this.metricsParams.domainId + "_" + i);
            final Double correlation = calculateCorrelationCoefficient(correlationCoefficientRequest);
            // compare highest correlation with correlation coefficient, if it is higher, then update the highest correlation report with the this one
            log.info("Correlation coefficient: {}", correlation);
            if (correlation <= highestCorrelationReport.getCorrelation()) {
                log.info("correlation is not higher than highest correlation, skip");
                continue;
            }
            correlationReport.setCorrelation(correlation);
            log.info("highestCorrelation score updated: [score: {}, index: {}], previous: [score: {}, index: {}]", correlation, i, highestCorrelationReport.getCorrelation(), highestCorrelationReport.getIndex());
            // update highestCorrelationReport with this one
            highestCorrelationReport = correlationReport;
        }
        stopWatch.stop();
        // Step 5: Tag groups with highest correlation
        // Find the group with the highest correlation coefficient
        final double correlation = highestCorrelationReport.getCorrelation();
        log.info("highest correlation: {}, index: {}\n{}", correlation, highestCorrelationReport.getIndex(), stopWatch.prettyPrint());
        return highestCorrelationReport;
    }

    public String getInnerSql() {
        String innerSql = "";
        UrlFilterParam urlFilter = metricsParams.getUrlFilter();
        final String filterType = urlFilter.getFilterType();
        final String searchInput = urlFilter.searchInput;
        final Integer metricsType = metricsParams.getMetricsType();
        final String relIds = metricsParams.getRelIds();
        final Integer domainId = metricsParams.getDomainId();
        switch (filterType) {
            case "Urls":
                if (metricsType == 1) {
                    innerSql = String.format(queryGSCInnerSql, StringUtils.isBlank(relIds) ? domainId : domainId + " AND rel_id in (" + relIds + ")", prePeriodStartDate, prePeriodEndDate);
                } else {
                    innerSql = String.format(queryGAInnerSql, getTable(metricsParams.dataSourceType, metricsParams.googleAnalyticsVersion), domainId, prePeriodStartDate, prePeriodEndDate);
                }
                innerSql = innerSql + "AND url_murmur_hash IN (" + searchInput + ")";
                break;
            case "Sql":
                if (metricsType == 1) {
                    final Matcher matcher = GSC_SQL_PATTERN.matcher(searchInput);
                    if (!matcher.find()) {
                        log.error("filterSqlCondition: {} not match GSC pattern", searchInput);
                    }
                    final String sqlCondition = matcher.group(1);
                    innerSql = "select trim(decodeURLComponent(url)) as url, log_date, clicks as entrances " + sqlCondition;
                } else {
                    final Matcher matcher = GA_SQL_PATTERN.matcher(searchInput);
                    if (!matcher.find()) {
                        log.error("filterSqlCondition: {} not match GA pattern", searchInput);
                    }
                    innerSql = GA_INNER_SELECT_SQL + matcher.group(1) + matcher.group(2);
                }
                break;
        }
        return innerSql;
    }

    @SneakyThrows
    private String getRetrieveMetricsSql(String innerSql) {
        int days = DEFAULT_OFF_DAYS;
        while (true) {
            final String sql = String.format(QUERY_SQL, innerSql, days, innerSql);
            final String countSql = "SELECT count(*) FROM (" + sql + ") t";
            final String queryUrl = DB_QUERY_URL + URLEncoder.encode(countSql, "UTF-8");
            final String countStr = httpQuerySql(queryUrl);
            final int count = Integer.parseInt(countStr.trim());
            if (days == MIN_DAYS) {
                log.warn("days already reach MIN_DAYS: {}, count = {}", days, count);
                if (count < 2) {
                    log.warn("count: {} < 2", count);
                }
                // return sql no matter count is less than MIN_URLS_SIZE when days == MIN_DAYS
                return String.format(QUERY_SQL, innerSql, 0, innerSql);
            }
            // return sql when count >= MIN_URLS_SIZE
            if (count >= MIN_URLS_SIZE) {
                log.info("count >= MIN_URLS_SIZE: {}, days = {}", count, days);
                return sql;
            }
            log.info("count: {}, days: {}", count, days);
            days--;
        }
    }

    private SummaryMetricsResult summaryMetricsByDate(List<UrlMetrics> metrics) {
        final SummaryMetricsResult summaryMetricsResult = new SummaryMetricsResult();
        // create a list of metrics with all dates from period to post period
        double sum = 0.0;
        double maxSum = 0.0;
        double secondMaxSum = 0.0;
        UrlMetrics maxUrlMetrics = new UrlMetrics();
        int maxUrlMetricsIndex = 0;
        for (int i = 0; i < metrics.size(); i++) {
            // sum all metric every day group by date
            UrlMetrics metric = metrics.get(i);
            final double metricSum = metric.metrics.values()
                    .stream().reduce(Double::sum)
                    .orElse(0.0);
            sum += metricSum;
            metric.setSumClicks(metricSum);
            if (metricSum > maxSum) {
                secondMaxSum = maxSum;
                maxSum = metricSum;
                maxUrlMetrics = metric;
                maxUrlMetricsIndex = i;
            } else if (metricSum > secondMaxSum) {
                secondMaxSum = metricSum;
            }
        }
        double avg = sum / metrics.size();
        // remove a extreme large sum value to reduce the noise if only have a sum is triple larger than avg
        // if secondMaxSum is larger than 3 times of avg means the max sum is not too large
        if (maxSum > avg * 3 && secondMaxSum < avg * 3) {
            final String url = maxUrlMetrics.url;
            log.warn("remove a extreme large sum value to reduce the noise, sum: {}, url: {}, maxSum: {}, avg: {}", sum, url, maxSum, avg);
            summaryMetricsResult.setExtremeUrl(url);
            metrics.remove(maxUrlMetricsIndex);
        }

        final int periodDates = DEFAULT_OFF_DAYS * 2 + 2;
        double[] summary = new double[periodDates];
        for (int i = 0; i < periodDates; i++) {
            final LocalDate localDate = prePeriodStartDate.plusDays(i);
            double metric = 0.0;
            // sum all metrics for this date
            for (final UrlMetrics urlMetrics : metrics) {
                metric += urlMetrics.getMetrics().getOrDefault(localDate, 0.0);
            }
            summary[i] = metric;
        }
        summaryMetricsResult.setMetrics(summary);
        return summaryMetricsResult;
    }

    @SneakyThrows
    private List<String> retrieveMetrics(String sql) {
        StatusLine statusLine;
        HttpUriRequest httpGet = new HttpGet(DB_QUERY_URL + URLEncoder.encode(sql, "UTF-8"));
        List<String> lines = new ArrayList<>();
        HttpEntity responseEntity = null;
        try {
            CloseableHttpResponse httpResponse = httpClient.execute(httpGet);
            responseEntity = httpResponse.getEntity();
            statusLine = httpResponse.getStatusLine();
            int statusCode = statusLine.getStatusCode();
            String reasonPhrase = statusLine.getReasonPhrase();
            if (statusCode != 200) {
                log.warn("status: {}, message: {}", statusCode, reasonPhrase);
                return Collections.emptyList();
            }
            try (BufferedReader bufferedReader = new BufferedReader(new
                    InputStreamReader(responseEntity.getContent(), StandardCharsets.UTF_8))) {
                String line;
                while ((line = bufferedReader.readLine()) != null) {
                    lines.add(line);
                }
            }
        } finally {
            EntityUtils.consumeQuietly(responseEntity);
        }
        return lines;
    }

    private Double calculateCorrelationCoefficient(CorrelationCoefficientRequest request) {
        final String requestJson = JSONUtil.toJsonStr(request);
        final HttpPost httpPost = new HttpPost(endpoint);
        httpPost.setEntity(new StringEntity(requestJson, "utf-8"));
//        log.info("calculateCorrelationCoefficient request: {}", requestJson);
        // retry 3 times
        Double score = 0.0;
        for (int i = 0; i < 3; i++) {
            try (CloseableHttpResponse httpResponse = httpClient.execute(httpPost)) {
                if (httpResponse.getStatusLine().getStatusCode() != 200) {
                    log.error("calculateCorrelationCoefficient error: {}, status: {}, retry: {}", httpResponse.getStatusLine().getReasonPhrase(), httpResponse.getStatusLine().getStatusCode(), i);
                    continue;
                }
                try {
                    final HttpEntity responseEntity = httpResponse.getEntity();
                    final String responseContent = EntityUtils.toString(responseEntity);
                    EntityUtils.consumeQuietly(responseEntity);
                    final CorrelationCoefficientResponse correlationCoefficientResponse = JSONUtil.toBean(responseContent, CorrelationCoefficientResponse.class);
                    final Map<String, Double> controlNameCorrCoefMap = correlationCoefficientResponse.getControl_name_corr_coef_map();
                    score = controlNameCorrCoefMap.values().iterator().next();
                } catch (IOException | ParseException e) {
                    log.error("calculateCorrelationCoefficient error: {}", e.getMessage());
                }
            } catch (IOException e) {
                log.error("calculateCorrelationCoefficient error: {}", e.getMessage());
            }
        }
        return score;
    }

    private CorrelationCoefficientRequest buildRequest(double[] testTimeSeries, List<double[]> controlTimeSeriesList, String controlName) {
        return CorrelationCoefficientRequest.builder()
                .access_token(accessToken)
                .test_time_series(testTimeSeries)
                .control_name_list(Collections.singletonList(controlName + "_" + System.currentTimeMillis()))
                .control_time_series_list(controlTimeSeriesList)
                .pre_period_start_date(prePeriodStartDate.toString())
                .pre_period_end_date(prePeriodEndDate.toString())
                .post_period_start_date(postPeriodStartDate.toString())
                .post_period_end_date(postPeriodEndDate.toString())
                .build();
    }

    @Data
    public static class MetricsParams {
        private Integer domainId;
        private Integer dataSourceType;
        private boolean enableParentChildRel;
        private String relIds;
        private UrlFilterParam urlFilter;
        private Integer metricsType;
        private Integer loopCount;
        @DateTimeFormat(pattern = "yyyy-MM-dd")
        private LocalDate endDate;
        private Integer googleAnalyticsVersion;
    }

    @Data
    public static class UrlFilterParam {
        private String filterType; // PageTags, Pages, ContentTypes, Urls, Sql
        private String actionType;
        private String searchInput;
    }

    @Data
    public static class UrlMetrics {
        private String url;
        private int sumTraffic;
        private double sumClicks;
        // store metric every day
        private Map<LocalDate, Double> metrics;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    private static class CorrelationCoefficientRequest {
        private final int version = 3;
        private String access_token;
        private double[] test_time_series;
        private List<String> control_name_list;
        private List<double[]> control_time_series_list;
        private String pre_period_start_date;
        private String pre_period_end_date;
        private String post_period_start_date;
        private String post_period_end_date;

    }

    @Data
    private static class CorrelationCoefficientResponse {
        private Boolean success;
        private Map<String, Double> control_name_corr_coef_map; // correlation coefficient by control name

    }

    @Data
    private static class SummaryMetricsResult {
        private double[] metrics;
        private String extremeUrl;
    }

    @Data
    public class CorrelationReport {

        private int index;
        private LocalDateTime time;
        private double correlation;
        private List<UrlMetrics> testGroup = new ArrayList<>();
        private double[] testTimeSeries;
        private String extremeTestUrl;
        private List<UrlMetrics> controlGroup = new ArrayList<>();
        private double[] controlTimeSeries;
        private String extremeControlUrl;
        private Set<String> randomAllocateTestUrls;
        private Set<String> randomAllocateControlUrls;

        public String summary() {
            return "index: " + index + ", time: " + time
                    + ", correlation: " + correlation
                    + ", testGroupSize: " + testGroup.size()
                    + ", controlGroupSize: " + controlGroup.size()
                    + ", extremeTestUrl: " + extremeTestUrl
                    + ", extremeControlUrl: " + extremeControlUrl;
        }

        public void setTestGroup(List<UrlMetrics> testGroup) {
            this.testGroup = testGroup;
            final SummaryMetricsResult summaryMetricsResult = summaryMetricsByDate(testGroup);
            this.testTimeSeries = summaryMetricsResult.getMetrics();
            if (summaryMetricsResult.extremeUrl != null) {
                this.extremeTestUrl = summaryMetricsResult.extremeUrl;
            }
        }

        public void setControlGroup(List<UrlMetrics> controlGroup) {
            this.controlGroup = controlGroup;
            final SummaryMetricsResult summaryMetricsResult = summaryMetricsByDate(this.controlGroup);
            this.controlTimeSeries = summaryMetricsResult.getMetrics();
            if (summaryMetricsResult.extremeUrl != null) {
                this.extremeControlUrl = summaryMetricsResult.extremeUrl;
            }
        }
    }
}
