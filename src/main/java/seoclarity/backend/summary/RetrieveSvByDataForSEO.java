package seoclarity.backend.summary;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import lombok.extern.apachecommons.CommonsLog;
import okhttp3.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import seoclarity.backend.dao.actonia.EngineCountryLanguageMappingEntityDAO;
import seoclarity.backend.dao.clickhouse.keywordsv.SvRetrieveResponseDao;
import seoclarity.backend.dao.rankcheck.AdwordsGeoIdDAO;
import seoclarity.backend.dao.rankcheck.KeywordCityAdwordsDataEntityDAO;
import seoclarity.backend.dao.rankcheck.SeoClarityKeywordEntityDAO;
import seoclarity.backend.dao.rankcheck.retrievesv.SvRetrieveStateDAO;
import seoclarity.backend.entity.actonia.adhoc.KeywordResultSv;
import seoclarity.backend.entity.clickhouse.keywordsv.SvRetrieveResponseEntity;
import seoclarity.backend.entity.rankcheck.KeywordCityAdwordsDataEntity;
import seoclarity.backend.entity.rankcheck.SeoClarityKeywordEntity;
import seoclarity.backend.entity.rankcheck.retrievesv.SvRetrieveStateEntity;
import seoclarity.backend.utils.CollectionSplitUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.*;
import java.net.URLDecoder;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 确认读取文件的列对应，请求的keyword为decode
 */
@CommonsLog
public class RetrieveSvByDataForSEO {

    private static final boolean IS_US = true;
    private static boolean IS_CITY = false;
    private static final int RETRY_COUNT = 3;
    private static final int API_RETRIEVE_MONTH = 48;

    private static final String postUrl = "https://api.dataforseo.com/v3/keywords_data/google_ads/search_volume/task_post";
    private static final String getUrl = "https://api.dataforseo.com/v3/keywords_data/google_ads/search_volume/tasks_ready";
    private static final String apiUrl = "https://api.dataforseo.com/v3/keywords_data/google_ads/search_volume/task_get/%s";
    private static final String credential = Credentials.basic("<EMAIL>", "TmjH1hLcIGqUqoTk");
    private static int REQUEST_KEYWORD_COUNT = 1000;
    private static final String UPLOAD_FILE_S3_BUCHET_NAME = "dfs-retrievesv-response";
    private static final int ENGINE_ID = 99; // TODO
    private final static String KEY_SPLIT = "!_!";
    private static final String SPLIT_FIELD = "\t";

    private static final String SPLIT_FIELD_COMMA = ",";
    private static final String LOCAL_PATH = "/home/<USER>/";

    private static final int RESULT_OK = 1;
    private static final int RESULT_ERROR = 0;
    private static int UPLOAD_INFO_ID = 23042614;
    private int locationId = 2840; // TODO

    private static int startNo = 0;
    private static int endNo = 0;

    private static final int DAILY_MAX_REQUEST_COUNT = 100;
    private static final int TIME_OUT = 60 * 1000;

    private List<String> errorLineList = new ArrayList<>();
    private File resultFile = new File("/home/<USER>/daily_us_0512/dataforseoSv_0515.txt");
    private File taskIdFile = new File("/home/<USER>/daily_us_0512/taskIdFile_0515.txt");
    private File successedTaskIdFile = new File("/home/<USER>/daily_us_0512/successedTaskIdFile_0515.txt");
    private File notFindTaskIdFile = new File("/home/<USER>/daily_us_0512/notFindTaskIdFile_0515.txt");
    private File noResultTaskIdFile = new File("/home/<USER>/daily_us_0512/noResultTaskIdFile_0515.txt");
    private File invalidKeywordFile = new File("/home/<USER>/daily_us_0512/invalidKeywordFile_0515.txt");
    private File noResultKeywordFile = new File("/home/<USER>/noResultKeywordFile" + LocalDate.now() + ".txt");

    private File resultIntlFile = new File("/home/<USER>/daily_intl_0515/dataforseoSv_0515.txt");
    private File taskIdIntlFile = new File("/home/<USER>/daily_intl_0515/taskIdFile_0515.txt");
    private File successedIntlTaskIdFile = new File("/home/<USER>/daily_intl_0515/successedTaskIdFile_0515.txt");
    private File notFindIntlTaskIdFile = new File("/home/<USER>/daily_intl_0515/notFindTaskIdFile_0515.txt");
    private File noResultIntlTaskIdFile = new File("/home/<USER>/daily_intl_0515/noResultTaskIdFile_0515.txt");
    private File invalidIntlKeywordFile = new File("/home/<USER>/daily_intl_0515/invalidKeywordFile_0515.txt");

    Set<String> successedKwList = new HashSet<>();
    List<String> invalidKwList = new ArrayList<>();
    List<String> errorKwList = new ArrayList<>();

    private Map<String, List<String>> elFileMap = new HashMap();
    private static File elFile;

    private EngineCountryLanguageMappingEntityDAO engineCountryLanguageMappingEntityDAO;
    private AdwordsGeoIdDAO adwordsGeoIdDAO;
    private SeoClarityKeywordEntityDAO seoClarityKeywordEntityDAO;
    private KeywordCityAdwordsDataEntityDAO keywordCityAdwordsDataEntityDAO;
    private SvRetrieveStateDAO svRetrieveStateDAO;
    private SvRetrieveResponseDao svRetrieveResponseDao;

    private static List<String> postedFileList = Arrays.asList("1_2076.csv", "4_2012.csv", "6_2826.csv",
            "1_2196.csv", "6_2702.csv", "4_2250.csv",
            "4_2372.csv", "1_2070.csv", "1_2191.csv", "3_2404.csv", "3_2528.csv", "10_2036.csv", "2_2804.csv", "3_2764.csv",
            "3_2400.csv", "3_2642.csv", "2_2807.csv", "1_2188.csv", "7_2300.csv", "8_2032.csv", "8_2276.csv", "3_2414.csv"
            );

    public RetrieveSvByDataForSEO() {
        engineCountryLanguageMappingEntityDAO = SpringBeanFactory.getBean("engineCountryLanguageMappingEntityDAO");
        adwordsGeoIdDAO = SpringBeanFactory.getBean("adwordsGeoIdDAO");
        seoClarityKeywordEntityDAO = SpringBeanFactory.getBean("seoClarityKeywordEntityDAO");
        keywordCityAdwordsDataEntityDAO = SpringBeanFactory.getBean("keywordCityAdwordsDataEntityDAO");
        svRetrieveStateDAO = SpringBeanFactory.getBean("svRetrieveStateDAO");
        svRetrieveResponseDao = SpringBeanFactory.getBean("svRetrieveResponseDao");
    }

    public static void main(String[] args) throws Exception {

        UPLOAD_INFO_ID = Integer.parseInt(FormatUtils.formatDate(new Date(), "yyMMddHH"));
        log.info("===UPLOAD_INFO_ID:" + UPLOAD_INFO_ID);
        RetrieveSvByDataForSEO retrieveSvByDataForSEO = new RetrieveSvByDataForSEO();

        int step = Integer.parseInt(args[0]);
        if (step == 1) {//step1: 分组
            retrieveSvByDataForSEO.splitGroupKeywordsFile(args[1]);
        } else if (step == 2) {//step2: post
            boolean isFolder = Boolean.parseBoolean(args[1]);
            IS_CITY = Boolean.parseBoolean(args[2]);//for city
            if (isFolder) {
                File[] files = new File(args[3]).listFiles();
                for (File f : files) {
//                    if (postedFileList.contains(f.getName())) {
//                        log.info("===skip file:" + f.getName());
//                        continue;
//                    }
                    retrieveSvByDataForSEO.processGroupKeywordFiles(f.getAbsolutePath());
                }
            } else {
                retrieveSvByDataForSEO.processGroupKeywordFiles(args[3]);
            }
        } else if (step == 3) {//step3: get result
            if (args.length > 2 && args[2] != null) {
                elFile = new File(args[2]);
            }
            retrieveSvByDataForSEO.getSuccessedKwsByTaskIdFile(args[1]);
        }

    }

    public static <K, V extends Comparable<? super V>> Map<K, V> sortDes(Map<K, V> map) {

        List<Map.Entry<K, V>> list = new ArrayList<>(map.entrySet());
        Collections.sort(list, new Comparator<Map.Entry<K, V>>() {
            @Override
            public int compare(Map.Entry<K, V> o1, Map.Entry<K, V> o2) {
                int compare = (o1.getValue()).compareTo(o2.getValue());
                return -compare;
            }
        });

        Map<K, V> retuenMap = new LinkedHashMap<K, V>();
        for (Map.Entry<K, V> entry : list) {
            retuenMap.put(entry.getKey(), entry.getValue());
        }
        return retuenMap;
    }

    private void startRetrieve(Map<Integer, List<String>> keywordLocationMap) throws Exception {

        log.info("====keywordLocationMap size:" + keywordLocationMap.size());
        //        log.info("====keywordLocationMap size:" + JSON.toJSONString(keywordLocationMap));

        for (Integer locationId : keywordLocationMap.keySet()) {
            List<String> keywordList = keywordLocationMap.get(locationId).stream().distinct().collect(Collectors.toList());
            List<List<String>> pageList = CollectionSplitUtils.splitCollectionBySize(keywordList, REQUEST_KEYWORD_COUNT);
            log.info("====pageList size:" + pageList.size() + ",locationId:" + locationId);

            int startIndex = 0;
            while (true) {
                if (startIndex == pageList.size()) {
                    log.info("finished all pages, move to next part.");
                    break;
                }
                List<String> splitList = pageList.get(startIndex);
                log.info("now page is :" + startIndex);
                log.info("====splitList size:" + splitList.size());
                startIndex++;
                int retry = 0;
                String taskId = null;
                try {
                    while (true) {
                        if (taskId == null && retry <= RETRY_COUNT) {
                            taskId = postUrl(splitList, locationId);
                            FileUtils.writeLines(invalidKeywordFile, invalidKwList, true);
                            invalidKwList = new ArrayList<>();
                            Thread.sleep(1 * 1000);
                            if (taskId != null && taskId.equals("PaymentRequired")) {
                                log.info("*****************1PaymentRequired,SL1H");
                                Thread.sleep(1000 * 60 * 60);
                                taskId = null;
                            }else if(taskId != null && !taskId.equals("PaymentRequired")){
                                System.out.println("===taskId：" + taskId);
                                System.out.println("===postUrlSuc");
                                List<String> taskIdList = new ArrayList<>();
                                taskIdList.add(taskId);
                                FileUtils.writeLines(taskIdFile, taskIdList, true);
                            } else {
                                log.error("===post failed:" + retry + "," + JSON.toJSONString(splitList));
                            }
                            retry++;
                        } else {
                            if (taskId != null && taskId.equals("PaymentRequired")) {
                                log.info("*****************2PaymentRequired,SL1H");
                                Thread.sleep(1000 * 60 * 60);
                                taskId = null;
                            }else {
                                break;
                            }
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    log.info("====error splitList: " + JSON.toJSONString(splitList));
                    break;
                }
            }
        }

    }

    private void updateCityAdwords(List<Result> allKeywordResultSvList, Map<String, List<KeywordCityAdwordsDataEntity>> keywordSvMap) {

        for (Result result : allKeywordResultSvList) {

            String keyword = result.getKeyword();
            Integer locationId = result.getLocation_code();
            //            System.out.println("=========kw:" + result.getKeyword());

            Integer avgSv = result.getSearch_volume();
            Double cpc = result.getCpc();
            Double competition = result.getCompetition_index();

            String key = locationId + KEY_SPLIT + keyword;

            List<KeywordCityAdwordsDataEntity> keywordCityAdwordsDataEntityList = keywordSvMap.get(key);
            if (CollectionUtils.isEmpty(keywordCityAdwordsDataEntityList)) {
                log.error("----no EntityList:" + key);
                StringBuffer stringBuffer = new StringBuffer();
                stringBuffer.append(keyword).append("\t");
                stringBuffer.append(locationId);
                errorLineList.add(stringBuffer.toString());
                continue;
            }


            if (result.getMonthly_searches() != null) {
                for (KeywordCityAdwordsDataEntity keywordCityAdwordsDataEntity : keywordCityAdwordsDataEntityList) {
                    for (Result.MonthlySearches monthlySearches : result.getMonthly_searches()) {

                        KeywordCityAdwordsDataEntity keywordCityAdwordsData = new KeywordCityAdwordsDataEntity();
                        keywordCityAdwordsData.setKeywordId(keywordCityAdwordsDataEntity.getKeywordId());
                        keywordCityAdwordsData.setCityId(keywordCityAdwordsDataEntity.getCityId());
                        keywordCityAdwordsData.setEngineId(ENGINE_ID);
                        keywordCityAdwordsData.setLanguageId(keywordCityAdwordsDataEntity.getLanguageId());
                        keywordCityAdwordsData.setUploadInfoId(UPLOAD_INFO_ID);
                        keywordCityAdwordsData.setAvgMonthlySearchVolume(avgSv == null ? 0 : avgSv);
                        keywordCityAdwordsData.setCostPerClick(cpc);

                        Integer monthYear = 0;
                        if (monthlySearches.getMonth().toString().length() == 1) {
                            monthYear = NumberUtils.toInt(monthlySearches.getYear() + "0" + monthlySearches.getMonth());
                        } else {
                            monthYear = NumberUtils.toInt(monthlySearches.getYear() + "" + monthlySearches.getMonth());
                        }

                        String monthSv = monthYear + "=" + monthlySearches.getSearch_volume();
                        keywordCityAdwordsData.setMonthlySearchDate(FormatUtils.toDate(monthYear.toString(), "yyyyMM"));
                        keywordCityAdwordsData.setMonthlySearchVolume(monthlySearches.getSearch_volume());

                        KeywordCityAdwordsDataEntity dataEntityList = keywordCityAdwordsDataEntityDAO.getExistEntitiesByInfos(keywordCityAdwordsData);
                        if (dataEntityList != null) {
                            log.info("====update:" + dataEntityList.getId());
                            keywordCityAdwordsData.setId(dataEntityList.getId());
                            keywordCityAdwordsDataEntityDAO.updateEntity(keywordCityAdwordsData);
                        } else {
                            log.info("====insert:" + keywordCityAdwordsData.getMonthlySearchDate());
                            keywordCityAdwordsDataEntityDAO.insert(keywordCityAdwordsData);
                        }

                    }

                }


            } else {
                log.info("====no sv result:" + keyword);
            }

        }


    }

    private void writeResult(String taskId, List<Result> allKeywordResultSvList) throws Exception {

        List<String> noResultList = new ArrayList<>();

        List<SvRetrieveResponseEntity> svRetrieveResponseEntityList = new ArrayList<>();
        for (Result result : allKeywordResultSvList) {
            List<String> resultList = new ArrayList<>();

            String keyword = result.getKeyword();
            Integer locationId = result.getLocation_code();

//            if(locationId != 2056 && locationId != 2578 && locationId != 2704){
//                continue;
//            }

            SvRetrieveResponseEntity svRetrieveResponseEntity = new SvRetrieveResponseEntity();
            svRetrieveResponseEntity.setTaskId(taskId);
            svRetrieveResponseEntity.setLocationId(locationId);
            svRetrieveResponseEntity.setKeywordName(keyword);
            svRetrieveResponseEntityList.add(svRetrieveResponseEntity);

//            String uniqueKey = result.getKeyword() + "!_!" + result.getLocation_code();
            //            System.out.println("=========kw:" + result.getKeyword());

//            int engineId = 0;
//            int languageId = 0;
//            if(locationId == 2840){
//                engineId =1;
//                languageId =1;
//            }else if(locationId == 2250){
//                engineId =4;
//                languageId =7;
//            }else if(locationId == 2380){
//                engineId =8;
//                languageId =9;
//            }else if(locationId == 2276){
//                engineId =14;
//                languageId =15;
//            }else if(locationId == 2826){
//                engineId =6;
//                languageId =8;
//            }

            Integer avgSv = result.getSearch_volume();
            Double cpc = result.getCpc();
            Double competition = result.getCompetition_index();

            /**
             * 一次性时使用engineId，languageId
             */
            if (elFileMap != null && elFileMap.size() > 0) {
                String dfsKey = locationId + KEY_SPLIT + keyword;
                List<String> elList = elFileMap.get(dfsKey);
                for (String el : elList) {
//                    if(elList.size() > 1){
//                        log.info("=2222222: " + dfsKey + "," + el);
//                    }
                    StringBuffer buffer = new StringBuffer();
                    buffer.append(el.split(KEY_SPLIT)[0]).append(SPLIT_FIELD);
                    buffer.append(el.split(KEY_SPLIT)[1]).append(SPLIT_FIELD);
                    buffer.append(keyword).append(SPLIT_FIELD);
                    buffer.append(locationId).append(SPLIT_FIELD);
                    buffer.append(avgSv == null ? "-" : avgSv).append(SPLIT_FIELD);
                    buffer.append(cpc == null ? "-" : cpc).append(SPLIT_FIELD);
                    buffer.append(competition == null ? "-" : competition.intValue()).append(SPLIT_FIELD);

                    if (result.getMonthly_searches() != null) {
//                    System.out.println("=========not null:" + keyword);
                        for (Result.MonthlySearches monthlySearches : result.getMonthly_searches()) {
                            int monthYear = 0;
                            if (monthlySearches.getMonth().toString().length() == 1) {
                                monthYear = NumberUtils.toInt(monthlySearches.getYear() + "0" + monthlySearches.getMonth());
                            } else {
                                monthYear = NumberUtils.toInt(monthlySearches.getYear() + "" + monthlySearches.getMonth());
                            }
                            String monthSv = monthYear + "=" + monthlySearches.getSearch_volume();
                            buffer.append(monthSv).append(SPLIT_FIELD);
                        }
                    } else {
                        log.info("====no sv result:" + keyword);
                        StringBuffer stringBuffer = new StringBuffer();
                        stringBuffer.append(keyword).append(SPLIT_FIELD);
                        stringBuffer.append(locationId);
                        noResultList.add(stringBuffer.toString());
                    }
                    resultList.add(buffer.toString());
                }

            } else {
                StringBuffer buffer = new StringBuffer();
                buffer.append(keyword).append(SPLIT_FIELD);
                buffer.append(locationId).append(SPLIT_FIELD);
                buffer.append(avgSv == null ? "-" : avgSv).append(SPLIT_FIELD);
                buffer.append(cpc == null ? "-" : cpc).append(SPLIT_FIELD);
                buffer.append(competition == null ? "-" : competition.intValue()).append(SPLIT_FIELD);

                if (result.getMonthly_searches() != null) {
//                    System.out.println("=========not null:" + keyword);
                    for (Result.MonthlySearches monthlySearches : result.getMonthly_searches()) {
                        int monthYear = 0;
                        if (monthlySearches.getMonth().toString().length() == 1) {
                            monthYear = NumberUtils.toInt(monthlySearches.getYear() + "0" + monthlySearches.getMonth());
                        } else {
                            monthYear = NumberUtils.toInt(monthlySearches.getYear() + "" + monthlySearches.getMonth());
                        }

                        String monthSv = monthYear + "=" + monthlySearches.getSearch_volume();
                        buffer.append(monthSv).append(SPLIT_FIELD);
                    }

                } else {
                    log.info("====no sv result:" + keyword);
                    StringBuffer stringBuffer = new StringBuffer();
                    stringBuffer.append(keyword).append(SPLIT_FIELD);
                    stringBuffer.append(locationId);
                    noResultList.add(stringBuffer.toString());
                }
                resultList.add(buffer.toString());
            }

            if (IS_US) {
                FileUtils.writeLines(resultFile, resultList, true);
            } else {
                FileUtils.writeLines(resultIntlFile, resultList, true);
            }

        }

        log.info("=====INSERTResponse:" + svRetrieveResponseEntityList.size());
        svRetrieveResponseDao.batchAdd(svRetrieveResponseEntityList);

        FileUtils.writeLines(noResultKeywordFile, noResultList, true);
    }

    private void setMonthSv(int index, KeywordCityAdwordsDataEntity keywordCityAdwordsData, KeywordResultSv keywordResultSv) {

        Date monthlySearchDate = null;
        Integer monthlySearchVolume = null;
        switch (index) {
            case 1:
                if (keywordResultSv.getM1_month() == null) {//todo 每次需要手动更改日期
                    keywordCityAdwordsData.setMonthlySearchDate(FormatUtils.toDate("20220301", "yyyyMMdd"));
                    keywordCityAdwordsData.setMonthlySearchVolume(0);
                } else {
                    if (keywordResultSv.getM1_month() < 10) {
                        monthlySearchDate = FormatUtils.toDate(keywordResultSv.getM1_year() + "-0" + keywordResultSv.getM1_month(), "yyyy-MM");
                    } else {
                        monthlySearchDate = FormatUtils.toDate(keywordResultSv.getM1_year() + "-" + keywordResultSv.getM1_month(), "yyyy-MM");
                    }
                    monthlySearchVolume = keywordResultSv.getM1();
                    keywordCityAdwordsData.setMonthlySearchDate(monthlySearchDate);
                    keywordCityAdwordsData.setMonthlySearchVolume(monthlySearchVolume);
                }
                break;
            case 2:
                if (keywordResultSv.getM2_month() == null) {
                    keywordCityAdwordsData.setMonthlySearchDate(FormatUtils.toDate("20220201", "yyyyMMdd"));
                    keywordCityAdwordsData.setMonthlySearchVolume(0);
                } else {
                    if (keywordResultSv.getM2_month() < 10) {
                        monthlySearchDate = FormatUtils.toDate(keywordResultSv.getM2_year() + "-0" + keywordResultSv.getM2_month(), "yyyy-MM");
                    } else {
                        monthlySearchDate = FormatUtils.toDate(keywordResultSv.getM2_year() + "-" + keywordResultSv.getM2_month(), "yyyy-MM");
                    }
                    monthlySearchVolume = keywordResultSv.getM2();
                    keywordCityAdwordsData.setMonthlySearchDate(monthlySearchDate);
                    keywordCityAdwordsData.setMonthlySearchVolume(monthlySearchVolume);
                }
                break;
            case 3:
                if (keywordResultSv.getM3_month() == null) {
                    keywordCityAdwordsData.setMonthlySearchDate(FormatUtils.toDate("20220101", "yyyyMMdd"));
                    keywordCityAdwordsData.setMonthlySearchVolume(0);
                } else {
                    if (keywordResultSv.getM3_month() < 10) {
                        monthlySearchDate = FormatUtils.toDate(keywordResultSv.getM3_year() + "-0" + keywordResultSv.getM3_month(), "yyyy-MM");
                    } else {
                        monthlySearchDate = FormatUtils.toDate(keywordResultSv.getM3_year() + "-" + keywordResultSv.getM3_month(), "yyyy-MM");
                    }
                    monthlySearchVolume = keywordResultSv.getM3();
                    keywordCityAdwordsData.setMonthlySearchDate(monthlySearchDate);
                    keywordCityAdwordsData.setMonthlySearchVolume(monthlySearchVolume);
                }
                break;
            case 4:
                if (keywordResultSv.getM4_month() == null) {
                    keywordCityAdwordsData.setMonthlySearchDate(FormatUtils.toDate("20211201", "yyyyMMdd"));
                    keywordCityAdwordsData.setMonthlySearchVolume(0);
                } else {
                    if (keywordResultSv.getM4_month() < 10) {
                        monthlySearchDate = FormatUtils.toDate(keywordResultSv.getM4_year() + "-0" + keywordResultSv.getM4_month(), "yyyy-MM");
                    } else {
                        monthlySearchDate = FormatUtils.toDate(keywordResultSv.getM4_year() + "-" + keywordResultSv.getM4_month(), "yyyy-MM");
                    }
                    monthlySearchVolume = keywordResultSv.getM4();
                    keywordCityAdwordsData.setMonthlySearchDate(monthlySearchDate);
                    keywordCityAdwordsData.setMonthlySearchVolume(monthlySearchVolume);
                }
                break;
            case 5:
                if (keywordResultSv.getM5_month() == null) {
                    keywordCityAdwordsData.setMonthlySearchDate(FormatUtils.toDate("20211101", "yyyyMMdd"));
                    keywordCityAdwordsData.setMonthlySearchVolume(0);
                } else {
                    if (keywordResultSv.getM5_month() < 10) {
                        monthlySearchDate = FormatUtils.toDate(keywordResultSv.getM5_year() + "-0" + keywordResultSv.getM5_month(), "yyyy-MM");
                    } else {
                        monthlySearchDate = FormatUtils.toDate(keywordResultSv.getM5_year() + "-" + keywordResultSv.getM5_month(), "yyyy-MM");
                    }
                    monthlySearchVolume = keywordResultSv.getM5();
                    keywordCityAdwordsData.setMonthlySearchDate(monthlySearchDate);
                    keywordCityAdwordsData.setMonthlySearchVolume(monthlySearchVolume);
                }
                break;
            case 6:
                if (keywordResultSv.getM6_month() == null) {
                    keywordCityAdwordsData.setMonthlySearchDate(FormatUtils.toDate("20211001", "yyyyMMdd"));
                    keywordCityAdwordsData.setMonthlySearchVolume(0);
                } else {
                    if (keywordResultSv.getM6_month() < 10) {
                        monthlySearchDate = FormatUtils.toDate(keywordResultSv.getM6_year() + "-0" + keywordResultSv.getM6_month(), "yyyy-MM");
                    } else {
                        monthlySearchDate = FormatUtils.toDate(keywordResultSv.getM6_year() + "-" + keywordResultSv.getM6_month(), "yyyy-MM");
                    }
                    monthlySearchVolume = keywordResultSv.getM6();
                    keywordCityAdwordsData.setMonthlySearchDate(monthlySearchDate);
                    keywordCityAdwordsData.setMonthlySearchVolume(monthlySearchVolume);
                }
                break;
            case 7:
                if (keywordResultSv.getM7_month() == null) {
                    keywordCityAdwordsData.setMonthlySearchDate(FormatUtils.toDate("20210901", "yyyyMMdd"));
                    keywordCityAdwordsData.setMonthlySearchVolume(0);
                } else {
                    if (keywordResultSv.getM7_month() < 10) {
                        monthlySearchDate = FormatUtils.toDate(keywordResultSv.getM7_year() + "-0" + keywordResultSv.getM7_month(), "yyyy-MM");
                    } else {
                        monthlySearchDate = FormatUtils.toDate(keywordResultSv.getM7_year() + "-" + keywordResultSv.getM7_month(), "yyyy-MM");
                    }
                    monthlySearchVolume = keywordResultSv.getM7();
                    keywordCityAdwordsData.setMonthlySearchDate(monthlySearchDate);
                    keywordCityAdwordsData.setMonthlySearchVolume(monthlySearchVolume);
                }
                break;
            case 8:
                if (keywordResultSv.getM8_month() == null) {
                    keywordCityAdwordsData.setMonthlySearchDate(FormatUtils.toDate("20210801", "yyyyMMdd"));
                    keywordCityAdwordsData.setMonthlySearchVolume(0);
                } else {
                    if (keywordResultSv.getM8_month() < 10) {
                        monthlySearchDate = FormatUtils.toDate(keywordResultSv.getM8_year() + "-0" + keywordResultSv.getM8_month(), "yyyy-MM");
                    } else {
                        monthlySearchDate = FormatUtils.toDate(keywordResultSv.getM8_year() + "-" + keywordResultSv.getM8_month(), "yyyy-MM");
                    }
                    monthlySearchVolume = keywordResultSv.getM8();
                    keywordCityAdwordsData.setMonthlySearchDate(monthlySearchDate);
                    keywordCityAdwordsData.setMonthlySearchVolume(monthlySearchVolume);
                }
                break;
            case 9:
                if (keywordResultSv.getM9_month() == null) {
                    keywordCityAdwordsData.setMonthlySearchDate(FormatUtils.toDate("20210701", "yyyyMMdd"));
                    keywordCityAdwordsData.setMonthlySearchVolume(0);
                } else {
                    if (keywordResultSv.getM9_month() < 10) {
                        monthlySearchDate = FormatUtils.toDate(keywordResultSv.getM9_year() + "-0" + keywordResultSv.getM9_month(), "yyyy-MM");
                    } else {
                        monthlySearchDate = FormatUtils.toDate(keywordResultSv.getM9_year() + "-" + keywordResultSv.getM9_month(), "yyyy-MM");
                    }
                    monthlySearchVolume = keywordResultSv.getM9();
                    keywordCityAdwordsData.setMonthlySearchDate(monthlySearchDate);
                    keywordCityAdwordsData.setMonthlySearchVolume(monthlySearchVolume);
                }
                break;
            case 10:
                if (keywordResultSv.getM10_month() == null) {
                    keywordCityAdwordsData.setMonthlySearchDate(FormatUtils.toDate("20210601", "yyyyMMdd"));
                    keywordCityAdwordsData.setMonthlySearchVolume(0);
                } else {
                    if (keywordResultSv.getM10_month() < 10) {
                        monthlySearchDate = FormatUtils.toDate(keywordResultSv.getM10_year() + "-0" + keywordResultSv.getM10_month(), "yyyy-MM");
                    } else {
                        monthlySearchDate = FormatUtils.toDate(keywordResultSv.getM10_year() + "-" + keywordResultSv.getM10_month(), "yyyy-MM");
                    }
                    monthlySearchVolume = keywordResultSv.getM10();
                    keywordCityAdwordsData.setMonthlySearchDate(monthlySearchDate);
                    keywordCityAdwordsData.setMonthlySearchVolume(monthlySearchVolume);
                }
                break;
            case 11:
                if (keywordResultSv.getM11_month() == null) {
                    keywordCityAdwordsData.setMonthlySearchDate(FormatUtils.toDate("20210501", "yyyyMMdd"));
                    keywordCityAdwordsData.setMonthlySearchVolume(0);
                } else {
                    if (keywordResultSv.getM11_month() < 10) {
                        monthlySearchDate = FormatUtils.toDate(keywordResultSv.getM11_year() + "-0" + keywordResultSv.getM11_month(), "yyyy-MM");
                    } else {
                        monthlySearchDate = FormatUtils.toDate(keywordResultSv.getM11_year() + "-" + keywordResultSv.getM11_month(), "yyyy-MM");
                    }
                    monthlySearchVolume = keywordResultSv.getM11();
                    keywordCityAdwordsData.setMonthlySearchDate(monthlySearchDate);
                    keywordCityAdwordsData.setMonthlySearchVolume(monthlySearchVolume);
                }
                break;
            case 12:
                if (keywordResultSv.getM12_month() == null) {
                    keywordCityAdwordsData.setMonthlySearchDate(FormatUtils.toDate("20210401", "yyyyMMdd"));
                    keywordCityAdwordsData.setMonthlySearchVolume(0);
                } else {
                    if (keywordResultSv.getM12_month() < 10) {
                        monthlySearchDate = FormatUtils.toDate(keywordResultSv.getM12_year() + "-0" + keywordResultSv.getM12_month(), "yyyy-MM");
                    } else {
                        monthlySearchDate = FormatUtils.toDate(keywordResultSv.getM12_year() + "-" + keywordResultSv.getM12_month(), "yyyy-MM");
                    }
                    monthlySearchVolume = keywordResultSv.getM12();
                    keywordCityAdwordsData.setMonthlySearchDate(monthlySearchDate);
                    keywordCityAdwordsData.setMonthlySearchVolume(monthlySearchVolume);
                }
                break;
        }
    }

    private String postUrl(List<String> keywordList, int location) throws UnsupportedEncodingException {

        if (CollectionUtils.isEmpty(keywordList)) {
            log.info("====keywordList empty, don't post.");
            return null;
        }

        LocalDate now = LocalDate.now();
        LocalDate minDate = now.minusMonths(API_RETRIEVE_MONTH);
        String dateFrom = minDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        System.out.println("===postUrl:" + ",location:" + location + ",date_from:" + dateFrom + ",time:" + LocalDate.now());
        MediaType mediaType = MediaType.parse("text/x-markdown; charset=utf-8");
        OkHttpClient client = new OkHttpClient();

        List<String> processingKeywordList = new ArrayList<>();
        processingKeywordList.addAll(keywordList);

        int requestCnt = 0;
        while (true) {

            HashMap<String, Object> parameters = new HashMap<>();
            parameters.put("keywords", processingKeywordList);
            parameters.put("location_code", location);
            parameters.put("date_from", dateFrom);
            String requestBody = "[" + new Gson().toJson(parameters) + "]";
            //        String requestBody = generateParametersForPost(parameters);

            //            System.out.println("==requestBody:" + requestBody);

            Request request = new Request.Builder()
                    .url(postUrl)
                    .header("Authorization", credential)
                    .post(RequestBody.create(mediaType, requestBody))
                    .build();
            Call call = client.newCall(request);
            try {
                Response response = call.execute();
                requestCnt++;

                if (requestCnt >= REQUEST_KEYWORD_COUNT) {
                    log.error("========over maxRetryCnt:" + requestCnt);
                    return null;
                }

                //判断是否成功
                if (response.isSuccessful()) {
                    JSONObject jsonObject = JSONObject.parseObject(Objects.requireNonNull(response.body()).string());
                    String length = jsonObject.get("tasks").toString();
                    JSONObject tasks = JSONObject.parseObject(length.substring(1, length.length() - 1));
                    //                    log.info("*********" + JSON.toJSONString(tasks));
                    String statusCode = tasks.get("status_code").toString();
                    log.info("-----------status_code:" + statusCode);
                    if (statusCode.equals("20100")) {
                        return tasks.get("id").toString();
                    } else if (statusCode.equals("40501")) {
                        //"status_message": "Invalid Field: 'keywords'. Keyword text has invalid characters or symbols: 'best+men%27s+face+routine'.",
                        //"status_message": Invalid Field: 'keywords'. Keyword text exceeds the allowed limit: 'cinnamon+beach+lane+home+near+ginn+ocean+hammock+beach+resort+site%3Avacationrentalpros.com'.
                        String statusMessage = tasks.get("status_message").toString();
                        //                        log.error("====status_message:" + statusMessage);
                        String errorKw = "";

                        if (statusMessage.contains("Keyword text has invalid characters or symbols")) {
                            errorKw = statusMessage.replaceAll("Invalid Field: 'keywords'. Keyword text has invalid characters or symbols: '", "");
                            errorKw = errorKw.substring(0, errorKw.length() - 2);
                            log.info("=====errorKw1:" + errorKw);
                        } else if (statusMessage.contains("Keyword text exceeds the allowed limit")) {
                            errorKw = statusMessage.replaceAll("Invalid Field: 'keywords'. Keyword text exceeds the allowed limit: '", "");
                            errorKw = errorKw.substring(0, errorKw.length() - 2);
                            log.info("=====errorKw2:" + errorKw);
                        } else if (statusMessage.contains("Keyword text has too many words")) {
                            errorKw = statusMessage.replaceAll("Invalid Field: 'keywords'. Keyword text has too many words: '", "");
                            errorKw = errorKw.substring(0, errorKw.length() - 2);
                            log.info("=====errorKw3:" + errorKw);
                        } else if (statusMessage.contains("Invalid Field: 'keywords'. Keyword is empty")) {
                            errorKw = statusMessage.replaceAll("Invalid Field: 'keywords'. Keyword is empty: '", "");
                            errorKw = errorKw.substring(0, errorKw.length() - 2);
                            log.info("=====errorKw4:" + errorKw);
                        } else if (statusMessage.contains("Invalid Field: 'location_code'.")) {
                            log.info("=====errorMsg:" + statusMessage + ",location:" + location);
                            return null;
                        } else {
                            log.info("=====new error:" + statusMessage + ",requestBody:" + requestBody);
                            return null;
                        }

                        StringBuffer stringBuffer = new StringBuffer();
                        stringBuffer.append(errorKw).append(SPLIT_FIELD);
                        stringBuffer.append(location);
                        invalidKwList.add(stringBuffer.toString());
                        processingKeywordList.remove(errorKw);
                        if (processingKeywordList.size() <= 0) {
                            log.info("====no kw need to post!");
                            return "noKw";
                        }
                    } else {
                        log.error("********post error status_code:" + statusCode);
                        System.exit(0);
                    }

                } else {
                    JSONObject jsonObject = JSONObject.parseObject(Objects.requireNonNull(response.body()).string());
                    String length = jsonObject.get("tasks").toString();
                    JSONObject tasks = JSONObject.parseObject(length.substring(1, length.length() - 1));
                    //                    log.info("*********" + JSON.toJSONString(tasks));
                    String statusCode = tasks.get("status_code").toString();
                    String statusMessage = tasks.get("status_message").toString();
                    log.info("-----------status_code:" + statusCode + ",statusMessage:" + statusMessage);
                    if (statusCode.equals("40200")) {//Payment Required
                        log.error("***********Payment Required*************");
                        return "PaymentRequired";
                    }else {
                        log.error("***********statusCode:" + statusCode + ",statusMessage:" + statusMessage);
                    }
                    System.out.println("post请求失败");
                    String errorMsg = "post api error:" + ",location:" + location + ",keywords size:" + keywordList.size();
                    log.error(errorMsg);
//                    System.exit(0);
                    return null;
                }

            } catch (Exception e) {
                System.out.println("post异常");
                String errorMsg = "post api error:" + ",location:" + location + ",keywords size:" + keywordList.size();
                log.error(e.getMessage(), e);
                return null;
            }

        }

    }

    private static List<String> getUrl() {
        System.out.println("===getUrl time:" + LocalDateTime.now());
        try {
            OkHttpClient client = new OkHttpClient();

            Response response = client.newCall(new Request.Builder().url(getUrl).header("Authorization", credential).get().build()).execute();

            if (response.isSuccessful()) {
                JSONObject jsonObject = JSONObject.parseObject(Objects.requireNonNull(response.body()).string());
                String tasksLength = jsonObject.get("tasks").toString();
                JSONObject tasks = JSONObject.parseObject(tasksLength.substring(1, tasksLength.length() - 1));
                if (tasks.get("result") == null) {
                    System.out.println("===no result time:" + LocalDateTime.now());
                    return null;
                }
                String resultLength = tasks.get("result").toString();
                String[] strings = resultLength.substring(1, resultLength.length() - 1).split("},");
                List<String> list = new ArrayList<>();
                for (String s : strings) {
                    if (!s.endsWith("}")) {
                        s = s + "}";
                    }
                    JSONObject result = JSONObject.parseObject(s);
                    list.add(result.get("id").toString());
                    System.out.println(result.get("id"));
                }
                return list;
            } else {
                System.out.println("get请求失败");
                String errorMsg = "gey tasks_ready api error";
            }
        } catch (Exception e) {
            System.out.println("get异常");
            String errorMsg = "gey tasks_ready api error";
            log.error(e.getMessage(), e);
        }
        return null;
    }

    private List<Result> getResult(String id) {
        System.out.println("===getResult:" + ",id:" + id + " time:" + LocalDateTime.now());
        try {
            File saveFolder = new File(LOCAL_PATH + "groupSvResult" + LocalDate.now() + "/s3File");
            if (!saveFolder.exists()) {
                saveFolder.mkdirs();
            }
            File saveFile = new File(saveFolder.getAbsolutePath() + "/" + id);

            OkHttpClient client = new OkHttpClient();
            Response response = client.newCall(new Request.Builder().
                    url(String.format(apiUrl, id)).
                    header("Authorization", credential)
                    .get().build()).execute();

            String responseBody = response.body().string();
            org.json.JSONObject json = new org.json.JSONObject(responseBody);
//            log.info("====responseStr:" + json);
            List<String> lines = new ArrayList<String>();
            lines.add(json.toString());
            FileUtils.writeLines(saveFile, lines);
//            int sendResult = sendToS3(FormatUtils.formatDate(new Date(), FormatUtils.DATE_PATTERN_2), id, saveFile.getAbsolutePath());
//            if (sendResult == RESULT_ERROR) {
//                // TODO
//                System.out.println(" ==SendToS3Error:" + id);
//            }else {
////                saveFile.delete();
//            }

            if (response.isSuccessful()) {
                JSONObject jsonObject = JSONObject.parseObject(Objects.requireNonNull(responseBody));
                String tasksLength = jsonObject.get("tasks").toString();
                JSONObject tasks = JSONObject.parseObject(tasksLength.substring(1, tasksLength.length() - 1));
                int statusCode = NumberUtils.toInt(tasks.get("status_code").toString());
                if (statusCode != 20000 && statusCode != 20100) {
                    String errMsg = tasks.get("status_message").toString();
                    log.error("task error:" + id + ",errMsg:" + errMsg);
                    return null;
                }
                String resultLength = tasks.get("result").toString();
                return JSONArray.parseArray(resultLength, Result.class);
            } else {
                System.out.println("api请求失败" + response.toString());
                String errorMsg = "get by taskId error:" + ",taskId:" + id;
            }
        } catch (Exception e) {
            System.out.println("api异常");
            String errorMsg = "get by taskId api error:" + ",taskId:" + id;
            log.error(e.getMessage(), e);
        }
        return new ArrayList<>();
    }


    private void splitGroupKeywordsFile(String fullPathLocalFileName) throws Exception {

        log.info("===========processing file:" + fullPathLocalFileName + ",st:" + startNo + ",end:" + endNo);
        File file = new File(fullPathLocalFileName);

        File invalidKwFile = new File(LOCAL_PATH + "groupSvResult" + LocalDate.now() + "/invalidKw.csv");

        if (invalidKwFile.exists()) {
            invalidKwFile.delete();
        }

        Set<String> invalidFileList = new HashSet<>();

        Map<String, List<KeywordCityAdwordsDataEntity>> keywordSvMap = new HashMap<>();
        Map<String, List<String>> groupKeywordMap = new HashMap();
        try {

            InputStreamReader inputReader = new InputStreamReader(new FileInputStream(file));
            BufferedReader bf = new BufferedReader(inputReader);
            String line;
            int linNo = 0;
            int keywordCount = 0;
            while ((line = bf.readLine()) != null) {
                linNo++;

                if (endNo != 0 && (linNo < startNo || linNo > endNo)) {
                    continue;
                }

                try {

                    if (StringUtils.isBlank(line)) {
                        System.out.println("==SkipEmptyLine:" + linNo);
                        continue;
                    }

                    String[] arr = line.split(SPLIT_FIELD);

                    int groupId = Integer.parseInt(arr[0]);
//                    int engineId = Integer.parseInt(arr[1]);
//                    int languageId = Integer.parseInt(arr[2]);
                    int criteriaId = Integer.parseInt(arr[1]);
                    int languageId = Integer.parseInt(arr[2]);
                    int cityId = Integer.parseInt(arr[3]);
                    String keywordName = arr[4];

                    //                        log.info("===groupId:" + groupId + ",criteriaId:" + criteriaId);

                    String formatKeywordName = FormatUtils.checkKeyword(keywordName);
                    if (StringUtils.isBlank(formatKeywordName)) {
                        log.info("====skip invalid keywords:" + keywordName + ",linNo:" + linNo);
                        invalidFileList.add(line);
                        continue;
                    }

                    String groupKey = groupId + "_" + criteriaId;
                    if (groupKeywordMap.get(groupKey) == null) {
                        List<String> keywordList = new ArrayList<>();
                        keywordList.add(line);
                        groupKeywordMap.put(groupKey, keywordList);
                    } else {
                        List<String> keywordList = groupKeywordMap.get(groupKey);
                        keywordList.add(line);
                        groupKeywordMap.put(groupKey, keywordList);
                    }


                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("***** error line:" + linNo + "," + line);
                    errorLineList.add(line);
                    continue;

                }

            }
            log.info("=========invalidFileList size:" + invalidFileList.size());
            List<SvRetrieveStateEntity> svRetrieveStateEntityList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(invalidFileList)) {
                for (String invalidLine : invalidFileList) {
                    String[] arr = invalidLine.split(SPLIT_FIELD);

                    int languageId = Integer.parseInt(arr[2]);
                    int cityId = Integer.parseInt(arr[3]);
                    String keywordName = arr[4];
                    String encodeKeywordName = FormatUtils.encodeKeyword(keywordName);
                    SeoClarityKeywordEntity rankCheckKeywordEntity = seoClarityKeywordEntityDAO.getByKeyword(encodeKeywordName);
                    if (rankCheckKeywordEntity == null) {
                        log.info("====not find kw:" + keywordName + ",encodeKeywordName:" + encodeKeywordName);
                        continue;
                    }
                    int rankCheckId = rankCheckKeywordEntity.getId();

                    SvRetrieveStateEntity svRetrieveStateEntity = new SvRetrieveStateEntity();
                    svRetrieveStateEntity.setRefreshDate(FormatUtils.formatDateToYyyyMmDd(new Date()));
                    svRetrieveStateEntity.setKeywordType(cityId == 0 ? 1 : 2);
                    svRetrieveStateEntity.setCityId(cityId);
                    svRetrieveStateEntity.setLanguageId(languageId);
                    svRetrieveStateEntity.setKeywordId(rankCheckId);
                    svRetrieveStateEntity.setStatus(SvRetrieveStateEntity.KEYWORD_BAD_KEYWORD);
                    svRetrieveStateEntityList.add(svRetrieveStateEntity);
                    if (svRetrieveStateEntityList.size() >= 200) {
                        svRetrieveStateDAO.insertForBatch(svRetrieveStateEntityList);
                        svRetrieveStateEntityList.clear();
                    }
                }
            }
            if (!org.springframework.util.CollectionUtils.isEmpty(svRetrieveStateEntityList)) {
                svRetrieveStateDAO.insertForBatch(svRetrieveStateEntityList);
            }

            FileUtils.writeLines(invalidKwFile, invalidFileList, true);

            log.info("=========groupKeywordMap size:" + groupKeywordMap.size());
            File folder = new File(LOCAL_PATH + "groupSv" + LocalDate.now());
            if (!folder.exists()) {
                folder.mkdirs();
            }

            for (String groupKey : groupKeywordMap.keySet()) {
                log.info("=========groupKey :" + groupKey + ",size:" + groupKeywordMap.get(groupKey).size());
                File groupFile = new File(folder.getAbsolutePath() + File.separator + groupKey + ".csv");
                FileUtils.writeLines(groupFile, groupKeywordMap.get(groupKey), true);
            }

            bf.close();
            inputReader.close();

        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    private void processGroupKeywordFiles(String fullPathLocalFileName) throws Exception {

//            startNo = 148001;
//            endNo = 99999999;
        log.info("===========processing file:" + fullPathLocalFileName + ",st:" + startNo + ",end:" + endNo);
        File file = new File(fullPathLocalFileName);
        Thread.sleep(1000 * 60);
        File noSvFile = new File(LOCAL_PATH + "groupSvResult" + LocalDate.now() + "/noSvGroupKeywords" + LocalDate.now() + ".csv");

//            if (noSvFile.exists()) {
//                noSvFile.delete();
//            }

        long ctm = System.currentTimeMillis();
        resultFile = new File(LOCAL_PATH + "groupSvResult" + LocalDate.now() + "/" + file.getName());
        taskIdFile = new File(LOCAL_PATH + "groupSvResult" + LocalDate.now() + "/taskIdFile_" + ctm + ".txt");
        successedTaskIdFile = new File(LOCAL_PATH + "groupSvResult" + LocalDate.now() + "/successedTaskIdFile_" + ctm + ".txt");
        notFindTaskIdFile = new File(LOCAL_PATH + "groupSvResult" + LocalDate.now() + "/notFindTaskIdFile_" + ctm + ".txt");
        noResultTaskIdFile = new File(LOCAL_PATH + "groupSvResult" + LocalDate.now() + "/noResultTaskIdFile_" + ctm + ".txt");
        invalidKeywordFile = new File(LOCAL_PATH + "groupSvResult" + LocalDate.now() + "/invalidKeywordFile_" + ctm + ".txt");

        Set<String> invalidKeywordList = new HashSet<>();

        Map<Integer, List<String>> keywordLocationMap = new HashMap();
        try {

            InputStreamReader inputReader = new InputStreamReader(new FileInputStream(file));
            BufferedReader bf = new BufferedReader(inputReader);
            String line;
            int linNo = 0;
            int keywordCount = 0;
            while ((line = bf.readLine()) != null) {
                linNo++;

                if (endNo != 0 && (linNo < startNo || linNo > endNo)) {
                    continue;
                }

                try {

                    if (StringUtils.isBlank(line)) {
                        System.out.println("==SkipEmptyLine:" + linNo);
                        continue;
                    }

                    String[] arr = line.split(SPLIT_FIELD);//todo 注意分隔符！！！！！！！
//                    String[] arr = line.split(SPLIT_FIELD_COMMA);

                    int groupId = 0;
                    int criteriaId = 0;
                    int engineId = 0;
                    int languageId = 0;
                    int cityId = 0;
                    String keywordName = "";
                    if (!IS_CITY) {//national
                        groupId = Integer.parseInt(arr[0]);
//                        engineId = Integer.parseInt(arr[1]);
                        languageId = Integer.parseInt(arr[2]);
                        criteriaId = Integer.parseInt(arr[1]);
                        cityId = Integer.parseInt(arr[3]);
                        keywordName = arr[4];

//                        keywordName = keywordName.replaceAll("%C2%A0", "+");
//                        keywordName = URLDecoder.decode(keywordName, "utf-8");

                        //qc header
//                        criteriaId = Integer.parseInt(arr[0]);
//                        languageId = Integer.parseInt(arr[1]);
//                        cityId = Integer.parseInt(arr[2]);
//                        keywordName = arr[3];
//                        keywordName = keywordName.replaceAll("%C2%A0", "+");
//                        keywordName = URLDecoder.decode(keywordName, "utf-8");

//                        engineId = Integer.parseInt(arr[0]);
//                        languageId = Integer.parseInt(arr[1]);
//                        criteriaId = Integer.parseInt(arr[2]);
//                        keywordName = arr[3];
//                        keywordName = keywordName.replaceAll("%C2%A0", "+");
//                        keywordName = URLDecoder.decode(keywordName, "utf-8");

                    } else {//city
                        languageId = Integer.parseInt(arr[0]);
                        cityId = Integer.parseInt(arr[1]);
                        criteriaId = Integer.parseInt(arr[2]);
                        keywordName = arr[3];

                        keywordName = keywordName.replaceAll("%C2%A0", "+");
                        keywordName = URLDecoder.decode(keywordName, "utf-8");
                    }

                    if (criteriaId == 2643) {
                        log.error("skip russia kw:" + keywordName + ",linNo:" + linNo);
                        continue;
                    }
                    //                        log.info("===groupId:" + groupId + ",criteriaId:" + criteriaId);

                    String formatKeywordName = FormatUtils.checkKeyword(keywordName);
                    if (StringUtils.isBlank(formatKeywordName)) {
                        log.info("====skip invalid keywords:" + keywordName + ",linNo:" + linNo);
//                        invalidKeywordList.add(line);

//                        /**
//                         * 更新时打开 update svRetrieveState
                        String encodeKeywordName = FormatUtils.encodeKeyword(keywordName);
                        SeoClarityKeywordEntity rankCheckKeywordEntity = seoClarityKeywordEntityDAO.getByKeyword(encodeKeywordName);
                        if (rankCheckKeywordEntity == null) {
                            log.info("====notFindKw1:" + keywordName + ",encodeKeywordName:" + encodeKeywordName);
                            continue;
                        }
                        int rankCheckId = rankCheckKeywordEntity.getId();

                        List<SvRetrieveStateEntity> svRetrieveStateEntityList = new ArrayList<>();
                        SvRetrieveStateEntity svRetrieveStateEntity = new SvRetrieveStateEntity();
                        svRetrieveStateEntity.setRefreshDate(FormatUtils.formatDateToYyyyMmDd(new Date()));
                        svRetrieveStateEntity.setKeywordType(cityId == 0 ? 1 : 2);
                        svRetrieveStateEntity.setCityId(cityId);
                        svRetrieveStateEntity.setLanguageId(languageId);
                        svRetrieveStateEntity.setKeywordId(rankCheckId);
                        svRetrieveStateEntity.setStatus(SvRetrieveStateEntity.KEYWORD_BAD_KEYWORD);
                        svRetrieveStateEntityList.add(svRetrieveStateEntity);
                        svRetrieveStateDAO.insertForBatch(svRetrieveStateEntityList);
//                         */
                        continue;
                    }

                    String key = criteriaId + KEY_SPLIT + formatKeywordName;
                    //String encodeKw = URLEncoder.encode(keywordName, "utf-8");

                    if (keywordLocationMap.get(criteriaId) == null) {
                        List<String> keywordList = new ArrayList<>();
                        keywordList.add(formatKeywordName);
                        keywordLocationMap.put(criteriaId, keywordList);
                    } else {
                        List<String> keywordList = keywordLocationMap.get(criteriaId);
                        keywordList.add(formatKeywordName);
                        keywordLocationMap.put(criteriaId, keywordList);
                    }

                    keywordCount++;
                    if (keywordCount >= REQUEST_KEYWORD_COUNT) {
                        log.info("====startRetrieve1:" + linNo);
                        startRetrieve(keywordLocationMap);
                        keywordCount = 0;
                        keywordLocationMap = new HashMap<>();
                    }

                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("***** error line:" + linNo + "," + line);
                    errorLineList.add(line);
                    continue;
                }

            }

            if (keywordCount > 0) {
                log.info("====startRetrieve2:" + linNo);
                startRetrieve(keywordLocationMap);
            }

            bf.close();
            inputReader.close();

        } catch (Exception e) {
            e.printStackTrace();
        }


    }

    private void getSuccessedKwsByTaskIdFile(String filePath) throws Exception {

        long ctm = System.currentTimeMillis();
        resultFile = new File(LOCAL_PATH + "GetSvResult" + LocalDate.now() + "/resultSv_" + ctm + ".txt");
        taskIdFile = new File(LOCAL_PATH + "GetSvResult" + LocalDate.now() + "/taskIdFile_" + ctm + ".txt");
        successedTaskIdFile = new File(LOCAL_PATH + "GetSvResult" + LocalDate.now() + "/successedTaskIdFile_" + ctm + ".txt");
        notFindTaskIdFile = new File(LOCAL_PATH + "GetSvResult" + LocalDate.now() + "/notFindTaskIdFile_" + ctm + ".txt");
        noResultTaskIdFile = new File(LOCAL_PATH + "GetSvResult" + LocalDate.now() + "/noResultTaskIdFile_" + ctm + ".txt");
        invalidKeywordFile = new File(LOCAL_PATH + "GetSvResult" + LocalDate.now() + "/invalidKeywordFile_" + ctm + ".txt");
        noResultKeywordFile = new File(LOCAL_PATH + "GetSvResult" + LocalDate.now() + "/noResultKeywordFile_" + ctm + ".txt");

        File taskIdFile = new File(filePath);
        List<String> taskIdList = FileUtils.readLines(taskIdFile);
        log.info("====read taskIdFile:" + taskIdFile.getName() + ",taskIdList size:" + taskIdList.size());

        if (elFile != null && elFile.exists()) {
            getElFileMap();
        }

        for (String taskId : taskIdList) {
//            if(!taskId.equals("11280410-0351-0110-0000-49744b21c26f")){
//                continue;
//            }
            List<Result> responseSvList = getResult(taskId);
            if (CollectionUtils.isNotEmpty(responseSvList)) {
                System.out.println("===getResultSuc:" + taskId);
                List<String> successTaskIdList = new ArrayList<>();
                successTaskIdList.add(taskId);
                FileUtils.writeLines(successedTaskIdFile, successTaskIdList, true);
                writeResult(taskId, responseSvList);
            } else {
                log.info("===no result:" + taskId);
                List<String> noResultTaskIdList = new ArrayList<>();
                noResultTaskIdList.add(taskId);
                FileUtils.writeLines(noResultTaskIdFile, noResultTaskIdList, true);
            }
        }

    }


    private void successCountFile() throws Exception {

        File successFile = new File(LOCAL_PATH + "successFile.txt");
        List<String> successFileList = FileUtils.readLines(successFile);

        File[] files = new File(LOCAL_PATH + "groupSv2022-07-25").listFiles();
        int kwCnt = 0;
        int sucKwCnt = 0;
        for (File f : files) {
            if (successFileList.contains(f.getName())) {
                List<String> sucLineList = FileUtils.readLines(f);
                sucKwCnt += sucLineList.size();
                continue;
            }
            List<String> lineList = FileUtils.readLines(f);
            kwCnt += lineList.size();
        }
        log.info("======sucKwCnt:" + sucKwCnt);
        log.info("======kwCnt:" + kwCnt);
    }

    private void writeInvalidKw() {

        File outFile = new File("/home/<USER>/daily_us/uniqueInvalidKeywordFile.txt");
        Set<String> kwSet = new HashSet<>();
        File file = new File("/home/<USER>/daily_us/invalidKeywordFile.txt");
        try {
            InputStreamReader inputReader = new InputStreamReader(new FileInputStream(file));
            BufferedReader bf = new BufferedReader(inputReader);
            String line;
            int linNo = 0;
            while ((line = bf.readLine()) != null) {
                linNo++;
                kwSet.add(line);
            }
            bf.close();
            inputReader.close();
            log.info("==========kwSet:" + kwSet.size());
            FileUtils.writeLines(outFile, kwSet, true);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void getElFileMap() {

        try {
            InputStreamReader inputReader = new InputStreamReader(new FileInputStream(elFile));
            BufferedReader bf = new BufferedReader(inputReader);
            String line;
            int linNo = 0;
            while ((line = bf.readLine()) != null) {
                linNo++;
                if (StringUtils.isBlank(line)) {
                    System.out.println("==SkipEmptyLine:" + linNo);
                    continue;
                }
                String[] arr = line.split(SPLIT_FIELD);//todo 注意分隔符！！！！！！！
//                String[] arr = line.split(SPLIT_FIELD_COMMA);

                int engineId = Integer.parseInt(arr[0]);
                int languageId = Integer.parseInt(arr[1]);
                int criteriaId = Integer.parseInt(arr[2]);
                String keywordName = arr[3];
                String mapKey = criteriaId + KEY_SPLIT + keywordName;
                if (elFileMap.get(mapKey) == null) {
                    List<String> elList = new ArrayList<>();
                    elList.add(engineId + KEY_SPLIT + languageId);
                    elFileMap.put(criteriaId + KEY_SPLIT + keywordName, elList);
                } else {
                    List<String> elList = elFileMap.get(mapKey);
                    elList.add(engineId + KEY_SPLIT + languageId);
                    elFileMap.put(criteriaId + KEY_SPLIT + keywordName, elList);
                }
            }
            bf.close();
            inputReader.close();
            log.info("==========elFileMap:" + elFileMap.size());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
