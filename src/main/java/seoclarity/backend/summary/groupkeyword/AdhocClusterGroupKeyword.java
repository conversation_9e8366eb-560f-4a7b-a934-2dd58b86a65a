package seoclarity.backend.summary.groupkeyword;

import cn.hutool.core.date.StopWatch;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import seoclarity.backend.dao.actonia.adhoc.AutoAdhocRankProjectEntityDAO;
import seoclarity.backend.dao.clickhouse.adhoc.AdhocInfoDao;
import seoclarity.backend.dao.clickhouse.keywordcluster.KeywordClusterFinalDao;
import seoclarity.backend.dao.clickhouse.keywordcluster.KeywordClusterStage1Dao;
import seoclarity.backend.dao.clickhouse.keywordcluster.KeywordHashDao;
import seoclarity.backend.entity.CLRankingDetailEntity;
import seoclarity.backend.entity.actonia.adhoc.AutoAdhocRankProjectEntity;
import seoclarity.backend.entity.clickhouse.keywordcluster.KeywordClusterFinalEntity;
import seoclarity.backend.entity.clickhouse.keywordcluster.KeywordClusterStage1Entity;
import seoclarity.backend.entity.clickhouse.keywordcluster.KeywordHashEntity;
import seoclarity.backend.keywordexpand.utils.ExportDataUtil;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.JaccardIndexUtil;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.utils.ZeptoMailSenderComponent;
import seoclarity.backend.utils.cityhash.CityHashUtil;

import java.io.File;
import java.io.IOException;
import java.math.BigInteger;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * https://www.wrike.com/open.htm?id=1307615998
 */
@Slf4j
public class AdhocClusterGroupKeyword {

    private static final String CH_DB_RG_URL = "http://108.62.1.82:8123";
    private static final String CH_DB_RG = "keyword_cluster";
    private static final String USER = "default";
    private static final String PSW = "clarity99!";
    private static final String KW_SPLIT = "!_!";
    protected String emailTo = "<EMAIL>";
    private static final String[] bccTo = {"<EMAIL>", "<EMAIL>"};
    private static final Date currentDate = new Date();
    private static final int pageSize = 10000;
    private static final int processKwLimit = 1200000;
    private static final int threadCnt = Runtime.getRuntime().availableProcessors() * 2;

    public static final int CLUSTER_STATUS_NOT_START = 0;
    public static final int CLUSTER_STATUS_COLLECTING = 11;
    public static final int CLUSTER_STATUS_COLLECTING_SUCCESS = 12;
    public static final int CLUSTER_STATUS_COLLECTING_FAIL = 13;
    public static final int CLUSTER_STATUS_JAC_CARD = 21;
    public static final int CLUSTER_STATUS_JAC_CARD_SUCCESS = 22;
    public static final int CLUSTER_STATUS_JAC_CARD_FAIL = 23;
    public static final int CLUSTER_STATUS_CLUSTER_KEYWORD_ARRAY = 31;
    public static final int CLUSTER_STATUS_CLUSTER_KEYWORD_ARRAY_SUCCESS = 32;
    public static final int CLUSTER_STATUS_CLUSTER_KEYWORD_ARRAY_FAIL = 33;
    public static final int CLUSTER_STATUS_SKIP_MANY_KEYWORD = 42;

    private String parentFilePath = "/home/<USER>/source/rade/tmp_file/";
    private boolean testFlag = true;
    private int processType = 1;
    private KeywordHashDao keywordHashDao;
    private KeywordClusterStage1Dao keywordClusterStage1Dao;
    private KeywordClusterFinalDao keywordClusterFinalDao;
    private AutoAdhocRankProjectEntityDAO autoAdhocRankProjectEntityDAO;
    private AdhocInfoDao adhocInfoDao;
    public ZeptoMailSenderComponent zeptoMailSenderComponent;

    public AdhocClusterGroupKeyword() {
        this.autoAdhocRankProjectEntityDAO = SpringBeanFactory.getBean("autoAdhocRankProjectEntityDAO");
        this.keywordHashDao = SpringBeanFactory.getBean("keywordHashDao");
        this.adhocInfoDao = SpringBeanFactory.getBean("adhocInfoDao");
        this.keywordClusterStage1Dao = SpringBeanFactory.getBean("keywordClusterStage1Dao");
        this.keywordClusterFinalDao = SpringBeanFactory.getBean("keywordClusterFinalDao");
        zeptoMailSenderComponent = SpringBeanFactory.getBean("zeptoMailSenderComponent");
    }

    public static void main(String[] args) {
        new AdhocClusterGroupKeyword().startProcess(args);
    }

    private void startProcess(String[] args) {
        if (args != null) {
            if (args.length > 0) {
                testFlag = Boolean.parseBoolean(args[0]);
            }
            if (args.length > 1) {
                processType = Integer.parseInt(args[1]);
            }
            if (args.length > 2) {
                parentFilePath = args[2];
            }
        }
        System.out.println("===paramInfo testFlag:" + testFlag + " processType:" + processType + " parentFilePath:" + parentFilePath);
        if (processType == 0) {
            try {
                process();
            } catch (Exception e) {
                String subject = "kwCluster - processError";
                String content = "process error <br> msg:" + e.getMessage();
                sendExceptionEmail(subject, content);
                e.printStackTrace();
            }
        } else if (processType == 1) {
            try {
                processSpecial();
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else if (processType == 4) {
            test();
        } else if (processType == 5) {
            try {
                processSpecial2();
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else if (processType == 50000 || processType == 200000 || processType == 500000 || processType == 1200000) {
            try {
                processSpecial3(processType);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
    }

    private void test() {
        String subject = "kwCluster - loadInfoStage1Error";
        String content = "testEmail";
        sendExceptionEmail(subject, content);
        System.out.println("test end");
    }

    private void processSpecial() throws Exception {
        System.out.println("===start process");
        File totalFile = new File("/home/<USER>/source/rade/tmp_file/group_keyword.txt");
        File outFile = new File("/home/<USER>/source/rade/tmp_file/result_keyword.txt");
        Map<String, List<String>> totalMap = new HashMap<>();

        System.out.println("===start read totalFile");
        List<String> totalFileList = FileUtils.readLines(totalFile);
        for (String source : totalFileList) {
            String[] arr = source.split("\t");
            String kw = arr[0].trim();
            String trim = arr[1].trim();
            /*trim = trim.replaceAll("\\[\'", "");
            trim = trim.replaceAll("\'\\]", "");
            trim = trim.replaceAll("\'", "");*/

            String[] hashArr = trim.split("!_!");
            totalMap.put(kw, Arrays.asList(hashArr));
        }
        totalFileList.clear();
        System.out.println("===info targetMapSize: totalMapSize:" + totalMap.size());

        System.out.println("===start matching");
        List<String> outFileList = new ArrayList<>();
        long start = System.currentTimeMillis();
        int index = 1;
        for (String keywordName : totalMap.keySet()) {
            long kwStart = System.currentTimeMillis();
            List<String> sourceHashList = totalMap.get(keywordName);
            HashSet<String> sourceTreeSet = new HashSet<>(sourceHashList);
            Set<String> resultKwSet = new TreeSet<>();
            for (String findKw : totalMap.keySet()) {
                if (!StringUtils.equalsIgnoreCase(keywordName, findKw)) {
                    HashSet<String> findTreeSet = new HashSet<>(totalMap.get(findKw));
                    double v = JaccardIndexUtil.jaccard_index(sourceTreeSet, findTreeSet);
                    if (v >= 0.6) {
                        resultKwSet.add(findKw);
                    }
                }
            }
            if (!resultKwSet.isEmpty()) {
                outFileList.add(String.join(KW_SPLIT, resultKwSet));
            }
            System.out.println("===index: " + index + " kw:" + keywordName + " time:" + (System.currentTimeMillis() - kwStart) / 1000 + "s");
            if (outFileList.size() > 100) {
                FileUtils.writeLines(outFile, outFileList, true);
                outFileList.clear();
            }
            index++;
        }
        long end = System.currentTimeMillis();
        System.out.println("===matchTime:" + (end - start) / 1000 + "s");
        if (!outFileList.isEmpty()) {
            FileUtils.writeLines(outFile, outFileList, true);
            outFileList.clear();
        }
    }

    private void exportCdbToFile(String filePath, String sql) {
        System.out.println("==exportSql:" + sql);
        try {
            ExportDataUtil.httpExportFromClarityDB(CH_DB_RG_URL, CH_DB_RG, USER, PSW, sql, filePath, true, false, new String[]{""});
            System.out.println("====expoert ok");
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
    }

    private void process() {
        List<AutoAdhocRankProjectEntity> projectList = null;
        if (!testFlag) {
            projectList = autoAdhocRankProjectEntityDAO.getAdHocClusterGroupKeywordProject();
        } else {
            projectList = Collections.singletonList(genTestProject());
        }
        if (CollectionUtils.isEmpty(projectList)) {
            log.info("====notProjectToRun!");
            return;
        }
        System.out.println("====projectInfo size:" + projectList.size());
        for (AutoAdhocRankProjectEntity project : projectList) {
            long start = System.currentTimeMillis();
            int projectId = project.getId();
            int domainId = project.getOwnDomainId();
            int searchEngineId = project.getSearchEngineId();
            int languageId = project.getLanguageId();
            boolean isMobile = project.getDevice() == 2;
            int kwCnt = 0;
            Integer clusterStatus = 0;
            if (project.getKewordClusterStatus() != null) {
                clusterStatus = project.getKewordClusterStatus();
            }
            if (clusterStatus == AdhocClusterGroupKeyword.CLUSTER_STATUS_SKIP_MANY_KEYWORD) {
                log.info("===skipProjectByManyKws projectId:{} domainId:{} SEId:{}", projectId, domainId, searchEngineId + "_" + languageId + "_" + (isMobile ? "m" : "d"));
                continue;
            }
            int infoKwCnt = adhocInfoDao.getForKeywordClusterInfoCount(isMobile, projectId, searchEngineId, languageId);
            if (infoKwCnt > processKwLimit) {
                log.info("====overLimit -> kwCnt:{} projectId:{} domainId:{} SEId:{}", infoKwCnt, projectId, domainId, searchEngineId + "_" + languageId + "_" + (isMobile ? "m" : "d"));
                if (clusterStatus == AdhocClusterGroupKeyword.CLUSTER_STATUS_NOT_START) {
                    String subject = "kwCluster - kw number exceeds 1.2 million";
                    String content = "kwCnt:" + infoKwCnt + " projectId:" + projectId + " oid:" + domainId + " SEId:" + searchEngineId + "_" + languageId + "_" + (isMobile ? "m" : "d");
                    sendAlertEmail(subject, content);
                    autoAdhocRankProjectEntityDAO.updateKwClusterStatus(projectId, AdhocClusterGroupKeyword.CLUSTER_STATUS_SKIP_MANY_KEYWORD, new Date());
                }
                continue;
            }

            List<CLRankingDetailEntity> keywordListTmp = adhocInfoDao.getForKeywordCluster(isMobile, projectId, domainId, searchEngineId, languageId, testFlag, 1, 0);
            if (keywordListTmp.isEmpty()) {
                log.info("====notKeywordToRun->projectId:" + projectId + " domainId:" + domainId + " searchEngineId:" + searchEngineId + " languageId:" + languageId);
                if (clusterStatus == AdhocClusterGroupKeyword.CLUSTER_STATUS_NOT_START) {
                    String subject = "kwCluster - kw number is 0";
                    String content = "projectId:" + projectId + " oid:" + domainId + " SEId:" + searchEngineId + "_" + languageId + "_" + (isMobile ? "m" : "d");
                    sendAlertEmail(subject, content);
                    autoAdhocRankProjectEntityDAO.updateKwClusterStatus(projectId, AdhocClusterGroupKeyword.CLUSTER_STATUS_COLLECTING_FAIL, new Date());
                }
                continue;
            }
            CLRankingDetailEntity clRankingDetailEntity = keywordListTmp.get(0);
            String rankDate = clRankingDetailEntity.getRankingDate();

            if (clusterStatus == AdhocClusterGroupKeyword.CLUSTER_STATUS_NOT_START/* || clusterStatus == AdhocClusterGroupKeyword.CLUSTER_STATUS_COLLECTING_FAIL*/) {
                try {
                    autoAdhocRankProjectEntityDAO.updateKwClusterStatus(projectId, AdhocClusterGroupKeyword.CLUSTER_STATUS_COLLECTING, new Date());
                    List<KeywordHashEntity> insertList = new ArrayList<>();
                    int totalCount = adhocInfoDao.getForKeywordClusterCount(isMobile, projectId, domainId, searchEngineId, languageId);
                    if (totalCount <= 0) {
                        log.info("====notKeywordToRun->projectId:" + projectId + " domainId:" + domainId + " searchEngineId:" + searchEngineId + " languageId:" + languageId);
                        autoAdhocRankProjectEntityDAO.updateKwClusterStatus(projectId, AdhocClusterGroupKeyword.CLUSTER_STATUS_COLLECTING_FAIL, new Date());
                        continue;
                    }
                    int totalPage = (int) Math.ceil((double) totalCount / pageSize);
                    System.out.println("====keywordInfo size:" + totalCount + " totalPage:" + totalPage + " projectId:" + projectId + " domainId:" + domainId + " searchEngineId:" + searchEngineId + " languageId:" + languageId);
                    System.out.println("==start load data......");
                    for (int page = 1; page <= totalPage; page++) {
                        int offset = (page - 1) * pageSize;
                        List<CLRankingDetailEntity> keywordList = adhocInfoDao.getForKeywordCluster(isMobile, projectId, domainId, searchEngineId, languageId, testFlag, pageSize, offset);

                        Map<String, List<CLRankingDetailEntity>> keywordGroup = keywordList.stream().collect(Collectors.groupingBy(CLRankingDetailEntity::getKeywordName));
                        for (String keyword : keywordGroup.keySet()) {
                            List<CLRankingDetailEntity> keywrodRankList = keywordGroup.get(keyword);
                            List<String> urlArray = keywrodRankList.stream().map(CLRankingDetailEntity::getUrl).collect(Collectors.toList());
                            KeywordHashEntity keywordHash = generateKeywordHashEntity(rankDate, domainId, projectId, keyword, urlArray);
                            insertList.add(keywordHash);
                            if (insertList.size() > 20000) {
                                keywordHashDao.batchAdd(insertList);
                                kwCnt += insertList.size();
                                insertList.clear();
                            }
                        }
                        try {
                            TimeUnit.MILLISECONDS.sleep(150);
                        } catch (InterruptedException e) {
                            System.out.println("==sleep error");
                        }
                        if (testFlag) {
                            if (page > 49) {
                                break;
                            }
                        }
                    }
                    if (!insertList.isEmpty()) {
                        keywordHashDao.batchAdd(insertList);
                        kwCnt += insertList.size();
                        insertList.clear();
                    }
                    log.info("===collectKwInfo infoKwCnt:{} detailKwCnt:{} detailCnt:{} projectId:{} oid:{} SEId:{} cost:{}s", infoKwCnt, kwCnt, totalCount, projectId, domainId, searchEngineId + "_" + languageId + "_" + (isMobile ? "m" : "d"), ((System.currentTimeMillis() - start) / 1000) + "s");


                    autoAdhocRankProjectEntityDAO.updateKwClusterStatus(projectId, AdhocClusterGroupKeyword.CLUSTER_STATUS_COLLECTING_SUCCESS, new Date());
                    try {
                        loadInfoStage1(projectId, domainId, rankDate);
                    } catch (Exception e) {
                        System.out.println("====loadInfoStage1 error projectId:" + projectId);
                        String subject = "kwCluster - loadInfoStage1Error";
                        String content = "kwCnt:" + infoKwCnt + "projectId:" + projectId + " oid:" + domainId + " SEId:" + searchEngineId + "_" + languageId + "_" + (isMobile ? "m" : "d") + "<br>" + " error:" + e.getMessage();
                        sendExceptionEmail(subject, content);
                        autoAdhocRankProjectEntityDAO.updateKwClusterStatus(projectId, AdhocClusterGroupKeyword.CLUSTER_STATUS_JAC_CARD_FAIL, new Date());
                        e.printStackTrace();
                    }
                } catch (Exception e) {
                    System.out.println("===collectKwError projectId:" + projectId);
                    String subject = "kwCluster - collectKwError";
                    String content = "kwCnt:" + infoKwCnt + "projectId:" + projectId + " oid:" + domainId + " SEId:" + searchEngineId + "_" + languageId + "_" + (isMobile ? "m" : "d") + "<br>" + " error:" + e.getMessage();
                    sendExceptionEmail(subject, content);
                    autoAdhocRankProjectEntityDAO.updateKwClusterStatus(projectId, AdhocClusterGroupKeyword.CLUSTER_STATUS_COLLECTING_FAIL, new Date());
                    e.printStackTrace();
                }
            }/* else if (clusterStatus == AdhocClusterGroupKeyword.CLUSTER_STATUS_JAC_CARD_FAIL) {
                try {
                    loadInfoStage1(projectId, domainId, rankDate);
                } catch (Exception e) {
                    System.out.println("====loadInfoStage1 error projectId:" + projectId);
                    String subject = "kwCluster - loadInfoStage1Error";
                    String content = "kwCnt:" + infoKwCnt + "projectId:" + projectId + " oid:" + domainId + " SEId:" + searchEngineId + "_" + languageId + "_" + (isMobile ? "m" : "d") + "<br>" + " error:" + e.getMessage();
                    sendExceptionEmail(subject, content);
                    autoAdhocRankProjectEntityDAO.updateKwClusterStatus(projectId, AdhocClusterGroupKeyword.CLUSTER_STATUS_JAC_CARD_FAIL, new Date());
                    e.printStackTrace();
                }
            } else if (clusterStatus == AdhocClusterGroupKeyword.CLUSTER_STATUS_CLUSTER_KEYWORD_ARRAY_FAIL) {
                try {
                    processMerger(projectId, domainId, rankDate);
                } catch (Exception e) {
                    System.out.println("===processMerger error projectId:" + projectId + " ownDomainId:" + domainId);
                    String subject = "kwCluster - processMergerError";
                    String content = "kwCnt:" + infoKwCnt + "projectId:" + projectId + " oid:" + domainId + " SEId:" + searchEngineId + "_" + languageId + "_" + (isMobile ? "m" : "d") + "<br>" + " error:" + e.getMessage();
                    sendExceptionEmail(subject, content);
                    autoAdhocRankProjectEntityDAO.updateKwClusterStatus(projectId, AdhocClusterGroupKeyword.CLUSTER_STATUS_CLUSTER_KEYWORD_ARRAY_FAIL, new Date());
                    e.printStackTrace();
                }
            }*/
            else {
                log.info("===statusErrorNeedCheck projectId:{} SEId:{} status:{}", projectId, searchEngineId + "_" + languageId + "_" + (isMobile ? "m" : "d"), clusterStatus);
                String subject = "kwCluster - statusError - projectId:" + projectId;
                String content = "status:" + clusterStatus + " kwCnt:" + infoKwCnt + " projectId:" + projectId + " oid:" + domainId + " SEId:" + searchEngineId + "_" + languageId + "_" + (isMobile ? "m" : "d");
                sendExceptionEmail(subject, content);
            }
        }
    }

    private void sendAlertEmail(String emailSubject, String msg) {
        Map<String, Object> reportMap = new HashMap<>();
        reportMap.put("userName", "ALL");
        reportMap.put("errMsg", "&nbsp;&nbsp;&nbsp;&nbsp;" + msg + ".<br> time:" + FormatUtils.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss"));
        zeptoMailSenderComponent.sendMimeMultiPartZeptoMailAndBccByFunctionType(emailTo, bccTo, emailSubject, "mail_alert_missing_bot_files.txt", "mail_alert_missing_bot_files.html",
                reportMap, null, ZeptoMailSenderComponent.FUNCTION_TYPE_BACKEND_INTERNAL, null, null);
    }

    private void sendExceptionEmail(String emailSubject, String msg) {
        Map<String, Object> reportMap = new HashMap<>();
        reportMap.put("userName", "ALL");
        reportMap.put("errMsg",  msg + ".<br> time:" + FormatUtils.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss"));
        zeptoMailSenderComponent.sendMimeMultiPartZeptoMailAndBccByFunctionType(emailTo, null, emailSubject, "mail_alert_missing_bot_files.txt", "mail_alert_missing_bot_files.html",
                reportMap, null, ZeptoMailSenderComponent.FUNCTION_TYPE_BACKEND_INTERNAL, null, null);
    }


    private void processSpecial2() throws Exception {
        File totalFile = new File("/home/<USER>/source/rade/tmp_file/group_keyword.txt");
        File outFile = new File("/home/<USER>/source/rade/tmp_file/result_keyword_1200000.txt");
        Map<String, HashSet<String>> totalMap = new HashMap<>();
        System.out.println("===start read totalFile");
        List<String> totalFileList = FileUtils.readLines(totalFile);
        for (String source : totalFileList) {
            String[] arr = source.split("\t");
            String kw = arr[0].trim();
            String trim = arr[1].trim();
            trim = trim.replaceAll("\\[\'", "");
            trim = trim.replaceAll("\'\\]", "");
            trim = trim.replaceAll("\'", "");

            // split trim to get hashSet
            final String[] hashArr = trim.split(",");
            final HashSet<String> hashSet = new HashSet<>();
            Collections.addAll(hashSet, hashArr);
            totalMap.put(kw, hashSet);
        }
        totalFileList.clear();
        System.out.println("===info targetMapSize: totalMapSize:" + totalMap.size());

        System.out.println("===start matching");
        long start = System.currentTimeMillis();
        System.out.println("===start process" + FormatUtils.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss"));
        totalMap.keySet().parallelStream().forEach(keywordName -> {
            List<String> outFileList = new ArrayList<>();
            long kwStart = System.currentTimeMillis();
            HashSet<String> sourceTreeSet = totalMap.get(keywordName);
            Set<String> resultKwSet = new ConcurrentSkipListSet<>();

            totalMap.keySet().parallelStream()
                    .filter(findKw -> !StringUtils.equalsIgnoreCase(keywordName, findKw))
                    .forEach(findKw -> {
                        HashSet<String> findTreeSet = totalMap.get(findKw);
                        double v = JaccardIndexUtil.jaccard_index(sourceTreeSet, findTreeSet);
                        if (v >= 0.6) {
                            resultKwSet.add(findKw);
                        }
                    });

            if (!resultKwSet.isEmpty()) {
                outFileList.add(String.join(KW_SPLIT, resultKwSet));
            }
            long end = System.currentTimeMillis();
            System.out.println("===matchTime:" + (end - kwStart) / 1000 + "s");
            if (!outFileList.isEmpty()) {
                try {
                    FileUtils.writeLines(outFile, outFileList, true);
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
                outFileList.clear();
            }
        });

    }

    private void processSpecial3(int processType) throws Exception {
        System.out.println("===start process");
        File totalFile = new File("/home/<USER>/source/rade/tmp_file/group_keyword_" + processType + ".txt");
        File outFile = new File("/home/<USER>/source/rade/tmp_file/result_keyword_" + processType + ".txt");
        System.out.println("===start matching");
        System.out.println("===start read totalFile");
        long start = System.currentTimeMillis();
        List<String> totalFileList = FileUtils.readLines(totalFile);
        List<Keyword> targetKeywords = new ArrayList<>(totalFileList.size());
        final ArrayList<Keyword> totalList = new ArrayList<>(totalFileList.size());
        for (String source : totalFileList) {
            String[] arr = source.split("\t");
            String kw = arr[0].trim();
            String trim = arr[1].trim();

            // split trim to get hashSet
            final String[] hashArr = trim.split(KW_SPLIT);
            final HashSet<String> hashSet = new HashSet<>();
            Collections.addAll(hashSet, hashArr);
            final Keyword keyword = new Keyword();
            keyword.name = kw.trim();
            keyword.hash = hashSet;
            totalList.add(keyword);
            final Keyword keyword1 = new Keyword();
            keyword1.name = kw.trim();
            keyword1.hash = hashSet;
            targetKeywords.add(keyword1);
        }
        totalFileList.clear();

        System.out.println("===info Size:" + totalList.size());
        int bound = totalList.size();
        for (int i = 0; i < bound; i++) {
            long kwStart = System.currentTimeMillis();
            final List<String> outFileList = new ArrayList<>();
            Set<String> resultSet = new ConcurrentSkipListSet<>();
            final Keyword keyword = totalList.get(i);
            targetKeywords.remove(0);
            // split two subList and calculate jaccard index
            targetKeywords.parallelStream()
                    .forEach(k -> {
                        final double v = JaccardIndexUtil.jaccard_index(keyword.hash, k.hash);
                        if (v >= 0.6) {
                            resultSet.add(k.name);
                        }
                    });
            if (!resultSet.isEmpty()) {
                resultSet.add(keyword.name);
                outFileList.add(String.join(KW_SPLIT, resultSet));
            }
            long end = System.currentTimeMillis();
            System.out.println("===index:" + i + " kw: '" + keyword.name + "' time:" + (end - kwStart) + "ms");
            if (!outFileList.isEmpty()) {
                try {
                    FileUtils.writeLines(outFile, outFileList, true);
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
                outFileList.clear();
            }
            resultSet.clear();
        }

        long end = System.currentTimeMillis();
        System.out.println("===total time:" + (end - start) / 1000 + "s");
    }

    /*
    // bk code 0304 before version(old parallelStream)
    private void loadInfoStage1(int projectId, int ownDomainId, String ranking_date) throws Exception {
        autoAdhocRankProjectEntityDAO.updateKwClusterStatus(projectId, AdhocClusterGroupKeyword.CLUSTER_STATUS_JAC_CARD, new Date());
        String sourceFilePath = getFilePath(false, 1, projectId, null);
        System.out.println("===start process loadInfoStage1 sourceFilePath:" + sourceFilePath);
        String sql = "select keyword_name, arrayStringConcat(urlSHA512Hash, '!_!') from dis_keyword_hash where project_id = " + projectId;
        exportCdbToFile(sourceFilePath, sql);
        File totalFile = new File(sourceFilePath);

        String outFilePath = getFilePath(false, 2, projectId, null);
        System.out.println("===outFilePath:" + outFilePath);
        File outFile = new File(outFilePath);
        System.out.println("===start matching");
        long start = System.currentTimeMillis();
        List<String> totalFileList = FileUtils.readLines(totalFile);
        List<Keyword> targetKeywords = new ArrayList<>(totalFileList.size());
        final ArrayList<Keyword> totalList = new ArrayList<>(totalFileList.size());
        for (String source : totalFileList) {
            String[] arr = source.split("\t");
            String kw = arr[0].trim();
            String trim = arr[1].trim();

            // split trim to get hashSet
            final String[] hashArr = trim.split(KW_SPLIT);
            final HashSet<String> hashSet = new HashSet<>();
            Collections.addAll(hashSet, hashArr);
            final Keyword keyword = new Keyword();
            keyword.name = kw.trim();
            keyword.hash = hashSet;
            totalList.add(keyword);
            final Keyword keyword1 = new Keyword();
            keyword1.name = kw.trim();
            keyword1.hash = hashSet;
            targetKeywords.add(keyword1);
        }
        totalFileList.clear();

        System.out.println("===info Size:" + totalList.size());
        int bound = totalList.size();
        for (int i = 0; i < bound; i++) {
            long kwStart = System.currentTimeMillis();
            final List<String> outFileList = new ArrayList<>();
            Set<String> resultSet = new ConcurrentSkipListSet<>();
            final Keyword keyword = totalList.get(i);
            targetKeywords.remove(0);
            // split two subList and calculate jaccard index
            targetKeywords.parallelStream()
                    .forEach(k -> {
                        final double v = JaccardIndexUtil.jaccard_index(keyword.hash, k.hash);
                        if (v >= 0.6) {
                            resultSet.add(k.name);
                        }
                    });
            if (!resultSet.isEmpty()) {
                resultSet.add(keyword.name);
                outFileList.add(String.join(KW_SPLIT, resultSet));
            }
            long end = System.currentTimeMillis();
            System.out.println("===index:" + i + " kw: '" + keyword.name + "' time:" + (end - kwStart) + "ms");
            if (!outFileList.isEmpty()) {
                try {
                    FileUtils.writeLines(outFile, outFileList, true);
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
                outFileList.clear();
            }
            resultSet.clear();
        }

        long end = System.currentTimeMillis();
        System.out.println("===total time:" + (end - start) / 1000 + "s");

        System.out.println("===startLoadDataToStage1");
        List<KeywordClusterStage1Entity> stageList = new ArrayList<>();
        List<String> contentList = FileUtils.readLines(outFile);
        for (String string : contentList) {
            String[] split = string.split(KW_SPLIT);
            List<String> keywordArray = new ArrayList<>();
            for (String str : split) {
                keywordArray.add(str.trim());
            }
            keywordArray = keywordArray.stream().distinct().sorted().collect(Collectors.toList());
            KeywordClusterStage1Entity stage1Entity = generateKeywordStage1Entity(ranking_date, ownDomainId, projectId, keywordArray);
            stageList.add(stage1Entity);
            if (stageList.size() >= pageSize) {
                keywordClusterStage1Dao.batchAdd(stageList);
                stageList.clear();
            }
        }
        if (!stageList.isEmpty()) {
            keywordClusterStage1Dao.batchAdd(stageList);
            stageList.clear();
        }
        autoAdhocRankProjectEntityDAO.updateKwClusterStatus(projectId, AdhocClusterGroupKeyword.CLUSTER_STATUS_JAC_CARD_SUCCESS, new Date());
        System.out.println("===loadInfoStage1 end");
        try {
            processMerger(projectId, ownDomainId, ranking_date);
        } catch (Exception e) {
            autoAdhocRankProjectEntityDAO.updateKwClusterStatus(projectId, AdhocClusterGroupKeyword.CLUSTER_STATUS_CLUSTER_KEYWORD_ARRAY_FAIL, new Date());
            System.out.println("===processMerger error projectId:" + projectId + " ownDomainId:" + ownDomainId);
            e.printStackTrace();
        }
    }*/

    private void loadInfoStage1(int projectId, int ownDomainId, String ranking_date) throws Exception {
        autoAdhocRankProjectEntityDAO.updateKwClusterStatus(projectId, AdhocClusterGroupKeyword.CLUSTER_STATUS_JAC_CARD, new Date());
        String sourceFilePath = getFilePath(false, 1, projectId, null);
        System.out.println("===start process loadInfoStage1 sourceFilePath:" + sourceFilePath);
        String sql = "select keyword_name, arrayStringConcat(urlSHA512Hash, '!_!') from dis_keyword_hash where project_id = " + projectId;
        exportCdbToFile(sourceFilePath, sql);
        File totalFile = new File(sourceFilePath);


        System.out.println("===start matching");
        long start = System.currentTimeMillis();
        List<String> totalFileList = FileUtils.readLines(totalFile);
        final ArrayList<Keyword> totalList = new ArrayList<>(totalFileList.size());
        for (String source : totalFileList) {
            String[] arr = source.split("\t");
            String kw = arr[0].trim();
            String trim = arr[1].trim();

            // split trim to get hashSet
            final String[] hashArr = trim.split(KW_SPLIT);
            final HashSet<String> hashSet = new HashSet<>();
            Collections.addAll(hashSet, hashArr);
            final Keyword keyword = new Keyword();
            keyword.name = kw.trim();
            keyword.hash = hashSet;
            totalList.add(keyword);
        }
        totalFileList.clear();


        // 线程分片处理
        List<String> threadFilePaths = new ArrayList<>();
        int totalSize = totalList.size();
        System.out.println("===info totalFileList:" + totalSize);
        int totalPages = (int) Math.ceil((double) totalSize / threadCnt);
        List<CopyOnWriteArrayList<AdhocClusterGroupKeyword.Keyword>> threadSubList = splitList(totalList, totalPages);
        ExecutorService executorService = Executors.newFixedThreadPool(threadCnt);

        List<Future<?>> futures = new ArrayList<>();
        try {
            for (int i = 0; i < threadSubList.size(); i++) {
                CopyOnWriteArrayList<AdhocClusterGroupKeyword.Keyword> keywords = threadSubList.get(i);
                int taskId = i + 1;
                String threadOutFilePath = getFilePath(true, 0, projectId, taskId);

                Future<?> submit = executorService.submit(new Stage1Thread(keywords, taskId, threadOutFilePath));
                futures.add(submit);

                threadFilePaths.add(threadOutFilePath);
            }
            // 等待所有任务完成
            for (Future<?> future : futures) {
                future.get();
            }
        } catch (InterruptedException | ExecutionException e) {
            throw new RuntimeException(e);
        } finally {
            executorService.shutdown();
        }
        System.out.println("===threadProcessInfo time:" + (System.currentTimeMillis() - start) / 1000 + "s");

        // 合并线程结果
        String mergerThreadFilePath = getFilePath(false, 5, projectId, null);
        File mergerThreadResFile = new File(mergerThreadFilePath);
        System.out.println("===mergerThreadResInfo projectId:" + projectId + " filePath:" + mergerThreadFilePath);
        for (String filePath : threadFilePaths) {
            File threadFile = new File(filePath);
            FileUtils.writeLines(mergerThreadResFile, FileUtils.readLines(threadFile), true);
            threadFile.deleteOnExit();
        }

        // 缓存kw -> kwList
        List<String> resultThreadList = FileUtils.readLines(mergerThreadResFile);
        Map<String, ConcurrentSkipListSet<Kw>> kwPKwListMap = new ConcurrentHashMap<>();
        for (String string : resultThreadList) {
            String[] split = string.split(KW_SPLIT);
            ConcurrentSkipListSet<Kw> kwSet = new ConcurrentSkipListSet<>();
            for (String s : split) {
                kwSet.add(new Kw(s));
            }
            for (String s : split) {
                kwPKwListMap.put(s, kwSet);
            }
        }
        resultThreadList.clear();

        int groupSize = threadSubList.size();
        System.out.println("===groupSizeInfo projectId:" + projectId + " groupSize:" + groupSize);
        long jacCardStart = System.currentTimeMillis();
        for (int i = 0; i < groupSize - 1; i++) {
            if (i >= groupSize - 1) {
                break;
            }
            CopyOnWriteArrayList<Keyword> keywords = threadSubList.get(i);
            log.info("outerIndex: {}", i);
            for (int j = i + 1; j <= groupSize - 1; j++) {
                CopyOnWriteArrayList<AdhocClusterGroupKeyword.Keyword> targetKeywords = threadSubList.get(j);
                log.info("innerIndex: {}", j);
                keywords.parallelStream().forEach(keyword ->  {
                    ConcurrentSkipListSet<Kw> outerKwMapSet = kwPKwListMap.getOrDefault(keyword.name, new ConcurrentSkipListSet<>());
                    targetKeywords.forEach(targetKeyword -> {
                        if (!outerKwMapSet.isEmpty() && outerKwMapSet.contains(new Kw(targetKeyword.name))) {
                            return;
                        }
                        final double v = JaccardIndexUtil.jaccard_index(keyword.hash, targetKeyword.hash);
                        if (v >= 0.6) {
                            Set<Kw> strings = kwPKwListMap.getOrDefault(targetKeyword.name, new ConcurrentSkipListSet<>());
                            strings.addAll(outerKwMapSet);
                            final Optional<String> existOpt = strings.stream().map(Kw::getName).filter(kwPKwListMap::containsKey).findAny();
                            if (existOpt.isPresent()) {
                                // update existing set and keyword
                                // get all keywords value to merge
                                strings.stream().map(kwPKwListMap::get).filter(Objects::nonNull).forEach(outerKwMapSet::addAll);
                            }
                            outerKwMapSet.add(new Kw(keyword.name));
                            outerKwMapSet.add(new Kw(targetKeyword.name));


                            outerKwMapSet.forEach(kw -> {
                                kwPKwListMap.put(kw.name, outerKwMapSet);
                            });
                        }
                    });
                });
            }
        }

        final List<Set<Kw>> collect = new ArrayList<>(kwPKwListMap.values());
        final List<String> result = collect.stream().map(s -> s.stream().map(Kw::getName).collect(Collectors.joining(KW_SPLIT))).distinct().collect(Collectors.toList());
        String outFilePath = getFilePath(false, 2, projectId, null);
        System.out.println("===outFilePath:" + outFilePath);
        File outFile = new File(outFilePath);
        FileUtils.writeLines(outFile, result);
        System.out.println("===jacCardInfo useTime:" + (System.currentTimeMillis() - jacCardStart) / 1000 / 60 + "m");
        System.out.println("===startLoadDataToStage1");
        List<KeywordClusterStage1Entity> stageList = new ArrayList<>();
        List<String> contentList = FileUtils.readLines(outFile);
        for (String string : contentList) {
            String[] split = string.split(KW_SPLIT);
            List<String> keywordArray = new ArrayList<>();
            for (String str : split) {
                keywordArray.add(str.trim());
            }
            keywordArray = keywordArray.stream().distinct().sorted().collect(Collectors.toList());
            KeywordClusterStage1Entity stage1Entity = generateKeywordStage1Entity(ranking_date, ownDomainId, projectId, keywordArray);
            stageList.add(stage1Entity);
            if (stageList.size() >= pageSize) {
                keywordClusterStage1Dao.batchAdd(stageList);
                stageList.clear();
            }
        }
        if (!stageList.isEmpty()) {
            keywordClusterStage1Dao.batchAdd(stageList);
            stageList.clear();
        }
        autoAdhocRankProjectEntityDAO.updateKwClusterStatus(projectId, AdhocClusterGroupKeyword.CLUSTER_STATUS_JAC_CARD_SUCCESS, new Date());
        System.out.println("===loadInfoStage1 end");
        try {
            processMerger(projectId, ownDomainId, ranking_date);
        } catch (Exception e) {
            System.out.println("===processMerger error projectId:" + projectId + " ownDomainId:" + ownDomainId);
            String subject = "kwCluster - processMergerError";
            String content = "projectId:" + projectId + " oid:" + ownDomainId + "<br>" + " error:" + e.getMessage();
            sendExceptionEmail(subject, content);
            autoAdhocRankProjectEntityDAO.updateKwClusterStatus(projectId, AdhocClusterGroupKeyword.CLUSTER_STATUS_CLUSTER_KEYWORD_ARRAY_FAIL, new Date());
            e.printStackTrace();
        }
    }

    private int mergerList(List<String> sourceList, String filePath, int projectId) throws Exception {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("getList");
        List<Set<String>> sets = sourceList.parallelStream()
                .map(row -> new HashSet<>(Arrays.asList(row.split(KW_SPLIT))))
                .collect(Collectors.toList());
        stopWatch.stop();
        int beforeSize = sets.size();
        stopWatch.start("mergerList");
        Map<String, Set<String>> invertedIndex = new HashMap<>();
        for (final String line : sourceList) {
            String[] keywords = line.split(KW_SPLIT);
            final Set<String> keywordSet = new HashSet<>(Arrays.asList(keywords));
            final Optional<String> existOpt = keywordSet.stream().filter(invertedIndex::containsKey).findAny();
            if (existOpt.isPresent()) {
                // update existing set and keyword
                // get all keywords value to merge
                Arrays.stream(keywords).map(invertedIndex::get).filter(Objects::nonNull).forEach(keywordSet::addAll);
            }
            // just add keyword and line to invertedIndex
            keywordSet.forEach(keyword -> {
                invertedIndex.put(keyword, keywordSet);
            });
        }

        final List<Set<String>> collect = new ArrayList<>(invertedIndex.values());

        stopWatch.stop();
        System.out.println("cost: " + stopWatch.prettyPrint());
        final List<String> result = collect.stream().map(s -> String.join(KW_SPLIT, s)).distinct().collect(Collectors.toList());
        int afterSize = result.size();

        System.out.println("====mergerSize:" + afterSize + " beforeSize:" + beforeSize + " projectId:" + projectId);
        FileUtils.writeLines(new File(filePath), result);
        return afterSize;
    }

    private void processMerger(int projectId, int ownDomainId, String rankingDate) throws Exception {
        autoAdhocRankProjectEntityDAO.updateKwClusterStatus(projectId, AdhocClusterGroupKeyword.CLUSTER_STATUS_CLUSTER_KEYWORD_ARRAY, new Date());
        String exportSql = "select arrayStringConcat(keywordArray, '!_!') from dis_keyword_clustering_stage1 where project_id = " + projectId;
        String stageExtractFilePath = getFilePath(false, 3, projectId, null);
        String afterProcessFilePath = getFilePath(false, 4, projectId, null);
        System.out.println("===stageExtractFilePath:" + stageExtractFilePath);
        exportCdbToFile(stageExtractFilePath, exportSql);

        List<String> replaceList = FileUtils.readLines(new File(stageExtractFilePath));
        FileUtils.writeLines(new File(afterProcessFilePath), replaceList);
        Set<Integer> resultRowSet = new HashSet<>();
        int resSize = 0;
        long start = System.currentTimeMillis();
        while (true) {
            int size = mergerList(replaceList, afterProcessFilePath, projectId);
            if (resultRowSet.contains(size)) {
                resSize = size;
                break;
            } else {
                resultRowSet.add(size);
            }
        }
        System.out.println("====mergerResultSize:" + resSize + " useTime:" + (System.currentTimeMillis() - start) / 1000 + "s. projectId:" + projectId + " ownDomainId:" + ownDomainId + " rankingDate:" + rankingDate);
        System.out.println("===startLoadIntoDB");
        List<KeywordClusterFinalEntity> finalList = new ArrayList<>();
        List<String> contentList = FileUtils.readLines(new File(afterProcessFilePath));
        for (String string : contentList) {
            String[] split = string.split(KW_SPLIT);
            List<String> keywordArray = new ArrayList<>();
            for (String str : split) {
                keywordArray.add(str.trim());
            }
            keywordArray = keywordArray.stream().distinct().sorted().collect(Collectors.toList());
            KeywordClusterFinalEntity finalEntity = generateKeywordFinalEntity(rankingDate, ownDomainId, projectId, keywordArray);
            finalList.add(finalEntity);
            if (finalList.size() >= pageSize) {
                keywordClusterFinalDao.batchAdd(finalList);
                finalList.clear();
            }
        }
        if (!finalList.isEmpty()) {
            keywordClusterFinalDao.batchAdd(finalList);
            finalList.clear();
        }
        autoAdhocRankProjectEntityDAO.updateKwClusterStatus(projectId, AdhocClusterGroupKeyword.CLUSTER_STATUS_CLUSTER_KEYWORD_ARRAY_SUCCESS, new Date());
        System.out.println("===loadInfoStage1 end");
    }

    private AutoAdhocRankProjectEntity genTestProject() {
        AutoAdhocRankProjectEntity project = new AutoAdhocRankProjectEntity();
        project.setId(1994);
        project.setOwnDomainId(8756);
        project.setSearchEngineId(3);
        project.setLanguageId(3);
        project.setDevice(2);
        project.setCreateDate(20231012);
        return project;
    }

    private KeywordHashEntity generateKeywordHashEntity(String rankDateStr, int ownDomainId, int projectId, String keywordName, List<String> urlArray) {
        KeywordHashEntity keywordHash = new KeywordHashEntity();
        keywordHash.setRankingDate(rankDateStr);
        keywordHash.setOwnDomainId(ownDomainId);
        keywordHash.setProjectId(projectId);
        keywordHash.setKeywordName(keywordName);
        keywordHash.setUrlArray(urlArray);
        keywordHash.setSign(1);
        return keywordHash;
    }
    private KeywordClusterStage1Entity generateKeywordStage1Entity(String rankDateStr, int ownDomainId, int projectId, List<String> keywordArray) {
        KeywordClusterStage1Entity keywordHash = new KeywordClusterStage1Entity();
        keywordHash.setRankingDate(rankDateStr);
        keywordHash.setOwnDomainId(ownDomainId);
        keywordHash.setProjectId(projectId);
        keywordHash.setKeywordArray(keywordArray);
        return keywordHash;
    }

    private KeywordClusterFinalEntity generateKeywordFinalEntity(String rankDateStr, int ownDomainId, int projectId, List<String> keywordArray) {
        KeywordClusterFinalEntity keywordHash = new KeywordClusterFinalEntity();
        keywordHash.setRankingDate(rankDateStr);
        keywordHash.setOwnDomainId(ownDomainId);
        keywordHash.setProjectId(projectId);
        keywordHash.setKeywordArray(keywordArray);
        List<BigInteger> integerList = keywordArray.stream().map(kw -> {
            BigInteger hash = CityHashUtil.getUrlHashForBigIntegerLowercase(kw);
            return hash;
        }).collect(Collectors.toList());
        keywordHash.setKeywordHashArray(integerList);
        return keywordHash;
    }

    public <T> List<CopyOnWriteArrayList<T>> splitList(List<T> originalList, int batchSize) {
        List<CopyOnWriteArrayList<T>> batches = new ArrayList<>();
        int fromIndex = 0;

        while (fromIndex < originalList.size()) {
            int toIndex = Math.min(fromIndex + batchSize, originalList.size());
            List<T> sublist = originalList.subList(fromIndex, toIndex);
            batches.add(new CopyOnWriteArrayList<>(sublist));
            fromIndex = toIndex;
        }

        return batches;
    }

    /**
     * step1: keyword_hash -> filePath
     * step2: filePath -> keyword_hash
     * @param isThread
     * @param step
     * @return
     */
    private String getFilePath(boolean isThread, int step, int projectId, Integer taskId) {
        String filePath = "";
        String dateStr = FormatUtils.formatDate(currentDate, "yyyyMMdd");

        File parentFolder = new File(parentFilePath + dateStr);
        if (!parentFolder.exists()) {
            parentFolder.mkdirs();
        }

        File file = new File(parentFolder.getAbsolutePath() + "/thread_folder");
        if (!file.exists()) {
            file.mkdirs();
        }

        String timeStr = FormatUtils.formatDate(new Date(), "yyyyMMddHHmmss");
        if (isThread) {
            filePath = parentFilePath + dateStr + "/thread_folder/" + projectId + "_" + taskId + "_stage1" + "_res_" + timeStr + ".txt";
        } else {
            switch (step) {
                case 1: // export form dis_keyword_hash
                    filePath = parentFilePath + dateStr + "/" + projectId  + "_kwHash" + "_source_" + timeStr + ".txt";
                    break;
                case 2:
                    filePath = parentFilePath + dateStr + "/" + projectId + "_kw_cluster_stage1_source_" + timeStr + ".txt";
                    break;
                case 3:
                    filePath = parentFilePath + dateStr + "/" + projectId + "_kw_cluster_final_source_" + timeStr + ".txt";
                    break;
                case 4:
                    filePath = parentFilePath + dateStr + "/" + projectId + "_kw_cluster_final_" + timeStr + ".txt";
                    break;
                case 5:
                    filePath = parentFilePath + dateStr + "/" + projectId + "_thread_res_merger_" + timeStr + ".txt";
                    break;
            }
        }
        return filePath;
    }

    public static class Keyword {
        public String name;
        public HashSet<String> hash;
    }

    @Data
    @EqualsAndHashCode
    @AllArgsConstructor
    public static class Kw implements Comparable<Kw> {
        public String name;

        @Override
        public int compareTo(@NotNull Kw o) {
            return this.name.compareTo(o.name);
        }
    }
}
