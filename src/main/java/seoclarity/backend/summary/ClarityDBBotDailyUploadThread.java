package seoclarity.backend.summary;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.google.gson.Gson;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.jboss.netty.util.NetUtil;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.actonia.bot.BotDirectoryDao;
import seoclarity.backend.dao.actonia.bot.HostVerificationDAO;
import seoclarity.backend.dao.clickhouse.bot.BotDetailDao;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.actonia.bot.HostVerificationEntity;
import seoclarity.backend.entity.clickhouse.bot.BotDetailEntity;
import seoclarity.backend.entity.clickhouse.bot.BotJsonVO;
import seoclarity.backend.service.CommonDataService;
import seoclarity.backend.utils.*;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.*;

/**
 * Created by Leo on 2017/2/27.
 * rm -f ./src/main/java/seoclarity/backend/summary/ClarityDBBotDailyUploadThread.java && vi ./src/main/java/seoclarity/backend/summary/ClarityDBBotDailyUploadThread.java
 *
 * mvn exec:java -Dexec.mainClass="seoclarity.backend.summary.ClarityDBBotDailyUpload" -Dexec.cleanupDaemonThreads=false -Dexec.args="2017-08-01"
 */
public class ClarityDBBotDailyUploadThread implements Runnable {

    private OwnDomainEntityDAO ownDomainEntityDAO;
    private BotDirectoryDao botDirectoryDao;
    private HostVerificationDAO hostVerificationDAO;
    private BotDetailDao botDetailDao;

    private File processFile;
    private String processFileName;
    private Date date;

    private Gson gson = new Gson();

    private static final Integer MAX_INSERT_NUM = 10 * 10000;
    private static final String DEFAULT_IP = "0.0.0.0";

    private Map<Integer, String> pointToDomainMap = new HashMap<>();

    public ClarityDBBotDailyUploadThread(OwnDomainEntityDAO ownDomainEntityDAO, BotDirectoryDao botDirectoryDao,
                                         HostVerificationDAO hostVerificationDAO, BotDetailDao botDetailDao,
                                         File processFile, Date date) {
        this.ownDomainEntityDAO = ownDomainEntityDAO;
        this.botDirectoryDao = botDirectoryDao;
        this.hostVerificationDAO = hostVerificationDAO;
        this.botDetailDao = botDetailDao;

        // https://www.wrike.com/open.htm?id=1203550577 -> 12286 need special process, only process when file name contains nl-nl. so need this filed.
        this.processFileName = processFile.getName();

        this.processFile = processFile;
        this.date = date;

        //Leo - https://www.wrike.com/open.htm?id=211327052
//        pointToDomainMap.put(4757, "551");
        pointToDomainMap.put(561, "1180");
        pointToDomainMap.put(4762, "4769");
        pointToDomainMap.put(4764, "6373");

        // https://www.wrike.com/open.htm?id=1207120311
        pointToDomainMap.put(9281, "9282");
        pointToDomainMap.put(9283, "9284");
//        pointToDomainMap.put(9309, "9334,9335");
        pointToDomainMap.put(9311, "9312,9313");
        pointToDomainMap.put(9290, "9291,9292,9293");
    }

    private boolean handleJsonVO(Date logDate, List<BotJsonVO> botJsonVOs) {

        List<BotDetailEntity> detailEntities = new ArrayList<>(20 * 10000);
        boolean insertResult = false;
        System.out.println("botJsonVOs size is : "+botJsonVOs.size());
        long startTime = System.currentTimeMillis();
        for (BotJsonVO botJsonVO : botJsonVOs) {
            try {
                processSingleLine(botJsonVO, logDate, detailEntities);
            } catch (Exception e) {
                e.printStackTrace();
                System.out.println("Error line :"+ gson.toJson(botJsonVO));
            }
            if (detailEntities.size() >= MAX_INSERT_NUM) {
                botDetailDao.insertForBatch(detailEntities, logDate);
                ClarityDBBotDailyUploadThreadMain.loadCount += detailEntities.size();
                System.out.println("Insert into ClarityDB with size : "+detailEntities.size());
                detailEntities.clear();
                insertResult = true;
            }
        }

        if (CollectionUtils.isNotEmpty(detailEntities)) {
            botDetailDao.insertForBatch(detailEntities, logDate);
            ClarityDBBotDailyUploadThreadMain.loadCount += detailEntities.size();
            System.out.println("Last Insert into ClarityDB with size : "+detailEntities.size());
            detailEntities.clear();
            insertResult = true;
        }

        System.out.println("process all lines "+botJsonVOs.size()+" time : "+ (System.currentTimeMillis() - startTime) / (1000) + "s, insertResult: "+insertResult);
        return insertResult;
    }

    private String userAgentKey;
    private Integer agentId;
    private String userAgentKeyForPoint;
    private BotDetailEntity botDetailEntity;
    private void processSingleLine(BotJsonVO botJsonVO,
                                   Date logDate,
                                   List<BotDetailEntity> detailEntities) {
        BotJsonVO.BotDataVO dataVO = botJsonVO.getData();
        if(null == dataVO.getOriginIP()) {
            dataVO.setOriginIP(DEFAULT_IP);
        }
        OwnDomainEntity domainEntity;
        Integer domainId = dataVO.getDomainId();
        if (null != domainId && domainId != 0) {
            if (ClarityDBBotDailyUploadThreadMain.domainTargetIdMap.containsKey(domainId)) {
                domainEntity = ClarityDBBotDailyUploadThreadMain.domainTargetIdMap.get(domainId);
            } else {
                domainEntity = ownDomainEntityDAO.getOwnDomainEntityByOwnDomainId(domainId);
                if(null == domainEntity) {
                    System.out.println("can't find active domain ID :"+domainId);
                    return;
                }
                ClarityDBBotDailyUploadThreadMain.domainTargetIdMap.put(domainId, domainEntity);
            }
        } else {
            String domain = dataVO.getReqHost();
            if (ClarityDBBotDailyUploadThreadMain.domainMap.containsKey(domain)) {
                domainEntity = ClarityDBBotDailyUploadThreadMain.domainMap.get(domain);
            } else {
                if (ClarityDBBotDailyUploadThreadMain.emptyDomainList.contains(domain)) {
                    return;
                }
                domainEntity = ownDomainEntityDAO.getOwnDomainEntityByOwnDomainName(domain);
                if(null == domainEntity) {
                    System.out.println("can't find active domain :"+domain);
                    ClarityDBBotDailyUploadThreadMain.emptyDomainList.add(domain);
                    return;
                }
                ClarityDBBotDailyUploadThreadMain.domainMap.put(domain, domainEntity);
                System.out.println("=======ImplicitMatchOID: oid:" + domainEntity.getId() + " domain:" + domain);
            }
        }
        // https://www.wrike.com/open.htm?id=1371776378
        if (5157 == domainEntity.getId()) {
            if (StringUtils.containsIgnoreCase(dataVO.getReqPath(),"/www.flights.com")) {
                String reqPath = dataVO.getReqPath();
                reqPath = reqPath.replaceAll("/www.flights.com", "");
                dataVO.setReqPath(reqPath);
            }
        }

        String url = getUrlFromDataVO(domainEntity, dataVO);

        // https://www.wrike.com/open.htm?id=1207120311
        if (ClarityDBBotDailyUploadThreadMain.urlPointDomainMap.containsKey(domainEntity.getId())) {
            Integer oldDomainId = domainEntity.getId();
            Map<String, OwnDomainEntity> urlPOidMap = ClarityDBBotDailyUploadThreadMain.urlPointDomainMap.get(oldDomainId);
            for (String urlKey : urlPOidMap.keySet()) {
                if (StringUtils.containsIgnoreCase(url, urlKey)) {
                    domainEntity = urlPOidMap.get(urlKey);
                }
            }
            // if matched domain id is equals oldDomainId need skip.
            if ((domainEntity.getId() == oldDomainId) && domainEntity.getId() != 8644) {
                return;
            }
        }

        //TODO this is hard code.
        if(domainEntity.getId() == 3458 && !BotUtils.isBotAgent(dataVO.getUserAgentStr())) {
            System.out.println("for 3458 hard code skip not BOT data.");
            return;
        }

        userAgentKey = domainEntity.getId() +"!_!"+ dataVO.getUserAgentStr();
        if (CommonDataService.getDirectoryEntities().containsKey(userAgentKey)) {
            agentId = CommonDataService.getDirectoryEntities().get(userAgentKey);
        } else {
            agentId = botDirectoryDao.getUserAgentIdByUserAgent(dataVO.getUserAgentStr(), domainEntity.getId());
            //System.out.println("checking userAgent from Mysql for userAgent : "+dataVO.getUserAgentStr());
            if (null == agentId || agentId <= 0) {
                agentId = botDirectoryDao.insertForEntity(dataVO.getUserAgentStr(), domainEntity.getId());
                if (agentId <= 0) {
                    System.out.println("insert for agent ERROR.");
                    return;
                }
            }
            CommonDataService.getDirectoryEntities().put(userAgentKey, agentId);
        }

        //Check if it is a real bot ip
        if(!ClarityDBBotDailyUploadThreadMain.verificationSyncIPList.contains(dataVO.getOriginIP()) && !dataVO.getOriginIP().equalsIgnoreCase(DEFAULT_IP)
                // TODO this is hard code. Rade - https://www.wrike.com/open.htm?id=1232633396
                && domainId != 4609) {
            HostVerificationEntity verificationEntity = hostVerificationDAO.getByIP(dataVO.getOriginIP());
            if(null == verificationEntity || verificationEntity.getIp() == null) {
                System.out.println("checking IP  : "+dataVO.getOriginIP());
                int botType = 0;
                int isBot = 0;
                if (BotUtils.isGoogleBot(dataVO.getUserAgentStr())) {
                    isBot = verificationIP(dataVO.getOriginIP(), BotUtils.googleHostRegEx);
                    botType = BotUtils.GOOGLE_TYPE;
                } else if (BotUtils.isBaiduBot(dataVO.getUserAgentStr())) {
                    isBot = verificationIP(dataVO.getOriginIP(), BotUtils.baiduHostRegEx);
                    botType = BotUtils.BAIDU_TYPE;
                } else if (BotUtils.isBingBot(dataVO.getUserAgentStr())) {
                    isBot = verificationIP(dataVO.getOriginIP(), BotUtils.bingHostRegEx);
                    botType = BotUtils.BING_TYPE;
                } else if (BotUtils.isYandexBot(dataVO.getUserAgentStr())) {
                    isBot = verificationIP(dataVO.getOriginIP(), BotUtils.yandexHostRegEx);
                    botType = BotUtils.YANDEX_TYPE;
                } else if (BotUtils.isYahooBot(dataVO.getUserAgentStr())) {
                    isBot = verificationIP(dataVO.getOriginIP(), BotUtils.yahooHostRegEx);
                    botType = BotUtils.YAHOO_TYPE;
                }
                if (botType != 0) {
                    System.out.println("is bot : "+(isBot == 1 ? "truly" : "fake")+" bot.");
                    try {
                        hostVerificationDAO.insertForEntity(dataVO.getOriginIP(), botType, isBot);
                    } catch (Exception e) {
                        e.printStackTrace();
                        System.out.println("IP exist in database : "+dataVO.getOriginIP());
                    }
                }
            }
            ClarityDBBotDailyUploadThreadMain.verificationSyncIPList.add(dataVO.getOriginIP());
        }

        if (pointToDomainMap.containsKey(domainEntity.getId())) {
            String[] pointOidArr = pointToDomainMap.get(domainEntity.getId()).split(",");
            for (String pointOidStr : pointOidArr) {
                int pointedOid = Integer.parseInt(pointOidStr.trim());
                userAgentKeyForPoint = pointedOid +"!_!"+ dataVO.getUserAgentStr();
                if (!CommonDataService.getDirectoryEntities().containsKey(userAgentKeyForPoint)) {
                    Integer agentIdPointed = botDirectoryDao.getUserAgentIdByUserAgent(dataVO.getUserAgentStr(), pointedOid);
                    System.out.println("checking userAgentKeyForPoint  : "+dataVO.getUserAgentStr());
                    if (null == agentIdPointed || agentIdPointed <= 0) {
                        agentIdPointed = botDirectoryDao.insertForEntity(dataVO.getUserAgentStr(), pointedOid);
                        if (agentIdPointed <= 0) {
                            System.out.println("insert for agent ERROR. pointedOid:" + pointedOid + ", userAgent:" + dataVO.getUserAgentStr());
                        }
                    }
                    CommonDataService.getDirectoryEntities().put(userAgentKeyForPoint, agentIdPointed);
                }
            }
        }

        Date date;
        Date recordLogDate;
        if (StringUtils.isNotEmpty(dataVO.getDate())) {
            date = FormatUtils.toDate(dataVO.getDate(), "yyyy-MM-dd HH:mm:ss");
            recordLogDate = FormatUtils.toDate(dataVO.getDate(), "yyyy-MM-dd HH:mm:ss");
        } else {
            //Leo - https://www.wrike.com/open.htm?id=186725979
            String timestamp = dataVO.getTimestamp();
            timestamp = StringUtils.replace(timestamp, ".", "");
            long times = NumberUtils.toLong(timestamp);
            if (timestamp.length() == 10) {
                times = times * 1000;
            }
            date = new Date(times);
            if (null == dataVO.getDisableTimeZone() || !dataVO.getDisableTimeZone()) {
                date = FormatUtils.changeTimeZone(date, "GMT");
            }
            recordLogDate = date;
        }


        /**new detail table*/
        botDetailEntity = new BotDetailEntity();
        botDetailEntity.setOwnDomainId(domainEntity.getId());
        //https://www.wrike.com/open.htm?id=1103632509
        //don't need to set full ua for expedia!!
//        if (ClarityDBBotDailyUploadThreadMain.expediaDomainIdSet.contains(domainEntity.getId())) {
//            botDetailEntity.setUseragent(dataVO.getUserAgentStr());
//        } else {
//            botDetailEntity.setUseragent(dataVO.getUserAgent());
//        }
        botDetailEntity.setUseragent(dataVO.getUserAgent());
        botDetailEntity.setUa_group_id(dataVO.getUaGroupId());
        botDetailEntity.setIpAddress(dataVO.getOriginIP());
        botDetailEntity.setResponseCode(NumberUtils.toInt(dataVO.getStatus(), 0));
        botDetailEntity.setUrl(url);
        //ewain 20200924 修改botDate获取方式
        //如果 date来源只能使用文件中的时间，则下列代码使用recordLogDate
        if (dataVO.isUseFileContextDate()) {
            botDetailEntity.setBotDate(recordLogDate);
        }else {
            botDetailEntity.setBotDate(logDate);
        }
        botDetailEntity.setTimestamp(date);
        Integer version = dataVO.getVersion();
        if(version == null && ClarityDBBotDailyUploadThreadMain.SAVE_VERSION != null && ClarityDBBotDailyUploadThreadMain.SAVE_VERSION > 0) {
            botDetailEntity.setVersioning(ClarityDBBotDailyUploadThreadMain.SAVE_VERSION);
        } else {
            botDetailEntity.setVersioning(version);
        }
        detailEntities.add(botDetailEntity);


        // https://www.wrike.com/open.htm?id=168812196
        // copy one domain data to another domain
        if (pointToDomainMap.containsKey(domainEntity.getId())) {
            String[] pointOidArr = pointToDomainMap.get(domainEntity.getId()).split(",");
            for (String pointOidStr : pointOidArr) {
                int pointedOid = Integer.parseInt(pointOidStr.trim());
                Integer pointedOidAgentId = CommonDataService.getDirectoryEntities().get(userAgentKeyForPoint);
                if (pointedOidAgentId == null || pointedOidAgentId == 0) {
                    System.out.println("Not found agentId for pointedOid:" + pointedOid + ", userAgent:" + dataVO.getUserAgentStr());
                } else {
                    BotDetailEntity botDetail = genBotDetailEntity(pointedOid, url, dataVO, logDate, date);
                    detailEntities.add(botDetail);
                }
            }
        }
    }

    private BotDetailEntity genBotDetailEntity(int oid, String url, BotJsonVO.BotDataVO dataVO, Date logDate, Date date) {
        botDetailEntity = new BotDetailEntity();
        botDetailEntity.setOwnDomainId(oid);
        botDetailEntity.setIpAddress(dataVO.getOriginIP());
        botDetailEntity.setResponseCode(NumberUtils.toInt(dataVO.getStatus(), 0));
        botDetailEntity.setUseragent(dataVO.getUserAgent());
        botDetailEntity.setUa_group_id(dataVO.getUaGroupId());
        botDetailEntity.setUrl(url);
        botDetailEntity.setBotDate(logDate);
        botDetailEntity.setTimestamp(date);
        botDetailEntity.setVersioning(dataVO.getVersion());
        return botDetailEntity;
    }

    private void insertOrUpdateHost(BotJsonVO.BotDataVO dataVO) {
        int botType = 0;
        int isBot = 0;
        if (BotUtils.isGoogleBot(dataVO.getUserAgentStr())) {
            isBot = verificationIP(dataVO.getOriginIP(), BotUtils.googleHostRegEx);
            botType = BotUtils.GOOGLE_TYPE;
        } else if (BotUtils.isBaiduBot(dataVO.getUserAgentStr())) {
            isBot = verificationIP(dataVO.getOriginIP(), BotUtils.baiduHostRegEx);
            botType = BotUtils.BAIDU_TYPE;
        } else if (BotUtils.isBingBot(dataVO.getUserAgentStr())) {
            isBot = verificationIP(dataVO.getOriginIP(), BotUtils.bingHostRegEx);
            botType = BotUtils.BING_TYPE;
        } else if (BotUtils.isYandexBot(dataVO.getUserAgentStr())) {
            isBot = verificationIP(dataVO.getOriginIP(), BotUtils.yandexHostRegEx);
            botType = BotUtils.YANDEX_TYPE;
        } else if (BotUtils.isYahooBot(dataVO.getUserAgentStr())) {
            isBot = verificationIP(dataVO.getOriginIP(), BotUtils.yahooHostRegEx);
            botType = BotUtils.YAHOO_TYPE;
        }

        HostVerificationEntity verificationEntity = hostVerificationDAO.getByIP(dataVO.getOriginIP());
        if(null == verificationEntity || verificationEntity.getIp() == null) {
            //System.out.println("checking IP  : "+dataVO.getOriginIP());
            if (botType != 0) {
                //System.out.println("is bot : "+(isBot == 1 ? "truly" : "fake")+" bot.");
                try {
                    hostVerificationDAO.insertForEntity(dataVO.getOriginIP(), botType, isBot);
                    System.out.println("insert new bot ip " + verificationEntity.getIp() + ", isbot flag: " + isBot + ", type: " + botType);
                } catch (Exception e) {
                    e.printStackTrace();
                    System.out.println("IP exist in database : "+ dataVO.getOriginIP());
                }
            }
        }else {
            if (botType != 0 && (botType != verificationEntity.getType().intValue() || isBot != verificationEntity.getIsbot().intValue())) {
                try {
                    hostVerificationDAO.update(verificationEntity.getIp(), isBot, botType);
                    System.out.println("update bot ip " + verificationEntity.getIp() + " to bot flag: " + isBot + ", botType: "  + botType);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

//    private int verificationIP(String ipAddress, String hostRegex) {
//        String hostName;
//        try {
//            hostName = FormatUtils.getHostName(ipAddress);
//        } catch (UnknownHostException e) {
//            e.printStackTrace();
//            return 1;
//        }
//        int result = FormatUtils.validateRegularEx(hostName, hostRegex) ? 1 : 0;
//        //System.out.println("checking IP: "+ipAddress+", got host : "+hostName + ", result: " + result);
//        return result;
//    }

    private int verificationIP(String ipAddress, String hostRegex) {
        byte[] ipByteArr = NetUtil.createByteArrayFromIpAddressString(ipAddress);
        InetAddress addr;
        try {
            addr = InetAddress.getByAddress(ipByteArr);
        } catch (UnknownHostException e) {
            e.printStackTrace();
            return 0;
        }
        String hostName = addr.getHostName();
        System.out.println(hostName);
        int result = FormatUtils.validateRegularEx(hostName, hostRegex) ? 1 : 0;
        System.out.println("checking IP: "+ipAddress+", got host : "+hostName + ", result: " + result);
        return result;
    }


    private String getUrlFromDataVO(OwnDomainEntity domainEntity, BotJsonVO.BotDataVO dataVO) {
        if(StringUtils.isNotBlank(dataVO.getReqProtocol()) && StringUtils.isNotBlank(dataVO.getReqPort()) && dataVO.getReqPort().contains(":")) {
            String protocol = StringUtils.removeEndIgnoreCase(dataVO.getReqProtocol(), "://");
            return protocol+"://"+ dataVO.getReqHost() + dataVO.getReqPort() + StringUtils.trimToEmpty(dataVO.getReqPath());
        } else if (StringUtils.isNotBlank(dataVO.getReqProtocol()) && StringUtils.isBlank(dataVO.getReqPort())){
            String protocol = StringUtils.removeEndIgnoreCase(dataVO.getReqProtocol(), "://");
            return protocol+"://"+ dataVO.getReqHost() + StringUtils.trimToEmpty(dataVO.getReqPath());
        } else if (StringUtils.isBlank(dataVO.getReqProtocol()) && StringUtils.isNotBlank(dataVO.getReqPort()) && dataVO.getReqPort().contains(":")) {
            return ((domainEntity.getProtocol() == null || domainEntity.getProtocol() == 0) ? "http://" : "https://" ) + dataVO.getReqHost() + dataVO.getReqPort() + StringUtils.trimToEmpty(dataVO.getReqPath());
        } else {
            return ((domainEntity.getProtocol() == null || domainEntity.getProtocol() == 0) ? "http://" : "https://" ) + dataVO.getReqHost() + StringUtils.trimToEmpty(dataVO.getReqPath());
        }
    }

    private List<BotJsonVO> getEntityFromJsonFile(File jsonFile) throws IOException {
        List<BotJsonVO> botJsonVOs = new ArrayList<>();
        List<String> lines;
        try (FileInputStream inputStream = new FileInputStream(jsonFile)) {
            lines = IOUtils.readLines(inputStream);
        } catch (Exception e) {
            System.out.println("File not found.");
            return null;
        }
        System.out.println("start load data " + jsonFile.getAbsolutePath() + " to clarityDB, file totalCount: " + lines.size());
        //System.out.println("==============================totalCount:" + lines.size()+" for file :"+jsonFile.getAbsolutePath());
        for (int i = 0, length = lines.size(); i < length; i++) {
            try {
                BotJsonVO botJsonVO;
                if (StrUtil.startWith(lines.get(i), "[")) {
                    JSONArray jsonArray = JSONUtil.parseArray(lines.get(i));
                    for (JSONObject jsonObject : jsonArray.jsonIter()) {
                        botJsonVO = jsonObject.toBean(BotJsonVO.class);
                        String userAgent = botJsonVO.getData().getUserAgentStr();

                        //https://www.wrike.com/open.htm?id=745245250
                        int uaGroupId = BotUtils.getUaGroupIdByUA(userAgent);
                        if (uaGroupId < 0) {
                            continue;
                        }
                        //https://www.wrike.com/open.htm?id=1135983604
                        String uri = botJsonVO.getData().getReqPath();
                        if (UrlFilterUtil.shouldSkipUrl(uri)) {
                            continue;
                        }
                        botJsonVO.getData().setUaGroupId(uaGroupId);
                        botJsonVOs.add(botJsonVO);
                    }
                } else {
                    botJsonVO = gson.fromJson(lines.get(i), BotJsonVO.class);
                    String userAgent = botJsonVO.getData().getUserAgentStr();

                    //https://www.wrike.com/open.htm?id=745245250
                    int uaGroupId = BotUtils.getUaGroupIdByUA(userAgent);
                    if (uaGroupId < 0) {
                        continue;
                    }
                    // https://www.wrike.com/open.htm?id=1135983604
                    String uri = botJsonVO.getData().getReqPath();
                    if (UrlFilterUtil.shouldSkipUrl(uri)) {
                        continue;
                    }
                    botJsonVO.getData().setUaGroupId(uaGroupId);

                    botJsonVOs.add(botJsonVO);
                }
            } catch (Exception e) {
                e.printStackTrace();
                System.out.println("Error for line :"+i);
            }
        }
        ClarityDBBotDailyUploadThreadMain.dataCount += botJsonVOs.size();
        return botJsonVOs;
    }

    private File unZipFile(File file) throws Exception {
        byte[] in = FileUtils.getBytesFromFile(file);
        File jsonFile = new File(file.getAbsolutePath().replaceAll(".gz", ""));
        if (jsonFile.exists()) {
            jsonFile.delete();
            System.out.println("File exist : "+jsonFile.getAbsolutePath());
        }
        System.out.println("File AbsolutePath: " + file.getAbsolutePath());
        System.out.println("File AbsolutePath: " + jsonFile.getAbsolutePath());
        return GZipUtil.unGzipBigFile(in, jsonFile);

//        byte[] out = GZipUtil.unzip(in);
//        return FileUtils.getFileFromBytes(out, jsonFile.getAbsolutePath());
    }

    @Override
    public void run() {
        try {
            //Leo - to handle unzip file logic
            File jsonFile;
            if(StringUtils.endsWithIgnoreCase(processFile.getName(), ".gz")) {
//                jsonFile = new File(processFile.getAbsolutePath().replaceAll(".gz", ""));
                ClarityDBBotDailyUploadThreadMain.zipFileSize+=processFile.length();
                jsonFile = unZipFile(processFile);
            } else {
                jsonFile = processFile;
            }
            ClarityDBBotDailyUploadThreadMain.unzippedFileSize+=jsonFile.length();
            //System.out.println("=====>start process json file in thread classssssssssssssss!");
            handleJsonVO(date, getEntityFromJsonFile(jsonFile));
            System.out.println("process json file " + jsonFile.getName() + " completed!");
            File completedFile = new File(jsonFile.getAbsolutePath() + "_completed");
            jsonFile.renameTo(completedFile);
            GZipUtil.createZip(jsonFile.getAbsolutePath() + "_completed", jsonFile.getAbsolutePath() + "_completed.gzip");
            completedFile.delete();
            processFile.delete();
        } catch (Exception e) {
            e.printStackTrace();

        }
    }
}
