package seoclarity.backend.summary;

import ch.ethz.ssh2.Connection;
import ch.ethz.ssh2.SCPClient;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import com.google.gson.Gson;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.filefilter.AbstractFileFilter;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.clickhouse.bot.BotQueryDetailDao;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.export.vo.ExpediaExtractVo;
import seoclarity.backend.utils.BotUtils;
import seoclarity.backend.utils.FTPUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.utils.loggly.LogglyUtils;
import seoclarity.backend.utils.loggly.LogglyVO;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

@CommonsLog
public class ExpediaBotSundayDataExtract {

    private static final Integer NOT_INCLUDE_OID = 4769;
    private BotQueryDetailDao botQueryDetailDao;
    private OwnDomainEntityDAO ownDomainEntityDAO;

    private static final int SSH_TRY_COUNT = 5;

    private String companyName = "expedia";
    private Date sDate;
    private Date eDate;
    private final Integer PAGE_SIZE = 15 * 10000;
    CSVFormat csvFullFormat = CSVFormat.DEFAULT.withHeader("Domain", "Country", "Week Begin Date", "Search Engine", "User Agent", "IP Address", "Spoofed", "URL", "Bot Requests", "Avg Requests", "Last Crawl Date", "2xx", "3xx", "4xx", "5xx", "device")
            .withDelimiter(',');

    private String fullFilePath = "/home/<USER>/4765/expedia_bot_sunday";
    private String fileName = "%d-%s-%s-BotClarity.txt";

    private static final String FTP_SERVER = "expediaftp.seoclarity.net";
    private static final String FTP_SERVER_USER = "expedia";
    private static final String FTP_SERVER_PW = "AmesEu";
    private static final String FTP_SERVER_PATH = "/Bot_Clarity_Extracts";

    private static final List<String> dateList2 = Arrays.asList("19-05-24","12-05-24","5-05-24","28-04-24","21-04-24","14-04-24","7-04-24","31-03-24","24-03-24","17-03-24","10-03-24","3-03-24","25-02-24","18-02-24","11-02-24","4-02-24","28-01-24","21-01-24","14-01-24","7-01-24","31-12-23","24-12-23","17-12-23","10-12-23");
    private static final List<String> dateList = Arrays.asList("3-12-23","26-11-23","19-11-23","12-11-23","5-11-23","29-10-23","22-10-23","15-10-23","1-10-23","24-09-23","17-09-23","10-09-23","3-09-23","27-08-23","20-08-23","13-08-23","6-08-23","30-07-23","23-07-23","16-07-23","9-07-23","2-07-23","25-06-23","18-06-23","11-06-23","4-06-23","28-05-23","14-05-23","7-05-23");


    private  static LogglyVO logglyVO = new LogglyVO();
    private static int totalCnt = 0;

    public ExpediaBotSundayDataExtract(Date sDate, Date eDate) {
        botQueryDetailDao = SpringBeanFactory.getBean("botQueryDetailDao");
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        this.sDate = sDate;
        this.eDate = eDate;
    }

    public static void main(String[] args) {
        Date sDate;
        Date eDate;
        for (String date : dateList) {
            try {
                sDate = eDate = DateUtils.parseDate(date, new String[]{"d-MM-yy"});
                log.info("sDate : " + sDate + ", eDate : " + eDate);
                ExpediaBotSundayDataExtract expediaBotWeeklyExtract = new ExpediaBotSundayDataExtract(sDate, eDate);
                expediaBotWeeklyExtract.process();
            } catch (ParseException e) {
                throw new RuntimeException(e);
            }
        }
    }

    public void process() {
        List<OwnDomainEntity> ownDomainEntities = ownDomainEntityDAO.getDomainListBasedCompanyName(companyName);
        String fileDirPath = String.format(fullFilePath + "/%s/", FormatUtils.formatDate(eDate, "yyyyMMdd"));
        FileUtil.mkdir(fileDirPath);

        for (OwnDomainEntity ownDomainEntity : ownDomainEntities) {
            if (null != ownDomainEntity.getId() && NOT_INCLUDE_OID.equals(ownDomainEntity.getId())) {
                log.info("---------> skip this domainId : "+ownDomainEntity.getId());
                continue;
            }
            log.info("Processing domainId : "+ownDomainEntity.getId());
            try {
                String pDate = FormatUtils.formatDate(new Date(), FormatUtils.DATE_FORMAT_YYYYMMDD);
                String sTime = FormatUtils.formatDate(new Date(), "yyyyMMdd HH:mm:ss");

                logglyVO.setoId(String.valueOf(ownDomainEntity.getId()));
                logglyVO.setName("ExpediaBotWeeklyExtract");
                logglyVO.setDevice(ownDomainEntity.isMobileDomain()?"m":"d");

                logglyVO.setpDate(pDate);
                List<String> groupList = new ArrayList<>();
                groupList.add(LogglyVO.GROUP_BOT_EXTRACT);
                logglyVO.setGroups(groupList);

                processFull(ownDomainEntity, fileDirPath);

                logglyVO.setStatus(LogglyVO.STATUS_OK);
                logglyVO.setsTime(sTime);
                logglyVO.seteTime(FormatUtils.formatDate(new Date(), "yyyyMMdd HH:mm:ss"));
                logglyVO.setRows(String.valueOf(totalCnt));
                String body = new Gson().toJson(logglyVO);
                LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);

            } catch (Exception e) {
                e.printStackTrace();
                logglyVO.setStatus(LogglyVO.STATUS_NG);
                String body = new Gson().toJson(logglyVO);
                LogglyUtils.sendLoggly(body,LogglyUtils.EXTRACT_TAG_NAME);
            }
        }

        try {
            FTPUtils.createRemoteDir(FTP_SERVER, FTP_SERVER_USER, FTP_SERVER_PW, 4765, FTP_SERVER_PATH + "/" + FormatUtils.formatDate(eDate, "yyyyMMdd") + "_sunday");
            List<File> allFile = new ArrayList<>(FileUtils.listFiles(new File(fileDirPath), null, true));
            List<String> allFileNameList = allFile.stream().map(var -> var.getAbsolutePath()).collect(Collectors.toList());
            FTPUtils.saveFilesToFTPWithRetryCount(FTP_SERVER, FTP_SERVER_USER, FTP_SERVER_PW, 4765, allFileNameList.toArray(new String[]{}), FTP_SERVER_PATH + "/" + FormatUtils.formatDate(eDate, "yyyyMMdd") + "_sunday/", 3);
        } catch (Exception e) {
            e.printStackTrace();
        }

        //删除过期文件
        try {
            log.info(" ============Start deleting expired files ");
            Collection<File> files = FileUtils.listFilesAndDirs(new File(fullFilePath), new AbstractFileFilter() {
                @Override
                public boolean accept(File file) {
                    return false;
                }
            }, new AbstractFileFilter() {
                @Override
                public boolean accept(File dir, String name) {
                    return name.length() == 8 && name.startsWith("20");
                }
            });
            for (File file : files) {
                if (file.isDirectory()) {
                    String name = file.getName();
                    if (name.equals("expedia_bot_full")) { continue; }
                    if (name.compareTo(FormatUtils.formatDate(eDate, "yyyyMMdd")) < 0) {
                        String absolutePath = file.getAbsolutePath();
                        FileUtils.forceDelete(file);
                        log.info("==============del: " + absolutePath);

                    }
                }
            }

            log.info(" ============ deleting expired files success");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void processFull(OwnDomainEntity ownDomainEntity, String fileDirPath) throws Exception {
        String ownPath = fileDirPath + String.format(fileName, ownDomainEntity.getId(), ownDomainEntity.getDomain(), FormatUtils.formatDate(eDate, "MMddyyyy"));
        if (new File(ownPath).exists()) {
            log.error("File eixst!!!! " + ownPath);
            return;
        }
        File oldFile = new File(ownPath);
        CSVPrinter csvPrinter = new CSVPrinter(new BufferedWriter(new FileWriter(ownPath)), csvFullFormat);

        Set<String> existRecords = new HashSet<>();
        int startIndex = 0;
        int queryCnt = 0;
        while (true) {
            log.info("query : "+startIndex);
            List<ExpediaExtractVo> mapList = botQueryDetailDao.botFullExtract(ownDomainEntity.getId(), sDate, eDate, startIndex, PAGE_SIZE);
            if (CollectionUtils.isEmpty(mapList)) {
                break;
            }
            startIndex += PAGE_SIZE;
            queryCnt += mapList.size();
            for (ExpediaExtractVo vo : mapList) {
                String key = vo.getUa_group_id()+"#"+vo.getIp_address()+"#"+vo.getUrlhash();
                if(existRecords.contains(key)) {
                    log.error("key exist => "+key);
                    continue;
                }
                existRecords.add(key);
                csvPrinter.printRecord(
                        ownDomainEntity.getDomain(),
                        ownDomainEntity.getSearchEngineCountry(),
                        DateFormatUtils.format(sDate, "yyyy-MM-dd"),
                        getBotType(vo.getUseragent() == null ? "" : vo.getUseragent()),
                        vo.getUseragent(),
                        vo.getIp_address(),
                        vo.getSpoofed() == null ? "N/A" : (StringUtils.equalsIgnoreCase(vo.getSpoofed().toString(), "1") ? "Y" : "N"),
                        vo.getUrlStr(),
                        vo.getCrawlCount(),
                        vo.getCrawlAvgCount(),
                        vo.getLastCrawlDate(),
                        vo.getCount2xx(),
                        vo.getCount3xx(),
                        vo.getCount4xx(),
                        vo.getCount5xx(),
                        getDevice(vo.getUa_group_id())

                );
            }
        }
        csvPrinter.flush();
        csvPrinter.close();
        if (startIndex == 0) {
            oldFile.delete();
        }
        log.info("==========domain " + ownDomainEntity.getId() + " data szie: " + queryCnt);
        totalCnt = queryCnt;
//        oldFile.delete();
    }

    private String getBotType(String userAgent) {
        if (BotUtils.isGoogleBot(userAgent)) {
            return "Google";
        } else if (BotUtils.isBingBot(userAgent)) {
            return "Bing";
        } else if (BotUtils.isYahooBot(userAgent)) {
            return "Yahoo";
        } else if (BotUtils.isYandexBot(userAgent)) {
            return "Yandex";
        } else if (BotUtils.isBaiduBot(userAgent)) {
            return "Baidu";
        }
        return "UNKNOWN";
    }

    private String getDevice(String ua_group_id) {
        String defaultDevice = "desktop";
        if (ua_group_id == null) {
            return defaultDevice;
        }

        if (Integer.parseInt(ua_group_id) == ExpediaExtractVo.DESKTOP_UA_GROUP_ID) {
            defaultDevice = "desktop";
        } else if (Integer.parseInt(ua_group_id) == ExpediaExtractVo.MOBILE_UA_GROUP_ID) {
            defaultDevice = "mobile";
        }

        return defaultDevice;
    }

    private void copyBySSH(String host, String userName, String pw, String from, String saveTo, int type)
            throws Exception {
        if (type == 0) {
            System.out.println("copy from local to remote , file name:" + from);
        } else {
            System.out.println("copy from remote to local , file name:" + from);
        }

        boolean copySucceed = false;
        for (int i = 0; i < SSH_TRY_COUNT; i++) {
            Connection connection = new Connection(host);
            connection.connect();
            if (connection.authenticateWithPassword(userName, pw)) {
                System.out.println("login successfully.");
                SCPClient scpClient = connection.createSCPClient();
                if (type == 0) {
                    scpClient.put(from, saveTo);
                } else {
                    scpClient.get(from, saveTo);
                }
                connection.close();
                copySucceed = true;
                break;
            }

            System.out.println("Failed to login to target host...");

            try {
                Thread.sleep(300000);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        if (copySucceed == true) {
            System.out.println("Copy " + from + " to " + host + ": " + saveTo + " successfully.");
        } else {
            throw new RuntimeException("Couldnot login " + host + " for " + SSH_TRY_COUNT
                    + " tries. Please copy file yourself.");
        }
    }

    /**
     * start at 1
     * @param processDate
     * @return
     */
    public static Date getWeekFirstDay(Date processDate) {
        LocalDate localDate = processDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        int value = localDate.getDayOfWeek().getValue() - 1;
        System.out.println("localDate "  + localDate + ", value: " + value);
        return org.apache.commons.lang3.time.DateUtils.addDays(processDate, -value);
    }

}
