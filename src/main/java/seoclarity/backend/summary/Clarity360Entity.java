package seoclarity.backend.summary;

public class Clarity360Entity {

	private String alt_img_detail;
	private String alt_img_list;
	private String alternate_links;
	private Integer amphtml_flag;
	private String amphtml_href;
	private String analyzed_url_flg_s;
	private String analyzed_url_s;
	private String archive_flg;
	private String archive_flg_x_tag;
	private String[] attrint_key;
	private Integer[] attrint_value;
	private String[] attrstr_key;
	private String[] attrstr_value;
	private Integer backlink_citationflow;
	private Integer backlink_cnt_anchortext;
	private Integer backlink_cnt_backlinks;
	private Integer backlink_cnt_backlinksdomains;
	private Integer backlink_trustflow;
	private String blocked_by_robots;
	private Integer bot_2xxrequestcount;
	private Integer bot_3xxrequestcount;
	private Integer bot_4xxrequestcount;
	private Integer bot_5xxrequestcount;
	private Integer bot_numofdays;
	private Integer bot_requestcount;
	private String canonical;
	private String canonical_flg;
	private Integer canonical_header_flag;
	private String canonical_header_type;
	private String canonical_type;
	private String canonical_url_is_consistent;
	private String change_tracking_hash_cd_json;
	private Integer content_extracted_count;
	private Integer content_extracted_flg;
	private String content_flg;
	private String content_md5;
	private String content_type;
	private Integer content_word_count;
	private String content_words;
	private Integer conv_crawl_date;
	private Integer count_of_objects;
	private Integer crawl_date_long;
	private Integer crawl_depth;
	private String crawl_request_date;
	private Integer crawl_request_id;
	private String[] custom_data_content;
	private Integer[] custom_data_index;
	private String[] custom_data_links;
	private Integer[] custom_data_match_found;
	private String[] custom_data_selector;
	private String[] custom_data_selector_type;
	private Integer[] custom_data_word_count;
	private String description;
	private String description_flg;
	private Integer description_length;
	private String description_murmurhash;
	private String description_simhash;
	private String[] description_stemmed;
	private Integer document_size;
	private String domain;
	private Integer domain_id;
	private String domain_name;
	private Float download_latency;
	private Float download_time;
	private String error_message;
	private String external_link;
	private Integer final_response_code;
	private String folder_level_1;
	private String folder_level_2;
	private String folder_level_3;
	private Integer folder_level_count;
	private String follow_flg;
	private String follow_flg_x_tag;
	private Integer gsc_clicks;
	private Float gsc_impressions;
	private Integer gsc_pos_count;
	private Float gsc_pos_sum;
	private String[] h1;
	private Integer h1_count;
	private String h1_flg;
	private Integer h1_length;
	private String h1_md5;
	private String h1_murmurhash;
	private String h1_simhash;
	private String[] h1_stemmed;
	private String[] h2;
	private String h2_murmurhash;
	private String h2_simhash;
	private String[] h2_stemmed;
	private Integer header_noarchive;
	private Integer header_nofollow;
	private Integer header_noindex;
	private Integer header_noodp;
	private Integer header_nosnippet;
	private Integer header_noydir;
	private String hreflang_errors;
	private String hreflang_links;
	private String[] hreflang_links_href;
	private String[] hreflang_links_href_array;
	private Integer[] hreflang_links_href_hash_array;
	private String[] hreflang_links_lang;
	private Integer hreflang_links_out_count;
	private String[] hreflang_links_type;
	private Integer hreflang_url_count;
	private String hsts;
	private String index_flg;
	private String index_flg_x_tag;
	private Integer indexable;
	private String insecure_resources;
	private Integer insecure_resources_flag;
	private Integer internallink_inbound_anchortx_cnt;
	private Integer internallink_inbound_cnt;
	private Integer internallink_outbound_cnt;
	private Integer is_footer_link;
	private Integer is_header_link;
	private String lang;
	private Integer long_redirect;
	private String meta_charset;
	private String meta_content_type;
	private Integer meta_disabled_sitelinks;
	private Integer meta_noodp;
	private Integer meta_nosnippet;
	private Integer meta_noydir;
	private Integer meta_redirect;
	private Integer mixed_redirects;
	private Integer mobile_rel_alternate_url_is_consistent;
	private Integer noodp;
	private Integer nosnippet;
	private Integer noydir;
	private String og_markup;
	private String[] og_markup_content;
	private Integer og_markup_flag;
	private Integer og_markup_length;
	private String[] og_markup_property;
	private Integer outlink_count;
	private Integer page_1;
	private String[] page_analysis_fragments;
	private Integer page_analysis_issue_count;
	private String page_analysis_issues;
	private String[] page_analysis_issues_array;
	private String page_analysis_results;
	private String page_link;
	private String[] page_link_destination_url;
	private Integer[] page_link_destination_url_hash;
	private Integer[] page_link_destination_url_murmur_hash;
	private Integer page_timeout_flag;
	private Integer paginated;
	private String pagination_links;
	private String[] pagination_links_direction;
	private String parent_page;
	private String[] post_processing_issues_array;
	;
	private String protocol;
	private Integer redirect_blocked;
	private String redirect_blocked_reason;
	private String redirect_chain;
	private String redirect_final_url;
	private Integer redirect_flg;
	private Integer redirect_times;
	private String rel_next_html_url;
	private Integer rel_next_url_is_consistent;
	private Integer rel_prev_url_is_consistent;
	private Integer report_id;
	private String request_headers;
	private String request_time;
	private Integer[] resources_is_blocked;
	private Integer[] resources_is_from_memory_cache;
	private String[] resources_request_type;
	private Integer[] resources_status_code;
	private String[] resources_types;
	private String[] resources_url;
	private String response_code;
	private String response_headers;
	private Integer retry_attempted;
	private Float rg_avg_rank;
	private Integer rg_highest_top_100_cnt;
	private String[] rg_intent_array;
	private Integer[] rg_intent_kwcnt_array;
	private Float rg_sov;
	private Integer rg_sum_top1_avg_sv;
	private Integer rg_sum_top1_est_traffic;
	private Integer rg_sum_top10_avg_sv;
	private Integer rg_sum_top10_est_traffic;
	private Integer rg_sum_top100_avg_sv;
	private Integer rg_sum_top2_avg_sv;
	private Integer rg_sum_top2_est_traffic;
	private Integer rg_sum_top20_avg_sv;
	private Integer rg_sum_top3_avg_sv;
	private Integer rg_sum_top3_est_traffic;
	private Integer rg_sum_top30_avg_sv;
	private Integer rg_top100cnt;
	private Integer rg_top10cnt;
	private Integer rg_top1cnt;
	private Integer rg_top20cnt;
	private Integer rg_top2cnt;
	private Integer rg_top30cnt;
	private Integer rg_top3cnt;
	private Integer rg_total_rank;
	private Integer rg_traffic_potential;
	private Integer rg_typicalpage_strength;
	private Float rg_wtd_avg_rank;
	private Integer rg_wtd_vol_rank;
	private Float ri_avg_rank;
	private Integer ri_mumofdays;
	private Integer ri_top1;
	private Integer ri_top10;
	private Integer ri_top3;
	private Integer ri_total_sv;
	private Float ri_wtd_avg_rank;
	private String robots;
	private String robots_contents;
	private Integer robots_contents_x_tag;
	private String robots_flg;
	private String robots_flg_x_tag;
	private Integer sa_bounces;
	private Integer sa_entrances;
	private Integer sa_exits;
	private Integer sa_goal10completions;
	private Float sa_goal10Value;
	private Integer sa_goal11completions;
	private Float sa_goal11Value;
	private Integer sa_goal12completions;
	private Float sa_goal12Value;
	private Integer sa_goal13completions;
	private Float sa_goal13Value;
	private Integer sa_goal14completions;
	private Float sa_goal14Value;
	private Integer sa_goal15completions;
	private Float sa_goal15Value;
	private Integer sa_goal16completions;
	private Float sa_goal16Value;
	private Integer sa_goal17completions;
	private Float sa_goal17Value;
	private Integer sa_goal18completions;
	private Float sa_goal18Value;
	private Integer sa_goal19completions;
	private Float sa_goal19Value;
	private Integer sa_goal1completions;
	private Float sa_goal1Value;
	private Integer sa_goal20completions;
	private Float sa_goal20Value;
	private Integer sa_goal2completions;
	private Float sa_goal2Value;
	private Integer sa_goal3completions;
	private Float sa_goal3Value;
	private Integer sa_goal4completions;
	private Float sa_goal4Value;
	private Integer sa_goal5completions;
	private Float sa_goal5Value;
	private Integer sa_goal6completions;
	private Float sa_goal6Value;
	private Integer sa_goal7completions;
	private Float sa_goal7Value;
	private Integer sa_goal8completions;
	private Float sa_goal8Value;
	private Integer sa_goal9completions;
	private Float sa_goal9Value;
	private Float sa_item_revenue;
	private Integer sa_pageviews;
	private Float sa_session_duration;
	private Float sa_time_on_page;
	private Integer sa_transactions;
	private Integer searchanlytics_numofdays;
	private Float server_response_time;
	private Integer siteanalytics_numofdays;
	private String source_url;
	private String[] sources;
	private String splash_took;
	private Integer status;
	private String[] structure_schema_errors_encoding;
	private String[] structure_schema_errors_error_type;
	private String[] structure_schema_errors_info;
	private String[] structure_schema_errors_message;
	private String[] structure_schema_errors_path;
	private String[] structure_schema_errors_schema_type;
	private String[] structure_schema_warnings_encoding;
	private String[] structure_schema_warnings_info;
	private String[] structure_schema_warnings_message;
	private String[] structure_schema_warnings_path;
	private String[] structure_schema_warnings_schema_type;
	private String[] structure_schema_warnings_warning_type;
	private String structured_data;
	private String[] structured_schema_encoding;
	private String[] structured_schema_type;
	private String target_date;
	private String title;
	private String title_flg;
	private Integer title_length;
	private String title_md5;
	private String title_murmurhash;
	private String title_simhash;
	private String[] title_stemmed;
	private Integer twitter_description_length;
	private String twitter_markup;
	private String[] twitter_markup_content;
	private Integer twitter_markup_flag;
	private Integer twitter_markup_length;
	private String[] twitter_markup_property;
	private String url;
	private Integer url_length;
	private String valid_twitter_card;
	private String viewport_content;
	private Integer viewport_flag;
	
	private String[] root_sitemap_hash_array;
	
	private String source;
	private Integer cnt;
	
	public String getAlt_img_detail() {
		return alt_img_detail;
	}
	public void setAlt_img_detail(String alt_img_detail) {
		this.alt_img_detail = alt_img_detail;
	}
	public String getAlt_img_list() {
		return alt_img_list;
	}
	public void setAlt_img_list(String alt_img_list) {
		this.alt_img_list = alt_img_list;
	}
	public String getAlternate_links() {
		return alternate_links;
	}
	public void setAlternate_links(String alternate_links) {
		this.alternate_links = alternate_links;
	}
	public Integer getAmphtml_flag() {
		return amphtml_flag;
	}
	public void setAmphtml_flag(Integer amphtml_flag) {
		this.amphtml_flag = amphtml_flag;
	}
	public String getAmphtml_href() {
		return amphtml_href;
	}
	public void setAmphtml_href(String amphtml_href) {
		this.amphtml_href = amphtml_href;
	}
	public String getAnalyzed_url_flg_s() {
		return analyzed_url_flg_s;
	}
	public void setAnalyzed_url_flg_s(String analyzed_url_flg_s) {
		this.analyzed_url_flg_s = analyzed_url_flg_s;
	}
	public String getAnalyzed_url_s() {
		return analyzed_url_s;
	}
	public void setAnalyzed_url_s(String analyzed_url_s) {
		this.analyzed_url_s = analyzed_url_s;
	}
	public String getArchive_flg() {
		return archive_flg;
	}
	public void setArchive_flg(String archive_flg) {
		this.archive_flg = archive_flg;
	}
	public String getArchive_flg_x_tag() {
		return archive_flg_x_tag;
	}
	public void setArchive_flg_x_tag(String archive_flg_x_tag) {
		this.archive_flg_x_tag = archive_flg_x_tag;
	}
	public String[] getAttrint_key() {
		return attrint_key;
	}
	public void setAttrint_key(String[] attrint_key) {
		this.attrint_key = attrint_key;
	}
	public Integer[] getAttrint_value() {
		return attrint_value;
	}
	public void setAttrint_value(Integer[] attrint_value) {
		this.attrint_value = attrint_value;
	}
	public String[] getAttrstr_key() {
		return attrstr_key;
	}
	public void setAttrstr_key(String[] attrstr_key) {
		this.attrstr_key = attrstr_key;
	}
	public String[] getAttrstr_value() {
		return attrstr_value;
	}
	public void setAttrstr_value(String[] attrstr_value) {
		this.attrstr_value = attrstr_value;
	}
	public Integer getBacklink_citationflow() {
		return backlink_citationflow;
	}
	public void setBacklink_citationflow(Integer backlink_citationflow) {
		this.backlink_citationflow = backlink_citationflow;
	}
	public Integer getBacklink_cnt_anchortext() {
		return backlink_cnt_anchortext;
	}
	public void setBacklink_cnt_anchortext(Integer backlink_cnt_anchortext) {
		this.backlink_cnt_anchortext = backlink_cnt_anchortext;
	}
	public Integer getBacklink_cnt_backlinks() {
		return backlink_cnt_backlinks;
	}
	public void setBacklink_cnt_backlinks(Integer backlink_cnt_backlinks) {
		this.backlink_cnt_backlinks = backlink_cnt_backlinks;
	}
	public Integer getBacklink_cnt_backlinksdomains() {
		return backlink_cnt_backlinksdomains;
	}
	public void setBacklink_cnt_backlinksdomains(Integer backlink_cnt_backlinksdomains) {
		this.backlink_cnt_backlinksdomains = backlink_cnt_backlinksdomains;
	}
	public Integer getBacklink_trustflow() {
		return backlink_trustflow;
	}
	public void setBacklink_trustflow(Integer backlink_trustflow) {
		this.backlink_trustflow = backlink_trustflow;
	}
	public String getBlocked_by_robots() {
		return blocked_by_robots;
	}
	public void setBlocked_by_robots(String blocked_by_robots) {
		this.blocked_by_robots = blocked_by_robots;
	}
	public Integer getBot_2xxrequestcount() {
		return bot_2xxrequestcount;
	}
	public void setBot_2xxrequestcount(Integer bot_2xxrequestcount) {
		this.bot_2xxrequestcount = bot_2xxrequestcount;
	}
	public Integer getBot_3xxrequestcount() {
		return bot_3xxrequestcount;
	}
	public void setBot_3xxrequestcount(Integer bot_3xxrequestcount) {
		this.bot_3xxrequestcount = bot_3xxrequestcount;
	}
	public Integer getBot_4xxrequestcount() {
		return bot_4xxrequestcount;
	}
	public void setBot_4xxrequestcount(Integer bot_4xxrequestcount) {
		this.bot_4xxrequestcount = bot_4xxrequestcount;
	}
	public Integer getBot_5xxrequestcount() {
		return bot_5xxrequestcount;
	}
	public void setBot_5xxrequestcount(Integer bot_5xxrequestcount) {
		this.bot_5xxrequestcount = bot_5xxrequestcount;
	}
	public Integer getBot_numofdays() {
		return bot_numofdays;
	}
	public void setBot_numofdays(Integer bot_numofdays) {
		this.bot_numofdays = bot_numofdays;
	}
	public Integer getBot_requestcount() {
		return bot_requestcount;
	}
	public void setBot_requestcount(Integer bot_requestcount) {
		this.bot_requestcount = bot_requestcount;
	}
	public String getCanonical() {
		return canonical;
	}
	public void setCanonical(String canonical) {
		this.canonical = canonical;
	}
	public String getCanonical_flg() {
		return canonical_flg;
	}
	public void setCanonical_flg(String canonical_flg) {
		this.canonical_flg = canonical_flg;
	}
	public Integer getCanonical_header_flag() {
		return canonical_header_flag;
	}
	public void setCanonical_header_flag(Integer canonical_header_flag) {
		this.canonical_header_flag = canonical_header_flag;
	}
	public String getCanonical_header_type() {
		return canonical_header_type;
	}
	public void setCanonical_header_type(String canonical_header_type) {
		this.canonical_header_type = canonical_header_type;
	}
	public String getCanonical_type() {
		return canonical_type;
	}
	public void setCanonical_type(String canonical_type) {
		this.canonical_type = canonical_type;
	}
	public String getCanonical_url_is_consistent() {
		return canonical_url_is_consistent;
	}
	public void setCanonical_url_is_consistent(String canonical_url_is_consistent) {
		this.canonical_url_is_consistent = canonical_url_is_consistent;
	}
	public String getChange_tracking_hash_cd_json() {
		return change_tracking_hash_cd_json;
	}
	public void setChange_tracking_hash_cd_json(String change_tracking_hash_cd_json) {
		this.change_tracking_hash_cd_json = change_tracking_hash_cd_json;
	}
	public Integer getContent_extracted_count() {
		return content_extracted_count;
	}
	public void setContent_extracted_count(Integer content_extracted_count) {
		this.content_extracted_count = content_extracted_count;
	}
	public Integer getContent_extracted_flg() {
		return content_extracted_flg;
	}
	public void setContent_extracted_flg(Integer content_extracted_flg) {
		this.content_extracted_flg = content_extracted_flg;
	}
	public String getContent_flg() {
		return content_flg;
	}
	public void setContent_flg(String content_flg) {
		this.content_flg = content_flg;
	}
	public String getContent_md5() {
		return content_md5;
	}
	public void setContent_md5(String content_md5) {
		this.content_md5 = content_md5;
	}
	public String getContent_type() {
		return content_type;
	}
	public void setContent_type(String content_type) {
		this.content_type = content_type;
	}
	public Integer getContent_word_count() {
		return content_word_count;
	}
	public void setContent_word_count(Integer content_word_count) {
		this.content_word_count = content_word_count;
	}
	public String getContent_words() {
		return content_words;
	}
	public void setContent_words(String content_words) {
		this.content_words = content_words;
	}
	public Integer getConv_crawl_date() {
		return conv_crawl_date;
	}
	public void setConv_crawl_date(Integer conv_crawl_date) {
		this.conv_crawl_date = conv_crawl_date;
	}
	public Integer getCount_of_objects() {
		return count_of_objects;
	}
	public void setCount_of_objects(Integer count_of_objects) {
		this.count_of_objects = count_of_objects;
	}
	public Integer getCrawl_date_long() {
		return crawl_date_long;
	}
	public void setCrawl_date_long(Integer crawl_date_long) {
		this.crawl_date_long = crawl_date_long;
	}
	public Integer getCrawl_depth() {
		return crawl_depth;
	}
	public void setCrawl_depth(Integer crawl_depth) {
		this.crawl_depth = crawl_depth;
	}
	public String getCrawl_request_date() {
		return crawl_request_date;
	}
	public void setCrawl_request_date(String crawl_request_date) {
		this.crawl_request_date = crawl_request_date;
	}
	public Integer getCrawl_request_id() {
		return crawl_request_id;
	}
	public void setCrawl_request_id(Integer crawl_request_id) {
		this.crawl_request_id = crawl_request_id;
	}
	public String[] getCustom_data_content() {
		return custom_data_content;
	}
	public void setCustom_data_content(String[] custom_data_content) {
		this.custom_data_content = custom_data_content;
	}
	public Integer[] getCustom_data_index() {
		return custom_data_index;
	}
	public void setCustom_data_index(Integer[] custom_data_index) {
		this.custom_data_index = custom_data_index;
	}
	public String[] getCustom_data_links() {
		return custom_data_links;
	}
	public void setCustom_data_links(String[] custom_data_links) {
		this.custom_data_links = custom_data_links;
	}
	public Integer[] getCustom_data_match_found() {
		return custom_data_match_found;
	}
	public void setCustom_data_match_found(Integer[] custom_data_match_found) {
		this.custom_data_match_found = custom_data_match_found;
	}
	public String[] getCustom_data_selector() {
		return custom_data_selector;
	}
	public void setCustom_data_selector(String[] custom_data_selector) {
		this.custom_data_selector = custom_data_selector;
	}
	public String[] getCustom_data_selector_type() {
		return custom_data_selector_type;
	}
	public void setCustom_data_selector_type(String[] custom_data_selector_type) {
		this.custom_data_selector_type = custom_data_selector_type;
	}
	public Integer[] getCustom_data_word_count() {
		return custom_data_word_count;
	}
	public void setCustom_data_word_count(Integer[] custom_data_word_count) {
		this.custom_data_word_count = custom_data_word_count;
	}
	public String getDescription() {
		return description;
	}
	public void setDescription(String description) {
		this.description = description;
	}
	public String getDescription_flg() {
		return description_flg;
	}
	public void setDescription_flg(String description_flg) {
		this.description_flg = description_flg;
	}
	public Integer getDescription_length() {
		return description_length;
	}
	public void setDescription_length(Integer description_length) {
		this.description_length = description_length;
	}
	public String getDescription_murmurhash() {
		return description_murmurhash;
	}
	public void setDescription_murmurhash(String description_murmurhash) {
		this.description_murmurhash = description_murmurhash;
	}
	public String getDescription_simhash() {
		return description_simhash;
	}
	public void setDescription_simhash(String description_simhash) {
		this.description_simhash = description_simhash;
	}
	public String[] getDescription_stemmed() {
		return description_stemmed;
	}
	public void setDescription_stemmed(String[] description_stemmed) {
		this.description_stemmed = description_stemmed;
	}
	public Integer getDocument_size() {
		return document_size;
	}
	public void setDocument_size(Integer document_size) {
		this.document_size = document_size;
	}
	public String getDomain() {
		return domain;
	}
	public void setDomain(String domain) {
		this.domain = domain;
	}
	public Integer getDomain_id() {
		return domain_id;
	}
	public void setDomain_id(Integer domain_id) {
		this.domain_id = domain_id;
	}
	public String getDomain_name() {
		return domain_name;
	}
	public void setDomain_name(String domain_name) {
		this.domain_name = domain_name;
	}
	public Float getDownload_latency() {
		return download_latency;
	}
	public void setDownload_latency(Float download_latency) {
		this.download_latency = download_latency;
	}
	public Float getDownload_time() {
		return download_time;
	}
	public void setDownload_time(Float download_time) {
		this.download_time = download_time;
	}
	public String getError_message() {
		return error_message;
	}
	public void setError_message(String error_message) {
		this.error_message = error_message;
	}
	public String getExternal_link() {
		return external_link;
	}
	public void setExternal_link(String external_link) {
		this.external_link = external_link;
	}
	public Integer getFinal_response_code() {
		return final_response_code;
	}
	public void setFinal_response_code(Integer final_response_code) {
		this.final_response_code = final_response_code;
	}
	public String getFolder_level_1() {
		return folder_level_1;
	}
	public void setFolder_level_1(String folder_level_1) {
		this.folder_level_1 = folder_level_1;
	}
	public String getFolder_level_2() {
		return folder_level_2;
	}
	public void setFolder_level_2(String folder_level_2) {
		this.folder_level_2 = folder_level_2;
	}
	public String getFolder_level_3() {
		return folder_level_3;
	}
	public void setFolder_level_3(String folder_level_3) {
		this.folder_level_3 = folder_level_3;
	}
	public Integer getFolder_level_count() {
		return folder_level_count;
	}
	public void setFolder_level_count(Integer folder_level_count) {
		this.folder_level_count = folder_level_count;
	}
	public String getFollow_flg() {
		return follow_flg;
	}
	public void setFollow_flg(String follow_flg) {
		this.follow_flg = follow_flg;
	}
	public String getFollow_flg_x_tag() {
		return follow_flg_x_tag;
	}
	public void setFollow_flg_x_tag(String follow_flg_x_tag) {
		this.follow_flg_x_tag = follow_flg_x_tag;
	}
	public Integer getGsc_clicks() {
		return gsc_clicks;
	}
	public void setGsc_clicks(Integer gsc_clicks) {
		this.gsc_clicks = gsc_clicks;
	}
	public Float getGsc_impressions() {
		return gsc_impressions;
	}
	public void setGsc_impressions(Float gsc_impressions) {
		this.gsc_impressions = gsc_impressions;
	}
	public Integer getGsc_pos_count() {
		return gsc_pos_count;
	}
	public void setGsc_pos_count(Integer gsc_pos_count) {
		this.gsc_pos_count = gsc_pos_count;
	}
	public Float getGsc_pos_sum() {
		return gsc_pos_sum;
	}
	public void setGsc_pos_sum(Float gsc_pos_sum) {
		this.gsc_pos_sum = gsc_pos_sum;
	}
	public String[] getH1() {
		return h1;
	}
	public void setH1(String[] h1) {
		this.h1 = h1;
	}
	public Integer getH1_count() {
		return h1_count;
	}
	public void setH1_count(Integer h1_count) {
		this.h1_count = h1_count;
	}
	public String getH1_flg() {
		return h1_flg;
	}
	public void setH1_flg(String h1_flg) {
		this.h1_flg = h1_flg;
	}
	public Integer getH1_length() {
		return h1_length;
	}
	public void setH1_length(Integer h1_length) {
		this.h1_length = h1_length;
	}
	public String getH1_md5() {
		return h1_md5;
	}
	public void setH1_md5(String h1_md5) {
		this.h1_md5 = h1_md5;
	}
	public String getH1_murmurhash() {
		return h1_murmurhash;
	}
	public void setH1_murmurhash(String h1_murmurhash) {
		this.h1_murmurhash = h1_murmurhash;
	}
	public String getH1_simhash() {
		return h1_simhash;
	}
	public void setH1_simhash(String h1_simhash) {
		this.h1_simhash = h1_simhash;
	}
	public String[] getH1_stemmed() {
		return h1_stemmed;
	}
	public void setH1_stemmed(String[] h1_stemmed) {
		this.h1_stemmed = h1_stemmed;
	}
	public String[] getH2() {
		return h2;
	}
	public void setH2(String[] h2) {
		this.h2 = h2;
	}
	public String getH2_murmurhash() {
		return h2_murmurhash;
	}
	public void setH2_murmurhash(String h2_murmurhash) {
		this.h2_murmurhash = h2_murmurhash;
	}
	public String getH2_simhash() {
		return h2_simhash;
	}
	public void setH2_simhash(String h2_simhash) {
		this.h2_simhash = h2_simhash;
	}
	public String[] getH2_stemmed() {
		return h2_stemmed;
	}
	public void setH2_stemmed(String[] h2_stemmed) {
		this.h2_stemmed = h2_stemmed;
	}
	public Integer getHeader_noarchive() {
		return header_noarchive;
	}
	public void setHeader_noarchive(Integer header_noarchive) {
		this.header_noarchive = header_noarchive;
	}
	public Integer getHeader_nofollow() {
		return header_nofollow;
	}
	public void setHeader_nofollow(Integer header_nofollow) {
		this.header_nofollow = header_nofollow;
	}
	public Integer getHeader_noindex() {
		return header_noindex;
	}
	public void setHeader_noindex(Integer header_noindex) {
		this.header_noindex = header_noindex;
	}
	public Integer getHeader_noodp() {
		return header_noodp;
	}
	public void setHeader_noodp(Integer header_noodp) {
		this.header_noodp = header_noodp;
	}
	public Integer getHeader_nosnippet() {
		return header_nosnippet;
	}
	public void setHeader_nosnippet(Integer header_nosnippet) {
		this.header_nosnippet = header_nosnippet;
	}
	public Integer getHeader_noydir() {
		return header_noydir;
	}
	public void setHeader_noydir(Integer header_noydir) {
		this.header_noydir = header_noydir;
	}
	public String getHreflang_errors() {
		return hreflang_errors;
	}
	public void setHreflang_errors(String hreflang_errors) {
		this.hreflang_errors = hreflang_errors;
	}
	public String getHreflang_links() {
		return hreflang_links;
	}
	public void setHreflang_links(String hreflang_links) {
		this.hreflang_links = hreflang_links;
	}
	public String[] getHreflang_links_href() {
		return hreflang_links_href;
	}
	public void setHreflang_links_href(String[] hreflang_links_href) {
		this.hreflang_links_href = hreflang_links_href;
	}
	public String[] getHreflang_links_href_array() {
		return hreflang_links_href_array;
	}
	public void setHreflang_links_href_array(String[] hreflang_links_href_array) {
		this.hreflang_links_href_array = hreflang_links_href_array;
	}
	public Integer[] getHreflang_links_href_hash_array() {
		return hreflang_links_href_hash_array;
	}
	public void setHreflang_links_href_hash_array(Integer[] hreflang_links_href_hash_array) {
		this.hreflang_links_href_hash_array = hreflang_links_href_hash_array;
	}
	public String[] getHreflang_links_lang() {
		return hreflang_links_lang;
	}
	public void setHreflang_links_lang(String[] hreflang_links_lang) {
		this.hreflang_links_lang = hreflang_links_lang;
	}
	public Integer getHreflang_links_out_count() {
		return hreflang_links_out_count;
	}
	public void setHreflang_links_out_count(Integer hreflang_links_out_count) {
		this.hreflang_links_out_count = hreflang_links_out_count;
	}
	public String[] getHreflang_links_type() {
		return hreflang_links_type;
	}
	public void setHreflang_links_type(String[] hreflang_links_type) {
		this.hreflang_links_type = hreflang_links_type;
	}
	public Integer getHreflang_url_count() {
		return hreflang_url_count;
	}
	public void setHreflang_url_count(Integer hreflang_url_count) {
		this.hreflang_url_count = hreflang_url_count;
	}
	public String getHsts() {
		return hsts;
	}
	public void setHsts(String hsts) {
		this.hsts = hsts;
	}
	public String getIndex_flg() {
		return index_flg;
	}
	public void setIndex_flg(String index_flg) {
		this.index_flg = index_flg;
	}
	public String getIndex_flg_x_tag() {
		return index_flg_x_tag;
	}
	public void setIndex_flg_x_tag(String index_flg_x_tag) {
		this.index_flg_x_tag = index_flg_x_tag;
	}
	public Integer getIndexable() {
		return indexable;
	}
	public void setIndexable(Integer indexable) {
		this.indexable = indexable;
	}
	public String getInsecure_resources() {
		return insecure_resources;
	}
	public void setInsecure_resources(String insecure_resources) {
		this.insecure_resources = insecure_resources;
	}
	public Integer getInsecure_resources_flag() {
		return insecure_resources_flag;
	}
	public void setInsecure_resources_flag(Integer insecure_resources_flag) {
		this.insecure_resources_flag = insecure_resources_flag;
	}
	public Integer getInternallink_inbound_anchortx_cnt() {
		return internallink_inbound_anchortx_cnt;
	}
	public void setInternallink_inbound_anchortx_cnt(Integer internallink_inbound_anchortx_cnt) {
		this.internallink_inbound_anchortx_cnt = internallink_inbound_anchortx_cnt;
	}
	public Integer getInternallink_inbound_cnt() {
		return internallink_inbound_cnt;
	}
	public void setInternallink_inbound_cnt(Integer internallink_inbound_cnt) {
		this.internallink_inbound_cnt = internallink_inbound_cnt;
	}
	public Integer getInternallink_outbound_cnt() {
		return internallink_outbound_cnt;
	}
	public void setInternallink_outbound_cnt(Integer internallink_outbound_cnt) {
		this.internallink_outbound_cnt = internallink_outbound_cnt;
	}
	public Integer getIs_footer_link() {
		return is_footer_link;
	}
	public void setIs_footer_link(Integer is_footer_link) {
		this.is_footer_link = is_footer_link;
	}
	public Integer getIs_header_link() {
		return is_header_link;
	}
	public void setIs_header_link(Integer is_header_link) {
		this.is_header_link = is_header_link;
	}
	public String getLang() {
		return lang;
	}
	public void setLang(String lang) {
		this.lang = lang;
	}
	public Integer getLong_redirect() {
		return long_redirect;
	}
	public void setLong_redirect(Integer long_redirect) {
		this.long_redirect = long_redirect;
	}
	public String getMeta_charset() {
		return meta_charset;
	}
	public void setMeta_charset(String meta_charset) {
		this.meta_charset = meta_charset;
	}
	public String getMeta_content_type() {
		return meta_content_type;
	}
	public void setMeta_content_type(String meta_content_type) {
		this.meta_content_type = meta_content_type;
	}
	public Integer getMeta_disabled_sitelinks() {
		return meta_disabled_sitelinks;
	}
	public void setMeta_disabled_sitelinks(Integer meta_disabled_sitelinks) {
		this.meta_disabled_sitelinks = meta_disabled_sitelinks;
	}
	public Integer getMeta_noodp() {
		return meta_noodp;
	}
	public void setMeta_noodp(Integer meta_noodp) {
		this.meta_noodp = meta_noodp;
	}
	public Integer getMeta_nosnippet() {
		return meta_nosnippet;
	}
	public void setMeta_nosnippet(Integer meta_nosnippet) {
		this.meta_nosnippet = meta_nosnippet;
	}
	public Integer getMeta_noydir() {
		return meta_noydir;
	}
	public void setMeta_noydir(Integer meta_noydir) {
		this.meta_noydir = meta_noydir;
	}
	public Integer getMeta_redirect() {
		return meta_redirect;
	}
	public void setMeta_redirect(Integer meta_redirect) {
		this.meta_redirect = meta_redirect;
	}
	public Integer getMixed_redirects() {
		return mixed_redirects;
	}
	public void setMixed_redirects(Integer mixed_redirects) {
		this.mixed_redirects = mixed_redirects;
	}
	public Integer getMobile_rel_alternate_url_is_consistent() {
		return mobile_rel_alternate_url_is_consistent;
	}
	public void setMobile_rel_alternate_url_is_consistent(Integer mobile_rel_alternate_url_is_consistent) {
		this.mobile_rel_alternate_url_is_consistent = mobile_rel_alternate_url_is_consistent;
	}
	public Integer getNoodp() {
		return noodp;
	}
	public void setNoodp(Integer noodp) {
		this.noodp = noodp;
	}
	public Integer getNosnippet() {
		return nosnippet;
	}
	public void setNosnippet(Integer nosnippet) {
		this.nosnippet = nosnippet;
	}
	public Integer getNoydir() {
		return noydir;
	}
	public void setNoydir(Integer noydir) {
		this.noydir = noydir;
	}
	public String getOg_markup() {
		return og_markup;
	}
	public void setOg_markup(String og_markup) {
		this.og_markup = og_markup;
	}
	public String[] getOg_markup_content() {
		return og_markup_content;
	}
	public void setOg_markup_content(String[] og_markup_content) {
		this.og_markup_content = og_markup_content;
	}
	public Integer getOg_markup_flag() {
		return og_markup_flag;
	}
	public void setOg_markup_flag(Integer og_markup_flag) {
		this.og_markup_flag = og_markup_flag;
	}
	public Integer getOg_markup_length() {
		return og_markup_length;
	}
	public void setOg_markup_length(Integer og_markup_length) {
		this.og_markup_length = og_markup_length;
	}
	public String[] getOg_markup_property() {
		return og_markup_property;
	}
	public void setOg_markup_property(String[] og_markup_property) {
		this.og_markup_property = og_markup_property;
	}
	public Integer getOutlink_count() {
		return outlink_count;
	}
	public void setOutlink_count(Integer outlink_count) {
		this.outlink_count = outlink_count;
	}
	public Integer getPage_1() {
		return page_1;
	}
	public void setPage_1(Integer page_1) {
		this.page_1 = page_1;
	}
	public String[] getPage_analysis_fragments() {
		return page_analysis_fragments;
	}
	public void setPage_analysis_fragments(String[] page_analysis_fragments) {
		this.page_analysis_fragments = page_analysis_fragments;
	}
	public Integer getPage_analysis_issue_count() {
		return page_analysis_issue_count;
	}
	public void setPage_analysis_issue_count(Integer page_analysis_issue_count) {
		this.page_analysis_issue_count = page_analysis_issue_count;
	}
	public String getPage_analysis_issues() {
		return page_analysis_issues;
	}
	public void setPage_analysis_issues(String page_analysis_issues) {
		this.page_analysis_issues = page_analysis_issues;
	}
	public String[] getPage_analysis_issues_array() {
		return page_analysis_issues_array;
	}
	public void setPage_analysis_issues_array(String[] page_analysis_issues_array) {
		this.page_analysis_issues_array = page_analysis_issues_array;
	}
	public String getPage_analysis_results() {
		return page_analysis_results;
	}
	public void setPage_analysis_results(String page_analysis_results) {
		this.page_analysis_results = page_analysis_results;
	}
	public String getPage_link() {
		return page_link;
	}
	public void setPage_link(String page_link) {
		this.page_link = page_link;
	}
	public String[] getPage_link_destination_url() {
		return page_link_destination_url;
	}
	public void setPage_link_destination_url(String[] page_link_destination_url) {
		this.page_link_destination_url = page_link_destination_url;
	}
	public Integer[] getPage_link_destination_url_hash() {
		return page_link_destination_url_hash;
	}
	public void setPage_link_destination_url_hash(Integer[] page_link_destination_url_hash) {
		this.page_link_destination_url_hash = page_link_destination_url_hash;
	}
	public Integer[] getPage_link_destination_url_murmur_hash() {
		return page_link_destination_url_murmur_hash;
	}
	public void setPage_link_destination_url_murmur_hash(Integer[] page_link_destination_url_murmur_hash) {
		this.page_link_destination_url_murmur_hash = page_link_destination_url_murmur_hash;
	}
	public Integer getPage_timeout_flag() {
		return page_timeout_flag;
	}
	public void setPage_timeout_flag(Integer page_timeout_flag) {
		this.page_timeout_flag = page_timeout_flag;
	}
	public Integer getPaginated() {
		return paginated;
	}
	public void setPaginated(Integer paginated) {
		this.paginated = paginated;
	}
	public String getPagination_links() {
		return pagination_links;
	}
	public void setPagination_links(String pagination_links) {
		this.pagination_links = pagination_links;
	}
	public String[] getPagination_links_direction() {
		return pagination_links_direction;
	}
	public void setPagination_links_direction(String[] pagination_links_direction) {
		this.pagination_links_direction = pagination_links_direction;
	}
	public String getParent_page() {
		return parent_page;
	}
	public void setParent_page(String parent_page) {
		this.parent_page = parent_page;
	}
	public String[] getPost_processing_issues_array() {
		return post_processing_issues_array;
	}
	public void setPost_processing_issues_array(String[] post_processing_issues_array) {
		this.post_processing_issues_array = post_processing_issues_array;
	}
	public String getProtocol() {
		return protocol;
	}
	public void setProtocol(String protocol) {
		this.protocol = protocol;
	}
	public Integer getRedirect_blocked() {
		return redirect_blocked;
	}
	public void setRedirect_blocked(Integer redirect_blocked) {
		this.redirect_blocked = redirect_blocked;
	}
	public String getRedirect_blocked_reason() {
		return redirect_blocked_reason;
	}
	public void setRedirect_blocked_reason(String redirect_blocked_reason) {
		this.redirect_blocked_reason = redirect_blocked_reason;
	}
	public String getRedirect_chain() {
		return redirect_chain;
	}
	public void setRedirect_chain(String redirect_chain) {
		this.redirect_chain = redirect_chain;
	}
	public String getRedirect_final_url() {
		return redirect_final_url;
	}
	public void setRedirect_final_url(String redirect_final_url) {
		this.redirect_final_url = redirect_final_url;
	}
	public Integer getRedirect_flg() {
		return redirect_flg;
	}
	public void setRedirect_flg(Integer redirect_flg) {
		this.redirect_flg = redirect_flg;
	}
	public Integer getRedirect_times() {
		return redirect_times;
	}
	public void setRedirect_times(Integer redirect_times) {
		this.redirect_times = redirect_times;
	}
	public String getRel_next_html_url() {
		return rel_next_html_url;
	}
	public void setRel_next_html_url(String rel_next_html_url) {
		this.rel_next_html_url = rel_next_html_url;
	}
	public Integer getRel_next_url_is_consistent() {
		return rel_next_url_is_consistent;
	}
	public void setRel_next_url_is_consistent(Integer rel_next_url_is_consistent) {
		this.rel_next_url_is_consistent = rel_next_url_is_consistent;
	}
	public Integer getRel_prev_url_is_consistent() {
		return rel_prev_url_is_consistent;
	}
	public void setRel_prev_url_is_consistent(Integer rel_prev_url_is_consistent) {
		this.rel_prev_url_is_consistent = rel_prev_url_is_consistent;
	}
	public Integer getReport_id() {
		return report_id;
	}
	public void setReport_id(Integer report_id) {
		this.report_id = report_id;
	}
	public String getRequest_headers() {
		return request_headers;
	}
	public void setRequest_headers(String request_headers) {
		this.request_headers = request_headers;
	}
	public String getRequest_time() {
		return request_time;
	}
	public void setRequest_time(String request_time) {
		this.request_time = request_time;
	}
	public Integer[] getResources_is_blocked() {
		return resources_is_blocked;
	}
	public void setResources_is_blocked(Integer[] resources_is_blocked) {
		this.resources_is_blocked = resources_is_blocked;
	}
	public Integer[] getResources_is_from_memory_cache() {
		return resources_is_from_memory_cache;
	}
	public void setResources_is_from_memory_cache(Integer[] resources_is_from_memory_cache) {
		this.resources_is_from_memory_cache = resources_is_from_memory_cache;
	}
	public String[] getResources_request_type() {
		return resources_request_type;
	}
	public void setResources_request_type(String[] resources_request_type) {
		this.resources_request_type = resources_request_type;
	}
	public Integer[] getResources_status_code() {
		return resources_status_code;
	}
	public void setResources_status_code(Integer[] resources_status_code) {
		this.resources_status_code = resources_status_code;
	}
	public String[] getResources_types() {
		return resources_types;
	}
	public void setResources_types(String[] resources_types) {
		this.resources_types = resources_types;
	}
	public String[] getResources_url() {
		return resources_url;
	}
	public void setResources_url(String[] resources_url) {
		this.resources_url = resources_url;
	}
	public String getResponse_code() {
		return response_code;
	}
	public void setResponse_code(String response_code) {
		this.response_code = response_code;
	}
	public String getResponse_headers() {
		return response_headers;
	}
	public void setResponse_headers(String response_headers) {
		this.response_headers = response_headers;
	}
	public Integer getRetry_attempted() {
		return retry_attempted;
	}
	public void setRetry_attempted(Integer retry_attempted) {
		this.retry_attempted = retry_attempted;
	}
	public Float getRg_avg_rank() {
		return rg_avg_rank;
	}
	public void setRg_avg_rank(Float rg_avg_rank) {
		this.rg_avg_rank = rg_avg_rank;
	}
	public Integer getRg_highest_top_100_cnt() {
		return rg_highest_top_100_cnt;
	}
	public void setRg_highest_top_100_cnt(Integer rg_highest_top_100_cnt) {
		this.rg_highest_top_100_cnt = rg_highest_top_100_cnt;
	}
	public String[] getRg_intent_array() {
		return rg_intent_array;
	}
	public void setRg_intent_array(String[] rg_intent_array) {
		this.rg_intent_array = rg_intent_array;
	}
	public Integer[] getRg_intent_kwcnt_array() {
		return rg_intent_kwcnt_array;
	}
	public void setRg_intent_kwcnt_array(Integer[] rg_intent_kwcnt_array) {
		this.rg_intent_kwcnt_array = rg_intent_kwcnt_array;
	}
	public Float getRg_sov() {
		return rg_sov;
	}
	public void setRg_sov(Float rg_sov) {
		this.rg_sov = rg_sov;
	}
	public Integer getRg_sum_top1_avg_sv() {
		return rg_sum_top1_avg_sv;
	}
	public void setRg_sum_top1_avg_sv(Integer rg_sum_top1_avg_sv) {
		this.rg_sum_top1_avg_sv = rg_sum_top1_avg_sv;
	}
	public Integer getRg_sum_top1_est_traffic() {
		return rg_sum_top1_est_traffic;
	}
	public void setRg_sum_top1_est_traffic(Integer rg_sum_top1_est_traffic) {
		this.rg_sum_top1_est_traffic = rg_sum_top1_est_traffic;
	}
	public Integer getRg_sum_top10_avg_sv() {
		return rg_sum_top10_avg_sv;
	}
	public void setRg_sum_top10_avg_sv(Integer rg_sum_top10_avg_sv) {
		this.rg_sum_top10_avg_sv = rg_sum_top10_avg_sv;
	}
	public Integer getRg_sum_top10_est_traffic() {
		return rg_sum_top10_est_traffic;
	}
	public void setRg_sum_top10_est_traffic(Integer rg_sum_top10_est_traffic) {
		this.rg_sum_top10_est_traffic = rg_sum_top10_est_traffic;
	}
	public Integer getRg_sum_top100_avg_sv() {
		return rg_sum_top100_avg_sv;
	}
	public void setRg_sum_top100_avg_sv(Integer rg_sum_top100_avg_sv) {
		this.rg_sum_top100_avg_sv = rg_sum_top100_avg_sv;
	}
	public Integer getRg_sum_top2_avg_sv() {
		return rg_sum_top2_avg_sv;
	}
	public void setRg_sum_top2_avg_sv(Integer rg_sum_top2_avg_sv) {
		this.rg_sum_top2_avg_sv = rg_sum_top2_avg_sv;
	}
	public Integer getRg_sum_top2_est_traffic() {
		return rg_sum_top2_est_traffic;
	}
	public void setRg_sum_top2_est_traffic(Integer rg_sum_top2_est_traffic) {
		this.rg_sum_top2_est_traffic = rg_sum_top2_est_traffic;
	}
	public Integer getRg_sum_top20_avg_sv() {
		return rg_sum_top20_avg_sv;
	}
	public void setRg_sum_top20_avg_sv(Integer rg_sum_top20_avg_sv) {
		this.rg_sum_top20_avg_sv = rg_sum_top20_avg_sv;
	}
	public Integer getRg_sum_top3_avg_sv() {
		return rg_sum_top3_avg_sv;
	}
	public void setRg_sum_top3_avg_sv(Integer rg_sum_top3_avg_sv) {
		this.rg_sum_top3_avg_sv = rg_sum_top3_avg_sv;
	}
	public Integer getRg_sum_top3_est_traffic() {
		return rg_sum_top3_est_traffic;
	}
	public void setRg_sum_top3_est_traffic(Integer rg_sum_top3_est_traffic) {
		this.rg_sum_top3_est_traffic = rg_sum_top3_est_traffic;
	}
	public Integer getRg_sum_top30_avg_sv() {
		return rg_sum_top30_avg_sv;
	}
	public void setRg_sum_top30_avg_sv(Integer rg_sum_top30_avg_sv) {
		this.rg_sum_top30_avg_sv = rg_sum_top30_avg_sv;
	}
	public Integer getRg_top100cnt() {
		return rg_top100cnt;
	}
	public void setRg_top100cnt(Integer rg_top100cnt) {
		this.rg_top100cnt = rg_top100cnt;
	}
	public Integer getRg_top10cnt() {
		return rg_top10cnt;
	}
	public void setRg_top10cnt(Integer rg_top10cnt) {
		this.rg_top10cnt = rg_top10cnt;
	}
	public Integer getRg_top1cnt() {
		return rg_top1cnt;
	}
	public void setRg_top1cnt(Integer rg_top1cnt) {
		this.rg_top1cnt = rg_top1cnt;
	}
	public Integer getRg_top20cnt() {
		return rg_top20cnt;
	}
	public void setRg_top20cnt(Integer rg_top20cnt) {
		this.rg_top20cnt = rg_top20cnt;
	}
	public Integer getRg_top2cnt() {
		return rg_top2cnt;
	}
	public void setRg_top2cnt(Integer rg_top2cnt) {
		this.rg_top2cnt = rg_top2cnt;
	}
	public Integer getRg_top30cnt() {
		return rg_top30cnt;
	}
	public void setRg_top30cnt(Integer rg_top30cnt) {
		this.rg_top30cnt = rg_top30cnt;
	}
	public Integer getRg_top3cnt() {
		return rg_top3cnt;
	}
	public void setRg_top3cnt(Integer rg_top3cnt) {
		this.rg_top3cnt = rg_top3cnt;
	}
	public Integer getRg_total_rank() {
		return rg_total_rank;
	}
	public void setRg_total_rank(Integer rg_total_rank) {
		this.rg_total_rank = rg_total_rank;
	}
	public Integer getRg_traffic_potential() {
		return rg_traffic_potential;
	}
	public void setRg_traffic_potential(Integer rg_traffic_potential) {
		this.rg_traffic_potential = rg_traffic_potential;
	}
	public Integer getRg_typicalpage_strength() {
		return rg_typicalpage_strength;
	}
	public void setRg_typicalpage_strength(Integer rg_typicalpage_strength) {
		this.rg_typicalpage_strength = rg_typicalpage_strength;
	}
	public Float getRg_wtd_avg_rank() {
		return rg_wtd_avg_rank;
	}
	public void setRg_wtd_avg_rank(Float rg_wtd_avg_rank) {
		this.rg_wtd_avg_rank = rg_wtd_avg_rank;
	}
	public Integer getRg_wtd_vol_rank() {
		return rg_wtd_vol_rank;
	}
	public void setRg_wtd_vol_rank(Integer rg_wtd_vol_rank) {
		this.rg_wtd_vol_rank = rg_wtd_vol_rank;
	}
	public Float getRi_avg_rank() {
		return ri_avg_rank;
	}
	public void setRi_avg_rank(Float ri_avg_rank) {
		this.ri_avg_rank = ri_avg_rank;
	}
	public Integer getRi_mumofdays() {
		return ri_mumofdays;
	}
	public void setRi_mumofdays(Integer ri_mumofdays) {
		this.ri_mumofdays = ri_mumofdays;
	}
	public Integer getRi_top1() {
		return ri_top1;
	}
	public void setRi_top1(Integer ri_top1) {
		this.ri_top1 = ri_top1;
	}
	public Integer getRi_top10() {
		return ri_top10;
	}
	public void setRi_top10(Integer ri_top10) {
		this.ri_top10 = ri_top10;
	}
	public Integer getRi_top3() {
		return ri_top3;
	}
	public void setRi_top3(Integer ri_top3) {
		this.ri_top3 = ri_top3;
	}
	public Integer getRi_total_sv() {
		return ri_total_sv;
	}
	public void setRi_total_sv(Integer ri_total_sv) {
		this.ri_total_sv = ri_total_sv;
	}
	public Float getRi_wtd_avg_rank() {
		return ri_wtd_avg_rank;
	}
	public void setRi_wtd_avg_rank(Float ri_wtd_avg_rank) {
		this.ri_wtd_avg_rank = ri_wtd_avg_rank;
	}
	public String getRobots() {
		return robots;
	}
	public void setRobots(String robots) {
		this.robots = robots;
	}
	public String getRobots_contents() {
		return robots_contents;
	}
	public void setRobots_contents(String robots_contents) {
		this.robots_contents = robots_contents;
	}
	public Integer getRobots_contents_x_tag() {
		return robots_contents_x_tag;
	}
	public void setRobots_contents_x_tag(Integer robots_contents_x_tag) {
		this.robots_contents_x_tag = robots_contents_x_tag;
	}
	public String getRobots_flg() {
		return robots_flg;
	}
	public void setRobots_flg(String robots_flg) {
		this.robots_flg = robots_flg;
	}
	public String getRobots_flg_x_tag() {
		return robots_flg_x_tag;
	}
	public void setRobots_flg_x_tag(String robots_flg_x_tag) {
		this.robots_flg_x_tag = robots_flg_x_tag;
	}
	public Integer getSa_bounces() {
		return sa_bounces;
	}
	public void setSa_bounces(Integer sa_bounces) {
		this.sa_bounces = sa_bounces;
	}
	public Integer getSa_entrances() {
		return sa_entrances;
	}
	public void setSa_entrances(Integer sa_entrances) {
		this.sa_entrances = sa_entrances;
	}
	public Integer getSa_exits() {
		return sa_exits;
	}
	public void setSa_exits(Integer sa_exits) {
		this.sa_exits = sa_exits;
	}
	public Integer getSa_goal10completions() {
		return sa_goal10completions;
	}
	public void setSa_goal10completions(Integer sa_goal10completions) {
		this.sa_goal10completions = sa_goal10completions;
	}
	public Float getSa_goal10Value() {
		return sa_goal10Value;
	}
	public void setSa_goal10Value(Float sa_goal10Value) {
		this.sa_goal10Value = sa_goal10Value;
	}
	public Integer getSa_goal11completions() {
		return sa_goal11completions;
	}
	public void setSa_goal11completions(Integer sa_goal11completions) {
		this.sa_goal11completions = sa_goal11completions;
	}
	public Float getSa_goal11Value() {
		return sa_goal11Value;
	}
	public void setSa_goal11Value(Float sa_goal11Value) {
		this.sa_goal11Value = sa_goal11Value;
	}
	public Integer getSa_goal12completions() {
		return sa_goal12completions;
	}
	public void setSa_goal12completions(Integer sa_goal12completions) {
		this.sa_goal12completions = sa_goal12completions;
	}
	public Float getSa_goal12Value() {
		return sa_goal12Value;
	}
	public void setSa_goal12Value(Float sa_goal12Value) {
		this.sa_goal12Value = sa_goal12Value;
	}
	public Integer getSa_goal13completions() {
		return sa_goal13completions;
	}
	public void setSa_goal13completions(Integer sa_goal13completions) {
		this.sa_goal13completions = sa_goal13completions;
	}
	public Float getSa_goal13Value() {
		return sa_goal13Value;
	}
	public void setSa_goal13Value(Float sa_goal13Value) {
		this.sa_goal13Value = sa_goal13Value;
	}
	public Integer getSa_goal14completions() {
		return sa_goal14completions;
	}
	public void setSa_goal14completions(Integer sa_goal14completions) {
		this.sa_goal14completions = sa_goal14completions;
	}
	public Float getSa_goal14Value() {
		return sa_goal14Value;
	}
	public void setSa_goal14Value(Float sa_goal14Value) {
		this.sa_goal14Value = sa_goal14Value;
	}
	public Integer getSa_goal15completions() {
		return sa_goal15completions;
	}
	public void setSa_goal15completions(Integer sa_goal15completions) {
		this.sa_goal15completions = sa_goal15completions;
	}
	public Float getSa_goal15Value() {
		return sa_goal15Value;
	}
	public void setSa_goal15Value(Float sa_goal15Value) {
		this.sa_goal15Value = sa_goal15Value;
	}
	public Integer getSa_goal16completions() {
		return sa_goal16completions;
	}
	public void setSa_goal16completions(Integer sa_goal16completions) {
		this.sa_goal16completions = sa_goal16completions;
	}
	public Float getSa_goal16Value() {
		return sa_goal16Value;
	}
	public void setSa_goal16Value(Float sa_goal16Value) {
		this.sa_goal16Value = sa_goal16Value;
	}
	public Integer getSa_goal17completions() {
		return sa_goal17completions;
	}
	public void setSa_goal17completions(Integer sa_goal17completions) {
		this.sa_goal17completions = sa_goal17completions;
	}
	public Float getSa_goal17Value() {
		return sa_goal17Value;
	}
	public void setSa_goal17Value(Float sa_goal17Value) {
		this.sa_goal17Value = sa_goal17Value;
	}
	public Integer getSa_goal18completions() {
		return sa_goal18completions;
	}
	public void setSa_goal18completions(Integer sa_goal18completions) {
		this.sa_goal18completions = sa_goal18completions;
	}
	public Float getSa_goal18Value() {
		return sa_goal18Value;
	}
	public void setSa_goal18Value(Float sa_goal18Value) {
		this.sa_goal18Value = sa_goal18Value;
	}
	public Integer getSa_goal19completions() {
		return sa_goal19completions;
	}
	public void setSa_goal19completions(Integer sa_goal19completions) {
		this.sa_goal19completions = sa_goal19completions;
	}
	public Float getSa_goal19Value() {
		return sa_goal19Value;
	}
	public void setSa_goal19Value(Float sa_goal19Value) {
		this.sa_goal19Value = sa_goal19Value;
	}
	public Integer getSa_goal1completions() {
		return sa_goal1completions;
	}
	public void setSa_goal1completions(Integer sa_goal1completions) {
		this.sa_goal1completions = sa_goal1completions;
	}
	public Float getSa_goal1Value() {
		return sa_goal1Value;
	}
	public void setSa_goal1Value(Float sa_goal1Value) {
		this.sa_goal1Value = sa_goal1Value;
	}
	public Integer getSa_goal20completions() {
		return sa_goal20completions;
	}
	public void setSa_goal20completions(Integer sa_goal20completions) {
		this.sa_goal20completions = sa_goal20completions;
	}
	public Float getSa_goal20Value() {
		return sa_goal20Value;
	}
	public void setSa_goal20Value(Float sa_goal20Value) {
		this.sa_goal20Value = sa_goal20Value;
	}
	public Integer getSa_goal2completions() {
		return sa_goal2completions;
	}
	public void setSa_goal2completions(Integer sa_goal2completions) {
		this.sa_goal2completions = sa_goal2completions;
	}
	public Float getSa_goal2Value() {
		return sa_goal2Value;
	}
	public void setSa_goal2Value(Float sa_goal2Value) {
		this.sa_goal2Value = sa_goal2Value;
	}
	public Integer getSa_goal3completions() {
		return sa_goal3completions;
	}
	public void setSa_goal3completions(Integer sa_goal3completions) {
		this.sa_goal3completions = sa_goal3completions;
	}
	public Float getSa_goal3Value() {
		return sa_goal3Value;
	}
	public void setSa_goal3Value(Float sa_goal3Value) {
		this.sa_goal3Value = sa_goal3Value;
	}
	public Integer getSa_goal4completions() {
		return sa_goal4completions;
	}
	public void setSa_goal4completions(Integer sa_goal4completions) {
		this.sa_goal4completions = sa_goal4completions;
	}
	public Float getSa_goal4Value() {
		return sa_goal4Value;
	}
	public void setSa_goal4Value(Float sa_goal4Value) {
		this.sa_goal4Value = sa_goal4Value;
	}
	public Integer getSa_goal5completions() {
		return sa_goal5completions;
	}
	public void setSa_goal5completions(Integer sa_goal5completions) {
		this.sa_goal5completions = sa_goal5completions;
	}
	public Float getSa_goal5Value() {
		return sa_goal5Value;
	}
	public void setSa_goal5Value(Float sa_goal5Value) {
		this.sa_goal5Value = sa_goal5Value;
	}
	public Integer getSa_goal6completions() {
		return sa_goal6completions;
	}
	public void setSa_goal6completions(Integer sa_goal6completions) {
		this.sa_goal6completions = sa_goal6completions;
	}
	public Float getSa_goal6Value() {
		return sa_goal6Value;
	}
	public void setSa_goal6Value(Float sa_goal6Value) {
		this.sa_goal6Value = sa_goal6Value;
	}
	public Integer getSa_goal7completions() {
		return sa_goal7completions;
	}
	public void setSa_goal7completions(Integer sa_goal7completions) {
		this.sa_goal7completions = sa_goal7completions;
	}
	public Float getSa_goal7Value() {
		return sa_goal7Value;
	}
	public void setSa_goal7Value(Float sa_goal7Value) {
		this.sa_goal7Value = sa_goal7Value;
	}
	public Integer getSa_goal8completions() {
		return sa_goal8completions;
	}
	public void setSa_goal8completions(Integer sa_goal8completions) {
		this.sa_goal8completions = sa_goal8completions;
	}
	public Float getSa_goal8Value() {
		return sa_goal8Value;
	}
	public void setSa_goal8Value(Float sa_goal8Value) {
		this.sa_goal8Value = sa_goal8Value;
	}
	public Integer getSa_goal9completions() {
		return sa_goal9completions;
	}
	public void setSa_goal9completions(Integer sa_goal9completions) {
		this.sa_goal9completions = sa_goal9completions;
	}
	public Float getSa_goal9Value() {
		return sa_goal9Value;
	}
	public void setSa_goal9Value(Float sa_goal9Value) {
		this.sa_goal9Value = sa_goal9Value;
	}
	public Float getSa_item_revenue() {
		return sa_item_revenue;
	}
	public void setSa_item_revenue(Float sa_item_revenue) {
		this.sa_item_revenue = sa_item_revenue;
	}
	public Integer getSa_pageviews() {
		return sa_pageviews;
	}
	public void setSa_pageviews(Integer sa_pageviews) {
		this.sa_pageviews = sa_pageviews;
	}
	public Float getSa_session_duration() {
		return sa_session_duration;
	}
	public void setSa_session_duration(Float sa_session_duration) {
		this.sa_session_duration = sa_session_duration;
	}
	public Float getSa_time_on_page() {
		return sa_time_on_page;
	}
	public void setSa_time_on_page(Float sa_time_on_page) {
		this.sa_time_on_page = sa_time_on_page;
	}
	public Integer getSa_transactions() {
		return sa_transactions;
	}
	public void setSa_transactions(Integer sa_transactions) {
		this.sa_transactions = sa_transactions;
	}
	public Integer getSearchanlytics_numofdays() {
		return searchanlytics_numofdays;
	}
	public void setSearchanlytics_numofdays(Integer searchanlytics_numofdays) {
		this.searchanlytics_numofdays = searchanlytics_numofdays;
	}
	public Float getServer_response_time() {
		return server_response_time;
	}
	public void setServer_response_time(Float server_response_time) {
		this.server_response_time = server_response_time;
	}
	public Integer getSiteanalytics_numofdays() {
		return siteanalytics_numofdays;
	}
	public void setSiteanalytics_numofdays(Integer siteanalytics_numofdays) {
		this.siteanalytics_numofdays = siteanalytics_numofdays;
	}
	public String getSource_url() {
		return source_url;
	}
	public void setSource_url(String source_url) {
		this.source_url = source_url;
	}
	public String[] getSources() {
		return sources;
	}
	public void setSources(String[] sources) {
		this.sources = sources;
	}
	public String getSplash_took() {
		return splash_took;
	}
	public void setSplash_took(String splash_took) {
		this.splash_took = splash_took;
	}
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	public String[] getStructure_schema_errors_encoding() {
		return structure_schema_errors_encoding;
	}
	public void setStructure_schema_errors_encoding(String[] structure_schema_errors_encoding) {
		this.structure_schema_errors_encoding = structure_schema_errors_encoding;
	}
	public String[] getStructure_schema_errors_error_type() {
		return structure_schema_errors_error_type;
	}
	public void setStructure_schema_errors_error_type(String[] structure_schema_errors_error_type) {
		this.structure_schema_errors_error_type = structure_schema_errors_error_type;
	}
	public String[] getStructure_schema_errors_info() {
		return structure_schema_errors_info;
	}
	public void setStructure_schema_errors_info(String[] structure_schema_errors_info) {
		this.structure_schema_errors_info = structure_schema_errors_info;
	}
	public String[] getStructure_schema_errors_message() {
		return structure_schema_errors_message;
	}
	public void setStructure_schema_errors_message(String[] structure_schema_errors_message) {
		this.structure_schema_errors_message = structure_schema_errors_message;
	}
	public String[] getStructure_schema_errors_path() {
		return structure_schema_errors_path;
	}
	public void setStructure_schema_errors_path(String[] structure_schema_errors_path) {
		this.structure_schema_errors_path = structure_schema_errors_path;
	}
	public String[] getStructure_schema_errors_schema_type() {
		return structure_schema_errors_schema_type;
	}
	public void setStructure_schema_errors_schema_type(String[] structure_schema_errors_schema_type) {
		this.structure_schema_errors_schema_type = structure_schema_errors_schema_type;
	}
	public String[] getStructure_schema_warnings_encoding() {
		return structure_schema_warnings_encoding;
	}
	public void setStructure_schema_warnings_encoding(String[] structure_schema_warnings_encoding) {
		this.structure_schema_warnings_encoding = structure_schema_warnings_encoding;
	}
	public String[] getStructure_schema_warnings_info() {
		return structure_schema_warnings_info;
	}
	public void setStructure_schema_warnings_info(String[] structure_schema_warnings_info) {
		this.structure_schema_warnings_info = structure_schema_warnings_info;
	}
	public String[] getStructure_schema_warnings_message() {
		return structure_schema_warnings_message;
	}
	public void setStructure_schema_warnings_message(String[] structure_schema_warnings_message) {
		this.structure_schema_warnings_message = structure_schema_warnings_message;
	}
	public String[] getStructure_schema_warnings_path() {
		return structure_schema_warnings_path;
	}
	public void setStructure_schema_warnings_path(String[] structure_schema_warnings_path) {
		this.structure_schema_warnings_path = structure_schema_warnings_path;
	}
	public String[] getStructure_schema_warnings_schema_type() {
		return structure_schema_warnings_schema_type;
	}
	public void setStructure_schema_warnings_schema_type(String[] structure_schema_warnings_schema_type) {
		this.structure_schema_warnings_schema_type = structure_schema_warnings_schema_type;
	}
	public String[] getStructure_schema_warnings_warning_type() {
		return structure_schema_warnings_warning_type;
	}
	public void setStructure_schema_warnings_warning_type(String[] structure_schema_warnings_warning_type) {
		this.structure_schema_warnings_warning_type = structure_schema_warnings_warning_type;
	}
	public String getStructured_data() {
		return structured_data;
	}
	public void setStructured_data(String structured_data) {
		this.structured_data = structured_data;
	}
	public String[] getStructured_schema_encoding() {
		return structured_schema_encoding;
	}
	public void setStructured_schema_encoding(String[] structured_schema_encoding) {
		this.structured_schema_encoding = structured_schema_encoding;
	}
	public String[] getStructured_schema_type() {
		return structured_schema_type;
	}
	public void setStructured_schema_type(String[] structured_schema_type) {
		this.structured_schema_type = structured_schema_type;
	}
	public String getTarget_date() {
		return target_date;
	}
	public void setTarget_date(String target_date) {
		this.target_date = target_date;
	}
	public String getTitle() {
		return title;
	}
	public void setTitle(String title) {
		this.title = title;
	}
	public String getTitle_flg() {
		return title_flg;
	}
	public void setTitle_flg(String title_flg) {
		this.title_flg = title_flg;
	}
	public Integer getTitle_length() {
		return title_length;
	}
	public void setTitle_length(Integer title_length) {
		this.title_length = title_length;
	}
	public String getTitle_md5() {
		return title_md5;
	}
	public void setTitle_md5(String title_md5) {
		this.title_md5 = title_md5;
	}
	public String getTitle_murmurhash() {
		return title_murmurhash;
	}
	public void setTitle_murmurhash(String title_murmurhash) {
		this.title_murmurhash = title_murmurhash;
	}
	public String getTitle_simhash() {
		return title_simhash;
	}
	public void setTitle_simhash(String title_simhash) {
		this.title_simhash = title_simhash;
	}
	public String[] getTitle_stemmed() {
		return title_stemmed;
	}
	public void setTitle_stemmed(String[] title_stemmed) {
		this.title_stemmed = title_stemmed;
	}
	public Integer getTwitter_description_length() {
		return twitter_description_length;
	}
	public void setTwitter_description_length(Integer twitter_description_length) {
		this.twitter_description_length = twitter_description_length;
	}
	public String getTwitter_markup() {
		return twitter_markup;
	}
	public void setTwitter_markup(String twitter_markup) {
		this.twitter_markup = twitter_markup;
	}
	public String[] getTwitter_markup_content() {
		return twitter_markup_content;
	}
	public void setTwitter_markup_content(String[] twitter_markup_content) {
		this.twitter_markup_content = twitter_markup_content;
	}
	public Integer getTwitter_markup_flag() {
		return twitter_markup_flag;
	}
	public void setTwitter_markup_flag(Integer twitter_markup_flag) {
		this.twitter_markup_flag = twitter_markup_flag;
	}
	public Integer getTwitter_markup_length() {
		return twitter_markup_length;
	}
	public void setTwitter_markup_length(Integer twitter_markup_length) {
		this.twitter_markup_length = twitter_markup_length;
	}
	public String[] getTwitter_markup_property() {
		return twitter_markup_property;
	}
	public void setTwitter_markup_property(String[] twitter_markup_property) {
		this.twitter_markup_property = twitter_markup_property;
	}
	public String getUrl() {
		return url;
	}
	public void setUrl(String url) {
		this.url = url;
	}
	public Integer getUrl_length() {
		return url_length;
	}
	public void setUrl_length(Integer url_length) {
		this.url_length = url_length;
	}
	public String getValid_twitter_card() {
		return valid_twitter_card;
	}
	public void setValid_twitter_card(String valid_twitter_card) {
		this.valid_twitter_card = valid_twitter_card;
	}
	public String getViewport_content() {
		return viewport_content;
	}
	public void setViewport_content(String viewport_content) {
		this.viewport_content = viewport_content;
	}
	public Integer getViewport_flag() {
		return viewport_flag;
	}
	public void setViewport_flag(Integer viewport_flag) {
		this.viewport_flag = viewport_flag;
	}
	public String[] getRoot_sitemap_hash_array() {
		return root_sitemap_hash_array;
	}
	public void setRoot_sitemap_hash_array(String[] root_sitemap_hash_array) {
		this.root_sitemap_hash_array = root_sitemap_hash_array;
	}
	
	public String getSource() {
		return source;
	}
	public void setSource(String source) {
		this.source = source;
	}
	public Integer getCnt() {
		return cnt;
	}
	public void setCnt(Integer cnt) {
		this.cnt = cnt;
	}


}
