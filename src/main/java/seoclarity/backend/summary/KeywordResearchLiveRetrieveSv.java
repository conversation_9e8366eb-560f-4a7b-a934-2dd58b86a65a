package seoclarity.backend.summary;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import lombok.extern.apachecommons.CommonsLog;
import okhttp3.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import seoclarity.backend.dao.rankcheck.AdwordsGeoIdDAO;
import seoclarity.backend.dao.actonia.MonthlySenderInfoEntityDAO;
import seoclarity.backend.dao.actonia.liveresearch.KeywordLiveResearchDetailEntityDAO;
import seoclarity.backend.dao.actonia.liveresearch.KeywordLiveResearchProjectEntityDAO;
import seoclarity.backend.dao.actonia.liveresearch.KeywordSvRetrieveQueueDAO;
import seoclarity.backend.dao.clickhouse.suggestkeyword.KeywordResearchClarityDBDAO;
import seoclarity.backend.dao.clickhouse.suggestkeyword.KeywordResearchSVClarityDBDAO;
import seoclarity.backend.entity.WordTokenizerEntity;
import seoclarity.backend.entity.actonia.AdwordsGeoIdTableEntity;
import seoclarity.backend.entity.actonia.adhoc.AutoAdhocRankKeywordSVEntity;
import seoclarity.backend.entity.actonia.liveresearch.KeywordLiveResearchProjectEntity;
import seoclarity.backend.entity.actonia.liveresearch.KeywordSvRetrieveQueueEntity;
import seoclarity.backend.entity.googlesuggest.KeywordResearchEntity;
import seoclarity.backend.entity.googlesuggest.KeywordResearchSVEntity;
import seoclarity.backend.service.ScKeywordSearchVolumeManager;
import seoclarity.backend.utils.EmailSenderComponent;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.utils.ZeptoMailSenderComponent;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * https://www.wrike.com/open.htm?id=592897417
 */

@CommonsLog
public class KeywordResearchLiveRetrieveSv {

    private static final String SOURCE_KEYWORD_SPLIT = "!_!";
    private static final int RETRY_COUNT = 3;
    private static final String postUrl = "https://api.dataforseo.com/v3/keywords_data/google/search_volume/task_post";
    private static final String getUrl = "https://api.dataforseo.com/v3/keywords_data/google/search_volume/tasks_ready";
    private static final String apiUrl = "https://api.dataforseo.com/v3/keywords_data/google/search_volume/task_get/%s";
    private static final String credential = Credentials.basic("<EMAIL>", "TmjH1hLcIGqUqoTk");
    SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmss");

    private static final String METRICS_NETWORK = "googlesearchnetwork";

    private static int REQUEST_KEYWORD_COUNT = 700;
    private static int LOCATION_ID = 0;

    private KeywordSvRetrieveQueueDAO keywordSvRetrieveQueueDAO;
    private KeywordLiveResearchProjectEntityDAO keywordLiveResearchProjectEntityDAO;
    private KeywordLiveResearchDetailEntityDAO keywordLiveResearchDetailEntityDAO;
    private AdwordsGeoIdDAO adwordsGeoIdDAO;
    private KeywordResearchClarityDBDAO keywordResearchClarityDBDAO;
    private KeywordResearchSVClarityDBDAO keywordResearchSVClarityDBDAO;
    private MonthlySenderInfoEntityDAO monthlySenderInfoEntityDAO;
    private ScKeywordSearchVolumeManager scKeywordSearchVolumeManager;
    private EmailSenderComponent emailSenderComponent;
    private ZeptoMailSenderComponent zeptoMailSenderComponent;

    public static Map<String, String> languageNameMap = new LinkedHashMap<String, String>();

    public KeywordResearchLiveRetrieveSv() {
        keywordSvRetrieveQueueDAO = SpringBeanFactory.getBean("keywordSvRetrieveQueueDAO");
        keywordLiveResearchProjectEntityDAO = SpringBeanFactory.getBean("keywordLiveResearchProjectEntityDAO");
        keywordLiveResearchDetailEntityDAO = SpringBeanFactory.getBean("keywordLiveResearchDetailEntityDAO");
        adwordsGeoIdDAO = SpringBeanFactory.getBean("adwordsGeoIdDAO");
        keywordResearchClarityDBDAO = SpringBeanFactory.getBean("keywordResearchClarityDBDAO");
        keywordResearchSVClarityDBDAO = SpringBeanFactory.getBean("keywordResearchSVClarityDBDAO");
        monthlySenderInfoEntityDAO = SpringBeanFactory.getBean("monthlySenderInfoEntityDAO");
        scKeywordSearchVolumeManager = SpringBeanFactory.getBean("scKeywordSearchVolumeManager");
        emailSenderComponent = SpringBeanFactory.getBean("emailSenderComponent");
        zeptoMailSenderComponent = SpringBeanFactory.getBean("zeptoMailSenderComponent");

        languageNameMap.put("ar", "Arabic");
        languageNameMap.put("da", "Danish");
        languageNameMap.put("de", "German");
        languageNameMap.put("en", "English");
        languageNameMap.put("es", "Spanish");
        languageNameMap.put("fi", "Finnish");
        languageNameMap.put("fr", "French");
        languageNameMap.put("it", "Italian");
        languageNameMap.put("ja", "Japanese");
        languageNameMap.put("lb", "German");
        languageNameMap.put("mk", "English");
        languageNameMap.put("nl", "Dutch");
        languageNameMap.put("pt", "Portuguese");
        languageNameMap.put("ru", "Russian");
        languageNameMap.put("sk", "Polish");
        languageNameMap.put("sv", "Swedish");
        languageNameMap.put("zh", "Chinese");
    }

    public static void main(String[] args) {

//        getResult("07150110-0351-0110-0000-822b4677bb6b");
//        List<KeywordResearchEntity> list = new ArrayList<>();
//        KeywordResearchEntity keywordResearchEntity = new KeywordResearchEntity();
//        keywordResearchEntity.setOwnDomainId(11);
//        keywordResearchEntity.setProjectId(1);
//        list.add(keywordResearchEntity);
//
//        KeywordResearchEntity keywordResearchEntity1 = new KeywordResearchEntity();
//        BeanUtils.copyProperties(keywordResearchEntity,keywordResearchEntity1);
//        keywordResearchEntity1.setOwnDomainId(2);
//
//        list.add(keywordResearchEntity1);
//        log.info("===" + new Gson().toJson(list));

        KeywordResearchLiveRetrieveSv keywordResearchLiveRetrieveSv = new KeywordResearchLiveRetrieveSv();

        if (args.length > 0 && args[0] != null) {
            keywordResearchLiveRetrieveSv.insertLocalKeywordResearchTest();
        } else {
            keywordResearchLiveRetrieveSv.process();
        }

//        adhocRetrieveSv.test();
    }

    private void insertLocalKeywordResearchTest() {

        List<KeywordResearchEntity> insertList = new ArrayList<>();

        for (int i = 0; i <= 10; i++) {
            KeywordResearchEntity keywordResearchEntity = new KeywordResearchEntity();
            keywordResearchEntity.setOwnDomainId(4);
            keywordResearchEntity.setProjectId(100);
            keywordResearchEntity.setEngineId(1);
            keywordResearchEntity.setLanguageId(1);
            keywordResearchEntity.setLocationId(0);
            keywordResearchEntity.setKeywordName("apple" + i);
            keywordResearchEntity.setSourceKeyword("apple");
            //keywordResearchEntity.setCrawlDate("2020-11-16");
            //keywordResearchEntity.setRequestDate("2020-11-16");
            //keywordResearchEntity.setCrawlDate("2020-11-16 15:41:22");
            //keywordResearchEntity.setRequestDate("2020-11-16");
            keywordResearchEntity.setDataSource(1);
            keywordResearchEntity.setSequenceNo(0);
            keywordResearchEntity.setWord(new String[]{});
            keywordResearchEntity.setStream(new String[]{});
            keywordResearchEntity.setKeywordVariationNgram(new String[]{});
            keywordResearchEntity.setKeywordVariationOneword(new String[]{});
            keywordResearchEntity.setSign(0);
            insertList.add(keywordResearchEntity);
        }

        keywordResearchClarityDBDAO.insertBatch(insertList);
    }

    private void insertLocalKeywordResearchSVTest() {

        List<KeywordResearchSVEntity> insertList = new ArrayList<>();

        for (int i = 0; i <= 5; i++) {
            KeywordResearchSVEntity keywordResearchSVEntity = new KeywordResearchSVEntity();
            keywordResearchSVEntity.setOwnDomainId(4);
            keywordResearchSVEntity.setProjectId(100);
            keywordResearchSVEntity.setEngineId(1);
            keywordResearchSVEntity.setLanguageId(1);
            keywordResearchSVEntity.setLocationId(0);
            keywordResearchSVEntity.setKeywordName("apple" + i);
            keywordResearchSVEntity.setSourceKeyword("apple");
            insertList.add(keywordResearchSVEntity);
        }

        keywordResearchSVClarityDBDAO.insertBatch(insertList);
    }

    private void test() {
        try {
            List<String> keywordList = new ArrayList<>();
            keywordList.add(URLEncoder.encode("Cameras deals", "utf-8"));
            keywordList.add(URLEncoder.encode("Optics deals", "utf-8"));
            keywordList.add(URLEncoder.encode("Camera Accessories deals", "utf-8"));
            keywordList.add(URLEncoder.encode("Photography deals", "utf-8"));
            Integer criteriaId = 2840;
//            List<KeywordResultSv>  responseSvList = getResult(keywordList, criteriaId);
//            log.info("==responseSvList count:" + responseSvList.size());
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    private void process() {
        Date date = new Date();
        Integer retrieveDate = FormatUtils.formatDateToYyyyMmDd(date);

        List<KeywordLiveResearchProjectEntity> processProjectIdList = keywordLiveResearchProjectEntityDAO.getNeedRetrieveSvProject();
        log.info("====processProjectIdList size:" + processProjectIdList.size());

        Map<String, KeywordSvRetrieveQueueEntity> keywordSvMap = new HashMap<>();
        List<Result> allKeywordResultSvList = new ArrayList<>();

//        int lastCriteriaId = 0;
        for (KeywordLiveResearchProjectEntity keywordLiveResearchProjectEntity : processProjectIdList) {

            List<KeywordSvRetrieveQueueEntity> retrieveKwSvList = new ArrayList<>();
            List<Integer> statusList = new ArrayList<>();

            int projectId = keywordLiveResearchProjectEntity.getId();
            int engineId = keywordLiveResearchProjectEntity.getSearchEngineId();
            int languageId = keywordLiveResearchProjectEntity.getLanguageId();
            int svRetrieveStatus = keywordLiveResearchProjectEntity.getSvRetrieveStatus();

            if (svRetrieveStatus == KeywordLiveResearchProjectEntity.RETRIEVESV_STATUS_NOT_STARTED) {
                List<KeywordResearchEntity> retrieveSvList = keywordResearchClarityDBDAO.getNeedRetrieveSvKeywordList(projectId);
                // insert mysql
                List<KeywordSvRetrieveQueueEntity> keywordSvRetrieveQueueList = new ArrayList<>();
                Map<String, KeywordSvRetrieveQueueEntity> keywordMap = new HashedMap();

                for (KeywordResearchEntity keywordResearchEntity : retrieveSvList) {
                    KeywordSvRetrieveQueueEntity keywordSvRetrieveQueueEntity = new KeywordSvRetrieveQueueEntity();
                    keywordSvRetrieveQueueEntity.setOwnDomainId(keywordResearchEntity.getOwnDomainId());
                    keywordSvRetrieveQueueEntity.setKeywordType(KeywordSvRetrieveQueueEntity.KEYWORD_TYPE_LIVE_RESEARCH);
                    keywordSvRetrieveQueueEntity.setProjectId(projectId);
                    keywordSvRetrieveQueueEntity.setSearchEngineId(engineId);
                    keywordSvRetrieveQueueEntity.setLanguageId(languageId);
                    keywordSvRetrieveQueueEntity.setKeywordName(keywordResearchEntity.getKeywordName());
                    keywordSvRetrieveQueueEntity.setClarityDBKeywordHash(keywordResearchEntity.getKeywordHash());
                    keywordSvRetrieveQueueEntity.setSourceKeyword(keywordResearchEntity.getSourceKeyword());
                    keywordSvRetrieveQueueEntity.setSvRetrieveStatus(KeywordSvRetrieveQueueEntity.SV_RETRIEVE_STATUS_NOT_START);
                    keywordSvRetrieveQueueList.add(keywordSvRetrieveQueueEntity);
                }
                keywordSvRetrieveQueueDAO.batchInsert(keywordSvRetrieveQueueList);

                statusList.add(KeywordLiveResearchProjectEntity.RETRIEVESV_STATUS_NOT_STARTED);
                retrieveKwSvList = keywordSvRetrieveQueueDAO.getNeedRetrieveSvByStatus(projectId, statusList);
                keywordLiveResearchProjectEntityDAO.updateSvStatusById(projectId, KeywordLiveResearchProjectEntity.RETRIEVESV_STATUS_PROCESSING);

            } else if (svRetrieveStatus == KeywordLiveResearchProjectEntity.RETRIEVESV_STATUS_PROCESSING) {
                statusList.add(KeywordLiveResearchProjectEntity.RETRIEVESV_STATUS_NOT_STARTED);
                statusList.add(KeywordLiveResearchProjectEntity.RETRIEVESV_STATUS_ERROR);
                retrieveKwSvList = keywordSvRetrieveQueueDAO.getNeedRetrieveSvByStatus(projectId, statusList);
            }

            AdwordsGeoIdTableEntity adwordsGeoIdTableEntity = adwordsGeoIdDAO.findCriteriaByCountryCode(keywordLiveResearchProjectEntity.getCountryCode());
            Integer criteriaId = adwordsGeoIdTableEntity.getCriteriaId();
            if (criteriaId == null) {
                log.error("criteriaId is null , skip , projectId : " + projectId);
                continue;
            }

//            MonthlySenderInfoEntity monthlySenderInfoEntity = monthlySenderInfoEntityDAO.getByEngineAndLanguage(engineId, languageId);
//            if (monthlySenderInfoEntity == null) {
//                log.error("===can not find sendInfo engine :" + engineId + ",language:" + languageId);
//                continue;
//            }
//
//            String code = monthlySenderInfoEntity.getCountryName();
//            String languageName = monthlySenderInfoEntity.getLanguageName();
//
//            String fullLanguageName = languageNameMap.get(languageName);
//            if (StringUtils.isBlank(fullLanguageName)) {
//                log.error("=======not find fullLanguageName using English :" + languageName);
//                fullLanguageName = "English";
//            }


            log.info("=====start project : " + projectId);
            //update not update sv by taskId
            updateSVByTaskId(projectId);

            try {

                List<String> processing = new ArrayList<>();
                List<String> processingKeywordHashList = new ArrayList<>();
//                List<Long> ids = new ArrayList<>();
                System.out.println("==========================retrieveKwSvList size:" + retrieveKwSvList.size());
                for (KeywordSvRetrieveQueueEntity keywordSvRetrieveQueueEntity : retrieveKwSvList) {

                    if (StringUtils.isNotBlank(keywordSvRetrieveQueueEntity.getSvRetrieveTaskId())) {
                        continue;
                    }

                    String keywordName = keywordSvRetrieveQueueEntity.getKeywordName();
//                    keywordSvRetrieveQueueEntity.setLanguageName(fullLanguageName);
//                    keywordSvRetrieveQueueEntity.setCountryCode(code);

                    try {
//                        ids.add(keywordSvRetrieveQueueEntity.getId());
                        String encodeKeywordName = URLEncoder.encode(keywordSvRetrieveQueueEntity.getKeywordName(), "utf-8");
                        keywordSvMap.put(encodeKeywordName, keywordSvRetrieveQueueEntity);
                        processing.add(URLEncoder.encode(keywordName, "utf-8"));
                        processingKeywordHashList.add(keywordSvRetrieveQueueEntity.getClarityDBKeywordHash());

                        if (processing.size() >= REQUEST_KEYWORD_COUNT) {
                            System.out.println("===processing size:" + processing.size());

                            allKeywordResultSvList.addAll(getSVByApi(processing, criteriaId, date,
                                    projectId, engineId, languageId, processingKeywordHashList));

                            processing.clear();
//                            ids.clear();
                            processingKeywordHashList.clear();
                            System.out.println("===clear");
                        }
                    } catch (Exception ex) {
                        ex.printStackTrace();
                        String message = "KeywordSuggest RetrieveSV Failed projectId:" + projectId;
                        sendMailReport("Failed", message, projectId);
                        log.error("===svEntityList error:" + projectId + ",processProjectIdList: " + processProjectIdList.size());
                    }
                }


                if (CollectionUtils.isNotEmpty(processing) && CollectionUtils.isNotEmpty(processingKeywordHashList)) {
                    System.out.println("==============<700 processing size:" + processing.size());
                    allKeywordResultSvList.addAll(getSVByApi(processing, criteriaId, date,
                            projectId, engineId, languageId, processingKeywordHashList));
                }
            } catch (Exception e) {
                e.printStackTrace();
                log.error("===processProjectIdList error:" + projectId);
            }

        }

        updateSvColumns(keywordSvMap, allKeywordResultSvList);

    }

    private List<Result> getSVByApi(List<String> processing, int criteriaId, Date date,
                                    int projectId, int engineId, int languageId, List<String> keywordHashList) throws Exception {


        List<Result> allKeywordResultSvList = new ArrayList<>();
        int retry = 0;
        String result = null;

        while (result == null && retry <= RETRY_COUNT) {

            List<Result> responseSvList = new ArrayList<>();
            System.out.println("===retry：" + retry);
            result = postUrl(processing, criteriaId);
            System.out.println("===out：" + result);

            if (result != null) {
                keywordSvRetrieveQueueDAO.updateSv(AutoAdhocRankKeywordSVEntity.SV_RETRIEVE_STATUS_PROCESSING, date,
                        formatter.format(new Date()), result, projectId, engineId, languageId, keywordHashList);
                System.out.println("====:updateSv:" + result);
                Thread.sleep(10 * 1000);
                List<String> list = getUrl();
                System.out.println("===getUrlSuc");
                if (list != null && list.contains(result)) {
                    responseSvList = getResult(result);
                    if (CollectionUtils.isNotEmpty(responseSvList)) {
                        System.out.println("===getResultSuc");
                        allKeywordResultSvList.addAll(responseSvList);
                        System.out.println("===allKeywordResultSvList: " + allKeywordResultSvList.size());
                    } else {
                        System.out.println("===getResult:" + JSON.toJSONString(responseSvList));
                    }
                    break;
                } else {
                    System.out.println("===sleep 60s try again");
                    Thread.sleep(60 * 1000);
                    list = getUrl();
                    System.out.println("===two getUrlSuc");
                    if (list != null && list.contains(result)) {
                        responseSvList = getResult(result);
                        if (CollectionUtils.isNotEmpty(responseSvList)) {
                            System.out.println("===two getResultSuc");
                            allKeywordResultSvList.addAll(responseSvList);
                            System.out.println("===allKeywordResultSvList: " + allKeywordResultSvList.size());
                        } else {
                            System.out.println("===getResult:" + JSON.toJSONString(responseSvList));
                        }
                        break;
                    }
                }
            }

            retry++;
        }

        return allKeywordResultSvList;
    }

    private void updateSVByTaskId(int projectId) {

        Map<String, KeywordSvRetrieveQueueEntity> keywordSVMap = new HashMap<>();
        Set<String> taskIdSet = new HashSet<>();
        List<KeywordSvRetrieveQueueEntity> svEntityList = keywordSvRetrieveQueueDAO.getNotUpdateSVList(projectId);
        log.info("===updateSVByTaskId:" + projectId + ",svEntityList:" + svEntityList.size());
        if (CollectionUtils.isNotEmpty(svEntityList)) {
            for (KeywordSvRetrieveQueueEntity keywordSvRetrieveQueueEntity : svEntityList) {

                try {
                    taskIdSet.add(keywordSvRetrieveQueueEntity.getSvRetrieveTaskId());
                    String encodeKeywordName = URLEncoder.encode(keywordSvRetrieveQueueEntity.getKeywordName(), "utf-8");
                    keywordSVMap.put(encodeKeywordName, keywordSvRetrieveQueueEntity);
                } catch (Exception e) {
                    e.printStackTrace();
                    continue;
                }

            }
        }

        List<Result> allKeywordResultSvList = new ArrayList<>();
        for (String taskId : taskIdSet) {
            allKeywordResultSvList.addAll(getResult(taskId));
        }

        updateSvColumns(keywordSVMap, allKeywordResultSvList);

    }

    private static String postUrl(List<String> keywordList, int location) throws UnsupportedEncodingException {
        System.out.println("===postUrl");
        MediaType mediaType = MediaType.parse("text/x-markdown; charset=utf-8");

        OkHttpClient client = new OkHttpClient();

        HashMap<String, Object> parameters = new HashMap<>();
        parameters.put("keywords", new Gson().toJson(keywordList));
        parameters.put("location_code", location);

        String requestBody = generateParametersForPost(parameters);

        System.out.println(requestBody);

        Request request = new Request.Builder()
                .url(postUrl)
                .header("Authorization", credential)
                .post(RequestBody.create(mediaType, requestBody))
                .build();
        Call call = client.newCall(request);
        try {
            Response response = call.execute();
            //判断是否成功
            if (response.isSuccessful()) {
                JSONObject jsonObject = JSONObject.parseObject(Objects.requireNonNull(response.body()).string());
                String length = jsonObject.get("tasks").toString();
                JSONObject tasks = JSONObject.parseObject(length.substring(1, length.length() - 1));
                return tasks.get("id").toString();
            } else {
                System.out.println("post请求失败");
            }
        } catch (Exception e) {
            System.out.println("post异常");
            e.printStackTrace();
        }

        return null;
    }

    private void updateSvColumns(Map<String, KeywordSvRetrieveQueueEntity> keywordSvMap, List<Result> allKeywordResultSvList) {

        log.info("===keywordSvMap: " + keywordSvMap.size());
        log.info("===allKeywordResultSvList: " + allKeywordResultSvList.size());

        List<KeywordResearchSVEntity> insertList = new ArrayList<>();
        List<KeywordSvRetrieveQueueEntity> updateList = new ArrayList<>();

        Set<Integer> projectSet = new HashSet<>();

        for (String key : keywordSvMap.keySet()) {

            try {

                for (Result result : allKeywordResultSvList) {

                    if (key.equalsIgnoreCase(result.getKeyword())) {
//                if (key.equalsIgnoreCase(result.getKeyword())) {
//                        System.out.println("=========in:" + key);
                        Date now = new Date();
                        KeywordSvRetrieveQueueEntity keywordSvRetrieveQueueEntity = keywordSvMap.get(key);

                        Integer projectId = keywordSvRetrieveQueueEntity.getProjectId();
                        String kwName = keywordSvRetrieveQueueEntity.getKeywordName().toLowerCase();

                        if (StringUtils.isBlank(kwName)) {
                            System.out.println("Skip empty keyword, projectId:" + projectId + ", kwName:" + kwName);
                            continue;
                        }

                        int engineId = keywordSvRetrieveQueueEntity.getSearchEngineId();
                        int languageId = keywordSvRetrieveQueueEntity.getLanguageId();

                        keywordSvRetrieveQueueEntity.setSvRetrieveTime(now);
                        keywordSvRetrieveQueueEntity.setSvUpdateDate(FormatUtils.formatDateToYyyyMmDd(now));
                        keywordSvRetrieveQueueEntity.setSvRetrieveStatus(AutoAdhocRankKeywordSVEntity.SV_RETRIEVE_STATUS_SUCCESSFULLY);

                        KeywordResearchSVEntity commonKeywordResearchSVEntity = null;
                        Map<String, KeywordResearchSVEntity> svMap = new HashedMap();

                        String[] sourceKeywordArr = keywordSvRetrieveQueueEntity.getSourceKeywordGroup().split(SOURCE_KEYWORD_SPLIT);
                        for (String sourceKeyword : sourceKeywordArr) {
                            KeywordResearchSVEntity keywordResearchSVEntity = new KeywordResearchSVEntity();

                            if (svMap.get(kwName) != null) {
                                commonKeywordResearchSVEntity = svMap.get(kwName);
                                BeanUtils.copyProperties(commonKeywordResearchSVEntity,keywordResearchSVEntity);
                            } else {

                                keywordResearchSVEntity.setProjectId(keywordSvRetrieveQueueEntity.getProjectId());
                                keywordResearchSVEntity.setOwnDomainId(keywordSvRetrieveQueueEntity.getOwnDomainId());
                                keywordResearchSVEntity.setEngineId(engineId);
                                keywordResearchSVEntity.setLanguageId(languageId);
                                keywordResearchSVEntity.setLocationId(LOCATION_ID);
                                keywordResearchSVEntity.setKeywordName(kwName);
//                                keywordResearchSVEntity.setSourceKeyword(keywordSvRetrieveQueueEntity.getSourceKeyword());

                                keywordResearchSVEntity.setAvgSearchVolume(result.getSearch_volume() == null ? 0 : result.getSearch_volume());
                                keywordResearchSVEntity.setCpc(result.getCpc() == null ? 0 : result.getCpc());
                                List<Integer> categoryList = getCategoryList(StringUtils.join(result.getCategories(), ","));
                                keywordResearchSVEntity.setCategory(
                                        CollectionUtils.isEmpty(categoryList) ? new Integer[]{} : categoryList.toArray(new Integer[categoryList.size()]));

//                        List<String> word = new ArrayList<String>();
//                        List<String> stream = new ArrayList<String>();
//                        List<String> keywordVariationOneword = new ArrayList<String>();
//                        List<String> keywordVariationNgram = new ArrayList<String>();
//                        String languageName = keywordSvRetrieveQueueEntity.getLanguageName();
//                        String countryCode = keywordSvRetrieveQueueEntity.getCountryCode();
//                        if (StringUtils.equalsIgnoreCase(languageName, "Arabic")) {
//                            word.addAll(SnowBallAndNgramForForeignLanguages.wordTokenizer(kwName, "ar"));
//                        } else {
//                            word.addAll(SnowBallAndNgramForForeignLanguages.wordTokenizer(kwName, countryCode));
//                        }
//                        stream.addAll(SnowBallAndNgramForForeignLanguages.wordStemmerTokenizer(kwName, languageName));
//
//                        keywordVariationOneword.addAll(SnowBallAndNgramForForeignLanguages.wordNgramTokenizer(kwName, true));
//                        keywordVariationNgram.addAll(SnowBallAndNgramForForeignLanguages.wordNgramTokenizer(kwName, false));

                                WordTokenizerEntity wordTokenizerEntity = scKeywordSearchVolumeManager.getWordTokenizerEntity(engineId, languageId, kwName);

                                keywordResearchSVEntity.setWord(wordTokenizerEntity.getWord());
                                keywordResearchSVEntity.setStream(wordTokenizerEntity.getStream());
                                keywordResearchSVEntity.setKeywordVariationOneword(wordTokenizerEntity.getKeywordVariationOneWord());
                                keywordResearchSVEntity.setKeywordVariationNgram(wordTokenizerEntity.getKeywordVariationNgram());

                                if (result.getMonthly_searches() != null) {
//                                    System.out.println("=========not null:" + key);
                                    for (Result.MonthlySearches monthlySearches : result.getMonthly_searches()) {
                                        if (monthlySearches.getMonth() == 1) {
                                            keywordResearchSVEntity.setMonthlySearchVolume1(monthlySearches.getSearch_volume() == null ? 0 : monthlySearches.getSearch_volume());
                                        } else if (monthlySearches.getMonth() == 2) {
                                            keywordResearchSVEntity.setMonthlySearchVolume2(monthlySearches.getSearch_volume() == null ? 0 : monthlySearches.getSearch_volume());
                                        } else if (monthlySearches.getMonth() == 3) {
                                            keywordResearchSVEntity.setMonthlySearchVolume3(monthlySearches.getSearch_volume() == null ? 0 : monthlySearches.getSearch_volume());
                                        } else if (monthlySearches.getMonth() == 4) {
                                            keywordResearchSVEntity.setMonthlySearchVolume4(monthlySearches.getSearch_volume() == null ? 0 : monthlySearches.getSearch_volume());
                                        } else if (monthlySearches.getMonth() == 5) {
                                            keywordResearchSVEntity.setMonthlySearchVolume5(monthlySearches.getSearch_volume() == null ? 0 : monthlySearches.getSearch_volume());
                                        } else if (monthlySearches.getMonth() == 6) {
                                            keywordResearchSVEntity.setMonthlySearchVolume6(monthlySearches.getSearch_volume() == null ? 0 : monthlySearches.getSearch_volume());
                                        } else if (monthlySearches.getMonth() == 7) {
                                            keywordResearchSVEntity.setMonthlySearchVolume7(monthlySearches.getSearch_volume() == null ? 0 : monthlySearches.getSearch_volume());
                                        } else if (monthlySearches.getMonth() == 8) {
                                            keywordResearchSVEntity.setMonthlySearchVolume8(monthlySearches.getSearch_volume() == null ? 0 : monthlySearches.getSearch_volume());
                                        } else if (monthlySearches.getMonth() == 9) {
                                            keywordResearchSVEntity.setMonthlySearchVolume9(monthlySearches.getSearch_volume() == null ? 0 : monthlySearches.getSearch_volume());
                                        } else if (monthlySearches.getMonth() == 10) {
                                            keywordResearchSVEntity.setMonthlySearchVolume10(monthlySearches.getSearch_volume() == null ? 0 : monthlySearches.getSearch_volume());
                                        } else if (monthlySearches.getMonth() == 11) {
                                            keywordResearchSVEntity.setMonthlySearchVolume11(monthlySearches.getSearch_volume() == null ? 0 : monthlySearches.getSearch_volume());
                                        } else if (monthlySearches.getMonth() == 12) {
                                            keywordResearchSVEntity.setMonthlySearchVolume12(monthlySearches.getSearch_volume() == null ? 0 : monthlySearches.getSearch_volume());
                                        }

                                    }
                                } else {
                                    continue;
                                }

                                keywordResearchSVEntity.setSvFromRankcheck(KeywordResearchSVEntity.SV_SOURCE_API);
                                keywordResearchSVEntity.setSign(1);
                                keywordResearchSVEntity.setVersioning(1);

                                svMap.put(kwName, keywordResearchSVEntity);
                            }

                            keywordResearchSVEntity.setSourceKeyword(sourceKeyword);
                            insertList.add(keywordResearchSVEntity);

                        }

                        updateList.add(keywordSvRetrieveQueueEntity);
                        projectSet.add(projectId);

                        if (insertList.size() >= 1000) {
                            System.out.println("======:insertList.size" + insertList.size() + ",updateList size:" + updateList.size());
                            keywordResearchSVClarityDBDAO.insertBatch(insertList);
                            keywordSvRetrieveQueueDAO.updateSvForBatch(updateList);
                            insertList.clear();
                            updateList.clear();
                        }
                    }

                }

            } catch (Exception e) {
                e.printStackTrace();
                log.error("===error line : " + key);
            }


        }

        if (CollectionUtils.isNotEmpty(insertList)) {
            System.out.println("======<1000:insertList.size" + ",updateList size:" + updateList.size());
            keywordResearchSVClarityDBDAO.insertBatch(insertList);
            keywordSvRetrieveQueueDAO.updateSvForBatch(updateList);
        }

        if (!CollectionUtils.isEmpty(projectSet)) {
            for(Integer projectId: projectSet){
                Integer unRetrievedCount = keywordSvRetrieveQueueDAO.getUnRetrieveCount(KeywordSvRetrieveQueueEntity.KEYWORD_TYPE_LIVE_RESEARCH, projectId);
                if(unRetrievedCount == null || unRetrievedCount == 0){
                    log.info("===up projectSuc:" + projectId);
                    keywordLiveResearchProjectEntityDAO.updateSvStatusById(projectId, KeywordLiveResearchProjectEntity.RETRIEVESV_STATUS_COMPLETEED_WITHOUT_ERROR);
                }
            }

        }

    }

    public List<Integer> getCategoryList(String category) {
        if (StringUtils.isBlank(category)) {
            return null;
        } else {
            String[] list = StringUtils.split(category, ',');
            List<Integer> res = new ArrayList<Integer>();
            for (String val : list) {
                if (StringUtils.isNotBlank(val)) {
                    try {
                        res.add(Integer.valueOf(val));
                    } catch (Exception e) {
                        System.out.println("Parse int val failed. category:" + category + ", val:" + val);
                        e.printStackTrace();
                    }
                }
            }
            return res;
        }
    }

    private static List<String> getUrl() {
        System.out.println("===getUrl");
        try {
            OkHttpClient client = new OkHttpClient();

            Response response = client.newCall(new Request.Builder().url(getUrl).header("Authorization", credential).get().build()).execute();

            if (response.isSuccessful()) {
                JSONObject jsonObject = JSONObject.parseObject(Objects.requireNonNull(response.body()).string());
                String tasksLength = jsonObject.get("tasks").toString();
                JSONObject tasks = JSONObject.parseObject(tasksLength.substring(1, tasksLength.length() - 1));
                String resultLength = tasks.get("result").toString();
                String[] strings = resultLength.substring(1, resultLength.length() - 1).split("},");
                List<String> list = new ArrayList<>();
                for (String s : strings) {
                    if (!s.endsWith("}")) {
                        s = s + "}";
                    }
                    JSONObject result = JSONObject.parseObject(s);
                    list.add(result.get("id").toString());
                    System.out.println(result.get("id"));
                }
                return list;
            } else {
                System.out.println("get请求失败");
            }
        } catch (Exception e) {
            System.out.println("get异常");
            e.printStackTrace();
        }
        return null;
    }

    private static List<Result> getResult(String id) {
        System.out.println("===getResult");
        try {
            OkHttpClient client = new OkHttpClient();
            Response response = client.newCall(new Request.Builder().url(String.format(apiUrl, id)).header("Authorization", credential).get().build()).execute();
            if (response.isSuccessful()) {
                JSONObject jsonObject = JSONObject.parseObject(Objects.requireNonNull(response.body()).string());
                String tasksLength = jsonObject.get("tasks").toString();
                JSONObject tasks = JSONObject.parseObject(tasksLength.substring(1, tasksLength.length() - 1));
                String resultLength = tasks.get("result").toString();
                return JSONArray.parseArray(resultLength, Result.class);
            } else {
                System.out.println("api请求失败");
            }
        } catch (Exception e) {
            System.out.println("api异常");
            e.printStackTrace();
        }
        return null;
    }


    //拼接参数用于POST请求

    private static String generateParametersForPost(HashMap<String, Object> parameters) {

        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append("[{\"");
        if (parameters.size() > 0) {
            Object[] keys = parameters.keySet().toArray();
            for (int i = 0; i < keys.length; i++) {
                if (i == (keys.length - 1)) {
                    stringBuffer.append(keys[i].toString()).append("\":").append(parameters.get(keys[i]).toString()).append("}]");
                    break;
                }
                stringBuffer.append(keys[i].toString()).append("\":").append(parameters.get(keys[i]).toString()).append(",\"");
            }
        }
        return stringBuffer.toString();
    }


    private void sendMailReport(String status, String message, Integer projectId) {
        Map<String, Object> reportMap = new HashMap<String, Object>();
        reportMap.put("userName", "Jason");
        reportMap.put("successMessage", message);
        String emailTo = "<EMAIL>";
        String subject = status + " Keyword Suggest RetrieveSV" + projectId;
        String[] ccTo = new String[]{"<EMAIL>"};
//        String[] ccTo = null;
//        emailSenderComponent.sendMimeMultiPartMailAndBcc(emailTo, ccTo, subject, "mail_resource_operate_report.txt", "mail_resource_operate_report.html", reportMap);
        zeptoMailSenderComponent.sendMimeMultiPartZeptoMailAndBccByFunctionType(emailTo, ccTo, subject, "mail_resource_operate_report.txt","mail_resource_operate_report.html", reportMap, null,
                ZeptoMailSenderComponent.FUNCTION_TYPE_BACKEND_INTERNAL, null, null);
    }

}
