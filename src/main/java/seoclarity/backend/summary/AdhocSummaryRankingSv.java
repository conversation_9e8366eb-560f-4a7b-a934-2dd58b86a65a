package seoclarity.backend.summary;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import seoclarity.backend.dao.actonia.adhoc.AdhocRankSvProjectRelDAO;
import seoclarity.backend.dao.actonia.adhoc.AutoAdhocRankKeywordSVEntityDAO;
import seoclarity.backend.dao.actonia.adhoc.AutoAdhocRankProjectEntityDAO;
import seoclarity.backend.dao.clickhouse.adhoc.AdhocInfoDao;
import seoclarity.backend.dao.clickhouse.adhoc.AdhocSearchvolumeDAO;
import seoclarity.backend.entity.CLRankingDetailEntity;
import seoclarity.backend.entity.actonia.adhoc.AdhocSearchvolumeEntity;
import seoclarity.backend.entity.actonia.adhoc.AutoAdhocRankKeywordSVEntity;
import seoclarity.backend.entity.actonia.adhoc.AutoAdhocRankProjectEntity;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.utils.WorkerUtils;
import seoclarity.backend.utils.ZeptoMailSenderComponent;
import seoclarity.backend.utils.cityhash.CityHashUtil;

import java.util.*;
import java.util.stream.Collectors;

import static seoclarity.backend.entity.actonia.adhoc.AutoAdhocRankProjectEntity.*;
import static seoclarity.backend.utils.WorkerUtils.CACHE_LIST_URL;
import static seoclarity.backend.utils.WorkerUtils.DELETE_CACHE_URL_PREFIX;

@CommonsLog
public class AdhocSummaryRankingSv {

    private static String KEY_SPLIT = "!_!";
    private static final String CACHE_SUMMARY_RANKING_COMPLETE_KEY = "event_summary_ranking_complete_adhoc";
    private static final String CACHE_SUMMARY_SV_COMPLETE_KEY = "event_summary_sv_complete_adhoc";
    private static final String CACHE_RANKING_UPLOAD_COMPLETE_KEY = "event_ranking_upload_complete_adhoc";
    private AutoAdhocRankProjectEntityDAO autoAdhocRankProjectEntityDAO;
    private AdhocInfoDao adhocInfoDao;
    private AdhocSearchvolumeDAO adhocSearchvolumeDAO;
    private AutoAdhocRankKeywordSVEntityDAO autoAdhocRankKeywordSVEntityDAO;
    private AdhocRankSvProjectRelDAO adhocRankSvProjectRelDAO;
    private ZeptoMailSenderComponent zeptoMailSenderComponent;

    public AdhocSummaryRankingSv() {
        autoAdhocRankProjectEntityDAO = SpringBeanFactory.getBean("autoAdhocRankProjectEntityDAO");
        adhocInfoDao = SpringBeanFactory.getBean("adhocInfoDao");
        adhocSearchvolumeDAO = SpringBeanFactory.getBean("adhocSearchvolumeDAO");
        autoAdhocRankKeywordSVEntityDAO = SpringBeanFactory.getBean("autoAdhocRankKeywordSVEntityDAO");
        adhocRankSvProjectRelDAO = SpringBeanFactory.getBean("adhocRankSvProjectRelDAO");
        zeptoMailSenderComponent = SpringBeanFactory.getBean("zeptoMailSenderComponent");
    }

    private List<Integer> getUploadCompleteProject() {
        //get from worker
        try {
            List<String> rankingList = getCacheList(CACHE_SUMMARY_RANKING_COMPLETE_KEY);
            List<String> svList = getCacheList(CACHE_SUMMARY_SV_COMPLETE_KEY);

            List<Integer> rankingProjectIdList = new ArrayList<>();
            List<Integer> svProjectIdList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(rankingList) && !CollectionUtils.isEmpty(svList)) {
                for (String key : rankingList) {
                    String keyArr[] = key.split("_");
                    int domainId = Integer.parseInt(keyArr[keyArr.length - 2]);
                    int projectId = Integer.parseInt(keyArr[keyArr.length - 1]);
                    log.info("===Ranking domainId:" + domainId + ",projectId:" + projectId);

                    List<Integer> rankParentProjectIdList = adhocRankSvProjectRelDAO.getParentProjectId(projectId);
                    if (CollectionUtils.isNotEmpty(rankParentProjectIdList)) {
                        rankingProjectIdList.addAll(rankParentProjectIdList);
                    }
                }

                for (String key : svList) {
                    String keyArr[] = key.split("_");
                    int domainId = Integer.parseInt(keyArr[keyArr.length - 2]);
                    int projectId = Integer.parseInt(keyArr[keyArr.length - 1]);
                    log.info("===Sv domainId:" + domainId + ",projectId:" + projectId);

                    List<Integer> svParentProjectIdList = adhocRankSvProjectRelDAO.getParentProjectId(projectId);
                    if (CollectionUtils.isNotEmpty(svParentProjectIdList)) {
                        svProjectIdList.addAll(svParentProjectIdList);
                    }
                }

                //取交集
                List<Integer> intersection = rankingProjectIdList.stream()
                        .filter(svProjectIdList::contains)
                        .collect(Collectors.toList());

                return intersection;
            } else {
                log.info("===no task need to run,exit!!!");
            }

        } catch (Exception e) {
            e.printStackTrace();
            log.info("=====get cache list error");
        }

        return null;
    }


    private void processByWorker(Integer processingProjectId) {

        List<Integer> projectIdList = new ArrayList<>();
        if(processingProjectId == null){
            projectIdList = getUploadCompleteProject();
            if (CollectionUtils.isEmpty(projectIdList)) {
                log.info("===no task need to run,exit!!!");
                return;
            }
        }else {
            projectIdList.add(processingProjectId);
        }

        for (Integer parentProjectId : projectIdList) {
            try {
                log.info("===start Summary parentProjectId:" + parentProjectId);
                String rankingWorkerKey = "";
                String svWorkerKey = "";
                Map<String, CLRankingDetailEntity> allRankingMap = new HashMap<>();
                Map<String, AdhocSearchvolumeEntity> svMap = new HashMap<>();

                AutoAdhocRankProjectEntity parentProject = autoAdhocRankProjectEntityDAO.getProjectById(parentProjectId);
                int domainId = parentProject.getOwnDomainId();
                int engineId = parentProject.getSearchEngineId();
                int languageId = parentProject.getLanguageId();
                boolean isMobile = parentProject.getDevice() == 2;
                int keywordCountInFile = 0;
                int uniqueKeywordCount = 0;
                int completedKeywordCount = 0;
                Date sendToRankQueueStartTime = null;
                List<AutoAdhocRankProjectEntity> subProjectList = autoAdhocRankProjectEntityDAO.getSubProjectsByParentProjectId(parentProjectId);
                Integer subSvProjectId = null;
                for (AutoAdhocRankProjectEntity projectEntity : subProjectList) {
                    keywordCountInFile = projectEntity.getKeywordCountInFile();
                    uniqueKeywordCount = projectEntity.getUniqueKeywordCount();
                    completedKeywordCount = projectEntity.getCompletedKeywordCount();
                    int subProjectId = projectEntity.getId();
//                    int domainId = projectEntity.getOwnDomainId();
//                    int engineId = projectEntity.getSearchEngineId();
//                    int languageId = projectEntity.getLanguageId();
//                    boolean isMobile = projectEntity.getDevice() == 2;
                    int retrieveType = projectEntity.getRetrieveType();
                    log.info("=====subProjectStart:" + subProjectId);

                    //分页查询，防止内存溢出
                    int pageSize = 300;
                    Long relId = 0l;
                    int currentTotal = 0;
                    while (true) {

                        long a = System.currentTimeMillis();
                        List<AutoAdhocRankKeywordSVEntity> relList = autoAdhocRankKeywordSVEntityDAO.getKeywordByPagesForRankingSv(subProjectId, relId, pageSize);
                        if (CollectionUtils.isEmpty(relList)) {
                            log.info("===breakPage");
                            break;
                        }

                        long b = System.currentTimeMillis();
                        currentTotal += relList.size();
                        log.info("Current size, relList:" + relList.size() + ", currentTotal:" + currentTotal + ", relId:" + relId
                                + ", projectId:" + subProjectId + ", cost:" + (b - a) / 1000);

                        Map<Integer, List<String>> keywordHashCityMap = relList.stream().collect(
                                Collectors.groupingBy(v1 -> v1.getCityId(),
                                        Collectors.mapping(
                                                v1 -> CityHashUtil.getUnsignedUrlHash(v1.getKeywordName().toLowerCase()),
                                                Collectors.toList()
                                        )));


                        log.info("===keywordHashCityMapSize:" + keywordHashCityMap.size());
                        for (Integer cityId : keywordHashCityMap.keySet()) {

                            if (retrieveType == RETRIEVE_TYPE_RANKCHECK_ONLY) {//todo 获取cdb ranking info数据
                                sendToRankQueueStartTime = projectEntity.getSendToRankQueueStartTime();

                                List<CLRankingDetailEntity> rankingList = adhocInfoDao.getInfoListByKeywordHashList(subProjectId,
                                        isMobile, engineId, languageId, cityId, keywordHashCityMap.get(cityId));
                                Map<String, CLRankingDetailEntity> rankingMap = rankingList.stream().collect(
                                        Collectors.toMap(
                                                v1 -> v1.getLocationId() + KEY_SPLIT + v1.getKeywordHash(),
                                                v1 -> v1,
                                                (existing, replacement) -> replacement
                                        )
                                );
                                allRankingMap.putAll(rankingMap);
                                rankingWorkerKey = CACHE_SUMMARY_RANKING_COMPLETE_KEY + "_" + domainId + "_" + subProjectId;
                                adhocInfoDao.insertBatchDetailTable(parentProjectId, subProjectId, null, null, isMobile, engineId, languageId);
                                adhocInfoDao.insertBatchSubRankTable(parentProjectId, subProjectId, isMobile, engineId, languageId);

                            } else if (retrieveType == RETRIEVE_GOOGLE_SV_ONLY || retrieveType == RETRIEVE_BING_SV_ONLY) {//todo 获取cdb sv数据
                                subSvProjectId = subProjectId;
                                List<AdhocSearchvolumeEntity> svList = adhocSearchvolumeDAO.getSvListByKeywordHashList(subProjectId,
                                        cityId, keywordHashCityMap.get(cityId));
                                svMap.putAll(svList.stream().collect(
                                        Collectors.toMap(
                                                v1 -> v1.getLocationId() + KEY_SPLIT + v1.getKeywordHash(),
                                                v1 -> v1
                                        )
                                ));
                                svWorkerKey = CACHE_SUMMARY_SV_COMPLETE_KEY + "_" + + domainId + "_" + subProjectId;
                            }
                        }
//                        log.info("-------rankingMap:" + JSON.toJSONString(rankingMap));
//                        log.info("-------svMap:" + JSON.toJSONString(svMap));
                        relId = relList.get(relList.size() - 1).getId();
                    }
                }
                log.info("-------allRankingMap Size:" + allRankingMap.size());
                //todo merge data
                List<CLRankingDetailEntity> insertList = new ArrayList<>();
                for (String key : allRankingMap.keySet()) {

                    CLRankingDetailEntity rankingInfo = allRankingMap.get(key);
                    AdhocSearchvolumeEntity svInfo = svMap.get(key);
                    rankingInfo.setProjectId(parentProjectId);
                    if(svInfo == null){
                        log.info("====svInfokey:" + key);
                    }else {
                        rankingInfo.setAvgSearchVolume(svInfo.getAvgSearchVolume() == null ? null : Long.parseLong(svInfo.getAvgSearchVolume().toString()));
                        rankingInfo.setCpc(svInfo.getCpc());
                        rankingInfo.setMonthlySearchVolume1(svInfo.getMonthlySearchVolume1());
                        rankingInfo.setMonthlySearchVolume2(svInfo.getMonthlySearchVolume2());
                        rankingInfo.setMonthlySearchVolume3(svInfo.getMonthlySearchVolume3());
                        rankingInfo.setMonthlySearchVolume4(svInfo.getMonthlySearchVolume4());
                        rankingInfo.setMonthlySearchVolume5(svInfo.getMonthlySearchVolume5());
                        rankingInfo.setMonthlySearchVolume6(svInfo.getMonthlySearchVolume6());
                        rankingInfo.setMonthlySearchVolume7(svInfo.getMonthlySearchVolume7());
                        rankingInfo.setMonthlySearchVolume8(svInfo.getMonthlySearchVolume8());
                        rankingInfo.setMonthlySearchVolume9(svInfo.getMonthlySearchVolume9());
                        rankingInfo.setMonthlySearchVolume10(svInfo.getMonthlySearchVolume10());
                        rankingInfo.setMonthlySearchVolume11(svInfo.getMonthlySearchVolume11());
                        rankingInfo.setMonthlySearchVolume12(svInfo.getMonthlySearchVolume12());

                        rankingInfo.setKeywordVariationOneword(svInfo.getKeywordVariationOneword());
                        rankingInfo.setKeywordVariationNgram(svInfo.getKeywordVariationNgram());
                        rankingInfo.setCategory(svInfo.getCategory());
                        rankingInfo.setMonthlySvAttrKey(Arrays.asList(svInfo.getMonthlySvAttrKey()));
                        rankingInfo.setMonthlySvAttrValue(Arrays.asList(svInfo.getMonthlySvAttrValue()));
                    }
                    insertList.add(rankingInfo);
                }
                log.info("===insertListSize:" + insertList.size());
                adhocInfoDao.insertBatchInfoTable(insertList, isMobile, engineId, languageId);//todo insert info,detail,subrank , update project status

                autoAdhocRankProjectEntityDAO.updateProjectSVStatusForRankingSv(parentProjectId,
                        AutoAdhocRankProjectEntity.STATUS_COMPLETED_SUCCESSFULLY, RANK_STATUS_COMPLETEED_WITHOUT_ERROR,
                        AutoAdhocRankProjectEntity.RETRIEVESV_STATUS_COMPLETEED_WITHOUT_ERROR,
                        AutoAdhocRankProjectEntity.RANK_UPLOAD_STATUS_COMPLETED_WITHOUT_ERROR, UPLOAD_SV_STSTUS_COMPLETED_WITHOUT_ERROR,
                        keywordCountInFile, uniqueKeywordCount, completedKeywordCount, sendToRankQueueStartTime);

                String deleteRankingUrl = DELETE_CACHE_URL_PREFIX + rankingWorkerKey;
                log.info("==deleteRankingUrl cache:" + deleteRankingUrl);
                deleteCache(deleteRankingUrl);

                //send cache to workers for parent project
                String workerKey = CACHE_RANKING_UPLOAD_COMPLETE_KEY + "_" + domainId  + "_" + parentProjectId;
                String workerValue = FormatUtils.formatDate(new Date(), FormatUtils.DATE_PATTERN_RAW_STRING_FROM_DB);
                try {
                    WorkerUtils.syncEventKeyToWorkers(WorkerUtils.WORKER_BASE_URL, workerKey, workerValue);
                } catch (Exception e) {
                    e.printStackTrace();
                }

                //多个父共用一个sv 子项目
                List<Integer> notFinishedList = adhocRankSvProjectRelDAO.getNotFinishedParentSvProjectList(subSvProjectId, parentProjectId);
                if(CollectionUtils.isEmpty(notFinishedList)){
                    String deleteSvUrl = DELETE_CACHE_URL_PREFIX + svWorkerKey;
                    log.info("==deleteSvUrl cache:" + deleteSvUrl);
                    deleteCache(deleteSvUrl);
                }else {
                    log.info("=====have not finished project, don't clear sv worker" + JSON.toJSONString(notFinishedList));
                }

            }catch (Exception e){
                e.printStackTrace();
                sendMailReport(parentProjectId, "ERROR" , "Failed SummaryAdhoc Ranking + SV:" + parentProjectId);
            }
        }

    }

    private void deleteCache(String url) {
        try {
            WorkerUtils.doDelete(url, WorkerUtils.KEY_SECURITY_HEADER, WorkerUtils.VALUE_SECURITY_HEADER);
        }catch (Exception e){
            e.printStackTrace();
            System.out.println("====doDelete error:" + url);
        }
    }
    private List<String> getCacheList(String key) throws Exception {
        List<String> resultList = new ArrayList<>();
        String url = CACHE_LIST_URL;
        if (StringUtils.isNotBlank(key)) {
            url += key;
        } else {
            log.info("===key null.");
            return null;
        }
        log.info("=====get list:" + url);
        String adhocCacheList = WorkerUtils.doGet(url, WorkerUtils.KEY_SECURITY_HEADER, WorkerUtils.VALUE_SECURITY_HEADER);
        String keys = new Gson().fromJson(adhocCacheList, Map.class).get("keys").toString();
        List<String> nameList = new Gson().fromJson(keys, List.class);
        JSONArray jsonArray = JSONObject.parseArray(JSON.toJSONString(nameList));
        List<Map> mapList = jsonArray.toJavaList(Map.class);
        log.info("===mapList :" + JSON.toJSONString(mapList));
        for (Map map : mapList) {
            log.info("===name :" + map.get("name"));
            resultList.add(map.get("name").toString());
        }
        return resultList;
    }

    private void sendMailReport(int projectId, String status, String message) {
        Map<String, Object> reportMap = new HashMap<String, Object>();
        reportMap.put("userName", "Jason");
        reportMap.put("successMessage", message);
        String emailTo = "<EMAIL>";
        String subject = "Adhoc Ranking + SV Summary , projectId " + projectId + "  " + status;
        String[] ccTo = new String[]{"<EMAIL>"};
//        String[] ccTo = null;

//        emailSenderComponent.sendMimeMultiPartMailAndBcc(emailTo, ccTo, subject, "mail_resource_operate_report.txt", "mail_resource_operate_report.html", reportMap);
        zeptoMailSenderComponent.sendMimeMultiPartZeptoMailAndBccByFunctionType(emailTo, ccTo, subject, "mail_resource_operate_report.txt", "mail_resource_operate_report.html", reportMap,
                null, ZeptoMailSenderComponent.FUNCTION_TYPE_BACKEND_INTERNAL, null, null);
    }

    private void updateAllRejectProject(){
        List<Integer> parentProjectIdList = autoAdhocRankProjectEntityDAO.getNotCanceledParentProjectList();
        if(CollectionUtils.isEmpty(parentProjectIdList)){
            log.info("======no parentProject,exit!");
            return;
        }

        for(Integer parentProjectId: parentProjectIdList){
            List<AutoAdhocRankProjectEntity> childProjectList = autoAdhocRankProjectEntityDAO.getSubProjectsByParentProjectId(parentProjectId);
            boolean rankingReject = false;
            boolean svReject = false;
            for(AutoAdhocRankProjectEntity childProject: childProjectList){
                int retrieveType = childProject.getRetrieveType();
                int status = childProject.getStatus();
                if(retrieveType == RETRIEVE_TYPE_RANKCHECK_ONLY && status == STATUS_CANCELLED){
                    rankingReject = true;
                }

                if((retrieveType == RETRIEVE_GOOGLE_SV_ONLY || retrieveType == RETRIEVE_BING_SV_ONLY )&& status == STATUS_CANCELLED){
                    svReject = true;
                }
            }

            if(rankingReject && svReject){
                log.info("===all child rejected, update parent t reject:" + parentProjectId);
                autoAdhocRankProjectEntityDAO.updateProjectStatus(parentProjectId, AutoAdhocRankProjectEntity.STATUS_CANCELLED, null, null);
            }
        }
    }

    public static void main(String[] args) {
        Integer processingParentProjectId = null;
        if(args != null && args.length > 0){
            processingParentProjectId = Integer.parseInt(args[0]);
        }
        AdhocSummaryRankingSv adhocSummaryRankingSv = new AdhocSummaryRankingSv();
        adhocSummaryRankingSv.processByWorker(processingParentProjectId);
        adhocSummaryRankingSv.updateAllRejectProject();
    }


}
