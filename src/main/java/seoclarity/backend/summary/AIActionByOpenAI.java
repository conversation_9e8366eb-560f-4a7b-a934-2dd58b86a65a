package seoclarity.backend.summary;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.internal.LinkedTreeMap;
import com.knuddels.jtokkit.Encodings;
import com.knuddels.jtokkit.api.Encoding;
import com.knuddels.jtokkit.api.EncodingRegistry;
import com.knuddels.jtokkit.api.EncodingResult;
import com.knuddels.jtokkit.api.ModelType;
import kong.unirest.HttpResponse;
import kong.unirest.Unirest;
import lombok.extern.apachecommons.CommonsLog;
import okhttp3.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import seoclarity.backend.dao.actonia.*;
import seoclarity.backend.dao.actonia.openai.OpenAIActionDAO;
import seoclarity.backend.dao.actonia.openai.OpenAPromptDAO;
import seoclarity.backend.dao.actonia.openai.OpenAiLoggingDAO;
import seoclarity.backend.dao.actonia.openai.OpenAiPromptMessageDAO;
import seoclarity.backend.dao.clickhouse.ClDailyRankingEntityDao;
import seoclarity.backend.dao.clickhouse.openai.PageClarityDAO;
import seoclarity.backend.dao.clickhouse.urlclaritydb.CrawlUrlDao;
import seoclarity.backend.entity.KeywordProperty;
import seoclarity.backend.entity.KeywordRankVO;
import seoclarity.backend.entity.ResultContent;
import seoclarity.backend.entity.actonia.*;
import seoclarity.backend.entity.actonia.openai.*;
import seoclarity.backend.entity.clickhouse.crawlurl.TargetUrlHtmlDaily;
import seoclarity.backend.multithread.BackendThreadFactory;
import seoclarity.backend.onetime.MonthlyKeywordExpansionSenderThread;
import seoclarity.backend.service.CommonDataService;
import seoclarity.backend.service.ScKeywordRankManager;
import seoclarity.backend.upload.TargeturlRecommendedInsert;
import seoclarity.backend.utils.*;
import seoclarity.backend.utils.murmurhash.MurmurHashUtils;

import java.io.File;
import java.io.IOException;
import java.math.BigInteger;
import java.net.InetSocketAddress;
import java.net.Proxy;
import java.net.URLDecoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static seoclarity.backend.entity.actonia.openai.AiActionParams.DATA_TYPE_MANAGED_URL;
import static seoclarity.backend.entity.actonia.openai.AiActionParams.DATA_TYPE_SITE_AUDIT;

@CommonsLog
public class AIActionByOpenAI {

    private static int threadCount = 50;
    private ExecutorService newThreadPool =
            new ThreadPoolExecutor(threadCount, threadCount, 60L, TimeUnit.SECONDS, new LinkedBlockingQueue<>());
    private static final String PATTERN = "\\{\\{(.*?)\\}\\}";
    private static final String SPLIT = "\t";
    private static final String TITLE_SPLIT = "','";

    private static final String PROVIDER = "openai";
    private static final String ENDPOINT = "chat/completions";
    private static final String TRANSLATE_WORKER_KEY = "event_qbase_kwTranslation_out_";
    private static final String REQUEST_URL = "https://gateway.ai.cloudflare.com/v1/11698fe22e694ee13aa14f29ed067dfe/seoclarity-ai";//https://www.wrike.com/open.htm?id=**********
    private static final String GOOGLE_TRANSLATE_REQUEST_URL = "https://translation.googleapis.com/language/translate/v2";
    private static final String API_KEY = "Bearer ***************************************************";
    private static final String GOOGLE_TRANSLATE_API_KEY = "AIzaSyDN1C9FHvY4Eod98AuGuSppMecpRkLQO-8";
    private static final String FUNC_CODE_SET_TAG_KEYWORD = "ai-action-set-tag-name-for-keywords";
    private static final String FUNC_CODE_TRANSLATE_KEYWORD = "ai-action-translate-keywords";
    private static final String FUNC_CODE_GOOGLE_TRANSLATE_KEYWORD = "ai-action-google-translate";
    private static final String FUNC_CODE_SUGGEST_TITLE = "ai-action-suggest-titles";
    private static final String FUNC_CODE_SUGGEST_META = "ai-action-suggest-meta-description";
    private static final String FUNC_CODE_KEYWORD_EXTRACTION = "ai-action-keyword-extraction";
    private static final String DOWNLOAD_FOLDER = "/home/<USER>/aiAction/";
    private static final String API_METHOD = "http://*************:8183/seoClarity/rankIntelligenceAPI/getTopXKeywordWithTitleMeta";
    private static final String API_METHOD_TOPCOMPETITOR = "http://*************:8183/seoClarity/cktppv2/getTopCompetitorRankingUrlTitlesForKeyword";

    private static final String API_SITE_CLARITY_METHOD = "http://*************:8183/siteClarity/getCrawlData";
    private static final int RETRY_TIMES = 5;
    private static final int REQUEST_KW_CNT = 1000;
    private static final int CONTENT_LENGTH = 1200;
    private static final int KEYWORD_EXTRACTION_TOKEN_LENGTH = 3000;
    private static final int TRANS_CONTENT_LENGTH = 600;
    private static final int REQUEST_URL_CNT = 1000;
    private static final int DEFAULT_USER = 214;
    private static final String CONTENT_LEVEL_KEYWORD = "keyword";
    private static final String CONTENT_LEVEL_URL = "url";
    private static final String MESSAGE_ROLE_SYSTEM = "system";
    private static final String MESSAGE_ROLE_USER = "user";
    private static final String FINISH_REASON_STOP = "stop";
    private static final int GOOGLE_TRANSLATE_MAX_NUMBER = 128;
    private static final int SITE_AUDIT_PAGE_SIZE = 1000;
    private static final int SITE_AUDIT_MAX_PROCESS_COUNT = 200000;//https://www.wrike.com/open.htm?id=1160321977

    private Gson gson = new GsonBuilder().disableHtmlEscaping().create();
    private OpenAIActionDAO openAIActionDAO;
    private KeywordEntityDAO keywordEntityDAO;

    private OpenAPromptDAO openAPromptDAO;
    private OpenAiPromptMessageDAO openAiPromptMessageDAO;
    private GroupTagEntityDAO groupTagEntityDAO;
    private OpenAiLoggingDAO openAiLoggingDAO;
    private ResourceBatchInfoEntityDAO resourceBatchInfoEntityDAO;
    private ResourceBatchDetailEntityDAO resourceBatchDetailEntityDAO;
    private OwnDomainEntityDAO ownDomainEntityDAO;
    private OwnDomainSettingEntityDAO ownDomainSettingEntityDAO;
    private ScKeywordRankManager scKeywordRankManager;
    private PageClarityDAO pageClarityDAO;
    private TargetUrlEntityDAO targetUrlEntityDAO;
    private KeywordTargeturlEntityDAO keywordTargeturlEntityDAO;
    private ClDailyRankingEntityDao clDailyRankingEntityDao;
    private CdbTrackedKeywordEntityDAO cdbTrackedKeywordEntityDAO;
    private CrawlUrlDao crawlUrlDao;
    private OwnDomainTrackingEntityDAO ownDomainTrackingEntityDAO;

    private double temperature = 0.6;
    private double topP = 1.0;
    private double frequencyPenalty = 0.1;
    private double presencePenalty = 0.1;
    private int requestCnt = 0;
    Encoding encoding;

    public AIActionByOpenAI() {
        openAIActionDAO = SpringBeanFactory.getBean("openAIActionDAO");
        keywordEntityDAO = SpringBeanFactory.getBean("keywordEntityDAO");
        openAPromptDAO = SpringBeanFactory.getBean("openAPromptDAO");
        openAiPromptMessageDAO = SpringBeanFactory.getBean("openAiPromptMessageDAO");
        groupTagEntityDAO = SpringBeanFactory.getBean("groupTagEntityDAO");
        resourceBatchInfoEntityDAO = SpringBeanFactory.getBean("resourceBatchInfoEntityDAO");
        resourceBatchDetailEntityDAO = SpringBeanFactory.getBean("resourceBatchDetailEntityDAO");
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        ownDomainSettingEntityDAO = SpringBeanFactory.getBean("ownDomainSettingEntityDAO");
        scKeywordRankManager = SpringBeanFactory.getBean("scKeywordRankManager");
        openAiLoggingDAO = SpringBeanFactory.getBean("openAiLoggingDAO");
        pageClarityDAO = SpringBeanFactory.getBean("pageClarityDAO");
        targetUrlEntityDAO = SpringBeanFactory.getBean("targetUrlEntityDAO");
        keywordTargeturlEntityDAO = SpringBeanFactory.getBean("keywordTargeturlEntityDAO");
        clDailyRankingEntityDao = SpringBeanFactory.getBean("clDailyRankingEntityDao");
        cdbTrackedKeywordEntityDAO = SpringBeanFactory.getBean("cdbTrackedKeywordEntityDAO");
        crawlUrlDao = SpringBeanFactory.getBean("crawlUrlDao");
        ownDomainTrackingEntityDAO = SpringBeanFactory.getBean("ownDomainTrackingEntityDAO");
    }

    public static void main(String[] args) throws Exception {
//        testRequestApi();
//        testFormatResponse();
//        testD();
//        new AIActionByOpenAI().postRequestSiteClarityApi();
//        testPostGoogleTranslate();
//        formatGoogleTranslateResult();
//        getTranslateDetailInfo();
//        formatResponseForKeywordExtraction();
//        Document doc = Jsoup.parse(html);
//        log.info("===" + doc.text());

        AIActionByOpenAI aiActionByOpenAI = new AIActionByOpenAI();
        aiActionByOpenAI.updateTranslateActionStatusByWorker();
        boolean isKeywordExtraction = false;
        if(args != null && args.length == 1){
            isKeywordExtraction = Boolean.parseBoolean(args[0]);
            aiActionByOpenAI.startProcess(isKeywordExtraction, null);
        } else if (args != null && args.length >= 2) {
            isKeywordExtraction = Boolean.parseBoolean(args[0]);
            aiActionByOpenAI.startProcess(isKeywordExtraction, args[1]);
        } else {
            aiActionByOpenAI.startProcess(isKeywordExtraction, null);
        }

//        aiActionByOpenAI.downloadTitleMeta(139);
//        aiActionByOpenAI.testThread();

//        parseExcel("/home/<USER>/Revolt_Aug_14_Only_links_less_than_6.xlsx");
//        parseExcel("D:\\workspace\\extract\\openai\\Revolt_Aug_14_Only_links_less_than_6.xlsx");
//        new AIActionByOpenAI().getMissingUrl("/home/<USER>/Revolt_Aug_14_Only_links_less_than_6.xlsx");

//        String content = "It would obviously be a blessing.";
//        splitOverLimitContent(content, "1");

//        testPostAPI();
    }

    private static void testPostAPI(){
        String jsonBody = "{\"model\":\"gpt-3.5-turbo\",\"messages\":[{\"role\":\"system\",\"content\":\"You are an expert at natural language processing.\\nYou will be provided a simple list of keywords.\\nFor each keyword, check if it meets the following criteria: are product related.\\nOutput the keyword and a Boolean field for yes/no in JSON format as follows\\n{keyword 1: \\\"yes\\\", keyword 2: \\\"no\\\"}\\nIf unsure, return {keyword 1: \\\"n/a\\\", keyword 2: \\\"n/a\\\"}. Never explain anything.\"},{\"role\":\"user\",\"content\":\"\\\"耳機 音箱\\\",\\\"線材 充電器\\\",\\\"耳机 音响\\\",\\\"线材 充电器\\\",\\\"mi sphere camera\\\",\\\"yeelight led智能燈泡\\\",\\\"小米\\\",\"}],\"temperature\":0.6,\"top_p\":1.0,\"frequency_penalty\":0.1,\"presence_penalty\":0.1}";
        jsonBody = "{\"model\":\"gpt-3.5-turbo\",\"messages\":[{\"role\":\"system\",\"content\":\"You are an expert at natural language processing.\\nYou will be provided a simple list of keywords.\\nFor each keyword, check if it meets the following criteria: are product related.\\nOutput the keyword and a Boolean field for yes/no in JSON format as follows\\n{keyword 1: \\\"yes\\\", keyword 2: \\\"no\\\"}\\nIf unsure, return {keyword 1: \\\"n/a\\\", keyword 2: \\\"n/a\\\"}. Never explain anything.\"},{\"role\":\"user\",\"content\":\"\\\"耳機 音箱\\\",\\\"線材 充電器\\\",\\\"耳机 音响\\\",\\\"线材 充电器\\\",\\\"mi sphere camera\\\",\\\"yeelight led智能燈泡\\\",\\\"小米\\\",\"}],\"temperature\":0.6,\"top_p\":1.0,\"frequency_penalty\":0.1,\"presence_penalty\":0.1\n" +
                "}";
        try {
            String response = startPost(jsonBody);

            System.out.println(response);
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    private void getMissingUrl(String file) throws Exception{

        XSSFWorkbook wb = new XSSFWorkbook(file);
        XSSFSheet sheet = wb.getSheetAt(0);
        int start = sheet.getFirstRowNum();
        int end = sheet.getLastRowNum();
//        end = 10;
        List<String> contentList = new ArrayList<>();
        Map<String, List<String>> contentMap = new HashMap<>();
        for (int n = start; n <= end; n++) {
            XSSFRow row = sheet.getRow(n);
            if (row == null || row.getPhysicalNumberOfCells() == 0) {
                continue;
            }
            try {
                String[] cols = getStringArray(row);
                String title = cols[0];
                String url = cols[1];
                String htmlContent = cols[2];
                contentList.add(url);
                if(contentMap.get(url) == null){
                    List<String> list = new ArrayList<>();
                    list.add(title + SPLIT + url + SPLIT + htmlContent);
                    contentMap.put(url, list);
                }else {
                    List<String> list = contentMap.get(url);
                    list.add(title + SPLIT + url + SPLIT + htmlContent);
                    contentMap.put(url, list);
                }

            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        log.info("===1:" + contentList.size());

        List<OpenAiLoggingEntity> openAiLoggingEntityList =  openAiLoggingDAO.getActionLogByActionId(333);
        for(OpenAiLoggingEntity openAiLoggingEntity : openAiLoggingEntityList){
            String url = formatKeywordExtractionDetailInfo(openAiLoggingEntity.getDetailInfo());
            contentList.remove(url);
        }

        openAiLoggingEntityList =  openAiLoggingDAO.getActionLogByActionId(332);
        for(OpenAiLoggingEntity openAiLoggingEntity : openAiLoggingEntityList){
            String url = formatKeywordExtractionDetailInfo(openAiLoggingEntity.getDetailInfo());
            contentList.remove(url);
        }
        openAiLoggingEntityList =  openAiLoggingDAO.getActionLogByActionId(346);
        for(OpenAiLoggingEntity openAiLoggingEntity : openAiLoggingEntityList){
            String url = formatKeywordExtractionDetailInfo(openAiLoggingEntity.getDetailInfo());
            contentList.remove(url);
        }
        log.info("===2:" + contentList.size());
        List<String> lineList= new ArrayList<>();
        for(String url : contentList){
            List<String> list = contentMap.get(url);
            lineList.addAll(list);
        }
        log.info("===3:" + lineList.size());
        File outFile = new File("/home/<USER>/Revolt_4.csv");
        FileUtils.writeLines(outFile, lineList, true);
    }

    private static String formatKeywordExtractionDetailInfo(String detailInfo){
        System.out.println("=======detailInfo:" + detailInfo);
        LinkedList detailInfoList = new Gson().fromJson(detailInfo, LinkedList.class);
        if(CollectionUtils.isEmpty(detailInfoList)){
            return null;
        }
        String url = new Gson().fromJson(new Gson().toJson(detailInfoList.get(0)), Map.class).get("url").toString();
        System.out.println("=======url:" + url);
        return url;
    }

    private void testThread()  {
        try {
            List<Future<Map<String, Object>>> futureTasks = new ArrayList<>();
            for(int i=0;i<5;i++){
                Future f = newThreadPool.submit(new AIActionByOpenAICommand(i));
                futureTasks.add(f);
            }

            for (Future future : futureTasks){
                Map<String, Object> map = (Map<String, Object>)future.get();
                log.info("===map:" + gson.toJson(map));
            }
            waitingForExit();
        }catch (Exception e){
            e.printStackTrace();
            log.info("====error");
        }


    }


    private void startProcess(Boolean isKeywordExtraction, String actionId) {
        log.info("============isKeywordExtraction:" + isKeywordExtraction + ",actionId:" + actionId);
        List<Integer> promptIdList = new ArrayList<>();
        if(isKeywordExtraction){
            promptIdList.add(54);//keyword-extraction
        }else {
            promptIdList.add(17);//tag
            promptIdList.add(77);//tagV2
            promptIdList.add(18);//translate
            promptIdList.add(19);//title
            promptIdList.add(20);//meta
            promptIdList.add(44);//titleV2
            promptIdList.add(62);//metaV2
            promptIdList.add(71);//google translate
            promptIdList.add(72);//metaV3 https://www.wrike.com/open.htm?id=1186373186
            promptIdList.add(73);//Sia Suggest Titles V2 <EMAIL>
        }

        List<Integer> statusList = new ArrayList<>();
        statusList.add(OpenAIActionEntity.STATUS_NEWLY_CREATED);
        statusList.add(OpenAIActionEntity.STATUS_FAILURE);

//        statusList.add(OpenAIActionEntity.STATUS_SUCCESS);//todo test
//        statusList.add(OpenAIActionEntity.STATUS_INVALID);//todo test
//        statusList.add(OpenAIActionEntity.STATUS_PROCESSING);//todo test
        List<OpenAIActionEntity> needProcessList = openAIActionDAO.getNeedProcessActionList(promptIdList, statusList);
        if (CollectionUtils.isEmpty(needProcessList)) {
            log.info("======no action need to process,exit.");
            return;
        }
        int aiActionId = 0;
        log.info("===needProcessList:" + needProcessList.size());
        actionEndLoop:
        for (OpenAIActionEntity openAIActionEntity : needProcessList) {

            try {
                aiActionId = openAIActionEntity.getId();
//                if(aiActionId != 216){//todo for test
//                    continue;
//                }
                if(StringUtils.isNotBlank(actionId) && aiActionId != Integer.parseInt(actionId)){
                    continue;
                }

                int domainId = openAIActionEntity.getOwnDomainId();
                int promptId = openAIActionEntity.getPromptId();
                int userId = openAIActionEntity.getCreateUserId();

//                if (domainId == 9566) {//todo for test
//                    continue;
//                }
//                if(promptId == 19 || promptId == 20){//todo for test
//                    log.info("===SKIP promptId:" + promptId + ",aiActionId:" + aiActionId);
//                    continue;
//                    promptId = 10;//page_title
//                }

//                if(promptId != 44){//todo for test
//                    log.info("===SKIP promptId:" + promptId + ",aiActionId:" + aiActionId);
//                    continue;
//                }

//                if(aiActionId < 30){//todo for test
//                    continue;
//                }

                OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getById(domainId);
                if (ownDomainEntity == null) {
                    log.info("===oidNotExist:" + domainId);
                    continue;
                }
                OwnDomainSettingEntity ownDomainSettingEntity = ownDomainSettingEntityDAO.getSettingByDomainId(domainId);

                OwnDomainTrackingEntity ownDomainTrackingEntity = ownDomainTrackingEntityDAO.getSettingByDomainId(domainId);

                log.info("========================ProcessingAction:" + aiActionId);
                openAIActionDAO.updateStatus(aiActionId, OpenAIActionEntity.STATUS_PROCESSING);
                String variableJson = openAIActionEntity.getVariableJson();

                int engineId = scKeywordRankManager.getSearchEngineId(ownDomainEntity);
                int languageId = scKeywordRankManager.getSearchLanguageId(ownDomainEntity);
                String rankingDate = FormatUtils.formatDate(new Date(), FormatUtils.DATE_PATTERN_2);

                /**
                 * get variable parameter, if not find the key name, will use the default value
                 * eg: "promptVariable":{"selected-criteria":"are product related"} , if na key name "selected-criteria",
                 *      will use the default value from table: open_ai_prompt_message, column:defaultVariableJson
                 */
                HashMap variableJsonMap = gson.fromJson(variableJson, HashMap.class);
//                log.info("===variableJsonEntity:" + gson.toJson(variableJsonMap.get("promptVariable")));
                HashMap promptVariableJsonMap = gson.fromJson(gson.toJsonTree(variableJsonMap.get("promptVariable")).getAsJsonObject(), HashMap.class);
                log.info("===promptVariableJsonMap:" + gson.toJson(promptVariableJsonMap));

                String inputLanguage = "";
                String outputLanguage = "";
                for (Object key : promptVariableJsonMap.keySet()) {
                    log.info("====key:" + key + ",value:" + promptVariableJsonMap.get(key));
                    if(key.equals("input-language")){
                        inputLanguage = promptVariableJsonMap.get("input-language").toString();
                    }
                    if(key.equals("output-language")){
                        outputLanguage = promptVariableJsonMap.get("output-language").toString();
                    }
                }

                VariableJsonEntity variableJsonEntity = gson.fromJson(variableJson, VariableJsonEntity.class);
                log.info("===variableJsonEntity:" + gson.toJson(variableJsonEntity));
                AiActionParams aiActionParams = variableJsonEntity.getAiActionParams();
                String funcCode = aiActionParams.getFuncCode();
                String contentLevel = aiActionParams.getContentLevel();
                String keywordBaseOn = aiActionParams.getKeywordBaseOn();
                aiActionParams.getOid();
                Boolean domainLevel = aiActionParams.getDomainLevel();
                String tagId = aiActionParams.getTagId();
                String targetTagName = aiActionParams.getTargetTagName();
                String engineLanguage = aiActionParams.getEngineLanguage();
                Map<String, String> urlIdKeywordList = aiActionParams.getUrlIdKeywordList();

                if (contentLevel.equals(CONTENT_LEVEL_KEYWORD)) {
                    requestCnt = REQUEST_KW_CNT;
                } else if (contentLevel.equals(CONTENT_LEVEL_URL)) {
                    requestCnt = REQUEST_URL_CNT;
                }

                int apiRequestContent = CONTENT_LENGTH;
                if(funcCode.equals(FUNC_CODE_TRANSLATE_KEYWORD)){
                    apiRequestContent = TRANS_CONTENT_LENGTH;
                }

                //get prompt
                OpenAPromptEntity openAPromptEntity = openAPromptDAO.getById(promptId);
                if (openAPromptEntity == null) {
                    log.error("=========openAPromptEntity null");
                    openAIActionDAO.updateStatus(aiActionId, OpenAIActionEntity.STATUS_INVALID);
                    continue;
                }
                String promptFuncCode = openAPromptEntity.getFuncCode();
                String model = openAPromptEntity.getModel();

                EncodingRegistry registry = Encodings.newDefaultEncodingRegistry();
                if(model.equalsIgnoreCase("gpt-3.5-turbo")){
                    encoding = registry.getEncodingForModel(ModelType.GPT_3_5_TURBO);
                }else if(model.equalsIgnoreCase("gpt-4")){
                    encoding = registry.getEncodingForModel(ModelType.GPT_4);
                }

                //get prompt message
                List<OpenAiPromptMessageEntity> openAiPromptMessageList = openAiPromptMessageDAO.getListByPromptId(promptId);
                if (CollectionUtils.isEmpty(openAiPromptMessageList) && !funcCode.equals(FUNC_CODE_GOOGLE_TRANSLATE_KEYWORD)) {
                    log.error("======no message.");
                    openAIActionDAO.updateStatus(aiActionId, OpenAIActionEntity.STATUS_INVALID);
                    continue;
                }

                Map<String, String> messageMap = new HashMap<>();
                messageEndloop:
                for (OpenAiPromptMessageEntity openAiPromptMessage : openAiPromptMessageList) {//组装变量
                    String role = openAiPromptMessage.getRole();
                    if (role.equals(MESSAGE_ROLE_USER)) {
//                        openAIActionDAO.updateStatus(aiActionId, OpenAIActionEntity.STATUS_INVALID);
                        continue;
                    }

                    String defaultVariableJson = openAiPromptMessage.getDefaultVariableJson();
                    HashMap defaultVariableJsonMap = gson.fromJson(defaultVariableJson, HashMap.class);

                    String content = openAiPromptMessage.getContent();
                    log.info("===content0:" + content);
                    Pattern pattern = Pattern.compile(PATTERN);
                    Matcher matcher = pattern.matcher(content);
                    //get variable {{}}
                    while (matcher.find()) {
                        log.info("role:" + role + ",matcher:" + matcher.group(0));
                        String replaceContent = matcher.group(0).replaceAll("\\{", "\\\\{").replaceAll("\\\\}", "\\\\}");
                        String variableKey = matcher.group(1);

                        String variableValue = "";
                        if (promptVariableJsonMap.get(variableKey) == null) {
                            if (defaultVariableJsonMap.get(variableKey) == null) {
                                log.error("=====not find default value for variable:" + variableKey);
                                openAIActionDAO.updateStatus(aiActionId, OpenAIActionEntity.STATUS_INVALID);
                                continue actionEndLoop;
                            }
                            log.info("---useDVJ:");
                            variableValue = defaultVariableJsonMap.get(variableKey).toString();

                        } else {
                            variableValue = promptVariableJsonMap.get(variableKey).toString();
                            log.info("---usePVJ:");
                        }

                        log.info("===replaceContent:" + replaceContent + ",variableValue:" + variableValue);
                        content = content.replaceAll(replaceContent, java.util.regex.Matcher.quoteReplacement(variableValue));
                        log.info("===content1:" + content);
                    }
                    messageMap.put(role, content);
                }


                Map<String, Object> totalResultMap = new HashMap<>();
                log.info("====tag level");
                if (promptFuncCode.equals(FUNC_CODE_SET_TAG_KEYWORD) || promptFuncCode.equals(FUNC_CODE_TRANSLATE_KEYWORD)) {//keyword-param
                    List<GroupTagEntity> groupTagKwList = groupTagEntityDAO.getKeywordListByTagId(domainId, Integer.parseInt(tagId));
                    if (CollectionUtils.isEmpty(groupTagKwList)) {
                        log.info("===tag empty:" + tagId);
                        openAIActionDAO.updateStatus(aiActionId, OpenAIActionEntity.STATUS_INVALID);
                        continue;
                    }

                    int startIndex = 0;

                    List<String> keywordList = new ArrayList<>();
                    for (GroupTagEntity groupTagEntity : groupTagKwList) {
                        keywordList.add(groupTagEntity.getRawKeywordName());
                    }

                    do {
                        boolean isOverToken = false;
                        String kwContent = "";
                        for (int i = startIndex; i < keywordList.size(); i++) {
                            kwContent += "\"" + keywordList.get(i) + "\",";
                            if (kwContent.length() >= apiRequestContent) {
                                //get token length https://jtokkit.knuddels.de/docs/getting-started/recipes/chatml
                                int tokenCount = encoding.countTokens(kwContent);
                                if (tokenCount >= apiRequestContent) {
                                    startIndex = i + 1;
                                    isOverToken = true;
                                    log.info("====OVERLENGTH:" + tokenCount + ",startIndex:" + startIndex);
                                    break;
                                }
                            }
                        }
                        if (!isOverToken) {
                            startIndex = keywordList.size();
                        }

                        RequestContent requestContent = setBodyMap(model, messageMap, kwContent, openAPromptEntity);
                        String bodyStr = gson.toJson(requestContent);
                        String response = getResponse(aiActionId, bodyStr, promptId, domainId, userId, promptFuncCode, null);
                        if (response == null) {
                            continue actionEndLoop;
                        }
                        //format response
                        try {
                            Map<String, Object> resultMap = formatResponse(response);
                            totalResultMap.putAll(resultMap);
                        } catch (Exception ex) {
                            openAIActionDAO.updateStatus(aiActionId, OpenAIActionEntity.STATUS_INVALID);
                            saveOpenAiLogging(aiActionId, promptId, domainId, userId, gson.toJson(requestContent), response, promptFuncCode, null);
                            log.error("forError1,skip");
                            ex.printStackTrace();
                            continue actionEndLoop;
                        }

                    } while (startIndex < keywordList.size());
                }else if(promptFuncCode.equals(FUNC_CODE_GOOGLE_TRANSLATE_KEYWORD)){//https://www.wrike.com/open.htm?id=1166192162
                    List<GroupTagEntity> groupTagKwList = groupTagEntityDAO.getKeywordListByTagId(domainId, Integer.parseInt(tagId));
                    if (CollectionUtils.isEmpty(groupTagKwList)) {
                        log.info("===tag empty:" + tagId);
                        openAIActionDAO.updateStatus(aiActionId, OpenAIActionEntity.STATUS_INVALID);
                        continue;
                    }

                    if(ownDomainSettingEntity.isEnableDifferentKs() || ownDomainSettingEntity.separateGeoEnabled()){
                        if(StringUtils.isBlank(engineLanguage)){
                            log.info("===no engineLanguage oid:" + domainId + ",tagId" + tagId + ",engineLanguage:" + engineLanguage);
                            openAIActionDAO.updateStatus(aiActionId, OpenAIActionEntity.STATUS_INVALID);
                            continue;
                        }
                        int actionEngineId = Integer.parseInt(engineLanguage.split("-")[0]);
                        int actionLanguageId = Integer.parseInt(engineLanguage.split("-")[1]);
                        String actionDevice = engineLanguage.split("-")[2];
                        groupTagKwList = checkTagEngineLanguageKw(domainId, Integer.parseInt(tagId), actionEngineId, actionLanguageId, actionDevice);
                        if(CollectionUtils.isEmpty(groupTagKwList)){
                            log.info("===no keywords in engineLanguage oid:" + domainId + ",tagId" + tagId + ",engineLanguage:" + engineLanguage);
                            openAIActionDAO.updateStatus(aiActionId, OpenAIActionEntity.STATUS_INVALID);
                            continue;
                        }
                        log.info("===existELKeywordList size:" + groupTagKwList.size());
                    }

                    LinkedList<String> keywordList = new LinkedList<>();
                    List<Map<String, String>> translateKwMapList = new ArrayList<>();

                    for (GroupTagEntity groupTagEntity : groupTagKwList) {
//                        if(isDiffSES && !existELKeywordList.contains(groupTagEntity.getKeywordId())){
//                            log.info("*******skip not in engineKw:" + groupTagEntity.getKeywordId() + ",EL:" + engineLanguage);
//                            continue;
//                        }

                        Map<String,String> transMap = new HashMap<>();
                        keywordList.add(groupTagEntity.getRawKeywordName());
                        transMap.put("keyword", groupTagEntity.getRawKeywordName());
                        translateKwMapList.add(transMap);

                        if(keywordList.size() >= GOOGLE_TRANSLATE_MAX_NUMBER){
                            int totalTokenCount = 0;
                            String detailInfo = "";
                            String bodyStr = "";
                            GoogleTranslateRequestVo googleTranslateRequestVo = new GoogleTranslateRequestVo();
                            googleTranslateRequestVo.setQ(keywordList);
                            googleTranslateRequestVo.setSource(inputLanguage);
                            googleTranslateRequestVo.setTarget(outputLanguage);
                            bodyStr = new Gson().toJson(googleTranslateRequestVo);
                            detailInfo = gson.toJson(translateKwMapList);
                            String response = getResponse(aiActionId, bodyStr, promptId, domainId, userId, promptFuncCode, null);
                            if (response == null) {
                                saveOpenAiLoggingForGoogleTranslate(aiActionId, promptId, domainId, userId, bodyStr, response, totalTokenCount, detailInfo);
                                continue actionEndLoop;
                            }
                            //format response
                            try {

                                LinkedList<TranslatedText> textLinkedList = formatGoogleTranslateResult(response);
                                Map<String, Object> keywordTranslateMap = new HashMap<>();
                                for(int i=0;i<keywordList.size();i++){
                                    String keyword = keywordList.get(i);
                                    totalTokenCount += keyword.length();
                                    String translateKw = textLinkedList.get(i).getTranslatedText();
                                    totalTokenCount += translateKw.length();
                                    keywordTranslateMap.put(keyword, translateKw);
                                }
                                totalResultMap.putAll(keywordTranslateMap);
                                totalTokenCount = totalTokenCount * 10;
                                saveOpenAiLoggingForGoogleTranslate(aiActionId, promptId, domainId, userId, bodyStr, response, totalTokenCount, detailInfo);

                            } catch (Exception ex) {
                                ex.printStackTrace();
                                openAIActionDAO.updateStatus(aiActionId, OpenAIActionEntity.STATUS_INVALID);
                                saveOpenAiLoggingForGoogleTranslate(aiActionId, promptId, domainId, userId, bodyStr, response, totalTokenCount, detailInfo);
                                log.error("forError1,skip");
                                continue actionEndLoop;
                            }
                            keywordList = new LinkedList<>();
                            transMap = new HashMap<>();
                            translateKwMapList = new ArrayList<>();
                        }
                    }

                    if(CollectionUtils.isNotEmpty(keywordList)){
                        int totalTokenCount = 0;
                        String detailInfo = "";
                        String bodyStr = "";
                        GoogleTranslateRequestVo googleTranslateRequestVo = new GoogleTranslateRequestVo();
                        googleTranslateRequestVo.setQ(keywordList);
                        googleTranslateRequestVo.setSource(inputLanguage);
                        googleTranslateRequestVo.setTarget(outputLanguage);
                        bodyStr = new Gson().toJson(googleTranslateRequestVo);
                        detailInfo = gson.toJson(translateKwMapList);
                        String response = getResponse(aiActionId, bodyStr, promptId, domainId, userId, promptFuncCode, null);
                        if (response == null) {
                            saveOpenAiLoggingForGoogleTranslate(aiActionId, promptId, domainId, userId, bodyStr, response, totalTokenCount, detailInfo);
                            continue actionEndLoop;
                        }
                        //format response
                        try {

                            LinkedList<TranslatedText> textLinkedList = formatGoogleTranslateResult(response);
                            Map<String, Object> keywordTranslateMap = new HashMap<>();
                            for(int i=0;i<keywordList.size();i++){
                                String keyword = keywordList.get(i);
                                totalTokenCount += keyword.length();
                                String translateKw = textLinkedList.get(i).getTranslatedText();
                                totalTokenCount += translateKw.length();
                                keywordTranslateMap.put(keyword, translateKw);
                            }
                            totalResultMap.putAll(keywordTranslateMap);
                            totalTokenCount = totalTokenCount * 10;
                            saveOpenAiLoggingForGoogleTranslate(aiActionId, promptId, domainId, userId, bodyStr, response, totalTokenCount, detailInfo);

                        } catch (Exception ex) {
                            ex.printStackTrace();
                            openAIActionDAO.updateStatus(aiActionId, OpenAIActionEntity.STATUS_INVALID);
                            saveOpenAiLoggingForGoogleTranslate(aiActionId, promptId, domainId, userId, bodyStr, response, totalTokenCount, detailInfo);
                            log.error("forError1,skip");
                            continue actionEndLoop;
                        }
                    }

                } else if (promptFuncCode.equals(FUNC_CODE_SUGGEST_TITLE) || promptFuncCode.equals(FUNC_CODE_SUGGEST_META)) {//url-param

                    Integer type = getProcessUrlType(promptFuncCode);
                    if (type == null) {
                        log.error("====urlTypeERRPR:" + promptFuncCode);
                        continue;
                    }

                    Integer targetUrlHtmlDailyDate = ownDomainTrackingEntity.getTargetUrlLatestDate();
                    if (targetUrlHtmlDailyDate == null) {
                        log.error("====targetUrlHtmlDailyDate null:" + aiActionId + ",oid:" + domainId);
                        continue;
                    }

                    DomainSearchEngineRelEntity domainSearchEngineRel = scKeywordRankManager.getPrimarySearchEngine(ownDomainEntity);
                    log.info("==device:" + domainSearchEngineRel.getDevice());
                    boolean isMobile = domainSearchEngineRel.getDevice().equals("m") ? true : false;

                    List<Long> processUrlIdList = new ArrayList<>();
                    List<String> keywordList = new ArrayList<>();
                    for(String urlIdStr: urlIdKeywordList.keySet()){
                        processUrlIdList.add(Long.parseLong(urlIdStr));
                        keywordList.add(urlIdKeywordList.get(urlIdStr));
                    }

                    long startId = 0;
                    while (true) {
                        List<GroupTagEntity> groupTagUrlList = groupTagEntityDAO.getUrlListByTagId(startId, domainId,
                                Integer.parseInt(tagId), requestCnt, processUrlIdList);
                        if (CollectionUtils.isEmpty(groupTagUrlList)) {
                            break;
                        }
                        log.info("==groupTagUrlListSize:" + groupTagUrlList.size());

                        Map<String, Long> urlStrToIdMap = new HashMap<>();
                        Map<Long, String> urlIdToStrMap = new HashMap<>();

                        List<String> murmurHash3List = new ArrayList<>();
                        List<String> urlHash200List = new ArrayList<>();
                        for (GroupTagEntity groupTagEntity : groupTagUrlList) {
                            String MurmurHash3 = MurmurHashUtils.getMurmurHash3_64(groupTagEntity.getUrl());
                            murmurHash3List.add(MurmurHash3);
                            urlHash200List.add(groupTagEntity.getUrlHash());
                            urlStrToIdMap.put(groupTagEntity.getUrl(), groupTagEntity.getUrlId());
                            urlIdToStrMap.put(groupTagEntity.getUrlId(), groupTagEntity.getUrl());
                        }

                        // get domain all response code is 200 urls for filter and original title/meta .
                        List<GroupTagEntity> response200UrlList = getResponseCodeIs200UrlList(groupTagUrlList, domainId, aiActionId,
                                targetUrlHtmlDailyDate, murmurHash3List, type);
                        if (CollectionUtils.isEmpty(response200UrlList)) {
                            log.info("====skipNotHave200UrlDomain -> domainId:" + domainId + " aiActionId:" + aiActionId + " target_url_html_daily_date:" + targetUrlHtmlDailyDate);
                            startId = groupTagUrlList.get(groupTagUrlList.size() - 1).getUrlId();
                            continue;
                        }

                        for (GroupTagEntity groupTagEntity : response200UrlList) {//开始每一条
                            long startTime = System.currentTimeMillis();
                            String url = groupTagEntity.getUrl();
                            List<Long> urlIdList = new ArrayList<>();
                            urlIdList.add(groupTagEntity.getUrlId());
                            String relationKeyword = urlIdKeywordList.get(groupTagEntity.getUrlId().toString());

                            //todo request one url a time
                            Map<Long, List<String>> urlContentMap = new HashMap<>();
                            Map<Long, String> associatedKeywordMap = new HashMap<>();

                            Map<String, Object> apiResultMap = getTitleMetaKeywordByUrl(groupTagEntity, domainId, urlIdList,
                                    type, engineId, languageId, isMobile, ownDomainEntity, relationKeyword);
                            if (apiResultMap == null || apiResultMap.get("contentList") == null || apiResultMap.get("keyword") == null) {
                                log.info("====apiResultMap null");
                                continue;
                            }

                            String asKw = apiResultMap.get("keyword").toString();
                            List<String> contentList = (List<String>) apiResultMap.get("contentList");
                            boolean isOverToken = false;
                            String urlContent = "";

                            String OriginalContent = groupTagEntity.getContent() == null ? "" : groupTagEntity.getContent();
                            if (promptFuncCode.equals(FUNC_CODE_SUGGEST_TITLE)) {//todo content: "Original Title:title1 Target Keyword:keyword1 Sample Titles:["title1","title2,title3"]"
                                urlContent += "\"Original Title:" + OriginalContent + " Target Keyword:" + asKw + " Sample Titles:[";

                            } else if (promptFuncCode.equals(FUNC_CODE_SUGGEST_META)) {//todo content: "Meta Description:meta1 Target Keyword:keyword1 Meta Description:["title1","title2,title3"]"
                                urlContent += "\"Meta Description:" + OriginalContent + " Target Keyword:" + asKw + " Meta Description:[";
                            }

                            if (CollectionUtils.isNotEmpty(contentList)) {
                                urlContent += "\"" + StringUtils.join(contentList, "\",\"");
                            }

                            urlContent += "\"]\"";
                            //get token length https://jtokkit.knuddels.de/docs/getting-started/recipes/chatml
                            int tokenCount = encoding.countTokens(urlContent);
                            log.info("=************tokenCount:" + tokenCount + ",len:" + urlContent.length());

                            if (urlContent.length() == 0) {//no valid url, skip request ai
                                log.info("===no valid url,continue");
                                continue;
                            }

                            urlContent = urlContent.substring(0, urlContent.length() - 1);
                            urlContent += "";

                            RequestContent requestContent = setBodyMap(model, messageMap, urlContent, openAPromptEntity);
                            String bodyStr = gson.toJson(requestContent);
                            String response = getResponse(aiActionId, bodyStr, promptId, domainId, userId, promptFuncCode, url);
                            if (response == null) {
                                continue;
                            }
                            //format response
                            try {
                                String aiResultTitle = formatResponseForNewTitleResult(response);
                                if(StringUtils.isBlank(aiResultTitle)){
                                    log.info("==noResultForAI");
                                    continue;
                                }
                                if(aiResultTitle.startsWith("\"")){
                                    aiResultTitle = aiResultTitle.substring(1);
                                    log.info("====RPStart,"  +aiResultTitle);
                                }
                                if(aiResultTitle.endsWith("\"")){
                                    aiResultTitle = aiResultTitle.substring(0, aiResultTitle.length() -1);
                                    log.info("====RPEnd,"  +aiResultTitle);
                                }

                                totalResultMap.put(url, formatEmoji(aiResultTitle));
                            } catch (Exception ex) {
                                openAIActionDAO.updateStatus(aiActionId, OpenAIActionEntity.STATUS_INVALID);
                                saveOpenAiLogging(aiActionId, promptId, domainId, userId, gson.toJson(requestContent), response, promptFuncCode, url);
                                log.error("forError1,skip");
                                ex.printStackTrace();
                                continue;
                            }
                            long endTime = System.currentTimeMillis();
                            log.info("====耗时:" + (endTime - startTime)/1000   + "s");
//                                }while(startIndex < groupTagUrlList.size());

                        }
                        startId = groupTagUrlList.get(groupTagUrlList.size() - 1).getUrlId();
                    }
                }else if(promptFuncCode.equals(FUNC_CODE_KEYWORD_EXTRACTION)){

                    List<Map<String, String>> contentList = new ArrayList<>();
                    List<String> fields = aiActionParams.getFields();
                    if(aiActionParams.getDataType().equals(DATA_TYPE_SITE_AUDIT)){
                        Long projectId = aiActionParams.getProjectId();
                        Integer crawlId = aiActionParams.getCrawlId();
                        contentList = postRequestSiteClarityApi(domainId, fields, crawlId, aiActionParams);

                    } else if (aiActionParams.getDataType().equals(DATA_TYPE_MANAGED_URL)) {
                        log.info("==stillCoding...");
                        Integer targetUrlHtmlDailyDate = ownDomainTrackingEntity.getTargetUrlLatestDate();
                        if(targetUrlHtmlDailyDate == null){
                            log.info("=====no crawled url!");
                            openAIActionDAO.updateStatus(aiActionId, OpenAIActionEntity.STATUS_INVALID);
                            continue;
                        }

                        boolean customDiv = false;
                        List<String> customDivFieldList = new ArrayList<>();
                        for(String field: fields){
                            if(!field.equalsIgnoreCase("title") && !field.equalsIgnoreCase("description")
                                    && !field.equalsIgnoreCase("h1")){
                                log.info("===customDiv:" + fields);
                                customDiv = true;
                                customDivFieldList.add(field);
                            }
                        }

                        long startId = 0;
                        while (true) {
                            List<GroupTagEntity> groupTagUrlList = groupTagEntityDAO.getUrlListByTagId(startId, domainId, Integer.parseInt(tagId), 200, null);
                            if (CollectionUtils.isEmpty(groupTagUrlList)) {
                                break;
                            }
                            log.info("==groupTagUrlListSize:" + groupTagUrlList.size());

                            List<String> urlHashList = new ArrayList<>();
                            for (GroupTagEntity groupTagEntity : groupTagUrlList) {
                                urlHashList.add(groupTagEntity.getUrlMurmur3Hash());
                            }

                            String trackDate = FormatUtils.formatDate(FormatUtils.toDate(targetUrlHtmlDailyDate.toString(),
                                    FormatUtils.DATE_FORMAT_YYYYMMDD), FormatUtils.DATE_PATTERN_2);

                            Map<String, Map<String, String>> urlCustomDateMap = new HashMap<>();
                            if(customDiv){
                                List<TargetUrlHtmlDaily> customDataUrlList = crawlUrlDao.getCustomDataByUrlList(domainId, trackDate, urlHashList);
                                if(CollectionUtils.isNotEmpty(customDataUrlList)){
                                    for(TargetUrlHtmlDaily customDataUrl: customDataUrlList){
                                        String urlHash = customDataUrl.getUrlMurmurHash();
                                        String customData = StringUtils.isBlank(customDataUrl.getCustomData()) ? null : customDataUrl.getCustomData();
                                        ResultContent[] results = new Gson().fromJson(customData, ResultContent[].class);
                                        if (results != null && results.length > 0) {
                                            for(ResultContent result : results) {
                                                String selector = result.getSelector();
                                                if(customDivFieldList.contains(selector)){
                                                    String[] contentArr = result.getContent();
                                                    if(contentArr == null || contentArr.length == 0){
                                                        log.info("===no content,skip.");
                                                        continue;
                                                    }
                                                    Map<String, String> selectorContentMap = new HashMap<>();
                                                    for(String content: contentArr){
                                                        String customContent = content.replaceAll("\\[","").
                                                                replaceAll("\\]","").replace("\\/","/")
                                                                .replace("\\n", "").replace("\"","");
                                                        Document doc = Jsoup.parse(customContent);
                                                        String formatContent = doc.text();
                                                        log.info("===formatContent:" + formatContent);
                                                        selectorContentMap.put(selector, formatContent);
                                                    }
                                                    urlCustomDateMap.put(urlHash, selectorContentMap);
                                                }
                                            }
                                        }
                                    }
                                }
                            }

                            List<TargetUrlHtmlDaily> targetUrlHtmlList = crawlUrlDao.getDataByUrlList(domainId, trackDate, urlHashList);
                            for(TargetUrlHtmlDaily targetUrlHtmlDaily: targetUrlHtmlList){

                                Map<String, String> contentMap = new HashMap<>();
                                String responseCode = targetUrlHtmlDaily.getResponseCode();
                                String url = targetUrlHtmlDaily.getUrl();
                                log.info("==============url:" + url);
                                if(!responseCode.equals("200")){
                                    log.info("===SKIPNOT200URLS:" + url);
                                    continue;
                                }
                                String urlHash = targetUrlHtmlDaily.getUrlMurmurHash();
                                String title = StringUtils.isBlank(targetUrlHtmlDaily.getTitle()) ? null : targetUrlHtmlDaily.getTitle();
                                String meta = StringUtils.isBlank(targetUrlHtmlDaily.getDescription()) ? null : targetUrlHtmlDaily.getDescription();
                                String h1Json = StringUtils.isBlank(targetUrlHtmlDaily.getH1()) ? null : targetUrlHtmlDaily.getH1();
                                String lastTrackDate = targetUrlHtmlDaily.getTrackDate();

                                List<String> h1ContentList = new ArrayList<>();
                                if(StringUtils.isNotBlank(h1Json)){
                                    JSONArray h1JsonArray = JSONArray.parseArray(h1Json);
                                    h1ContentList = h1JsonArray.toJavaList(String.class);
                                }

                                String content = "";
                                if(fields.contains("title")){
                                    content += "\"title\":\"" + title + "\"";
                                    int titleTokenCnt = encoding.countTokens(title);
                                    log.info("===titleTokenCount:" + titleTokenCnt);
                                }
                                if(fields.contains("description")){
                                    if(StringUtils.isNotBlank(content) && content.length() > 5){
                                        content += ", ";
                                    }
                                    String metaContent = "\"description\":\"" + meta + "\"";
                                    content += metaContent;
                                    int metaTokenCnt = encoding.countTokens(meta);
                                    log.info("===metaTokenCount:" + metaTokenCnt);
                                }
                                if(fields.contains("h1") && CollectionUtils.isNotEmpty(h1ContentList)){
                                    if(StringUtils.isNotBlank(content) && content.length() > 5){
                                        content += ", ";
                                    }
                                    content += "\"h1\": [";
                                    content += "\"" + StringUtils.join(h1ContentList, "\",\"") + "\"]";
                                    int h1TokenCnt = encoding.countTokens(StringUtils.join(h1ContentList, "\",\""));
                                    log.info("===h1tokenCount:" + h1TokenCnt);
                                }

                                if(customDiv){
                                    Map<String, String> customUrlMap = urlCustomDateMap.get(urlHash);
                                    if(customUrlMap != null){
                                        for(String selector: customUrlMap.keySet()){//get custom div content
                                            log.info("=====selector:" + selector + ",content:" + customUrlMap.get(selector));
                                            if(StringUtils.isNotBlank(content) && content.length() > 5){
                                                content += ", ";
                                            }
                                            content += "\"" + selector + "\":\"" + customUrlMap.get(selector) + "\"";
                                        }
                                    }
                                }

                                if(StringUtils.isBlank(content)){
                                    log.info("====NOContent,skip:" + url);
                                    continue;
                                }

                                //log.info("====content:  " + content);
                                int totalTokenCnt = encoding.countTokens(content);
                                log.info("===totalTokenCnt:" + totalTokenCnt);
                                if(totalTokenCnt >= KEYWORD_EXTRACTION_TOKEN_LENGTH){
                                    log.info("*********tokenCountOverLimit,split.");
                                    contentList.addAll(splitOverLimitContent(meta, url));
                                }else {
                                    contentMap.put("url", url);
                                    contentMap.put("content", content);
                                    log.info("====requestBody:" + gson.toJson(content));
                                    contentList.add(contentMap);
                                }
                            }
                            startId = groupTagUrlList.get(groupTagUrlList.size() - 1).getUrlId();
                        }

                    }

                    if(CollectionUtils.isEmpty(contentList)){
                        log.info("=====contentListEMPTY!");
                        openAIActionDAO.updateStatus(aiActionId, OpenAIActionEntity.STATUS_SUCCESS);
                        continue;
                    }
                    System.out.println("contentList:" + contentList.size());

                    List<List<Map<String, String>>> lists = CollectionUtil.split(contentList, 10);
                    try {//todo multi threads
                        List<Future<Map<String, Object>>> futureTasks = new ArrayList<>();

                        for (List<Map<String, String>> list : lists) {
                            AIActionByOpenAICommand aiActionByOpenAICommand = new AIActionByOpenAICommand(
                                    aiActionId, promptId, domainId, userId, promptFuncCode,
                                    list, messageMap, model, openAPromptEntity);
                            Future f = newThreadPool.submit(aiActionByOpenAICommand);
                            futureTasks.add(f);
                        }

                        if(CollectionUtils.isNotEmpty(futureTasks)){
                            for (Future future : futureTasks){
                                Map<String, Object> map = (Map<String, Object>)future.get();
                                log.info("===map:" + gson.toJson(map));
                                for(String key: map.keySet()){
                                    totalResultMap.put(key, map.get(key));
                                }
                            }
                        }
                    }catch (Exception e){
                        e.printStackTrace();
                        log.info("====error");
                        openAIActionDAO.updateStatus(aiActionId, OpenAIActionEntity.STATUS_INVALID);
                        continue;
                    }

                    /**
                     * 单线程执行
                     */
//                    int index =0;
//                    for(Map<String, String> contentMap : contentList){
//                        index ++;
//                        String content = contentMap.get("content");
//                        String url = contentMap.get("url");
//                        RequestContent requestContent = setBodyMap(model, messageMap, content, openAPromptEntity);
//                        String bodyStr = gson.toJson(requestContent);
//                        String response = getResponse(aiActionId, bodyStr, promptId, domainId, userId, promptFuncCode, url);
//                        if (response == null) {
//                            continue actionEndLoop;
//                        }
//                        totalResultMap.put(index + "_" + url, url);
//                    }

                }
                processResultWithFunction(aiActionId, domainId, funcCode, targetTagName, totalResultMap, engineLanguage);

                /**
                 * tag: queuebase脚本更新到success
                 * translate: cdb 同步完成发worker，更新到success
                 */
                if (promptFuncCode.equals(FUNC_CODE_SUGGEST_TITLE) || promptFuncCode.equals(FUNC_CODE_SUGGEST_META)
                        || promptFuncCode.equals(FUNC_CODE_KEYWORD_EXTRACTION)) {
                    openAIActionDAO.updateStatus(aiActionId, OpenAIActionEntity.STATUS_SUCCESS);
                }

            } catch (Exception e) {
                e.printStackTrace();
                openAIActionDAO.updateStatus(aiActionId, OpenAIActionEntity.STATUS_FAILURE);
            }

        }
        waitingForExit();
    }

    /**
     * * 查找关联关键字 steps :Associated Keyword -> Ranking Keyword -> RG Keywords
     */
    private Map<String, Object> getTitleMetaKeywordByUrl(GroupTagEntity groupTagEntity, int domainId, List<Long> urlIdList, int type,
                                                         int engineId, int languageId, boolean isMobile,
                                                         OwnDomainEntity ownDomainEntity, String relationKeyword) throws Exception {

        Map<String, Object> resultMap = new HashMap<>();

        //第一步：从keywordurl关系表找  --> updated selected keyword from UI https://www.wrike.com/open.htm?id=1233498661
//        List<KeywordTargetUrlEntity> keywordUrlList = keywordTargeturlEntityDAO.getKeywordNameByTargetUrlId(domainId, urlIdList);//会有重复的urlId，keyword-url 多对多
//        log.info("==========STEP1,keywordUrlListSIZE:" + keywordUrlList.size());
//        List<String> keywordHashList = new ArrayList<>();

        String domainQueryFrequency = "";
        if (ownDomainEntity.isBiWeeklyRankFrequency()) {
            domainQueryFrequency = "BIWEEK";
        } else if (ownDomainEntity.isWeeklyRankFrequency()) {
            domainQueryFrequency = "WEEK";
        } else {
            domainQueryFrequency = "DAY";
        }

//        if (CollectionUtils.isNotEmpty(keywordUrlList)) {
//            for (KeywordTargetUrlEntity keywordTargetUrl : keywordUrlList) {
//                String keywordName = URLDecoder.decode(keywordTargetUrl.getKeywordName(), "utf-8");
//                String keywordHash = keywordTargetUrl.getKeywordHash();
//                keywordHashList.add(keywordHash);

        try {
            Map<String, Object> paramMap = new HashMap<String, Object>();
            paramMap.put("accessToken", ClarityDBAPIUtils.ACCESS_TOKEN_VALUE);
            paramMap.put("countInTop", 1);
            paramMap.put("domainQueryFrequency", domainQueryFrequency);
            paramMap.put("engine", engineId);
            paramMap.put("language", languageId);
            paramMap.put("mobile", isMobile);
            paramMap.put("ownDomainName", ownDomainEntity.getDomain());
            paramMap.put("domainId", domainId);
            paramMap.put("ownDomainId", domainId);
            paramMap.put("urlList", null);
            paramMap.put("queryKeywords", new String[]{relationKeyword});
            log.info("====apiREQ1:" + new Gson().toJson(paramMap));
            String apiResponse = ClarityDBAPIUtils.simplePost(API_METHOD, new Gson().toJson(paramMap));
            log.info("====apiRES1:" + apiResponse);

            if (StringUtils.length(apiResponse) > 30 && StringUtils.contains(apiResponse, "\"keyword_name\"")) {
                ApiResponseVo apiResponseVo = new Gson().fromJson(apiResponse, ApiResponseVo.class);
                log.info("====apiResponseVo:" + new Gson().toJson(apiResponseVo));
                List<ApiResponseDataVo> apiResponseDataVos = apiResponseVo.getData();
                resultMap.put("keyword", relationKeyword);
                for (ApiResponseDataVo apiResponseDataVo : apiResponseDataVos) {

                    List<String> contentList = new ArrayList<>();
                    if (type == 1) {
                        contentList.add(formatEmoji(apiResponseDataVo.getLabel()));
                    } else if (type == 2) {
                        contentList.add(formatEmoji(apiResponseDataVo.getMeta()));
                    }
                    resultMap.put("contentList", contentList);

                }
                return resultMap;
            }

        } catch (Exception e) {
            log.info("====getAPIError1:" + API_METHOD);
            e.printStackTrace();
            throw e;
        }
//            }
//        }

        Thread.sleep(500);
        //第二步：剩下的直接从daily ranking找
        log.info("*************START 2");
        try {
            Map<String, Object> paramMap = new HashMap<String, Object>();
            paramMap.put("accessToken", ClarityDBAPIUtils.ACCESS_TOKEN_VALUE);
            paramMap.put("countInTop", 1);
            paramMap.put("domainQueryFrequency", domainQueryFrequency);
            paramMap.put("engine", engineId);
            paramMap.put("language", languageId);
            paramMap.put("mobile", isMobile);
            paramMap.put("ownDomainName", ownDomainEntity.getDomain());
            paramMap.put("domainId", domainId);
            paramMap.put("ownDomainId", domainId);
            paramMap.put("ctrList", new String[]{"0.193", "2", "0.114", "3", "0.077", "4", "0.052", "5", "0.041", "6", "0.033", "7", "0.026"
                    , "8", "0.021", "9", "0.02", "10", "0.022", "0"});
            paramMap.put("queryKeywords", new String[]{relationKeyword});
            paramMap.put("urlList", new String[]{groupTagEntity.getUrl()});
            log.info("====apiREQ2:" + new Gson().toJson(paramMap));
            String apiResponse = ClarityDBAPIUtils.simplePost(API_METHOD, new Gson().toJson(paramMap));
            log.info("====apiRES2:" + apiResponse);

            if (StringUtils.length(apiResponse) > 30 && StringUtils.contains(apiResponse, "\"keyword_name\"")) {
                ApiResponseVo apiResponseVo = new Gson().fromJson(apiResponse, ApiResponseVo.class);
                log.info("====apiResponseVo:" + new Gson().toJson(apiResponseVo));
                List<ApiResponseDataVo> apiResponseDataVos = apiResponseVo.getData();

                for (ApiResponseDataVo apiResponseDataVo : apiResponseDataVos) {
                    String keywordName = apiResponseDataVo.getKeyword_name();
                    resultMap.put("keyword", keywordName);
                    List<String> contentList = new ArrayList<>();
                    if (type == 1) {
                        contentList.add(formatEmoji(apiResponseDataVo.getLabel()));
                    } else if (type == 2) {
                        contentList.add(formatEmoji(apiResponseDataVo.getMeta()));
                    }
                    resultMap.put("contentList", contentList);
                }
                return resultMap;
            }

        } catch (Exception e) {
            log.info("====getAPIError2:" + API_METHOD);
            e.printStackTrace();
            throw e;
        }

        Thread.sleep(500);
        //第三步: to query Research Grid Keyword and its Competitor URLs
        //https://www.wrike.com/open.htm?id=1135879833
        log.info("*************START 3");
        PaginationQueryVO pagePaginationQueryVO = new PaginationQueryVO();
        pagePaginationQueryVO.setPage(1);
        pagePaginationQueryVO.setPageSize(10);

        try {
            Map<String, Object> paramMap = new HashMap<String, Object>();
            paramMap.put("accessToken", ClarityDBAPIUtils.ACCESS_TOKEN_VALUE);
            paramMap.put("ownDomainId", domainId);
            paramMap.put("engineId", engineId);
            paramMap.put("languageId", languageId);
            paramMap.put("query", groupTagEntity.getUrl());
            paramMap.put("device", isMobile ? "m" : "d");
            paramMap.put("paginationQueryVO", pagePaginationQueryVO);

            log.info("====apiREQ3:" + new Gson().toJson(paramMap));
            String apiResponse = ClarityDBAPIUtils.simplePost(API_METHOD_TOPCOMPETITOR, new Gson().toJson(paramMap));
            log.info("====apiRES3:" + apiResponse);

            if (StringUtils.length(apiResponse) > 30 && StringUtils.contains(apiResponse, "\"keyword_name\"")) {
                ApiResponseVo apiResponseVo = new Gson().fromJson(apiResponse, ApiResponseVo.class);
                log.info("====apiResponseVo:" + new Gson().toJson(apiResponseVo));
                List<ApiResponseDataVo> apiResponseDataVos = apiResponseVo.getData();
                for (ApiResponseDataVo apiResponseDataVo : apiResponseDataVos) {
                    String keywordName = apiResponseDataVo.getKeyword_name();
                    resultMap.put("keyword", keywordName);
                    List<String> contentList = new ArrayList<>();
                    if (type == 1) {
                        contentList.add(formatEmoji(apiResponseDataVo.getLabel()));
                    } else if (type == 2) {
                        contentList.add(formatEmoji(apiResponseDataVo.getMeta()));
                    }
                    resultMap.put("contentList", contentList);
                }
            }
            return resultMap;
        } catch (Exception e) {
            log.info("====getAPIError3:" + API_METHOD_TOPCOMPETITOR);
            e.printStackTrace();
            throw e;
        }
    }

    private List<GroupTagEntity> getResponseCodeIs200UrlList(List<GroupTagEntity> groupTagUrlList, int domainId, int aiActionId,
                                                             Integer dailyDate, List<String> murmurHash3List, int type) {
        List<String> urlHashList = new ArrayList<>();
        List<Map<String, Object>> response200UrlList = null;
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        try {
            response200UrlList = pageClarityDAO.getUrlResponseCodeByDomainId(domainId, dateFormat.parse(dailyDate.toString()), murmurHash3List);
        } catch (ParseException e) {
            log.info("===getUrlResponseCodeByDomainId domainId:" + domainId + " aiActionId:" + aiActionId + " dailyDate:" + dailyDate);
            e.printStackTrace();
        }
        if (CollectionUtils.isEmpty(response200UrlList)) {
            return null;
        }

        log.info("====response200UrlList:" + response200UrlList.size() + " urlMurmur3HashList:" + urlHashList.size());

        Map<String, String> response200UrlMap = new HashMap<>();
        for (Map<String, Object> objectMap : response200UrlList) {
            String urlMurmurHash = objectMap.get("urlMurmur3Hash").toString();
            urlHashList.add(urlMurmurHash);
            String title = objectMap.get("title").toString();
            String meta = objectMap.get("meta").toString();
            if (type == 1) {
                response200UrlMap.put(urlMurmurHash, title);
            } else if (type == 2) {
                response200UrlMap.put(urlMurmurHash, meta);
            }
        }

        log.info("===beforeFilter200Url groupTagUrlList:" + groupTagUrlList.size());
        //filter 200 urls
        List<GroupTagEntity> filteredGroupTagUrlList = filterUrl(groupTagUrlList, domainId, aiActionId, urlHashList);
        if (CollectionUtils.isEmpty(filteredGroupTagUrlList)) {
            log.info("====no 200 urls,domainId:" + domainId + " aiActionId:" + aiActionId);
            return null;
        }
        log.info("====afterFilter200Url:" + filteredGroupTagUrlList.size() + ",domainId:" + domainId + " aiActionId:" + aiActionId);

        for (GroupTagEntity groupTagEntity : filteredGroupTagUrlList) {
            String url = groupTagEntity.getUrl();
            String urlMurmurHash = MurmurHashUtils.getMurmurHash3_64(url);
            String content = response200UrlMap.get(urlMurmurHash);
            groupTagEntity.setContent(content);
        }

        return filteredGroupTagUrlList;
    }

    private List<GroupTagEntity> filterUrl(List<GroupTagEntity> groupTagUrlList, int domainId, int aiActionId, List<String> urlHashList) {
        List<GroupTagEntity> resultList = new ArrayList<>();
        groupTagUrlList.forEach(tarUrl -> {
            String url = tarUrl.getUrl();
            String urlMurmurHash = MurmurHashUtils.getMurmurHash3_64(url);
            if (urlHashList.contains(urlMurmurHash)) {
                resultList.add(tarUrl);
            } else {
                log.info("===Not200Url: domainId:" + domainId + " aiActionId:" + aiActionId + " tagName:" + tarUrl.getTagName() + " tarUrlId:" + tarUrl.getUrlId());
            }
        });
        return resultList;
    }

    private String getResponse(int aiActionId, String bodyStr, int promptId, int domainId, int userId, String promptFuncCode, String url) {
        int errorTime = 0;
        String response = "";
        while (true) {
            if (errorTime >= RETRY_TIMES) {
                openAIActionDAO.updateStatus(aiActionId, OpenAIActionEntity.STATUS_FAILURE);
                if(!promptFuncCode.equals(FUNC_CODE_GOOGLE_TRANSLATE_KEYWORD)){
                    saveOpenAiLogging(aiActionId, promptId, domainId, userId, bodyStr, response, promptFuncCode, url);
                }
                return null;
            }
            try {
                if(promptFuncCode.equals(FUNC_CODE_GOOGLE_TRANSLATE_KEYWORD)){
                    response = postGoogleTranslate(bodyStr);
                }else {
                    response = startPost(bodyStr);
                    saveOpenAiLogging(aiActionId, promptId, domainId, userId, bodyStr, response, promptFuncCode, url);
                }
                return response;
            } catch (RuntimeException re){
                log.error("=====errorAction:" + aiActionId);
                openAIActionDAO.updateStatus(aiActionId, OpenAIActionEntity.STATUS_INVALID);
                return null;
            } catch (Exception e) {
                errorTime++;
                e.printStackTrace();
                log.error("ET1:" + errorTime + "SL10S");
                try {
                    Thread.sleep(1000 * 10);
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }
        }
    }

    private RequestContent setBodyMap(String model, Map<String, String> messageMap, String kwContent, OpenAPromptEntity openAPrompt) {

        RequestContent requestContent = new RequestContent();
        requestContent.setModel(model);
        requestContent.setTemperature(openAPrompt.getTemperature() == null ? temperature : openAPrompt.getTemperature());
        requestContent.setTop_p(openAPrompt.getTopP() == null ? topP : openAPrompt.getTopP());
        requestContent.setFrequency_penalty(openAPrompt.getFrequencyPenalty() == null ? frequencyPenalty : openAPrompt.getFrequencyPenalty());
        requestContent.setPresence_penalty(openAPrompt.getPresencePenalty() == null ? presencePenalty : openAPrompt.getPresencePenalty());

        List<Message> messages = new ArrayList<>();
        for (String role : messageMap.keySet()) {
            Message message = new Message();
            message.setRole(role);
            message.setContent(messageMap.get(role));
            messages.add(message);
        }

        Message kwMessage = new Message();
        kwMessage.setRole(MESSAGE_ROLE_USER);
        kwMessage.setContent(kwContent);
        messages.add(kwMessage);
        log.info("====messages:" + gson.toJson(messages));

        requestContent.setMessages(messages);
        return requestContent;
    }

    private static String startPost(String bodyStr) throws Exception {
        log.info("************* bodyStr:" + bodyStr);
        long startTime = System.currentTimeMillis();
        String resultString = "";
        HttpResponse<String> response = null;
        try {
//            Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress("127.0.0.1", 10809));

            Gson gson = new Gson();
            RequestContent requestContent = gson.fromJson(bodyStr, RequestContent.class);

            Map<String, String> headersMap = new HashMap<>();
            headersMap.put("authorization", API_KEY);
            headersMap.put("content-type", "application/json");

            List<OpenAiRequestWrapper> openAiRequestWrapperList = new ArrayList<>();
            OpenAiRequestWrapper openAiRequestWrapper = new OpenAiRequestWrapper();
            openAiRequestWrapper.setProvider(PROVIDER);
            openAiRequestWrapper.setEndpoint(ENDPOINT);
            openAiRequestWrapper.setHeaders(headersMap);
            openAiRequestWrapper.setQuery(requestContent);
            openAiRequestWrapperList.add(openAiRequestWrapper);
//            bodyStr = gson.toJson(openAiRequestWrapper);
            bodyStr = gson.toJson(openAiRequestWrapperList);
            log.info("************* WrapperbodyStr:" + bodyStr);

            response = Unirest.post(REQUEST_URL)
                    .header("Content-Type", "application/json")
                    .body(bodyStr)
                    .asString();
            resultString = response.getBody();

            int statusCode = response.getStatus();

            log.info("====TIME:" + ((System.currentTimeMillis() - startTime) / 1000) + "s");
            log.info("====response:" + resultString);
            return resultString;

        } catch (Exception e) {
            log.error("=====Failed post:" + bodyStr);
            e.printStackTrace();
            throw e;
        }
//        return null;
    }

    private static String postGoogleTranslate(String bodyStr) throws Exception {
        log.info("*************postGoogleTranslate bodyStr:" + bodyStr);
        long startTime = System.currentTimeMillis();
        String resultString = "";
        Response response = null;
        try {
//            Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress("127.0.0.1", 10809));

            OkHttpClient client = new OkHttpClient().newBuilder().callTimeout(5, TimeUnit.MINUTES)
//            OkHttpClient client = new OkHttpClient().newBuilder().proxy(proxy).callTimeout(5, TimeUnit.MINUTES)
                    .connectTimeout(10, TimeUnit.MINUTES)
                    .readTimeout(10, TimeUnit.MINUTES)
                    .writeTimeout(10, TimeUnit.MINUTES)
                    .build();
            MediaType mediaType = MediaType.parse("application/json");
            RequestBody body = RequestBody.create(mediaType, bodyStr);
            Request request = new Request.Builder()
                    .url(GOOGLE_TRANSLATE_REQUEST_URL)
                    .method("POST", body)
                    .addHeader("X-goog-api-key", GOOGLE_TRANSLATE_API_KEY)
                    .addHeader("Content-Type", "application/json")
                    .build();

            response = client.newCall(request).execute();
            if (!response.isSuccessful()) {
                log.info("Failed post msg:" + response.body().string());
                throw new RuntimeException("Unexcepted code:" + response);
            }
            resultString = response.body().string();
            log.info("====TIME:" + ((System.currentTimeMillis() - startTime) / 1000) + "s");
            System.out.println("====response:" + resultString);
            return resultString;

        } catch (Exception e) {
            System.out.println("=====Failed post:" + bodyStr);
            e.printStackTrace();
            throw e;
        } finally {
            if (response != null) {
                log.info("====close");
                response.body().close();
                response.close();
            }
        }
//        return null;
    }

    private static Map<String, Object> formatResponse(String content) throws Exception {
        Gson gson1 = new GsonBuilder().disableHtmlEscaping().create();
        Map<String, Object> map = new HashMap<>();
        try {
            ResponseContent responseContent = gson1.fromJson(content, ResponseContent.class);
            List<Choices> choicesList = responseContent.getChoices();
            if (CollectionUtils.isNotEmpty(choicesList)) {
                for (Choices choices : choicesList) {
                    String finishReason = choices.getFinish_reason();
                    if (!finishReason.equals(FINISH_REASON_STOP)) {
                        log.error("=====finish error, split and try again:" + finishReason);
                    }
                    if (choices.getMessage() == null) {
                        log.error("===MSGNULL" + gson1.toJson(responseContent));
                        continue;
                    }
                    String messageContent = choices.getMessage().getContent();
                    if (StringUtils.isBlank(messageContent)) {
                        log.error("===messageContentNULL" + gson1.toJson(responseContent));
                        continue;
                    }
                    log.info("======" + gson1.toJson(messageContent));
                    map = JSONObject.parseObject(messageContent, new TypeReference<Map<String, Object>>() {
                    });

                    for (String key : map.keySet()) {
                        String keyword = key;
                        String value = map.get(key).toString();
                        log.info("key: " + keyword + " ,value:" + value);
                    }

                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("====formatResponseError:" + content);
            throw e;
        }
        return map;
    }

    private void saveOpenAiLogging(int openAIActionId, int promptId, int ownDomainId, int userId, String requestBody, String responseJson, String promptFuncCode, String url) {
        OpenAiLoggingEntity openAiLoggingEntity = new OpenAiLoggingEntity();
        openAiLoggingEntity.setOpenAIActionId(openAIActionId);
        openAiLoggingEntity.setPromptId(promptId);
        openAiLoggingEntity.setOwnDomainId(ownDomainId);
        String formatRequestBody = CommonDataService.filterEmoji(requestBody);
        openAiLoggingEntity.setVariableJson(formatRequestBody);
        String formatResponseJson = CommonDataService.filterEmoji(responseJson);
        openAiLoggingEntity.setResponseJson(formatResponseJson);
        openAiLoggingEntity.setUserId(userId);

        setLogging(requestBody, responseJson, promptFuncCode, openAiLoggingEntity, url);
        log.info("=====detailInfo:" + openAiLoggingEntity.getDetailInfo());
        openAiLoggingDAO.insert(openAiLoggingEntity);
    }

    private void saveOpenAiLoggingForGoogleTranslate(int openAIActionId, int promptId, int ownDomainId, int userId,
                                                     String requestBody, String responseJson,
                                                     int totalToken, String detailInfo) {

        OpenAiLoggingEntity openAiLoggingEntity = new OpenAiLoggingEntity();
        openAiLoggingEntity.setOpenAIActionId(openAIActionId);
        openAiLoggingEntity.setPromptId(promptId);
        openAiLoggingEntity.setOwnDomainId(ownDomainId);
        String formatRequestBody = CommonDataService.filterEmoji(requestBody);
        openAiLoggingEntity.setVariableJson(formatRequestBody);
        String formatResponseJson = CommonDataService.filterEmoji(responseJson);
        openAiLoggingEntity.setResponseJson(formatResponseJson);
        openAiLoggingEntity.setUserId(userId);
        openAiLoggingEntity.setTotalTokens(totalToken);
        openAiLoggingEntity.setDetailInfo(detailInfo);
        log.info("=====totalToken:" + totalToken + ",detailInfo:" + openAiLoggingEntity.getDetailInfo());
        openAiLoggingDAO.insert(openAiLoggingEntity);
    }

    /**
     *  "message": {
     *                 "role": "assistant",
     *                 "content": "\"Improve Product Visibility Online with SKU Schema - Schema Markup Generator, Tester, Deployer\""
     *             }
     * @param content
     * @return
     * @throws Exception
     */
    private static String formatResponseForNewTitleResult(String content) throws Exception {
        Gson gson1 = new GsonBuilder().disableHtmlEscaping().create();
        try {
            ResponseContent responseContent = gson1.fromJson(content, ResponseContent.class);
            List<Choices> choicesList = responseContent.getChoices();
            if (CollectionUtils.isNotEmpty(choicesList)) {
                for (Choices choices : choicesList) {
                    String finishReason = choices.getFinish_reason();
                    if (!finishReason.equals(FINISH_REASON_STOP)) {
                        log.error("=====finish error, split and try again:" + finishReason);
                    }
                    if (choices.getMessage() == null) {
                        log.error("===MSGNULL" + gson1.toJson(responseContent));
                        continue;
                    }
                    String messageContent = choices.getMessage().getContent();
                    if (StringUtils.isBlank(messageContent)) {
                        log.error("===messageContentNULL" + gson1.toJson(responseContent));
                        continue;
                    }
                    log.info("======" + messageContent);
                    return messageContent;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("====formatResponseForNewTitleResultError:" + content);
            throw e;
        }
        return null;
    }

    private static String formatResponseForKeywordExtraction(){
        String content = "" +
                "{ \"id\": \"chatcmpl-7lDce5RFaD425phnHoYyKON2oyDuh\", \"object\": \"chat.completion\", \"created\": 1691488136, \"model\": \"gpt-3.5-turbo-0613\", \"choices\": [ { \"index\": 0, \"message\": { \"role\": \"assistant\", \"content\": \"[{\\\"entity\\\":\\\"301 Moved Permanently\\\",\\\"entityType\\\":\\\"title\\\",\\\"entityAlternativeName\\\":[\\\"301 Moved Permanently\\\"]},{\\\"entity\\\":\\\"301 Moved Permanently\\\",\\\"entityType\\\":\\\"h1\\\",\\\"entityAlternativeName\\\":[\\\"301 Moved Permanently\\\"]}]\" }, \"finish_reason\": \"stop\" } ], \"usage\": { \"prompt_tokens\": 119, \"completion_tokens\": 51, \"total_tokens\": 170 } }" +
                "";
        Gson gson1 = new GsonBuilder().disableHtmlEscaping().create();
        try {
            ResponseContent responseContent = gson1.fromJson(content, ResponseContent.class);
            List<Choices> choicesList = responseContent.getChoices();
            if (CollectionUtils.isNotEmpty(choicesList)) {
                for (Choices choices : choicesList) {

                    if (choices.getMessage() == null) {
                        log.error("===MSGNULL" + gson1.toJson(responseContent));
                        continue;
                    }
                    String messageContent = choices.getMessage().getContent();
                    if (StringUtils.isBlank(messageContent)) {
                        log.error("===messageContentNULL" + gson1.toJson(responseContent));
                        continue;
                    }
                    log.info("======" + messageContent);
                    List list = gson1.fromJson(messageContent , List.class);
                    Map map = gson1.fromJson(gson1.toJson(list.get(0)), Map.class);
                    String entity = map.get("entity").toString();
                    String entityType = map.get("entityType").toString();
                    List entityAlternativeNameList = gson1.fromJson(gson1.toJson(map.get("entityAlternativeName")), List.class);
                    log.info("==entity:" + entity + ",entityType:" + entityType + ",entityAlternativeName:" + StringUtils.join(entityAlternativeNameList, ","));
                    return messageContent;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("====formatResponseForNewTitleResultError:" + content);
            throw e;
        }
        return null;
    }

    private void setLogging(String requestBody, String responseJson, String promptFuncCode, OpenAiLoggingEntity openAiLoggingEntity, String url) {
        try {
            Map<String, Object> resultMap = formatResponseForLogging(requestBody, responseJson, promptFuncCode, url);
            openAiLoggingEntity.setTotalTokens((int) resultMap.get("totalTokens"));
            openAiLoggingEntity.setDetailInfo((String) resultMap.get("detailInfo"));
        } catch (Exception e) {
            openAiLoggingEntity.setTotalTokens(0);
            openAiLoggingEntity.setDetailInfo("");
            log.info("=====setLoggingInfoError: aiActionId:" + openAiLoggingEntity.getOpenAIActionId() + " domainId:" + openAiLoggingEntity.getOwnDomainId());
            e.printStackTrace();
        }
    }

    private Map<String, Object> formatResponseForLogging(String requestBody, String responseJson, String promptFuncCode, String url) throws Exception {
        Gson gson1 = new GsonBuilder().disableHtmlEscaping().create();
        Map<String, Object> map = new HashMap<>();
        String detailInfo = "";
        Integer totalTokens = 0;
        try {
            ResponseContent response = gson1.fromJson(responseJson, ResponseContent.class);
            Usage usage = response.getUsage();
            totalTokens = usage.getTotal_tokens();

            RequestContent request = gson1.fromJson(requestBody, RequestContent.class);
            if (FUNC_CODE_SET_TAG_KEYWORD.equals(promptFuncCode) || FUNC_CODE_TRANSLATE_KEYWORD.equals(promptFuncCode)) {
                List<Message> messages = request.getMessages();
                for (Message message : messages) {
                    if (MESSAGE_ROLE_USER.equals(message.getRole())) {
                        String content = message.getContent();
                        content = content.substring(0, content.length() - 1);
                        String[] kwArr = content.split(",");
                        List<Map<String, String>> kwList = new ArrayList<>();
                        for (int i = 0; i < kwArr.length; i++) {
                            Map<String, String> kwMap = new HashMap<>();
                            String keyword = kwArr[i].replaceAll("\\\"", "");
                            kwMap.put("keyword", keyword);
                            kwList.add(kwMap);
                        }
                        detailInfo = gson1.toJson(kwList);
                    }
                }
            } else if (FUNC_CODE_SUGGEST_TITLE.equals(promptFuncCode) || FUNC_CODE_SUGGEST_META.equals(promptFuncCode)
                    || FUNC_CODE_KEYWORD_EXTRACTION.equals(promptFuncCode)) {
                List<Map<String, String>> urlList = new ArrayList<>();
                Map<String, String> urlMap = new HashMap();
                urlMap.put("url", url);
                urlList.add(urlMap);
                detailInfo = gson1.toJson(urlList);

//                List<Message> messages = request.getMessages();
//                for (Message message : messages) {
//                    if (MESSAGE_ROLE_USER.equals(message.getRole())) {
//                        String content = message.getContent();
//                        JSONArray urlJsonArray = JSONUtil.parseArray(content);
//                        List<Map<String, Object>> urlList = new ArrayList<>();
//                        for (Object obj : urlJsonArray) {
//                            Map urlMap = (Map) obj;
//                            urlMap.keySet().forEach(key -> {
//                                Map<String, Object> urlMap1 = new HashMap();
//                                urlMap1.put("url", key);
//                                urlList.add(urlMap1);
//                            });
//                        }
//                        detailInfo = gson1.toJson(urlList);
//                    }
//                }
            }
        } catch (Exception e) {
            log.error("====formatLoggingResponseError:" + responseJson);
            e.printStackTrace();
            throw e;
        }
        map.put("totalTokens", totalTokens);
        map.put("detailInfo", detailInfo);
        return map;
    }

    private void processResultWithFunction(int aiActionId, int domainId, String funcCode, String tagName, Map<String, Object> resultMap, String engineLanguage) {

        int processCount = 0;
        if (funcCode.equals(FUNC_CODE_SET_TAG_KEYWORD)) {
            List<String> needAddKeywordList = new ArrayList<>();
            for (String keyword : resultMap.keySet()) {
                if (!resultMap.get(keyword).toString().equalsIgnoreCase("no") && !resultMap.get(keyword).toString().equalsIgnoreCase("n/a")) {
                    needAddKeywordList.add(keyword);
                }
            }
            processCount = needAddKeywordList.size();
            if (CollectionUtils.isNotEmpty(needAddKeywordList)) {
                //add into queuebase: add tag and keywords
                log.info("********addQueueBaseTag:" + ResourceBatchInfoEntity.OPERATION_TYPE_BATCH_ASSOCIATE_MANAGED_KEYWORD_TO_TAG + ",domainId:" + domainId + ",keywordList size:" + needAddKeywordList.size() + ",tagName:" + tagName);
                addQueueBase(aiActionId, ResourceBatchInfoEntity.OPERATION_TYPE_BATCH_ASSOCIATE_MANAGED_KEYWORD_TO_TAG, domainId, needAddKeywordList, tagName, null, null);
            } else {
                log.info("***********processCountEmpty");
                openAIActionDAO.updateStatus(aiActionId, OpenAIActionEntity.STATUS_SUCCESS);
            }


        } else if (funcCode.equals(FUNC_CODE_TRANSLATE_KEYWORD) || funcCode.equals(FUNC_CODE_GOOGLE_TRANSLATE_KEYWORD)) {
            Map<String, String> needAddKeywordMap = new HashMap<>();
            for (String keyword : resultMap.keySet()) {
                String transLateKeyword = resultMap.get(keyword).toString();
                if (!transLateKeyword.contains("n/a") && !transLateKeyword.contains("N/A")) {
                    needAddKeywordMap.put(keyword, transLateKeyword);
                }
            }
            processCount = needAddKeywordMap.size();
            if (needAddKeywordMap.size() > 0) {
                //add into queuebase: translate keywords
                log.info("********addQueueBaseTrans:" + ResourceBatchInfoEntity.OPERATION_TYPE_BATCH_ADD_KEYWORD_TRANSLATION + ",domainId:" + domainId + ",needAddKeywordMap size:" + needAddKeywordMap.size());
                addQueueBase(aiActionId, ResourceBatchInfoEntity.OPERATION_TYPE_BATCH_ADD_KEYWORD_TRANSLATION, domainId, null, null, needAddKeywordMap, engineLanguage);
            } else {
                log.info("***********needAddKeywordMapEmpty");
                openAIActionDAO.updateStatus(aiActionId, OpenAIActionEntity.STATUS_SUCCESS);
            }

        } else if (funcCode.equals(FUNC_CODE_SUGGEST_TITLE) || funcCode.equals(FUNC_CODE_SUGGEST_META)) {
            List<TargeturlRecommendedEntity> targetUrlRecommendedList = new ArrayList<>();
            for (String url : resultMap.keySet()) {
                TargeturlRecommendedEntity targeturlRecommendedEntity = new TargeturlRecommendedEntity();
                String content = resultMap.get(url).toString();
                targeturlRecommendedEntity.setOwnDomainId(domainId);
                targeturlRecommendedEntity.setTargeturl(url);
                String MurmurHash3 = MurmurHashUtils.getMurmurHash3_64(url);
                targeturlRecommendedEntity.setUrlMurmur3Hash(MurmurHash3);
                if (funcCode.equals(FUNC_CODE_SUGGEST_TITLE)) {
                    targeturlRecommendedEntity.setContentType(TargeturlRecommendedEntity.CONTENT_TYPE_TITLE);
                } else if (funcCode.equals(FUNC_CODE_SUGGEST_META)) {
                    targeturlRecommendedEntity.setContentType(TargeturlRecommendedEntity.CONTENT_TYPE_META_DESC);
                }
                targeturlRecommendedEntity.setContent(content);
                targetUrlRecommendedList.add(targeturlRecommendedEntity);
            }
            log.info("===targetUrlRecommendedList size:" + new Gson().toJson(targetUrlRecommendedList));
            processCount = targetUrlRecommendedList.size();
            if (CollectionUtils.isNotEmpty(targetUrlRecommendedList)) {
                TargeturlRecommendedInsert targeturlRecommendedInsert = new TargeturlRecommendedInsert();
                targeturlRecommendedInsert.insertBatch(domainId, targetUrlRecommendedList);
            }
        }else if(funcCode.equals(FUNC_CODE_KEYWORD_EXTRACTION)){
            processCount = resultMap.size();
        }
        openAIActionDAO.updateProcessCount(aiActionId, processCount);
    }

    private void addQueueBase(int aiActionId, int operationType, int domainId, List<String> keywordList, String tagName,
                              Map<String, String> transMap, String engineLanguage) {

        int actionType = ResourceBatchInfoEntity.TYPE_ADD;

        //diff search engine
        OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getById(domainId);
        String resourceSearchEngine = "";
        List<DomainSearchEngineRelEntity> domainSearchEngineRelList = scKeywordRankManager.getAllSERels(ownDomainEntity);
        if (CollectionUtils.isNotEmpty(domainSearchEngineRelList)) {
            for (DomainSearchEngineRelEntity rel : domainSearchEngineRelList) {
                resourceSearchEngine += rel.getRankcheckSearchEngineId() + "-"
                        + rel.getRankcheckSearchLanguageid() + "-" + rel.getDevice() + ",";
            }
        }
        resourceSearchEngine = resourceSearchEngine.substring(0, resourceSearchEngine.length() - 1);
        System.out.println("======= resourceSearchEngine:" + resourceSearchEngine);

        ResourceBatchInfoEntity resourceBatchInfoEntity = new ResourceBatchInfoEntity();
        resourceBatchInfoEntity.setActionType(actionType);
        resourceBatchInfoEntity.setOwnDomainId(domainId);
        resourceBatchInfoEntity.setOperationType(operationType);
        resourceBatchInfoEntity.setEngineLanguageDevice(resourceSearchEngine);
        if(operationType == ResourceBatchInfoEntity.OPERATION_TYPE_BATCH_ADD_KEYWORD_TRANSLATION && StringUtils.isBlank(engineLanguage)){
            log.error("===translate engineLanguage empty aiActionId:" + aiActionId);
        }else if(operationType == ResourceBatchInfoEntity.OPERATION_TYPE_BATCH_ADD_KEYWORD_TRANSLATION && StringUtils.isNotBlank(engineLanguage)){
            log.info("===INFOtranslateELD:" + engineLanguage);
            resourceBatchInfoEntity.setEngineLanguageDevice(engineLanguage);
        }

        resourceBatchInfoEntity.setStatus(ResourceAddInfoEntity.STATUS_PROCESS_FINISHED_WITHOUT_ERROR);
        resourceBatchInfoEntity.setUserId(DEFAULT_USER);
        resourceBatchInfoEntity.setCreateDate(new Date());
        resourceBatchInfoEntity.setCustomFlag(ResourceBatchInfoEntity.CREATE_TYPE_AI);
        long id = resourceBatchInfoEntityDAO.insert(resourceBatchInfoEntity);

        List<ResourceBatchDetailEntity> resourceBatchDetailList = new ArrayList<>();
        if (operationType == ResourceBatchInfoEntity.OPERATION_TYPE_BATCH_ASSOCIATE_MANAGED_KEYWORD_TO_TAG) {
            for (String kw : keywordList) {
                ResourceBatchDetailEntity rbd = new ResourceBatchDetailEntity();
                rbd.setInfoId(id);
                rbd.setActionType(actionType);
                rbd.setOwnDomainId(domainId);
                rbd.setResourceMain(kw);
                rbd.setResourceAdditional(tagName);
//                rbd.setResourceSearchengines(resourceSearchEngine);
                rbd.setResourceMd5(Md5Util.Md5(kw + tagName + domainId + new Date().getTime()));
                rbd.setCreateDate(Integer.parseInt(DateFormatUtils.format(new Date(), "yyyyMMdd")));
                rbd.setResourceId(Long.parseLong(String.valueOf(aiActionId)));
                resourceBatchDetailList.add(rbd);
            }
        } else if (operationType == ResourceBatchInfoEntity.OPERATION_TYPE_BATCH_ADD_KEYWORD_TRANSLATION) {

            for (String kw : transMap.keySet()) {
                ResourceBatchDetailEntity rbd = new ResourceBatchDetailEntity();
                rbd.setInfoId(id);
                rbd.setActionType(actionType);
                rbd.setOwnDomainId(domainId);
                rbd.setResourceMain(kw);
                rbd.setResourceSubordinate(transMap.get(kw));
                rbd.setResourceMd5(Md5Util.Md5(kw + transMap.get(kw) + domainId + new Date().getTime()));
                if(StringUtils.isBlank(engineLanguage)){
                    log.error("===translate engineLanguage empty aiActionId:" + aiActionId);
                }else {
                    log.info("===translateELD:" + engineLanguage);
                    rbd.setResourceSearchengines(engineLanguage);
                }
                rbd.setResourceId(Long.parseLong(String.valueOf(aiActionId)));
                rbd.setCreateDate(Integer.parseInt(DateFormatUtils.format(new Date(), "yyyyMMdd")));
                resourceBatchDetailList.add(rbd);
            }

        }
        resourceBatchDetailEntityDAO.insertBatchIgnoreDup(resourceBatchDetailList);
        System.out.println("Qbs====>create new info, id: " + id + ", resourceBatchDetailList size: " + resourceBatchDetailList.size());
        resourceBatchInfoEntityDAO.updateStatusAfterProcess(id, ResourceAddInfoEntity.STATUS_NEWLY_CREATED, new Date());
    }

    private Integer getProcessUrlType(String code) {
        if (code.equals(FUNC_CODE_SUGGEST_TITLE)) {
            return 1;
        } else if (code.equals(FUNC_CODE_SUGGEST_META)) {
            return 2;
        }
        return null;
    }

    private void updateTranslateActionStatusByWorker() {
        //get from worker
        List<String> cacheList = new ArrayList<>();
        try {
            cacheList = WorkerUtils.getCacheList(WorkerUtils.CACHE_LIST_URL + TRANSLATE_WORKER_KEY);
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("=====get cache list error");
        }

        if (CollectionUtils.isEmpty(cacheList)) {
            log.info("==cacheListEMPTY");
            return;
        }

        for (String key : cacheList) {
            log.info("===get key:" + key);//event_qbase_kwTranslation_out_{actionId}
            int aiActionId = Integer.parseInt(key.replaceAll(TRANSLATE_WORKER_KEY, "").split("_")[0]);
            log.info("===UPSTATUS:" + aiActionId);
            openAIActionDAO.updateStatus(aiActionId, OpenAIActionEntity.STATUS_SUCCESS);

            String deleteUrl = WorkerUtils.DELETE_CACHE_URL_PREFIX + key;
            deleteCache(deleteUrl);
        }

    }

    private void downloadTitleMeta(int actionId) {
        File folder = new File(DOWNLOAD_FOLDER);
        if (!folder.exists()) {
            folder.mkdirs();
        }

        OpenAIActionEntity openAIActionEntity = openAIActionDAO.getActionById(actionId);
        if(openAIActionEntity == null){
            log.info("action can not download.");
            return;
        }
        int promptId = openAIActionEntity.getPromptId();
        //header
        List<String> lines = new ArrayList<String>();
        File outFile = new File(DOWNLOAD_FOLDER + "AIAction_" + actionId + ".csv");
        if (outFile.exists()) {
            outFile.delete();
        }
        StringBuffer stb = new StringBuffer();
        stb.append("url").append(SPLIT);
        if (promptId == 19 || promptId == 44) {
            stb.append("title");
        } else if (promptId == 20 || promptId == 62) {
            stb.append("meta");
        } else if (promptId == 17) {
            lines.add("Keyword" + SPLIT + "Match Found");
        }
        lines.add(stb.toString());

        List<OpenAiLoggingEntity> openAiLoggingList = openAiLoggingDAO.getActionLogByActionId(actionId);
        if (CollectionUtils.isNotEmpty(openAiLoggingList)) {

            for (OpenAiLoggingEntity openAiLoggingEntity : openAiLoggingList) {
                String responseJson = openAiLoggingEntity.getResponseJson();
                //format response
                try {
                    if(promptId == 19 || promptId == 20 || promptId == 17){
                        Map<String, Object> resultMap = formatResponse(responseJson);
                        log.info("===resultMap:" + new Gson().toJson(resultMap));
                        for (String key : resultMap.keySet()) {
                            stb = new StringBuffer();
                            stb.append(key).append(SPLIT);
                            stb.append(resultMap.get(key));
                            lines.add(stb.toString());
                        }
                    } else if (promptId == 44 || promptId == 62) {
                        String aiResult = formatResponseForNewTitleResult(responseJson);
                        List detailInfoList = new Gson().fromJson(openAiLoggingEntity.getDetailInfo(), List.class);
                        LinkedTreeMap<String, String> linkedTreeMap =  (LinkedTreeMap<String, String>) detailInfoList.get(0);
                        String key = linkedTreeMap.get("url");
                        log.info(key);
                        stb = new StringBuffer();
                        stb.append(key).append(SPLIT);
                        stb.append(aiResult);
                        lines.add(stb.toString());
                    }
                } catch (Exception ex) {
                    log.error("forError,skip");
                    ex.printStackTrace();
                    continue;
                }
            }
            try {
                FileUtils.writeLines(outFile, lines, true);
            }catch (Exception e){
                e.printStackTrace();
            }
        }

    }

    private void deleteCache(String url) {
        try {
            System.out.println("==delete cache:" + url);
            WorkerUtils.doDelete(url, WorkerUtils.KEY_SECURITY_HEADER, WorkerUtils.VALUE_SECURITY_HEADER);
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("====doDelete error:" + url);
        }
    }

    private String formatEmoji(String content){
        if (CommonDataService.containsEmoji(content)) {
            String newStr = CommonDataService.filterEmoji(content);
            System.out.println(" ##containsEmoji:\"" + content + "\" len:" + content.length() + " new:\"" + newStr + "\" len:" + newStr.length() +
                    " changed:" + (content.length() > newStr.length()));
            content = newStr;
        }
        return content;
    }

    private static void testRequestApi() {
        try {
            Map<String, Object> paramMap = new HashMap<String, Object>();
            paramMap.put("accessToken", ClarityDBAPIUtils.ACCESS_TOKEN_VALUE);
            paramMap.put("countInTop", 1);
            paramMap.put("domainQueryFrequency", "DAY");
            paramMap.put("engine", 1);
            paramMap.put("language", 1);
            paramMap.put("mobile", false);
            paramMap.put("ownDomainName", "www.hm.com");
            paramMap.put("domainId", 4661);
            paramMap.put("ownDomainId", 4661);
            paramMap.put("urlList", null);
//            paramMap.put("queryKeywords", new String[]{"long sleeved t shirts"});
            paramMap.put("ctrList", new String[]{"0.193", "2", "0.114", "3", "0.077", "4", "0.052", "5", "0.041", "6", "0.033", "7", "0.026"
                    , "8", "0.021", "9", "0.02", "10", "0.022", "0"});
            paramMap.put("urlList", new String[]{"https://www2.hm.com/en_sg/customer-service/shopping-at-hm/online-shopping.html"});
//            log.info("====apiREQ1:" + new Gson().toJson(paramMap));
            String apiResponse = ClarityDBAPIUtils.simplePost(API_METHOD, new Gson().toJson(paramMap));
            log.info("====apiRES1:" + apiResponse);
            ApiResponseVo apiResponseVo = new Gson().fromJson(apiResponse, ApiResponseVo.class);
//            log.info("====apiResponseVo:" + new Gson().toJson(apiResponseVo));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private List<Map<String, String>> postRequestSiteClarityApi(int domainId, List<String> responseFields, int crawlId,AiActionParams aiActionParams) throws Exception{
//        private List<Map<String, String>> postRequestSiteClarityApi() throws Exception{

//        OpenAIActionEntity openAIActionEntity =  openAIActionDAO.getActionById(12);
//        String variableJson = openAIActionEntity.getVariableJson();
//        VariableJsonEntity variableJsonEntity = gson.fromJson(variableJson, VariableJsonEntity.class);
//        log.info("===variableJsonEntity:" + gson.toJson(variableJsonEntity));
//        AiActionParams aiActionParams = variableJsonEntity.getAiActionParams();
//        List<SiteClarityFiltersVO> filters = aiActionParams.getFilters();

        Gson gson = new GsonBuilder().disableHtmlEscaping().create();
        List<Map<String, String>> contentList = new ArrayList<>();

        try {
            int pageIndex = 0;
            int totalDocs = 0;
            int processDocs = 0;
            boolean customDiv = false;
            List<String> customDivFieldList = new ArrayList<>();

            List<String> requestFields = new ArrayList<>();
//            List<String> responseFields = new ArrayList<>();
//            responseFields.add("title");
//            responseFields.add("//div[contains(@class, 'section-text')]");
            for(String fields: responseFields){
                if(!fields.equalsIgnoreCase("title") && !fields.equalsIgnoreCase("description")
                        && !fields.equalsIgnoreCase("h1")){
                    log.info("===customDiv:" + fields);
                    customDiv = true;
                    customDivFieldList.add(fields);
                }else {
                    requestFields.add(fields);
                }
            }

//                responseFields.add("h1");
//                responseFields.add("title");
//                responseFields.add("description");
            requestFields.add("url");
            requestFields.add("response_code");
            if(customDiv){
                requestFields.add("custom_data.content");
                requestFields.add("custom_data.selector");
            }
//            int domainId = 11813;
//            int crawlId = 9954696;

            Map<String, String> sortMap = new HashMap<>();
            sortMap.put("field", "url");
            sortMap.put("order", "asc");

            Map<String, Object> paramMap = new HashMap<String, Object>();
            paramMap.put("accessToken", ClarityDBAPIUtils.ACCESS_TOKEN_VALUE);
            paramMap.put("domain_id", domainId);
            paramMap.put("allData", true);
            paramMap.put("fromAI", true);
            paramMap.put("crawl_request_log_id_i", crawlId);
//                paramMap.put("crawl_request_log_id_i", 9954895);
            paramMap.put("enableChildQuery", false);
            paramMap.put("postProcessor", "detailsTab");
            paramMap.put("type", "CRAWL");
            paramMap.put("responseFields", requestFields);//"responseFields": [ "h1", "title", "url", "response_code", "description", "custom_data.content", "custom_data.selector"]
            paramMap.put("sort", sortMap);//{"field": "id","order": "desc"}
            paramMap.put("pageType", "Details/DataTable");

            paramMap.put("size", SITE_AUDIT_PAGE_SIZE);
            String filterStr = aiActionParams.getFiltersStr();
            if( StringUtils.isNotBlank(filterStr)){
                JSONArray filterjsonArray = JSONArray.parseArray(filterStr);
                List<Map> filterList = filterjsonArray.toJavaList(Map.class);
                paramMap.put("filters", filterList);
            }

            while (true){

                if(processDocs >= SITE_AUDIT_MAX_PROCESS_COUNT || (processDocs > 0 && processDocs >= totalDocs)){
                    log.info("====OVER processDocs:" + processDocs + ",totalDocs:" + totalDocs);
                    break;
                }

                int from = pageIndex * SITE_AUDIT_PAGE_SIZE;
                log.info("===from:" + from);

                paramMap.put("from", from);
                log.info("===paramMap:" + gson.toJson(paramMap));

                String apiResponse = ClarityDBAPIUtils.simplePost(API_SITE_CLARITY_METHOD, gson.toJson(paramMap));
                log.info("====testRequestSiteClarityApi:" + apiResponse);

                if(apiResponse.contains("<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?><errors><error>")){
                    log.info("====request error!" + apiResponse);
                    return contentList;
                }

                Map responseMap = gson.fromJson(apiResponse, Map.class);
                if (pageIndex == 0) {
                    Double totalDocsDouble =Double.parseDouble(responseMap.get("totalDocs").toString());
                    totalDocs = totalDocsDouble.intValue();
                    log.info("==============TotalDocs:" + totalDocs);
                }
                JSONArray jsonArray = JSONArray.parseArray(gson.toJson(responseMap.get("docs")));

                List<Map> docsMapList = jsonArray.toJavaList(Map.class);
                for(int i=0;i<docsMapList.size();i++){

                    String responseCode = docsMapList.get(i).get("response_code").toString();
                    String url = docsMapList.get(i).get("url").toString();
                    log.info("==============url:" + url);
                    if(!responseCode.equals("200")){
                        log.info("===SKIPNOT200URLS:" + url);
                        continue;
                    }
                    String title = docsMapList.get(i).get("title") == null ? null : docsMapList.get(i).get("title").toString();
                    String meta = docsMapList.get(i).get("description") == null ? null : docsMapList.get(i).get("description").toString();
                    String h1Json = docsMapList.get(i).get("h1") == null ? null : gson.toJson(docsMapList.get(i).get("h1"));
                    List<String> h1ContentList = new ArrayList<>();
                    if(StringUtils.isNotBlank(h1Json)){
                        JSONArray h1JsonArray = JSONArray.parseArray(h1Json);
                        h1ContentList = h1JsonArray.toJavaList(String.class);
                    }

                    boolean hasTitle = false;
                    boolean hasMeta = false;
                    boolean hash1 = false;
                    int tokenCount = 0;
                    Map<String, String> contentMap = new HashMap<>();
                    String content = "";
                    if(StringUtils.isNotBlank(title)){
                        content += "\"title\":\"" + title + "\"";
                        hasTitle = true;
                        int titleTokenCnt = encoding.countTokens(title);
                        log.info("===titleTokenCount:" + titleTokenCnt);
                        tokenCount += encoding.countTokens(title);
                    }
                    if(StringUtils.isNotBlank(meta)){
                        if(StringUtils.isNotBlank(content) && content.length() > 5){
                            content += ", ";
                        }
                        String metaContent = "\"description\":\"" + meta + "\"";
                        content += metaContent;
                        hasMeta = true;
                        int metaTokenCnt = encoding.countTokens(meta);
                        log.info("===metaTokenCount:" + metaTokenCnt);
                        tokenCount += metaTokenCnt;
                    }
                    if(CollectionUtils.isNotEmpty(h1ContentList)){
                        if(StringUtils.isNotBlank(content) && content.length() > 5){
                            content += ", ";
                        }
                        content += "\"h1\": [";
                        content += "\"" + StringUtils.join(h1ContentList, "\",\"") + "\"]";
                        hash1 = true;
                        int h1TokenCnt = encoding.countTokens(StringUtils.join(h1ContentList, "\",\""));
                        log.info("===h1tokenCount:" + h1TokenCnt);
                        tokenCount += h1TokenCnt;
                    }


                    if(customDiv){
                        log.info("===customDiv");
                        String customSelectorJson = gson.toJson(docsMapList.get(i).get("custom_data.selector"));
                        JSONArray customSelectorJsonArray = JSONArray.parseArray(customSelectorJson);
                        List<String> customSelectorList = customSelectorJsonArray.toJavaList(String.class);

                        String customContentJson = gson.toJson(docsMapList.get(i).get("custom_data.content"));
                        JSONArray customContentJsonArray = JSONArray.parseArray(customContentJson);
                        List<String> customContentList = customContentJsonArray.toJavaList(String.class);

                        for(int k=0;k<customSelectorList.size();k++){//get custom div content
                            String customSelector = customSelectorList.get(k);
                            if(customDivFieldList.contains(customSelector)){
                                String customContent = customContentList.get(k).replaceAll("\\[","").
                                        replaceAll("\\]","").replace("\\/","/")
                                        .replace("\\n", "").replace("\"","");
//                                log.info("==customSelector:" + customSelector + ",customContent:" + customContent);
                                Document doc = Jsoup.parse(customContent);
                                String formatContent = doc.text();
//                                log.info("===customContentFormat:" + formatContent);
                                if(StringUtils.isNotBlank(content) && content.length() > 5){
                                    content += ", ";
                                }
                                content += "\"" + customSelector + "\":\"" + formatContent + "\"";
                            }
                        }
                    }
                    if(StringUtils.isBlank(content)){
                        log.info("====NOContent,skip:" + url);
                        continue;
                    }

//                    log.info("====content:  " + content);
                    int totalTokenCnt = encoding.countTokens(content);
                    log.info("===totalTokenCnt:" + totalTokenCnt);
                    if(totalTokenCnt >= KEYWORD_EXTRACTION_TOKEN_LENGTH){
                        log.info("*********tokenCountOverLimit,split.");
                        contentList.addAll(splitOverLimitContent(meta, url));
                    }else {
                        contentMap.put("url", url);
                        contentMap.put("content", content);
                        log.info("====requestBody:" + gson.toJson(content));
                        contentList.add(contentMap);
                    }

                }

                if(CollectionUtils.isEmpty(docsMapList)){
                    log.info("=====noDoc,exit.");
                    break;
                }
                processDocs += docsMapList.size();
                log.info("====docsListSize:" + docsMapList.size() + "***********processDocsSIZE:" + processDocs);

                pageIndex ++;
            }
            return contentList;
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    private static List<Map<String, String>> splitOverLimitContent(String content, String url){
        List<Map<String, String>> splitList = new ArrayList<>();
        EncodingRegistry registry = Encodings.newDefaultEncodingRegistry();
        Encoding encoding = registry.getEncodingForModel(ModelType.GPT_3_5_TURBO);
        EncodingResult encodingResult = encoding.encode(content, KEYWORD_EXTRACTION_TOKEN_LENGTH);
        String splitContent = content;
        while (true){
            if(encodingResult.isTruncated()){
                Map<String, String> contentMap = new HashMap<>();
                log.info("==NeedTruncated:" + url);
                List<Integer> tokenList = encodingResult.getTokens();
                String decode = encoding.decode(tokenList);
                log.info("===decode:" + decode);
                contentMap.put("url", url);
                contentMap.put("content", decode);
                splitList.add(contentMap);

                contentMap = new HashMap<>();
                String reStr = splitContent.replace(decode, "");
                log.info("===reStr:" + encoding.countTokens(reStr) + "," +reStr);
                if(encoding.countTokens(reStr) <= KEYWORD_EXTRACTION_TOKEN_LENGTH){
                    contentMap.put("url", url);
                    contentMap.put("content", reStr);
                    splitList.add(contentMap);
                    break;
                }

                encodingResult = encoding.encode(reStr, KEYWORD_EXTRACTION_TOKEN_LENGTH);
                splitContent = reStr;
            }else {
                break;
            }
        }

        log.info("===============splitList:" + new Gson().toJson(splitList));
        return splitList;
    }

    private List<GroupTagEntity> checkTagEngineLanguageKw(int domainId, int tagId, int engineId, int languageId, String device) {
        List<GroupTagEntity> existELKeywordList = cdbTrackedKeywordEntityDAO.getTagKeywordsByEngineLanguage(domainId, tagId, engineId, languageId, device);
        return existELKeywordList;
    }

    private static LinkedList<TranslatedText> formatGoogleTranslateResult(String response) throws Exception{
        LinkedList<TranslatedText> textLinkedList;
        try {
            GoogleTranslateResponseVo googleTranslateResponseVo = new Gson().fromJson(response, GoogleTranslateResponseVo.class);
            textLinkedList = googleTranslateResponseVo.getData().getTranslations();
            for(TranslatedText translatedText: textLinkedList){
                log.info("===translatedText:" + translatedText.getTranslatedText());
            }
        }catch (Exception e){
            throw e;
        }
        return textLinkedList;
    }

    private static void getTranslateDetailInfo(){
        String detailInfo = "[{\"keyword\": \"girl shoes\"}, {\"keyword\": \"girl shoes\"}, {\"keyword\": \"girl shoes\"}, {\"keyword\": \"girl shoes\"}, {\"keyword\": \"girl shoes\"}, {\"keyword\": \"girl shoes\"}, {\"keyword\": \"girl shoes\"}, {\"keyword\": \"girl shoes\"}]";
        LinkedList list = new Gson().fromJson(detailInfo, LinkedList.class);
        new Gson().fromJson(new Gson().toJson(list.get(0)), Map.class).get("keyword");
    }

    protected void waitingForExit() {
        waitingThread(newThreadPool);
        newThreadPool.shutdown();
    }

    protected void waitingThread(ExecutorService executorThreadPool) {
        while (((ThreadPoolExecutor) executorThreadPool).getActiveCount() > 0) {
            try {
                log.info("Still have alive thread, waiting...");
                Thread.sleep(60 * 1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }

    public void waitForThreadPool() throws InterruptedException {
        while (!newThreadPool.isShutdown()) {
            Thread.sleep(10 * 1000);
            int aliveCount = ((ThreadPoolExecutor) newThreadPool).getActiveCount();
            System.out.println("Thread aliveCount : " + aliveCount);
            if (aliveCount == 0) {
                newThreadPool.shutdown();
            }
        }
    }

    private static List<Map<String, String>> parseExcel(String file) throws Exception {

        File outFIle = new File("D:\\workspace\\extract\\openai\\titleRows.csv");
        List<String> lineList = new ArrayList<>();
        XSSFWorkbook wb = new XSSFWorkbook(file);
        XSSFSheet sheet = wb.getSheetAt(0);
        int start = sheet.getFirstRowNum();
        int end = sheet.getLastRowNum();
        end = 10;
        Map<String, List<KeywordRankVO>> dateMap = new HashMap<>();

        System.out.println("===file:" + file + ", lines:" + sheet.getPhysicalNumberOfRows());

        int totalColumns = 0;
        int lineCnt = 0;
        List<Map<String, String>> contentList = new ArrayList<>();
        for (int n = 1; n <= end; n++) {
            lineCnt++;
            XSSFRow row = sheet.getRow(n);
            if (row == null || row.getPhysicalNumberOfCells() == 0) {
                continue;
            }

            try {
                Map<String, String> contentMap = new HashMap<>();
                String[] cols = getStringArray(row);
                String title = cols[0];
                String url = cols[1];
                String htmlContent = cols[2];

//                if(skipRows.contains(lineCnt)){
//                    lineList.add(title + SPLIT + url + SPLIT + htmlContent);
//                }

                if(title.equalsIgnoreCase("Title") && url.equalsIgnoreCase("Url") && htmlContent.equalsIgnoreCase("Content")){
                    log.info("===skip header");
                    continue;
                }
                Document doc = Jsoup.parse(htmlContent.replace("\"", "").replace("\\n", ""));
                String content = doc.text();

                String requestContent = "";
                if (StringUtils.isNotBlank(title)) {
                    requestContent += "\"title\":\"" + title + "\"";
                } else {
                    log.info("===noTitle:" + n);
                }
                if (StringUtils.isNotBlank(content)) {
                    requestContent += ", \"content\":\"" + content + "\"";
                } else {
                    log.info("===noContent:" + n);
                }

                EncodingRegistry registry = Encodings.newDefaultEncodingRegistry();
                Encoding encoding = registry.getEncodingForModel(ModelType.GPT_3_5_TURBO);
                int tokenCount = encoding.countTokens(requestContent);


//                EncodingResult encodingResult = encoding.encode(requestContent, 2999);
//                if(encodingResult.isTruncated()){
//                    List<Integer> tokenList = encodingResult.getTokens();
//                    String decode = encoding.decode(tokenList);
//                    log.info("===decode:" + decode);
//
//                    String reStr = requestContent.replace(decode, "");
//                    log.info("===2:" + reStr);
//                }


//                log.info(tokenCount);
                if (tokenCount > 3000) {
                    log.info("=============== " + splitOverLimitContent(requestContent, url));;
                    log.info("====line:" + n + ",requestContent:" + requestContent);


//                    log.info("====line:" + n + ",requestContent:" + requestContent);
//                    log.info("====line:" + n + ",tokenCount:" + tokenCount);
                    requestContent = "\"title\":\"" + title + "\"";
//                    log.info("====requestContent:" + requestContent);
//                    break;
                }
//                contentMap.put("url", url);
//                contentMap.put("content", requestContent);
//                contentList.add(contentMap);


            } catch (Exception e) {
                e.printStackTrace();
            }
        }

//        FileUtils.writeLines(outFIle, lineList, true);

        return contentList;
    }

    private static List<Map<String, String>> parseCsv(String file) throws Exception {
        int lineCnt = 0;
        List<Map<String, String>> contentList = new ArrayList<>();
        List<String> lineList = FileUtils.readLines(new File(file), "utf-8");
        for (String row: lineList) {
            lineCnt++;

            if (StringUtils.isBlank(row)) {
                continue;
            }

            try {
                Map<String, String> contentMap = new HashMap<>();
                String[] cols = row.split(SPLIT);
                String title = cols[0];
                String url = cols[1];
                String htmlContent = cols[2];
                if(title.equalsIgnoreCase("Title") && url.equalsIgnoreCase("Url") && htmlContent.equalsIgnoreCase("Content")){
                    log.info("===skip header");
                    continue;
                }
//                System.out.println("11111111111" + cols[0]);
//                System.out.println("22222222222" + cols[1]);
//                System.out.println("33333333333" + cols[2]);
                Document doc = Jsoup.parse(htmlContent.replace("\"", "").replace("\\n", ""));
                String content = doc.text();

                String requestContent = "";
                if (StringUtils.isNotBlank(title)) {
                    requestContent += "\"title\":\"" + title + "\"";
                } else {
                    log.info("===noTitle:" + lineCnt);
                }
                if (StringUtils.isNotBlank(content)) {
                    requestContent += ", \"content\":\"" + content + "\"";
                } else {
                    log.info("===noContent:" + lineCnt);
                }

                EncodingRegistry registry = Encodings.newDefaultEncodingRegistry();
                Encoding encoding = registry.getEncodingForModel(ModelType.GPT_3_5_TURBO);
                int tokenCount = encoding.countTokens(requestContent);
//                log.info(tokenCount);
                if (tokenCount > 3000) {
//                    log.info("====line:" + n + ",requestContent:" + requestContent);
                    log.info("====line:" + lineCnt + ",tokenCount:" + tokenCount);
                    requestContent = "\"title\":\"" + title + "\"";
                    EncodingResult encodingResult = encoding.encode(requestContent, 3000);
                    log.info("==" + encodingResult.toString());
//                    log.info("====requestContent:" + requestContent);
//                    break;
                }
                contentMap.put("url", url);
                contentMap.put("content", requestContent);
                contentList.add(contentMap);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return contentList;
    }
    private static String[] getStringArray(XSSFRow row) {
        String[] lines = new String[row.getLastCellNum()];
        for (int i = 0; i < row.getLastCellNum(); i++) {
            lines[i] = row.getCell(i) == null? "":row.getCell(i) .toString();
        }
        return lines;
    }



}














class ApiResponseVo {

    private List<ApiResponseDataVo> data;

    public List<ApiResponseDataVo> getData() {
        return data;
    }

    public void setData(List<ApiResponseDataVo> data) {
        this.data = data;
    }
}

class ApiResponseDataVo {
    private String keyword_name;
    private String url;
    private String label;
    private String meta;

    public String getKeyword_name() {
        return keyword_name;
    }

    public void setKeyword_name(String keyword_name) {
        this.keyword_name = keyword_name;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getMeta() {
        return meta;
    }

    public void setMeta(String meta) {
        this.meta = meta;
    }
}

class PaginationQueryVO {
    private Integer page;
    private Integer pageSize;

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
}

class Docs{

    private String response_code;
    private List<String> h1;
    private String description;
    private String title;
    private String url;

    public List<String> getH1() {
        return h1;
    }

    public void setH1(List<String> h1) {
        this.h1 = h1;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getResponse_code() {
        return response_code;
    }

    public void setResponse_code(String response_code) {
        this.response_code = response_code;
    }
}

class GoogleTranslateRequestVo{
    private List<String> q;
    private String source;
    private String target;

    public List<String> getQ() {
        return q;
    }

    public void setQ(List<String> q) {
        this.q = q;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getTarget() {
        return target;
    }

    public void setTarget(String target) {
        this.target = target;
    }
}

class GoogleTranslateResponseVo{
    private GoogleResponseData data;

    public GoogleResponseData getData() {
        return data;
    }

    public void setData(GoogleResponseData data) {
        this.data = data;
    }
}

class GoogleResponseData{
    private LinkedList<TranslatedText> translations;

    public LinkedList<TranslatedText> getTranslations() {
        return translations;
    }

    public void setTranslations(LinkedList<TranslatedText> translations) {
        this.translations = translations;
    }
}

class TranslatedText{
    private String translatedText;

    public String getTranslatedText() {
        return translatedText;
    }

    public void setTranslatedText(String translatedText) {
        this.translatedText = translatedText;
    }
}