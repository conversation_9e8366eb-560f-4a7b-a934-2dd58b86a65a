package seoclarity.backend.summary.onetime;

import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.s3.AmazonS3Client;
import com.amazonaws.services.s3.model.GetObjectRequest;
import com.amazonaws.services.s3.model.S3Object;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import seoclarity.backend.utils.LogEntry;

import java.io.*;
import java.util.*;
import java.util.zip.GZIPInputStream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

@CommonsLog
public class DownloadVedFile {

    private String bucketName = "zr-seoc";
    private String savePath = "D:\\Extract\\ved\\download\\";



    public DownloadVedFile(){

    }

    public static void main(String[] args) {

        File file = new File("D:\\Extract\\ved\\vedtype-withunique-kwd-20190910-shaka.txt");

        DownloadVedFile downloadVedFile = new DownloadVedFile();
        downloadVedFile.process(file);

    }

    private void process(File file) {

        Set<String> pathSet = new HashSet<>();
        try (
                InputStream ist = new FileInputStream(file);
                InputStreamReader isrt = new InputStreamReader(ist, "UTF-8");
                BufferedReader brr = new BufferedReader(isrt);
        ) {
            String line;
            int count = 0;

            while ((line = brr.readLine()) != null) {

                if(StringUtils.isBlank(line)){
                    System.out.println("------empty line skip: " + line);
                    continue;
                }
                String[] row = line.split("\t");
                String s3Path = row[3];
                if(StringUtils.isBlank(s3Path)){
                    System.out.println("------empty s3Path skip: " + line);
                    continue;
                }
                s3Path = s3Path.replaceAll("zr-seoc/","");
                pathSet.add(s3Path);
                count ++;

                if(count == 162){
                    System.out.println("------count == 162 exit : " + s3Path);
                    break;
                }

            }
        } catch (IOException e) {
            e.printStackTrace();
        }

        System.out.println("------pathSet size : " + pathSet.size());

        String accessKeyId = "********************";
        String secretAccessKey = "W8+yFElbNlnXyxaCFND5d3fVBzXnbyLJCbCPkzJy";
        AWSCredentials credentials = new BasicAWSCredentials(accessKeyId, secretAccessKey);
        AmazonS3Client s3client = new AmazonS3Client(credentials);
        //keyword_id, engine,  language,  device, s3_path
        for (String s3Path : pathSet) {
            log.info("Process key :" + s3Path);
            GetObjectRequest rangeObjectRequest = null;
            try {
                rangeObjectRequest = new GetObjectRequest(bucketName, s3Path);
                S3Object objectPortion = s3client.getObject(rangeObjectRequest);
                if (objectPortion != null && objectPortion.getObjectContent() != null) {
                    InputStream objectData = objectPortion.getObjectContent();
                    String newFileName = s3Path.replaceAll("/", "_");
                    log.info("newFileName :" + newFileName);
//                    File downloadZipFile = new File(savePath + newFileName);
                    File tmpFile = File.createTempFile("ranking", "html");
                    String html = null;
                    //Leo - make sure webservice can read JAVA & Nodejs HTML
                    String contentType = objectPortion.getObjectMetadata().getContentType();
                    if (StringUtils.containsIgnoreCase(contentType, "octet-stream")) {
                        FileOutputStream fos = new FileOutputStream(tmpFile);
                        byte[] read_buf = new byte[1024];
                        int read_len = 0;
                        while ((read_len = objectData.read(read_buf)) > 0) {
                            fos.write(read_buf, 0, read_len);
                        }
                        objectData.close();
                        fos.close();
                        html = unGZip(tmpFile);
                        FileUtils.writeStringToFile(new File(savePath + (newFileName.replaceAll(".gz",""))), html);
                    } else {
                        unZipIt(objectData, tmpFile);
//                        html = FileUtils.readFileToString(tmpFile);
                        FileUtils.writeStringToFile(new File(savePath + (newFileName.replaceAll(".gz",""))), html);
                    }
                    tmpFile.delete();
//                    log.info("Html " + key + " :\n " + html.length());

                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

    }


    private static String unGZip(File targzFile) throws IOException {
        FileInputStream fileIn = null;
        BufferedInputStream bufIn = null;
        GZIPInputStream gzipIn = null;
        try {
            fileIn = new FileInputStream(targzFile);
            bufIn = new BufferedInputStream(fileIn);
            gzipIn = new GZIPInputStream(bufIn);
            return new String(IOUtils.toByteArray(gzipIn));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private static void unZipIt(InputStream zipFile, File outputFile) {

        byte[] buffer = new byte[1024];

        try {
            // get the zip file content
            ZipInputStream zis = new ZipInputStream(zipFile);
            // get the zipped file list entry
            ZipEntry ze = zis.getNextEntry();

            while (ze != null) {
                FileOutputStream fos = new FileOutputStream(outputFile);

                int len;
                while ((len = zis.read(buffer)) > 0) {
                    fos.write(buffer, 0, len);
                }

                fos.close();
                ze = zis.getNextEntry();
            }

            zis.closeEntry();
            zis.close();

        } catch (IOException ex) {
            ex.printStackTrace();
        }
    }

}
