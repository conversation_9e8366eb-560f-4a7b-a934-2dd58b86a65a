package seoclarity.backend.summary.onetime;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.actonia.OwnDomainSettingEntityDAO;
import seoclarity.backend.dao.clickhouse.gsc.GscBaseDao;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.actonia.OwnDomainSettingEntity;
import seoclarity.backend.entity.clickhouse.gsc.GscEntity;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;

//mvn exec:java -Dexec.mainClass="seoclarity.backend.summary.onetime.RefreshLastestDate" -Dexec.args=""
public class RefreshLastestDate {
	
    private GscBaseDao gscBaseDao;
    private OwnDomainSettingEntityDAO ownDomainSettingEntityDAO;
    private OwnDomainEntityDAO ownDomainEntityDAO;
    
	public RefreshLastestDate() {
		gscBaseDao = SpringBeanFactory.getBean("gscBaseDao");
		ownDomainSettingEntityDAO = SpringBeanFactory.getBean("ownDomainSettingEntityDAO");
		ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
	}

	public static void main(String[] args) {
		RefreshLastestDate refreshLastFinalDate = new RefreshLastestDate();
		
		refreshLastFinalDate.init();
		
		refreshLastFinalDate.processForLastFreshDate();
		refreshLastFinalDate.processForLastFinalDate();
		refreshLastFinalDate.processForSharedProfile();
	}
	
	
	private static Map<Integer, Date> latestFinalMap = new HashMap<>();
	private static Map<Integer, Date> latestFreshMap = new HashMap<>();
	private static List<Integer> gscDomainIdList = new ArrayList<>();
	
	private void init() {
		List<OwnDomainEntity> domainAndProfileList = ownDomainEntityDAO.getGSCProcessingDomain(null);
        Map<Integer, OwnDomainEntity> domainMap = new HashMap<>();
        domainMap = domainAndProfileList.stream().collect(Collectors.toMap(OwnDomainEntity::getId, Function.identity()));
		
        Map<Integer, OwnDomainSettingEntity> cacheMap = new HashMap<>();
		List<OwnDomainSettingEntity> domainList = ownDomainSettingEntityDAO.getAllGscLatestDate();
		cacheMap = domainList.stream().collect(Collectors.toMap(OwnDomainSettingEntity::getOwnDomainId, Function.identity()));
		
		for(Integer ownDomainId : domainMap.keySet()) {
			
			if (cacheMap.keySet().contains(ownDomainId)) {
				latestFinalMap.put(ownDomainId, cacheMap.get(ownDomainId).getGscLatestFinalDate());
				latestFreshMap.put(ownDomainId, cacheMap.get(ownDomainId).getGscLatestFreshDate());
			} else {
				latestFinalMap.put(ownDomainId, null);
				latestFreshMap.put(ownDomainId, null);
			}
			
			gscDomainIdList.add(ownDomainId);
		}
		
		System.out.println("Cache domain GSC latest data size:" + domainList.size());
	}
	
	private void processForLastFreshDate() {
		
		try {
			//---------------------------------------update last fresh date
			List<GscEntity> gscdateList = gscBaseDao.getLatestDate();
			Integer ownDomainId = 0;
			String lastFreshDateStr = "";
			Date newLastFreshDate;
			Date lastFreshDateInDb;
			
			Map<Integer, String> oidDateMap = new HashMap<Integer, String>();
			for(GscEntity gscEntity : gscdateList) {
				
				ownDomainId = gscEntity.getOwnDomainId();
				if (!gscDomainIdList.contains(ownDomainId)) {
					continue;
				}
				
				lastFreshDateStr = gscEntity.getLogDateStr();
				newLastFreshDate = FormatUtils.toDate(lastFreshDateStr, "yyyy-MM-dd");
				lastFreshDateInDb = latestFreshMap.get(ownDomainId);
				
//				System.out.println("= updating OID:" + ownDomainId + ", FRdb:" + lastFreshDateStr 
//						+ " ==> FRnew:" + FormatUtils.formatDate(lastFreshDateInDb, "yyyy-MM-dd"));
				
				if (lastFreshDateInDb == null || newLastFreshDate.getTime() > lastFreshDateInDb.getTime()) {
					System.out.println("==== updating OID:" + ownDomainId + ", FRdb:" + lastFreshDateStr 
							+ " ==> FRnew:" + FormatUtils.formatDate(lastFreshDateInDb, "yyyy-MM-dd"));
					oidDateMap.put(ownDomainId, lastFreshDateStr);
				}
			}

			if (oidDateMap.size() > 0) {
				ownDomainSettingEntityDAO.updateGscLatestDateByOid(oidDateMap);
			}
			
			System.out.println("update last fresh date ! size:" + oidDateMap.size());
		} catch (Exception e) {
			e.printStackTrace();
		}
		
	}
	
	private void processForLastFinalDate() {
		
		try {
			//---------------------------------------update last final date
			List<GscEntity> gscdateList = gscBaseDao.getLatestFinalDate();
			Integer ownDomainId = 0;
			String lastFinalDateStr = "";
			Date newLastFinalDate;
			Date lastFinalDateInDb;
			
			Map<Integer, String> oidDateMap = new HashMap<Integer, String>();
			for(GscEntity gscEntity : gscdateList) {
				ownDomainId = gscEntity.getOwnDomainId();
				if (!gscDomainIdList.contains(ownDomainId)) {
					continue;
				}
				
				lastFinalDateStr = gscEntity.getLogDateStr();
				newLastFinalDate = FormatUtils.toDate(lastFinalDateStr, "yyyy-MM-dd");
				lastFinalDateInDb = latestFinalMap.get(ownDomainId);
				
//				System.out.println("= updating OID:" + ownDomainId + ", FNdb:" + lastFinalDateStr 
//						+ " ==> FNnew:" + FormatUtils.formatDate(lastFinalDateInDb, "yyyy-MM-dd"));
				
				if (lastFinalDateInDb == null || newLastFinalDate.getTime() > lastFinalDateInDb.getTime()) {
					System.out.println("==== updating OID:" + ownDomainId + ", FNdb:" + lastFinalDateStr 
							+ " ==> FNnew:" + FormatUtils.formatDate(lastFinalDateInDb, "yyyy-MM-dd"));
					oidDateMap.put(ownDomainId, lastFinalDateStr);
				}
			}

			if (oidDateMap.size() > 0) {
				ownDomainSettingEntityDAO.updateGscLatestFinalDateByOid(oidDateMap);
			}
			
			System.out.println("update last final date ! size:" + oidDateMap.size());
		} catch (Exception e) {
			e.printStackTrace();
		}
		
		
	}
	
	private void processForSharedProfile() {
		
		try {
			List<OwnDomainSettingEntity> gscdateList = ownDomainSettingEntityDAO.getSharedProfileLatestDate();
			Integer ownDomainId = 0;
			String lastFinalDateStr = "";
			String lastFreshDateStr = "";
			
			Date newLastFinalDate;
			
			Date lastFinalDateInDb;
			Date newLastFreshDate;
			Date lastFreshDateInDb;
			
			
			Map<Integer, String[]> oidDateMap = new HashMap<Integer, String[]>();
			for(OwnDomainSettingEntity ownDomainSettingEntity : gscdateList) {
				ownDomainId = ownDomainSettingEntity.getOwnDomainId();
//				if (!gscDomainIdList.contains(ownDomainId)) {
//					continue;
//				}
				
				lastFinalDateStr = FormatUtils.formatDate(ownDomainSettingEntity.getGscLatestFinalDate(), "yyyy-MM-dd");
				lastFreshDateStr = FormatUtils.formatDate(ownDomainSettingEntity.getGscLatestFreshDate(), "yyyy-MM-dd");
				
				newLastFinalDate = FormatUtils.toDate(lastFinalDateStr, "yyyy-MM-dd");
				lastFinalDateInDb = latestFinalMap.get(ownDomainId);
				
				newLastFreshDate = FormatUtils.toDate(lastFreshDateStr, "yyyy-MM-dd");
				lastFreshDateInDb = latestFreshMap.get(ownDomainId);
				
//				System.out.println("= updating OID:" + ownDomainId 
//						+ ", FNdb:" + lastFinalDateStr + " ==> FNnew:" + FormatUtils.formatDate(lastFinalDateInDb, "yyyy-MM-dd")  
//						+ ", FRdb:" + lastFreshDateStr + " ==> FRnew:" + FormatUtils.formatDate(lastFreshDateInDb, "yyyy-MM-dd"));
				
				
				if (lastFinalDateInDb == null || newLastFinalDate.getTime() > lastFinalDateInDb.getTime() || 
						lastFreshDateInDb == null || newLastFreshDate.getTime() > lastFreshDateInDb.getTime()) {
					System.out.println("==== updating OID:" + ownDomainId 
							+ ", FNdb:" + lastFinalDateStr + " ==> FNnew:" + FormatUtils.formatDate(lastFinalDateInDb, "yyyy-MM-dd")  
							+ ", FRdb:" + lastFreshDateStr + " ==> FRnew:" + FormatUtils.formatDate(lastFreshDateInDb, "yyyy-MM-dd"));
					
					oidDateMap.put(ownDomainId, new String[] {lastFreshDateStr, lastFinalDateStr});
				}
			}

			if (oidDateMap.size() > 0) {
				ownDomainSettingEntityDAO.updateGscLatestAndFinalDateByOid(oidDateMap);
			}
			
			System.out.println("Shared profile Updated! size:" + oidDateMap.size());
		} catch (Exception e) {
			e.printStackTrace();
		}
		
	}

}
