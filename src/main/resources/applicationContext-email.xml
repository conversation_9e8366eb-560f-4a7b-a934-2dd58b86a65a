<beans xmlns="http://www.springframework.org/schema/beans"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns:context="http://www.springframework.org/schema/context"
    xmlns:aop="http://www.springframework.org/schema/aop"
    xmlns:tx="http://www.springframework.org/schema/tx"
    xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans-2.5.xsd
        http://www.springframework.org/schema/context
        http://www.springframework.org/schema/context/spring-context-2.5.xsd
        http://www.springframework.org/schema/aop
        http://www.springframework.org/schema/aop/spring-aop-2.5.xsd
        http://www.springframework.org/schema/tx
        http://www.springframework.org/schema/tx/spring-tx-2.5.xsd"
    default-autowire="byName">
    
	<bean id="javaMailSender" class="org.springframework.mail.javamail.JavaMailSenderImpl">
		<property name="host" value="${gmail.smtp.host}" />
		<property name="username" value="${gmail.smtp.username}" />
		<property name="password" value="${gmail.smtp.password}" />
		<property name="port" value="${gmail.smtp.port}" />
		<property name="javaMailProperties">
			<props>
				<prop key="mail.smtp.timeout">${gmail.smtp.timeout}</prop>
				<prop key="mail.smtp.auth">${gmail.smtp.auth}</prop>
			</props>
		</property>
	</bean>
	
	<bean id="emailSenderComponent" class="seoclarity.backend.utils.EmailSenderComponent">
		<property name="javaMailSender" ref="javaMailSender"/>
		<property name="velocityEngine" ref="velocityEngine"/>
		<property name="from" value="${mail.from}" />
		<property name="replyTo" value="${mail.replyTo}" />
	</bean>
	
	<!-- Velocity Engine for Email Template -->
	<bean id="velocityEngine" class="org.springframework.ui.velocity.VelocityEngineFactoryBean">
		<property name="resourceLoaderPath" value="classpath:email_resource" />
		<property name="velocityProperties">
			<props>
				<prop key="directive.foreach.counter.initial.value">0</prop>
				<prop key="input.encoding">utf-8</prop>
				<prop key="output.encoding">utf-8</prop>
			</props>
		</property>
	</bean>

	<bean id="zeptoMailSenderComponent" class="seoclarity.backend.utils.ZeptoMailSenderComponent">
		<property name="velocityEngine" ref="velocityEngine"/>
		<property name="from" value="${mail.from}" />
		<property name="replyTo" value="${mail.replyTo}" />
	</bean>

</beans>