{"mappings": {"properties": {"id": {"type": "keyword"}, "url": {"type": "keyword"}, "ownDomainId": {"type": "integer"}, "nextFetchDate": {"type": "date", "format": "yyyy-MM-dd'T'HH:mm:ss.SSSZ"}, "urlId": {"type": "integer"}, "urlMurmurHash": {"type": "keyword"}, "version": {"type": "integer"}, "processEndTime": {"type": "date", "format": "strict_date_optional_time||epoch_millis"}, "sendTime": {"type": "date", "format": "strict_date_optional_time||epoch_millis"}}}}